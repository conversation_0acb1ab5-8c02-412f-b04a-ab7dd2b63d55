  /**
 * 时间戳转换为指定格式的时间字符串
 * @param {number|string} timestamp - 时间戳
 * @param {string} format - 格式，默认为 'yyyy-MM-dd HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */ function formatTimestamp(timestamp:number) {
    if (!timestamp) return ''
  
    const date = new Date(Number(timestamp))
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  
  export {
    formatTimestamp,
  }
  