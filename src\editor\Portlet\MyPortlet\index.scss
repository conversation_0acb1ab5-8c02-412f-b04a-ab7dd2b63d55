.portlet-data-box {
  background-color: #fff;
  border-radius: 4px;
  padding: 0;
  width: 100%;
}

.portlet-tabs-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.portlet-tabs-list {
  width: 100%;

  .portlet-tab-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    border-radius: 2px;
    border: 1px solid #e8e9eb;
    margin-bottom: 12px;

    &:hover {
      background-color: #f9f9f9;
    }

    .portlet-tab-info {
      display: flex;
      align-items: center;

      .drag-handle {
        color: #ccc;
        cursor: move;
      }

      .item-title {
        font-size: 14px;
      }
    }

    .portlet-tab-actions {
      display: flex;

      button {
        color: #666;

        &:hover {
          color: #108cee;
        }
      }
    }
  }
}

.portlet-footer {
  display: flex;
  gap: 10px;

  button {
    flex: 1;
    padding: 10px 0;
    border: 1px solid #ddd;
    background-color: #fff;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

/* 内容配置区域样式 */
.portlet-content-items {
  margin-top: 10px;

  .cxd-Panel {
    margin-bottom: 10px;
    border: 1px solid #e8e9eb;

    .cxd-Panel-heading {
      background-color: #f9f9f9;
      border-bottom: 1px solid #e8e9eb;

      .cxd-Panel-title {
        font-size: 14px;
        font-weight: 500;
      }
    }

    .cxd-Panel-body {
      padding: 10px;
    }
  }

  .text-muted {
    color: #8c8c8c;
    font-size: 12px;
    margin-bottom: 10px;
  }
}

/* 入口项管理器样式 */
.entry-items-box {
  background-color: #fff;
  border-radius: 4px;
  padding: 0;
  width: 100%;
}

.entry-items-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.entry-items-list {
  width: 100%;

  .entry-item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    border-radius: 2px;
    border: 1px solid #e8e9eb;
    margin-bottom: 10px;
    background-color: #fafafa;

    &:hover {
      background-color: #f0f0f0;
    }

    .entry-item-info {
      display: flex;
      align-items: center;

      .item-title {
        font-size: 14px;
      }

      .item-icon {
        margin-left: 8px;
        color: #666;

        i {
          font-size: 14px;
        }
      }
    }

    .entry-item-actions {
      display: flex;

      button {
        color: #666;
        padding: 2px 5px;

        &:hover {
          color: #3370ff;
        }
      }
    }
  }
}

.entry-items-footer {
  display: flex;
  gap: 10px;

  button {
    flex: 1;
    padding: 10px 0;
    border: 1px solid #ddd;
    background-color: #fff;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

/* 对话框中右对齐开关控件样式 */
.text-right .cxd-Switch {
  float: right;
}

.font-bold {
  font-weight: bold;
}

.mb-2 {
  margin-bottom: 0.5rem;
}
