import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, SearchBox, Drawer, Input } from 'amis';
import './index.scss';
import { getFormDataVersionList, updateApplicationPageAndClass } from '@/utils/api/api';
import AMISRenderer from '@/component/AMISRenderer';
import Background from 'amis-editor/lib/renderer/style-control/Background';
import { width } from '@/editor/EChartsEditor/Common';

interface VersionHistoryProps {
  show: boolean;
  onClose: () => void;
  pageId: string | number;
  pageSchema: string;
  onRollback?: () => void;
  formDataId: string | number;
  store: any
}

interface VersionItem {
  id: string | number;
  createTime: string | number;
  createUser: string;
  versionNumber: string | number;
  versionName?: string;
  data: string
}

const VersionHistory: React.FC<VersionHistoryProps> = ({ show, onClose, onRollback, pageId, pageSchema, formDataId, store }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [versionList, setVersionList] = useState<VersionItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editingVersionId, setEditingVersionId] = useState<string | number | null>(null);
  const [versionTitle, setVersionTitle] = useState('');
  const [activeMenuId, setActiveMenuId] = useState<string | number | null>(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const [openDiffPopup, setOpenDiffPopup] = useState(false);
  const [selectedVersionForDiff, setSelectedVersionForDiff] = useState<VersionItem | null>(null);
  const [showNameEditor, setShowNameEditor] = useState(false);
  const [editingVersion, setEditingVersion] = useState<VersionItem | null>(null);
  const [versionType, setVersionType] = useState<'all' | 'mine'>('all');
  const [showRollbackPopup, setShowRollbackPopup] = useState(false);

  // 获取版本数据
  useEffect(() => {
    if (show) {
      setLoading(true);
      getVersionList();
    }
  }, [show, pageId, versionType]);

  function getVersionList() {
    var data: any = {
      applicationPageId: pageId,
      versionName: searchTerm
    }
    //判断是否为我的版本
    if (versionType === 'mine') {
      data.userId = store.userInfo.id;
    }

    getFormDataVersionList(data).then(res => {
      setVersionList(res.data);
      setLoading(false);
    }).catch(err => {
      setVersionList([]);
      setLoading(false);
    });
  }

  // 过滤版本列表
  const filteredVersions = versionList;

  // 处理添加版本标题
  const handleAddVersionTitle = (version: VersionItem) => {
    setEditingVersionId(version.id);
    setVersionTitle(version.versionName || '');
    setEditingVersion(version);
    setShowNameEditor(true);
    setActiveMenuId(null);
  };

  // 处理回滚操作
  const handleRollback = (version: VersionItem) => {
    // console.log('回滚到版本', version);
    // // 这里应该调用回滚API
    // updateApplicationPageAndClass({
    //   formDataId: version.id,
    //   id: pageId
    // }).then(res => {
    //   onRollback && onRollback();
    //   // getVersionList();
    // }).catch(err => {
    //   setError(err.message);
    // })

    setSelectedVersionForDiff(version);
    setShowRollbackPopup(true);
    // 成功后可能需要关闭对话框或刷新数据
    setActiveMenuId(null);
  };

  // 处理对比操作
  const handleCompare = (version: VersionItem) => {
    console.log('对比版本', version);
    setSelectedVersionForDiff(version);
    setOpenDiffPopup(true);
    setActiveMenuId(null);
  };

  // 显示操作菜单
  const showMenu = (e: React.MouseEvent, item: VersionItem) => {
    e.stopPropagation();
    setActiveMenuId(item.id);
    setMenuPosition({
      x: e.clientX,
      y: e.clientY
    });
  };

  // 点击其他地方关闭菜单
  useEffect(() => {
    const handleClickOutside = () => {
      setActiveMenuId(null);
    };

    if (activeMenuId !== null) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [activeMenuId]);

  // 鼠标悬停处理函数
  const handleMouseOver = (e: React.MouseEvent<HTMLDivElement>) => {
    e.currentTarget.style.backgroundColor = '#f5f5f5';
  };

  // 鼠标离开处理函数
  const handleMouseOut = (e: React.MouseEvent<HTMLDivElement>) => {
    e.currentTarget.style.backgroundColor = 'transparent';
  };

  // 生成对比弹窗的schema
  const getDiffSchema = () => {
    if (!selectedVersionForDiff) return "";
    
    // 这里可以根据实际需求构建对比弹窗的schema
    return {
      type: "dialog",
      title: `版本对比 - ${selectedVersionForDiff.versionName || selectedVersionForDiff.versionNumber}`,
      "size": "lg",
      body: [
        {
        type: "diff-editor",
        title: "版本对比",
        value:  JSON.parse(selectedVersionForDiff.data) ,
        diffValue: pageSchema
      }
    ]
    };
  };

  const getNameScheme = () => {
    if (!editingVersion) return {};
    
    return {
      type: "dialog",
      title: editingVersion.versionName ? "修改版本标题" : "新增版本标题",
      body: {
        type: "form",
        api: {
          method: "put",
          url: "/admin-api/system/form-data/update-version",
          data: {
            versionName: '${versionName}',
            id: editingVersion.id
          },
          adaptor: function (payload: any, response: any, api: any, context: any) {
            console.log(context); // 打印上下文数据
            setShowNameEditor(false);
            onRollback && onRollback();
            return {
              ...payload
            };
          }
        },
        body: [
          {
            type: "input-text",
            name: "versionName",
            label: "版本标题",
            required: true,
            value: editingVersion.versionName || ""
          }
        ]
      },
      actions: [
        {
          type: "button",
          label: "取消",
          actionType: "close"
        },
        {
          type: "submit",
          label: "确定",
          primary: true
        }
      ]
    };
  };

  const getRollbackSchema = () => {
    if (!selectedVersionForDiff) return "";
    
    return {
      type: "dialog",
      title: "确定要回滚版本吗？",
      body: [
        {
          type: "tpl",
          tpl: "回滚后，可以还原到该历史版本，确定要进行回滚吗？"
        }
      ],
      actions: [
        {
          type: "button",
          label: "取消",
          actionType: "close"
        },
        {
          type: "submit",
          label: "确定",
          primary: true,
          actionType: "ajax",
          api: {
            method: "put",
            url: "/admin-api/system/application-page-and-class/update",
            data: {
              formDataId: selectedVersionForDiff.id,
              id: pageId
            },
            adaptor: function (payload: any, response: any, api: any, context: any) {
              console.log(context); // 打印上下文数据
              setShowRollbackPopup(false);
              onRollback && onRollback();
              return {
                ...payload
              };
            },
          }
        }
      ]
    };
  };


  return (
    <Drawer
      show={show}
      onHide={onClose}
      title="历史版本"
      className="version-history-modal"
      closeOnEsc
      size="md"
      position="left"
    >
      <div className="version-history-container">
        <div className="version-history-header" style={{margin: '10px', fontSize: '18px'}}>
          历史版本
          <div className="version-tabs" style={{display: 'inline-block', marginLeft: '10px'}}>
            <Button 
              level={versionType === 'all' ? 'primary' : 'default'} 
              onClick={() => setVersionType('all')}
              style={{borderRadius: '4px 0 0 4px', marginRight: '0'}}
            >
              所有版本
            </Button>
            <Button 
              level={versionType === 'mine' ? 'primary' : 'default'} 
              onClick={() => setVersionType('mine')}
              style={{borderRadius: '0 4px 4px 0', marginLeft: '0'}}
            >
              我的版本
            </Button>
          </div>
        </div>

        <div className="version-history-search">
          <SearchBox
            placeholder="请输入版本标题搜索"
            value={searchTerm}
            onChange={(value: string) => setSearchTerm(value)}
            onSearch={getVersionList}
            className="version-search-box"
          />
        </div>
        
        <div className="version-list">
          {loading ? (
            <div className="version-loading">加载中...</div>
          ) : error ? (
            <div className="version-error">{error}</div>
          ) : filteredVersions.length > 0 ? (
            filteredVersions.map((item, index) => (
              <div 
                key={item.id} 
                className={`version-item ${formDataId === item.id ? 'current-version' : ''}`}
              >
                <div className="version-item-dot"></div>
                <div className="version-item-content">
                  <div className="version-item-time">
                    {item.createTime
                      ? new Date(item.createTime).toLocaleString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit'
                        })
                      : ''}
                  </div>
                  <div className="version-item-info">
                    <div className="version-item-version">{item.versionName || item.versionNumber}</div>
                  </div>
                  <div className="version-item-author">{item.createUser}</div>
                </div>
                <div className="version-item-actions">
                  <Button
                    level="link"
                    className="version-item-action"
                    onClick={(e: React.MouseEvent) => showMenu(e, item)}
                  >
                    <i className="fa fa-ellipsis-h"></i>
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <div className="version-empty">无匹配版本记录</div>
          )}
        </div>

        {/* 自定义下拉菜单 */}
        {activeMenuId !== null && (
          <div 
            className="version-dropdown-menu"
            style={{
              position: 'fixed',
              left: `${menuPosition.x}px`,
              top: `${menuPosition.y}px`,
              zIndex: 1000,
              background: '#fff',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              borderRadius: '4px',
              padding: '4px 0',
              width: '150px'
            }}
          >
            {versionList.find(item => item.id === activeMenuId) && (
              <>
                <div 
                  className="version-dropdown-item"
                  onClick={() => handleAddVersionTitle(versionList.find(item => item.id === activeMenuId)!)}
                  style={{
                    padding: '8px 16px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                  onMouseOver={handleMouseOver}
                  onMouseOut={handleMouseOut}
                >
                  {versionList.find(item => item.id === activeMenuId)?.versionName ? '修改版本标题' : '新增版本标题'}
                </div>
                <div 
                  className="version-dropdown-item"
                  onClick={() => handleRollback(versionList.find(item => item.id === activeMenuId)!)}
                  style={{
                    padding: '8px 16px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                  onMouseOver={handleMouseOver}
                  onMouseOut={handleMouseOut}
                >
                  回滚
                </div>
                <div 
                  className="version-dropdown-item"
                  onClick={() => handleCompare(versionList.find(item => item.id === activeMenuId)!)}
                  style={{
                    padding: '8px 16px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                  onMouseOver={handleMouseOver}
                  onMouseOut={handleMouseOut}
                >
                  对比
                </div>
              </>
            )}
          </div>
        )}
      </div>

      {/* 对比弹框 */}
      {openDiffPopup && (
        <AMISRenderer
          show={openDiffPopup}
          onClose={() => setOpenDiffPopup(false)}
          schema={getDiffSchema()}
        />
      )}

      {/* 新增版本标题弹窗 */}
      {showNameEditor && (
        <AMISRenderer
          show={showNameEditor}
          onClose={() => setShowNameEditor(false)}
          schema={getNameScheme()}
        />
      )}

      {/* 回滚弹窗 */}
      {showRollbackPopup && (
        <AMISRenderer
          show={showRollbackPopup}
          onClose={() => setShowRollbackPopup(false)}
          schema={getRollbackSchema()}
        />
      )}
    </Drawer>
  );
};

export default VersionHistory; 