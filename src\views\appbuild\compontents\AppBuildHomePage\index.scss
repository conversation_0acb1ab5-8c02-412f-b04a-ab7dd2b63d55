.app-build-home-page {
  position: relative;
  width: calc(100% - 1.5rem);
  height: calc(100vh - 4.625rem);
  margin: 0.75rem;
  padding-top: 40px;
  overflow: auto;
  /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
  -webkit-scrollbar {
    display: none;
  }
  /* 隐藏Firefox浏览器的滚动条 */
  scrollbar-width: none;

  &-main {
    position: relative;
    width: 67.5rem;
    overflow: hidden;
    margin: auto;
    padding: 1rem 1.25rem;
  }

  .app-build-content {
    width: 100%;
    margin-top: 20px;
    overflow: auto;
    height: calc(100vh - 5.75rem);
    /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
    -webkit-scrollbar {
      display: none;
    }
    /* 隐藏Firefox浏览器的滚动条 */
    scrollbar-width: none;
  }

  .app-build-tabs-title {
    font-size: 16px;
    font-weight: 500;
  }

  // 加载和空状态
  .app-loading {
    text-align: center;
    padding: 50px 0;
    color: var(--text-color);
  }

  .app-empty {
    text-align: center;
    padding: 50px 0;
    color: var(--text-color);
  }

  // 搭建中心样式
  .center-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    color: var(--text-color);
  }

  // 最近使用的应用列表样式
  .app-cards-container {
    width: 100%;

    .app-cards-grid {
      position: relative;
      width: 100%;
      display: grid;
      grid-template-columns: repeat(auto-fill, 15.3125rem);
      grid-gap: 0.75rem 1.25rem;
    }

    .app-card {
      width: 15.3125rem;
      height: 6.625rem;
      box-shadow: 0 0 0.625rem 0 var(--borderColor);
      background-color: var(--light-bg);
      border-radius: 0.375rem;
      padding: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      cursor: pointer;

      &:hover {
        box-shadow: 0 0 0.625rem 0 var(--borderColor);
      }

      &:active {
        opacity: 0.6;
      }

      &-content {
        width: 100%;
        height: 2.625rem;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .app-card-icon {
          position: relative;
          width: 42px;
          height: 42px;

          &-img {
            width: 100%;
            height: 100%;
          }
        }

        .app-card-info {
          flex: 1;
          margin-left: 0.875rem;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .app-card-title {
            font-size: 0.875rem;
            color: var(--text-color);
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
          }

          .app-card-desc {
            font-size: 0.625rem;
            color: var(--text--muted-color);
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
          }
        }
      }

      &-footer {
        margin-top: 16px;
        width: 100%;
        height: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .app-card-status {
          height: 1.25rem;
          display: flex;
          font-size: 0.63rem;
          align-items: center;
          justify-content: center;
        }

        .status-enabled {
          padding: 0.125rem 0.5rem;
          border-radius: 4px;
          color: #52c41a;
          background-color: #f6ffed;
        }

        .status-disabled {
          padding: 0.125rem 0.5rem;
          border-radius: 4px;
          color: var(--text--muted-color);
          background-color: var(--borderColor);
        }

        .home-icon {
          color: var(--primary);
          font-size: 0.75rem;
        }

        .app-card-action {
          width: 1.25rem;
          height: 1.25rem;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--borderColor);
          border-radius: 50%;
          i {
            font-size: 0.75rem;
            color: var(--primary);
          }
        }
      }
    }
  }
}

// 搭建中心卡片自定义样式
.center-crud {
  .build-center-card {
    height: 72px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: var(--light-bg);
    border-radius: 4px;
    padding: 16px;
    cursor: pointer;
  }

  .center-card-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    text-align: center;
  }

  .center-card-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }

    span {
      width: 42px !important;
      height: 42px !important;
      margin: 0 !important;
      border-radius: 4px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  .center-card-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.app-build-content {
  .view-mode-selector {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  // 强制覆盖AMIS默认样式
  .cxd-Form--inline,
  .dark-Form--inline {
    display: flex !important;
    flex-wrap: wrap !important;
    width: 100% !important;

    // 搜索框容器
    .cxd-Form-item:nth-child(1),
    .dark-Form-item:nth-child(1) {
      flex: 0 0 auto !important;
      margin-right: auto !important; // 这会将搜索框推到最左边
      width: 280px !important;
      max-width: 280px !important;
    }

    // 筛选条件容器
    .cxd-Form-item:nth-child(n + 2),
    .dark-Form-item:nth-child(n + 2) {
      margin-left: 8px !important;
    }
  }
}

// 我的应用筛选器样式
.my-app-filter {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  flex-wrap: nowrap !important;

  // 内联布局样式
  &.cxd-Form--inline,
  &.dark-Form--inline {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    flex-wrap: nowrap !important;
    width: 100% !important;

    // 重置所有表单项的默认样式
    .cxd-Form-item,
    .dark-Form-item {
      margin: 0 !important;
      padding: 0 !important;
    }

    // 筛选条件的选择器样式
    .cxd-Select,
    .dark-Select {
      min-width: 140px !important;
    }

    // 统一所有输入框和下拉框的背景色样式
    .cxd-TextControl-input,
    .cxd-Select-control,
    .cxd-Select,
    .dark-TextControl-input,
    .dark-Select-control,
    .dark-Select {
      border: none !important;
      background-color: var(--light-bg) !important;
      border-radius: 4px !important;
      box-shadow: none !important;
    }

    // 搜索框内置图标样式
    .search-input-with-icon {
      position: relative;

      .cxd-TextControl-input,
      .dark-TextControl-input {
        padding-right: 35px !important;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23999' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'/%3E%3C/svg%3E") !important;
        background-repeat: no-repeat !important;
        background-position: right 10px center !important;
        background-size: 16px 16px !important;
      }
    }

    // 选择器下拉箭头样式
    .cxd-Select-arrow,
    .dark-Select-arrow {
      width: 16px;
      height: 16px;
      color: var(--text--muted-color);
      font-size: 12px;
    }

    .cxd-Wrapper--md,
    .cxd-Container--md,
    .dark-Wrapper--md,
    .dark-Container--md {
      padding: 0 !important;
    }
  }
}

.status-enabled-crud {
  color: #52c41a;
}

.status-disabled-crud {
  color: var(--text--muted-color);
}

.cxd-Card-body,
.dark-Card-body {
  padding: 0;
}

.cxd-Card,
.dark-Card {
  border: none;
}

.cxd-Panel-body,
.dark-Panel-body {
  padding: 0;
  margin-bottom: 16px;
}

.cxd-Panel,
.dark-Panel {
  border: none;
  -webkit-box-shadow: none;
}

.filter-layout {
  &.cxd-Form--inline,
  &.dark-Form--inline {
    display: flex !important;
    flex-wrap: nowrap !important;
    justify-content: space-between !important;
    width: 100% !important;

    // 强制搜索框靠左
    .cxd-Form-item:first-child,
    .dark-Form-item:first-child {
      flex: 0 0 280px !important;
      margin-right: auto !important;
      width: 280px !important;
      max-width: 280px !important;
      min-width: 280px !important;
      float: left !important;
      position: relative !important;
      left: 0 !important;
    }

    // // 强制其他筛选条件靠右
    .cxd-Form-item:nth-child(n+3),
    .dark-Form-item:nth-child(n+3) {
      flex: 0 0 auto !important;
      margin-right: 8px !important;
      text-align: right !important;
      float: right !important;
    }
  }
}

.filter-body {
  display: flex !important;
  justify-content: space-between !important;
  width: 100% !important;
  align-items: center !important;

  .search-wrapper {
    flex: 0 0 280px !important;
    margin-right: auto !important;
  }

  .filter-wrapper {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: flex-end !important;
    flex-wrap: nowrap !important;
    margin-left: auto !important;
    text-align: right !important;

    // 确保内部的选择器靠右对齐
    .cxd-SelectControl,
    .dark-SelectControl {
      text-align: right !important;
    }
  }
}
