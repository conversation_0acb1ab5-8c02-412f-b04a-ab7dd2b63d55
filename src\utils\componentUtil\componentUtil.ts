import schemaTplData from './schemaTplClassified.json';

/**
 * 获取所有组件分类信息
 * @returns 按分类组织的组件信息
 */
export const getSchemaCategories = () => {
  return schemaTplData.byCategory || {};
};

/**
 * 获取按文件分类的组件信息
 * @returns 按文件组织的组件信息
 */
export const getSchemaByFile = () => {
  return schemaTplData.byFile || {};
};

/**
 * 获取所有组件类型列表
 * @returns 所有组件类型的数组
 */
export const getAllSchemaTypes = () => {
  return schemaTplData.allTypes || [];
};

/**
 * 获取指定组件的分类信息
 * @param schemaName 组件名称
 * @returns 组件的分类信息，包括所属分类、所属文件和是否有效
 */
export const getSchemaClassification = (schemaName: string) => {
  const SCHEMA_CATEGORIES = getSchemaCategories();
  const SCHEMA_BY_FILE = getSchemaByFile();
  const ALL_SCHEMA_TYPES = getAllSchemaTypes();
  
  // 查找该schema属于哪个分类
  const categoryInfo: Record<string, string[]> = {};
  
  Object.entries(SCHEMA_CATEGORIES).forEach(([category, items]) => {
    if (Array.isArray(items) && items.includes(schemaName)) {
      categoryInfo[category] = items as string[];
    }
  });
  
  // 查找该schema属于哪个文件
  let fileInfo = '';
  Object.entries(SCHEMA_BY_FILE).forEach(([file, items]) => {
    if (Array.isArray(items) && items.includes(schemaName)) {
      fileInfo = file;
    }
  });
  
  return {
    categoryInfo,
    fileInfo,
    isValid: ALL_SCHEMA_TYPES.includes(schemaName)
  };
};

/**
 * 获取组件配置项的分类映射
 * @param configCategories 配置项分类对象
 * @returns 配置项到分类的映射
 */
export const getConfigCategoryMap = (configCategories: Record<string, string[]>) => {
  const categoryMap: Record<string, string> = {};
  
  Object.entries(configCategories).forEach(([category, items]) => {
    items.forEach(item => {
      categoryMap[item] = category;
    });
  });
  
  return categoryMap;
};

export default {
  getSchemaCategories,
  getSchemaByFile,
  getAllSchemaTypes,
  getSchemaClassification,
  getConfigCategoryMap
};