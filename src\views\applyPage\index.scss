.asidePagesClass {
  background-color: var(--body-bg);
  color: var(--text-color);
  border-right: 1px solid var(--borderColor);
  transition: width 0.3s ease;

  // 页面导航头部样式优化
  .page-nav-header {
    // 导航容器样式
    .cxd-Nav {
      // 如果没有导航项，隐藏整个导航容器
      &:empty,
      &:not(:has(.cxd-Nav-Menu-item)) {
        display: none !important;
        height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
      }
    }

    // 减少头部组件的底部间距
    margin-bottom: 0;
    padding-bottom: 0;

    // 减少头部和内容之间的间距
    + .pages-nav-content {
      margin-top: 0;
      padding-top: 0;
    }
  }

  // 缩起状态
  &.collapsed {
    width: 60px !important;
    min-width: 60px !important;

    // 确保头部区域始终显示
    .my-nav-header,
    .page-nav-header-bar {
      display: flex !important;
      justify-content: center;
      align-items: center;
      min-height: 40px;
      padding: 8px;
    }

    // 隐藏文本内容，但保持容器结构
    .nav-header-title,
    .nav-item-name,
    .nav-item-text,
    .pagesNavClass-name,
    .pagesNavPage-name {
      display: none;
    }

    // 隐藏搜索框
    .nav-search-box,
    .cxd-Nav-searchbox {
      display: none;
    }

    // 隐藏搜索和添加按钮，但保持缩起按钮
    .search-btn,
    .add-nav-btn,
    .nav-add-btn {
      display: none !important;
    }

    // 确保缩起按钮始终可见
    .collapse-btn {
      display: flex !important;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      width: 32px;
      height: 32px;
      background: transparent;
      border: none;
      cursor: pointer;

      .nav-icon,
      .collapse-icon {
        display: block !important;
        width: 16px;
        height: 16px;
      }
    }

    // 调整导航项布局
    .nav-item,
    .pagesNavClass,
    .pagesNavPage {
      justify-content: center;
      padding: 0.25rem;

      .nav-item-icon,
      .pagesNavClass-typeIcon,
      .pagesNavPage-typeIcon {
        margin: 0;
      }

      .nav-item-expand-icon,
      .pagesNavClass-icon {
        display: none;
      }
    }

    // 隐藏子菜单
    .pagesNavChildren {
      display: none;
    }

    // 确保标题区域仍然显示（但隐藏文本）
    .page-nav-header-bar {
      display: flex !important;
      align-items: center;
      justify-content: center;
      min-height: 40px; // 保持最小高度以容纳按钮
    }

    // 确保缩起按钮在缩起状态下可见
    .cxd-Nav-Menu-item {
      &[data-action="collapse"],
      &.nav-collapse-item {
        display: flex !important;
        justify-content: center;
        align-items: center;

        .cxd-Nav-Menu-item-icon,
        .nav-collapse-icon {
          display: block !important;
        }

        .cxd-Nav-Menu-item-label {
          display: none;
        }
      }
    }

    // MyNavPage 组件特定样式
    .cxd-Nav {
      // 确保导航容器在缩起状态下仍然显示头部
      .cxd-Nav-Menu {
        // 隐藏导航项，但保持头部
        .cxd-Nav-Menu-item:not(.nav-header-item) {
          display: none;
        }
      }

      // 头部容器样式
      .nav-header-container,
      .my-nav-header {
        display: flex !important;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 40px;
      }
    }

    // 通用缩起按钮样式
    .my-nav-collapse-btn,
    [class*="collapse"],
    button[title*="导航"] {
      display: flex !important;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      margin: 0 auto;
      background: transparent !important;
      border: none !important;
      cursor: pointer;

      svg, .nav-icon {
        display: block !important;
        width: 16px;
        height: 16px;
        stroke: currentColor;
      }
    }
  }
}

.headerPagesClassAppPage {
  position: relative;
  width: 100%;
  height: 100%;
  border-bottom: 1px solid var(--borderColor);
  z-index: 9999 !important;
}


// 页面导航头部样式
.page-nav-header {
  .my-nav-wrapper {
    border-bottom: 1px solid var(--borderColor);
  }

  .nav-header {
    padding: 0.4375rem 0.5rem !important;
    min-height: auto !important;

    .nav-header-title {
      font-size: 0.875rem;
      color: var(--text-color);
    }

    .nav-header-actions {
      .search-btn,
      .add-nav-btn,
      .collapse-btn {
        width: 1.875rem !important;
        height: 1.875rem !important;
        min-width: 1.875rem !important;
        min-height: 1.875rem !important;

        &:hover {
          background-color: var(--light-bg) !important;
          border-radius: 0.4rem !important;
        }

        i {
          font-size: 1rem;
          color: var(--text-color);
        }
      }

      .collapse-btn.collapsed i {
        transform: rotate(-90deg);
      }
    }
  }
}

// 页面导航内容样式
.pages-nav-content {
  .nav-item-page {
    display: flex;
    align-items: center;
    padding: 0.25rem 0.5rem;

    .nav-item-icon {
      width: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;

      .nav-item-check-icon {
        color: var(--primary-color);
        font-size: 0.75rem;
      }
    }

    .nav-item-type-icon {
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.5rem;

      .nav-item-type-icon-img {
        width: 1rem;
        height: 1rem;
      }

      .nav-item-type-icon-svg {
        width: 1rem;
        height: 1rem;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }

    .nav-item-name {
      flex: 1;

      .nav-item-name-text {
        font-size: 0.875rem;
        color: var(--text-color);
      }

      .nav-item-name-input {
        font-size: 0.875rem;
        border: 1px solid var(--primary-color);
        border-radius: 0.25rem;
        padding: 0.125rem 0.25rem;
      }
    }

    &:hover {
      background-color: var(--light-bg);
    }

    &.active {
      background-color: var(--light-bg);
      font-weight: bold;
    }
  }
}

// 应用模板导航样式
.app-template-nav {
  .nav-item-template {
    display: flex;
    align-items: center;
    padding: 0.25rem 0.5rem;

    &.nav-item-category {
      font-weight: 500;

      .nav-item-expand-icon {
        width: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;

        .nav-item-expand-icon-val {
          color: var(--text-color);
          font-size: 0.75rem;
        }
      }
    }

    &.nav-item-page {
      .nav-item-icon {
        width: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;

        .nav-item-check-icon {
          color: var(--primary-color);
          font-size: 0.75rem;
        }
      }
    }

    .nav-item-type-icon {
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.5rem;

      .nav-item-type-icon-val {
        color: var(--text-color);
        font-size: 1rem;
      }

      .nav-item-type-icon-svg {
        width: 1rem;
        height: 1rem;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }

    .nav-item-name-text {
      flex: 1;
      font-size: 0.875rem;
      color: var(--text-color);
    }

    &:hover {
      background-color: var(--light-bg);
    }

    &.active {
      background-color: var(--light-bg);
      font-weight: bold;
    }
  }
}

.asidePageCollapsePage {
  transform: rotate(-90deg) !important;
}

.typePageList {
  padding-left: 1.25rem;
}

.itemClick {
  color: var(--text-color);
  font-weight: bold;
  background-color: var(--light-bg);
}


.item-down {
  transform: rotate(0deg) !important;
}

.displayNone {
  display: none !important;
}

.customContextMenu {
  position: fixed;
  width: 9.375rem;
  overflow: hidden;
  box-shadow: 0 0 0.125rem 0.125rem var(--borderColor);
  z-index: 99999 !important; // 提高 z-index 并使用 !important
  background-color: var(--body-bg); // 确保有背景色

  &-item {
    width: 100%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    padding-left: 2rem;
    background-color: var(--body-bg); // 确保菜单项有背景色
    cursor: pointer;

    &:hover {
      background-color: var(--light-bg);
    }

    &:active {
      opacity: 0.8;
    }

    &-icon {
      font-size: 0.75rem;
      width: 1.75rem;
      height: 1.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-name {
      font-size: 0.75rem;
      color: var(--text-color);
      cursor: default;
    }
  }
}

.applyPageHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  background-color: var(--body-bg);
  border-bottom: 1px solid var(--borderColor);
}

/* 全屏布局样式 */
.full-screen-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--body-bg);

  .full-screen-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .full-screen-app-setting,
  .full-screen-app-publish,
  .full-screen-approval,
  .full-screen-data-set {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }
}

.headActionBar {
  position: relative;
  width: calc(100% - 1rem);
  margin: 0.5rem;
  &-item {
    width: 100%;
    height: 2.5rem;
    display: flex;
    align-items: center;
    padding-left: 2.5rem;
    margin-top: 0.25rem;
    cursor: default;
    border-radius: 0.5rem;

    &:hover {
      background-color: var(--light-bg);
    }

    &-icon {
      width: 1rem;
      height: 1rem;
      display: block;
    }

    &-name {
      font-size: 0.875rem;
      margin-left: 0.75rem;
    }
  }

  &-line {
    position: relative;
    width: 100%;
    height: 1px;
    background-color: var(--borderColor);
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
  }
}

.applyReportMian {
  // width: calc(100% - 2rem);
  height: 100%;
  // margin: 0.75rem;
  // padding: 1rem 1.25rem;
  padding: 12px;
}

.pagesNavChildren {
  box-sizing: border-box;
  width: 100%;
  // padding-left: 1.5rem;
  overflow: hidden;
  transition: height 2s ease-in-out; // 添加高度过渡效果
}

// 页面导航菜单
.pagesNav {
  width: calc(100% - 1rem);
  margin: 0 auto;
  margin-top: 0.5rem;

  .pagesNavClass {
    width: 100%;
    min-height: 1.5rem;
    display: flex;
    align-items: center;
    border-radius: 0.5rem;

    padding: 0.5rem;
    padding-left: 1.875rem;

    &:hover {
      background-color: var(--light-bg);
    }

    &.collapsed {
      .pagesNavClass-icon {
        transform: rotate(-90deg); // 箭头旋转
      }

      & + .pagesNavChildren {
        height: 0;
        // display: none; // 隐藏子内容
      }
    }

    .pagesNavClass-icon {
      transition: transform 0.2s; // 添加旋转动画
    }

    &-typeIcon {
      width: 1rem;
      height: 1rem;
      margin-left: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &-val {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        svg {
          width: 100%;
          height: 100%;
          display: block;
        }
        
        img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }
    &-name {
      flex: 1;
      margin-left: 0.5rem;
      font-size: 0.875rem;
      color: var(--text-color);
      cursor: default;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;

      &-input{
        width: 100%;
        height: 100%;
        border: none;
      }
    }

    &-icon {
      width: 0.75rem;
      height: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      &-val {
        width: 0.625rem;
        height: 0.625rem;
        display: block;
        transform: rotate(0deg);
        transition: transform 0.3s;
      }
    }
  }
  .pagesNavPage {
    width: 100%;
    min-height: 1.5rem;
    display: flex;
    align-items: center;
    border-radius: 0.5rem;

    padding: 0.5rem;
    padding-left: 1.875rem;

    &:hover {
      background-color: var(--light-bg);
    }

    &-icon {
      width: 0.75rem;
      height: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;

      &-val {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &-typeIcon {
      width: 1rem;
      height: 1rem;
      margin-left: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &-val {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        svg {
          width: 100%;
          height: 100%;
          display: block;
        }
        
        img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }
    &-name {
      flex: 1;
      margin-left: 0.5rem;
      font-size: 0.875rem;
      color: var(--text-color);
      cursor: default;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;

      &-input{
        width: 100%;
        height: 100%;
        border: none;
      }
    }
  }
}



.dark-Page,
.cxd-Page {
  height: 100vh;
}
