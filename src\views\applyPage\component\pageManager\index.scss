.page-manager {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--body-bg);

  &-header {
    flex-shrink: 0;
  }

  &-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  &-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
}

.pagesNav {
  width: 100%;
}

.page-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.page-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background-color: var(--background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: var(--primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-bg);
    border-radius: 6px;
    margin-right: 12px;
    flex-shrink: 0;

    i {
      font-size: 18px;
      color: var(--text--muted-color);
    }
  }

  &-content {
    flex: 1;
    min-width: 0;
  }

  &-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
    word-break: break-all;
  }

  &-children {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  &-actions {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover {
      background-color: var(--light-bg);
    }

    i {
      font-size: 14px;
      color: var(--text--muted-color);
    }
  }
}

.page-child-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--light-bg);
  border-radius: 4px;
  font-size: 14px;
  color: var(--text-color);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: var(--primary-bg);
    color: var(--primary);
  }

  i {
    margin-right: 8px;
    font-size: 12px;
    color: var(--text--muted-color);
  }

  span {
    flex: 1;
    word-break: break-all;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  &-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-bg);
    border-radius: 50%;
    margin-bottom: 20px;

    i {
      font-size: 32px;
      color: var(--text--muted-color);
    }
  }

  &-text {
    font-size: 16px;
    color: var(--text--muted-color);
    margin-bottom: 24px;
  }

  &-action {
    .btn {
      padding: 10px 24px;
      font-size: 14px;
      border-radius: 6px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;

      &.btn-primary {
        background-color: var(--primary);
        color: white;

        &:hover {
          background-color: var(--primary-hover);
        }
      }
    }
  }
}

.displayNone {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-manager {
    &-main {
      padding: 12px;
    }
  }

  .page-item {
    padding: 12px;

    &-icon {
      width: 32px;
      height: 32px;
      margin-right: 8px;

      i {
        font-size: 14px;
      }
    }

    &-name {
      font-size: 14px;
    }
  }

  .page-child-item {
    padding: 6px 8px;
    font-size: 12px;
  }
}
