import renderApiConfigTabs from './apiConfig';

const apiSettingObj = (schema: any, id: any, appId: any, field: any, apiConfirm: any): any => {
    //过滤type为operation的列
    const columns = schema?.body[0]?.columns || [];
    var operationColumn = columns.find((column: any) => column.type === 'operation');
    var headBtns =  schema?.body[0]?.headerToolbar
    const addBtn = headBtns.find((btn: any) => btn.label == '新增')


    var buttons = operationColumn?.buttons ? operationColumn.buttons.map((btn: any) => ({ ...btn })) : [];
    if (addBtn) {
        buttons.push({...addBtn})
    }
    buttons.forEach((item: any) => {
        item.isEnable = !item.hidden
        if (item.label == '编辑') {
            item.api = typeof item.drawer.body[0].api === 'object' ? JSON.stringify(item.drawer.body[0].api) : item.drawer.body[0].api;
        } else if (item.label == '查看') {
            item.api = typeof item.drawer.body[0].initApi === 'object' ? JSON.stringify(item.drawer.body[0].initApi) : item.drawer.body[0].initApi;
        } else if (item.label == '删除') {
            item.api = typeof item.dialog.body[0].api === 'object' ? JSON.stringify(item.dialog.body[0].api) : item.dialog.body[0].api;
        } else if (item.label == '新增') {
            item.api = typeof item.drawer.body[0].api === 'object' ? JSON.stringify(item.drawer.body[0].api) : item.drawer.body[0].api;
        }
    })
      return {
        type: 'page',
        regions: ['body'],
        id: 'u:06ba71552faa',
        pullRefresh: {
          disabled: true
        },
        asideResizor: false,
        title: '',
        data: {
          "items": buttons
        },
        body: [
          {
            type: 'crud',
            syncLocation: false,
            // showIndex: true,
            // draggable: true,
            quickSaveItemApi: {
              method: 'put',
              url: '/admin-api/system/form-data/update',
              data: {
                "id": id,
                "applicationPageId": appId,
                "field": field
              },
              requestAdaptor: function (api: any, context: any) {
                const nameToMatch = context.name;
                const indexToUpdate = columns.findIndex((column: any) => column.name === nameToMatch);
                if (context.needWordBreak) {
                  context.textOverflow = 'default'
                } else {
                  context.textOverflow = 'ellipsis'
                }
                if (indexToUpdate !== -1) {
                  columns[indexToUpdate] = context
                } 
  
                schema.body[0].columns = columns
                //context替换columns中的值
                const jsonString = JSON.stringify(schema)
  
  
                return {
                  ...api,
                  data: {
                    ...api.data, // 获取暴露的 api 中的 data 变量
                    data: jsonString // 新添加数据
                  }
                };
              }
            },
            perPageAvailable: [5, 10, 20, 50, 100],
            columns: [
              {
                label: '类型',
                type: 'text',
                name: 'label',
              },
              {
                label: '启用',
                name: 'isEnable',
                type: 'switch',
                onEvent: {
                  change: {
                    weight: 0,
                    actions: [
                      {
                        actionType: 'confirmDialog',
                        dialog: {
                          type: 'dialog',
                          title: '确认操作',
                          body: [
                            {
                              type: 'tpl',
                              tpl: '确定要${event.data.value ? "启用" : "禁用"}${label}吗？',
                              wrapperComponent: '',
                              inline: false,
                              id: 'u:confirmTpl'
                            }
                          ],
                          showCloseButton: true,
                          showErrorMsg: true,
                          showLoading: true,
                          className: 'app-popover',
                          id: 'u:confirmDialog',
                          actions: [
                            {
                              type: 'button',
                              actionType: 'cancel',
                              label: '取消',
                              id: 'u:cancelBtn'
                            },
                            {
                              type: 'button',
                              actionType: 'confirm',
                              label: '确定',
                              primary: true,
                              id: 'u:confirmBtn'
                            }
                          ]
                        }
                      },
                      {
                        actionType: 'ajax',
                        outputVar: 'responseResult',
                        options: {},
                        api: {
                          method: 'put',
                          url: '/admin-api/system/form-data/update',
                          data: {
                            id: id,
                            applicationPageId: appId,
                            field: field
                          },
                          requestAdaptor: function (api: any, context: any) {
                            // 保持与原有quickSaveItemApi一致的处理逻辑
                            console.log(context)
                            // !context.value
                            if (context.__super.label == '编辑' || context.__super.label == '查看' || context.__super.label == '删除') {
                                let columns = schema?.body[0]?.columns
                                let operationColumn = columns.find((column: any) => column.type === 'operation')
                                let item = operationColumn.buttons.find((btn: any) => btn.label == context.__super.label)
                                item.hidden = !context.value
                            } else if (context.__super.label == '新增') {
                                let headerToolbar = schema.body[0].headerToolbar
                                let item = headerToolbar.find((btn: any) => btn.label == context.__super.label)
                                item.hidden = !context.value
                            }
                            const jsonString = JSON.stringify(schema)
                            return {
                              ...api,
                              data: {
                                ...api.data,
                                data: jsonString
                              }
                            };
                          }
                        }
                      }
                    ]
                  }
                }
              },
            //   {
            //     type: "formula",
            //     name: "isEnable",
            //     formula: "${!hidden}"
            //   },
              {
                label: 'api',
                name: 'api',
                type: 'text'
              },
            //   {
            //     type: "formula",
            //     name: "api",
            //     formula: "${api | toJson}"
            //   },
            //   {
            //     label: '自动换行',
            //     name: 'needWordBreak',
            //     quickEdit: {
            //       type: "checkbox",
            //       saveImmediately: true,
            //       mode: 'inline'
            //     }
            //   },
              // {//需指定选项
              //   label: '列过滤',
              //   name: 'filterable',
              //   type: "checkbox",
              // },
              {
                type: 'operation',
                label: '操作',
                buttons: [
                  {
                    label: 'API设置',
                    type: 'button',
                    actionType: 'dialog',
                    dialog: {
                      title: 'API设置',
                      size: 'md',
                      className: 'ae-ApiControl-dialog',
                      headerClassName: 'font-bold',
                      bodyClassName: 'ae-ApiControl-dialog-body',
                      closeOnEsc: true,
                      closeOnOutside: false,
                      showCloseButton: true,
                      body: [
                        {
                          type: 'service',
                          name: 'getApi',
                          data: '${api | toJson}',
                          actionType: 'dialog',
                          body: renderApiConfigTabs(false, apiConfirm)
                        }
                      ]
                    }
                  }
                ],
                id: 'u:0d6e1a5d6a6d'
              }
            ]
          }
        ],
        themeCss: {
          baseControlClassName: {
            'background:default': '#f6f6f6'
          }
        }
      };
    };
    
    export {apiSettingObj};
    
    