{"title": {"desc": "<p>标题组件，包含主标题和副标题。</p>\n<p>在 ECharts 2.x 中单个 ECharts 实例最多只能拥有一个标题组件。但是在 ECharts 3 中可以存在任意多个标题组件，这在需要标题进行排版，或者单个实例中的多个图表都需要标题时会比较有用。</p>\n<p><strong>例如下面不同缓动函数效果的示例，每一个缓动效果图都带有一个标题组件：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-easing&edit=1&reset=1\" width=\"700\" height=\"400\"><iframe />\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    title: {\n        text: 'Main Title',\n        subtext: 'Sub Title',\n        left: 'center',\n        top: 'center',\n        textStyle: {\n            fontSize: 30\n        },\n        subtextStyle: {\n            fontSize: 20\n        }\n    }\n}\n", "name": "title-only", "title": "只有标题的实例", "title-en": "Title"}]}, "legend": {"desc": "<p>图例组件。</p>\n<p>图例组件展现了不同系列的标记(symbol)，颜色和名字。可以通过点击图例控制哪些系列不显示。</p>\n<p>ECharts 3 中单个 echarts 实例中可以存在多个图例组件，会方便多个图例的布局。</p>\n<p>当图例数量过多时，可以使用 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=pie-legend&amp;edit=1&amp;reset=1\" target=\"_blank\">滚动图例（垂直）</a> 或 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=radar2&amp;edit=1&amp;reset=1\" target=\"_blank\">滚动图例（水平）</a>，参见：<a href=\"#legend.type\">legend.type</a></p>\n\n\n\n", "exampleBaseOptions": [{"code": "\n\noption = {\n    color: ['#003366', '#006699', '#4cabce', '#e5323e'],\n    dataset: {\n        source: [\n            ['type', '2012', '2013', '2014', '2015', '2016'],\n            ['Forest', 320, 332, 301, 334, 390],\n            ['Steppe', 220, 182, 191, 234, 290],\n            ['Desert', 150, 232, 201, 154, 190],\n            ['Wetland', 98, 77, 101, 99, 40]\n        ]\n    },\n    legend: {\n    },\n    xAxis: {\n        type: 'category',\n        axisTick: {show: false}\n    },\n    yAxis: {},\n    series: [\n        {\n            type: 'bar',\n            seriesLayoutBy: 'row'\n        },\n        {\n            type: 'bar',\n            seriesLayoutBy: 'row'\n        },\n        {\n            type: 'bar',\n            seriesLayoutBy: 'row'\n        },\n        {\n            type: 'bar',\n            seriesLayoutBy: 'row'\n        }\n    ]\n};\n", "name": "legend", "title": "基础图例", "title-en": "Basic Legend"}, {"code": "\nconst option = {\n    legend: {\n        width: 350,\n        left: 0\n    },\n    tooltip: {},\n    dataset: {\n        source: [\n            ['product', '2012', '2013', '2014', '2015'],\n            ['Matcha Latte', 41.1, 30.4, 65.1, 53.3],\n            ['Milk Tea', 86.5, 92.1, 85.7, 83.1],\n            ['Cheese Cocoa', 24.1, 67.2, 79.5, 86.4]\n        ]\n    },\n    xAxis: [\n        {type: 'category', gridIndex: 0},\n        {type: 'category', gridIndex: 1}\n    ],\n    yAxis: [\n        {gridIndex: 0},\n        {gridIndex: 1}\n    ],\n    grid: [\n        {bottom: '55%'},\n        {top: '55%'}\n    ],\n    series: [\n        // These series are in the first grid.\n        {type: 'bar', seriesLayoutBy: 'row'},\n        {type: 'bar', seriesLayoutBy: 'row'},\n        {type: 'bar', seriesLayoutBy: 'row'},\n        // These series are in the second grid.\n        {type: 'bar', xAxisIndex: 1, yAxisIndex: 1},\n        {type: 'bar', xAxisIndex: 1, yAxisIndex: 1},\n        {type: 'bar', xAxisIndex: 1, yAxisIndex: 1},\n        {type: 'bar', xAxisIndex: 1, yAxisIndex: 1}\n    ]\n};\n\n", "name": "legend-more", "title": "多源图例", "title-en": "Legend on Multiple Source"}]}, "grid": {"desc": "<p>直角坐标系内绘图网格，单个 grid 内最多可以放置上下两个 X 轴，左右两个 Y 轴。可以在网格上绘制<a href=\"#series-line\">折线图</a>，<a href=\"#series-bar\">柱状图</a>，<a href=\"#series-scatter\">散点图（气泡图）</a>。</p>\n<p>在 ECharts 2.x 里单个 echarts 实例中最多只能存在一个 grid 组件，在 ECharts 3 中可以存在任意个 grid 组件。</p>\n<p><strong>例如下面这个 Anscombe Quartet 的示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=scatter-anscombe-quartet&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    color: ['#3398DB'],\n    tooltip: {\n        trigger: 'axis',\n        axisPointer: {            // 坐标轴指示器，坐标轴触发有效\n            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'\n        }\n    },\n    grid: {\n    },\n    xAxis: [\n        {\n            type: 'category',\n            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n            axisTick: {\n                alignWithLabel: true\n            }\n        }\n    ],\n    yAxis: [\n        {\n            type: 'value'\n        }\n    ],\n    series: [\n        {\n            name: '直接访问',\n            type: 'bar',\n            barWidth: '60%',\n            data: [10, 52, 200, 334, 390, 330, 220]\n        }\n    ]\n};\n\n", "title": "基础网格示例", "name": "grid", "title-en": "Basic Grid"}]}, "xAxis": {"desc": "<p>直角坐标系 grid 中的 x 轴，一般情况下单个 grid 组件最多只能放上下两个 x 轴，多于两个 x 轴需要通过配置 <a href=\"#xAxis.offset\">offset</a> 属性防止同个位置多个 x 轴的重叠。</p>\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    color: ['#3398DB'],\n    tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n            type: 'shadow'\n        }\n    },\n    grid: {},\n    xAxis: [\n        {\n            type: 'category',\n            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n        }\n    ],\n    yAxis: [\n        {\n            type: 'value'\n        }\n    ],\n    series: [\n        {\n            name: '直接访问',\n            type: 'bar',\n            barWidth: '60%',\n            data: [10, 52, 200, 334, 390, 330, 220]\n        }\n    ]\n};\n", "title": "基础 x 轴示例", "name": "x-axis", "title-en": "Basic X Axis"}, {"code": "\n// https://echarts.apache.org/examples/zh/editor.html?c=line-function\nconst option = {\"animation\":false,\"grid\":{\"top\":40,\"left\":50,\"right\":40,\"bottom\":50},\"xAxis\":{\"name\":\"x\",\"minorTick\":{\"show\":true},\"splitLine\":{\"lineStyle\":{\"color\":\"#999\"}},\"minorSplitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#ddd\"}}},\"yAxis\":{\"name\":\"y\",\"min\":-100,\"max\":100,\"minorTick\":{\"show\":true},\"splitLine\":{\"lineStyle\":{\"color\":\"#999\"}},\"minorSplitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#ddd\"}}},\"dataZoom\":[{\"show\":true,\"type\":\"inside\",\"filterMode\":\"none\",\"xAxisIndex\":[0],\"startValue\":-20,\"endValue\":20},{\"show\":true,\"type\":\"inside\",\"filterMode\":\"none\",\"yAxisIndex\":[0],\"startValue\":-20,\"endValue\":20}],\"series\":[{\"type\":\"line\",\"showSymbol\":false,\"clip\":true,\"smooth\":true,\"data\":[[-100,-7.28568859177975],[-99.5,-10.308641686442629],[-99,-12.413148293738898],[-98.5,-13.515078781534513],[-98,-13.621163798783135],[-97.5,-12.823906192675082],[-97,-11.288665356829664],[-96.5,-9.23425907937281],[-96,-6.909008869380329],[-95.5,-4.564555818396275],[-95,-2.429959682553713],[-94.5,-0.6885479810639478],[-94,0.5402906066423075],[-93.5,1.21264800981265],[-93,1.3638845704762748],[-92.5,1.1043049945382515],[-92,0.607128800864712],[-91.5,0.08988902269203362],[-91,-0.2089830400941321],[-90.5,-0.05616574430357663],[-90,0.7504337485361828],[-89.5,2.357963053593699],[-89,4.8396258541001735],[-88.5,8.182302580101384],[-88,12.28210110977527],[-87.5,16.94843212511602],[-87,21.916427041284017],[-86.5,26.866748982321948],[-86,31.45115851897577],[-85.5,35.321648453659435],[-85,38.160606043315696],[-84.5,39.709329939589225],[-84,39.792336052740495],[-83.5,38.33522352984697],[-83,35.37441051193005],[-82.5,31.057742636470405],[-82,25.635764735233245],[-81.5,19.444258578788666],[-81,12.879415101870299],[-80.5,6.367660473015441],[-80,0.33263375275288687],[-79.5,-4.837923076371445],[-79,-8.822578093923074],[-78.5,-11.391358995906408],[-78,-12.423514388716917],[-77.5,-11.916388761583795],[-77,-9.984670358500548],[-76.5,-6.850092793304596],[-76,-2.8224853658042144],[-75.5,1.7261975266933924],[-75,6.392644923935266],[-74.5,10.775134678186568],[-74,14.505865183234313],[-73.5,17.279956480954215],[-73,18.878712863643415],[-72.5,19.185255699222136],[-72,18.191313030152614],[-71.5,15.994731145683359],[-71,12.788083709042441],[-70.5,8.83952449599976],[-70,4.467692549018498],[-69.5,0.012975590895437749],[-69,-4.1922741387420865],[-68.5,-7.851922117811794],[-68,-10.730163368329142],[-67.5,-12.669495084794402],[-67,-13.601062453462621],[-66.5,-13.5465994712637],[-66,-12.611959711686918],[-65.5,-10.97299885981492],[-65,-8.855262294629314],[-64.5,-6.509484909480171],[-64,-4.185277201955389],[-63.5,-2.105518924587793],[-63,-0.4438966754207421],[-62.5,0.692286852115487],[-62,1.2724079897455145],[-61.5,1.3449501671845296],[-61,1.0318351442775524],[-60.5,0.5151905269770695],[-60,0.017801185985999862],[-59.5,-0.22090709289360527],[-59,0.028928422051093385],[-58.5,0.9617190257942162],[-58,2.713348257419601],[-57.5,5.3431673515049924],[-57,8.822875928469273],[-56.5,13.03348526904271],[-56,17.770830581210273],[-55.5,22.759318555202444],[-55,27.67283754054597],[-54.5,32.1610885780304],[-54,35.87907719610712],[-53.5,38.51718597410974],[-53,39.82915643467974],[-52.5,39.65545557536569],[-52,37.93987574464047],[-51.5,34.737784778835895],[-51,30.215157003198623],[-50.5,24.638312364356967],[-50,18.3551004804382],[-49.5,11.769017244537302],[-49,5.308367228986769],[-48.5,-0.6069701736010016],[-48,-5.598389007462409],[-47.5,-9.358254797929293],[-47,-11.673945378494896],[-46.5,-12.44420341716319],[-46,-11.68642247579883],[-45.5,-9.534261396568692],[-45,-6.225807256488313],[-44.5,-2.08331287986152],[-44,2.5137494777176124],[-43.5,7.160013600760711],[-43,11.457257707042197],[-42.5,15.04636057918079],[-42,17.635494470616305],[-41.5,19.022220309574532],[-41,19.10769812254342],[-40.5,17.901924909054376],[-40,15.519701164991243],[-39.5,12.16783590862111],[-39,8.124856971095452],[-38.5,3.7151319027968746],[-38,-0.7202306728773972],[-37.5,-4.853079136766696],[-37,-8.395881007451033],[-36.5,-11.124933349885053],[-36,-12.897138971827948],[-35.5,-13.658972761082097],[-35,-13.446987387978195],[-34.5,-12.379984634126401],[-34,-10.643737522168548],[-33.5,-8.469820815939153],[-33,-6.110632965724325],[-32.5,-3.81302424996074],[-32,-1.7930543977126796],[-31.5,-0.21427928123454362],[-31,0.8283777214546426],[-30.5,1.3176465270661046],[-30,1.3149721422382217],[-29.5,0.9535163621628415],[-29,0.42375789153044796],[-28.5,-0.04695086970601061],[-28,-0.21893632662110582],[-27.5,0.13346760225517343],[-27,1.1963329846094723],[-26.5,3.0937660296313783],[-26,5.870948366027804],[-25.5,9.484311863088184],[-25,13.79992777928205],[-24.5,18.60044702205284],[-24,23.600146792414954],[-23.5,28.466890368388025],[-23,32.84915907101051],[-22.5,36.40582759756618],[-22,38.83607084270149],[-21.5,39.90673883640768],[-21,39.47472266749568],[-20.5,37.50224316117999],[-20,34.0635906227061],[-19.5,29.34257625068219],[-19,23.62075967148033],[-18.5,17.257321645532187],[-18,10.662185216671737],[-17.5,4.264587283955446],[-17,-1.5202875629660895],[-16.5,-6.323814866394274],[-16,-9.852842017681294],[-15.5,-11.912562521377135],[-15,-12.421420510444117],[-14.5,-11.416790659702185],[-14,-9.050965359379433],[-13.5,-5.577808226132026],[-13,-1.3312284065670519],[-12.5,3.3026758385509267],[-12,7.917423620193896],[-11.5,12.119203239280052],[-11,15.55837638282317],[-10.5,17.956796360351262],[-10,19.12868503085732],[-9.5,18.99338986202127],[-9,17.579061587148733],[-8.5,15.01709021482992],[-8,11.527942043659884],[-7.5,7.399781778875801],[-7,2.9618766903727725],[-6.5,-1.4447894690655656],[-6,-5.497053399663779],[-5.5,-8.91664548985408],[-5,-11.492442353296507],[-4.5,-13.096007425735229],[-4,-13.689159299781739],[-3.5,-13.323045922867745],[-3,-12.128981716306324],[-2.5,-10.30205367796348],[-2,-8.079154145047465],[-1.5,-5.713593654471709],[-1,-3.448743016334093],[-0.5,-1.4932232631765217],[0,0],[0.5,0.9486397319584265],[1,1.3488096893269022],[1.5,1.2747170638693173],[2,0.870353404927271],[2.5,0.3339660345950574],[3,-0.10322470763397162],[3.5,-0.20204765659669835],[4,0.2582374710442245],[4.5,1.454725891649126],[5,3.4992630153517794],[5.5,6.422580567311993],[6,10.165796338844878],[6.5,14.580238819160703],[7,19.435802797730613],[7.5,24.437261127880166],[8,29.24722169622069],[8.5,33.513794004739815],[9,36.900573657650156],[9.5,39.11630758706193],[10,39.941591483707086],[10.5,39.25017620157846],[11,37.022903312892616],[11.5,33.35291338730777],[12,28.44151963796814],[12.5,22.58494859182894],[13,16.152947095922325],[13.5,9.560970707452038],[14,3.238238801433192],[14.5,-2.4056842830906398],[15,-7.012978673567718],[15.5,-10.305624836117072],[16,-12.107056254092159],[16.5,-12.355580200073405],[17,-11.10843817373304],[17.5,-8.536176088592812],[18,-4.90782121466318],[18.5,-0.5681459110878003],[19,4.091031098382733],[19.5,8.663055207241856],[20,12.759421640647318],[20.5,16.040752592352533],[21,18.243176576243357],[21.5,19.197939288291717],[22,18.842679990982205],[22.5,17.223544347651067],[23,14.488108235548665],[23.5,10.86988618038699],[24,6.6659221812418945],[24.5,2.209546267783195],[25,-2.1592238447013776],[25.5,-6.122984088310804],[26,-9.413361256758252],[26.5,-11.832253816320339],[27,-13.266101020473464],[27.5,-13.692038393324927],[28,-13.175547760361352],[28.5,-11.859988828360883],[29,-9.949136490320415],[29.5,-7.684477199530865],[30,-5.319482513871472],[30.5,-3.0933368490897424],[31,-1.2066268829646725],[31.5,0.1987000891124841],[32,1.0532133853370986],[32.5,1.3664018475405608],[33,1.2249981140652904],[33.5,0.7833810466567335],[34,0.24696017979113305],[34.5,-0.1498891855695422],[35,-0.16925007139800816],[35.5,0.4039734569218113],[36,1.737283984396856],[36.5,3.929813834125026],[37,6.997602267276391],[37.5,10.866447559164017],[38,15.37317300071458],[38.5,20.275381487984532],[39,25.268995081304684],[39.5,30.012154044519434],[40,34.153449933416525],[40.5,37.362044132215885],[41,39.35701605241099],[41.5,39.93331403791406],[42,38.98194585746568],[42.5,36.502522494982095],[43,32.606917653346514],[43.5,27.513569360946178],[44,21.532762221559814],[44.5,15.04401754771972],[45,8.467414060409247],[45.5,2.231202253174259],[46,-3.2615873259646198],[46.5,-7.664738112952494],[47,-10.715980079127142],[47.5,-12.257368751095374],[48,-12.247190107477518],[48.5,-10.762392147500078],[49,-7.991352244733339],[49.5,-4.217613972442744],[50,0.20400418462188566],[50.5,4.876880077839909],[51,9.395124449081761],[51.5,13.376421273233174],[52,16.492404111543],[52.5,18.494034773760912],[53,19.22990353310072],[53.5,18.656001240762667],[54,16.836266627996974],[54.5,13.934019969891603],[55,10.195185607831574],[55.5,5.924910825028729],[56,1.4597454208423777],[56.5,-2.8620933688495294],[57,-6.729712922557857],[57.5,-9.885241147398862],[58,-12.144004442705404],[58.5,-13.40749282195799],[59,-13.668091400301305],[59.5,-13.005317296528741],[60,-11.574077708447874],[60.5,-9.586188374077032],[61,-7.28699674721417],[61.5,-4.929386789541986],[62,-2.747663831836344],[62.5,-0.9338091588054483],[63,0.38164363608619517],[63.5,1.142302663092698],[64,1.3709841074751992],[64.5,1.1666726035062154],[65,0.6936609119957133],[65.5,0.16389208586664838],[66,-0.18582804773230152],[66.5,-0.11958792315217186],[67,0.5713580738956626],[67.5,2.0443276909305883],[68,4.385320374542261],[68.5,7.595478879431859],[69,11.585317583836206],[69.5,16.177432003975525],[70,21.117632346422035],[70.5,26.093670276933018],[71,30.760022354178012],[71.5,34.76661989385577],[72,37.78902591993554],[72.5,39.557391519206995],[73,39.88159291393935],[73.5,38.670252311997615],[74,35.94185467473224],[74.5,31.826844943066902],[75,26.56036723047081],[75.5,20.46612073928973],[76,13.932584913724465],[76.5,7.383539732774445],[77,1.2453162173977006],[77.5,-4.086488316022926],[78,-8.278033050158065],[78.5,-11.083377630925245],[79,-12.36353853437559],[79.5,-12.096849635887272],[80,-10.37975947481941],[80.5,-7.418013821074427],[81,-3.508992319114549],[81.5,0.983279497595262],[82,5.658302370713721],[82.5,10.111887309003258],[83,13.968771747749276],[83.5,16.91232297515734],[84,18.708856862379733],[84.5,19.224586162676218],[85,18.43386853040691],[85.5,16.41819147255127],[86,13.356141579952205],[86.5,9.505386930309934],[87,5.178385663170118],[87.5,0.7140604782586624],[88,-3.5519974865612904],[88.5,-7.316138713800894],[89,-10.331566649107813],[89.5,-12.427404556274425],[90,-13.520327586597327],[90.5,-13.617862853500739],[91,-12.813227911493573],[91.5,-11.272350638002058],[92,-9.214421396127614],[92.5,-6.887907876479652],[93,-4.5443623106137405],[93.5,-2.4125343613659083],[94,-0.6752550400481316],[94.5,0.5487174411715527],[95,1.2161743368259692],[95.5,1.3631725739539164],[96,1.1006393533733236],[96.5,0.6022782227915058],[97,0.0859164667235129],[97.5,-0.20994348985294733],[98,-0.05214413451216387],[98.5,0.761018385811453],[99,2.3761100198305223],[99.5,4.865611281247671],[100,8.215603575381051]]}]}\n", "title": "Minor Ticks", "name": "minor-ticks-x-axis", "title-en": "Minor Axis Ticks"}]}, "yAxis": {"desc": "<p>直角坐标系 grid 中的 y 轴，一般情况下单个 grid 组件最多只能放左右两个 y 轴，多于两个 y 轴需要通过配置 <a href=\"#yAxis.offset\">offset</a> 属性防止同个位置多个 Y 轴的重叠。</p>\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    color: ['#3398DB'],\n    tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n            type: 'shadow'\n        }\n    },\n    grid: {},\n    xAxis: [\n        {\n            type: 'category',\n            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n        }\n    ],\n    yAxis: [\n        {\n            type: 'value'\n        }\n    ],\n    series: [\n        {\n            name: '直接访问',\n            type: 'bar',\n            barWidth: '60%',\n            data: [10, 52, 200, 334, 390, 330, 220]\n        }\n    ]\n};\n", "title": "基础 y 轴示例", "name": "y-axis", "title-en": "Basic Y Axis"}, {"code": "\noption = {\n    legend: {\n        left: 'left',\n        data: ['2的指数', '3的指数']\n    },\n    xAxis: {\n        type: 'category',\n        name: 'x',\n        splitLine: {show: false},\n        data: ['一', '二', '三', '四', '五', '六', '七', '八', '九']\n    },\n    grid: {\n        left: '3%',\n        right: '4%',\n        bottom: '3%',\n        containLabel: true\n    },\n    yAxis: {\n        type: 'log',\n        name: 'y',\n        minorTick: {\n            show: true\n        },\n        minorSplitLine: {\n            show: true\n        }\n    },\n    series: [\n        {\n            name: '3的指数',\n            type: 'line',\n            data: [1, 3, 9, 27, 81, 247, 741, 2223, 6669]\n        },\n        {\n            name: '2的指数',\n            type: 'line',\n            data: [1, 2, 4, 8, 16, 32, 64, 128, 256]\n        },\n        {\n            name: '1/2的指数',\n            type: 'line',\n            data: [1/2, 1/4, 1/8, 1/16, 1/32, 1/64, 1/128, 1/256, 1/512]\n        }\n    ]\n};\n\n", "title": "Log 轴示例", "name": "y-axis-log", "title-en": "Log Axis"}]}, "polar": {"desc": "<p>极坐标系，可以用于散点图和折线图。每个极坐标系拥有一个<a href=\"#angleAxis\">角度轴</a>和一个<a href=\"#radiusAxis\">半径轴</a>。</p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=scatter-polar-punchCard&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\n\nconst hours = ['12a', '1a', '2a', '3a', '4a', '5a', '6a',\n        '7a', '8a', '9a','10a','11a',\n        '12p', '1p', '2p', '3p', '4p', '5p',\n        '6p', '7p', '8p', '9p', '10p', '11p'];\nconst days = ['Saturday', 'Friday', 'Thursday',\n        'Wednesday', 'Tuesday', 'Monday', 'Sunday'];\n\nconst data = [[0,0,5],[0,1,1],[0,2,0],[0,3,0],[0,4,0],[0,5,0],[0,6,0],[0,7,0],[0,8,0],[0,9,0],[0,10,0],[0,11,2],[0,12,4],[0,13,1],[0,14,1],[0,15,3],[0,16,4],[0,17,6],[0,18,4],[0,19,4],[0,20,3],[0,21,3],[0,22,2],[0,23,5],[1,0,7],[1,1,0],[1,2,0],[1,3,0],[1,4,0],[1,5,0],[1,6,0],[1,7,0],[1,8,0],[1,9,0],[1,10,5],[1,11,2],[1,12,2],[1,13,6],[1,14,9],[1,15,11],[1,16,6],[1,17,7],[1,18,8],[1,19,12],[1,20,5],[1,21,5],[1,22,7],[1,23,2],[2,0,1],[2,1,1],[2,2,0],[2,3,0],[2,4,0],[2,5,0],[2,6,0],[2,7,0],[2,8,0],[2,9,0],[2,10,3],[2,11,2],[2,12,1],[2,13,9],[2,14,8],[2,15,10],[2,16,6],[2,17,5],[2,18,5],[2,19,5],[2,20,7],[2,21,4],[2,22,2],[2,23,4],[3,0,7],[3,1,3],[3,2,0],[3,3,0],[3,4,0],[3,5,0],[3,6,0],[3,7,0],[3,8,1],[3,9,0],[3,10,5],[3,11,4],[3,12,7],[3,13,14],[3,14,13],[3,15,12],[3,16,9],[3,17,5],[3,18,5],[3,19,10],[3,20,6],[3,21,4],[3,22,4],[3,23,1],[4,0,1],[4,1,3],[4,2,0],[4,3,0],[4,4,0],[4,5,1],[4,6,0],[4,7,0],[4,8,0],[4,9,2],[4,10,4],[4,11,4],[4,12,2],[4,13,4],[4,14,4],[4,15,14],[4,16,12],[4,17,1],[4,18,8],[4,19,5],[4,20,3],[4,21,7],[4,22,3],[4,23,0],[5,0,2],[5,1,1],[5,2,0],[5,3,3],[5,4,0],[5,5,0],[5,6,0],[5,7,0],[5,8,2],[5,9,0],[5,10,4],[5,11,1],[5,12,5],[5,13,10],[5,14,5],[5,15,7],[5,16,11],[5,17,6],[5,18,0],[5,19,5],[5,20,3],[5,21,4],[5,22,2],[5,23,0],[6,0,1],[6,1,0],[6,2,0],[6,3,0],[6,4,0],[6,5,0],[6,6,0],[6,7,0],[6,8,0],[6,9,0],[6,10,1],[6,11,0],[6,12,2],[6,13,1],[6,14,3],[6,15,4],[6,16,0],[6,17,0],[6,18,0],[6,19,0],[6,20,1],[6,21,2],[6,22,2],[6,23,6]];\n\nconst option = {\n    title: {\n        text: 'Punch Card of Github',\n        link: 'https://github.com/pissang/echarts-next/graphs/punch-card'\n    },\n    legend: {\n        data: ['Punch Card'],\n        left: 'right'\n    },\n    polar: {},\n    angleAxis: {\n        type: 'category',\n        data: hours,\n        boundaryGap: false,\n        splitLine: {\n            show: true,\n            lineStyle: {\n                color: '#999',\n                type: 'dashed'\n            }\n        },\n        axisLine: {\n            show: false\n        }\n    },\n    radiusAxis: {\n        type: 'category',\n        data: days,\n        axisLine: {\n            show: false\n        },\n        axisLabel: {\n            rotate: 45\n        }\n    },\n    series: [{\n        name: 'Punch Card',\n        type: 'scatter',\n        coordinateSystem: 'polar',\n        symbolSize: function (val) {\n            return val[2] * 2;\n        },\n        data: data\n    }]\n};\n", "name": "polar", "title": "极坐标系", "title-en": "Polar"}]}, "radiusAxis": {"desc": "<p>极坐标系的径向轴。</p>\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst data = [];\n\nfor (let i = 0; i <= 360; i++) {\n    const t = i / 180 * Math.PI;\n    const r = Math.sin(2 * t) * Math.cos(2 * t);\n    data.push([r, i]);\n}\n\nconst option = {\n    polar: {\n        center: ['50%', '54%']\n    },\n    tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n            type: 'cross'\n        }\n    },\n    angleAxis: {\n        type: 'value',\n        startAngle: 0,\n    },\n    radiusAxis: {\n        min: 0\n    },\n    series: [{\n        coordinateSystem: 'polar',\n        name: 'line',\n        type: 'line',\n        showSymbol: false,\n        data: data\n    }],\n    animationDuration: 2000\n};\n", "name": "two-number-axis", "title": "双数值轴", "title-en": "Tow Number Axes"}, {"code": "\n\nconst hours = ['12a', '1a', '2a', '3a', '4a', '5a', '6a',\n        '7a', '8a', '9a','10a','11a',\n        '12p', '1p', '2p', '3p', '4p', '5p',\n        '6p', '7p', '8p', '9p', '10p', '11p'];\nconst days = ['Saturday', 'Friday', 'Thursday',\n        'Wednesday', 'Tuesday', 'Monday', 'Sunday'];\n\nconst option = {\n    title: {\n        text: 'Punch Card of Github'\n    },\n    legend: {\n        data: ['Punch Card'],\n        left: 'right'\n    },\n    polar: {},\n    angleAxis: {\n        type: 'category',\n        data: hours,\n        boundaryGap: false\n    },\n    radiusAxis: {\n        type: 'category',\n        data: days\n    }\n};\n", "name": "two-category-axis", "title": "双类目轴", "title-en": "Tow Category Axes"}]}, "angleAxis": {"desc": "<p>极坐标系的角度轴。</p>\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst data = [];\n\nfor (let i = 0; i <= 360; i++) {\n    const t = i / 180 * Math.PI;\n    const r = Math.sin(2 * t) * Math.cos(2 * t);\n    data.push([r, i]);\n}\n\nconst option = {\n    polar: {\n        center: ['50%', '54%']\n    },\n    tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n            type: 'cross'\n        }\n    },\n    angleAxis: {\n        type: 'value',\n        startAngle: 0,\n    },\n    radiusAxis: {\n        min: 0\n    },\n    series: [{\n        coordinateSystem: 'polar',\n        name: 'line',\n        type: 'line',\n        showSymbol: false,\n        data: data\n    }],\n    animationDuration: 2000\n};\n", "name": "two-number-axis", "title": "双数值轴", "title-en": "Two Number Axes"}, {"code": "\n\nconst hours = ['12a', '1a', '2a', '3a', '4a', '5a', '6a',\n        '7a', '8a', '9a','10a','11a',\n        '12p', '1p', '2p', '3p', '4p', '5p',\n        '6p', '7p', '8p', '9p', '10p', '11p'];\nconst days = ['Saturday', 'Friday', 'Thursday',\n        'Wednesday', 'Tuesday', 'Monday', 'Sunday'];\n\nconst option = {\n    title: {\n        text: 'Punch Card of Github'\n    },\n    legend: {\n        data: ['Punch Card'],\n        left: 'right'\n    },\n    polar: {},\n    angleAxis: {\n        type: 'category',\n        data: hours,\n        boundaryGap: false\n    },\n    radiusAxis: {\n        type: 'category',\n        data: days\n    }\n};\n", "name": "two-category-axis", "title": "双类目轴", "title-en": "Two Category Axes"}]}, "radar": {"desc": "<p>雷达图坐标系组件，只适用于<a href=\"#series-radar\">雷达图</a>。该组件等同 ECharts 2 中的 polar 组件。因为 3 中的 polar 被重构为标准的极坐标组件，为避免混淆，雷达图使用 radar 组件作为其坐标系。</p>\n<p>雷达图坐标系与极坐标系不同的是它的每一个轴（indicator 指示器）都是一个单独的维度，可以通过 <a href=\"#radar.name\">name</a>、<a href=\"#radar.axisLine\">axisLine</a>、<a href=\"#radar.axisTick\">axisTick</a>、<a href=\"#radar.axisLabel\">axisLabel</a>、<a href=\"#radar.splitLine\">splitLine</a>、 <a href=\"#radar.splitArea\">splitArea</a> 几个配置项配置指示器坐标轴线的样式。</p>\n<p>下面是一个 radar 组件的一个自定义例子。</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/radar&edit=1&reset=1\" width=\"400\" height=\"400\"><iframe />\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    title: {\n        text: '基础雷达图'\n    },\n    tooltip: {},\n    legend: {\n        data: ['Allocated Budget', 'Actual Spending']\n    },\n    radar: {\n        indicator: [\n            { name: 'sales', max: 6500},\n            { name: 'Administration', max: 16000},\n            { name: 'Information Techology', max: 30000},\n            { name: 'Customer Support', max: 38000},\n            { name: 'Development', max: 52000},\n            { name: 'Marketing', max: 25000}\n        ]\n    },\n    series: [{\n        name: '预算 vs 开销（Budget vs spending）',\n        type: 'radar',\n        data: [\n            {\n                value: [4300, 10000, 28000, 35000, 50000, 19000],\n                name: 'Allocated Budget'\n            },\n            {\n                value: [5000, 14000, 28000, 31000, 42000, 21000],\n                name: 'Actual Spending'\n            }\n        ]\n    }]\n};\n", "name": "radar", "title": "基础雷达图", "title-en": "Radar"}]}, "dataZoom": {"desc": "<p><code class=\"codespan\">dataZoom</code> 组件 用于区域缩放，从而能自由关注细节的数据信息，或者概览数据整体，或者去除离群点的影响。</p>\n<p>现在支持这几种类型的 <code class=\"codespan\">dataZoom</code> 组件：</p>\n<ul>\n<li><p><a href=\"#dataZoom-inside\">内置型数据区域缩放组件（dataZoomInside）</a>：内置于坐标系中，使用户可以在坐标系上通过鼠标拖拽、鼠标滚轮、手指滑动（触屏上）来缩放或漫游坐标系。</p>\n</li>\n<li><p><a href=\"#dataZoom-slider\">滑动条型数据区域缩放组件（dataZoomSlider）</a>：有单独的滑动条，用户在滑动条上进行缩放或漫游。</p>\n</li>\n<li><p><a href=\"#toolbox.feature.dataZoom\">框选型数据区域缩放组件（dataZoomSelect）</a>：提供一个选框进行数据区域缩放。即 <a href=\"#toolbox.feature.dataZoom\">toolbox.feature.dataZoom</a>，配置项在 <code class=\"codespan\">toolbox</code> 中。</p>\n</li>\n</ul>\n<p>如下例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/scatter-dataZoom-all&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p><br></p>\n<hr>\n<p><strong>✦ dataZoom 和 数轴的关系 ✦</strong></p>\n<p><code class=\"codespan\">dataZoom</code> 主要是对 <code class=\"codespan\">数轴（axis）</code> 进行操作（控制数轴的显示范围，或称窗口（window））。</p>\n<blockquote>\n<p>可以通过 <a href=\"#dataZoom.xAxisIndex\">dataZoom.xAxisIndex</a> 或 <a href=\"#dataZoom.yAxisIndex\">dataZoom.yAxisIndex</a> 或 <a href=\"#dataZoom.radiusAxisIndex\">dataZoom.radiusAxisIndex</a> 或 <a href=\"#dataZoom.angleAxisIndex\">dataZoom.angleAxisIndex</a> 来指定 <code class=\"codespan\">dataZoom</code> 控制哪个或哪些数轴。</p>\n</blockquote>\n<p><code class=\"codespan\">dataZoom</code> 组件可 <strong>同时存在多个</strong>，起到共同控制的作用。如果多个 <code class=\"codespan\">dataZoom</code> 组件共同控制同一个数轴，他们会自动联动。</p>\n<p><br></p>\n<hr>\n<p><strong>✦ dataZoom 组件如何影响轴和数据 ✦</strong></p>\n<p><code class=\"codespan\">dataZoom</code> 的运行原理是通过 <code class=\"codespan\">数据过滤</code> 以及在内部设置轴的显示窗口来达到 <code class=\"codespan\">数据窗口缩放</code> 的效果。</p>\n<p>数据过滤模式（<a href=\"#dataZoom.filterMode\">dataZoom.filterMode</a>）的设置不同，效果也不同。</p>\n<p>可选值为：</p>\n<ul>\n<li><p>&#39;filter&#39;：当前数据窗口外的数据，被 <strong>过滤掉</strong>。即 <strong>会</strong> 影响其他轴的数据范围。每个数据项，只要有一个维度在数据窗口外，整个数据项就会被过滤掉。</p>\n</li>\n<li><p>&#39;weakFilter&#39;：当前数据窗口外的数据，被 <strong>过滤掉</strong>。即 <strong>会</strong> 影响其他轴的数据范围。每个数据项，只有当全部维度都在数据窗口同侧外部，整个数据项才会被过滤掉。</p>\n</li>\n<li><p>&#39;empty&#39;：当前数据窗口外的数据，被 <strong>设置为空</strong>。即 <strong>不会</strong> 影响其他轴的数据范围。</p>\n</li>\n<li><p>&#39;none&#39;: 不过滤数据，只改变数轴范围。</p>\n</li>\n</ul>\n<p>如何设置，由用户根据场景和需求自己决定。经验来说：</p>\n<ul>\n<li><p>当『只有 X 轴 或 只有 Y 轴受 <code class=\"codespan\">dataZoom</code> 组件控制』时，常使用 <code class=\"codespan\">filterMode: &#39;filter&#39;</code>，这样能使另一个轴自适应过滤后的数值范围。</p>\n</li>\n<li><p>当『X 轴 Y 轴分别受 <code class=\"codespan\">dataZoom</code> 组件控制』时：</p>\n<ul>\n<li><p>如果 X 轴和 Y 轴是『同等地位的、不应互相影响的』，比如在『双数值轴散点图』中，那么两个轴可都设为 <code class=\"codespan\">fiterMode: &#39;empty&#39;</code>。</p>\n</li>\n<li><p>如果 X 轴为主，Y 轴为辅，比如在『柱状图』中，需要『拖动 <code class=\"codespan\">dataZoomX</code> 改变 X 轴过滤柱子时，Y 轴的范围也自适应剩余柱子的高度』，『拖动 <code class=\"codespan\">dataZoomY</code> 改变 Y 轴过滤柱子时，X 轴范围不受影响』，那么就 X轴设为 <code class=\"codespan\">fiterMode: &#39;filter&#39;</code>，Y 轴设为 <code class=\"codespan\">fiterMode: &#39;empty&#39;</code>，即主轴 <code class=\"codespan\">&#39;filter&#39;</code>，辅轴 <code class=\"codespan\">&#39;empty&#39;</code>。</p>\n</li>\n</ul>\n</li>\n</ul>\n<p>下面是个具体例子：</p>\n<pre><code class=\"lang-javascript\">option = {\n    dataZoom: [\n        {\n            id: &#39;dataZoomX&#39;,\n            type: &#39;slider&#39;,\n            xAxisIndex: [0],\n            filterMode: &#39;filter&#39;\n        },\n        {\n            id: &#39;dataZoomY&#39;,\n            type: &#39;slider&#39;,\n            yAxisIndex: [0],\n            filterMode: &#39;empty&#39;\n        }\n    ],\n    xAxis: {type: &#39;value&#39;},\n    yAxis: {type: &#39;value&#39;},\n    series{\n        type: &#39;bar&#39;,\n        data: [\n            // 第一列对应 X 轴，第二列对应 Y 轴。\n            [12, 24, 36],\n            [90, 80, 70],\n            [3, 9, 27],\n            [1, 11, 111]\n        ]\n    }\n}\n</code></pre>\n<p>上例中，<code class=\"codespan\">dataZoomX</code> 的 <code class=\"codespan\">filterMode</code> 设置为 <code class=\"codespan\">&#39;filter&#39;</code>。于是，假设当用户拖拽 <code class=\"codespan\">dataZoomX</code>（不去动 <code class=\"codespan\">dataZoomY</code>）导致其 valueWindow 变为 <code class=\"codespan\">[2, 50]</code> 时，<code class=\"codespan\">dataZoomX</code> 对 series.data 的第一列进行遍历，窗口外的整项去掉，最终得到的 series.data 为：</p>\n<pre><code class=\"lang-javascript\">[\n    // 第一列对应 X 轴，第二列对应 Y 轴。\n    [12, 24, 36],\n    // [90, 80, 70] 整项被过滤掉，因为 90 在 dataWindow 之外。\n    [3, 9, 27]\n    // [1, 11, 111] 整项被过滤掉，因为 1 在 dataWindow 之外。\n]\n</code></pre>\n<p>过滤前，series.data 中对应 Y 轴的值有 <code class=\"codespan\">24</code>、<code class=\"codespan\">80</code>、<code class=\"codespan\">9</code>、<code class=\"codespan\">11</code>，过滤后，只剩下 <code class=\"codespan\">24</code> 和 <code class=\"codespan\">9</code>，那么 Y 轴的显示范围就会自动改变以适应剩下的这两个值的显示（如果 Y 轴没有被设置 <code class=\"codespan\">min</code>、<code class=\"codespan\">max</code> 固定其显示范围的话）。</p>\n<p>所以，<code class=\"codespan\">filterMode: &#39;filter&#39;</code> 的效果是：过滤数据后使另外的轴也能自动适应当前数据的范围。</p>\n<p>再从头来，上例中 <code class=\"codespan\">dataZoomY</code> 的 <code class=\"codespan\">filterMode</code> 设置为 <code class=\"codespan\">&#39;empty&#39;</code>。于是，假设当用户拖拽 <code class=\"codespan\">dataZoomY</code>（不去动 <code class=\"codespan\">dataZoomX</code>）导致其 dataWindow 变为 <code class=\"codespan\">[10, 60]</code> 时，<code class=\"codespan\">dataZoomY</code> 对 series.data 的第二列进行遍历，窗口外的值被设置为 empty （即替换为 NaN，这样设置为空的项，其所对应柱形，在 X 轴还有占位，只是不显示出来）。最终得到的 series.data 为：</p>\n<pre><code class=\"lang-javascript\">[\n    // 第一列对应 X 轴，第二列对应 Y 轴。\n    [12, 24, 36],\n    [90, NaN, 70], // 设置为 empty (NaN)\n    [3, NaN, 27],  // 设置为 empty (NaN)\n    [1, 11, 111]\n]\n</code></pre>\n<p>这时，series.data 中对应于 X 轴的值仍然全部保留不受影响，为 <code class=\"codespan\">12</code>、<code class=\"codespan\">90</code>、<code class=\"codespan\">3</code>、<code class=\"codespan\">1</code>。那么用户对 <code class=\"codespan\">dataZoomY</code> 的拖拽操作不会影响到 X 轴的范围。这样的效果，对于离群点（outlier）过滤功能，比较清晰。</p>\n<p>如下面的例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/bar-dataZoom-filterMode&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n\n\n\n<p>另外，如果在任一个数轴上设置了 <code class=\"codespan\">min</code>、<code class=\"codespan\">max</code>（如设置 <code class=\"codespan\">yAxis: {min: 0, max: 400}</code>），那么这个数轴无论如何也不会被其他数轴的 dataZoom 行为影响了。</p>\n<p><br></p>\n<hr>\n<p><strong>✦ 数据窗口的设置 ✦</strong></p>\n<p><code class=\"codespan\">dataZoom</code> 的数据窗口范围的设置，目前支持两种形式：</p>\n<ul>\n<li><p>百分比形式：即设置 <a href=\"#dataZoom.start\">dataZoom.start</a> 和 <a href=\"#dataZoom.end\">dataZoom.end</a>。</p>\n</li>\n<li><p>绝对数值形式：即设置 <a href=\"#dataZoom.startValue\">dataZoom.startValue</a> 和 <a href=\"#dataZoom.endValue\">dataZoom.endValue</a>。</p>\n</li>\n</ul>\n<p>注意：当使用百分比形式指定 <code class=\"codespan\">dataZoom</code> 范围时，且处于如下场景（或类似场景）中，<code class=\"codespan\">dataZoom</code> 的结果是和 <code class=\"codespan\">dataZoom</code> 组件的定义顺序相关的。</p>\n<pre><code class=\"lang-javascript\">option = {\n    dataZoom: [\n        {\n            id: &#39;dataZoomX&#39;,\n            type: &#39;slider&#39;,\n            xAxisIndex: [0],\n            filterMode: &#39;filter&#39;, // 设定为 &#39;filter&#39; 从而 X 的窗口变化会影响 Y 的范围。\n            start: 30,\n            end: 70\n        },\n        {\n            id: &#39;dataZoomY&#39;,\n            type: &#39;slider&#39;,\n            yAxisIndex: [0],\n            filterMode: &#39;empty&#39;,\n            start: 20,\n            end: 80\n        }\n    ],\n    xAxis: {\n        type: &#39;value&#39;\n    },\n    yAxis: {\n        type: &#39;value&#39;\n        // yAxis 中并没有使用 min、max 来显示限定轴的显示范围。\n    },\n    series{\n        type: &#39;bar&#39;,\n        data: [\n            // 第一列对应 X 轴，第二列对应 Y 轴。\n            [12, 24, 36],\n            [90, 80, 70],\n            [3, 9, 27],\n            [1, 11, 111]\n        ]\n    }\n}\n</code></pre>\n<p>在上例中，<code class=\"codespan\">dataZoomY</code> 的 <code class=\"codespan\">start: 20, end: 80</code> 到底表示什么意思？</p>\n<ul>\n<li><p>如果 <code class=\"codespan\">yAxis.min</code>、<code class=\"codespan\">yAxis.max</code> 进行了直接设置：</p>\n<p>  那么 <code class=\"codespan\">dataZoomY</code> 的 <code class=\"codespan\">start: 20, end: 80</code> 表示 <code class=\"codespan\">yAxis.min</code> ~ <code class=\"codespan\">yAxis.max</code> 的 <code class=\"codespan\">20%</code> 到 <code class=\"codespan\">80%</code>。</p>\n</li>\n<li><p>如果 <code class=\"codespan\">yAxis.min</code>、<code class=\"codespan\">yAxis.max</code> 没有设置：</p>\n<ul>\n<li><p>如果 <code class=\"codespan\">dataZoomX</code> 设置为 <code class=\"codespan\">filterMode: &#39;empty&#39;</code>：</p>\n<p>  那么 <code class=\"codespan\">dataZoomY</code> 的 <code class=\"codespan\">start: 20, end: 80</code> 表示 series.data 中 <code class=\"codespan\">dataMinY</code> ~ <code class=\"codespan\">dataMaxY</code>（即上例中的 <code class=\"codespan\">9</code> ~ <code class=\"codespan\">80</code>）的 <code class=\"codespan\">20%</code> 到 <code class=\"codespan\">80%</code>。</p>\n</li>\n<li><p>如果 <code class=\"codespan\">dataZoomX</code> 设置为 <code class=\"codespan\">filterMode: &#39;filter&#39;</code>：</p>\n<p>  那么，因为 <code class=\"codespan\">dataZoomX</code> 定义 <code class=\"codespan\">dataZoomY</code> 组件之前，所以 <code class=\"codespan\">dataZoomX</code> 的 <code class=\"codespan\">start: 30, end: 70</code> 表示全部数据的 <code class=\"codespan\">30%</code> 到 <code class=\"codespan\">70%</code>，而 <code class=\"codespan\">dataZoomY</code> 组件的 <code class=\"codespan\">start: 20, end: 80</code> 表示经过 <code class=\"codespan\">dataZoomX</code> 过滤处理后，所得数据集的 <code class=\"codespan\">20%</code> 到 <code class=\"codespan\">80%</code>。</p>\n<p>  如果需要改变这种处理顺序，那么改变 <code class=\"codespan\">dataZoomX</code> 和 <code class=\"codespan\">dataZoomY</code> 在 option 中的出现顺序即可。</p>\n</li>\n</ul>\n</li>\n</ul>\n"}, "dataZoom-inside": {"desc": "<p><strong>内置型数据区域缩放组件（dataZoomInside）</strong></p>\n<p>（参考<a href=\"#dataZoom\">数据区域缩放组件（dataZoom）的介绍</a>）</p>\n<p>所谓『内置』，即内置在坐标系中。</p>\n<ul>\n<li>平移：在坐标系中滑动拖拽进行数据区域平移。</li>\n<li>缩放：<ul>\n<li>PC端：鼠标在坐标系范围内滚轮滚动（MAC触控板类同）</li>\n<li>移动端：在移动端触屏上，支持两指滑动缩放。</li>\n</ul>\n</li>\n</ul>\n\n", "exampleBaseOptions": [{"code": "\nconst data = [[\"2014-07-14\",156],[\"2014-07-15\",140],[\"2014-07-16\",133],[\"2014-07-17\",186],[\"2014-07-18\",182],[\"2014-07-19\",106],[\"2014-07-20\",119],[\"2014-07-21\",68],[\"2014-07-22\",54],[\"2014-07-23\",82],[\"2014-07-24\",90],[\"2014-07-25\",134],[\"2014-07-26\",188],[\"2014-07-27\",194],[\"2014-07-28\",159],[\"2014-07-29\",159],[\"2014-07-30\",169],[\"2014-07-31\",244],[\"2014-08-01\",199],[\"2014-08-02\",163],[\"2014-08-03\",149],[\"2014-08-05\",80],[\"2014-08-06\",67],[\"2014-08-07\",162],[\"2014-08-08\",140],[\"2014-08-09\",143],[\"2014-08-10\",125],[\"2014-08-11\",76],[\"2014-08-12\",119],[\"2014-08-13\",70],[\"2014-08-14\",104],[\"2014-08-15\",109],[\"2014-08-16\",159],[\"2014-08-17\",124],[\"2014-08-18\",135],[\"2014-08-19\",150],[\"2014-08-20\",164],[\"2014-08-21\",169],[\"2014-08-22\",83],[\"2014-08-23\",155],[\"2014-08-24\",75],[\"2014-08-25\",59],[\"2014-08-26\",78],[\"2014-08-27\",136],[\"2014-08-28\",103],[\"2014-08-29\",104],[\"2014-08-30\",176],[\"2014-08-31\",89],[\"2014-09-01\",127],[\"2014-09-03\",54],[\"2014-09-04\",100],[\"2014-09-05\",140],[\"2014-09-06\",186],[\"2014-09-07\",200],[\"2014-09-08\",61],[\"2014-09-09\",109],[\"2014-09-10\",111],[\"2014-09-11\",114],[\"2014-09-12\",97],[\"2014-09-13\",94],[\"2014-09-14\",66],[\"2014-09-15\",54],[\"2014-09-16\",87],[\"2014-09-17\",80],[\"2014-09-18\",84],[\"2014-09-19\",117],[\"2014-09-20\",168],[\"2014-09-21\",129],[\"2014-09-22\",127],[\"2014-09-23\",64],[\"2014-09-24\",60],[\"2014-09-25\",144],[\"2014-09-26\",170],[\"2014-09-27\",58],[\"2014-09-28\",87],[\"2014-09-29\",70],[\"2014-09-30\",53],[\"2014-10-01\",92],[\"2014-10-02\",78],[\"2014-10-03\",123],[\"2014-10-04\",95],[\"2014-10-05\",54],[\"2014-10-06\",68],[\"2014-10-07\",200],[\"2014-10-08\",314],[\"2014-10-09\",379],[\"2014-10-10\",346],[\"2014-10-11\",233],[\"2014-10-14\",80],[\"2014-10-15\",73],[\"2014-10-16\",76],[\"2014-10-17\",132],[\"2014-10-18\",211],[\"2014-10-19\",289],[\"2014-10-20\",250],[\"2014-10-21\",82],[\"2014-10-22\",99],[\"2014-10-23\",163],[\"2014-10-24\",267],[\"2014-10-25\",353],[\"2014-10-26\",78],[\"2014-10-27\",72],[\"2014-10-28\",88],[\"2014-10-29\",140],[\"2014-10-30\",206],[\"2014-10-31\",204],[\"2014-11-01\",65],[\"2014-11-03\",59],[\"2014-11-04\",150],[\"2014-11-05\",79],[\"2014-11-07\",63],[\"2014-11-08\",93],[\"2014-11-09\",80],[\"2014-11-10\",95],[\"2014-11-11\",59],[\"2014-11-13\",65],[\"2014-11-14\",77],[\"2014-11-15\",143],[\"2014-11-16\",98],[\"2014-11-17\",64],[\"2014-11-18\",93],[\"2014-11-19\",282],[\"2014-11-23\",155],[\"2014-11-24\",94],[\"2014-11-25\",196],[\"2014-11-26\",293],[\"2014-11-27\",83],[\"2014-11-28\",114],[\"2014-11-29\",276],[\"2014-12-01\",54],[\"2014-12-02\",65],[\"2014-12-03\",51],[\"2014-12-05\",62],[\"2014-12-06\",89],[\"2014-12-07\",65],[\"2014-12-08\",82],[\"2014-12-09\",276],[\"2014-12-10\",153],[\"2014-12-11\",52],[\"2014-12-13\",69],[\"2014-12-14\",113],[\"2014-12-15\",82],[\"2014-12-17\",99],[\"2014-12-19\",53],[\"2014-12-22\",103],[\"2014-12-23\",100],[\"2014-12-25\",73],[\"2014-12-26\",155],[\"2014-12-27\",243],[\"2014-12-28\",155],[\"2014-12-29\",125],[\"2014-12-30\",65],[\"2015-01-01\",65],[\"2015-01-02\",79],[\"2015-01-03\",200],[\"2015-01-04\",226],[\"2015-01-05\",122],[\"2015-01-06\",60],[\"2015-01-07\",85],[\"2015-01-08\",190],[\"2015-01-09\",105],[\"2015-01-10\",208],[\"2015-01-11\",59],[\"2015-01-12\",160],[\"2015-01-13\",211],[\"2015-01-14\",265],[\"2015-01-15\",386],[\"2015-01-16\",118],[\"2015-01-17\",89],[\"2015-01-18\",94],[\"2015-01-19\",77],[\"2015-01-20\",113],[\"2015-01-22\",143],[\"2015-01-23\",257],[\"2015-01-24\",117],[\"2015-01-25\",185],[\"2015-01-26\",119],[\"2015-01-28\",65],[\"2015-01-29\",87],[\"2015-01-31\",60],[\"2015-02-01\",108],[\"2015-02-02\",188],[\"2015-02-03\",143],[\"2015-02-05\",62],[\"2015-02-06\",100],[\"2015-02-09\",152],[\"2015-02-10\",166],[\"2015-02-11\",55],[\"2015-02-12\",59],[\"2015-02-13\",175],[\"2015-02-14\",293],[\"2015-02-15\",326],[\"2015-02-16\",153],[\"2015-02-18\",73],[\"2015-02-19\",267],[\"2015-02-20\",183],[\"2015-02-21\",394],[\"2015-02-22\",158],[\"2015-02-23\",86],[\"2015-02-24\",207]];\n\nconst option = {\n    color: ['#3398DB'],\n    title: {\n        text: 'Beijing AQI'\n    },\n    tooltip: {\n        trigger: 'axis'\n    },\n    xAxis: {\n        data: data.map(function (item) {\n            return item[0];\n        })\n    },\n    yAxis: {\n        splitLine: {\n            show: false\n        }\n    },\n    dataZoom: [{\n        start: 80,\n        type: 'inside'\n    }, {\n        start: 80\n    }],\n    series: {\n        name: 'Beijing AQI',\n        type: 'bar',\n        data: data.map(function (item) {\n            return item[1];\n        })\n    }\n};\n", "name": "data-zoom-inside", "title": "使用拖拽滚轮平移缩放", "title-en": "DataZoom with Pan and Zoom"}]}, "dataZoom-slider": {"desc": "<p><strong>滑动条型数据区域缩放组件（dataZoomInside）</strong></p>\n<p>滑动条型数据区域缩放组件提供了数据缩略图显示，缩放，刷选，拖拽，点击快速定位等数据筛选的功能。下图显示了该组件可交互部分</p>\n<p><img width=\"600\" height=\"auto\" src=\"documents/asset/img/dataZoom-zone.png\"></p>\n\n", "exampleBaseOptions": [{"code": "\nconst data = [[\"2014-07-14\",156],[\"2014-07-15\",140],[\"2014-07-16\",133],[\"2014-07-17\",186],[\"2014-07-18\",182],[\"2014-07-19\",106],[\"2014-07-20\",119],[\"2014-07-21\",68],[\"2014-07-22\",54],[\"2014-07-23\",82],[\"2014-07-24\",90],[\"2014-07-25\",134],[\"2014-07-26\",188],[\"2014-07-27\",194],[\"2014-07-28\",159],[\"2014-07-29\",159],[\"2014-07-30\",169],[\"2014-07-31\",244],[\"2014-08-01\",199],[\"2014-08-02\",163],[\"2014-08-03\",149],[\"2014-08-05\",80],[\"2014-08-06\",67],[\"2014-08-07\",162],[\"2014-08-08\",140],[\"2014-08-09\",143],[\"2014-08-10\",125],[\"2014-08-11\",76],[\"2014-08-12\",119],[\"2014-08-13\",70],[\"2014-08-14\",104],[\"2014-08-15\",109],[\"2014-08-16\",159],[\"2014-08-17\",124],[\"2014-08-18\",135],[\"2014-08-19\",150],[\"2014-08-20\",164],[\"2014-08-21\",169],[\"2014-08-22\",83],[\"2014-08-23\",155],[\"2014-08-24\",75],[\"2014-08-25\",59],[\"2014-08-26\",78],[\"2014-08-27\",136],[\"2014-08-28\",103],[\"2014-08-29\",104],[\"2014-08-30\",176],[\"2014-08-31\",89],[\"2014-09-01\",127],[\"2014-09-03\",54],[\"2014-09-04\",100],[\"2014-09-05\",140],[\"2014-09-06\",186],[\"2014-09-07\",200],[\"2014-09-08\",61],[\"2014-09-09\",109],[\"2014-09-10\",111],[\"2014-09-11\",114],[\"2014-09-12\",97],[\"2014-09-13\",94],[\"2014-09-14\",66],[\"2014-09-15\",54],[\"2014-09-16\",87],[\"2014-09-17\",80],[\"2014-09-18\",84],[\"2014-09-19\",117],[\"2014-09-20\",168],[\"2014-09-21\",129],[\"2014-09-22\",127],[\"2014-09-23\",64],[\"2014-09-24\",60],[\"2014-09-25\",144],[\"2014-09-26\",170],[\"2014-09-27\",58],[\"2014-09-28\",87],[\"2014-09-29\",70],[\"2014-09-30\",53],[\"2014-10-01\",92],[\"2014-10-02\",78],[\"2014-10-03\",123],[\"2014-10-04\",95],[\"2014-10-05\",54],[\"2014-10-06\",68],[\"2014-10-07\",200],[\"2014-10-08\",314],[\"2014-10-09\",379],[\"2014-10-10\",346],[\"2014-10-11\",233],[\"2014-10-14\",80],[\"2014-10-15\",73],[\"2014-10-16\",76],[\"2014-10-17\",132],[\"2014-10-18\",211],[\"2014-10-19\",289],[\"2014-10-20\",250],[\"2014-10-21\",82],[\"2014-10-22\",99],[\"2014-10-23\",163],[\"2014-10-24\",267],[\"2014-10-25\",353],[\"2014-10-26\",78],[\"2014-10-27\",72],[\"2014-10-28\",88],[\"2014-10-29\",140],[\"2014-10-30\",206],[\"2014-10-31\",204],[\"2014-11-01\",65],[\"2014-11-03\",59],[\"2014-11-04\",150],[\"2014-11-05\",79],[\"2014-11-07\",63],[\"2014-11-08\",93],[\"2014-11-09\",80],[\"2014-11-10\",95],[\"2014-11-11\",59],[\"2014-11-13\",65],[\"2014-11-14\",77],[\"2014-11-15\",143],[\"2014-11-16\",98],[\"2014-11-17\",64],[\"2014-11-18\",93],[\"2014-11-19\",282],[\"2014-11-23\",155],[\"2014-11-24\",94],[\"2014-11-25\",196],[\"2014-11-26\",293],[\"2014-11-27\",83],[\"2014-11-28\",114],[\"2014-11-29\",276],[\"2014-12-01\",54],[\"2014-12-02\",65],[\"2014-12-03\",51],[\"2014-12-05\",62],[\"2014-12-06\",89],[\"2014-12-07\",65],[\"2014-12-08\",82],[\"2014-12-09\",276],[\"2014-12-10\",153],[\"2014-12-11\",52],[\"2014-12-13\",69],[\"2014-12-14\",113],[\"2014-12-15\",82],[\"2014-12-17\",99],[\"2014-12-19\",53],[\"2014-12-22\",103],[\"2014-12-23\",100],[\"2014-12-25\",73],[\"2014-12-26\",155],[\"2014-12-27\",243],[\"2014-12-28\",155],[\"2014-12-29\",125],[\"2014-12-30\",65],[\"2015-01-01\",65],[\"2015-01-02\",79],[\"2015-01-03\",200],[\"2015-01-04\",226],[\"2015-01-05\",122],[\"2015-01-06\",60],[\"2015-01-07\",85],[\"2015-01-08\",190],[\"2015-01-09\",105],[\"2015-01-10\",208],[\"2015-01-11\",59],[\"2015-01-12\",160],[\"2015-01-13\",211],[\"2015-01-14\",265],[\"2015-01-15\",386],[\"2015-01-16\",118],[\"2015-01-17\",89],[\"2015-01-18\",94],[\"2015-01-19\",77],[\"2015-01-20\",113],[\"2015-01-22\",143],[\"2015-01-23\",257],[\"2015-01-24\",117],[\"2015-01-25\",185],[\"2015-01-26\",119],[\"2015-01-28\",65],[\"2015-01-29\",87],[\"2015-01-31\",60],[\"2015-02-01\",108],[\"2015-02-02\",188],[\"2015-02-03\",143],[\"2015-02-05\",62],[\"2015-02-06\",100],[\"2015-02-09\",152],[\"2015-02-10\",166],[\"2015-02-11\",55],[\"2015-02-12\",59],[\"2015-02-13\",175],[\"2015-02-14\",293],[\"2015-02-15\",326],[\"2015-02-16\",153],[\"2015-02-18\",73],[\"2015-02-19\",267],[\"2015-02-20\",183],[\"2015-02-21\",394],[\"2015-02-22\",158],[\"2015-02-23\",86],[\"2015-02-24\",207]];\n\nconst option = {\n    color: ['#3398DB'],\n    tooltip: {\n        trigger: 'axis'\n    },\n    xAxis: {\n        data: data.map(function (item) {\n            return item[0];\n        })\n    },\n    yAxis: {\n        splitLine: {\n            show: false\n        }\n    },\n    dataZoom: [{\n    }],\n    series: {\n        name: 'Beijing AQI',\n        type: 'bar',\n        data: data.map(function (item) {\n            return item[1];\n        })\n    }\n};\n", "name": "data-zoom-slider", "title": "滑块缩放的 dataZoom", "title-en": "DataZoom with Slider"}]}, "visualMap": {"desc": "<p><code class=\"codespan\">visualMap</code> 是视觉映射组件，用于进行『视觉编码』，也就是将数据映射到视觉元素（视觉通道）。</p>\n<p>视觉元素可以是：<br></p>\n<ul>\n<li><code class=\"codespan\">symbol</code>: 图元的图形类别。</li>\n<li><code class=\"codespan\">symbolSize</code>: 图元的大小。</li>\n<li><code class=\"codespan\">color</code>: 图元的颜色。</li>\n<li><code class=\"codespan\">colorAlpha</code>: 图元的颜色的透明度。</li>\n<li><code class=\"codespan\">opacity</code>: 图元以及其附属物（如文字标签）的透明度。</li>\n<li><code class=\"codespan\">colorLightness</code>: 颜色的明暗度，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n<li><code class=\"codespan\">colorSaturation</code>: 颜色的饱和度，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n<li><code class=\"codespan\">colorHue</code>: 颜色的色调，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n</ul>\n<p><code class=\"codespan\">visualMap</code> 组件可以定义多个，从而可以同时对数据中的多个维度进行视觉映射。</p>\n<p><code class=\"codespan\">visualMap</code> 组件可以定义为 <a href=\"#visualMap-piecewise\">分段型（visualMapPiecewise）</a> 或 <a href=\"#visualMap-continuous\">连续型（visualMapContinuous）</a>，通过 <code class=\"codespan\">type</code> 来区分。例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    visualMap: [\n        { // 第一个 visualMap 组件\n            type: &#39;continuous&#39;, // 定义为连续型 visualMap\n            ...\n        },\n        { // 第二个 visualMap 组件\n            type: &#39;piecewise&#39;, // 定义为分段型 visualMap\n            ...\n        }\n    ],\n    ...\n};\n</code></pre>\n<p><br>\n<strong>✦ 视觉映射方式的配置 ✦</strong></p>\n<p>既然是『数据』到『视觉元素』的映射，<code class=\"codespan\">visualMap</code> 中可以指定数据的『哪个维度』（参见<a href=\"#visualMap.dimension\">visualMap.dimension</a>）映射到哪些『视觉元素』（参见<a href=\"#visualMap.inRange\">visualMap.inRange</a> 和 <a href=\"#visualMap.outOfRange\">visualMap.outOfRange</a>）中。</p>\n<p><br>\n在 visualMap 组件所控制的 series 中，如果 series 中某个数据项需要避开 visualMap 映射，可以这么配置：</p>\n<pre><code>series: {\n    type: &#39;...&#39;,\n    data: [\n        {name: &#39;Shanghai&#39;, value: 251},\n        {name: &#39;Haikou&#39;, value: 21},\n        // 设置 `visualMap: false` 则 visualMap 不对此项进行控制，此时系列\n        // 可使用自身的视觉参数（color/symbol/ ...控制此项的显示。\n        {name: &#39;Beijing&#39;, value: 821, visualMap: false},\n        ...\n    ]\n}\n</code></pre><p><br>\n<strong>✦ 与 ECharts2 中 dataRange 的关系 ✦</strong></p>\n<p><code class=\"codespan\">visualMap</code> 是由 ECharts2 中的 <code class=\"codespan\">dataRange</code> 组件改名以及扩展而来。ECharts3里 <code class=\"codespan\">option</code> 中的 <code class=\"codespan\">dataRange</code> 配置项仍然被兼容，会自动转换成 <code class=\"codespan\">visualMap</code> 配置项。在option中推荐写 <code class=\"codespan\">visualMap</code> 而非 <code class=\"codespan\">dataRange</code>。</p>\n"}, "visualMap-continuous": {"desc": "<p><strong>连续型视觉映射组件（visualMapContinuous）</strong></p>\n<p>（参考<a href=\"#visualMap\">视觉映射组件（visualMap）的介绍</a>）</p>\n<p><code class=\"codespan\">visualMapContinuous</code>中，可以通过 <a href=\"#visualMap.calculable\">visualMap.calculable</a> 来显示或隐藏手柄（手柄能拖拽改变值域）。</p>\n\n", "exampleBaseOptions": [{"code": "\n// https://echarts.apache.org/examples/zh/editor.html?c=heatmap-large\noption = {\"tooltip\":{},\"xAxis\":{\"type\":\"category\",\"data\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]},\"yAxis\":{\"type\":\"category\",\"data\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19]},\"visualMap\":{\"min\":0,\"max\":100,\"calculable\":true,\"realtime\":false,\"inRange\":{\"color\":[\"#313695\",\"#4575b4\",\"#74add1\",\"#abd9e9\",\"#e0f3f8\",\"#ffffbf\",\"#fee090\",\"#fdae61\",\"#f46d43\",\"#d73027\",\"#a50026\"]}},\"series\":[{\"name\":\"Gaussian\",\"type\":\"heatmap\",\"data\":[[0,0,50],[0,1,50],[0,2,50],[0,3,50],[0,4,50],[0,5,50],[0,6,50],[0,7,50],[0,8,50],[0,9,50],[0,10,50],[0,11,50],[0,12,50],[0,13,50],[0,14,50],[0,15,50],[0,16,50],[0,17,50],[0,18,50],[0,19,50],[0,20,50],[1,0,54.994209375000004],[1,1,54.98855287535156],[1,2,54.983669047750006],[1,3,54.980227686550776],[1,4,54.978760914000006],[1,5,54.97964233398438],[1,6,54.983074003125],[1,7,54.98908121921484],[1,8,54.997515127],[1,9,55.00806314130469],[1,10,55.020267187500004],[1,11,55.0335497593164],[1,12,55.047247794],[1,13,55.0606543648125],[1,14,55.073068190875],[1,15,55.08385096435546],[1,16,55.092492495],[1,17,55.09868367200781],[1,18,55.10239724325],[1,19,55.103976411832036],[1,20,55.104231250000005],[2,0,59.9144],[2,1,59.87254178725],[2,2,59.83612736],[2,3,59.80991875325],[2,4,59.797737472],[2,5,59.80230078125],[2,6,59.825115776000004],[2,7,59.866431231250004],[2,8,59.925247232000004],[2,9,59.999382583250004],[2,10,60.08560000000001],[2,11,60.17978907725],[2,12,60.27720704],[2,13,60.37277727325],[2,14,60.461445632],[2,15,60.53859453125],[2,16,60.60051481600001],[2,17,60.64493541125],[2,18,60.67161075200001],[2,19,60.68296599325001],[2,20,60.6848],[3,0,64.60082187500001],[3,1,64.4705362889961],[3,2,64.35634390175],[3,3,64.27246293910156],[3,4,64.230427154],[3,5,64.23854693603516],[3,6,64.301550051125],[3,7,64.42040201135937],[3,8,64.592306075],[3,9,64.81088287663673],[3,10,65.0665296875],[3,11,65.34695930592969],[3,12,65.637918578],[3,13,65.92408654830078],[3,14,66.190152240875],[3,15,66.4220720703125],[3,16,66.60850688299999],[3,17,66.74243862852734],[3,18,66.82296666124999],[3,19,66.85728367200781],[3,20,66.86283125],[4,0,68.8416],[4,1,68.557701681],[4,2,68.30702156800001],[4,3,68.11922918100001],[4,4,68.01867264],[4,5,68.023140625],[4,6,68.143015296],[4,7,68.380816173],[4,8,68.731134976],[4,9,69.180961425],[4,10,69.7104],[4,11,70.29377766100001],[4,12,70.90114252800001],[4,13,71.50015352100002],[4,14,72.05836096],[4,15,72.545878125],[4,16,72.938443776],[4,17,73.22087563299999],[4,18,73.39091481599999],[4,19,73.46346124499999],[4,20,73.4752],[5,0,72.412109375],[5,1,71.90412197265626],[5,2,71.45227109375],[5,3,71.10730203857422],[5,4,70.91138125],[5,5,70.89576721191406],[5,6,71.07918007812499],[5,7,71.46687003173828],[5,8,72.050384375],[5,9,72.80803334960937],[5,10,73.7060546875],[5,11,74.70047689208985],[5,12,75.73968125],[5,13,76.76766257324219],[5,14,77.72798867187501],[5,15,78.5684585571289],[5,16,79.246459375],[5,17,79.73502207031248],[5,18,80.02957578124999],[5,19,80.15540096435545],[5,20,80.17578125],[6,0,75.10759999999999],[6,1,74.306365026875],[6,2,73.588477184],[6,3,73.030288488875],[6,4,72.69610342399999],[6,5,72.634326171875],[6,6,72.87470864],[6,7,73.426699275875],[6,8,74.278892672],[6,9,75.399579960875],[6,10,76.7384],[6,11,78.229091346875],[6,12,79.79334502399999],[6,13,81.345758073875],[6,14,82.79988790399999],[6,15,84.075407421875],[6,16,85.10636096],[6,17,85.85052099087498],[6,18,86.299845632],[6,19,86.49203694087497],[6,20,86.5232],[7,0,76.769071875],[7,1,75.61228988726954],[7,2,74.56835686375],[7,3,73.74219509090625],[7,4,73.223470242],[7,5,73.08077093505861],[7,6,73.357375684125],[7,7,74.06860724378906],[7,8,75.20077434699999],[7,9,76.71170083616016],[7,10,78.53284218750001],[7,11,80.57298942873437],[7,12,82.72356045],[7,13,84.86547870807422],[7,14,86.877639323875],[7,15,88.64696257324218],[7,16,90.080034771],[7,17,91.11633654830075],[7,18,91.74305852324999],[7,19,92.01150436481247],[7,20,92.05508124999999],[8,0,77.30239999999999],[8,1,75.739096288],[8,2,74.318210048],[8,3,73.17437552],[8,4,72.42448998399999],[8,5,72.1595],[8,6,72.43833036800001],[8,7,73.283955808],[8,8,74.68161536],[8,9,76.579169504],[8,10,78.8896],[8,11,81.495652448],[8,12,84.256621568],[8,13,87.0172792],[8,14,89.618945024],[8,15,91.9127],[8,16,93.774742528],[8,17,95.12388732799997],[8,18,95.94120704],[8,19,96.29181654399996],[8,20,96.3488],[9,0,76.690709375],[9,1,74.68461634627343],[9,2,72.84836233175],[9,3,71.34554847203516],[9,4,70.32070372999999],[9,5,69.88829931640626],[9,6,70.124509509125],[9,7,71.06171886594922],[9,8,72.685775831],[9,9,74.93599273472657],[9,10,77.70789218750001],[9,11,80.85869986680079],[9,12,84.21558369799999],[9,13,87.58663942873439],[9,14,90.774622596875],[9,15,93.59342689208985],[9,16,95.88730891100002],[9,17,97.55285930592964],[9,18,98.56372032724998],[9,19,98.99804975931636],[9,20,99.06873125],[10,0,75],[10,1,72.5318484375],[10,2,70.25680000000001],[10,3,68.36488593749999],[10,4,67.0272],[10,5,66.3818359375],[10,6,66.5232],[10,7,67.4946984375],[10,8,69.2848],[10,9,71.8264734375],[10,10,75],[10,11,78.63916093750001],[10,12,82.5408],[10,13,86.47776093750001],[10,14,90.2152],[10,15,93.5302734375],[10,16,96.2352],[10,17,98.20369843749995],[10,18,99.40079999999998],[10,19,99.91603593749994],[10,20,100],[11,0,72.378021875],[11,1,69.44673325310546],[11,2,66.72599725775],[11,3,64.42817184539844],[11,4,62.748488433999995],[11,5,61.847702941894525],[11,6,61.838750433125],[11,7,62.77740336915625],[11,8,64.656933467],[11,9,67.40677716374609],[11,10,70.89520468749998],[11,11,74.93599273472657],[11,12,79.299100754],[11,13,83.72535083616017],[11,14,87.94511121087498],[11,15,91.70098334960937],[11,16,94.774492675],[11,17,97.01678287663667],[11,18,98.38331383324999],[11,19,98.9725631413046],[11,20,99.06873125],[12,0,69.04639999999999],[12,1,65.669172041],[12,2,62.51293567999999],[12,3,59.807031077],[12,4,57.767232512],[12,5,56.57501562499999],[12,6,56.361431935999995],[12,7,57.195590644999996],[12,8,59.077747712],[12,9,61.937002217],[12,10,65.6336],[12,11,69.965844581],[12,12,74.68161536],[12,13,79.49449309700002],[12,14,84.10449267199999],[12,15,88.223403125],[12,16,91.60473497600002],[12,17,94.07827482499994],[12,18,95.58924723199998],[12,19,96.24208387699991],[12,20,96.3488],[13,0,65.28600937499999],[13,1,61.497287028703106],[13,2,57.932315881749986],[13,3,54.83122884943358],[13,4,52.425841313999975],[13,5,50.91555895996092],[13,6,50.44844821612499],[13,7,51.10746930434764],[13,8,52.901871894999985],[13,9,55.763753369156234],[13,10,59.549779687499985],[13,11,64.0480688659492],[13,12,68.99023705799999],[13,13,74.06860724378906],[13,14,78.95858052587498],[13,15,83.34617003173828],[13,16,86.96069742300001],[13,17,89.6126520113593],[13,18,91.23671248124997],[13,19,91.93993121921474],[13,20,92.05508124999999],[14,0,61.415600000000005],[14,1,57.264924029125005],[14,2,53.33296140800001],[14,3,49.86404046912501],[14,4,47.100920320000014],[14,5,45.257486328125005],[14,6,44.497094816000015],[14,7,44.91656696612501],[14,8,46.535831936000015],[14,9,49.29321918312502],[14,10,53.04640000000001],[14,11,57.578978259125016],[14,12,62.612730368000015],[14,13,67.82549443412502],[14,14,72.87470864000001],[14,15,77.42659882812501],[14,16,81.19101529600002],[14,17,83.96191880112494],[14,18,85.66351577599998],[14,19,86.40204275312492],[14,20,86.52320000000002],[15,0,57.763671875],[15,1,53.312397229003906],[15,2,49.06741484375],[15,3,45.27069145507813],[15,4,42.17058125],[15,5,39.99156951904297],[15,6,38.910067578125],[15,7,39.03625895996093],[15,8,40.401996875],[15,9,42.95475294189453],[15,10,46.5576171875],[15,11,50.995349316406255],[15,12,55.98648125000001],[15,13,61.20147093505861],[15,14,66.286907421875],[15,15,70.89576721191406],[15,16,74.72372187500001],[15,17,77.55149693603508],[15,18,79.29328203124997],[15,19,80.05119233398428],[15,20,80.17578125],[16,0,54.633599999999994],[16,1,49.950476159999994],[16,2,45.45472614399999],[16,3,41.37986803199999],[16,4,37.974610943999984],[16,5,35.469999999999985],[16,6,34.052920319999984],[16,7,33.84596006399998],[16,8,34.89363251199998],[16,9,37.15495718399999],[16,10,40.50239999999998],[16,11,44.727172479999986],[16,12,49.550889983999994],[16,13,54.643588992000005],[16,14,59.64810342399998],[16,15,64.21079999999999],[16,16,68.01867264],[16,17,70.84279590399991],[16,18,72.58813747199996],[16,19,73.34972966399988],[16,20,73.47519999999999],[17,0,52.26200937500006],[17,1,47.417614852445375],[17,2,42.73643318375007],[17,3,38.43829799326961],[17,4,34.767499282000074],[17,5,31.957741455078203],[17,6,30.20367171912508],[17,7,29.638978849433673],[17,8,30.32106232700008],[17,9,32.222271845398524],[17,10,35.227717187500076],[17,11,39.13964847203523],[17,12,43.68840677000008],[17,13,48.549945090906334],[17,14,53.36991973887506],[17,15,57.79435203857428],[17,16,61.506860431000064],[17,17,64.27246293910154],[17,18,65.98795000325003],[17,19,66.73882768655074],[17,20,66.86283125000006],[18,0,50.770400000000016],[18,1,45.83042317175002],[18,2,41.02573452800002],[18,3,36.55840193375003],[18,4,32.66432614400002],[18,5,29.57643359375003],[18,6,27.494561408000028],[18,7,26.56203463175002],[18,8,26.84893568000003],[18,9,28.342066007750034],[18,10,30.941600000000026],[18,11,34.46443108175002],[18,12,38.654210048000024],[18,13,43.198075613750035],[18,14,47.75007718400001],[18,15,51.961289843750016],[18,16,55.51662156800003],[18,17,58.178312651749955],[18,18,59.83612735999999],[18,19,60.56423779774992],[18,20,60.68480000000002],[19,0,50.110021875000086],[19,1,45.127380337464935],[19,2,40.24985442175009],[19,3,35.65901485244542],[19,4,31.5795074100001],[19,5,28.24084722900401],[19,6,25.84595527912511],[19,7,24.546437028703227],[19,8,24.424603291000114],[19,9,25.482233253105584],[19,10,27.636079687500104],[19,11,30.720116346273546],[19,12,34.494527538000106],[19,13,38.661439887269644],[19,14,42.88739627687509],[19,15,46.832571972656346],[19,16,50.1867329310001],[19,17,52.71193628899612],[19,18,54.29197303725006],[19,19,54.98855287535156],[19,20,55.10423125000008],[20,0,50],[20,1,45.005790624999996],[20,2,40.0856],[20,3,35.399178125],[20,4,31.158399999999997],[20,5,27.587890625],[20,6,24.892400000000002],[20,7,23.230928125],[20,8,22.6976],[20,9,23.309290625000003],[20,10,25],[20,11,27.621978125000002],[20,12,30.9536],[20,13,34.713990625000015],[20,14,38.58439999999999],[20,15,42.236328125],[20,16,45.36640000000001],[20,17,47.73799062499994],[20,18,49.22959999999998],[20,19,49.889978124999914],[20,20,50]],\"emphasis\":{\"itemStyle\":{\"borderColor\":\"#333\",\"borderWidth\":1}},\"progressive\":1000,\"animation\":false}]};\n", "name": "visual-map-heatmap", "title": "热力图颜色线性映射", "title-en": "Heatmap - Linear Map"}]}, "visualMap-piecewise": {"desc": "<p><strong>分段型视觉映射组件（visualMapPiecewise）</strong></p>\n<p>（参考<a href=\"#visualMap\">视觉映射组件（visualMap）的介绍</a>）</p>\n<p>分段型视觉映射组件，有三种模式：</p>\n<ul>\n<li><strong>连续型数据平均分段</strong>: 依据 <a href=\"#visualMap-piecewise.splitNumber\">visualMap-piecewise.splitNumber</a> 来自动平均分割成若干块。</li>\n<li><strong>连续型数据自定义分段</strong>: 依据 <a href=\"#visualMap-piecewise.pieces\">visualMap-piecewise.pieces</a> 来定义每块范围。</li>\n<li><strong>离散数据根据类别分段</strong>: 类别定义在 <a href=\"#visualMap-piecewise.categories\">visualMap-piecewise.categories</a> 中。</li>\n</ul>\n<p><br>\n<br></p>\n\n", "exampleBaseOptions": [{"code": "\n// https://echarts.apache.org/examples/zh/editor.html?c=heatmap-large\noption = {\"tooltip\":{},\"xAxis\":{\"type\":\"category\",\"data\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]},\"yAxis\":{\"type\":\"category\",\"data\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19]},\"visualMap\":{\"type\":\"piecewise\",\"splitNumber\": 8,\"min\":0,\"max\":100,\"calculable\":true,\"realtime\":false,\"inRange\":{\"color\":[\"#313695\",\"#4575b4\",\"#74add1\",\"#abd9e9\",\"#e0f3f8\",\"#ffffbf\",\"#fee090\",\"#fdae61\",\"#f46d43\",\"#d73027\",\"#a50026\"]}},\"series\":[{\"name\":\"Gaussian\",\"type\":\"heatmap\",\"data\":[[0,0,50],[0,1,50],[0,2,50],[0,3,50],[0,4,50],[0,5,50],[0,6,50],[0,7,50],[0,8,50],[0,9,50],[0,10,50],[0,11,50],[0,12,50],[0,13,50],[0,14,50],[0,15,50],[0,16,50],[0,17,50],[0,18,50],[0,19,50],[0,20,50],[1,0,54.994209375000004],[1,1,54.98855287535156],[1,2,54.983669047750006],[1,3,54.980227686550776],[1,4,54.978760914000006],[1,5,54.97964233398438],[1,6,54.983074003125],[1,7,54.98908121921484],[1,8,54.997515127],[1,9,55.00806314130469],[1,10,55.020267187500004],[1,11,55.0335497593164],[1,12,55.047247794],[1,13,55.0606543648125],[1,14,55.073068190875],[1,15,55.08385096435546],[1,16,55.092492495],[1,17,55.09868367200781],[1,18,55.10239724325],[1,19,55.103976411832036],[1,20,55.104231250000005],[2,0,59.9144],[2,1,59.87254178725],[2,2,59.83612736],[2,3,59.80991875325],[2,4,59.797737472],[2,5,59.80230078125],[2,6,59.825115776000004],[2,7,59.866431231250004],[2,8,59.925247232000004],[2,9,59.999382583250004],[2,10,60.08560000000001],[2,11,60.17978907725],[2,12,60.27720704],[2,13,60.37277727325],[2,14,60.461445632],[2,15,60.53859453125],[2,16,60.60051481600001],[2,17,60.64493541125],[2,18,60.67161075200001],[2,19,60.68296599325001],[2,20,60.6848],[3,0,64.60082187500001],[3,1,64.4705362889961],[3,2,64.35634390175],[3,3,64.27246293910156],[3,4,64.230427154],[3,5,64.23854693603516],[3,6,64.301550051125],[3,7,64.42040201135937],[3,8,64.592306075],[3,9,64.81088287663673],[3,10,65.0665296875],[3,11,65.34695930592969],[3,12,65.637918578],[3,13,65.92408654830078],[3,14,66.190152240875],[3,15,66.4220720703125],[3,16,66.60850688299999],[3,17,66.74243862852734],[3,18,66.82296666124999],[3,19,66.85728367200781],[3,20,66.86283125],[4,0,68.8416],[4,1,68.557701681],[4,2,68.30702156800001],[4,3,68.11922918100001],[4,4,68.01867264],[4,5,68.023140625],[4,6,68.143015296],[4,7,68.380816173],[4,8,68.731134976],[4,9,69.180961425],[4,10,69.7104],[4,11,70.29377766100001],[4,12,70.90114252800001],[4,13,71.50015352100002],[4,14,72.05836096],[4,15,72.545878125],[4,16,72.938443776],[4,17,73.22087563299999],[4,18,73.39091481599999],[4,19,73.46346124499999],[4,20,73.4752],[5,0,72.412109375],[5,1,71.90412197265626],[5,2,71.45227109375],[5,3,71.10730203857422],[5,4,70.91138125],[5,5,70.89576721191406],[5,6,71.07918007812499],[5,7,71.46687003173828],[5,8,72.050384375],[5,9,72.80803334960937],[5,10,73.7060546875],[5,11,74.70047689208985],[5,12,75.73968125],[5,13,76.76766257324219],[5,14,77.72798867187501],[5,15,78.5684585571289],[5,16,79.246459375],[5,17,79.73502207031248],[5,18,80.02957578124999],[5,19,80.15540096435545],[5,20,80.17578125],[6,0,75.10759999999999],[6,1,74.306365026875],[6,2,73.588477184],[6,3,73.030288488875],[6,4,72.69610342399999],[6,5,72.634326171875],[6,6,72.87470864],[6,7,73.426699275875],[6,8,74.278892672],[6,9,75.399579960875],[6,10,76.7384],[6,11,78.229091346875],[6,12,79.79334502399999],[6,13,81.345758073875],[6,14,82.79988790399999],[6,15,84.075407421875],[6,16,85.10636096],[6,17,85.85052099087498],[6,18,86.299845632],[6,19,86.49203694087497],[6,20,86.5232],[7,0,76.769071875],[7,1,75.61228988726954],[7,2,74.56835686375],[7,3,73.74219509090625],[7,4,73.223470242],[7,5,73.08077093505861],[7,6,73.357375684125],[7,7,74.06860724378906],[7,8,75.20077434699999],[7,9,76.71170083616016],[7,10,78.53284218750001],[7,11,80.57298942873437],[7,12,82.72356045],[7,13,84.86547870807422],[7,14,86.877639323875],[7,15,88.64696257324218],[7,16,90.080034771],[7,17,91.11633654830075],[7,18,91.74305852324999],[7,19,92.01150436481247],[7,20,92.05508124999999],[8,0,77.30239999999999],[8,1,75.739096288],[8,2,74.318210048],[8,3,73.17437552],[8,4,72.42448998399999],[8,5,72.1595],[8,6,72.43833036800001],[8,7,73.283955808],[8,8,74.68161536],[8,9,76.579169504],[8,10,78.8896],[8,11,81.495652448],[8,12,84.256621568],[8,13,87.0172792],[8,14,89.618945024],[8,15,91.9127],[8,16,93.774742528],[8,17,95.12388732799997],[8,18,95.94120704],[8,19,96.29181654399996],[8,20,96.3488],[9,0,76.690709375],[9,1,74.68461634627343],[9,2,72.84836233175],[9,3,71.34554847203516],[9,4,70.32070372999999],[9,5,69.88829931640626],[9,6,70.124509509125],[9,7,71.06171886594922],[9,8,72.685775831],[9,9,74.93599273472657],[9,10,77.70789218750001],[9,11,80.85869986680079],[9,12,84.21558369799999],[9,13,87.58663942873439],[9,14,90.774622596875],[9,15,93.59342689208985],[9,16,95.88730891100002],[9,17,97.55285930592964],[9,18,98.56372032724998],[9,19,98.99804975931636],[9,20,99.06873125],[10,0,75],[10,1,72.5318484375],[10,2,70.25680000000001],[10,3,68.36488593749999],[10,4,67.0272],[10,5,66.3818359375],[10,6,66.5232],[10,7,67.4946984375],[10,8,69.2848],[10,9,71.8264734375],[10,10,75],[10,11,78.63916093750001],[10,12,82.5408],[10,13,86.47776093750001],[10,14,90.2152],[10,15,93.5302734375],[10,16,96.2352],[10,17,98.20369843749995],[10,18,99.40079999999998],[10,19,99.91603593749994],[10,20,100],[11,0,72.378021875],[11,1,69.44673325310546],[11,2,66.72599725775],[11,3,64.42817184539844],[11,4,62.748488433999995],[11,5,61.847702941894525],[11,6,61.838750433125],[11,7,62.77740336915625],[11,8,64.656933467],[11,9,67.40677716374609],[11,10,70.89520468749998],[11,11,74.93599273472657],[11,12,79.299100754],[11,13,83.72535083616017],[11,14,87.94511121087498],[11,15,91.70098334960937],[11,16,94.774492675],[11,17,97.01678287663667],[11,18,98.38331383324999],[11,19,98.9725631413046],[11,20,99.06873125],[12,0,69.04639999999999],[12,1,65.669172041],[12,2,62.51293567999999],[12,3,59.807031077],[12,4,57.767232512],[12,5,56.57501562499999],[12,6,56.361431935999995],[12,7,57.195590644999996],[12,8,59.077747712],[12,9,61.937002217],[12,10,65.6336],[12,11,69.965844581],[12,12,74.68161536],[12,13,79.49449309700002],[12,14,84.10449267199999],[12,15,88.223403125],[12,16,91.60473497600002],[12,17,94.07827482499994],[12,18,95.58924723199998],[12,19,96.24208387699991],[12,20,96.3488],[13,0,65.28600937499999],[13,1,61.497287028703106],[13,2,57.932315881749986],[13,3,54.83122884943358],[13,4,52.425841313999975],[13,5,50.91555895996092],[13,6,50.44844821612499],[13,7,51.10746930434764],[13,8,52.901871894999985],[13,9,55.763753369156234],[13,10,59.549779687499985],[13,11,64.0480688659492],[13,12,68.99023705799999],[13,13,74.06860724378906],[13,14,78.95858052587498],[13,15,83.34617003173828],[13,16,86.96069742300001],[13,17,89.6126520113593],[13,18,91.23671248124997],[13,19,91.93993121921474],[13,20,92.05508124999999],[14,0,61.415600000000005],[14,1,57.264924029125005],[14,2,53.33296140800001],[14,3,49.86404046912501],[14,4,47.100920320000014],[14,5,45.257486328125005],[14,6,44.497094816000015],[14,7,44.91656696612501],[14,8,46.535831936000015],[14,9,49.29321918312502],[14,10,53.04640000000001],[14,11,57.578978259125016],[14,12,62.612730368000015],[14,13,67.82549443412502],[14,14,72.87470864000001],[14,15,77.42659882812501],[14,16,81.19101529600002],[14,17,83.96191880112494],[14,18,85.66351577599998],[14,19,86.40204275312492],[14,20,86.52320000000002],[15,0,57.763671875],[15,1,53.312397229003906],[15,2,49.06741484375],[15,3,45.27069145507813],[15,4,42.17058125],[15,5,39.99156951904297],[15,6,38.910067578125],[15,7,39.03625895996093],[15,8,40.401996875],[15,9,42.95475294189453],[15,10,46.5576171875],[15,11,50.995349316406255],[15,12,55.98648125000001],[15,13,61.20147093505861],[15,14,66.286907421875],[15,15,70.89576721191406],[15,16,74.72372187500001],[15,17,77.55149693603508],[15,18,79.29328203124997],[15,19,80.05119233398428],[15,20,80.17578125],[16,0,54.633599999999994],[16,1,49.950476159999994],[16,2,45.45472614399999],[16,3,41.37986803199999],[16,4,37.974610943999984],[16,5,35.469999999999985],[16,6,34.052920319999984],[16,7,33.84596006399998],[16,8,34.89363251199998],[16,9,37.15495718399999],[16,10,40.50239999999998],[16,11,44.727172479999986],[16,12,49.550889983999994],[16,13,54.643588992000005],[16,14,59.64810342399998],[16,15,64.21079999999999],[16,16,68.01867264],[16,17,70.84279590399991],[16,18,72.58813747199996],[16,19,73.34972966399988],[16,20,73.47519999999999],[17,0,52.26200937500006],[17,1,47.417614852445375],[17,2,42.73643318375007],[17,3,38.43829799326961],[17,4,34.767499282000074],[17,5,31.957741455078203],[17,6,30.20367171912508],[17,7,29.638978849433673],[17,8,30.32106232700008],[17,9,32.222271845398524],[17,10,35.227717187500076],[17,11,39.13964847203523],[17,12,43.68840677000008],[17,13,48.549945090906334],[17,14,53.36991973887506],[17,15,57.79435203857428],[17,16,61.506860431000064],[17,17,64.27246293910154],[17,18,65.98795000325003],[17,19,66.73882768655074],[17,20,66.86283125000006],[18,0,50.770400000000016],[18,1,45.83042317175002],[18,2,41.02573452800002],[18,3,36.55840193375003],[18,4,32.66432614400002],[18,5,29.57643359375003],[18,6,27.494561408000028],[18,7,26.56203463175002],[18,8,26.84893568000003],[18,9,28.342066007750034],[18,10,30.941600000000026],[18,11,34.46443108175002],[18,12,38.654210048000024],[18,13,43.198075613750035],[18,14,47.75007718400001],[18,15,51.961289843750016],[18,16,55.51662156800003],[18,17,58.178312651749955],[18,18,59.83612735999999],[18,19,60.56423779774992],[18,20,60.68480000000002],[19,0,50.110021875000086],[19,1,45.127380337464935],[19,2,40.24985442175009],[19,3,35.65901485244542],[19,4,31.5795074100001],[19,5,28.24084722900401],[19,6,25.84595527912511],[19,7,24.546437028703227],[19,8,24.424603291000114],[19,9,25.482233253105584],[19,10,27.636079687500104],[19,11,30.720116346273546],[19,12,34.494527538000106],[19,13,38.661439887269644],[19,14,42.88739627687509],[19,15,46.832571972656346],[19,16,50.1867329310001],[19,17,52.71193628899612],[19,18,54.29197303725006],[19,19,54.98855287535156],[19,20,55.10423125000008],[20,0,50],[20,1,45.005790624999996],[20,2,40.0856],[20,3,35.399178125],[20,4,31.158399999999997],[20,5,27.587890625],[20,6,24.892400000000002],[20,7,23.230928125],[20,8,22.6976],[20,9,23.309290625000003],[20,10,25],[20,11,27.621978125000002],[20,12,30.9536],[20,13,34.713990625000015],[20,14,38.58439999999999],[20,15,42.236328125],[20,16,45.36640000000001],[20,17,47.73799062499994],[20,18,49.22959999999998],[20,19,49.889978124999914],[20,20,50]],\"emphasis\":{\"itemStyle\":{\"borderColor\":\"#333\",\"borderWidth\":1}},\"progressive\":1000,\"animation\":false}]};\n", "name": "visual-map-heatmap", "title": "热力图颜色分段映射", "title-en": "Heatmap - Step Map"}]}, "tooltip": {"desc": "<p>提示框组件。</p>\n<hr>\n<p><strong>提示框组件的通用介绍：</strong></p>\n<p>提示框组件可以设置在多种地方：</p>\n<ul>\n<li><p>可以设置在全局，即 <a href=\"#tooltip\">tooltip</a></p>\n</li>\n<li><p>可以设置在坐标系中，即 <a href=\"#grid.tooltip\">grid.tooltip</a>、<a href=\"#polar.tooltip\">polar.tooltip</a>、<a href=\"#single.tooltip\">single.tooltip</a></p>\n</li>\n<li><p>可以设置在系列中，即 <a href=\"#series.tooltip\">series.tooltip</a></p>\n</li>\n<li><p>可以设置在系列的每个数据项中，即 <a href=\"#series.data.tooltip\">series.data.tooltip</a></p>\n</li>\n</ul>\n<hr>\n\n\n\n", "exampleBaseOptions": [{"code": "\nvar base = +new Date(2016, 9, 3);\nvar oneDay = 24 * 3600 * 1000;\nvar valueBase = Math.random() * 300;\nvar valueBase2 = Math.random() * 50;\nvar data = [];\nvar data2 = [];\n\nfor (var i = 1; i < 10; i++) {\n    var now = new Date(base += oneDay);\n    var dayStr = [now.getFullYear(), now.getMonth() + 1, now.getDate()].join('-');\n\n    valueBase = Math.round((Math.random() - 0.5) * 20 + valueBase);\n    valueBase <= 0 && (valueBase = Math.random() * 300);\n    data.push([dayStr, valueBase]);\n\n    valueBase2 = Math.round((Math.random() - 0.5) * 20 + valueBase2);\n    valueBase2 <= 0 && (valueBase2 = Math.random() * 50);\n    data2.push([dayStr, valueBase2]);\n}\n\nconst option = {\n    legend: {\n        top: 'bottom',\n        data: ['意向']\n    },\n    tooltip: {\n        triggerOn: 'none',\n        alwaysShowContent: true,\n        position: function (pt) {\n            return [pt[0], 130];\n        }\n    },\n    xAxis: {\n        type: 'time',\n        // boundaryGap: [0, 0],\n        axisPointer: {\n            value: '2016-10-7',\n            snap: true,\n            label: {\n                show: true,\n                formatter: function (params) {\n                    return echarts.format.formatTime('yyyy-MM-dd', params.value);\n                }\n            },\n            handle: {\n                show: true\n            }\n        },\n        splitLine: {\n            show: false\n        }\n    },\n    yAxis: {\n        type: 'value',\n        axisTick: {\n            inside: true\n        },\n        splitLine: {\n            show: false\n        },\n        axisLabel: {\n            inside: true\n        },\n        z: 10\n    },\n    grid: {\n        top: 110,\n        left: 15,\n        right: 15,\n        height: 160\n    },\n    series: [\n        {\n            name: '模拟数据',\n            type: 'line',\n            smooth: true,\n            symbol: 'circle',\n            symbolSize: 5,\n            sampling: 'lttb',\n            itemStyle: {\n                color: '#8ec6ad'\n            },\n            stack: 'a',\n            areaStyle: {\n            },\n            data: data\n        },\n        {\n            name: '模拟数据',\n            type: 'line',\n            smooth: true,\n            stack: 'a',\n            symbol: 'circle',\n            symbolSize: 5,\n            sampling: 'lttb',\n            itemStyle: {\n                color: '#d68262'\n            },\n            areaStyle: {\n            },\n            data: data2\n        }\n\n    ]\n};\n", "name": "tooltip", "title": "提示框", "title-en": "<PERSON><PERSON><PERSON>"}]}, "axisPointer": {"desc": "<p>这是坐标轴指示器（axisPointer）的全局公用设置。</p>\n\n\n<hr>\n<p>坐标轴指示器是指示坐标轴当前刻度的工具。</p>\n<p>如下例，鼠标悬浮到图上，可以出现标线和刻度文本。</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/candlestick-axisPointer&edit=1&reset=1\" width=\"600\" height=\"450\"><iframe />\n\n\n<p>上例中，使用了 <a href=\"#axisPointer.link\">axisPointer.link</a> 来关联不同的坐标系中的 axisPointer。</p>\n<p>坐标轴指示器也有适合触屏的交互方式，如下：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-tooltip-touch&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p>坐标轴指示器在多轴的场景能起到辅助作用：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=multiple-y-axis&edit=1&reset=1\" width=\"600\" height=\"300\"><iframe />\n\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=multiple-x-axis&edit=1&reset=1\" width=\"600\" height=\"300\"><iframe />\n\n\n\n\n<hr>\n<blockquote>\n<p><strong>注意：</strong>\n一般来说，axisPointer 的具体配置项会配置在各个轴中（如 <a href=\"#xAxis.axisPointer\">xAxis.axisPointer</a>）或者 <code class=\"codespan\">tooltip</code> 中（如 <a href=\"#tooltip.axisPointer\">tooltip.axisPointer</a>）。</p>\n</blockquote>\n<blockquote>\n<p>但是这几个选项只能配置在全局的 axisPointer 中：<a href=\"#axisPointer.triggerOn\">axisPointer.triggerOn</a>、<a href=\"#axisPointer.link\">axisPointer.link</a>。</p>\n</blockquote>\n<hr>\n<p><strong>如何显示 axisPointer：</strong></p>\n<p>直角坐标系 <a href=\"#grid\">grid</a>、极坐标系 <a href=\"#polar\">polar</a>、单轴坐标系 <a href=\"#single\">single</a> 中的每个轴都自己的 axisPointer。</p>\n<p>他们的 axisPointer 默认不显示。有两种方法可以让他们显示：</p>\n<ul>\n<li><p>设置轴上的 <code class=\"codespan\">axisPointer.show</code>（例如 <a href=\"#xAxis.axisPointer.show\">xAxis.axisPointer.show</a>）为 <code class=\"codespan\">true</code>，则显示此轴的 axisPointer。</p>\n</li>\n<li><p>设置 <a href=\"#tooltip.trigger\">tooltip.trigger</a> 设置为 <code class=\"codespan\">&#39;axis&#39;</code> 或者 <a href=\"#tooltip.axisPointer.type\">tooltip.axisPointer.type</a> 设置为 <code class=\"codespan\">&#39;cross&#39;</code>，则此时坐标系会自动选择显示哪个轴的 axisPointer，也可以使用 <a href=\"#tooltip.axisPointer.axis\">tooltip.axisPointer.axis</a> 改变这种选择。注意，轴上如果设置了 axisPointer，会覆盖此设置。</p>\n</li>\n</ul>\n<hr>\n<p><strong>如何显示 axisPointer 的 label：</strong></p>\n<p>axisPointer 的 label 默认不显示（也就是默认只显示指示线），除非：</p>\n<ul>\n<li><p>设置轴上的 <code class=\"codespan\">axisPointer.label.show</code>（例如 <a href=\"#xAxis.axisPointer.show\">xAxis.axisPointer.label.show</a>）为 <code class=\"codespan\">true</code>，则显示此轴的 axisPointer 的 label。</p>\n</li>\n<li><p>设置 <a href=\"#tooltip.axisPointer.type\">tooltip.axisPointer.type</a> 为 <code class=\"codespan\">&#39;cross&#39;</code> 时会自动显示 axisPointer 的 label。</p>\n</li>\n</ul>\n<hr>\n<p><strong>关于触屏的 axisPointer 的设置</strong></p>\n<p>设置轴上的 <code class=\"codespan\">axisPointer.handle.show</code>（例如 <a href=\"#xAxis.axisPointer.handle.show\">xAxis.axisPointer.handle.show</a> 为 <code class=\"codespan\">true</code> 则会显示出此 axisPointer 的拖拽按钮。（polar 坐标系暂不支持此功能）。</p>\n<p><strong>注意：</strong>\n如果发现此时 tooltip 效果不良好，可设置 <a href=\"#tooltip.triggerOn\">tooltip.triggerOn</a> 为 <code class=\"codespan\">&#39;none&#39;</code>（于是效果为：手指按住按钮则显示 tooltip，松开按钮则隐藏 tooltip），或者 <a href=\"#tooltip.alwaysShowContent\">tooltip.alwaysShowContent</a> 为 <code class=\"codespan\">true</code>（效果为 tooltip 一直显示）。</p>\n<p>参见<a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=line-tooltip-touch&amp;edit=1&amp;reset=1\" target=\"_blank\">例子</a>。</p>\n<hr>\n<p><strong>自动吸附到数据（snap）</strong></p>\n<p>对于数值轴、时间轴，如果开启了 <a href=\"#xAxis.axisPointer.snap\">snap</a>，则 axisPointer 会自动吸附到最近的点上。</p>\n<hr>\n<hr>\n", "exampleBaseOptions": [{"code": "\nvar base = +new Date(2016, 9, 3);\nvar oneDay = 24 * 3600 * 1000;\nvar valueBase = Math.random() * 300;\nvar valueBase2 = Math.random() * 50;\nvar data = [];\nvar data2 = [];\n\nfor (var i = 1; i < 10; i++) {\n    var now = new Date(base += oneDay);\n    var dayStr = [now.getFullYear(), now.getMonth() + 1, now.getDate()].join('-');\n\n    valueBase = Math.round((Math.random() - 0.5) * 20 + valueBase);\n    valueBase <= 0 && (valueBase = Math.random() * 300);\n    data.push([dayStr, valueBase]);\n\n    valueBase2 = Math.round((Math.random() - 0.5) * 20 + valueBase2);\n    valueBase2 <= 0 && (valueBase2 = Math.random() * 50);\n    data2.push([dayStr, valueBase2]);\n}\n\noption = {\n    legend: {\n        top: 'bottom',\n        data: ['意向']\n    },\n    tooltip: {\n        triggerOn: 'none',\n        position: function (pt) {\n            return [pt[0], 130];\n        }\n    },\n    xAxis: {\n        type: 'time',\n        // boundaryGap: [0, 0],\n        axisPointer: {\n            value: '2016-10-7',\n            snap: true,\n            label: {\n                show: true,\n                formatter: function (params) {\n                    return echarts.format.formatTime('yyyy-MM-dd', params.value);\n                }\n            },\n            handle: {\n                show: true\n            }\n        },\n        splitLine: {\n            show: false\n        }\n    },\n    yAxis: {\n        type: 'value',\n        axisTick: {\n            inside: true\n        },\n        splitLine: {\n            show: false\n        },\n        axisLabel: {\n            inside: true,\n            formatter: '{value}\\n'\n        },\n        z: 10\n    },\n    grid: {\n        top: 110,\n        left: 15,\n        right: 15,\n        height: 160\n    },\n    series: [\n        {\n            name: '模拟数据',\n            type: 'line',\n            smooth: true,\n            symbol: 'circle',\n            symbolSize: 5,\n            sampling: 'lttb',\n            itemStyle: {\n                color: '#8ec6ad'\n            },\n            stack: 'a',\n            areaStyle: {\n            },\n            data: data\n        },\n        {\n            name: '模拟数据',\n            type: 'line',\n            smooth: true,\n            stack: 'a',\n            symbol: 'circle',\n            symbolSize: 5,\n            sampling: 'lttb',\n            itemStyle: {\n                color: '#d68262'\n            },\n            areaStyle: {\n            },\n            data: data2\n        }\n\n    ]\n};\n\n", "name": "axis-pointer", "title": "坐标轴指示器", "title-en": "<PERSON> Pointer"}]}, "toolbox": {"desc": "<p>工具栏。内置有<a href=\"#toolbox.feature.saveAsImage\">导出图片</a>，<a href=\"#toolbox.feature.dataView\">数据视图</a>，<a href=\"#toolbox.feature.magicType\">动态类型切换</a>，<a href=\"#toolbox.feature.dataZoom\">数据区域缩放</a>，<a href=\"#toolbox.feature.reset\">重置</a>五个工具。</p>\n<p><strong>如下示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-marker&reset=1&edit=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\noption = {\n    toolbox: {\n        show: true,\n        feature: {\n            dataZoom: {\n                yAxisIndex: 'none'\n            },\n            dataView: {readOnly: false},\n            magicType: {type: ['line', 'bar']},\n            restore: {},\n            saveAsImage: {}\n        }\n    },\n    xAxis: {\n        type: 'category',\n        boundaryGap: false,\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n    },\n    yAxis: {\n        type: 'value',\n        axisLabel: {\n            formatter: '{value} °C'\n        }\n    },\n    series: [\n        {\n            name: '最高气温',\n            type: 'line',\n            data: [11, 11, 15, 13, 12, 13, 10],\n            markPoint: {\n                data: [\n                    {type: 'max', name: 'Max'},\n                    {type: 'min', name: 'Min'}\n                ]\n            },\n            markLine: {\n                data: [\n                    {type: 'average', name: 'Avg'}\n                ]\n            }\n        },\n        {\n            name: '最低气温',\n            type: 'line',\n            data: [1, -2, 2, 5, 3, 2, 0],\n            markPoint: {\n                data: [\n                    {name: '周最低', value: -2, xAxis: 1, yAxis: -1.5}\n                ]\n            },\n            markLine: {\n                data: [\n                    {type: 'average', name: 'Avg'},\n                    [{\n                        symbol: 'none',\n                        x: '90%',\n                        yAxis: 'max'\n                    }, {\n                        symbol: 'circle',\n                        label: {\n                            position: 'start',\n                            formatter: 'Max'\n                        },\n                        type: 'max',\n                        name: 'Top'\n                    }]\n                ]\n            }\n        }\n    ]\n};\n", "title": "工具栏", "title-en": "Toolbox"}]}, "brush": {"desc": "<p><code class=\"codespan\">brush</code> 是区域选择组件，用户可以选择图中一部分数据，从而便于向用户展示被选中数据，或者他们的一些统计计算结果。</p>\n<p><br></p>\n<hr>\n<p><strong>刷子的类型和启动按钮</strong></p>\n<p>目前 <code class=\"codespan\">brush</code> 组件支持的图表类型：<code class=\"codespan\">scatter</code>、<code class=\"codespan\">bar</code>、<code class=\"codespan\">candlestick</code>（<code class=\"codespan\">parallel</code> 本身自带刷选功能，但并非由 brush 组件来提供）。</p>\n<p>点击 <code class=\"codespan\">toolbox</code> 中的按钮，能够进行『区域选择』、『清除选择』等操作。</p>\n<p><br>\n<code class=\"codespan\">横向刷子</code> 的示例如下（点击 <code class=\"codespan\">toolbox</code> 中的按钮启动刷选）：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=candlestick-brush&edit=1&reset=1\" width=\"800\" height=\"500\"><iframe />\n\n\n<p><br>\n<code class=\"codespan\">bar</code> 图中的 <code class=\"codespan\">brush</code>（点击 <code class=\"codespan\">toolbox</code> 中的按钮启动刷选）：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=bar-brush&edit=1&reset=1\" width=\"800\" height=\"400\"><iframe />\n\n\n\n<p>启动 <code class=\"codespan\">brush</code> 的按钮既可以在 <code class=\"codespan\">toolbox</code> 中指定（参见 <a href=\"#toolbox.feature.brush.type\">toolbox.feature.brush.type</a>），也可以在 <code class=\"codespan\">brush</code> 组件的配置中指定（参见 <a href=\"#brush.toolbox\">brush.toolbox</a>）。</p>\n<p>支持这几种选框：<code class=\"codespan\">矩形刷子</code>，<code class=\"codespan\">任意形状刷子</code>，<code class=\"codespan\">横向刷子</code>，<code class=\"codespan\">纵向刷子</code>。参见 <a href=\"#brush.toolbox\">brush.toolbox</a>。</p>\n<p>可以使用 <code class=\"codespan\">保持选择</code> 按钮，切换单选和多选模式。</p>\n<ul>\n<li>单选即同时只能存在一个选框，可单击空白区域消除选框。</li>\n<li>多选即同时可存在多个选框，单击空白区域不能消除选框，需要点击『清除按钮』消除线框。</li>\n</ul>\n<p><br></p>\n<hr>\n<p><strong>刷选和坐标系的关系</strong></p>\n<p>可以设置 <code class=\"codespan\">brush</code> 是『全局的』还是『属于坐标系的』。</p>\n<p><strong>全局 brush</strong></p>\n<p>在 echarts 实例中任意地方刷选。这是默认情况。如果没有指定为『坐标系 brush』，就是『全局 brush』。</p>\n<p><strong>坐标系 brush</strong></p>\n<p>在 指定的坐标系中刷选。选框可以跟随坐标系的缩放和平移（roam 和 dataZoom）而移动。</p>\n<p>坐标系 brush 实际更为常用，尤其是在 geo 中。</p>\n<p>通过指定 <a href=\"#brush.geoIndex\">brush.geoIndex</a> 或 <a href=\"#brush.xAxisIndex\">brush.xAxisIndex</a> 或 <a href=\"#brush.yAxisIndex\">brush.yAxisIndex</a> 来规定可以在哪些坐标系中进行刷选。</p>\n<p>这几个配置项的取值可以是：</p>\n<ul>\n<li><code class=\"codespan\">&#39;all&#39;</code>，表示所有</li>\n<li><code class=\"codespan\">number</code>，如 <code class=\"codespan\">0</code>，表示这个 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">Array</code>，如 <code class=\"codespan\">[0, 4, 2]</code>，表示指定这些 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">&#39;none&#39;</code> 或 <code class=\"codespan\">null</code> 或 <code class=\"codespan\">undefined</code>，表示不指定。</li>\n</ul>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    geo: {\n        ...\n    },\n    brush: {\n        geoIndex: &#39;all&#39;, // 只可以在所有 geo 坐标系中刷选，也就是上面定义的 geo 组件中。\n        ...\n    }\n};\n</code></pre>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    grid: [\n        {...}, // grid 0\n        {...}  // grid 1\n    ],\n    xAxis: [\n        {gridIndex: 1, ...}, // xAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // xAxis 1，属于 grid 0。\n    ],\n    yAxis: [\n        {gridIndex: 1, ...}, // yAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // yAxis 1，属于 grid 0。\n    ],\n    brush: {\n        xAxisIndex: [0, 1], // 只可以在 xAxisIndex 为 `0` 和 `1` 的 xAxis 所在的直角坐标系中刷选。\n        ...\n    }\n};\n</code></pre>\n<p><br></p>\n<hr>\n<p><strong> 使用 API 控制选框 </strong></p>\n<p>可以通过调用 <code class=\"codespan\">dispatchAction</code> 来用程序主动渲染选框，例如：</p>\n<pre><code class=\"lang-javascript\">myChart.dispatchAction({\n    type: &#39;brush&#39;,\n    areas: [\n        {\n            geoIndex: 0,\n            // 指定选框的类型。\n            brushType: &#39;polygon&#39;,\n            // 指定选框的形状。\n            coordRange: [[119.72,34.85],[119.68,34.85],[119.5,34.84],[119.19,34.77]]\n        }\n    ]\n});\n</code></pre>\n<p>详情参见 <a href=\"api.html#action.brush\" target=\"_blank\">action.brush</a></p>\n<p><br></p>\n<hr>\n<p><strong> brushLink </strong></p>\n<p>不同系列间，选中的项可以联动。</p>\n<p>参见如下效果（刷选一个 <code class=\"codespan\">scatter</code>，其他 <code class=\"codespan\">scatter</code> 以及 <code class=\"codespan\">parallel</code> 图都会有选中效果）：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=scatter-matrix&edit=1&reset=1\" width=\"800\" height=\"550\"><iframe />\n\n\n<p><code class=\"codespan\">brushLink</code> 配置项是一个数组，内容是 seriesIndex，指定了哪些 series 可以被联动。例如可以是：</p>\n<ul>\n<li><code class=\"codespan\">[3, 4, 5]</code> 表示 seriesIndex 为 <code class=\"codespan\">3</code>, <code class=\"codespan\">4</code>, <code class=\"codespan\">5</code> 的 series 可以被联动。</li>\n<li><code class=\"codespan\">&#39;all&#39;</code> 表示所有 series 都进行 brushLink。</li>\n<li><code class=\"codespan\">&#39;none&#39;</code> 或 <code class=\"codespan\">null</code> 或 <code class=\"codespan\">undefined</code> 表示不启用 brushLink 功能。</li>\n</ul>\n<p><strong>注意</strong></p>\n<p>brushLink 是通过 dataIndex 进行映射，所以需要保证，<strong>联动的每个系列的 <code class=\"codespan\">data</code> 都是 <code class=\"codespan\">index</code> 对应的</strong>。*</p>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    brush: {\n        brushLink: [0, 1]\n    },\n    series: [\n        {\n            type: &#39;bar&#39;\n            data: [232,    4434,    545,      654]     // data 有四个项\n        },\n        {\n            type: &#39;parallel&#39;,\n            data: [[4, 5], [3, 5], [66, 33], [99, 66]] // data 同样有四个项，两个系列的 data 是对应的。\n        }\n    ]\n};\n</code></pre>\n<p>参见 <a href=\"#brush.brushLink\">brush.brushLink</a>。</p>\n<p><br></p>\n<hr>\n<p><strong> throttle / debounce / 事件延迟 </strong></p>\n<p>默认情况，刷选或者移动选区的时候，会不断得发 <code class=\"codespan\">brushSelected</code> 事件，从而告诉外界选中的内容。</p>\n<p>但是频繁的事件可能导致性能问题，或者动画效果很差。所以 brush 组件提供了 <a href=\"#brush.throttleType\">brush.throttleType</a>，<a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 来解决这个问题。</p>\n<p>throttleType 取值可以是：</p>\n<ul>\n<li><code class=\"codespan\">&#39;debounce&#39;</code>：表示只有停止动作了（即一段时间没有操作了），才会触发事件。时间阈值由 <a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 指定。</li>\n<li><code class=\"codespan\">&#39;fixRate&#39;</code>：表示按照一定的频率触发事件，时间间隔由 <a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 指定。</li>\n</ul>\n<p><br></p>\n<hr>\n<p><strong> 被选中项和未被选中项的视觉设置 </strong></p>\n<p>参见 <a href=\"#brush.inBrush\">brush.inBrush</a> 和 <a href=\"#brush.outOfBrush\">brush.outOfBrush</a>。</p>\n<p><br></p>\n<hr>\n<p>下面是详细配置。</p>\n"}, "geo": {"desc": "<p>地理坐标系组件。</p>\n<p>地理坐标系组件用于地图的绘制，支持在地理坐标系上绘制<a href=\"#series-scatter\">散点图</a>，<a href=\"#series-lines\">线集</a>。</p>\n<p><code class=\"codespan\">3.1.10</code> 开始 geo 组件也支持鼠标事件。事件参数为</p>\n<pre><code class=\"lang-js\">{\n    componentType: &#39;geo&#39;,\n    // Geo 组件在 option 中的 index\n    geoIndex: number,\n    // 点击区域的名称，比如&quot;上海&quot;\n    name: string,\n    // 传入的点击区域的 region 对象，见 geo.regions\n    region: Object\n}\n</code></pre>\n<p><strong>Tip:</strong>\ngeo 区域的颜色也可以被 map series 所控制，参见 <a href=\"#series-map.geoIndex\">series-map.geoIndex</a>。</p>\n"}, "parallel": {"desc": "<p><strong>平行坐标系介绍</strong></p>\n<p><a href=\"https://en.wikipedia.org/wiki/Parallel_coordinates\" target=\"_blank\">平行坐标系（Parallel Coordinates）</a> 是一种常用的可视化高维数据的图表。</p>\n<p>例如 <a href=\"#series-parallel.data\">series-parallel.data</a> 中有如下数据：</p>\n<pre><code class=\"lang-javascript\">[\n    [1,  55,  9,   56,  0.46,  18,  6,  &#39;良&#39;],\n    [2,  25,  11,  21,  0.65,  34,  9,  &#39;优&#39;],\n    [3,  56,  7,   63,  0.3,   14,  5,  &#39;良&#39;],\n    [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n    { // 数据项也可以是 Object，从而里面能含有对线条的特殊设置。\n        value: [5,  42,  24,  44,  0.76,  40,  16, &#39;优&#39;]\n        lineStyle: {...},\n    }\n    ...\n]\n</code></pre>\n<p>数据中，每一行是一个『数据项』，每一列属于一个『维度』。（例如上面数据每一列的含义分别是：『日期』,『AQI指数』, 『PM2.5』, 『PM10』, 『一氧化碳值』, 『二氧化氮值』, 『二氧化硫值』）。</p>\n<p>平行坐标系适用于对这种多维数据进行可视化分析。每一个维度（每一列）对应一个坐标轴，每一个『数据项』是一条线，贯穿多个坐标轴。在坐标轴上，可以进行数据选取等操作。如下：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/parallel-all&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p><strong>配置方式概要</strong></p>\n<p>『平行坐标系』的 <code class=\"codespan\">option</code> 基本配置如下例：</p>\n<pre><code class=\"lang-javascript\">option = {\n    parallelAxis: [                     // 这是一个个『坐标轴』的定义\n        {dim: 0, name: schema[0].text}, // 每个『坐标轴』有个 &#39;dim&#39; 属性，表示坐标轴的维度号。\n        {dim: 1, name: schema[1].text},\n        {dim: 2, name: schema[2].text},\n        {dim: 3, name: schema[3].text},\n        {dim: 4, name: schema[4].text},\n        {dim: 5, name: schema[5].text},\n        {dim: 6, name: schema[6].text},\n        {dim: 7, name: schema[7].text,\n            type: &#39;category&#39;,           // 坐标轴也可以支持类别型数据\n            data: [&#39;优&#39;, &#39;良&#39;, &#39;轻度污染&#39;, &#39;中度污染&#39;, &#39;重度污染&#39;, &#39;严重污染&#39;]\n        }\n    ],\n    parallel: {                         // 这是『坐标系』的定义\n        left: &#39;5%&#39;,                     // 平行坐标系的位置设置\n        right: &#39;13%&#39;,\n        bottom: &#39;10%&#39;,\n        top: &#39;20%&#39;,\n        parallelAxisDefault: {          // 『坐标轴』的公有属性可以配置在这里避免重复书写\n            type: &#39;value&#39;,\n            nameLocation: &#39;end&#39;,\n            nameGap: 20\n        }\n    },\n    series: [                           // 这里三个系列共用一个平行坐标系\n        {\n            name: &#39;北京&#39;,\n            type: &#39;parallel&#39;,           // 这个系列类型是 &#39;parallel&#39;\n            data: [\n                [1,  55,  9,   56,  0.46,  18,  6,  &#39;良&#39;],\n                [2,  25,  11,  21,  0.65,  34,  9,  &#39;优&#39;],\n                ...\n            ]\n        },\n        {\n            name: &#39;上海&#39;,\n            type: &#39;parallel&#39;,\n            data: [\n                [3,  56,  7,   63,  0.3,   14,  5,  &#39;良&#39;],\n                [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n                ...\n            ]\n        },\n        {\n            name: &#39;广州&#39;,\n            type: &#39;parallel&#39;,\n            data: [\n                [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n                [5,  42,  24,  44,  0.76,  40,  16, &#39;优&#39;],\n                ...\n            ]\n        }\n    ]\n};\n</code></pre>\n<p>需要涉及到三个组件：<a href=\"#parallel\">parallel</a>、<a href=\"#parallelAxis\">parallelAxis</a>、<a href=\"#series-parallel\">series-parallel</a></p>\n<ul>\n<li><p><a href=\"#parallel\">parallel</a></p>\n<p>  这个配置项是平行坐标系的『坐标系』本身。一个系列（<code class=\"codespan\">series</code>）或多个系列（如上图中的『北京』、『上海』、『广州』分别各是一个系列）可以共用这个『坐标系』。</p>\n<p>  和其他坐标系一样，坐标系也可以创建多个。</p>\n<p>  位置设置，也是放在这里进行。</p>\n</li>\n<li><p><a href=\"#parallelAxis\">parallelAxis</a></p>\n<p>  这个是『坐标系』中的坐标轴的配置。自然，需要有多个坐标轴。</p>\n<p>  其中有 <a href=\"#parallelAxis.parallelIndex\">parallelAxis.parallelIndex</a> 属性，指定这个『坐标轴』在哪个『坐标系』中。默认使用第一个『坐标系』。</p>\n</li>\n<li><p><a href=\"#series-parallel\">series-parallel</a></p>\n<p>  这个是『系列』的定义。系列被画到『坐标系』上。</p>\n<p>  其中有 <a href=\"#series-parallel.parallelIndex\">series-parallel.parallelIndex</a> 属性，指定使用哪个『坐标系』。默认使用第一个『坐标系』。</p>\n</li>\n</ul>\n<p><strong>配置注意和最佳实践</strong></p>\n<p>配置多个 <a href=\"#parallelAxis\">parallelAxis</a> 时，有些值一样的属性，如果书写多遍则比较繁琐，那么可以放置在 <a href=\"#parallel.parallelAxisDefault\">parallel.parallelAxisDefault</a> 里。在坐标轴初始化前，<a href=\"#parallel.parallelAxisDefault\">parallel.parallelAxisDefault</a> 里的配置项，会分别融合进 <a href=\"#parallelAxis\">parallelAxis</a>，形成最终的坐标轴的配置。</p>\n<p><strong>如果数据量很大并且发生卡顿</strong></p>\n<p>建议把 <a href=\"#series-parallel.lineStyle.width\">series-parallel.lineStyle.width</a> 设为 <code class=\"codespan\">0.5</code>（或更小），\n可能显著改善性能。</p>\n<p><strong>高维数据的显示</strong></p>\n<p>维度比较多时，比如有 50+ 的维度，那么就会有 50+ 个轴。那么可能会页面显示不下。</p>\n<p>可以通过 <a href=\"#parallel.axisExpandable\">parallel.axisExpandable</a> 来改善显示效果。</p>\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst dataBJ = [\n    [1,55,9,56,0.46,18,6,\"良\"],\n    [2,25,11,21,0.65,34,9,\"优\"],\n    [3,56,7,63,0.3,14,5,\"良\"],\n    [4,33,7,29,0.33,16,6,\"优\"],\n    [5,42,24,44,0.76,40,16,\"优\"],\n    [6,82,58,90,1.77,68,33,\"良\"],\n    [7,74,49,77,1.46,48,27,\"良\"],\n    [8,78,55,80,1.29,59,29,\"良\"],\n    [9,267,216,280,4.8,108,64,\"重度污染\"],\n    [10,185,127,216,2.52,61,27,\"中度污染\"],\n    [11,39,19,38,0.57,31,15,\"优\"],\n    [12,41,11,40,0.43,21,7,\"优\"],\n    [13,64,38,74,1.04,46,22,\"良\"],\n    [14,108,79,120,1.7,75,41,\"轻度污染\"],\n    [15,108,63,116,1.48,44,26,\"轻度污染\"],\n    [16,33,6,29,0.34,13,5,\"优\"],\n    [17,94,66,110,1.54,62,31,\"良\"],\n    [18,186,142,192,3.88,93,79,\"中度污染\"],\n    [19,57,31,54,0.96,32,14,\"良\"],\n    [20,22,8,17,0.48,23,10,\"优\"],\n    [21,39,15,36,0.61,29,13,\"优\"],\n    [22,94,69,114,2.08,73,39,\"良\"],\n    [23,99,73,110,2.43,76,48,\"良\"],\n    [24,31,12,30,0.5,32,16,\"优\"],\n    [25,42,27,43,1,53,22,\"优\"],\n    [26,154,117,157,3.05,92,58,\"中度污染\"],\n    [27,234,185,230,4.09,123,69,\"重度污染\"],\n    [28,160,120,186,2.77,91,50,\"中度污染\"],\n    [29,134,96,165,2.76,83,41,\"轻度污染\"],\n    [30,52,24,60,1.03,50,21,\"良\"],\n    [31,46,5,49,0.28,10,6,\"优\"]\n];\n\nconst dataGZ = [\n    [1,26,37,27,1.163,27,13,\"优\"],\n    [2,85,62,71,1.195,60,8,\"良\"],\n    [3,78,38,74,1.363,37,7,\"良\"],\n    [4,21,21,36,0.634,40,9,\"优\"],\n    [5,41,42,46,0.915,81,13,\"优\"],\n    [6,56,52,69,1.067,92,16,\"良\"],\n    [7,64,30,28,0.924,51,2,\"良\"],\n    [8,55,48,74,1.236,75,26,\"良\"],\n    [9,76,85,113,1.237,114,27,\"良\"],\n    [10,91,81,104,1.041,56,40,\"良\"],\n    [11,84,39,60,0.964,25,11,\"良\"],\n    [12,64,51,101,0.862,58,23,\"良\"],\n    [13,70,69,120,1.198,65,36,\"良\"],\n    [14,77,105,178,2.549,64,16,\"良\"],\n    [15,109,68,87,0.996,74,29,\"轻度污染\"],\n    [16,73,68,97,0.905,51,34,\"良\"],\n    [17,54,27,47,0.592,53,12,\"良\"],\n    [18,51,61,97,0.811,65,19,\"良\"],\n    [19,91,71,121,1.374,43,18,\"良\"],\n    [20,73,102,182,2.787,44,19,\"良\"],\n    [21,73,50,76,0.717,31,20,\"良\"],\n    [22,84,94,140,2.238,68,18,\"良\"],\n    [23,93,77,104,1.165,53,7,\"良\"],\n    [24,99,130,227,3.97,55,15,\"良\"],\n    [25,146,84,139,1.094,40,17,\"轻度污染\"],\n    [26,113,108,137,1.481,48,15,\"轻度污染\"],\n    [27,81,48,62,1.619,26,3,\"良\"],\n    [28,56,48,68,1.336,37,9,\"良\"],\n    [29,82,92,174,3.29,0,13,\"良\"],\n    [30,106,116,188,3.628,101,16,\"轻度污染\"],\n    [31,118,50,0,1.383,76,11,\"轻度污染\"]\n];\n\nconst dataSH = [\n    [1,91,45,125,0.82,34,23,\"良\"],\n    [2,65,27,78,0.86,45,29,\"良\"],\n    [3,83,60,84,1.09,73,27,\"良\"],\n    [4,109,81,121,1.28,68,51,\"轻度污染\"],\n    [5,106,77,114,1.07,55,51,\"轻度污染\"],\n    [6,109,81,121,1.28,68,51,\"轻度污染\"],\n    [7,106,77,114,1.07,55,51,\"轻度污染\"],\n    [8,89,65,78,0.86,51,26,\"良\"],\n    [9,53,33,47,0.64,50,17,\"良\"],\n    [10,80,55,80,1.01,75,24,\"良\"],\n    [11,117,81,124,1.03,45,24,\"轻度污染\"],\n    [12,99,71,142,1.1,62,42,\"良\"],\n    [13,95,69,130,1.28,74,50,\"良\"],\n    [14,116,87,131,1.47,84,40,\"轻度污染\"],\n    [15,108,80,121,1.3,85,37,\"轻度污染\"],\n    [16,134,83,167,1.16,57,43,\"轻度污染\"],\n    [17,79,43,107,1.05,59,37,\"良\"],\n    [18,71,46,89,0.86,64,25,\"良\"],\n    [19,97,71,113,1.17,88,31,\"良\"],\n    [20,84,57,91,0.85,55,31,\"良\"],\n    [21,87,63,101,0.9,56,41,\"良\"],\n    [22,104,77,119,1.09,73,48,\"轻度污染\"],\n    [23,87,62,100,1,72,28,\"良\"],\n    [24,168,128,172,1.49,97,56,\"中度污染\"],\n    [25,65,45,51,0.74,39,17,\"良\"],\n    [26,39,24,38,0.61,47,17,\"优\"],\n    [27,39,24,39,0.59,50,19,\"优\"],\n    [28,93,68,96,1.05,79,29,\"良\"],\n    [29,188,143,197,1.66,99,51,\"中度污染\"],\n    [30,174,131,174,1.55,108,50,\"中度污染\"],\n    [31,187,143,201,1.39,89,53,\"中度污染\"]\n];\n\nconst schema = [\n    {name: 'date', index: 0, text: '日期'},\n    {name: 'AQIindex', index: 1, text: 'AQI'},\n    {name: 'PM25', index: 2, text: 'PM2.5'},\n    {name: 'PM10', index: 3, text: 'PM10'},\n    {name: 'CO', index: 4, text: ' CO'},\n    {name: 'NO2', index: 5, text: 'NO2'},\n    {name: 'SO2', index: 6, text: 'SO2'},\n    {name: '等级', index: 7, text: '等级'}\n];\n\nconst option = {\n    color: [\n        '#c23531', '#91c7ae', '#dd8668'\n    ],\n    legend: {\n        top: 10,\n        data: ['北京', '上海', '广州'],\n        itemGap: 20\n    },\n    parallelAxis: [\n        {dim: 0, name: schema[0].text, inverse: true, max: 31, nameLocation: 'start'},\n        {dim: 1, name: schema[1].text},\n        {dim: 2, name: schema[2].text},\n        {dim: 3, name: schema[3].text},\n        {dim: 4, name: schema[4].text},\n        {dim: 5, name: schema[5].text},\n        {dim: 6, name: schema[6].text},\n        {dim: 7, name: schema[7].text,\n        type: 'category', data: ['优', '良', '轻度污染', '中度污染', '重度污染', '严重污染']}\n    ],\n    parallel: {\n        left: '5%',\n        right: '13%',\n        bottom: '10%',\n        top: '20%',\n        parallelAxisDefault: {\n            type: 'value',\n            name: 'AQI指数',\n            nameLocation: 'end',\n            nameGap: 20,\n            nameTextStyle: {\n                fontSize: 12\n            }\n        }\n    },\n    series: [\n        {\n            name: '北京',\n            type: 'parallel',\n            data: dataBJ\n        },\n        {\n            name: '上海',\n            type: 'parallel',\n            data: dataSH\n        },\n        {\n            name: '广州',\n            type: 'parallel',\n            data: dataGZ\n        }\n    ]\n};\n", "name": "parallel", "title": "平行坐标", "title-en": "<PERSON><PERSON><PERSON>"}, {"code": "\n\nvar geoCoordMap = {\n    \"Amsterdam\": [4.895168,52.370216],\n    \"Athens\": [-83.357567,33.951935],\n    \"Auckland\": [174.763332,-36.84846],\n    \"Bangkok\": [100.501765,13.756331],\n    \"Barcelona\": [2.173403,41.385064],\n    \"Beijing\": [116.407395,39.904211],\n    \"Berlin\": [13.404954,52.520007],\n    \"Bogotá\": [-74.072092,4.710989],\n    \"Bratislava\": [17.107748,48.148596],\n    \"Brussels\": [4.35171,50.85034],\n    \"Budapest\": [19.040235,47.497912],\n    \"Buenos Aires\": [-58.381559,-34.603684],\n    \"Bucharest\": [26.102538,44.426767],\n    \"Caracas\": [-66.903606,10.480594],\n    \"Chicago\": [-87.629798,41.878114],\n    \"Delhi\": [77.209021,28.613939],\n    \"Doha\": [51.53104,25.285447],\n    \"Dubai\": [55.270783,25.204849],\n    \"Dublin\": [-6.26031,53.349805],\n    \"Frankfurt\": [8.682127,50.110922],\n    \"Geneva\": [6.143158,46.204391],\n    \"Helsinki\": [24.938379,60.169856],\n    \"Hong Kong\": [114.109497,22.396428],\n    \"Istanbul\": [28.978359,41.008238],\n    \"Jakarta\": [106.845599,-6.208763],\n    \"Johannesburg\": [28.047305,-26.204103],\n    \"Cairo\": [31.235712,30.04442],\n    \"Kiev\": [30.5234,50.4501],\n    \"Copenhagen\": [12.568337,55.676097],\n    \"Kuala Lumpur\": [101.686855,3.139003],\n    \"Lima\": [-77.042793,-12.046374],\n    \"Lisbon\": [-9.139337,38.722252],\n    \"Ljubljana\": [14.505751,46.056947],\n    \"London\": [-0.127758,51.507351],\n    \"Los Angeles\": [-118.243685,34.052234],\n    \"Luxembourg\": [6.129583,49.815273],\n    \"Lyon\": [4.835659,45.764043],\n    \"Madrid\": [-3.70379,40.416775],\n    \"Milan\": [9.185924,45.465422],\n    \"Manama\": [50.58605,26.228516],\n    \"Manila\": [120.984219,14.599512],\n    \"Mexico City\": [-99.133208,19.432608],\n    \"Miami\": [-80.19179,25.76168],\n    \"Montreal\": [-73.567256,45.501689],\n    \"Moscow\": [37.6173,55.755826],\n    \"Mumbai\": [72.877656,19.075984],\n    \"Munich\": [11.581981,48.135125],\n    \"Nairobi\": [36.821946,-1.292066],\n    \"New York\": [-74.005941,40.712784],\n    \"Nicosia\": [33.382276,35.185566],\n    \"Oslo\": [10.752245,59.913869],\n    \"Paris\": [2.352222,48.856614],\n    \"Prague\": [14.4378,50.075538],\n    \"Riga\": [24.105186,56.949649],\n    \"Rio de Janeiro\": [-43.172896,-22.906847],\n    \"Rome\": [12.496366,41.902783],\n    \"Santiago de Chile\": [-70.669265,-33.44889],\n    \"São Paulo\": [-46.633309,-23.55052],\n    \"Seoul\": [126.977969,37.566535],\n    \"Shanghai\": [121.473701,31.230416],\n    \"Singapore\": [103.819836,1.352083],\n    \"Sofia\": [23.321868,42.697708],\n    \"Stockholm\": [18.068581,59.329323],\n    \"Sydney\": [151.209296,-33.86882],\n    \"Taipei\": [121.565418,25.032969],\n    \"Tallinn\": [24.753575,59.436961],\n    \"Tel Aviv\": [34.781768,32.0853],\n    \"Tokyo\": [139.691706,35.689487],\n    \"Toronto\": [-79.383184,43.653226],\n    \"Vilnius\": [25.279651,54.687156],\n    \"Warsaw\": [21.012229,52.229676],\n    \"Vienna\": [16.373819,48.208174],\n    \"Zurich\": [8.541694,47.376887]\n};\n\nvar schema = [\n    \"Cities\",\n    \"Gross purchasing power\",\n    \"Net purchasing power\",\n    \"Prices (excl. rent)\",\n    \"Prices (incl. rent)\",\n    \"Gross wages\",\n    \"Net wages\",\n    \"Working time [hours per year]\",\n    \"Vacation [paid working days per year]\",\n    \"Time required for 1 Big Mac [minutes]\",\n    \"Time required for 1 kg of bread [minutes]\",\n    \"Time required for 1 kg of rice [minutes]\",\n    \"Time required for 1 iPhone 4S, 16 GB [hours]\",\n    \"City break\",\n    \"Inflation 2006\",\n    \"Inflation 2007\",\n    \"Inflation 2008\",\n    \"Inflation 2009\",\n    \"Inflation 2010\",\n    \"Inflation 2011\",\n    \"Prices (incl. rent)\",\n    \"Food basket\",\n    \"Services\",\n    \"Normal local rent medium [USD per month]\",\n    \"Household appliances\",\n    \"Bus or tram or underground\",\n    \"Train\",\n    \"Taxi  [USD per 5 km trip]\",\n    \"Medium-sized cars price\",\n    \"Medium-sized cars tax\",\n    \"Medium-sized cars gas\",\n    \"Restaurant [USD per dinner]\",\n    \"Hotel *** [USD per night]\",\n    \"Hotel ***** [USD per night]\",\n    \"Women's medium clothing\",\n    \"Men's medium clothing\",\n    \"Furnished medium 4-room apartment [USD per month]\",\n    \"Unfurnished medium 3-room apartment [USD per month]\",\n    \"Net hourly wages [USD per hour]\",\n    \"Gross hourly wages [USD per hour]\",\n    \"Taxes and social security contributions\",\n    \"Primary school teacher [USD per year]\",\n    \"Bus driver [USD per year]\",\n    \"Automobile mechanic [USD per year]\",\n    \"Building labourer [USD per year]\",\n    \"Skilled industrial worker [USD per year]\",\n    \"Cook [USD per year]\",\n    \"Departement head [USD per year]\",\n    \"Product manager [USD per year]\",\n    \"Engineer [USD per year]\",\n    \"Bank credit clerk [USD per year]\",\n    \"Secretary [USD per year]\",\n    \"Saleswoman [USD per year]\",\n    \"Female industrial worker [USD per year]\",\n    \"Female call center worker [USD per year]\",\n    \"Financial analyst [USD per year]\",\n    \"Financial analyst [USD pro Jahr]\"\n];\n\nvar rawData = [\n  [\"Amsterdam\",101.6,90.1,77.1,69.1,78.3,69.4,1755,24,15,7,9,44,720,1.651,1.59,2.205,0.974,0.93,2.477,67.4,364,690,1113,4960,3.19,30.05,16.34,24000,689,1.8,50,200,390,690,1040,2331,1580,17.5,25.5,30,48400,39200,26300,30200,55400,39800,104400,58700,64600,49200,40300,31100,40300,27700,66700,66700  ],\n  [\"Athens\",62.6,60.5,66.2,58.2,41.4,40,1822,22,29,13,25,86,590,3.314,2.991,4.236,1.349,4.701,3.1,56.8,390,580,880,4620,1.81,13.81,5.5,24900,389,2.02,54,100,210,630,1110,1489,647,10.1,13.5,24,26200,23300,18500,17100,24500,24200,57200,44000,34100,30700,21000,17700,15400,16300,34400,34400  ],\n  [\"Auckland\",77.9,82.9,76.7,67.8,59.8,63.5,1852,20,15,16,7,51,580,3.362,2.377,3.959,2.116,2.303,4.027,66.1,496,630,1023,4450,2.57,40.86,13.62,23900,226,1.33,45,190,280,560,670,1644,1333,16,19.5,17,35700,31500,36500,28500,41800,31100,61300,55000,56300,37300,33400,26900,27200,27500,64900,64900  ],\n  [\"Bangkok\",26.4,31.4,55.4,48.2,14.6,17.4,2312,7,36,25,20,165,550,4.637,2.242,5.468,-0.845,3.272,3.807,47,422,440,414,4370,0.75,3.47,2.47,29600,103,1,56,90,320,400,600,1463,932,4.4,4.8,5,8300,8400,11100,3000,10900,10900,32200,22400,24600,14500,7800,6000,5800,6500,19400,19400  ],\n  [\"Barcelona\",79.7,78.6,74.7,65.6,59.6,58.7,1760,29,18,11,6,52,740,3.563,2.844,4.13,-0.238,2.043,3.052,64,393,750,984,5000,2.59,41.96,10.36,26900,177,1.77,51,170,330,580,1110,1269,1087,14.8,19.4,23,41300,34100,29100,29800,31500,32100,40800,67000,43100,38900,28900,25500,25000,28000,58300,58300  ],\n  [\"Beijing\",28.2,29.9,60.3,51.8,17,18,1979,9,34,27,16,184,730,1.467,4.767,5.852,-0.683,3.325,5.417,50.6,463,420,310,4370,0.26,14.25,3.64,23800,67,1.24,41,160,400,660,700,1554,660,4.5,5.6,17,11400,7000,8500,7600,6200,11900,13300,11700,10700,18300,17100,8900,5400,7600,19800,19800  ],\n  [\"Berlin\",109.7,97.1,72.2,64.1,79.2,70.1,1742,28,16,11,9,56,720,1.784,2.276,2.754,0.234,1.15,2.483,62.5,389,530,841,4670,2.98,80.3,10.79,35600,246,2.1,34,120,230,570,710,2395,1178,17.7,25.8,30,56900,38600,35500,28500,47400,57600,84200,74500,72100,51700,38100,28200,32000,28100,81700,81700  ],\n  [\"Bogotá\",41.4,40.7,53.1,47,22.3,22,1981,15,52,34,17,142,540,4.296,5.544,6.998,4.202,2.27,3.416,45.8,363,410,634,4170,0.84,null,2.81,20200,303,1.24,25,140,300,310,440,1554,841,5.5,7.3,12,11100,7600,8400,7000,7300,11900,27600,65800,19700,14600,12300,7400,4100,5300,31900,31900  ],\n  [\"Bratislava\",51.3,50.7,53.9,47.1,27.7,27.3,1884,23,31,20,19,126,490,4.264,1.89,3.935,0.925,0.697,4.079,45.9,344,330,414,4740,1.08,22.97,5.61,26700,65,1.93,28,120,230,250,340,1683,841,6.9,9,22,11300,14100,11300,10000,16300,18900,20300,43300,22800,15800,16100,9600,12600,16400,48300,48300  ],\n  [\"Brussels\",107.5,78.5,75.8,68.8,81.5,59.5,1729,20,19,10,11,54,730,2.337,1.814,4.493,-0.009,2.332,3.469,67.1,433,690,1243,4580,2.42,26.03,15.71,23200,500,1.91,63,130,280,630,800,2538,1839,15,26.5,42,44000,36500,38200,34200,52100,43600,97000,73700,67100,56900,42300,35200,33500,36000,78200,78200  ],\n  [\"Budapest\",35.5,32,56.7,50.4,20.1,18.1,1912,22,49,13,26,206,740,3.878,7.934,6.067,4.209,4.85,3.9,49.1,340,390,556,5270,1.43,15.96,7.64,22600,76,1.95,28,130,410,580,920,2123,1165,4.6,6.6,28,8900,11500,9300,7000,10900,16800,25600,21100,23300,21400,11100,8200,6500,8900,29300,29300  ],\n  [\"Buenos Aires\",42.9,46.2,55,47.7,23.6,25.4,1830,13,45,12,16,187,620,10.898,8.83,8.585,6.27,10.461,9.775,46.5,310,380,401,5170,0.28,null,7.97,20200,307,1.27,25,160,280,400,660,1359,738,6.4,7.7,16,8700,16300,11900,10200,11400,15800,34300,17600,19200,17100,15800,14600,7500,10400,15400,15400  ],\n  [\"Bucharest\",37.1,34,39.8,34.8,14.8,13.5,1836,26,57,21,26,230,370,6.552,4.84,7.848,5.581,6.101,5.812,33.9,244,270,388,3830,0.75,9.43,3.17,11700,36,1.9,25,100,190,180,300,984,530,3.4,4.8,29,5600,7500,9500,7900,7400,14900,13900,22000,11400,8800,6000,8200,5800,5300,13200,13200  ],\n  [\"Caracas\",21.9,25.7,91.1,85.4,20,23.4,1878,17,80,59,13,272,830,13.654,18.703,30.37,27.081,28.187,26.09,83.3,689,690,2098,8460,0.35,null,11.65,49000,18,0.01,68,190,400,520,950,3820,2784,5.9,6.5,7,10500,5400,10900,6100,9700,25400,16800,30600,22800,15800,8400,9500,5400,6700,12400,12400  ],\n  [\"Chicago\",105.3,101.9,79.1,72.9,83.3,80.6,1853,12,11,15,9,32,540,3.222,2.86,3.798,-0.321,1.641,3.142,71.1,460,780,1398,4270,2.25,34.99,12.5,22100,95,1.11,38,200,270,740,1200,3535,2214,20.3,27.1,25,49300,52100,44000,49000,58500,48300,79300,88500,88200,40300,42600,23200,33800,38700,103500,103500  ],\n  [\"Delhi\",23,25,33.2,29.5,7.6,8.3,2264,13,65,15,40,370,370,6.177,6.372,8.349,10.882,11.99,8.628,28.8,208,210,466,4590,0.25,10.58,1.95,15300,93,0.77,18,100,250,260,410,867,556,2.1,2.5,11,4500,2500,1900,1300,4800,10200,13500,11100,9600,6700,5400,3000,1800,4000,14100,14100  ],\n  [\"Doha\",38.8,50.2,68.6,66.9,26.6,34.4,2165,25,20,12,15,82,690,11.828,13.764,15.049,-4.865,-2.433,2,65.3,355,870,1735,3790,1.14,null,4.12,17000,27,0.24,63,200,360,340,470,4869,3004,8.7,8.7,0,22300,10400,9800,4100,20600,13700,49500,47000,32900,27100,19800,10900,3200,11500,23100,23100  ],\n  [\"Dubai\",63.5,82,78.2,77.2,49.6,64.2,2095,24,11,10,14,46,1120,9.272,11.115,11.454,1.56,0.878,0.882,75.3,484,790,2447,4550,0.54,null,8.17,23100,94,1.01,95,200,680,1270,1450,4882,3483,16.2,16.2,0,35900,16300,14200,3600,38300,58100,116800,91400,64800,26200,22900,13600,9800,19600,80000,80000  ],\n  [\"Dublin\",101.9,103.3,76.3,69.7,77.7,78.8,1707,21,14,7,10,39,580,2.7,2.873,3.108,-1.683,-1.557,1.139,68,454,720,1554,5160,2.74,34.53,14.89,32000,427,2.01,55,130,260,470,600,2331,1592,19.8,25.3,20,57100,45500,38300,28200,42600,42100,88800,86100,54300,45800,31700,25300,28200,28800,85000,85000  ],\n  [\"Frankfurt\",102.2,90.5,86.3,77.2,88.2,78.1,1731,28,14,8,10,42,950,1.784,2.276,2.754,0.234,1.15,2.483,75.3,439,710,1282,5510,3.24,86.77,16.23,36800,78,1.89,68,130,370,840,890,2370,1644,19.7,28.7,30,60900,33300,40400,34100,48800,40000,83300,77700,77700,61600,50600,37900,34100,29100,104100,104100  ],\n  [\"Geneva\",116,111.8,106.6,96.8,123.6,119.2,1893,20,14,6,7,24,1220,1.047,0.732,2.43,-0.476,0.685,0.228,94.5,714,1080,1567,5330,3.67,58.23,27.78,25200,135,2.04,99,270,620,850,1150,4701,2434,30,40.2,24,89600,77400,61900,58900,78400,76500,105800,113300,89400,110900,64900,49200,55400,61800,171100,171100  ],\n  [\"Helsinki\",93,86,86.5,82.3,80.2,74.2,1712,28,16,13,6,44,960,1.279,1.584,3.9,1.635,1.686,3.323,80.3,497,840,1437,5400,3.28,34.83,12.71,34600,152,1.97,44,200,450,610,1200,8677,1437,18.7,26.1,27,48000,42200,38200,38700,40500,41200,89900,60500,69200,44700,38600,34600,33500,35600,74500,74500  ],\n  [\"Hong Kong\",58.5,68.1,73.1,75.2,42.8,49.8,2295,11,9,23,9,53,970,2.018,2.027,4.285,0.588,2.312,5.281,73.4,651,520,1800,4770,1.33,20.48,3.99,23800,509,1.65,58,290,610,390,620,9661,4222,12.5,13.9,9,52500,20100,20100,18900,23100,40800,64400,63700,44600,22300,25100,22100,14000,19000,62600,62600  ],\n  [\"Istanbul\",39,39.4,71.5,65.6,27.9,28.2,2139,19,42,9,14,166,720,9.597,8.756,10.444,6.251,8.567,6.472,64,430,630,1282,5490,0.95,15.84,8.94,34600,1189,2.37,44,240,420,630,880,3147,1476,7.1,9.1,20,14800,14600,13500,9500,20300,51300,38600,39100,34100,19300,13500,9500,9200,9300,33500,33500  ],\n  [\"Jakarta\",14.7,17.2,53.7,48.6,7.9,9.2,2111,11,62,100,27,348,500,13.104,6.034,9.777,4.813,5.133,5.357,47.4,369,330,673,4460,0.38,2.64,2.93,47800,717,0.76,19,160,320,190,390,2719,1087,2.3,2.6,7,2700,2600,2200,1300,10100,4400,17000,13000,10700,5300,4800,2700,1400,3200,15800,15800  ],\n  [\"Johannesburg\",80.6,75.5,52.1,47.3,41.5,38.9,1886,15,26,9,10,94,490,4.688,7.09,11.504,7.125,4.27,4.999,46.1,310,400,738,3800,1.25,15.59,4.07,35500,56,1.45,28,100,320,310,400,2162,1295,9.8,13.5,21,17700,11900,21000,7500,41800,18000,51500,36800,74900,11900,22400,10500,15900,6600,65900,65900  ],\n  [\"Cairo\",26.2,28.7,42.4,36.3,11,12.1,2331,15,67,8,19,290,420,4.198,10.952,11.704,16.244,11.703,11.068,35.4,300,300,168,4220,0.19,5.24,1.49,21500,50,0.15,27,100,200,380,430,1113,492,3,3.6,15,1600,1400,2700,2600,6600,48800,27700,20500,9600,3200,6200,5400,1900,3100,10900,10900  ],\n  [\"Kiev\",19.5,20.9,53.1,46.9,10.5,11.2,1850,24,45,18,29,266,930,9.009,12.843,25.201,15.9,9.365,7.958,45.7,263,450,556,4140,0.25,12.15,4.56,24900,null,1.22,42,150,530,430,600,1631,854,2.8,3.4,16,3800,5300,6500,4900,7000,10500,11100,14100,6700,10000,4900,4100,4000,3900,13300,13300  ],\n  [\"Copenhagen\",122,92.6,100.9,88.8,123.1,93.4,1674,29,15,9,6,36,1060,1.9,1.712,3.399,1.319,2.298,2.757,86.7,567,960,1100,5060,4.88,59.92,17.33,63400,641,1.99,72,270,490,950,1150,2616,1735,23.5,40.1,41,86500,56300,68400,58500,68900,61000,86400,94500,89200,72900,61900,46000,64800,42200,109200,109200  ],\n  [\"Kuala Lumpur\",41.1,42,52.1,46.2,21.5,22,1986,15,25,20,21,129,500,3.609,2.027,5.4,0.6,1.7,3.2,45.1,346,400,777,4300,0.68,7.06,2.44,25700,91,0.59,58,120,220,230,540,1256,621,5.5,7,17,10400,8300,10700,6100,12800,17100,44000,31200,23600,11400,13200,7400,5100,7100,22700,22700  ],\n  [\"Lima\",43.6,45.5,50.8,44.5,22.2,23.1,2107,27,21,20,15,162,600,2.004,1.78,5.788,2.936,1.53,3.369,43.4,303,410,492,4480,0.47,null,4.52,19300,95,1.36,36,130,400,310,470,1502,543,5.8,7.2,15,6300,6200,7600,5400,15900,12200,37300,40700,24300,10100,18900,6700,7600,8300,29400,29400  ],\n  [\"Lisbon\",65.3,63.2,67.5,60.2,44,42.6,1695,22,22,13,8,96,720,3.043,2.423,2.646,-0.903,1.391,3.558,58.7,310,570,1100,5180,1.83,27.63,11.5,38100,181,1.95,45,80,390,390,510,1308,1178,10.7,14.3,22,32000,22500,19700,13900,25900,33700,33700,35200,36100,32400,17500,15000,18500,14900,63100,63100  ],\n  [\"Ljubljana\",57.5,50.5,63.3,55.2,36.4,32,1792,22,25,22,38,101,550,2.458,3.611,5.7,0.855,1.834,1.828,53.8,368,490,479,4970,2.03,18.49,7.25,24600,140,1.71,32,140,240,560,1000,1774,1023,8.1,11.9,30,29000,15000,18100,13000,17900,28000,56600,41700,35000,23400,16300,12800,12400,17900,27800,27800  ],\n  [\"London\",91.2,86.2,87.2,83,79.5,75.2,1786,22,15,6,13,42,930,2.3,2.346,3.629,2.12,3.339,4.454,81,436,770,1981,4910,3.7,81.95,23.03,28000,217,2.4,50,200,440,480,800,4830,3263,19,25.9,26,55700,44400,40900,39100,51700,36400,80300,75000,65900,46600,40700,26300,37600,27100,64200,64200  ],\n  [\"Los Angeles\",113.8,106.7,75.8,68.7,86.3,80.9,1942,12,11,18,6,33,520,3.222,2.86,3.798,-0.321,1.641,3.142,67,501,580,1204,3590,1.5,34.33,25.06,30100,296,1.13,37,170,270,710,1240,2564,1877,20.4,28.1,28,55700,60200,42100,47000,62300,51900,58500,69200,90500,46100,44900,36500,47300,45700,80300,80300  ],\n  [\"Luxembourg\",111.6,116.2,94.4,85.5,105.4,109.7,1788,25,11,9,9,30,970,2.667,2.313,3.383,0.37,2.274,3.409,83.4,524,860,1813,4660,1.94,54.13,19.43,31100,118,1.6,81,140,380,960,1440,2305,1839,27.6,34.3,18,113300,93900,38900,28500,45300,35500,126300,58300,66800,93900,45700,28500,29900,37200,89400,89400  ],\n  [\"Lyon\",81.8,82.5,78.4,68.8,64.2,64.7,1641,26,16,10,12,52,740,1.912,1.607,3.159,0.103,1.735,2.294,67.2,477,700,945,4540,2.07,40.02,13.08,30400,null,1.84,29,180,310,740,1180,1554,1217,16.3,20.9,21,33900,30300,26200,24000,29400,43500,81500,70600,45100,55200,26000,22500,23800,24700,100900,100900  ],\n  [\"Madrid\",83.6,85,69.7,61.7,57,57.9,1733,30,18,9,6,53,590,3.563,2.844,4.13,-0.238,2.043,3.052,60.2,432,630,1049,4940,1.94,44.51,13.27,21600,177,1.76,53,170,240,580,910,1295,1061,14.6,18.5,19,29100,25400,20500,23200,27300,35900,39200,63500,52700,53500,26900,17500,19900,25600,65100,65100  ],\n  [\"Milan\",88.2,77.2,79.7,72.2,70.3,61.5,1753,23,17,14,15,55,770,2.217,2.038,3.5,0.764,1.639,2.903,70.4,487,710,1256,4790,1.94,34.75,16.84,18800,186,2.24,73,170,320,870,1170,2862,1813,15.5,22.9,31,32800,36500,29400,28900,36900,51000,68000,60400,57900,43500,32000,29700,28800,23200,58700,58700  ],\n  [\"Manama\",56.4,71.9,54,49.5,30.5,38.8,1989,22,19,5,9,72,530,2.041,3.252,3.533,2.786,1.969,1,48.3,278,510,906,3810,0.8,null,11.52,18600,93,0.93,18,170,350,390,510,2486,1282,9.8,9.9,1,19800,10900,9200,7600,24000,11500,57900,33700,34400,15000,15300,19900,11800,14400,27800,27800  ],\n  [\"Manila\",19.2,19.5,41.5,35.9,8,8.1,2245,10,72,70,27,435,450,6.234,2.8,9.299,4.191,3.793,4.761,35,292,300,194,5630,0.34,5.58,2.88,17400,47,1.02,18,160,300,140,270,1437,505,2,2.6,19,3400,2500,2700,2700,3600,8900,10900,18800,9800,3500,4000,3100,2800,4800,9700,9700  ],\n  [\"Mexico City\",26.8,29.5,51.2,45.7,13.7,15.1,2375,6,48,25,21,220,440,3.629,3.967,5.125,5.297,4.155,3.403,44.6,259,480,738,4580,0.37,null,3.6,19400,28,0.81,31,130,210,620,930,1398,984,3.8,4.5,12,8400,3800,3400,3100,4800,19800,21400,21200,13600,7100,15500,4800,3100,14600,28000,28000  ],\n  [\"Miami\",106.2,103.7,77,70.7,81.8,79.9,1938,12,12,12,5,32,560,3.222,2.86,3.798,-0.321,1.641,3.142,69,499,610,1515,3580,1.83,27.33,15.32,32400,98,1.05,28,160,330,630,880,2693,1929,20.1,26.6,23,63500,30600,40700,40100,56500,46200,70300,101500,95100,49300,46800,30700,33800,37700,71500,71500  ],\n  [\"Montreal\",93.1,80.9,81.8,73.7,76.2,66.2,1782,13,19,14,14,44,720,2.018,2.123,2.385,0.3,1.776,2.891,71.9,519,690,1359,4610,2.87,70.58,12.98,21400,308,1.33,51,210,310,630,1100,2266,1735,16.7,24.8,32,56700,48700,43800,27600,42500,44600,59700,65100,61600,58900,40800,25900,35400,32000,56300,56300  ],\n  [\"Moscow\",45.1,50.3,66.2,61.3,30.4,33.8,1799,25,18,7,12,119,970,9.679,9.007,14.117,11.654,6.854,8.443,59.8,314,690,854,5050,0.85,10.56,13.24,21200,73,0.93,73,200,530,820,1040,3639,2784,8.5,9.9,14,11900,18600,15800,13000,15900,30300,28800,29500,25500,19200,16800,12200,11800,10000,46100,46100  ],\n  [\"Mumbai\",24.9,27.3,34.1,31,8.5,9.3,2251,19,56,29,30,338,400,6.177,6.372,8.349,10.882,11.99,8.628,30.3,186,210,453,3610,0.13,2.72,1.76,11500,754,0.91,24,160,280,320,540,1683,802,2.3,2.8,11,3800,3200,2100,1300,3100,7900,19800,21100,15400,7500,3800,2700,1400,4800,18000,18000  ],\n  [\"Munich\",108.3,89.9,84.6,75.1,91.5,76,1755,25,14,11,11,42,800,1.784,2.276,2.754,0.234,1.15,2.483,73.3,500,720,971,4770,3.24,71.23,18.04,38400,150,1.86,53,170,340,830,1130,2499,1813,19.2,29.8,34,51200,40000,37300,28000,51700,55200,105900,115500,79500,78200,50100,40300,31500,30200,105900,105900  ],\n  [\"Nairobi\",21.4,21,48.6,43.7,10.4,10.2,2196,21,83,27,41,292,490,14.455,9.76,13.1,10.552,4.087,13.998,42.7,291,370,479,4340,0.54,35.8,7.16,23900,null,1.29,22,170,220,220,280,2382,1230,2.6,3.4,24,4000,3100,2600,2500,4400,16200,11400,16600,21000,7500,4300,3400,2800,4900,14200,14200  ],\n  [\"New York\",100,100,100,100,100,100,2061,13,9,12,5,28,1180,3.222,2.86,3.798,-0.321,1.641,3.142,97.6,552,1000,3354,3960,2.42,85.98,8.5,20500,100,1.15,71,340,730,570,980,7239,4299,25.2,32.6,22,62900,57200,50000,69300,79100,85500,118200,119300,107400,68400,54800,42300,41300,41700,120600,120600  ],\n  [\"Nicosia\",95,107.1,64,57,60.8,68.5,1778,22,11,7,8,54,490,2.245,2.165,4.377,0.174,2.564,3.486,55.6,370,490,919,5480,1.45,null,9.31,24600,74,1.67,33,140,270,630,950,1877,932,17.3,19.8,11,47400,24700,28600,24500,33800,52700,62800,48600,47700,45600,23100,20200,21000,21100,107000,107000  ],\n  [\"Oslo\",102.7,83.9,116,104.5,119.1,97.4,1749,25,17,11,12,36,1000,2.332,0.729,3.766,2.166,2.4,1.301,102,599,1270,1968,5190,5.12,48.58,23.22,42700,574,2.37,98,220,410,840,1450,3250,2214,24.5,38.8,36,61500,60000,78200,62800,72900,59800,123400,97400,79400,69300,64900,49500,63700,46500,144000,144000  ],\n  [\"Paris\",94.8,89.4,83.9,77.5,78.1,73.6,1557,29,15,14,12,44,1100,1.912,1.607,3.159,0.103,1.735,2.294,75.6,522,770,1670,5030,2.16,42.87,9.39,33700,486,1.89,64,210,600,1020,1410,3250,2279,18.5,25.4,26,38700,32800,28000,25900,32100,36800,71000,71100,67100,80700,34700,25300,25400,25600,86900,86900  ],\n  [\"Prague\",45.1,46.2,54.3,48,24.5,25.1,1829,20,34,13,16,132,740,2.543,2.862,6.339,1.034,1.464,1.929,46.8,295,460,725,4900,1.37,12.78,8.05,27800,116,1.88,58,120,350,250,530,1230,867,6.3,8,20,13200,16200,13600,9100,15400,17100,24500,25300,21000,20100,13200,12200,9200,10500,21900,21900  ],\n  [\"Riga\",44.3,39.2,54.5,47.2,24.2,21.4,1806,23,33,22,22,168,580,6.571,10.083,15.252,3.259,-1.224,4.223,46,316,410,466,4460,1.3,9.23,6.34,29700,98,1.76,36,110,300,440,570,932,841,5.4,7.9,31,8400,13700,15500,14600,15300,14400,23300,30400,16800,18900,13700,8700,9300,13000,33000,33000  ],\n  [\"Rio de Janeiro\",44.4,45,61.2,55.5,27.2,27.5,1895,30,45,33,11,160,710,4.196,3.638,5.672,4.888,5.039,6.636,54.2,354,530,764,5400,1.56,null,7.29,16100,357,0.88,23,180,440,230,350,3198,1320,6.9,8.9,15,13700,9800,14200,8400,18600,21200,62400,33300,37600,10900,10700,6600,6300,7500,70300,70300  ],\n  [\"Rome\",69.6,60.9,79.2,73.8,55.1,48.2,1898,22,23,17,19,70,650,2.217,2.038,3.5,0.764,1.639,2.903,72,497,690,1813,5190,1.94,36.69,9.38,32400,389,2.27,36,210,320,910,830,3237,2033,12.1,17.9,31,30600,33500,22900,19600,31300,74200,53100,65100,28000,34300,34800,22100,16200,13200,94500,94500  ],\n  [\"Santiago de Chile\",42.8,40.6,52.9,47.6,22.6,21.5,2034,15,55,22,21,157,630,3.392,4.408,8.716,1.485,1.408,3.34,46.5,348,460,673,4520,1.17,6.49,7.13,13400,255,1.24,33,140,410,360,610,2344,1023,5.4,7.4,23,15400,10000,11000,6700,12200,12300,28100,36700,32000,21600,11300,8200,7400,8500,30600,30600  ],\n  [\"São Paulo\",48.7,49.4,61.7,56.1,30.1,30.5,1809,30,39,27,7,106,770,4.196,3.638,5.672,4.888,5.039,6.636,54.8,379,540,854,5010,1.53,null,6.83,23700,485,1.28,42,310,470,300,600,2810,1580,7.7,9.8,16,10600,11300,12600,6600,12600,25500,61900,67000,28200,14600,13900,8700,7100,4800,19900,19900  ],\n  [\"Seoul\",80.8,74,67.9,66.3,54.8,50.2,2308,13,16,14,10,56,780,1.467,4.767,5.852,2.757,2.938,4.026,64.7,629,590,2175,4790,0.9,17.54,3.4,26000,523,1.56,60,240,510,220,300,3444,2641,12.7,17.9,23,65400,43600,11700,13000,41600,69800,65400,52300,82900,34100,27100,21800,10500,24500,130800,130800  ],\n  [\"Shanghai\",37.2,38.4,56.2,49.7,20.9,21.6,1966,9,28,43,8,142,740,2.242,2.535,4.674,-0.683,3.325,5.417,48.5,404,470,712,3950,0.58,10.4,3.64,29400,76,1.26,64,250,440,400,960,1424,919,5.4,6.8,17,12200,8500,9300,6700,9200,23700,39600,24700,15800,28900,12300,7400,8200,7600,53200,53200  ],\n  [\"Singapore\",50.8,53.3,94.9,89.2,48.2,50.7,2036,14,18,21,12,58,920,0.973,2.096,6.514,0.589,2.824,5.247,87.1,589,710,1994,5120,1.36,null,8.65,124900,966,1.25,88,180,410,530,840,4455,3496,12.8,15.7,18,41200,21900,19600,15000,27200,30400,82600,77600,46800,27800,28200,19000,14500,21600,86800,86800  ],\n  [\"Sofia\",32.6,32.1,42.4,36.5,13.8,13.6,1894,22,36,20,28,248,420,7.417,7.571,11.95,2.473,3.036,3.389,35.6,265,290,336,3890,0.66,6.62,2,27700,119,1.71,19,80,220,270,430,764,453,3.4,4.5,23,4700,7300,7500,5700,9300,11100,18100,15200,10400,10700,7400,7400,5100,10900,21900,21900  ],\n  [\"Stockholm\",90.2,84.9,92,81.7,82.9,78.1,1795,26,17,18,11,45,810,1.498,1.677,3.298,1.989,1.907,1.366,79.7,553,900,1178,4950,4.52,41.6,24.64,36400,334,2.14,68,180,340,760,1240,2525,1826,19.7,27,26,46600,41300,41300,43300,44300,45800,88800,81900,72500,48600,41100,37400,37900,41700,83400,83400  ],\n  [\"Sydney\",112.5,117.1,83.7,77.8,94.1,98,1846,15,11,9,6,32,690,3.538,2.332,4.353,1.82,2.845,3.389,75.9,508,680,1644,5210,3.43,39.35,9.75,22200,245,1.5,45,220,350,580,820,4183,2175,24.7,30.6,18,57400,41800,39400,39200,72000,55900,111000,93400,79000,52200,50500,40400,50500,36800,110300,110300  ],\n  [\"Taipei\",52,61.4,63.9,58,33.3,39.3,2115,11,15,9,11,79,650,0.598,1.798,3.527,-0.872,0.963,1.422,56.6,448,490,945,4290,0.68,11.25,4.37,22400,382,1.1,63,120,350,980,1070,2434,1696,9.9,10.8,8,25600,23200,16700,20100,24700,41300,61500,44400,29300,20600,15500,12700,11000,14800,36000,36000  ],\n  [\"Tallinn\",47.9,48.6,58.3,50.2,28,28.3,1760,28,27,20,15,139,490,4.43,6.598,10.366,-0.085,2.894,5.121,49,333,380,453,5200,1.81,9.79,5.86,21200,null,1.72,34,160,250,610,830,984,712,7.1,9.1,20,11400,17700,21200,10600,17900,21400,51000,16600,27300,11800,14200,8800,10600,13600,27800,27800  ],\n  [\"Tel Aviv\",57,57.6,75.4,68.5,43,43.5,1966,16,17,9,20,100,600,2.107,0.516,4.745,3.342,2.686,3.45,66.8,476,620,1282,5740,1.72,16.95,13.57,33900,413,2.14,40,180,360,440,580,2577,1709,11,14,18,20700,26500,18900,21000,25000,33500,48700,56200,56200,28900,18400,12300,15200,19700,83000,83000  ],\n  [\"Tokyo\",84.7,82.9,109,100.1,92.4,90.4,2012,16,8,14,15,35,1190,0.3,0,1.396,-1.347,-0.72,-0.283,97.7,927,940,1631,4820,2.46,44.72,21.42,26300,495,1.62,72,370,730,1220,1880,6177,2486,22.8,30.1,24,78200,56300,54000,47000,77700,70200,89400,102100,77200,79400,48800,35100,48000,44700,144000,144000  ],\n  [\"Toronto\",103.4,92.4,74.3,67.2,76.8,68.6,1847,14,10,11,9,38,680,2.018,2.123,2.385,0.3,1.776,2.891,65.6,453,750,1087,4520,3.08,35.62,13.31,15000,75,1.25,71,150,340,310,840,2564,2020,17.3,25,27,82900,36700,33300,46200,44300,53000,66600,47300,84800,32900,26300,28000,29700,37400,74600,74600  ],\n  [\"Vilnius\",42.6,41.6,50.9,43.6,21.7,21.2,1789,24,32,19,33,168,410,3.788,5.772,11.138,4.164,1.19,4.124,42.5,284,360,323,4770,0.94,13.73,4.63,23700,null,1.72,22,90,220,480,510,984,492,5.3,7.1,23,10500,12200,13900,9800,17700,21900,23600,18500,16600,20200,10400,6500,8500,8000,38500,38500  ],\n  [\"Warsaw\",44.3,40.8,53.7,48,23.8,21.9,1792,23,35,13,24,141,650,1.033,2.493,4.215,3.45,2.514,4.268,46.8,291,420,712,4410,0.79,14.62,3.15,25000,55,1.76,31,110,280,580,950,1618,1204,5.5,7.7,28,11900,10000,12600,9700,13200,20500,20900,27600,17900,11900,11700,11000,8400,7600,24900,24900  ],\n  [\"Vienna\",100.6,88.8,81.3,72.1,80.2,70.8,1786,25,13,8,8,46,830,1.686,2.203,3.223,0.401,1.69,3.6,70.3,503,680,945,5560,2.59,42.03,17.27,29800,453,1.8,47,140,360,980,1040,2486,1424,17.8,26.1,29,44700,42900,34100,29500,56200,49000,96100,82900,69800,49100,49900,30000,25400,32600,72500,72500  ],\n  [\"Zurich\",119.1,120.3,110,102.5,131.1,132.4,1887,23,12,5,5,22,1250,1.047,0.732,2.43,-0.476,0.685,0.228,100,704,1130,2551,5130,4.66,68.47,28.93,45200,426,2.01,90,280,630,1100,1190,4481,2499,33.4,42.7,21,104600,90700,68900,61800,79800,69900,137200,130000,115700,96900,71100,61400,53200,58900,140400,140400  ]\n];\n\n\n\nfunction makeMapData(rawData) {\n    var mapData = [];\n    for (var i = 0; i < rawData.length; i++) {\n        var geoCoord = geoCoordMap[rawData[i][0]];\n        if (geoCoord) {\n            mapData.push({\n                name: rawData[i][0],\n                value: geoCoord.concat(rawData[i].slice(1))\n            });\n        }\n    }\n    return mapData;\n};\n\nfunction makeParallelAxis(schema) {\n    var parallelAxis = [];\n    for (var i = 1; i < schema.length; i++) {\n        parallelAxis.push({dim: i, name: schema[i]});\n    }\n    return parallelAxis;\n}\n\n\nconst option = {\n    parallel: {\n        axisExpandable: true,\n        axisExpandCenter: 15,\n        axisExpandCount: 10,\n        axisExpandWidth: 60\n    },\n    parallelAxis: makeParallelAxis(schema),\n    series: [\n        {\n            name: 'parallel',\n            type: 'parallel',\n            smooth: true,\n            lineStyle: {\n                color: '#577ceb',\n                width: 0.5,\n                opacity: 0.6\n            },\n            z: 100,\n            blendMode: 'lighter',\n            data: rawData\n        }\n    ]\n};\n\n", "name": "parallel-large", "title": "大数据量平行坐标"}]}, "parallelAxis": {"desc": "<p>这个组件是平行坐标系中的坐标轴。</p>\n<p><strong>平行坐标系介绍</strong></p>\n<p><a href=\"https://en.wikipedia.org/wiki/Parallel_coordinates\" target=\"_blank\">平行坐标系（Parallel Coordinates）</a> 是一种常用的可视化高维数据的图表。</p>\n<p>例如 <a href=\"#series-parallel.data\">series-parallel.data</a> 中有如下数据：</p>\n<pre><code class=\"lang-javascript\">[\n    [1,  55,  9,   56,  0.46,  18,  6,  &#39;良&#39;],\n    [2,  25,  11,  21,  0.65,  34,  9,  &#39;优&#39;],\n    [3,  56,  7,   63,  0.3,   14,  5,  &#39;良&#39;],\n    [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n    { // 数据项也可以是 Object，从而里面能含有对线条的特殊设置。\n        value: [5,  42,  24,  44,  0.76,  40,  16, &#39;优&#39;]\n        lineStyle: {...},\n    }\n    ...\n]\n</code></pre>\n<p>数据中，每一行是一个『数据项』，每一列属于一个『维度』。（例如上面数据每一列的含义分别是：『日期』,『AQI指数』, 『PM2.5』, 『PM10』, 『一氧化碳值』, 『二氧化氮值』, 『二氧化硫值』）。</p>\n<p>平行坐标系适用于对这种多维数据进行可视化分析。每一个维度（每一列）对应一个坐标轴，每一个『数据项』是一条线，贯穿多个坐标轴。在坐标轴上，可以进行数据选取等操作。如下：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/parallel-all&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p><strong>配置方式概要</strong></p>\n<p>『平行坐标系』的 <code class=\"codespan\">option</code> 基本配置如下例：</p>\n<pre><code class=\"lang-javascript\">option = {\n    parallelAxis: [                     // 这是一个个『坐标轴』的定义\n        {dim: 0, name: schema[0].text}, // 每个『坐标轴』有个 &#39;dim&#39; 属性，表示坐标轴的维度号。\n        {dim: 1, name: schema[1].text},\n        {dim: 2, name: schema[2].text},\n        {dim: 3, name: schema[3].text},\n        {dim: 4, name: schema[4].text},\n        {dim: 5, name: schema[5].text},\n        {dim: 6, name: schema[6].text},\n        {dim: 7, name: schema[7].text,\n            type: &#39;category&#39;,           // 坐标轴也可以支持类别型数据\n            data: [&#39;优&#39;, &#39;良&#39;, &#39;轻度污染&#39;, &#39;中度污染&#39;, &#39;重度污染&#39;, &#39;严重污染&#39;]\n        }\n    ],\n    parallel: {                         // 这是『坐标系』的定义\n        left: &#39;5%&#39;,                     // 平行坐标系的位置设置\n        right: &#39;13%&#39;,\n        bottom: &#39;10%&#39;,\n        top: &#39;20%&#39;,\n        parallelAxisDefault: {          // 『坐标轴』的公有属性可以配置在这里避免重复书写\n            type: &#39;value&#39;,\n            nameLocation: &#39;end&#39;,\n            nameGap: 20\n        }\n    },\n    series: [                           // 这里三个系列共用一个平行坐标系\n        {\n            name: &#39;北京&#39;,\n            type: &#39;parallel&#39;,           // 这个系列类型是 &#39;parallel&#39;\n            data: [\n                [1,  55,  9,   56,  0.46,  18,  6,  &#39;良&#39;],\n                [2,  25,  11,  21,  0.65,  34,  9,  &#39;优&#39;],\n                ...\n            ]\n        },\n        {\n            name: &#39;上海&#39;,\n            type: &#39;parallel&#39;,\n            data: [\n                [3,  56,  7,   63,  0.3,   14,  5,  &#39;良&#39;],\n                [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n                ...\n            ]\n        },\n        {\n            name: &#39;广州&#39;,\n            type: &#39;parallel&#39;,\n            data: [\n                [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n                [5,  42,  24,  44,  0.76,  40,  16, &#39;优&#39;],\n                ...\n            ]\n        }\n    ]\n};\n</code></pre>\n<p>需要涉及到三个组件：<a href=\"#parallel\">parallel</a>、<a href=\"#parallelAxis\">parallelAxis</a>、<a href=\"#series-parallel\">series-parallel</a></p>\n<ul>\n<li><p><a href=\"#parallel\">parallel</a></p>\n<p>  这个配置项是平行坐标系的『坐标系』本身。一个系列（<code class=\"codespan\">series</code>）或多个系列（如上图中的『北京』、『上海』、『广州』分别各是一个系列）可以共用这个『坐标系』。</p>\n<p>  和其他坐标系一样，坐标系也可以创建多个。</p>\n<p>  位置设置，也是放在这里进行。</p>\n</li>\n<li><p><a href=\"#parallelAxis\">parallelAxis</a></p>\n<p>  这个是『坐标系』中的坐标轴的配置。自然，需要有多个坐标轴。</p>\n<p>  其中有 <a href=\"#parallelAxis.parallelIndex\">parallelAxis.parallelIndex</a> 属性，指定这个『坐标轴』在哪个『坐标系』中。默认使用第一个『坐标系』。</p>\n</li>\n<li><p><a href=\"#series-parallel\">series-parallel</a></p>\n<p>  这个是『系列』的定义。系列被画到『坐标系』上。</p>\n<p>  其中有 <a href=\"#series-parallel.parallelIndex\">series-parallel.parallelIndex</a> 属性，指定使用哪个『坐标系』。默认使用第一个『坐标系』。</p>\n</li>\n</ul>\n<p><strong>配置注意和最佳实践</strong></p>\n<p>配置多个 <a href=\"#parallelAxis\">parallelAxis</a> 时，有些值一样的属性，如果书写多遍则比较繁琐，那么可以放置在 <a href=\"#parallel.parallelAxisDefault\">parallel.parallelAxisDefault</a> 里。在坐标轴初始化前，<a href=\"#parallel.parallelAxisDefault\">parallel.parallelAxisDefault</a> 里的配置项，会分别融合进 <a href=\"#parallelAxis\">parallelAxis</a>，形成最终的坐标轴的配置。</p>\n<p><strong>如果数据量很大并且发生卡顿</strong></p>\n<p>建议把 <a href=\"#series-parallel.lineStyle.width\">series-parallel.lineStyle.width</a> 设为 <code class=\"codespan\">0.5</code>（或更小），\n可能显著改善性能。</p>\n<p><strong>高维数据的显示</strong></p>\n<p>维度比较多时，比如有 50+ 的维度，那么就会有 50+ 个轴。那么可能会页面显示不下。</p>\n<p>可以通过 <a href=\"#parallel.axisExpandable\">parallel.axisExpandable</a> 来改善显示效果。</p>\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst dataBJ = [\n    [1,55,9,56,0.46,18,6,\"良\"],\n    [2,25,11,21,0.65,34,9,\"优\"],\n    [3,56,7,63,0.3,14,5,\"良\"],\n    [4,33,7,29,0.33,16,6,\"优\"],\n    [5,42,24,44,0.76,40,16,\"优\"],\n    [6,82,58,90,1.77,68,33,\"良\"],\n    [7,74,49,77,1.46,48,27,\"良\"],\n    [8,78,55,80,1.29,59,29,\"良\"],\n    [9,267,216,280,4.8,108,64,\"重度污染\"],\n    [10,185,127,216,2.52,61,27,\"中度污染\"],\n    [11,39,19,38,0.57,31,15,\"优\"],\n    [12,41,11,40,0.43,21,7,\"优\"],\n    [13,64,38,74,1.04,46,22,\"良\"],\n    [14,108,79,120,1.7,75,41,\"轻度污染\"],\n    [15,108,63,116,1.48,44,26,\"轻度污染\"],\n    [16,33,6,29,0.34,13,5,\"优\"],\n    [17,94,66,110,1.54,62,31,\"良\"],\n    [18,186,142,192,3.88,93,79,\"中度污染\"],\n    [19,57,31,54,0.96,32,14,\"良\"],\n    [20,22,8,17,0.48,23,10,\"优\"],\n    [21,39,15,36,0.61,29,13,\"优\"],\n    [22,94,69,114,2.08,73,39,\"良\"],\n    [23,99,73,110,2.43,76,48,\"良\"],\n    [24,31,12,30,0.5,32,16,\"优\"],\n    [25,42,27,43,1,53,22,\"优\"],\n    [26,154,117,157,3.05,92,58,\"中度污染\"],\n    [27,234,185,230,4.09,123,69,\"重度污染\"],\n    [28,160,120,186,2.77,91,50,\"中度污染\"],\n    [29,134,96,165,2.76,83,41,\"轻度污染\"],\n    [30,52,24,60,1.03,50,21,\"良\"],\n    [31,46,5,49,0.28,10,6,\"优\"]\n];\n\nconst dataGZ = [\n    [1,26,37,27,1.163,27,13,\"优\"],\n    [2,85,62,71,1.195,60,8,\"良\"],\n    [3,78,38,74,1.363,37,7,\"良\"],\n    [4,21,21,36,0.634,40,9,\"优\"],\n    [5,41,42,46,0.915,81,13,\"优\"],\n    [6,56,52,69,1.067,92,16,\"良\"],\n    [7,64,30,28,0.924,51,2,\"良\"],\n    [8,55,48,74,1.236,75,26,\"良\"],\n    [9,76,85,113,1.237,114,27,\"良\"],\n    [10,91,81,104,1.041,56,40,\"良\"],\n    [11,84,39,60,0.964,25,11,\"良\"],\n    [12,64,51,101,0.862,58,23,\"良\"],\n    [13,70,69,120,1.198,65,36,\"良\"],\n    [14,77,105,178,2.549,64,16,\"良\"],\n    [15,109,68,87,0.996,74,29,\"轻度污染\"],\n    [16,73,68,97,0.905,51,34,\"良\"],\n    [17,54,27,47,0.592,53,12,\"良\"],\n    [18,51,61,97,0.811,65,19,\"良\"],\n    [19,91,71,121,1.374,43,18,\"良\"],\n    [20,73,102,182,2.787,44,19,\"良\"],\n    [21,73,50,76,0.717,31,20,\"良\"],\n    [22,84,94,140,2.238,68,18,\"良\"],\n    [23,93,77,104,1.165,53,7,\"良\"],\n    [24,99,130,227,3.97,55,15,\"良\"],\n    [25,146,84,139,1.094,40,17,\"轻度污染\"],\n    [26,113,108,137,1.481,48,15,\"轻度污染\"],\n    [27,81,48,62,1.619,26,3,\"良\"],\n    [28,56,48,68,1.336,37,9,\"良\"],\n    [29,82,92,174,3.29,0,13,\"良\"],\n    [30,106,116,188,3.628,101,16,\"轻度污染\"],\n    [31,118,50,0,1.383,76,11,\"轻度污染\"]\n];\n\nconst dataSH = [\n    [1,91,45,125,0.82,34,23,\"良\"],\n    [2,65,27,78,0.86,45,29,\"良\"],\n    [3,83,60,84,1.09,73,27,\"良\"],\n    [4,109,81,121,1.28,68,51,\"轻度污染\"],\n    [5,106,77,114,1.07,55,51,\"轻度污染\"],\n    [6,109,81,121,1.28,68,51,\"轻度污染\"],\n    [7,106,77,114,1.07,55,51,\"轻度污染\"],\n    [8,89,65,78,0.86,51,26,\"良\"],\n    [9,53,33,47,0.64,50,17,\"良\"],\n    [10,80,55,80,1.01,75,24,\"良\"],\n    [11,117,81,124,1.03,45,24,\"轻度污染\"],\n    [12,99,71,142,1.1,62,42,\"良\"],\n    [13,95,69,130,1.28,74,50,\"良\"],\n    [14,116,87,131,1.47,84,40,\"轻度污染\"],\n    [15,108,80,121,1.3,85,37,\"轻度污染\"],\n    [16,134,83,167,1.16,57,43,\"轻度污染\"],\n    [17,79,43,107,1.05,59,37,\"良\"],\n    [18,71,46,89,0.86,64,25,\"良\"],\n    [19,97,71,113,1.17,88,31,\"良\"],\n    [20,84,57,91,0.85,55,31,\"良\"],\n    [21,87,63,101,0.9,56,41,\"良\"],\n    [22,104,77,119,1.09,73,48,\"轻度污染\"],\n    [23,87,62,100,1,72,28,\"良\"],\n    [24,168,128,172,1.49,97,56,\"中度污染\"],\n    [25,65,45,51,0.74,39,17,\"良\"],\n    [26,39,24,38,0.61,47,17,\"优\"],\n    [27,39,24,39,0.59,50,19,\"优\"],\n    [28,93,68,96,1.05,79,29,\"良\"],\n    [29,188,143,197,1.66,99,51,\"中度污染\"],\n    [30,174,131,174,1.55,108,50,\"中度污染\"],\n    [31,187,143,201,1.39,89,53,\"中度污染\"]\n];\n\nconst schema = [\n    {name: 'date', index: 0, text: '日期'},\n    {name: 'AQIindex', index: 1, text: 'AQI'},\n    {name: 'PM25', index: 2, text: 'PM2.5'},\n    {name: 'PM10', index: 3, text: 'PM10'},\n    {name: 'CO', index: 4, text: ' CO'},\n    {name: 'NO2', index: 5, text: 'NO2'},\n    {name: 'SO2', index: 6, text: 'SO2'},\n    {name: '等级', index: 7, text: '等级'}\n];\n\nconst option = {\n    color: [\n        '#c23531', '#91c7ae', '#dd8668'\n    ],\n    legend: {\n        top: 10,\n        data: ['北京', '上海', '广州'],\n        itemGap: 20\n    },\n    parallelAxis: [\n        {dim: 0, name: schema[0].text, inverse: true, max: 31, nameLocation: 'start'},\n        {dim: 1, name: schema[1].text},\n        {dim: 2, name: schema[2].text},\n        {dim: 3, name: schema[3].text},\n        {dim: 4, name: schema[4].text},\n        {dim: 5, name: schema[5].text},\n        {dim: 6, name: schema[6].text},\n        {dim: 7, name: schema[7].text,\n        type: 'category', data: ['优', '良', '轻度污染', '中度污染', '重度污染', '严重污染']}\n    ],\n    parallel: {\n        left: '5%',\n        right: '13%',\n        bottom: '10%',\n        top: '20%',\n        parallelAxisDefault: {\n            type: 'value',\n            name: 'AQI指数',\n            nameLocation: 'end',\n            nameGap: 20,\n            nameTextStyle: {\n                fontSize: 12\n            }\n        }\n    },\n    series: [\n        {\n            name: '北京',\n            type: 'parallel',\n            data: dataBJ\n        },\n        {\n            name: '上海',\n            type: 'parallel',\n            data: dataSH\n        },\n        {\n            name: '广州',\n            type: 'parallel',\n            data: dataGZ\n        }\n    ]\n};\n", "name": "parallel-axis", "title": "平行坐标", "title-en": "<PERSON><PERSON><PERSON>"}]}, "singleAxis": {"desc": "<p>单轴。可以被应用到散点图中展现一维数据，如下示例</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=scatter-single-axis&edit=1&reset=1\" width=\"700\" height=\"500\"><iframe />\n\n\n\n"}, "timeline": {"desc": "<p><code class=\"codespan\">timeline</code> 组件，提供了在多个 ECharts <code class=\"codespan\">option</code> 间进行切换、播放等操作的功能。</p>\n<p>示例效果如下：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/mix-timeline-all&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p><code class=\"codespan\">timeline</code> 和其他组件有些不同，它需要操作『多个option』。\n假设，我们把 ECharts 的传统的 option 称为<em>原子option</em>，那么使用 <code class=\"codespan\">timeline</code> 时，传入 ECharts 的 option 就成为了一个集合多个原子option的<em>复合option</em>。如下示例：</p>\n<pre><code class=\"lang-javascript\">// 如下，baseOption 是一个 『原子option』，options 数组中的每一项也是一个 『原子option』。\n// 每个『原子option』中就是本文档中描述的各种配置项。\nmyChart.setOption(\n    {\n        baseOption: {\n            timeline: {\n                ...,\n                data: [&#39;2002-01-01&#39;, &#39;2003-01-01&#39;, &#39;2004-01-01&#39;]\n            },\n            grid: {...},\n            xAxis: [...],\n            yAxis: [...],\n            series: [\n                { // 系列一的一些其他配置\n                    type: &#39;bar&#39;,\n                    ...\n                },\n                { // 系列二的一些其他配置\n                    type: &#39;line&#39;,\n                    ...\n                },\n                { // 系列三的一些其他配置\n                    type: &#39;pie&#39;,\n                    ...\n                }\n            ]\n        },\n        options: [\n            { // 这是&#39;2002-01-01&#39; 对应的 option\n                title: {\n                    text: &#39;2002年统计值&#39;\n                },\n                series: [\n                    {data: []}, // 系列一的数据\n                    {data: []}, // 系列二的数据\n                    {data: []}  // 系列三的数据\n                ]\n            },\n            { // 这是&#39;2003-01-01&#39; 对应的 option\n                title: {\n                    text: &#39;2003年统计值&#39;\n                },\n                series: [\n                    {data: []},\n                    {data: []},\n                    {data: []}\n                ]\n            },\n            { // 这是&#39;2004-01-01&#39; 对应的 option\n                title: {\n                    text: &#39;2004年统计值&#39;\n                },\n                series: [\n                    {data: []},\n                    {data: []},\n                    {data: []}\n                ]\n            }\n        ]\n    }\n);\n</code></pre>\n<p>在上例中，<code class=\"codespan\">timeline.data</code> 中的每一项，对应于 <code class=\"codespan\">options</code> 数组中的每个 <code class=\"codespan\">option</code>。</p>\n<p><br>\n<strong>使用注意与最佳实践：</strong></p>\n<ul>\n<li><p>公有的配置项，推荐配置在 <code class=\"codespan\">baseOption</code> 中。<code class=\"codespan\">timeline</code> 播放切换时，会把 <code class=\"codespan\">options</code> 数组中的对应的 <code class=\"codespan\">option</code>，与 <code class=\"codespan\">baseOption</code> 进行 merge 形成最终的 <code class=\"codespan\">option</code>。</p>\n</li>\n<li><p><code class=\"codespan\">options</code> 数组中，如果某一数组项中配置了某个属性，那么其他数组项中也必须配置某个属性，而不能缺省。否则这个属性的执行效果会遗留。</p>\n</li>\n<li><p><em>复合 option</em> 中的 <code class=\"codespan\">options</code> 不支持 merge。</p>\n<p>  也就是说，当第二（或三、四、五 ...）次 <code class=\"codespan\">chart.setOption(rawOption)</code> 时，如果 <code class=\"codespan\">rawOption</code> 是<em>复合 option</em>（即包含 <code class=\"codespan\">options</code> 列表），那么新的 <code class=\"codespan\">rawOption.options</code> 列表不会和老的 <code class=\"codespan\">options</code> 列表进行 merge，而是简单替代。当然，<code class=\"codespan\">rawOption.baseOption</code> 仍然会正常和老的 option 进行merge。</p>\n</li>\n</ul>\n<p><br>\n<strong>与 ECharts 2 的兼容性：</strong></p>\n<ul>\n<li><p>ECharts 3 中不再支持 timeline.notMerge 参数，也就是不支持 notMerge 模式。如果遇到这种场景需要使用，可在外部进行option管理，并用 setOption(option, true) 这样的notMerge方式设置。</p>\n</li>\n<li><p>ECharts 3 和 ECharts 2 相比，timeline 属性的定义位置有所不同，移到了 <code class=\"codespan\">baseOption</code> 中，统一作为一个普通的组件看待。但是，仍然兼容 ECharts2 的 timeline 定义位置，只是不再推荐这样写。</p>\n</li>\n</ul>\n\n", "exampleBaseOptions": [{"code": "\nvar dataMap = {};\nfunction dataFormatter(obj) {\n    var pList = ['北京','天津','河北','山西','内蒙古','辽宁','吉林','黑龙江','上海','江苏','浙江','安徽','福建','江西','山东','河南','湖北','湖南','广东','广西','海南','重庆','四川','贵州','云南','西藏','陕西','甘肃','青海','宁夏','新疆'];\n    var temp;\n    for (var year = 2002; year <= 2011; year++) {\n        var max = 0;\n        var sum = 0;\n        temp = obj[year];\n        for (var i = 0, l = temp.length; i < l; i++) {\n            max = Math.max(max, temp[i]);\n            sum += temp[i];\n            obj[year][i] = {\n                name : pList[i],\n                value : temp[i]\n            }\n        }\n        obj[year + 'max'] = Math.floor(max / 100) * 100;\n        obj[year + 'sum'] = sum;\n    }\n    return obj;\n}\n\ndataMap.dataGDP = dataFormatter({\n    //max : 60000,\n    2011:[16251.93,11307.28,24515.76,11237.55,14359.88,22226.7,10568.83,12582,19195.69,49110.27,32318.85,15300.65,17560.18,11702.82,45361.85,26931.03,19632.26,19669.56,53210.28,11720.87,2522.66,10011.37,21026.68,5701.84,8893.12,605.83,12512.3,5020.37,1670.44,2102.21,6610.05],\n    2010:[14113.58,9224.46,20394.26,9200.86,11672,18457.27,8667.58,10368.6,17165.98,41425.48,27722.31,12359.33,14737.12,9451.26,39169.92,23092.36,15967.61,16037.96,46013.06,9569.85,2064.5,7925.58,17185.48,4602.16,7224.18,507.46,10123.48,4120.75,1350.43,1689.65,5437.47],\n    2009:[12153.03,7521.85,17235.48,7358.31,9740.25,15212.49,7278.75,8587,15046.45,34457.3,22990.35,10062.82,12236.53,7655.18,33896.65,19480.46,12961.1,13059.69,39482.56,7759.16,1654.21,6530.01,14151.28,3912.68,6169.75,441.36,8169.8,3387.56,1081.27,1353.31,4277.05],\n    2008:[11115,6719.01,16011.97,7315.4,8496.2,13668.58,6426.1,8314.37,14069.87,30981.98,21462.69,8851.66,10823.01,6971.05,30933.28,18018.53,11328.92,11555,36796.71,7021,1503.06,5793.66,12601.23,3561.56,5692.12,394.85,7314.58,3166.82,1018.62,1203.92,4183.21],\n    2007:[9846.81,5252.76,13607.32,6024.45,6423.18,11164.3,5284.69,7104,12494.01,26018.48,18753.73,7360.92,9248.53,5800.25,25776.91,15012.46,9333.4,9439.6,31777.01,5823.41,1254.17,4676.13,10562.39,2884.11,4772.52,341.43,5757.29,2703.98,797.35,919.11,3523.16],\n    2006:[8117.78,4462.74,11467.6,4878.61,4944.25,9304.52,4275.12,6211.8,10572.24,21742.05,15718.47,6112.5,7583.85,4820.53,21900.19,12362.79,7617.47,7688.67,26587.76,4746.16,1065.67,3907.23,8690.24,2338.98,3988.14,290.76,4743.61,2277.35,648.5,725.9,3045.26],\n    2005:[6969.52,3905.64,10012.11,4230.53,3905.03,8047.26,3620.27,5513.7,9247.66,18598.69,13417.68,5350.17,6554.69,4056.76,18366.87,10587.42,6590.19,6596.1,22557.37,3984.1,918.75,3467.72,7385.1,2005.42,3462.73,248.8,3933.72,1933.98,543.32,612.61,2604.19],\n    2004:[6033.21,3110.97,8477.63,3571.37,3041.07,6672,3122.01,4750.6,8072.83,15003.6,11648.7,4759.3,5763.35,3456.7,15021.84,8553.79,5633.24,5641.94,18864.62,3433.5,819.66,3034.58,6379.63,1677.8,3081.91,220.34,3175.58,1688.49,466.1,537.11,2209.09],\n    2003:[5007.21,2578.03,6921.29,2855.23,2388.38,6002.54,2662.08,4057.4,6694.23,12442.87,9705.02,3923.11,4983.67,2807.41,12078.15,6867.7,4757.45,4659.99,15844.64,2821.11,713.96,2555.72,5333.09,1426.34,2556.02,185.09,2587.72,1399.83,390.2,445.36,1886.35],\n    2002:[4315,2150.76,6018.28,2324.8,1940.94,5458.22,2348.54,3637.2,5741.03,10606.85,8003.67,3519.72,4467.55,2450.48,10275.5,6035.48,4212.82,4151.54,13502.42,2523.73,642.73,2232.86,4725.01,1243.43,2312.82,162.04,2253.39,1232.03,340.65,377.16,1612.6]\n});\n\ndataMap.dataPI = dataFormatter({\n    //max : 4000,\n    2011:[136.27,159.72,2905.73,641.42,1306.3,1915.57,1277.44,1701.5,124.94,3064.78,1583.04,2015.31,1612.24,1391.07,3973.85,3512.24,2569.3,2768.03,2665.2,2047.23,659.23,844.52,2983.51,726.22,1411.01,74.47,1220.9,678.75,155.08,184.14,1139.03],\n    2010:[124.36,145.58,2562.81,554.48,1095.28,1631.08,1050.15,1302.9,114.15,2540.1,1360.56,1729.02,1363.67,1206.98,3588.28,3258.09,2147,2325.5,2286.98,1675.06,539.83,685.38,2482.89,625.03,1108.38,68.72,988.45,599.28,134.92,159.29,1078.63],\n    2009:[118.29,128.85,2207.34,477.59,929.6,1414.9,980.57,1154.33,113.82,2261.86,1163.08,1495.45,1182.74,1098.66,3226.64,2769.05,1795.9,1969.69,2010.27,1458.49,462.19,606.8,2240.61,550.27,1067.6,63.88,789.64,497.05,107.4,127.25,759.74],\n    2008:[112.83,122.58,2034.59,313.58,907.95,1302.02,916.72,1088.94,111.8,2100.11,1095.96,1418.09,1158.17,1060.38,3002.65,2658.78,1780,1892.4,1973.05,1453.75,436.04,575.4,2216.15,539.19,1020.56,60.62,753.72,462.27,105.57,118.94,691.07],\n    2007:[101.26,110.19,1804.72,311.97,762.1,1133.42,783.8,915.38,101.84,1816.31,986.02,1200.18,1002.11,905.77,2509.14,2217.66,1378,1626.48,1695.57,1241.35,361.07,482.39,2032,446.38,837.35,54.89,592.63,387.55,83.41,97.89,628.72],\n    2006:[88.8,103.35,1461.81,276.77,634.94,939.43,672.76,750.14,93.81,1545.05,925.1,1011.03,865.98,786.14,2138.9,1916.74,1140.41,1272.2,1532.17,1032.47,323.48,386.38,1595.48,382.06,724.4,50.9,484.81,334,67.55,79.54,527.8],\n    2005:[88.68,112.38,1400,262.42,589.56,882.41,625.61,684.6,90.26,1461.51,892.83,966.5,827.36,727.37,1963.51,1892.01,1082.13,1100.65,1428.27,912.5,300.75,463.4,1481.14,368.94,661.69,48.04,435.77,308.06,65.34,72.07,509.99],\n    2004:[87.36,105.28,1370.43,276.3,522.8,798.43,568.69,605.79,83.45,1367.58,814.1,950.5,786.84,664.5,1778.45,1649.29,1020.09,1022.45,1248.59,817.88,278.76,428.05,1379.93,334.5,607.75,44.3,387.88,286.78,60.7,65.33,461.26],\n    2003:[84.11,89.91,1064.05,215.19,420.1,615.8,488.23,504.8,81.02,1162.45,717.85,749.4,692.94,560,1480.67,1198.7,798.35,886.47,1072.91,658.78,244.29,339.06,1128.61,298.69,494.6,40.7,302.66,237.91,48.47,55.63,412.9],\n    2002:[82.44,84.21,956.84,197.8,374.69,590.2,446.17,474.2,79.68,1110.44,685.2,783.66,664.78,535.98,1390,1288.36,707,847.25,1015.08,601.99,222.89,317.87,1047.95,281.1,463.44,39.75,282.21,215.51,47.31,52.95,305]\n});\n\ndataMap.dataSI = dataFormatter({\n    //max : 26600,\n    2011:[3752.48,5928.32,13126.86,6635.26,8037.69,12152.15,5611.48,5962.41,7927.89,25203.28,16555.58,8309.38,9069.2,6390.55,24017.11,15427.08,9815.94,9361.99,26447.38,5675.32,714.5,5543.04,11029.13,2194.33,3780.32,208.79,6935.59,2377.83,975.18,1056.15,3225.9],\n    2010:[3388.38,4840.23,10707.68,5234,6367.69,9976.82,4506.31,5025.15,7218.32,21753.93,14297.93,6436.62,7522.83,5122.88,21238.49,13226.38,7767.24,7343.19,23014.53,4511.68,571,4359.12,8672.18,1800.06,3223.49,163.92,5446.1,1984.97,744.63,827.91,2592.15],\n    2009:[2855.55,3987.84,8959.83,3993.8,5114,7906.34,3541.92,4060.72,6001.78,18566.37,11908.49,4905.22,6005.3,3919.45,18901.83,11010.5,6038.08,5687.19,19419.7,3381.54,443.43,3448.77,6711.87,1476.62,2582.53,136.63,4236.42,1527.24,575.33,662.32,1929.59],\n    2008:[2626.41,3709.78,8701.34,4242.36,4376.19,7158.84,3097.12,4319.75,6085.84,16993.34,11567.42,4198.93,5318.44,3554.81,17571.98,10259.99,5082.07,5028.93,18502.2,3037.74,423.55,3057.78,5823.39,1370.03,2452.75,115.56,3861.12,1470.34,557.12,609.98,2070.76],\n    2007:[2509.4,2892.53,7201.88,3454.49,3193.67,5544.14,2475.45,3695.58,5571.06,14471.26,10154.25,3370.96,4476.42,2975.53,14647.53,8282.83,4143.06,3977.72,16004.61,2425.29,364.26,2368.53,4648.79,1124.79,2038.39,98.48,2986.46,1279.32,419.03,455.04,1647.55],\n    2006:[2191.43,2457.08,6110.43,2755.66,2374.96,4566.83,1915.29,3365.31,4969.95,12282.89,8511.51,2711.18,3695.04,2419.74,12574.03,6724.61,3365.08,3187.05,13469.77,1878.56,308.62,1871.65,3775.14,967.54,1705.83,80.1,2452.44,1043.19,331.91,351.58,1459.3],\n    2005:[2026.51,2135.07,5271.57,2357.04,1773.21,3869.4,1580.83,2971.68,4381.2,10524.96,7164.75,2245.9,3175.92,1917.47,10478.62,5514.14,2852.12,2612.57,11356.6,1510.68,240.83,1564,3067.23,821.16,1426.42,63.52,1951.36,838.56,264.61,281.05,1164.79],\n    2004:[1853.58,1685.93,4301.73,1919.4,1248.27,3061.62,1329.68,2487.04,3892.12,8437.99,6250.38,1844.9,2770.49,1566.4,8478.69,4182.1,2320.6,2190.54,9280.73,1253.7,205.6,1376.91,2489.4,681.5,1281.63,52.74,1553.1,713.3,211.7,244.05,914.47],\n    2003:[1487.15,1337.31,3417.56,1463.38,967.49,2898.89,1098.37,2084.7,3209.02,6787.11,5096.38,1535.29,2340.82,1204.33,6485.05,3310.14,1956.02,1777.74,7592.78,984.08,175.82,1135.31,2014.8,569.37,1047.66,47.64,1221.17,572.02,171.92,194.27,719.54],\n    2002:[1249.99,1069.08,2911.69,1134.31,754.78,2609.85,943.49,1843.6,2622.45,5604.49,4090.48,1337.04,2036.97,941.77,5184.98,2768.75,1709.89,1523.5,6143.4,846.89,148.88,958.87,1733.38,481.96,934.88,32.72,1007.56,501.69,144.51,153.06,603.15]\n});\n\ndataMap.dataTI = dataFormatter({\n    //max : 25000,\n    2011:[12363.18,5219.24,8483.17,3960.87,5015.89,8158.98,3679.91,4918.09,11142.86,20842.21,14180.23,4975.96,6878.74,3921.2,17370.89,7991.72,7247.02,7539.54,24097.7,3998.33,1148.93,3623.81,7014.04,2781.29,3701.79,322.57,4355.81,1963.79,540.18,861.92,2245.12],\n    2010:[10600.84,4238.65,7123.77,3412.38,4209.03,6849.37,3111.12,4040.55,9833.51,17131.45,12063.82,4193.69,5850.62,3121.4,14343.14,6607.89,6053.37,6369.27,20711.55,3383.11,953.67,2881.08,6030.41,2177.07,2892.31,274.82,3688.93,1536.5,470.88,702.45,1766.69],\n    2009:[9179.19,3405.16,6068.31,2886.92,3696.65,5891.25,2756.26,3371.95,8930.85,13629.07,9918.78,3662.15,5048.49,2637.07,11768.18,5700.91,5127.12,5402.81,18052.59,2919.13,748.59,2474.44,5198.8,1885.79,2519.62,240.85,3143.74,1363.27,398.54,563.74,1587.72],\n    2008:[8375.76,2886.65,5276.04,2759.46,3212.06,5207.72,2412.26,2905.68,7872.23,11888.53,8799.31,3234.64,4346.4,2355.86,10358.64,5099.76,4466.85,4633.67,16321.46,2529.51,643.47,2160.48,4561.69,1652.34,2218.81,218.67,2699.74,1234.21,355.93,475,1421.38],\n    2007:[7236.15,2250.04,4600.72,2257.99,2467.41,4486.74,2025.44,2493.04,6821.11,9730.91,7613.46,2789.78,3770,1918.95,8620.24,4511.97,3812.34,3835.4,14076.83,2156.76,528.84,1825.21,3881.6,1312.94,1896.78,188.06,2178.2,1037.11,294.91,366.18,1246.89],\n    2006:[5837.55,1902.31,3895.36,1846.18,1934.35,3798.26,1687.07,2096.35,5508.48,7914.11,6281.86,2390.29,3022.83,1614.65,7187.26,3721.44,3111.98,3229.42,11585.82,1835.12,433.57,1649.2,3319.62,989.38,1557.91,159.76,1806.36,900.16,249.04,294.78,1058.16],\n    2005:[4854.33,1658.19,3340.54,1611.07,1542.26,3295.45,1413.83,1857.42,4776.2,6612.22,5360.1,2137.77,2551.41,1411.92,5924.74,3181.27,2655.94,2882.88,9772.5,1560.92,377.17,1440.32,2836.73,815.32,1374.62,137.24,1546.59,787.36,213.37,259.49,929.41],\n    2004:[4092.27,1319.76,2805.47,1375.67,1270,2811.95,1223.64,1657.77,4097.26,5198.03,4584.22,1963.9,2206.02,1225.8,4764.7,2722.4,2292.55,2428.95,8335.3,1361.92,335.3,1229.62,2510.3,661.8,1192.53,123.3,1234.6,688.41,193.7,227.73,833.36],\n    2003:[3435.95,1150.81,2439.68,1176.65,1000.79,2487.85,1075.48,1467.9,3404.19,4493.31,3890.79,1638.42,1949.91,1043.08,4112.43,2358.86,2003.08,1995.78,7178.94,1178.25,293.85,1081.35,2189.68,558.28,1013.76,96.76,1063.89,589.91,169.81,195.46,753.91],\n    2002:[2982.57,997.47,2149.75,992.69,811.47,2258.17,958.88,1319.4,3038.9,3891.92,3227.99,1399.02,1765.8,972.73,3700.52,1978.37,1795.93,1780.79,6343.94,1074.85,270.96,956.12,1943.68,480.37,914.5,89.56,963.62,514.83,148.83,171.14,704.5]\n});\n\ndataMap.dataEstate = dataFormatter({\n    //max : 3600,\n    2011:[1074.93,411.46,918.02,224.91,384.76,876.12,238.61,492.1,1019.68,2747.89,1677.13,634.92,911.16,402.51,1838.14,987,634.67,518.04,3321.31,465.68,208.71,396.28,620.62,160.3,222.31,17.44,398.03,134.25,29.05,79.01,176.22],\n    2010:[1006.52,377.59,697.79,192,309.25,733.37,212.32,391.89,1002.5,2600.95,1618.17,532.17,679.03,340.56,1622.15,773.23,564.41,464.21,2813.95,405.79,188.33,266.38,558.56,139.64,223.45,14.54,315.95,110.02,25.41,60.53,143.44],\n    2009:[1062.47,308.73,612.4,173.31,286.65,605.27,200.14,301.18,1237.56,2025.39,1316.84,497.94,656.61,305.9,1329.59,622.98,546.11,400.11,2470.63,348.98,121.76,229.09,548.14,136.15,205.14,13.28,239.92,101.37,23.05,47.56,115.23],\n    2008:[844.59,227.88,513.81,166.04,273.3,500.81,182.7,244.47,939.34,1626.13,1052.03,431.27,506.98,281.96,1104.95,512.42,526.88,340.07,2057.45,282.96,95.6,191.21,453.63,104.81,195.48,15.08,193.27,93.8,19.96,38.85,89.79],\n    2007:[821.5,183.44,467.97,134.12,191.01,410.43,153.03,225.81,958.06,1365.71,981.42,366.57,511.5,225.96,953.69,447.44,409.65,301.8,2029.77,239.45,67.19,196.06,376.84,93.19,193.59,13.24,153.98,83.52,16.98,29.49,91.28],\n    2006:[658.3,156.64,397.14,117.01,136.5,318.54,131.01,194.7,773.61,1017.91,794.41,281.98,435.22,184.67,786.51,348.7,294.73,254.81,1722.07,192.2,44.45,158.2,336.2,80.24,165.92,11.92,125.2,73.21,15.17,25.53,68.9],\n    2005:[493.73,122.67,330.87,106,98.75,256.77,112.29,163.34,715.97,799.73,688.86,231.66,331.8,171.88,664.9,298.19,217.17,215.63,1430.37,165.05,38.2,143.88,286.23,76.38,148.69,10.02,108.62,63.78,14.1,22.97,55.79],\n    2004:[436.11,106.14,231.08,95.1,73.81,203.1,97.93,137.74,666.3,534.17,587.83,188.28,248.44,167.2,473.27,236.44,204.8,191.5,1103.75,122.52,30.64,129.12,264.3,68.3,116.54,5.8,95.9,56.84,13,20.78,53.55],\n    2003:[341.88,92.31,185.19,78.73,61.05,188.49,91.99,127.2,487.82,447.47,473.16,162.63,215.84,138.02,418.21,217.58,176.8,186.49,955.66,100.93,25.14,113.69,231.72,59.86,103.79,4.35,83.9,48.09,11.41,16.85,47.84],\n    2002:[298.02,73.04,140.89,65.83,51.48,130.94,76.11,118.7,384.86,371.09,360.63,139.18,188.09,125.27,371.13,199.31,145.17,165.29,808.16,82.83,21.45,90.48,210.82,53.49,95.68,3.42,77.68,41.52,9.74,13.46,43.04]\n});\n\ndataMap.dataFinancial = dataFormatter({\n    //max : 3200,\n    2011:[2215.41,756.5,746.01,519.32,447.46,755.57,207.65,370.78,2277.4,2600.11,2730.29,503.85,862.41,357.44,1640.41,868.2,674.57,501.09,2916.13,445.37,105.24,704.66,868.15,297.27,456.23,31.7,432.11,145.05,62.56,134.18,288.77],\n    2010:[1863.61,572.99,615.42,448.3,346.44,639.27,190.12,304.59,1950.96,2105.92,2326.58,396.17,767.58,241.49,1361.45,697.68,561.27,463.16,2658.76,384.53,78.12,496.56,654.7,231.51,375.08,27.08,384.75,100.54,54.53,97.87,225.2],\n    2009:[1603.63,461.2,525.67,361.64,291.1,560.2,180.83,227.54,1804.28,1596.98,1899.33,359.6,612.2,165.1,1044.9,499.92,479.11,402.57,2283.29,336.82,65.73,389.97,524.63,194.44,351.74,23.17,336.21,88.27,45.63,75.54,198.87],\n    2008:[1519.19,368.1,420.74,290.91,219.09,455.07,147.24,177.43,1414.21,1298.48,1653.45,313.81,497.65,130.57,880.28,413.83,393.05,334.32,1972.4,249.01,47.33,303.01,411.14,151.55,277.66,22.42,287.16,72.49,36.54,64.8,171.97],\n    2007:[1302.77,288.17,347.65,218.73,148.3,386.34,126.03,155.48,1209.08,1054.25,1251.43,223.85,385.84,101.34,734.9,302.31,337.27,260.14,1705.08,190.73,34.43,247.46,359.11,122.25,168.55,11.51,231.03,61.6,27.67,51.05,149.22],\n    2006:[982.37,186.87,284.04,169.63,108.21,303.41,100.75,74.17,825.2,653.25,906.37,166.01,243.9,79.75,524.94,219.72,174.99,204.72,899.91,129.14,16.37,213.7,299.5,89.43,143.62,6.44,152.25,50.51,23.69,36.99,99.25],\n    2005:[840.2,147.4,213.47,135.07,72.52,232.85,83.63,35.03,675.12,492.4,686.32,127.05,186.12,69.55,448.36,181.74,127.32,162.37,661.81,91.93,13.16,185.18,262.26,73.67,130.5,7.57,127.58,44.73,20.36,32.25,80.34],\n    2004:[713.79,136.97,209.1,110.29,55.89,188.04,77.17,32.2,612.45,440.5,523.49,94.1,171,65.1,343.37,170.82,118.85,118.64,602.68,74,11.56,162.38,236.5,60.3,118.4,5.4,90.1,42.99,19,27.92,70.3],\n    2003:[635.56,112.79,199.87,118.48,55.89,145.38,73.15,32.2,517.97,392.11,451.54,87.45,150.09,64.31,329.71,165.11,107.31,99.35,534.28,61.59,10.68,147.04,206.24,48.01,105.48,4.74,77.87,42.31,17.98,24.8,64.92],\n    2002:[561.91,76.86,179.6,124.1,48.39,137.18,75.45,31.6,485.25,368.86,347.53,81.85,138.28,76.51,310.07,158.77,96.95,92.43,454.65,35.86,10.08,134.52,183.13,41.45,102.39,2.81,67.3,42.08,16.75,21.45,52.18]\n});\n\n\nconst option = {\n    baseOption: {\n        timeline: {\n            axisType: 'category',\n            autoPlay: false,\n            data: [\n                '2002-01-01','2003-01-01','2004-01-01',\n                {\n                    value: '2005-01-01',\n                    tooltip: {\n                        formatter: '{b} GDP达到一个高度'\n                    },\n                    symbol: 'diamond',\n                    symbolSize: 16\n                },\n                '2006-01-01', '2007-01-01','2008-01-01','2009-01-01','2010-01-01',\n                {\n                    value: '2011-01-01',\n                    tooltip: {\n                        formatter: function (params) {\n                            return params.name + 'GDP达到又一个高度';\n                        }\n                    },\n                    symbol: 'diamond',\n                    symbolSize: 18\n                },\n            ],\n            label: {\n                formatter : function(s) {\n                    return (new Date(s)).getFullYear();\n                }\n            }\n        },\n        title: {\n            subtext: '数据来自国家统计局'\n        },\n        tooltip: {},\n        legend: {\n            left: 'right',\n            data: ['第一产业', '第二产业', '第三产业'],\n            selected: {\n                'GDP': false, '金融': false, '房地产': false\n            }\n        },\n        calculable : true,\n        grid: {\n            top: 80,\n            bottom: 100\n        },\n        xAxis: [\n            {\n                'type':'category',\n                'axisLabel':{'interval':0},\n                'data':[\n                    '北京','\\n天津','河北','\\n山西','内蒙古','\\n辽宁','吉林','\\n黑龙江',\n                    '上海','\\n江苏','浙江','\\n安徽','福建','\\n江西','山东','\\n河南',\n                    '湖北','\\n湖南','广东','\\n广西','海南','\\n重庆','四川','\\n贵州',\n                    '云南','\\n西藏','陕西','\\n甘肃','青海','\\n宁夏','新疆'\n                ],\n                splitLine: {show: false}\n            }\n        ],\n        yAxis: [\n            {\n                type: 'value',\n                name: 'GDP（亿元）',\n                // max: 53500\n                max: 30000\n            }\n        ],\n        series: [\n            {name: 'GDP', type: 'bar'},\n            {name: '金融', type: 'bar'},\n            {name: '房地产', type: 'bar'},\n            {name: '第一产业', type: 'bar'},\n            {name: '第二产业', type: 'bar'},\n            {name: '第三产业', type: 'bar'},\n            {\n                name: 'GDP占比',\n                type: 'pie',\n                center: ['75%', '35%'],\n                radius: '28%'\n            }\n        ]\n    },\n    options: [\n        {\n            title: {text: '2002全国宏观经济指标'},\n            series: [\n                {data: dataMap.dataGDP['2002']},\n                {data: dataMap.dataFinancial['2002']},\n                {data: dataMap.dataEstate['2002']},\n                {data: dataMap.dataPI['2002']},\n                {data: dataMap.dataSI['2002']},\n                {data: dataMap.dataTI['2002']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2002sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2002sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2002sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2003全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2003']},\n                {data: dataMap.dataFinancial['2003']},\n                {data: dataMap.dataEstate['2003']},\n                {data: dataMap.dataPI['2003']},\n                {data: dataMap.dataSI['2003']},\n                {data: dataMap.dataTI['2003']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2003sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2003sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2003sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2004全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2004']},\n                {data: dataMap.dataFinancial['2004']},\n                {data: dataMap.dataEstate['2004']},\n                {data: dataMap.dataPI['2004']},\n                {data: dataMap.dataSI['2004']},\n                {data: dataMap.dataTI['2004']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2004sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2004sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2004sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2005全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2005']},\n                {data: dataMap.dataFinancial['2005']},\n                {data: dataMap.dataEstate['2005']},\n                {data: dataMap.dataPI['2005']},\n                {data: dataMap.dataSI['2005']},\n                {data: dataMap.dataTI['2005']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2005sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2005sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2005sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2006全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2006']},\n                {data: dataMap.dataFinancial['2006']},\n                {data: dataMap.dataEstate['2006']},\n                {data: dataMap.dataPI['2006']},\n                {data: dataMap.dataSI['2006']},\n                {data: dataMap.dataTI['2006']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2006sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2006sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2006sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2007全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2007']},\n                {data: dataMap.dataFinancial['2007']},\n                {data: dataMap.dataEstate['2007']},\n                {data: dataMap.dataPI['2007']},\n                {data: dataMap.dataSI['2007']},\n                {data: dataMap.dataTI['2007']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2007sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2007sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2007sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2008全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2008']},\n                {data: dataMap.dataFinancial['2008']},\n                {data: dataMap.dataEstate['2008']},\n                {data: dataMap.dataPI['2008']},\n                {data: dataMap.dataSI['2008']},\n                {data: dataMap.dataTI['2008']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2008sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2008sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2008sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2009全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2009']},\n                {data: dataMap.dataFinancial['2009']},\n                {data: dataMap.dataEstate['2009']},\n                {data: dataMap.dataPI['2009']},\n                {data: dataMap.dataSI['2009']},\n                {data: dataMap.dataTI['2009']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2009sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2009sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2009sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2010全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2010']},\n                {data: dataMap.dataFinancial['2010']},\n                {data: dataMap.dataEstate['2010']},\n                {data: dataMap.dataPI['2010']},\n                {data: dataMap.dataSI['2010']},\n                {data: dataMap.dataTI['2010']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2010sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2010sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2010sum']}\n                ]}\n            ]\n        },\n        {\n            title : {text: '2011全国宏观经济指标'},\n            series : [\n                {data: dataMap.dataGDP['2011']},\n                {data: dataMap.dataFinancial['2011']},\n                {data: dataMap.dataEstate['2011']},\n                {data: dataMap.dataPI['2011']},\n                {data: dataMap.dataSI['2011']},\n                {data: dataMap.dataTI['2011']},\n                {data: [\n                    {name: '第一产业', value: dataMap.dataPI['2011sum']},\n                    {name: '第二产业', value: dataMap.dataSI['2011sum']},\n                    {name: '第三产业', value: dataMap.dataTI['2011sum']}\n                ]}\n            ]\n        }\n    ]\n};\n", "name": "timeline", "title": "时间轴", "title-en": "Timeline"}]}, "graphic": {"desc": "<p><code class=\"codespan\">graphic</code> 是原生图形元素组件。可以支持的图形元素包括：</p>\n<p><a href=\"#graphic.elements-image\">image</a>,\n<a href=\"#graphic.elements-text\">text</a>,\n<a href=\"#graphic.elements-circle\">circle</a>,\n<a href=\"#graphic.elements-sector\">sector</a>,\n<a href=\"#graphic.elements-ring\">ring</a>,\n<a href=\"#graphic.elements-polygon\">polygon</a>,\n<a href=\"#graphic.elements-polyline\">polyline</a>,\n<a href=\"#graphic.elements-rect\">rect</a>,\n<a href=\"#graphic.elements-line\">line</a>,\n<a href=\"#graphic.elements-bezierCurve\">bezierCurve</a>,\n<a href=\"#graphic.elements-arc\">arc</a>,\n<a href=\"#graphic.elements-group\">group</a>,</p>\n<p>下面示例中，使用图形元素做了水印，和文本块：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-graphic&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p>下面示例中，使用隐藏的图形元素实现了拖拽：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-draggable&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n\n<p><br></p>\n<hr>\n<p><strong>graphic 设置介绍</strong></p>\n<p>只配一个图形元素时的简写方法：</p>\n<pre><code class=\"lang-javascript\">myChart.setOption({\n    ...,\n    graphic: {\n        type: &#39;image&#39;,\n        ...\n    }\n});\n</code></pre>\n<p>配多个图形元素：</p>\n<pre><code class=\"lang-javascript\">myChart.setOption({\n    ...,\n    graphic: [\n        { // 一个图形元素，类型是 image。\n            type: &#39;image&#39;,\n            ...\n        },\n        { // 一个图形元素，类型是 text，指定了 id。\n            type: &#39;text&#39;,\n            id: &#39;text1&#39;,\n            ...\n        },\n        { // 一个图形元素，类型是 group，可以嵌套子节点。\n            type: &#39;group&#39;,\n            children: [\n                {\n                    type: &#39;rect&#39;,\n                    id: &#39;rect1&#39;,\n                    ...\n                },\n                {\n                    type: &#39;image&#39;,\n                    ...\n                },\n                ...\n            ]\n        }\n        ...\n    ]\n});\n\n</code></pre>\n<p>使用 setOption 来删除或更换（替代）已有的图形元素：</p>\n<pre><code class=\"lang-javascript\">myChart.setOption({\n    ...,\n    graphic: [\n        { // 删除上例中定义的 &#39;text1&#39; 元素。\n            id: &#39;text1&#39;,\n            $action: &#39;remove&#39;,\n            ...\n        },\n        { // 将上例中定义的 &#39;rect1&#39; 元素换成 circle。\n          // 注意尽管 &#39;rect1&#39; 在一个 group 中，但这里并不需要顾忌层级，用id指定就可以了。\n            id: &#39;rect1&#39;,\n            $action: &#39;replace&#39;,\n            type: &#39;circle&#39;,\n            ...\n        }\n    ]\n});\n</code></pre>\n<p>注意，如果没有指定 id，第二次 setOption 时会按照元素在 option 中出现的顺序和已有的图形元素进行匹配。这有时会产生不易理解的效果。\n所以，一般来说，更新 elements 时推荐使用 id 进行准确的指定，而非省略 id。</p>\n<p><br></p>\n<hr>\n<p><strong>图形元素设置介绍</strong></p>\n<p>介绍每个图形元素的配置。不同类型的图形元素的设置有这些共性：</p>\n<pre><code class=\"lang-javascript\">{\n    // id 用于在更新图形元素时指定更新哪个图形元素，如果不需要用可以忽略。\n    id: &#39;xxx&#39;,\n\n    // 这个字段在第一次设置时不能忽略，取值见上方『支持的图形元素』。\n    type: &#39;image&#39;,\n\n    // 下面的各个属性如果不需要设置都可以忽略，忽略则取默认值。\n\n    // 指定本次 setOption 对此图形元素进行的操作。默认是 &#39;merge&#39;，还可以 &#39;replace&#39; 或 &#39;remove&#39;。\n    $action: &#39;replace&#39;,\n\n    // 这是四个相对于父元素的定位属性，每个属性可取『像素值』或者『百分比』或者 &#39;center&#39;/&#39;middle&#39;。\n    left: 10,\n    // right: 10,\n    top: &#39;center&#39;,\n    // bottom: &#39;10%&#39;,\n\n    shape: {\n        // 定位、形状相关的设置，如 x, y, cx, cy, width, height, r, points 等。\n        // 注意，如果设置了 left/right/top/bottom，这里的定位用的 x/y/cx/cy 会失效。\n    },\n\n    style: {\n        // 样式相关的设置，如 fill, stroke, lineWidth, shadowBlur 等。\n    },\n\n    // 表示 z 高度，从而指定了图形元素的覆盖关系。\n    z: 10,\n    // 表示不响应事件。\n    silent: true,\n    // 表示节点不显示\n    invisible: false,\n    // 设置是否整体限制在父节点范围内。可选值：&#39;raw&#39;, &#39;all&#39;。\n    bouding: &#39;raw&#39;,\n    // 是否可以被拖拽。\n    draggable: false,\n    // 事件的监听器，还可以是 onmousemove, ondrag 等。支持的事件参见下。\n    onclick: function () {...}\n}\n</code></pre>\n<p><br></p>\n<hr>\n<p><strong>图形元素的事件</strong></p>\n<p>支持这些事件配置：\n<code class=\"codespan\">onclick</code>, <code class=\"codespan\">onmouseover</code>, <code class=\"codespan\">onmouseout</code>, <code class=\"codespan\">onmousemove</code>, <code class=\"codespan\">onmousewheel</code>, <code class=\"codespan\">onmousedown</code>, <code class=\"codespan\">onmouseup</code>, <code class=\"codespan\">ondrag</code>, <code class=\"codespan\">ondragstart</code>, <code class=\"codespan\">ondragend</code>, <code class=\"codespan\">ondragenter</code>, <code class=\"codespan\">ondragleave</code>, <code class=\"codespan\">ondragover</code>, <code class=\"codespan\">ondrop</code>。</p>\n<p><br></p>\n<hr>\n<p><strong>图形元素的层级关系</strong></p>\n<p>只有 <code class=\"codespan\">group</code> 元素可以有子节点，从而以该 <code class=\"codespan\">group</code> 元素为根的元素树可以共同定位（共同移动）。</p>\n<p><br></p>\n<hr>\n<p><strong>图形元素的基本形状设置</strong></p>\n<p>每个图形元素本身有自己的图形基本的位置和尺寸设置，例如：</p>\n<pre><code class=\"lang-javascript\">{\n    type: &#39;rect&#39;,\n    shape: {\n        x: 10,\n        y: 10,\n        width: 100,\n        height: 200\n    }\n},\n{\n    type: &#39;circle&#39;,\n    shape: {\n        cx: 20,\n        cy: 30,\n        r: 100\n    }\n},\n{\n    type: &#39;image&#39;,\n    style: {\n        image: &#39;http://xxx.xxx.xxx/a.png&#39;,\n        x: 100,\n        y: 200,\n        width: 230,\n        height: 400\n    }\n},\n{\n    type: &#39;text&#39;,\n    style: {\n        text: &#39;This text&#39;,\n        x: 100,\n        y: 200\n    }\n\n}\n</code></pre>\n<p><br></p>\n<hr>\n<p><strong>图形元素的定位和 transfrom</strong></p>\n<p>除此以外，可以以 transform 的方式对图形进行平移、旋转、缩放，\n参见：<a href=\"#graphic.elements.position\">position</a>、<a href=\"#graphic.elements.rotation\">rotation</a>、<a href=\"#graphic.elements.scale\">scale</a>、<a href=\"#graphic.elements.origin\">origin</a>。</p>\n<pre><code class=\"lang-javascript\">{\n    type: &#39;rect&#39;,\n    position: [100, 200], // 平移，默认值为 [0, 0]。\n    scale: [2, 4], // 缩放，默认值为 [1, 1]。表示缩放的倍数。\n    rotation: Math.PI / 4, // 旋转，默认值为 0。表示旋转的弧度值。正值表示逆时针旋转。\n    origin: [10, 20], // 旋转和缩放的中心点，默认值为 [0, 0]。\n    shape: {\n        // ...\n    }\n}\n</code></pre>\n<ul>\n<li>每个图形元素在父节点的坐标系中进行 transform，也就是说父子节点的 transform 能『叠加』。</li>\n<li>每个图形元素进行 transform 顺序是：<ol>\n<li>平移 [-el.origin[0], -el.origin[1]]。</li>\n<li>根据 el.scale 缩放。</li>\n<li>根据 el.rotation 旋转。</li>\n<li>根据 el.origin 平移。</li>\n<li>根据 el.position 平移。</li>\n</ol>\n</li>\n<li>也就是说先缩放旋转后平移，这样平移不会影响缩放旋转的 origin。</li>\n</ul>\n<p><br></p>\n<hr>\n<p><strong>图形元素相对定位</strong></p>\n<p>以上两者是基本的绝对定位，除此之外，在实际应用中，容器尺寸常常是不确定甚至动态变化的，所以需要提供相对定位的机制。graphic 组件使用 <a href=\"#graphic.elements.left\">left</a> / <a href=\"#graphic.elements.right\">right</a> / <a href=\"#graphic.elements.top\">top</a> / <a href=\"#graphic.elements.bottom\">bottom</a> / <a href=\"#graphic.elements.width\">width</a> / <a href=\"#graphic.elements.height\">height</a> 提供了相对定位的机制。</p>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">{ // 将图片定位到最下方的中间：\n    type: &#39;image&#39;,\n    left: &#39;center&#39;, // 水平定位到中间\n    bottom: &#39;10%&#39;,  // 定位到距离下边界 10% 处\n    style: {\n        image: &#39;http://xxx.xxx.xxx/a.png&#39;,\n        width: 45,\n        height: 45\n    }\n},\n{ // 将旋转过的 group 整体定位右下角：\n    type: &#39;group&#39;,\n    right: 0,  // 定位到右下角\n    bottom: 0, // 定位到右下角\n    rotation: Math.PI / 4,\n    children: [\n        {\n            type: &#39;rect&#39;,\n            left: &#39;center&#39;, // 相对父元素居中\n            top: &#39;middle&#39;,  // 相对父元素居中\n            shape: {\n                width: 190,\n                height: 90\n            },\n            style: {\n                fill: &#39;#fff&#39;,\n                stroke: &#39;#999&#39;,\n                lineWidth: 2,\n                shadowBlur: 8,\n                shadowOffsetX: 3,\n                shadowOffsetY: 3,\n                shadowColor: &#39;rgba(0,0,0,0.3)&#39;\n            }\n        },\n        {\n            type: &#39;text&#39;,\n            left: &#39;center&#39;, // 相对父元素居中\n            top: &#39;middle&#39;,  // 相对父元素居中\n            style: {\n                fill: &#39;#777&#39;,\n                text: [\n                    &#39;This is text&#39;,\n                    &#39;这是一段文字&#39;,\n                    &#39;Print some text&#39;\n                ].join(&#39;\\n&#39;),\n                font: &#39;14px Microsoft YaHei&#39;\n            }\n        }\n    ]\n}\n</code></pre>\n<p>注意，可以用 <a href=\"graphic.elements.bounding\" target=\"_blank\">bounding</a> 来设置是否整体限制在父节点范围内。</p>\n"}, "calendar": {"desc": "<p>日历坐标系组件。</p>\n<p>在ECharts中，我们非常有创意地实现了日历图，是通过使用日历坐标系组件来达到日历图效果的，如下方的几个示例图所示，我们可以在热力图、散点图、关系图中使用日历坐标系。</p>\n<p>在日历坐标系中使用热力图的示例:</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=calendar-heatmap&edit=1&reset=1\" width=\"800\" height=\"400\"><iframe />\n\n\n<p>在日历坐标系中使用散点图的示例:</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=calendar-effectscatter&edit=1&reset=1\" width=\"800\" height=\"600\"><iframe />\n\n\n<p>在日历坐标系中使用关系图（以及混合图表）的示例:</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=calendar-graph&edit=1&reset=1\" width=\"600\" height=\"600\"><iframe />\n\n\n<p>灵活利用 echarts 图表和坐标系的组合，以及 API，可以实现更丰富的效果。\n<a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=calendar-lunar&amp;edit=1&amp;reset=1\" target=\"_blank\">在日历中使用文字</a>、\n<a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=calendar-pie&amp;edit=1&amp;reset=1\" target=\"_blank\">在日历中放置饼图</a></p>\n<hr>\n<p><strong>水平和垂直放置日历</strong></p>\n<p>在日历坐标系可以水平放置，也可以垂直放置。如上面的例子，使用热力图时，经常是水平放置的。但是如果需要格子的尺寸大些，水平放置就过于宽了，于是也可以选择垂直放置。参见 <a href=\"#calendar.orient\">calendar.orient</a>。</p>\n<hr>\n<p><strong>尺寸的自适应</strong></p>\n<p>可以设置日历坐标系使他支持不同尺寸的容器（页面）大小变化的自适应。首先，和 echarts 其他组件一样，日历坐标系可以选择使用 <a href=\"#calendar.left\">left</a> <a href=\"#calendar.right\">right</a> <a href=\"#calendar.top\">top</a> <a href=\"bottom\" target=\"_blank\">bottom</a> <a href=\"#calendar.width\">width</a> <a href=\"#calendar.height\">height</a> 来描述尺寸和位置，从而将日历摆放在上下左右各种位置，并随着页面尺寸变动而改变自身尺寸。另外，也可以使用 <a href=\"#calendar.cellSize\">cellSize</a> 来固定日历格子的长宽。</p>\n<hr>\n<p><strong>中西方日历习惯的支持</strong></p>\n<p>中西方日历有所差别，西方常使用星期日作为一周的第一天，中国使用星期一为一周的第一天。日历坐标系做了这种切换的支持。参见 <a href=\"#calendar.dayLabel.firstDay\">calendar.dayLabel.firstDay</a>。</p>\n<p>另外，日历上的『月份』和『星期几』的文字，也可以较方便的切换中英文，甚至自定义。参见 <a href=\"#calendar.dayLabel.nameMap\">calendar.dayLabel.nameMap</a> <a href=\"#calendar.monthLabel.nameMap\">calendar.monthLabel.nameMap</a>。</p>\n<hr>\n\n\n\n", "exampleBaseOptions": [{"code": "\nfunction getVirtulData(year) {\n    year = year || '2017';\n    var date = +new Date(year + '-01-01');\n    var end = +new Date((+year + 1) + '-01-01');\n    var dayTime = 3600 * 24 * 1000;\n    var data = [];\n    for (var time = date; time < end; time += dayTime) {\n        data.push([\n            time,\n            Math.floor(Math.random() * 10000)\n        ]);\n    }\n    return data;\n}\n\nconst option = {\n    tooltip: {},\n    visualMap: {\n        min: 0,\n        max: 10000,\n        type: 'piecewise',\n        orient: 'horizontal',\n        left: 'center',\n        top: 65,\n        textStyle: {\n            color: '#000'\n        }\n    },\n    calendar: {\n        top: 120,\n        left: 30,\n        right: 30,\n        cellSize: ['auto', 13],\n        range: '2016',\n        itemStyle: {\n            borderWidth: 0.5\n        },\n        yearLabel: {show: false}\n    },\n    series: {\n        type: 'heatmap',\n        coordinateSystem: 'calendar',\n        data: getVirtulData(2016)\n    }\n};\n", "name": "calendar", "title": "日历图", "title-en": "Calendar"}]}, "dataset": {"desc": "<p>ECharts 4 开始支持了 <code class=\"codespan\">数据集</code>（<code class=\"codespan\">dataset</code>）组件用于单独的数据集声明，从而数据可以单独管理，被多个组件复用，并且可以自由指定数据到视觉的映射。这在不少场景下能带来使用上的方便。</p>\n<p>关于 <code class=\"codespan\">dataset</code> 的详情，请参见<a href=\"tutorial.html#%E4%BD%BF%E7%94%A8%20dataset%20%E7%AE%A1%E7%90%86%E6%95%B0%E6%8D%AE\" target=\"_blank\">教程</a>。</p>\n"}, "aria": {"desc": "<p>W3C 制定了无障碍富互联网应用规范集（<a href=\"https://www.w3.org/WAI/intro/aria\" target=\"_blank\">WAI-ARIA</a>，the Accessible Rich Internet Applications Suite），致力于使得网页内容和网页应用能够被更多残障人士访问。Apache ECharts 4 遵从这一规范，支持自动根据图表配置项智能生成描述，使得盲人可以在朗读设备的帮助下了解图表内容，让图表可以被更多人群访问。除此之外，Apache ECharts 5 新增支持贴花纹理，作为颜色的辅助表达，进一步用以区分数据。</p>\n<p>默认关闭，需要通过将 <a href=\"#aria.enabled\">aria.enabled</a> 设置为 <code class=\"codespan\">true</code> 开启。</p>\n"}, "series-line": {"desc": "<p><strong>折线/面积图</strong></p>\n<p>折线图是用折线将各个数据点<a href=\"#series-line.symbol\">标志</a>连接起来的图表，用于展现数据的变化趋势。可用于<a href=\"#grid\">直角坐标系</a>和<a href=\"#polar\">极坐标系</a>上。</p>\n<p><strong>Tip:</strong> 设置 <a href=\"#series-line.areaStyle\">areaStyle</a> 后可以绘制面积图。</p>\n<p><strong>Tip:</strong> 配合分段型 <a href=\"#visualMap-piecewise\">visualMap</a> 组件可以将折线/面积图通过不同颜色分区间。如下示例</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-aqi&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    xAxis: {\n        type: 'category',\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n    },\n    yAxis: {},\n    series: [{\n        data: [820, 932, 901, 934, 1290, 1330, 1320],\n        type: 'line'\n    }]\n};\n", "name": "cartesian-line", "title": "基础折线图", "title-en": "Basic Line Chart"}, {"code": "\nconst option = {\n    xAxis: {\n        type: 'category',\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n    },\n    yAxis: {},\n    series: [{\n        data: [820, 932, 901, '-', 1290, 1330, 1320],\n        type: 'line'\n    }]\n};\n", "name": "cartesian-line-empty-data", "title": "有空数据的折线图", "title-en": "Line with Empty Data"}]}, "series-bar": {"desc": "<p><strong>柱状图</strong></p>\n<p>柱状图（或称条形图）是一种通过柱形的高度（横向的情况下则是宽度）来表现数据大小的一种常用图表类型。</p>\n\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    tooltip: {},\n    legend: {},\n    xAxis: {\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n    },\n    yAxis: {},\n    series: [{\n        name: '<PERSON>',\n        type: 'bar',\n        data: [5, 20, 36, 10, 10, 20, 4]\n    }]\n};\n", "name": "cartesian-bar", "title": "直角坐标系上的柱状图", "title-en": "Bar on Cartesian"}, {"code": "\nconst option = {\n    angleAxis: {\n        max: 30\n    },\n    radiusAxis: {\n        type: 'category',\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n        z: 10\n    },\n    polar: {},\n    series: [{\n        name: '<PERSON>',\n        type: 'bar',\n        data: [5, 20, 36, 10, 10, 20, 4],\n        coordinateSystem: 'polar'\n    }],\n    legend: {},\n};\n", "name": "polar-bar", "title": "极坐标系上的柱状图", "title-en": "Bar on Polar"}, {"code": "\noption = {\n    legend: {\n        data: ['Food', 'Cloth', 'Book']\n    },\n    grid: {\n        left: '3%',\n        right: '4%',\n        bottom: '3%',\n        containLabel: true\n    },\n    xAxis: {\n        type: 'category',\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n    },\n    yAxis: {\n        type: 'value'\n    },\n    series: [\n        {\n            name: 'Food',\n            type: 'bar',\n            data: [320, 302, 301, 334, 390, 330, 320]\n        },\n        {\n            name: 'Cloth',\n            type: 'bar',\n            data: [150, 212, 201, 154, 190, 330, 410]\n        },\n        {\n            name: 'Book',\n            type: 'bar',\n            data: [820, 832, 901, 934, 1290, 1330, 1320]\n        }\n    ]\n};\n", "name": "cartesian-bar-multiple-series", "title": "多系列柱状图", "title-en": "Multiple Series"}]}, "series-pie": {"desc": "<p><strong>饼图</strong></p>\n<p>饼图主要用于表现不同类目的数据在总和中的占比。每个的弧度表示数据数量的比例。</p>\n<p>从 ECharts v4.6.0 版本起，我们提供了 <code class=\"codespan\">&#39;labelLine&#39;</code> 与 <code class=\"codespan\">&#39;edge&#39;</code> 两种新的布局方式。详情参见 <a href=\"#series-pie.label.alignTo\">label.alignTo</a>。</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=pie-alignTo&reset=1&edit=1\" width=\"900\" height=\"250\"><iframe />\n\n\n<p>对于一个图表中有多个饼图的场景，可以使用 <a href=\"#series-pie.left\">left</a>、<a href=\"#series-pie.right\">right</a>、<a href=\"#series-pie.top\">top</a>、<a href=\"#series-pie.bottom\">bottom</a>、<a href=\"#series-pie.width\">width</a>、<a href=\"#series-pie.height\">height</a> 配置每个饼图系列的位置和视口大小。<a href=\"#series-pie.radius\">radius</a>、<a href=\"#series-pie.label.margin\">label.margin</a> 等支持百分比的配置项，是相对于该配置项决定的矩形的大小而言的。</p>\n<p><strong>Tip:</strong> 饼图更适合表现数据相对于总数的百分比等关系。如果只是表示不同类目数据间的大小，建议使用 <a href=\"bar\" target=\"_blank\">柱状图</a>，人们对于微小的弧度差别相比于微小的长度差别更不敏感，或者也可以通过配置 <a href=\"#series-pie.roseType\">roseType</a> 显示成南丁格尔图，通过半径大小区分数据的大小。</p>\n<p><strong>下面是自定义南丁格尔图的示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=pie-custom&edit=1&reset=1\" width=\"500\" height=\"400\"><iframe />\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    legend: {\n        orient: 'vertical',\n        left: 'left',\n        data: ['Apple', 'Grapes', 'Pineapples', 'Oranges', 'Bananas']\n    },\n    series: [\n        {\n            type: 'pie',\n            data: [\n                {value: 335, name: '<PERSON>'},\n                {value: 310, name: '<PERSON>rap<PERSON>'},\n                {value: 234, name: 'Pineap<PERSON>'},\n                {value: 135, name: '<PERSON>s'},\n                {value: 1548, name: '<PERSON><PERSON><PERSON>'}\n            ]\n        }\n    ]\n};\n\n", "name": "pie", "title": "基础饼图", "title-en": "Basic Pie"}]}, "series-scatter": {"desc": "<p>散点（气泡）图。<a href=\"#grid\">直角坐标系</a>上的散点图可以用来展现数据的 <code class=\"codespan\">x</code>，<code class=\"codespan\">y</code> 之间的关系，如果数据项有多个维度，其它维度的值可以通过不同大小的 <a href=\"#series-scatter.symbol\">symbol</a> 展现成气泡图，也可以用颜色来表现。这些可以配合 <a href=\"#visualMap\">visualMap</a> 组件完成。</p>\n<p>可以应用在<a href=\"#grid\">直角坐标系</a>，<a href=\"#polar\">极坐标系</a>，<a href=\"#geo\">地理坐标系</a>上。</p>\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    legend: {},\n    grid: {\n        left: '3%',\n        right: '7%',\n        bottom: '3%',\n        containLabel: true\n    },\n    xAxis: {\n        type: 'value',\n        scale: true,\n        axisLabel: {\n            formatter: '{value} cm'\n        },\n        splitLine: {\n            show: false\n        }\n    },\n    yAxis: {\n        type: 'value',\n        scale: true,\n        axisLabel: {\n            formatter: '{value} kg'\n        },\n        splitLine: {\n            show: false\n        }\n    },\n    series: [\n        {\n            name: 'Male',\n            type: 'scatter',\n            data: [[174.0, 65.6], [175.3, 71.8], [193.5, 80.7], [186.5, 72.6], [187.2, 78.8],[181.5, 74.8], [184.0, 86.4], [184.5, 78.4], [175.0, 62.0], [184.0, 81.6],[180.0, 76.6], [177.8, 83.6], [192.0, 90.0], [176.0, 74.6], [174.0, 71.0],[184.0, 79.6], [192.7, 93.8], [171.5, 70.0], [173.0, 72.4], [176.0, 85.9],[176.0, 78.8], [180.5, 77.8], [172.7, 66.2], [176.0, 86.4], [173.5, 81.8],[178.0, 89.6], [180.3, 82.8], [180.3, 76.4], [164.5, 63.2], [173.0, 60.9],[183.5, 74.8], [175.5, 70.0], [188.0, 72.4], [189.2, 84.1], [172.8, 69.1],[170.0, 59.5], [182.0, 67.2], [170.0, 61.3], [177.8, 68.6], [184.2, 80.1],[186.7, 87.8], [171.4, 84.7], [172.7, 73.4], [175.3, 72.1], [180.3, 82.6],[182.9, 88.7], [188.0, 84.1], [177.2, 94.1], [172.1, 74.9], [167.0, 59.1],[169.5, 75.6], [174.0, 86.2], [172.7, 75.3], [182.2, 87.1], [164.1, 55.2],[163.0, 57.0], [171.5, 61.4], [184.2, 76.8], [174.0, 86.8], [174.0, 72.2],[177.0, 71.6], [186.0, 84.8], [167.0, 68.2], [171.8, 66.1], [182.0, 72.0],[167.0, 64.6], [177.8, 74.8], [164.5, 70.0], [192.0, 101.6], [175.5, 63.2],[171.2, 79.1], [181.6, 78.9], [167.4, 67.7], [181.1, 66.0], [177.0, 68.2],[174.5, 63.9], [177.5, 72.0], [170.5, 56.8], [182.4, 74.5], [197.1, 90.9],[180.1, 93.0], [175.5, 80.9], [180.6, 72.7], [184.4, 68.0], [175.5, 70.9],[180.6, 72.5], [177.0, 72.5], [177.1, 83.4], [181.6, 75.5], [176.5, 73.0],[175.0, 70.2], [174.0, 73.4], [165.1, 70.5], [177.0, 68.9], [192.0, 102.3],[176.5, 68.4], [169.4, 65.9], [182.1, 75.7], [179.8, 84.5], [175.3, 87.7],[184.9, 86.4], [177.3, 73.2], [167.4, 53.9], [178.1, 72.0], [168.9, 55.5],[157.2, 58.4], [180.3, 83.2], [170.2, 72.7], [177.8, 64.1], [172.7, 72.3],[165.1, 65.0], [186.7, 86.4], [165.1, 65.0], [174.0, 88.6], [175.3, 84.1],[185.4, 66.8], [177.8, 75.5], [180.3, 93.2], [180.3, 82.7], [177.8, 58.0],[177.8, 79.5], [177.8, 78.6], [177.8, 71.8], [177.8, 116.4], [163.8, 72.2],[188.0, 83.6], [198.1, 85.5], [175.3, 90.9], [166.4, 85.9], [190.5, 89.1],[166.4, 75.0], [177.8, 77.7], [179.7, 86.4], [172.7, 90.9], [190.5, 73.6],[185.4, 76.4], [168.9, 69.1], [167.6, 84.5], [175.3, 64.5], [170.2, 69.1],[190.5, 108.6], [177.8, 86.4], [190.5, 80.9], [177.8, 87.7], [184.2, 94.5],[176.5, 80.2], [177.8, 72.0], [180.3, 71.4], [171.4, 72.7], [172.7, 84.1],[172.7, 76.8], [177.8, 63.6], [177.8, 80.9], [182.9, 80.9], [170.2, 85.5],[167.6, 68.6], [175.3, 67.7], [165.1, 66.4], [185.4, 102.3], [181.6, 70.5],[172.7, 95.9], [190.5, 84.1], [179.1, 87.3], [175.3, 71.8], [170.2, 65.9],[193.0, 95.9], [171.4, 91.4], [177.8, 81.8], [177.8, 96.8], [167.6, 69.1],[167.6, 82.7], [180.3, 75.5], [182.9, 79.5], [176.5, 73.6], [186.7, 91.8],[188.0, 84.1], [188.0, 85.9], [177.8, 81.8], [174.0, 82.5], [177.8, 80.5],[171.4, 70.0], [185.4, 81.8], [185.4, 84.1], [188.0, 90.5], [188.0, 91.4],[182.9, 89.1], [176.5, 85.0], [175.3, 69.1], [175.3, 73.6], [188.0, 80.5],[188.0, 82.7], [175.3, 86.4], [170.5, 67.7], [179.1, 92.7], [177.8, 93.6],[175.3, 70.9], [182.9, 75.0], [170.8, 93.2], [188.0, 93.2], [180.3, 77.7],[177.8, 61.4], [185.4, 94.1], [168.9, 75.0], [185.4, 83.6], [180.3, 85.5],[174.0, 73.9], [167.6, 66.8], [182.9, 87.3], [160.0, 72.3], [180.3, 88.6],[167.6, 75.5], [186.7, 101.4], [175.3, 91.1], [175.3, 67.3], [175.9, 77.7],[175.3, 81.8], [179.1, 75.5], [181.6, 84.5], [177.8, 76.6], [182.9, 85.0],[177.8, 102.5], [184.2, 77.3], [179.1, 71.8], [176.5, 87.9], [188.0, 94.3],[174.0, 70.9], [167.6, 64.5], [170.2, 77.3], [167.6, 72.3], [188.0, 87.3],[174.0, 80.0], [176.5, 82.3], [180.3, 73.6], [167.6, 74.1], [188.0, 85.9],[180.3, 73.2], [167.6, 76.3], [183.0, 65.9], [183.0, 90.9], [179.1, 89.1],[170.2, 62.3], [177.8, 82.7], [179.1, 79.1], [190.5, 98.2], [177.8, 84.1],[180.3, 83.2], [180.3, 83.2]\n            ]\n        }\n    ]\n};\n", "name": "scatter", "title": "基础散点图", "title-en": "Basic Scatter"}, {"code": "\nconst option = {\n    title: {\n        text: '1990 与 2015 年各国家人均寿命与 GDP'\n    },\n    legend: {\n        right: 10,\n        data: ['1990', '2015']\n    },\n    xAxis: {},\n    yAxis: {},\n    series: [{\n        name: '1990',\n        type: 'scatter',\n        data: [[28604,77,17096869,'Australia',1990],[31163,77.4,27662440,'Canada',1990],[1516,68,1154605773,'China',1990],[13670,74.7,10582082,'Cuba',1990],[28599,75,4986705,'Finland',1990],[29476,77.1,56943299,'France',1990],[31476,75.4,78958237,'Germany',1990],[28666,78.1,254830,'Iceland',1990],[1777,57.7,*********,'India',1990],[29550,79.1,*********,'Japan',1990],[2076,67.9,20194354,'North Korea',1990],[12087,72,42972254,'South Korea',1990],[24021,75.4,3397534,'New Zealand',1990],[43296,76.8,4240375,'Norway',1990],[10088,70.8,38195258,'Poland',1990],[19349,69.6,*********,'Russia',1990],[10670,67.3,53994605,'Turkey',1990],[26424,75.7,57110117,'United Kingdom',1990],[37062,75.4,*********,'United States',1990]],\n        symbolSize: function (data) {\n            return Math.sqrt(data[2]) / 5e2;\n        }\n    }, {\n        name: '2015',\n        data: [[44056,81.8,23968973,'Australia',2015],[43294,81.7,35939927,'Canada',2015],[13334,76.9,1376048943,'China',2015],[21291,78.5,11389562,'Cuba',2015],[38923,80.8,5503457,'Finland',2015],[37599,81.9,64395345,'France',2015],[44053,81.1,80688545,'Germany',2015],[42182,82.8,329425,'Iceland',2015],[5903,66.8,1311050527,'India',2015],[36162,83.5,*********,'Japan',2015],[1390,71.4,25155317,'North Korea',2015],[34644,80.7,50293439,'South Korea',2015],[34186,80.6,4528526,'New Zealand',2015],[64304,81.6,5210967,'Norway',2015],[24787,77.3,38611794,'Poland',2015],[23038,73.13,*********,'Russia',2015],[19360,76.5,78665830,'Turkey',2015],[38225,81.4,64715810,'United Kingdom',2015],[53354,79.1,*********,'United States',2015]],\n        type: 'scatter',\n        symbolSize: function (data) {\n            return Math.sqrt(data[2]) / 5e2;\n        }\n    }]\n};\n", "name": "scatter-bubble", "title": "气泡图"}]}, "series-effectScatter": {"desc": "<p>带有涟漪特效动画的散点（气泡）图。利用动画特效可以将某些想要突出的数据进行视觉突出。</p>\n<p><strong>Tip:</strong> ECharts 2.x 中在地图上通过 markPoint 实现地图特效在 ECharts 3 中建议通过地理坐标系上的 effectScatter 实现。</p>\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    xAxis: {},\n    yAxis: {\n        scale: true\n    },\n    series: [{\n        name: '1990',\n        type: 'effectScatter',\n        data: [[28604,77,17096869,'Australia',1990],[31163,77.4,27662440,'Canada',1990],[1516,68,1154605773,'China',1990],[13670,74.7,10582082,'Cuba',1990],[28599,75,4986705,'Finland',1990],[29476,77.1,56943299,'France',1990],[31476,75.4,78958237,'Germany',1990],[28666,78.1,254830,'Iceland',1990],[1777,57.7,*********,'India',1990],[29550,79.1,*********,'Japan',1990],[2076,67.9,20194354,'North Korea',1990],[12087,72,42972254,'South Korea',1990],[24021,75.4,3397534,'New Zealand',1990],[43296,76.8,4240375,'Norway',1990],[10088,70.8,38195258,'Poland',1990],[19349,69.6,*********,'Russia',1990],[10670,67.3,53994605,'Turkey',1990],[26424,75.7,57110117,'United Kingdom',1990],[37062,75.4,*********,'United States',1990]],\n        symbolSize: function (data) {\n            return Math.sqrt(data[2]) / 5e2;\n        }\n    }]\n};\n", "name": "effectScatter-bubble", "title": "特效气泡图", "title-en": "Bubble Chart with Effect"}]}, "series-radar": {"desc": "<p><strong>雷达图</strong></p>\n<p>雷达图主要用于表现多变量的数据，例如球员的各个属性分析。依赖 <a href=\"#radar\">radar</a> 组件。</p>\n<p>下面是 AQI 数据用雷达图表现的示例。</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=radar-aqi&edit=1&reset=1\" width=\"600\" height=\"500\"><iframe />\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    title: {\n        text: '基础雷达图'\n    },\n    tooltip: {},\n    legend: {\n        data: ['Allocated Budget', 'Actual Spending']\n    },\n    radar: {\n        indicator: [\n            { name: 'sales', max: 6500},\n            { name: 'Administration', max: 16000},\n            { name: 'Information Techology', max: 30000},\n            { name: 'Customer Support', max: 38000},\n            { name: 'Development', max: 52000},\n            { name: 'Marketing', max: 25000}\n        ]\n    },\n    series: [{\n        name: '预算 vs 开销（Budget vs spending）',\n        type: 'radar',\n        data: [\n            {\n                value: [4300, 10000, 28000, 35000, 50000, 19000],\n                name: 'Allocated Budget'\n            },\n            {\n                value: [5000, 14000, 28000, 31000, 42000, 21000],\n                name: 'Actual Spending'\n            }\n        ]\n    }]\n};\n", "name": "radar", "title": "基础雷达图", "title-en": "Basic Radar"}]}, "series-tree": {"desc": "<p><strong>树图</strong></p>\n<p>树图主要用来可视化树形数据结构，是一种特殊的层次类型，具有唯一的根节点，左子树，和右子树。</p>\n<p><strong>注意：目前不支持在单个 series 中直接绘制森林，可以通过在一个 option 中配置多个 series 实现森林</strong></p>\n<p><strong>树图示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=tree-vertical&edit=1&reset=1\" width=\"900\" height=\"780\"><iframe />\n\n\n<p><strong>多个 series 组合成森林示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=tree-legend&edit=1&reset=1\" width=\"800\" height=\"680\"><iframe />\n\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    series: [{\n        type: 'tree',\n        data: [{\n            name: 'root',\n            children: [{\n                name: 'Child A',\n                children: [{\n                    name: 'Leaf C'\n                }, {\n                    name: 'Leaf D'\n                }, {\n                    name: 'Leaf E'\n                }, {\n                    name: 'Leaf F'\n                }]\n            }, {\n                name: 'Child <PERSON>',\n                children: [{\n                    name: 'Leaf G'\n                }, {\n                    name: 'Leaf H'\n                }]\n            }, {\n                name: 'Child D'\n            }, {\n                name: 'Child F',\n                children: [{\n                    name: 'Leaf J'\n                }, {\n                    name: 'Leaf K'\n                }]\n            }]\n        }],\n        label: {\n            position: 'right'\n        }\n    }]\n}\n", "name": "tree", "title": "基础树图", "title-en": "Basic Tree"}]}, "series-treemap": {"desc": "<p><a href=\"https://en.wikipedia.org/wiki/Treemapping\" target=\"_blank\">Treemap</a> 是一种常见的表达『层级数据』『树状数据』的可视化形式。它主要用面积的方式，便于突出展现出『树』的各层级中重要的节点。</p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=treemap-obama&edit=1&reset=1\" width=\"700\" height=\"580\"><iframe />\n\n\n\n<p><strong>视觉映射：</strong></p>\n<p>treemap 首先是把数值映射到『面积』这种视觉元素上。</p>\n<p>此外，也支持对数据的其他维度进行视觉映射，例如映射到颜色、颜色明暗度上。</p>\n<p>关于视觉设置，详见 <a href=\"#series-treemap.levels\">series-treemap.levels</a>。</p>\n<p><strong>下钻（drill down）：</strong></p>\n<p><code class=\"codespan\">drill down</code> 功能即点击后才展示子层级。\n设置了 <a href=\"#series-treemap.leafDepth\">leafDepth</a> 后，下钻（<code class=\"codespan\">drill down</code>）功能开启。</p>\n<p><strong>如下是 drill down 的例子：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=treemap-drill-down&edit=1&reset=1\" width=\"800\" height=\"500\"><iframe />\n\n\n\n<p>注：treemap 的配置项 和 ECharts2 相比有一些变化，一些不太成熟的配置方式不再支持或不再兼容：</p>\n<ul>\n<li><p><code class=\"codespan\">center/size</code> 方式的定位不再支持，而是统一使用 <code class=\"codespan\">left/top/bottom/right/width/height</code> 方式定位。</p>\n</li>\n<li><p><code class=\"codespan\">breadcrumb</code> 的配置被移动到了 <code class=\"codespan\">itemStyle/itemStyle.emphasis</code> 外部，和 <code class=\"codespan\">itemStyle</code> 平级。</p>\n</li>\n<li><p><code class=\"codespan\">root</code> 的设置暂时不支持。目前可以使用 <code class=\"codespan\">zoom</code> 的方式来查看树更下层次的细节，或者使用 <a href=\"#series-treemap.leafDepth\">leafDepth</a> 开启 &quot;drill down&quot; 功能。</p>\n</li>\n<li><p><code class=\"codespan\">label</code> 的配置被移动到了 <code class=\"codespan\">itemStyle/itemStyle.emphasis</code> 外部，和 <code class=\"codespan\">itemStyle</code> 平级。</p>\n</li>\n<li><p><code class=\"codespan\">itemStyle.childBorderWidth</code>、<code class=\"codespan\">itemStyle.childBorderColor</code>不再支持（因为这个配置方式只能定义两层的treemap）。统一使用 <a href=\"#series-treemap.levels\">series-treemap.levels</a> 来进行各层级的定义。</p>\n</li>\n</ul>\n\n", "exampleBaseOptions": [{"code": "\nconst data = [{\n    name: 'Food',\n    children: [{\n        value: 3,\n        name: 'Fruit',\n        children: [{\n            value: 1,\n            name: '<PERSON>'\n        }, {\n            value: 2,\n            name: 'Orange',\n            children: [{\n                name: 'Seville Orange',\n                value: 1\n            }, {\n                name: '<PERSON> Orange',\n                value: 1\n            }]\n        }]\n    }, {\n        value: 9,\n        name: '<PERSON>',\n        children: [{\n            value: 6,\n            name: '<PERSON><PERSON>',\n            children: [{\n                name: '<PERSON><PERSON><PERSON>',\n                value: 1\n            }, {\n                name: '<PERSON><PERSON>',\n                value: 1\n            }, {\n                name: '<PERSON>',\n                value: 1\n            }, {\n                name: '<PERSON><PERSON>',\n                value: 1\n            }]\n        }, {\n            value: 2,\n            name: '<PERSON>',\n            children: [{\n                name: '<PERSON>',\n                value: 1\n            }]\n        }, {\n            name: '<PERSON><PERSON><PERSON>',\n            value: 1\n        }]\n    }]\n}, {\n    value: 6,\n    name: 'Drinks',\n    children: [{\n        value: 3,\n        name: '<PERSON>',\n        children: [{\n            name: 'USA',\n            value: 2\n        }, {\n            name: 'Europe',\n            children: [{\n                name: 'Germany',\n                value: 1\n            }]\n        }]\n    }, {\n        name: 'Soft Drink',\n        children: [{\n            value: 3,\n            name: '<PERSON><PERSON>',\n            children: [{\n                name: 'Apple Juice',\n                value: 1\n            }, {\n                name: 'Orange Juice',\n                value: 2\n            }]\n        }]\n    }]\n}, {\n    value: 6,\n    name: '<PERSON>',\n    children: [{\n        name: '<PERSON><PERSON><PERSON>',\n        children: [{\n            name: 'Shi<PERSON>',\n            value: 1\n        }, {\n            name: 'Jackets',\n            value: 3,\n            children: [{\n                name: 'Men',\n                value: 1\n            }, {\n                name: 'Woman',\n                value: 1\n            }]\n        }, {\n            value: 2,\n            name: 'Coats',\n            children: [{\n                name: 'Men',\n                value: 1\n            }, {\n                name: 'Woman',\n                value: 1\n            }]\n        }]\n    }]\n}, {\n    name: 'Computers',\n    children: [{\n        name: 'Components',\n        value: 4,\n        children: [{\n            name: 'Barebones',\n            value: 1\n        }, {\n            value: 2,\n            name: 'External',\n            children: [{\n                name: 'Hard Drives',\n                value: 2\n            }]\n        }, {\n            name: 'Monitors',\n            value: 1\n        }]\n    }, {\n        value: 3,\n        name: 'Other',\n        children: [{\n            name: 'USB',\n            value: 1,\n        }, {\n            name: 'Cases'\n        }, {\n            name: 'Sound Cards',\n            value: 1\n        }]\n    }]\n}];\n\nconst option = {\n    series: {\n        type: 'treemap',\n        data: data\n    }\n};\n\n", "name": "treemap", "title": "基础矩形树图", "title-en": "Basic Treemap"}]}, "series-sunburst": {"desc": "<p><a href=\"https://en.wikipedia.org/wiki/Pie_chart#Ring_chart_/_Sunburst_chart_/_Multilevel_pie_chart\" target=\"_blank\">旭日图（Sunburst）</a>由多层的环形图组成，在数据结构上，内圈是外圈的父节点。因此，它既能像<a href=\"#series-pie\">饼图</a>一样表现局部和整体的占比，又能像<a href=\"#series-treemap\">矩形树图</a>一样表现层级关系。</p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=sunburst-monochrome&edit=1&reset=1\" width=\"700\" height=\"500\"><iframe />\n\n\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=sunburst-drink&edit=1&reset=1\" width=\"700\" height=\"700\"><iframe />\n\n\n<p><strong>数据下钻</strong></p>\n<p>旭日图默认支持数据下钻，也就是说，当用户点击了某个扇形块之后，将会以该节点作为根结点显示，并且在中间出现一个返回上层节点的圆。如果不希望有数据下钻功能，可以通过将 <a href=\"#series-treemap.nodeClick\">series-sunburst.nodeClick</a> 设置为 <code class=\"codespan\">false</code> 实现。</p>\n\n", "exampleBaseOptions": [{"code": "\nconst data = [{\n    name: 'Food',\n    children: [{\n        value: 3,\n        name: 'Fruit',\n        children: [{\n            value: 1,\n            name: '<PERSON>'\n        }, {\n            value: 2,\n            name: 'Orange',\n            children: [{\n                name: 'Seville Orange',\n                value: 1\n            }, {\n                name: '<PERSON> Orange',\n                value: 1\n            }]\n        }]\n    }, {\n        value: 9,\n        name: '<PERSON>',\n        children: [{\n            value: 6,\n            name: '<PERSON><PERSON>',\n            children: [{\n                name: '<PERSON><PERSON><PERSON>',\n                value: 1\n            }, {\n                name: '<PERSON><PERSON>',\n                value: 1\n            }, {\n                name: '<PERSON>',\n                value: 1\n            }, {\n                name: '<PERSON><PERSON>',\n                value: 1\n            }]\n        }, {\n            value: 2,\n            name: '<PERSON>',\n            children: [{\n                name: '<PERSON>',\n                value: 1\n            }]\n        }, {\n            name: '<PERSON><PERSON><PERSON>',\n            value: 1\n        }]\n    }]\n}, {\n    value: 6,\n    name: 'Drinks',\n    children: [{\n        value: 3,\n        name: '<PERSON>',\n        children: [{\n            name: 'USA',\n            value: 2\n        }, {\n            name: 'Europe',\n            children: [{\n                name: 'Germany',\n                value: 1\n            }]\n        }]\n    }, {\n        name: 'Soft Drink',\n        children: [{\n            value: 3,\n            name: '<PERSON><PERSON>',\n            children: [{\n                name: 'Apple Juice',\n                value: 1\n            }, {\n                name: 'Orange Juice',\n                value: 2\n            }]\n        }]\n    }]\n}, {\n    value: 6,\n    name: '<PERSON>',\n    children: [{\n        name: '<PERSON><PERSON><PERSON>',\n        children: [{\n            name: 'Shi<PERSON>',\n            value: 1\n        }, {\n            name: 'Jackets',\n            value: 3,\n            children: [{\n                name: 'Men',\n                value: 1\n            }, {\n                name: 'Woman',\n                value: 1\n            }]\n        }, {\n            value: 2,\n            name: 'Coats',\n            children: [{\n                name: 'Men',\n                value: 1\n            }, {\n                name: 'Woman',\n                value: 1\n            }]\n        }]\n    }]\n}, {\n    name: 'Computers',\n    children: [{\n        name: 'Components',\n        value: 4,\n        children: [{\n            name: 'Barebones',\n            value: 1\n        }, {\n            value: 2,\n            name: 'External',\n            children: [{\n                name: 'Hard Drives',\n                value: 2\n            }]\n        }, {\n            name: 'Monitors',\n            value: 1\n        }]\n    }, {\n        value: 3,\n        name: 'Other',\n        children: [{\n            name: 'USB',\n            value: 1,\n        }, {\n            name: 'Cases'\n        }, {\n            name: 'Sound Cards',\n            value: 1\n        }]\n    }]\n}];\n\nconst option = {\n    series: {\n        type: 'sunburst',\n        data: data\n    }\n};\n\n", "name": "sunburst", "title": "基础旭日图", "title-en": "Basic Sunburst"}]}, "series-boxplot": {"desc": "<p><a href=\"https://en.wikipedia.org/wiki/Box_plot\" target=\"_blank\">Boxplot</a> 中文可以称为『箱形图』、『盒须图』、『盒式图』、『盒状图』、『箱线图』，是一种用作显示一组数据分散情况资料的统计图。它能显示出一组数据的最大值、最小值、中位数、下四分位数及上四分位数。</p>\n<p><strong>示例如下：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=boxplot-light-velocity&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p>也支持多个 <code class=\"codespan\">series</code> 在同一个坐标系中，参见 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=boxplot-multi&amp;edit=1&amp;reset=1\" target=\"_blank\">例子</a>。</p>\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n     title: [{\n             text: \"Michelson-Morley Experiment\",\n             left: \"center\"\n         }\n     ],\n     xAxis: {\n         type: \"category\",\n         data: [\"0\", \"1\", \"2\", \"3\", \"4\"],\n         boundaryGap: true,\n         nameGap: 30,\n         splitArea: {\n             show: false\n         },\n         axisLabel: {\n             formatter: \"expr {value}\"\n         },\n         splitLine: {\n             show: false\n         },\n     },\n     yAxis: {\n         type: \"value\",\n         name: \"km/s minus 299,000\",\n         splitArea: {\n             show: true,\n         },\n     },\n     series: [{\n         name: \"boxplot\",\n         type: \"boxplot\",\n         data: [\n             [655, 850, 940, 980, 1070],\n             [760, 800, 845, 885, 960],\n             [780, 840, 855, 880, 940],\n             [720, 767.5, 815, 865, 920],\n             [740, 807.5, 810, 870, 950],\n         ]\n     }]\n}\n", "name": "boxplot", "title": "盒须图", "title-en": "Boxplot"}]}, "series-candlestick": {"desc": "<p><a href=\"https://en.wikipedia.org/wiki/Candlestick_chart\" target=\"_blank\">Candlestick</a> 即我们常说的 <code class=\"codespan\">K线图</code>。</p>\n<p>在 ECharts3 中，同时支持 <code class=\"codespan\">&#39;candlestick&#39;</code> 和 <code class=\"codespan\">&#39;k&#39;</code>这两种 <code class=\"codespan\">&#39;series.type&#39;</code>（<code class=\"codespan\">&#39;k&#39;</code> 会被自动转为 <code class=\"codespan\">&#39;candlestick&#39;</code>）。</p>\n<p><strong>示例如下：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=candlestick-sh&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n<p><br>\n<strong>关于『涨』『跌』的颜色：</strong></p>\n<p>不同国家或地区对于 K线图 的颜色定义不一样，可能是『红涨绿跌』或『红涨蓝跌』（如大陆、台湾、日本、韩国等），可能是『绿涨红跌』（如西方国家、香港、新加坡等）。K线图也不一定要用红蓝、红绿来表示涨跌，也可以是『有色/无色』等表示方法。</p>\n<p>默认配置项，采用的是『红涨蓝跌』。如果想更改这个颜色配置，在这些配置项中更改即可：</p>\n<ul>\n<li><a href=\"#series-candlestick.itemStyle.color\">series-candlestick.itemStyle.color</a>：阳线填充色（即『涨』）</li>\n<li><a href=\"#series-candlestick.itemStyle.color0\">series-candlestick.itemStyle.color0</a>：阴线填充色（即『跌』）</li>\n<li><a href=\"#series-candlestick.itemStyle.borderColor\">series-candlestick.itemStyle.borderColor</a>：阳线边框色（即『涨』）</li>\n<li><a href=\"series-candlestick.itemStyle.borderColor0\" target=\"_blank\">series-candlestick.itemStyle.borderColor0</a>：阴线边框色（即『跌』）</li>\n</ul>\n<p><br>\n<br></p>\n\n", "exampleBaseOptions": [{"code": "\nvar upColor = '#ec0000';\nvar upBorderColor = '#8A0000';\nvar downColor = '#00da3c';\nvar downBorderColor = '#008F28';\n\n\n// 数据意义：开盘(open)，收盘(close)，最低(lowest)，最高(highest)\nvar data0 = splitData([\n    ['2013/1/24', 2320.26,2320.26,2287.3,2362.94],\n    ['2013/1/25', 2300,2291.3,2288.26,2308.38],\n    ['2013/1/28', 2295.35,2346.5,2295.35,2346.92],\n    ['2013/1/29', 2347.22,2358.98,2337.35,2363.8],\n    ['2013/1/30', 2360.75,2382.48,2347.89,2383.76],\n    ['2013/1/31', 2383.43,2385.42,2371.23,2391.82],\n    ['2013/2/1', 2377.41,2419.02,2369.57,2421.15],\n    ['2013/2/4', 2425.92,2428.15,2417.58,2440.38],\n    ['2013/2/5', 2411,2433.13,2403.3,2437.42],\n    ['2013/2/6', 2432.68,2434.48,2427.7,2441.73],\n    ['2013/2/7', 2430.69,2418.53,2394.22,2433.89],\n    ['2013/2/8', 2416.62,2432.4,2414.4,2443.03],\n    ['2013/2/18', 2441.91,2421.56,2415.43,2444.8],\n    ['2013/2/19', 2420.26,2382.91,2373.53,2427.07],\n    ['2013/2/20', 2383.49,2397.18,2370.61,2397.94],\n    ['2013/2/21', 2378.82,2325.95,2309.17,2378.82],\n    ['2013/2/22', 2322.94,2314.16,2308.76,2330.88],\n    ['2013/2/25', 2320.62,2325.82,2315.01,2338.78],\n    ['2013/2/26', 2313.74,2293.34,2289.89,2340.71],\n    ['2013/2/27', 2297.77,2313.22,2292.03,2324.63],\n    ['2013/2/28', 2322.32,2365.59,2308.92,2366.16],\n    ['2013/3/1', 2364.54,2359.51,2330.86,2369.65],\n    ['2013/3/4', 2332.08,2273.4,2259.25,2333.54],\n    ['2013/3/5', 2274.81,2326.31,2270.1,2328.14],\n    ['2013/3/6', 2333.61,2347.18,2321.6,2351.44],\n    ['2013/3/7', 2340.44,2324.29,2304.27,2352.02],\n    ['2013/3/8', 2326.42,2318.61,2314.59,2333.67],\n    ['2013/3/11', 2314.68,2310.59,2296.58,2320.96],\n    ['2013/3/12', 2309.16,2286.6,2264.83,2333.29],\n    ['2013/3/13', 2282.17,2263.97,2253.25,2286.33],\n    ['2013/3/14', 2255.77,2270.28,2253.31,2276.22],\n    ['2013/3/15', 2269.31,2278.4,2250,2312.08],\n    ['2013/3/18', 2267.29,2240.02,2239.21,2276.05],\n    ['2013/3/19', 2244.26,2257.43,2232.02,2261.31],\n    ['2013/3/20', 2257.74,2317.37,2257.42,2317.86],\n    ['2013/3/21', 2318.21,2324.24,2311.6,2330.81],\n    ['2013/3/22', 2321.4,2328.28,2314.97,2332],\n    ['2013/3/25', 2334.74,2326.72,2319.91,2344.89],\n    ['2013/3/26', 2318.58,2297.67,2281.12,2319.99],\n    ['2013/3/27', 2299.38,2301.26,2289,2323.48],\n    ['2013/3/28', 2273.55,2236.3,2232.91,2273.55],\n    ['2013/3/29', 2238.49,2236.62,2228.81,2246.87],\n    ['2013/4/1', 2229.46,2234.4,2227.31,2243.95],\n    ['2013/4/2', 2234.9,2227.74,2220.44,2253.42],\n    ['2013/4/3', 2232.69,2225.29,2217.25,2241.34],\n    ['2013/4/8', 2196.24,2211.59,2180.67,2212.59],\n    ['2013/4/9', 2215.47,2225.77,2215.47,2234.73],\n    ['2013/4/10', 2224.93,2226.13,2212.56,2233.04],\n    ['2013/4/11', 2236.98,2219.55,2217.26,2242.48],\n    ['2013/4/12', 2218.09,2206.78,2204.44,2226.26],\n    ['2013/4/15', 2199.91,2181.94,2177.39,2204.99],\n    ['2013/4/16', 2169.63,2194.85,2165.78,2196.43],\n    ['2013/4/17', 2195.03,2193.8,2178.47,2197.51],\n    ['2013/4/18', 2181.82,2197.6,2175.44,2206.03],\n    ['2013/4/19', 2201.12,2244.64,2200.58,2250.11],\n    ['2013/4/22', 2236.4,2242.17,2232.26,2245.12],\n    ['2013/4/23', 2242.62,2184.54,2182.81,2242.62],\n    ['2013/4/24', 2187.35,2218.32,2184.11,2226.12],\n    ['2013/4/25', 2213.19,2199.31,2191.85,2224.63],\n    ['2013/4/26', 2203.89,2177.91,2173.86,2210.58],\n    ['2013/5/2', 2170.78,2174.12,2161.14,2179.65],\n    ['2013/5/3', 2179.05,2205.5,2179.05,2222.81],\n    ['2013/5/6', 2212.5,2231.17,2212.5,2236.07],\n    ['2013/5/7', 2227.86,2235.57,2219.44,2240.26],\n    ['2013/5/8', 2242.39,2246.3,2235.42,2255.21],\n    ['2013/5/9', 2246.96,2232.97,2221.38,2247.86],\n    ['2013/5/10', 2228.82,2246.83,2225.81,2247.67],\n    ['2013/5/13', 2247.68,2241.92,2231.36,2250.85],\n    ['2013/5/14', 2238.9,2217.01,2205.87,2239.93],\n    ['2013/5/15', 2217.09,2224.8,2213.58,2225.19],\n    ['2013/5/16', 2221.34,2251.81,2210.77,2252.87],\n    ['2013/5/17', 2249.81,2282.87,2248.41,2288.09],\n    ['2013/5/20', 2286.33,2299.99,2281.9,2309.39],\n    ['2013/5/21', 2297.11,2305.11,2290.12,2305.3],\n    ['2013/5/22', 2303.75,2302.4,2292.43,2314.18],\n    ['2013/5/23', 2293.81,2275.67,2274.1,2304.95],\n    ['2013/5/24', 2281.45,2288.53,2270.25,2292.59],\n    ['2013/5/27', 2286.66,2293.08,2283.94,2301.7],\n    ['2013/5/28', 2293.4,2321.32,2281.47,2322.1],\n    ['2013/5/29', 2323.54,2324.02,2321.17,2334.33],\n    ['2013/5/30', 2316.25,2317.75,2310.49,2325.72],\n    ['2013/5/31', 2320.74,2300.59,2299.37,2325.53],\n    ['2013/6/3', 2300.21,2299.25,2294.11,2313.43],\n    ['2013/6/4', 2297.1,2272.42,2264.76,2297.1],\n    ['2013/6/5', 2270.71,2270.93,2260.87,2276.86],\n    ['2013/6/6', 2264.43,2242.11,2240.07,2266.69],\n    ['2013/6/7', 2242.26,2210.9,2205.07,2250.63],\n    ['2013/6/13', 2190.1,2148.35,2126.22,2190.1]\n]);\n\n\nfunction splitData(rawData) {\n    var categoryData = [];\n    var values = []\n    for (var i = 0; i < rawData.length; i++) {\n        categoryData.push(rawData[i].splice(0, 1)[0]);\n        values.push(rawData[i])\n    }\n    return {\n        categoryData: categoryData,\n        values: values\n    };\n}\n\nfunction calculateMA(dayCount) {\n    var result = [];\n    for (var i = 0, len = data0.values.length; i < len; i++) {\n        if (i < dayCount) {\n            result.push('-');\n            continue;\n        }\n        var sum = 0;\n        for (var j = 0; j < dayCount; j++) {\n            sum += data0.values[i - j][1];\n        }\n        result.push(sum / dayCount);\n    }\n    return result;\n}\n\n\n\nconst option = {\n    tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n            type: 'cross'\n        }\n    },\n    legend: {\n        data: ['日K', 'MA5', 'MA10', 'MA20', 'MA30']\n    },\n    grid: {\n        left: '10%',\n        right: '10%',\n        bottom: '15%'\n    },\n    xAxis: {\n        type: 'category',\n        data: data0.categoryData,\n        scale: true,\n        boundaryGap: false,\n        axisLine: {onZero: false},\n        splitLine: {show: false},\n        splitNumber: 20,\n        min: 'dataMin',\n        max: 'dataMax'\n    },\n    yAxis: {\n        scale: true,\n        splitArea: {\n            show: true\n        }\n    },\n    dataZoom: [\n        {\n            type: 'inside',\n            start: 50,\n            end: 100\n        },\n        {\n            show: true,\n            type: 'slider',\n            top: '90%',\n            start: 50,\n            end: 100\n        }\n    ],\n    series: [\n        {\n            name: '日K',\n            type: 'candlestick',\n            data: data0.values\n        }\n    ]\n};\n\n\n", "name": "candlestick", "title": "K 线图", "title-en": "Candlestick"}]}, "series-heatmap": {"desc": "<p><strong>热力图</strong></p>\n<p>热力图主要通过颜色去表现数值的大小，必须要配合 <a href=\"#visualMap\">visualMap</a> 组件使用。</p>\n<p>可以应用在<a href=\"#grid\">直角坐标系</a>以及<a href=\"#geo\">地理坐标系</a>上，这两个坐标系上的表现形式相差很大，直角坐标系上必须要使用两个类目轴。</p>\n<p>下面是在直角坐标系上应用的例子：</p>\n<p><strong>直角坐标系：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=heatmap-cartesian&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst hours = ['12a', '1a', '2a', '3a', '4a', '5a', '6a',\n        '7a', '8a', '9a','10a','11a',\n        '12p', '1p', '2p', '3p', '4p', '5p',\n        '6p', '7p', '8p', '9p', '10p', '11p'];\nconst days = ['Saturday', 'Friday', 'Thursday',\n        'Wednesday', 'Tuesday', 'Monday', 'Sunday'];\n\nconst data = [[0,0,5],[0,1,1],[0,2,0],[0,3,0],[0,4,0],[0,5,0],[0,6,0],[0,7,0],[0,8,0],[0,9,0],[0,10,0],[0,11,2],[0,12,4],[0,13,1],[0,14,1],[0,15,3],[0,16,4],[0,17,6],[0,18,4],[0,19,4],[0,20,3],[0,21,3],[0,22,2],[0,23,5],[1,0,7],[1,1,0],[1,2,0],[1,3,0],[1,4,0],[1,5,0],[1,6,0],[1,7,0],[1,8,0],[1,9,0],[1,10,5],[1,11,2],[1,12,2],[1,13,6],[1,14,9],[1,15,11],[1,16,6],[1,17,7],[1,18,8],[1,19,12],[1,20,5],[1,21,5],[1,22,7],[1,23,2],[2,0,1],[2,1,1],[2,2,0],[2,3,0],[2,4,0],[2,5,0],[2,6,0],[2,7,0],[2,8,0],[2,9,0],[2,10,3],[2,11,2],[2,12,1],[2,13,9],[2,14,8],[2,15,10],[2,16,6],[2,17,5],[2,18,5],[2,19,5],[2,20,7],[2,21,4],[2,22,2],[2,23,4],[3,0,7],[3,1,3],[3,2,0],[3,3,0],[3,4,0],[3,5,0],[3,6,0],[3,7,0],[3,8,1],[3,9,0],[3,10,5],[3,11,4],[3,12,7],[3,13,14],[3,14,13],[3,15,12],[3,16,9],[3,17,5],[3,18,5],[3,19,10],[3,20,6],[3,21,4],[3,22,4],[3,23,1],[4,0,1],[4,1,3],[4,2,0],[4,3,0],[4,4,0],[4,5,1],[4,6,0],[4,7,0],[4,8,0],[4,9,2],[4,10,4],[4,11,4],[4,12,2],[4,13,4],[4,14,4],[4,15,14],[4,16,12],[4,17,1],[4,18,8],[4,19,5],[4,20,3],[4,21,7],[4,22,3],[4,23,0],[5,0,2],[5,1,1],[5,2,0],[5,3,3],[5,4,0],[5,5,0],[5,6,0],[5,7,0],[5,8,2],[5,9,0],[5,10,4],[5,11,1],[5,12,5],[5,13,10],[5,14,5],[5,15,7],[5,16,11],[5,17,6],[5,18,0],[5,19,5],[5,20,3],[5,21,4],[5,22,2],[5,23,0],[6,0,1],[6,1,0],[6,2,0],[6,3,0],[6,4,0],[6,5,0],[6,6,0],[6,7,0],[6,8,0],[6,9,0],[6,10,1],[6,11,0],[6,12,2],[6,13,1],[6,14,3],[6,15,4],[6,16,0],[6,17,0],[6,18,0],[6,19,0],[6,20,1],[6,21,2],[6,22,2],[6,23,6]].map(function (item) {\n    return [item[1], item[0], item[2] || '-'];\n});\n\noption = {\n    tooltip: {\n        position: 'top'\n    },\n    animation: false,\n    grid: {\n        height: '50%',\n        top: '10%'\n    },\n    xAxis: {\n        type: 'category',\n        data: hours,\n        splitArea: {\n            show: true\n        }\n    },\n    yAxis: {\n        type: 'category',\n        data: days,\n        splitArea: {\n            show: true\n        }\n    },\n    visualMap: {\n        min: 0,\n        max: 10,\n        calculable: true,\n        orient: 'horizontal',\n        left: 'center',\n        bottom: '15%'\n    },\n    series: [{\n        name: 'Punch Card',\n        type: 'heatmap',\n        data: data\n    }]\n};\n", "name": "heatmap", "title": "直角坐标系热力图", "title-en": "Heatmap on Cartesian"}]}, "series-map": {"desc": "<p><strong>地图。</strong></p>\n<p>地图主要用于地理区域数据的可视化，配合 <a href=\"#visualMap\">visualMap</a> 组件用于展示不同区域的人口分布密度等数据。</p>\n<p>多个<a href=\"#series-map.map\">地图类型</a>相同的系列会在同一地图上显示，这时候使用第一个系列的配置项作为地图绘制的配置。</p>\n<p><strong>Tip: </strong>在 ECharts 3 中不再建议在地图类型的图表使用 <code class=\"codespan\">markLine</code> 和 <code class=\"codespan\">markPoint</code>。如果要实现点数据或者线数据的可视化，可以使用在<a href=\"#geo\">地理坐标系组件</a>上的<a href=\"#series-scatter\">散点图</a>和<a href=\"#series-lines\">线图</a>。</p>\n"}, "series-parallel": {"desc": "<p>平行坐标系的系列。</p>\n<p><strong>平行坐标系介绍</strong></p>\n<p><a href=\"https://en.wikipedia.org/wiki/Parallel_coordinates\" target=\"_blank\">平行坐标系（Parallel Coordinates）</a> 是一种常用的可视化高维数据的图表。</p>\n<p>例如 <a href=\"#series-parallel.data\">series-parallel.data</a> 中有如下数据：</p>\n<pre><code class=\"lang-javascript\">[\n    [1,  55,  9,   56,  0.46,  18,  6,  &#39;良&#39;],\n    [2,  25,  11,  21,  0.65,  34,  9,  &#39;优&#39;],\n    [3,  56,  7,   63,  0.3,   14,  5,  &#39;良&#39;],\n    [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n    { // 数据项也可以是 Object，从而里面能含有对线条的特殊设置。\n        value: [5,  42,  24,  44,  0.76,  40,  16, &#39;优&#39;]\n        lineStyle: {...},\n    }\n    ...\n]\n</code></pre>\n<p>数据中，每一行是一个『数据项』，每一列属于一个『维度』。（例如上面数据每一列的含义分别是：『日期』,『AQI指数』, 『PM2.5』, 『PM10』, 『一氧化碳值』, 『二氧化氮值』, 『二氧化硫值』）。</p>\n<p>平行坐标系适用于对这种多维数据进行可视化分析。每一个维度（每一列）对应一个坐标轴，每一个『数据项』是一条线，贯穿多个坐标轴。在坐标轴上，可以进行数据选取等操作。如下：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/parallel-all&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n<p><strong>配置方式概要</strong></p>\n<p>『平行坐标系』的 <code class=\"codespan\">option</code> 基本配置如下例：</p>\n<pre><code class=\"lang-javascript\">option = {\n    parallelAxis: [                     // 这是一个个『坐标轴』的定义\n        {dim: 0, name: schema[0].text}, // 每个『坐标轴』有个 &#39;dim&#39; 属性，表示坐标轴的维度号。\n        {dim: 1, name: schema[1].text},\n        {dim: 2, name: schema[2].text},\n        {dim: 3, name: schema[3].text},\n        {dim: 4, name: schema[4].text},\n        {dim: 5, name: schema[5].text},\n        {dim: 6, name: schema[6].text},\n        {dim: 7, name: schema[7].text,\n            type: &#39;category&#39;,           // 坐标轴也可以支持类别型数据\n            data: [&#39;优&#39;, &#39;良&#39;, &#39;轻度污染&#39;, &#39;中度污染&#39;, &#39;重度污染&#39;, &#39;严重污染&#39;]\n        }\n    ],\n    parallel: {                         // 这是『坐标系』的定义\n        left: &#39;5%&#39;,                     // 平行坐标系的位置设置\n        right: &#39;13%&#39;,\n        bottom: &#39;10%&#39;,\n        top: &#39;20%&#39;,\n        parallelAxisDefault: {          // 『坐标轴』的公有属性可以配置在这里避免重复书写\n            type: &#39;value&#39;,\n            nameLocation: &#39;end&#39;,\n            nameGap: 20\n        }\n    },\n    series: [                           // 这里三个系列共用一个平行坐标系\n        {\n            name: &#39;北京&#39;,\n            type: &#39;parallel&#39;,           // 这个系列类型是 &#39;parallel&#39;\n            data: [\n                [1,  55,  9,   56,  0.46,  18,  6,  &#39;良&#39;],\n                [2,  25,  11,  21,  0.65,  34,  9,  &#39;优&#39;],\n                ...\n            ]\n        },\n        {\n            name: &#39;上海&#39;,\n            type: &#39;parallel&#39;,\n            data: [\n                [3,  56,  7,   63,  0.3,   14,  5,  &#39;良&#39;],\n                [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n                ...\n            ]\n        },\n        {\n            name: &#39;广州&#39;,\n            type: &#39;parallel&#39;,\n            data: [\n                [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n                [5,  42,  24,  44,  0.76,  40,  16, &#39;优&#39;],\n                ...\n            ]\n        }\n    ]\n};\n</code></pre>\n<p>需要涉及到三个组件：<a href=\"#parallel\">parallel</a>、<a href=\"#parallelAxis\">parallelAxis</a>、<a href=\"#series-parallel\">series-parallel</a></p>\n<ul>\n<li><p><a href=\"#parallel\">parallel</a></p>\n<p>  这个配置项是平行坐标系的『坐标系』本身。一个系列（<code class=\"codespan\">series</code>）或多个系列（如上图中的『北京』、『上海』、『广州』分别各是一个系列）可以共用这个『坐标系』。</p>\n<p>  和其他坐标系一样，坐标系也可以创建多个。</p>\n<p>  位置设置，也是放在这里进行。</p>\n</li>\n<li><p><a href=\"#parallelAxis\">parallelAxis</a></p>\n<p>  这个是『坐标系』中的坐标轴的配置。自然，需要有多个坐标轴。</p>\n<p>  其中有 <a href=\"#parallelAxis.parallelIndex\">parallelAxis.parallelIndex</a> 属性，指定这个『坐标轴』在哪个『坐标系』中。默认使用第一个『坐标系』。</p>\n</li>\n<li><p><a href=\"#series-parallel\">series-parallel</a></p>\n<p>  这个是『系列』的定义。系列被画到『坐标系』上。</p>\n<p>  其中有 <a href=\"#series-parallel.parallelIndex\">series-parallel.parallelIndex</a> 属性，指定使用哪个『坐标系』。默认使用第一个『坐标系』。</p>\n</li>\n</ul>\n<p><strong>配置注意和最佳实践</strong></p>\n<p>配置多个 <a href=\"#parallelAxis\">parallelAxis</a> 时，有些值一样的属性，如果书写多遍则比较繁琐，那么可以放置在 <a href=\"#parallel.parallelAxisDefault\">parallel.parallelAxisDefault</a> 里。在坐标轴初始化前，<a href=\"#parallel.parallelAxisDefault\">parallel.parallelAxisDefault</a> 里的配置项，会分别融合进 <a href=\"#parallelAxis\">parallelAxis</a>，形成最终的坐标轴的配置。</p>\n<p><strong>如果数据量很大并且发生卡顿</strong></p>\n<p>建议把 <a href=\"#series-parallel.lineStyle.width\">series-parallel.lineStyle.width</a> 设为 <code class=\"codespan\">0.5</code>（或更小），\n可能显著改善性能。</p>\n<p><strong>高维数据的显示</strong></p>\n<p>维度比较多时，比如有 50+ 的维度，那么就会有 50+ 个轴。那么可能会页面显示不下。</p>\n<p>可以通过 <a href=\"#parallel.axisExpandable\">parallel.axisExpandable</a> 来改善显示效果。</p>\n\n", "exampleBaseOptions": [{"code": "\nconst dataBJ = [\n    [1,55,9,56,0.46,18,6,\"良\"],\n    [2,25,11,21,0.65,34,9,\"优\"],\n    [3,56,7,63,0.3,14,5,\"良\"],\n    [4,33,7,29,0.33,16,6,\"优\"],\n    [5,42,24,44,0.76,40,16,\"优\"],\n    [6,82,58,90,1.77,68,33,\"良\"],\n    [7,74,49,77,1.46,48,27,\"良\"],\n    [8,78,55,80,1.29,59,29,\"良\"],\n    [9,267,216,280,4.8,108,64,\"重度污染\"],\n    [10,185,127,216,2.52,61,27,\"中度污染\"],\n    [11,39,19,38,0.57,31,15,\"优\"],\n    [12,41,11,40,0.43,21,7,\"优\"],\n    [13,64,38,74,1.04,46,22,\"良\"],\n    [14,108,79,120,1.7,75,41,\"轻度污染\"],\n    [15,108,63,116,1.48,44,26,\"轻度污染\"],\n    [16,33,6,29,0.34,13,5,\"优\"],\n    [17,94,66,110,1.54,62,31,\"良\"],\n    [18,186,142,192,3.88,93,79,\"中度污染\"],\n    [19,57,31,54,0.96,32,14,\"良\"],\n    [20,22,8,17,0.48,23,10,\"优\"],\n    [21,39,15,36,0.61,29,13,\"优\"],\n    [22,94,69,114,2.08,73,39,\"良\"],\n    [23,99,73,110,2.43,76,48,\"良\"],\n    [24,31,12,30,0.5,32,16,\"优\"],\n    [25,42,27,43,1,53,22,\"优\"],\n    [26,154,117,157,3.05,92,58,\"中度污染\"],\n    [27,234,185,230,4.09,123,69,\"重度污染\"],\n    [28,160,120,186,2.77,91,50,\"中度污染\"],\n    [29,134,96,165,2.76,83,41,\"轻度污染\"],\n    [30,52,24,60,1.03,50,21,\"良\"],\n    [31,46,5,49,0.28,10,6,\"优\"]\n];\n\nconst dataGZ = [\n    [1,26,37,27,1.163,27,13,\"优\"],\n    [2,85,62,71,1.195,60,8,\"良\"],\n    [3,78,38,74,1.363,37,7,\"良\"],\n    [4,21,21,36,0.634,40,9,\"优\"],\n    [5,41,42,46,0.915,81,13,\"优\"],\n    [6,56,52,69,1.067,92,16,\"良\"],\n    [7,64,30,28,0.924,51,2,\"良\"],\n    [8,55,48,74,1.236,75,26,\"良\"],\n    [9,76,85,113,1.237,114,27,\"良\"],\n    [10,91,81,104,1.041,56,40,\"良\"],\n    [11,84,39,60,0.964,25,11,\"良\"],\n    [12,64,51,101,0.862,58,23,\"良\"],\n    [13,70,69,120,1.198,65,36,\"良\"],\n    [14,77,105,178,2.549,64,16,\"良\"],\n    [15,109,68,87,0.996,74,29,\"轻度污染\"],\n    [16,73,68,97,0.905,51,34,\"良\"],\n    [17,54,27,47,0.592,53,12,\"良\"],\n    [18,51,61,97,0.811,65,19,\"良\"],\n    [19,91,71,121,1.374,43,18,\"良\"],\n    [20,73,102,182,2.787,44,19,\"良\"],\n    [21,73,50,76,0.717,31,20,\"良\"],\n    [22,84,94,140,2.238,68,18,\"良\"],\n    [23,93,77,104,1.165,53,7,\"良\"],\n    [24,99,130,227,3.97,55,15,\"良\"],\n    [25,146,84,139,1.094,40,17,\"轻度污染\"],\n    [26,113,108,137,1.481,48,15,\"轻度污染\"],\n    [27,81,48,62,1.619,26,3,\"良\"],\n    [28,56,48,68,1.336,37,9,\"良\"],\n    [29,82,92,174,3.29,0,13,\"良\"],\n    [30,106,116,188,3.628,101,16,\"轻度污染\"],\n    [31,118,50,0,1.383,76,11,\"轻度污染\"]\n];\n\nconst dataSH = [\n    [1,91,45,125,0.82,34,23,\"良\"],\n    [2,65,27,78,0.86,45,29,\"良\"],\n    [3,83,60,84,1.09,73,27,\"良\"],\n    [4,109,81,121,1.28,68,51,\"轻度污染\"],\n    [5,106,77,114,1.07,55,51,\"轻度污染\"],\n    [6,109,81,121,1.28,68,51,\"轻度污染\"],\n    [7,106,77,114,1.07,55,51,\"轻度污染\"],\n    [8,89,65,78,0.86,51,26,\"良\"],\n    [9,53,33,47,0.64,50,17,\"良\"],\n    [10,80,55,80,1.01,75,24,\"良\"],\n    [11,117,81,124,1.03,45,24,\"轻度污染\"],\n    [12,99,71,142,1.1,62,42,\"良\"],\n    [13,95,69,130,1.28,74,50,\"良\"],\n    [14,116,87,131,1.47,84,40,\"轻度污染\"],\n    [15,108,80,121,1.3,85,37,\"轻度污染\"],\n    [16,134,83,167,1.16,57,43,\"轻度污染\"],\n    [17,79,43,107,1.05,59,37,\"良\"],\n    [18,71,46,89,0.86,64,25,\"良\"],\n    [19,97,71,113,1.17,88,31,\"良\"],\n    [20,84,57,91,0.85,55,31,\"良\"],\n    [21,87,63,101,0.9,56,41,\"良\"],\n    [22,104,77,119,1.09,73,48,\"轻度污染\"],\n    [23,87,62,100,1,72,28,\"良\"],\n    [24,168,128,172,1.49,97,56,\"中度污染\"],\n    [25,65,45,51,0.74,39,17,\"良\"],\n    [26,39,24,38,0.61,47,17,\"优\"],\n    [27,39,24,39,0.59,50,19,\"优\"],\n    [28,93,68,96,1.05,79,29,\"良\"],\n    [29,188,143,197,1.66,99,51,\"中度污染\"],\n    [30,174,131,174,1.55,108,50,\"中度污染\"],\n    [31,187,143,201,1.39,89,53,\"中度污染\"]\n];\n\nconst schema = [\n    {name: 'date', index: 0, text: '日期'},\n    {name: 'AQIindex', index: 1, text: 'AQI'},\n    {name: 'PM25', index: 2, text: 'PM2.5'},\n    {name: 'PM10', index: 3, text: 'PM10'},\n    {name: 'CO', index: 4, text: ' CO'},\n    {name: 'NO2', index: 5, text: 'NO2'},\n    {name: 'SO2', index: 6, text: 'SO2'},\n    {name: '等级', index: 7, text: '等级'}\n];\n\nconst option = {\n    color: [\n        '#c23531', '#91c7ae', '#dd8668'\n    ],\n    legend: {\n        top: 10,\n        data: ['北京', '上海', '广州'],\n        itemGap: 20\n    },\n    parallelAxis: [\n        {dim: 0, name: schema[0].text, inverse: true, max: 31, nameLocation: 'start'},\n        {dim: 1, name: schema[1].text},\n        {dim: 2, name: schema[2].text},\n        {dim: 3, name: schema[3].text},\n        {dim: 4, name: schema[4].text},\n        {dim: 5, name: schema[5].text},\n        {dim: 6, name: schema[6].text},\n        {dim: 7, name: schema[7].text,\n        type: 'category', data: ['优', '良', '轻度污染', '中度污染', '重度污染', '严重污染']}\n    ],\n    parallel: {\n        left: '5%',\n        right: '13%',\n        bottom: '10%',\n        top: '20%',\n        parallelAxisDefault: {\n            type: 'value',\n            name: 'AQI指数',\n            nameLocation: 'end',\n            nameGap: 20,\n            nameTextStyle: {\n                fontSize: 12\n            }\n        }\n    },\n    series: [\n        {\n            name: '北京',\n            type: 'parallel',\n            data: dataBJ\n        },\n        {\n            name: '上海',\n            type: 'parallel',\n            data: dataSH\n        },\n        {\n            name: '广州',\n            type: 'parallel',\n            data: dataGZ\n        }\n    ]\n};\n", "name": "parallel-series", "title": "平行坐标", "title-en": "<PERSON><PERSON><PERSON>"}]}, "series-lines": {"desc": "<p><strong>路径图</strong></p>\n<p>用于带有起点和终点信息的线数据的绘制，主要用于地图上的航线，路线的可视化。</p>\n<p>ECharts 2.x 里会用地图上的 <code class=\"codespan\">markLine</code> 去绘制迁徙效果，在 ECharts 3 里建议使用单独的 <code class=\"codespan\">lines</code> 类型图表。</p>\n"}, "series-graph": {"desc": "<p><strong>关系图</strong></p>\n<p>用于展现节点以及节点之间的关系数据。</p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=graph&reset=1&edit=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\"title\":{\"text\":\"Les Miserables\",\"subtext\":\"Default layout\",\"top\":\"bottom\",\"left\":\"right\"},\"tooltip\":{},\"legend\":[{\"data\":[\"类目0\",\"类目1\",\"类目2\",\"类目3\",\"类目4\",\"类目5\",\"类目6\",\"类目7\",\"类目8\"]}],\"animationDuration\":1500,\"animationEasingUpdate\":\"quinticInOut\",\"series\":[{\"name\":\"Les Miserables\",\"type\":\"graph\",\"layout\":\"none\",\"data\":[{\"id\":\"0\",\"name\":\"<PERSON><PERSON>\",\"symbolSize\":19.12381,\"x\":-266.82776,\"y\":299.6904,\"value\":28.685715,\"category\":0},{\"id\":\"1\",\"name\":\"<PERSON>\",\"symbolSize\":2.6666666666666665,\"x\":-418.08344,\"y\":446.8853,\"value\":4,\"category\":0},{\"id\":\"2\",\"name\":\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"symbolSize\":6.323809333333333,\"x\":-212.76357,\"y\":245.29176,\"value\":9.485714,\"category\":1},{\"id\":\"3\",\"name\":\"MmeMagloire\",\"symbolSize\":6.323809333333333,\"x\":-242.82404,\"y\":235.26283,\"value\":9.485714,\"category\":1},{\"id\":\"4\",\"name\":\"CountessDeLo\",\"symbolSize\":2.6666666666666665,\"x\":-379.30386,\"y\":429.06424,\"value\":4,\"category\":0},{\"id\":\"5\",\"name\":\"Geborand\",\"symbolSize\":2.6666666666666665,\"x\":-417.26337,\"y\":406.03506,\"value\":4,\"category\":0},{\"id\":\"6\",\"name\":\"Champtercier\",\"symbolSize\":2.6666666666666665,\"x\":-332.6012,\"y\":485.16974,\"value\":4,\"category\":0},{\"id\":\"7\",\"name\":\"Cravatte\",\"symbolSize\":2.6666666666666665,\"x\":-382.69568,\"y\":475.09113,\"value\":4,\"category\":0},{\"id\":\"8\",\"name\":\"Count\",\"symbolSize\":2.6666666666666665,\"x\":-320.384,\"y\":387.17325,\"value\":4,\"category\":0},{\"id\":\"9\",\"name\":\"OldMan\",\"symbolSize\":2.6666666666666665,\"x\":-344.39832,\"y\":451.16772,\"value\":4,\"category\":0},{\"id\":\"10\",\"name\":\"Labarre\",\"symbolSize\":2.6666666666666665,\"x\":-89.34107,\"y\":234.56128,\"value\":4,\"category\":1},{\"id\":\"11\",\"name\":\"Valjean\",\"symbolSize\":66.66666666666667,\"x\":-87.93029,\"y\":-6.8120565,\"value\":100,\"category\":1},{\"id\":\"12\",\"name\":\"Marguerite\",\"symbolSize\":4.495239333333333,\"x\":-339.77908,\"y\":-184.69139,\"value\":6.742859,\"category\":1},{\"id\":\"13\",\"name\":\"MmeDeR\",\"symbolSize\":2.6666666666666665,\"x\":-194.31313,\"y\":178.55301,\"value\":4,\"category\":1},{\"id\":\"14\",\"name\":\"Isabeau\",\"symbolSize\":2.6666666666666665,\"x\":-158.05168,\"y\":201.99768,\"value\":4,\"category\":1},{\"id\":\"15\",\"name\":\"Gervais\",\"symbolSize\":2.6666666666666665,\"x\":-127.701546,\"y\":242.55057,\"value\":4,\"category\":1},{\"id\":\"16\",\"name\":\"Tholomyes\",\"symbolSize\":17.295237333333333,\"x\":-385.2226,\"y\":-393.5572,\"value\":25.942856,\"category\":2},{\"id\":\"17\",\"name\":\"Listolier\",\"symbolSize\":13.638097333333334,\"x\":-516.55884,\"y\":-393.98975,\"value\":20.457146,\"category\":2},{\"id\":\"18\",\"name\":\"Fameuil\",\"symbolSize\":13.638097333333334,\"x\":-464.79382,\"y\":-493.57944,\"value\":20.457146,\"category\":2},{\"id\":\"19\",\"name\":\"Blacheville\",\"symbolSize\":13.638097333333334,\"x\":-515.1624,\"y\":-456.9891,\"value\":20.457146,\"category\":2},{\"id\":\"20\",\"name\":\"Favourite\",\"symbolSize\":13.638097333333334,\"x\":-408.12122,\"y\":-464.5048,\"value\":20.457146,\"category\":2},{\"id\":\"21\",\"name\":\"Dahlia\",\"symbolSize\":13.638097333333334,\"x\":-456.44113,\"y\":-425.13303,\"value\":20.457146,\"category\":2},{\"id\":\"22\",\"name\":\"Zephine\",\"symbolSize\":13.638097333333334,\"x\":-459.1107,\"y\":-362.5133,\"value\":20.457146,\"category\":2},{\"id\":\"23\",\"name\":\"Fantine\",\"symbolSize\":28.266666666666666,\"x\":-313.42786,\"y\":-289.44803,\"value\":42.4,\"category\":2},{\"id\":\"24\",\"name\":\"MmeThenardier\",\"symbolSize\":20.95238266666667,\"x\":4.6313396,\"y\":-273.8517,\"value\":31.428574,\"category\":7},{\"id\":\"25\",\"name\":\"Thenardier\",\"symbolSize\":30.095235333333335,\"x\":82.80825,\"y\":-203.1144,\"value\":45.142853,\"category\":7},{\"id\":\"26\",\"name\":\"Cosette\",\"symbolSize\":20.95238266666667,\"x\":78.64646,\"y\":-31.512747,\"value\":31.428574,\"category\":6},{\"id\":\"27\",\"name\":\"Javert\",\"symbolSize\":31.923806666666668,\"x\":-81.46074,\"y\":-204.20204,\"value\":47.88571,\"category\":7},{\"id\":\"28\",\"name\":\"Fauchelevent\",\"symbolSize\":8.152382000000001,\"x\":-225.73984,\"y\":82.41631,\"value\":12.228573,\"category\":4},{\"id\":\"29\",\"name\":\"Bamatabois\",\"symbolSize\":15.466666666666667,\"x\":-385.6842,\"y\":-20.206686,\"value\":23.2,\"category\":3},{\"id\":\"30\",\"name\":\"Perpetue\",\"symbolSize\":4.495239333333333,\"x\":-403.92447,\"y\":-197.69823,\"value\":6.742859,\"category\":2},{\"id\":\"31\",\"name\":\"Simplice\",\"symbolSize\":8.152382000000001,\"x\":-281.4253,\"y\":-158.45137,\"value\":12.228573,\"category\":2},{\"id\":\"32\",\"name\":\"Scaufflaire\",\"symbolSize\":2.6666666666666665,\"x\":-122.41348,\"y\":210.37503,\"value\":4,\"category\":1},{\"id\":\"33\",\"name\":\"Woman1\",\"symbolSize\":4.495239333333333,\"x\":-234.6001,\"y\":-113.15067,\"value\":6.742859,\"category\":1},{\"id\":\"34\",\"name\":\"Judge\",\"symbolSize\":11.809524666666666,\"x\":-387.84915,\"y\":58.7059,\"value\":17.714287,\"category\":3},{\"id\":\"35\",\"name\":\"Champmathieu\",\"symbolSize\":11.809524666666666,\"x\":-338.2307,\"y\":87.48405,\"value\":17.714287,\"category\":3},{\"id\":\"36\",\"name\":\"Brevet\",\"symbolSize\":11.809524666666666,\"x\":-453.26874,\"y\":58.94648,\"value\":17.714287,\"category\":3},{\"id\":\"37\",\"name\":\"Chenildieu\",\"symbolSize\":11.809524666666666,\"x\":-386.44904,\"y\":140.05937,\"value\":17.714287,\"category\":3},{\"id\":\"38\",\"name\":\"Cochepaille\",\"symbolSize\":11.809524666666666,\"x\":-446.7876,\"y\":123.38005,\"value\":17.714287,\"category\":3},{\"id\":\"39\",\"name\":\"Pontmercy\",\"symbolSize\":6.323809333333333,\"x\":336.49738,\"y\":-269.55914,\"value\":9.485714,\"category\":6},{\"id\":\"40\",\"name\":\"Boulatruelle\",\"symbolSize\":2.6666666666666665,\"x\":29.187843,\"y\":-460.13132,\"value\":4,\"category\":7},{\"id\":\"41\",\"name\":\"Eponine\",\"symbolSize\":20.95238266666667,\"x\":238.36697,\"y\":-210.00926,\"value\":31.428574,\"category\":7},{\"id\":\"42\",\"name\":\"Anzelma\",\"symbolSize\":6.323809333333333,\"x\":189.69513,\"y\":-346.50662,\"value\":9.485714,\"category\":7},{\"id\":\"43\",\"name\":\"Woman2\",\"symbolSize\":6.323809333333333,\"x\":-187.00418,\"y\":-145.02663,\"value\":9.485714,\"category\":6},{\"id\":\"44\",\"name\":\"MotherInnocent\",\"symbolSize\":4.495239333333333,\"x\":-252.99521,\"y\":129.87549,\"value\":6.742859,\"category\":4},{\"id\":\"45\",\"name\":\"Gribier\",\"symbolSize\":2.6666666666666665,\"x\":-296.07935,\"y\":163.11964,\"value\":4,\"category\":4},{\"id\":\"46\",\"name\":\"Jondrette\",\"symbolSize\":2.6666666666666665,\"x\":550.3201,\"y\":522.4031,\"value\":4,\"category\":5},{\"id\":\"47\",\"name\":\"MmeBurgon\",\"symbolSize\":4.495239333333333,\"x\":488.13535,\"y\":356.8573,\"value\":6.742859,\"category\":5},{\"id\":\"48\",\"name\":\"Gavroche\",\"symbolSize\":41.06667066666667,\"x\":387.89572,\"y\":110.462326,\"value\":61.600006,\"category\":8},{\"id\":\"49\",\"name\":\"Gillenormand\",\"symbolSize\":13.638097333333334,\"x\":126.4831,\"y\":68.10622,\"value\":20.457146,\"category\":6},{\"id\":\"50\",\"name\":\"Magnon\",\"symbolSize\":4.495239333333333,\"x\":127.07365,\"y\":-113.05923,\"value\":6.742859,\"category\":6},{\"id\":\"51\",\"name\":\"MlleGillenormand\",\"symbolSize\":13.638097333333334,\"x\":162.63559,\"y\":117.6565,\"value\":20.457146,\"category\":6},{\"id\":\"52\",\"name\":\"MmePontmercy\",\"symbolSize\":4.495239333333333,\"x\":353.66415,\"y\":-205.89165,\"value\":6.742859,\"category\":6},{\"id\":\"53\",\"name\":\"MlleVaubois\",\"symbolSize\":2.6666666666666665,\"x\":165.43939,\"y\":339.7736,\"value\":4,\"category\":6},{\"id\":\"54\",\"name\":\"LtGillenormand\",\"symbolSize\":8.152382000000001,\"x\":137.69348,\"y\":196.1069,\"value\":12.228573,\"category\":6},{\"id\":\"55\",\"name\":\"Marius\",\"symbolSize\":35.58095333333333,\"x\":206.44687,\"y\":-13.805411,\"value\":53.37143,\"category\":6},{\"id\":\"56\",\"name\":\"BaronessT\",\"symbolSize\":4.495239333333333,\"x\":194.82993,\"y\":224.78036,\"value\":6.742859,\"category\":6},{\"id\":\"57\",\"name\":\"Mabeuf\",\"symbolSize\":20.95238266666667,\"x\":597.6618,\"y\":135.18481,\"value\":31.428574,\"category\":8},{\"id\":\"58\",\"name\":\"Enjolras\",\"symbolSize\":28.266666666666666,\"x\":355.78366,\"y\":-74.882454,\"value\":42.4,\"category\":8},{\"id\":\"59\",\"name\":\"Combeferre\",\"symbolSize\":20.95238266666667,\"x\":515.2961,\"y\":-46.167564,\"value\":31.428574,\"category\":8},{\"id\":\"60\",\"name\":\"Prouvaire\",\"symbolSize\":17.295237333333333,\"x\":614.29285,\"y\":-69.3104,\"value\":25.942856,\"category\":8},{\"id\":\"61\",\"name\":\"Feuilly\",\"symbolSize\":20.95238266666667,\"x\":550.1917,\"y\":-128.17537,\"value\":31.428574,\"category\":8},{\"id\":\"62\",\"name\":\"Courfeyrac\",\"symbolSize\":24.609526666666667,\"x\":436.17184,\"y\":-12.7286825,\"value\":36.91429,\"category\":8},{\"id\":\"63\",\"name\":\"Bahorel\",\"symbolSize\":22.780953333333333,\"x\":602.55225,\"y\":16.421427,\"value\":34.17143,\"category\":8},{\"id\":\"64\",\"name\":\"Bossuet\",\"symbolSize\":24.609526666666667,\"x\":455.81955,\"y\":-115.45826,\"value\":36.91429,\"category\":8},{\"id\":\"65\",\"name\":\"Joly\",\"symbolSize\":22.780953333333333,\"x\":516.40784,\"y\":47.242233,\"value\":34.17143,\"category\":8},{\"id\":\"66\",\"name\":\"Grantaire\",\"symbolSize\":19.12381,\"x\":646.4313,\"y\":-151.06331,\"value\":28.685715,\"category\":8},{\"id\":\"67\",\"name\":\"MotherPlutarch\",\"symbolSize\":2.6666666666666665,\"x\":668.9568,\"y\":204.65488,\"value\":4,\"category\":8},{\"id\":\"68\",\"name\":\"Gueulemer\",\"symbolSize\":19.12381,\"x\":78.4799,\"y\":-347.15146,\"value\":28.685715,\"category\":7},{\"id\":\"69\",\"name\":\"Babet\",\"symbolSize\":19.12381,\"x\":150.35959,\"y\":-298.50797,\"value\":28.685715,\"category\":7},{\"id\":\"70\",\"name\":\"Claquesous\",\"symbolSize\":19.12381,\"x\":137.3717,\"y\":-410.2809,\"value\":28.685715,\"category\":7},{\"id\":\"71\",\"name\":\"Montparnasse\",\"symbolSize\":17.295237333333333,\"x\":234.87747,\"y\":-400.85983,\"value\":25.942856,\"category\":7},{\"id\":\"72\",\"name\":\"Toussaint\",\"symbolSize\":6.323809333333333,\"x\":40.942253,\"y\":113.78272,\"value\":9.485714,\"category\":1},{\"id\":\"73\",\"name\":\"Child1\",\"symbolSize\":4.495239333333333,\"x\":437.939,\"y\":291.58234,\"value\":6.742859,\"category\":8},{\"id\":\"74\",\"name\":\"Child2\",\"symbolSize\":4.495239333333333,\"x\":466.04922,\"y\":283.3606,\"value\":6.742859,\"category\":8},{\"id\":\"75\",\"name\":\"Brujon\",\"symbolSize\":13.638097333333334,\"x\":238.79364,\"y\":-314.06345,\"value\":20.457146,\"category\":7},{\"id\":\"76\",\"name\":\"MmeHucheloup\",\"symbolSize\":13.638097333333334,\"x\":712.18353,\"y\":4.8131495,\"value\":20.457146,\"category\":8}],\"links\":[{\"id\":\"0\",\"source\":\"1\",\"target\":\"0\"},{\"id\":\"1\",\"source\":\"2\",\"target\":\"0\"},{\"id\":\"2\",\"source\":\"3\",\"target\":\"0\"},{\"id\":\"3\",\"source\":\"3\",\"target\":\"2\"},{\"id\":\"4\",\"source\":\"4\",\"target\":\"0\"},{\"id\":\"5\",\"source\":\"5\",\"target\":\"0\"},{\"id\":\"6\",\"source\":\"6\",\"target\":\"0\"},{\"id\":\"7\",\"source\":\"7\",\"target\":\"0\"},{\"id\":\"8\",\"source\":\"8\",\"target\":\"0\"},{\"id\":\"9\",\"source\":\"9\",\"target\":\"0\"},{\"id\":\"13\",\"source\":\"11\",\"target\":\"0\"},{\"id\":null,\"source\":\"11\",\"target\":\"2\"},{\"id\":\"11\",\"source\":\"11\",\"target\":\"3\"},{\"id\":\"10\",\"source\":\"11\",\"target\":\"10\"},{\"id\":\"14\",\"source\":\"12\",\"target\":\"11\"},{\"id\":\"15\",\"source\":\"13\",\"target\":\"11\"},{\"id\":\"16\",\"source\":\"14\",\"target\":\"11\"},{\"id\":\"17\",\"source\":\"15\",\"target\":\"11\"},{\"id\":\"18\",\"source\":\"17\",\"target\":\"16\"},{\"id\":\"19\",\"source\":\"18\",\"target\":\"16\"},{\"id\":\"20\",\"source\":\"18\",\"target\":\"17\"},{\"id\":\"21\",\"source\":\"19\",\"target\":\"16\"},{\"id\":\"22\",\"source\":\"19\",\"target\":\"17\"},{\"id\":\"23\",\"source\":\"19\",\"target\":\"18\"},{\"id\":\"24\",\"source\":\"20\",\"target\":\"16\"},{\"id\":\"25\",\"source\":\"20\",\"target\":\"17\"},{\"id\":\"26\",\"source\":\"20\",\"target\":\"18\"},{\"id\":\"27\",\"source\":\"20\",\"target\":\"19\"},{\"id\":\"28\",\"source\":\"21\",\"target\":\"16\"},{\"id\":\"29\",\"source\":\"21\",\"target\":\"17\"},{\"id\":\"30\",\"source\":\"21\",\"target\":\"18\"},{\"id\":\"31\",\"source\":\"21\",\"target\":\"19\"},{\"id\":\"32\",\"source\":\"21\",\"target\":\"20\"},{\"id\":\"33\",\"source\":\"22\",\"target\":\"16\"},{\"id\":\"34\",\"source\":\"22\",\"target\":\"17\"},{\"id\":\"35\",\"source\":\"22\",\"target\":\"18\"},{\"id\":\"36\",\"source\":\"22\",\"target\":\"19\"},{\"id\":\"37\",\"source\":\"22\",\"target\":\"20\"},{\"id\":\"38\",\"source\":\"22\",\"target\":\"21\"},{\"id\":\"47\",\"source\":\"23\",\"target\":\"11\"},{\"id\":\"46\",\"source\":\"23\",\"target\":\"12\"},{\"id\":\"39\",\"source\":\"23\",\"target\":\"16\"},{\"id\":\"40\",\"source\":\"23\",\"target\":\"17\"},{\"id\":\"41\",\"source\":\"23\",\"target\":\"18\"},{\"id\":\"42\",\"source\":\"23\",\"target\":\"19\"},{\"id\":\"43\",\"source\":\"23\",\"target\":\"20\"},{\"id\":\"44\",\"source\":\"23\",\"target\":\"21\"},{\"id\":\"45\",\"source\":\"23\",\"target\":\"22\"},{\"id\":null,\"source\":\"24\",\"target\":\"11\"},{\"id\":\"48\",\"source\":\"24\",\"target\":\"23\"},{\"id\":\"52\",\"source\":\"25\",\"target\":\"11\"},{\"id\":\"51\",\"source\":\"25\",\"target\":\"23\"},{\"id\":\"50\",\"source\":\"25\",\"target\":\"24\"},{\"id\":null,\"source\":\"26\",\"target\":\"11\"},{\"id\":null,\"source\":\"26\",\"target\":\"16\"},{\"id\":\"53\",\"source\":\"26\",\"target\":\"24\"},{\"id\":\"56\",\"source\":\"26\",\"target\":\"25\"},{\"id\":\"57\",\"source\":\"27\",\"target\":\"11\"},{\"id\":\"58\",\"source\":\"27\",\"target\":\"23\"},{\"id\":null,\"source\":\"27\",\"target\":\"24\"},{\"id\":\"59\",\"source\":\"27\",\"target\":\"25\"},{\"id\":\"61\",\"source\":\"27\",\"target\":\"26\"},{\"id\":\"62\",\"source\":\"28\",\"target\":\"11\"},{\"id\":\"63\",\"source\":\"28\",\"target\":\"27\"},{\"id\":\"66\",\"source\":\"29\",\"target\":\"11\"},{\"id\":\"64\",\"source\":\"29\",\"target\":\"23\"},{\"id\":\"65\",\"source\":\"29\",\"target\":\"27\"},{\"id\":\"67\",\"source\":\"30\",\"target\":\"23\"},{\"id\":null,\"source\":\"31\",\"target\":\"11\"},{\"id\":null,\"source\":\"31\",\"target\":\"23\"},{\"id\":null,\"source\":\"31\",\"target\":\"27\"},{\"id\":\"68\",\"source\":\"31\",\"target\":\"30\"},{\"id\":\"72\",\"source\":\"32\",\"target\":\"11\"},{\"id\":\"73\",\"source\":\"33\",\"target\":\"11\"},{\"id\":\"74\",\"source\":\"33\",\"target\":\"27\"},{\"id\":\"75\",\"source\":\"34\",\"target\":\"11\"},{\"id\":\"76\",\"source\":\"34\",\"target\":\"29\"},{\"id\":\"77\",\"source\":\"35\",\"target\":\"11\"},{\"id\":null,\"source\":\"35\",\"target\":\"29\"},{\"id\":\"78\",\"source\":\"35\",\"target\":\"34\"},{\"id\":\"82\",\"source\":\"36\",\"target\":\"11\"},{\"id\":\"83\",\"source\":\"36\",\"target\":\"29\"},{\"id\":\"80\",\"source\":\"36\",\"target\":\"34\"},{\"id\":\"81\",\"source\":\"36\",\"target\":\"35\"},{\"id\":\"87\",\"source\":\"37\",\"target\":\"11\"},{\"id\":\"88\",\"source\":\"37\",\"target\":\"29\"},{\"id\":\"84\",\"source\":\"37\",\"target\":\"34\"},{\"id\":\"85\",\"source\":\"37\",\"target\":\"35\"},{\"id\":\"86\",\"source\":\"37\",\"target\":\"36\"},{\"id\":\"93\",\"source\":\"38\",\"target\":\"11\"},{\"id\":\"94\",\"source\":\"38\",\"target\":\"29\"},{\"id\":\"89\",\"source\":\"38\",\"target\":\"34\"},{\"id\":\"90\",\"source\":\"38\",\"target\":\"35\"},{\"id\":\"91\",\"source\":\"38\",\"target\":\"36\"},{\"id\":\"92\",\"source\":\"38\",\"target\":\"37\"},{\"id\":\"95\",\"source\":\"39\",\"target\":\"25\"},{\"id\":\"96\",\"source\":\"40\",\"target\":\"25\"},{\"id\":\"97\",\"source\":\"41\",\"target\":\"24\"},{\"id\":\"98\",\"source\":\"41\",\"target\":\"25\"},{\"id\":\"101\",\"source\":\"42\",\"target\":\"24\"},{\"id\":\"100\",\"source\":\"42\",\"target\":\"25\"},{\"id\":\"99\",\"source\":\"42\",\"target\":\"41\"},{\"id\":\"102\",\"source\":\"43\",\"target\":\"11\"},{\"id\":\"103\",\"source\":\"43\",\"target\":\"26\"},{\"id\":\"104\",\"source\":\"43\",\"target\":\"27\"},{\"id\":null,\"source\":\"44\",\"target\":\"11\"},{\"id\":\"105\",\"source\":\"44\",\"target\":\"28\"},{\"id\":\"107\",\"source\":\"45\",\"target\":\"28\"},{\"id\":\"108\",\"source\":\"47\",\"target\":\"46\"},{\"id\":\"112\",\"source\":\"48\",\"target\":\"11\"},{\"id\":\"110\",\"source\":\"48\",\"target\":\"25\"},{\"id\":\"111\",\"source\":\"48\",\"target\":\"27\"},{\"id\":\"109\",\"source\":\"48\",\"target\":\"47\"},{\"id\":null,\"source\":\"49\",\"target\":\"11\"},{\"id\":\"113\",\"source\":\"49\",\"target\":\"26\"},{\"id\":null,\"source\":\"50\",\"target\":\"24\"},{\"id\":\"115\",\"source\":\"50\",\"target\":\"49\"},{\"id\":\"119\",\"source\":\"51\",\"target\":\"11\"},{\"id\":\"118\",\"source\":\"51\",\"target\":\"26\"},{\"id\":\"117\",\"source\":\"51\",\"target\":\"49\"},{\"id\":null,\"source\":\"52\",\"target\":\"39\"},{\"id\":\"120\",\"source\":\"52\",\"target\":\"51\"},{\"id\":\"122\",\"source\":\"53\",\"target\":\"51\"},{\"id\":\"125\",\"source\":\"54\",\"target\":\"26\"},{\"id\":\"124\",\"source\":\"54\",\"target\":\"49\"},{\"id\":\"123\",\"source\":\"54\",\"target\":\"51\"},{\"id\":\"131\",\"source\":\"55\",\"target\":\"11\"},{\"id\":\"132\",\"source\":\"55\",\"target\":\"16\"},{\"id\":\"133\",\"source\":\"55\",\"target\":\"25\"},{\"id\":null,\"source\":\"55\",\"target\":\"26\"},{\"id\":\"128\",\"source\":\"55\",\"target\":\"39\"},{\"id\":\"134\",\"source\":\"55\",\"target\":\"41\"},{\"id\":\"135\",\"source\":\"55\",\"target\":\"48\"},{\"id\":\"127\",\"source\":\"55\",\"target\":\"49\"},{\"id\":\"126\",\"source\":\"55\",\"target\":\"51\"},{\"id\":\"129\",\"source\":\"55\",\"target\":\"54\"},{\"id\":\"136\",\"source\":\"56\",\"target\":\"49\"},{\"id\":\"137\",\"source\":\"56\",\"target\":\"55\"},{\"id\":null,\"source\":\"57\",\"target\":\"41\"},{\"id\":null,\"source\":\"57\",\"target\":\"48\"},{\"id\":\"138\",\"source\":\"57\",\"target\":\"55\"},{\"id\":\"145\",\"source\":\"58\",\"target\":\"11\"},{\"id\":null,\"source\":\"58\",\"target\":\"27\"},{\"id\":\"142\",\"source\":\"58\",\"target\":\"48\"},{\"id\":\"141\",\"source\":\"58\",\"target\":\"55\"},{\"id\":\"144\",\"source\":\"58\",\"target\":\"57\"},{\"id\":\"148\",\"source\":\"59\",\"target\":\"48\"},{\"id\":\"147\",\"source\":\"59\",\"target\":\"55\"},{\"id\":null,\"source\":\"59\",\"target\":\"57\"},{\"id\":\"146\",\"source\":\"59\",\"target\":\"58\"},{\"id\":\"150\",\"source\":\"60\",\"target\":\"48\"},{\"id\":\"151\",\"source\":\"60\",\"target\":\"58\"},{\"id\":\"152\",\"source\":\"60\",\"target\":\"59\"},{\"id\":\"153\",\"source\":\"61\",\"target\":\"48\"},{\"id\":\"158\",\"source\":\"61\",\"target\":\"55\"},{\"id\":\"157\",\"source\":\"61\",\"target\":\"57\"},{\"id\":\"154\",\"source\":\"61\",\"target\":\"58\"},{\"id\":\"156\",\"source\":\"61\",\"target\":\"59\"},{\"id\":\"155\",\"source\":\"61\",\"target\":\"60\"},{\"id\":\"164\",\"source\":\"62\",\"target\":\"41\"},{\"id\":\"162\",\"source\":\"62\",\"target\":\"48\"},{\"id\":\"159\",\"source\":\"62\",\"target\":\"55\"},{\"id\":null,\"source\":\"62\",\"target\":\"57\"},{\"id\":\"160\",\"source\":\"62\",\"target\":\"58\"},{\"id\":\"161\",\"source\":\"62\",\"target\":\"59\"},{\"id\":null,\"source\":\"62\",\"target\":\"60\"},{\"id\":\"165\",\"source\":\"62\",\"target\":\"61\"},{\"id\":null,\"source\":\"63\",\"target\":\"48\"},{\"id\":\"174\",\"source\":\"63\",\"target\":\"55\"},{\"id\":null,\"source\":\"63\",\"target\":\"57\"},{\"id\":null,\"source\":\"63\",\"target\":\"58\"},{\"id\":\"167\",\"source\":\"63\",\"target\":\"59\"},{\"id\":null,\"source\":\"63\",\"target\":\"60\"},{\"id\":\"172\",\"source\":\"63\",\"target\":\"61\"},{\"id\":\"169\",\"source\":\"63\",\"target\":\"62\"},{\"id\":\"184\",\"source\":\"64\",\"target\":\"11\"},{\"id\":null,\"source\":\"64\",\"target\":\"48\"},{\"id\":\"175\",\"source\":\"64\",\"target\":\"55\"},{\"id\":\"183\",\"source\":\"64\",\"target\":\"57\"},{\"id\":\"179\",\"source\":\"64\",\"target\":\"58\"},{\"id\":\"182\",\"source\":\"64\",\"target\":\"59\"},{\"id\":\"181\",\"source\":\"64\",\"target\":\"60\"},{\"id\":\"180\",\"source\":\"64\",\"target\":\"61\"},{\"id\":\"176\",\"source\":\"64\",\"target\":\"62\"},{\"id\":\"178\",\"source\":\"64\",\"target\":\"63\"},{\"id\":\"187\",\"source\":\"65\",\"target\":\"48\"},{\"id\":\"194\",\"source\":\"65\",\"target\":\"55\"},{\"id\":\"193\",\"source\":\"65\",\"target\":\"57\"},{\"id\":null,\"source\":\"65\",\"target\":\"58\"},{\"id\":\"192\",\"source\":\"65\",\"target\":\"59\"},{\"id\":null,\"source\":\"65\",\"target\":\"60\"},{\"id\":\"190\",\"source\":\"65\",\"target\":\"61\"},{\"id\":\"188\",\"source\":\"65\",\"target\":\"62\"},{\"id\":\"185\",\"source\":\"65\",\"target\":\"63\"},{\"id\":\"186\",\"source\":\"65\",\"target\":\"64\"},{\"id\":\"200\",\"source\":\"66\",\"target\":\"48\"},{\"id\":\"196\",\"source\":\"66\",\"target\":\"58\"},{\"id\":\"197\",\"source\":\"66\",\"target\":\"59\"},{\"id\":\"203\",\"source\":\"66\",\"target\":\"60\"},{\"id\":\"202\",\"source\":\"66\",\"target\":\"61\"},{\"id\":\"198\",\"source\":\"66\",\"target\":\"62\"},{\"id\":\"201\",\"source\":\"66\",\"target\":\"63\"},{\"id\":\"195\",\"source\":\"66\",\"target\":\"64\"},{\"id\":\"199\",\"source\":\"66\",\"target\":\"65\"},{\"id\":\"204\",\"source\":\"67\",\"target\":\"57\"},{\"id\":null,\"source\":\"68\",\"target\":\"11\"},{\"id\":null,\"source\":\"68\",\"target\":\"24\"},{\"id\":\"205\",\"source\":\"68\",\"target\":\"25\"},{\"id\":\"208\",\"source\":\"68\",\"target\":\"27\"},{\"id\":null,\"source\":\"68\",\"target\":\"41\"},{\"id\":\"209\",\"source\":\"68\",\"target\":\"48\"},{\"id\":\"213\",\"source\":\"69\",\"target\":\"11\"},{\"id\":\"214\",\"source\":\"69\",\"target\":\"24\"},{\"id\":\"211\",\"source\":\"69\",\"target\":\"25\"},{\"id\":null,\"source\":\"69\",\"target\":\"27\"},{\"id\":\"217\",\"source\":\"69\",\"target\":\"41\"},{\"id\":\"216\",\"source\":\"69\",\"target\":\"48\"},{\"id\":\"212\",\"source\":\"69\",\"target\":\"68\"},{\"id\":\"221\",\"source\":\"70\",\"target\":\"11\"},{\"id\":\"222\",\"source\":\"70\",\"target\":\"24\"},{\"id\":\"218\",\"source\":\"70\",\"target\":\"25\"},{\"id\":\"223\",\"source\":\"70\",\"target\":\"27\"},{\"id\":\"224\",\"source\":\"70\",\"target\":\"41\"},{\"id\":\"225\",\"source\":\"70\",\"target\":\"58\"},{\"id\":\"220\",\"source\":\"70\",\"target\":\"68\"},{\"id\":\"219\",\"source\":\"70\",\"target\":\"69\"},{\"id\":\"230\",\"source\":\"71\",\"target\":\"11\"},{\"id\":\"233\",\"source\":\"71\",\"target\":\"25\"},{\"id\":\"226\",\"source\":\"71\",\"target\":\"27\"},{\"id\":\"232\",\"source\":\"71\",\"target\":\"41\"},{\"id\":null,\"source\":\"71\",\"target\":\"48\"},{\"id\":\"228\",\"source\":\"71\",\"target\":\"68\"},{\"id\":\"227\",\"source\":\"71\",\"target\":\"69\"},{\"id\":\"229\",\"source\":\"71\",\"target\":\"70\"},{\"id\":\"236\",\"source\":\"72\",\"target\":\"11\"},{\"id\":\"234\",\"source\":\"72\",\"target\":\"26\"},{\"id\":\"235\",\"source\":\"72\",\"target\":\"27\"},{\"id\":\"237\",\"source\":\"73\",\"target\":\"48\"},{\"id\":\"238\",\"source\":\"74\",\"target\":\"48\"},{\"id\":\"239\",\"source\":\"74\",\"target\":\"73\"},{\"id\":\"242\",\"source\":\"75\",\"target\":\"25\"},{\"id\":\"244\",\"source\":\"75\",\"target\":\"41\"},{\"id\":null,\"source\":\"75\",\"target\":\"48\"},{\"id\":\"241\",\"source\":\"75\",\"target\":\"68\"},{\"id\":\"240\",\"source\":\"75\",\"target\":\"69\"},{\"id\":\"245\",\"source\":\"75\",\"target\":\"70\"},{\"id\":\"246\",\"source\":\"75\",\"target\":\"71\"},{\"id\":\"252\",\"source\":\"76\",\"target\":\"48\"},{\"id\":\"253\",\"source\":\"76\",\"target\":\"58\"},{\"id\":\"251\",\"source\":\"76\",\"target\":\"62\"},{\"id\":\"250\",\"source\":\"76\",\"target\":\"63\"},{\"id\":\"247\",\"source\":\"76\",\"target\":\"64\"},{\"id\":\"248\",\"source\":\"76\",\"target\":\"65\"},{\"id\":\"249\",\"source\":\"76\",\"target\":\"66\"}],\"categories\":[{\"name\":\"类目0\"},{\"name\":\"类目1\"},{\"name\":\"类目2\"},{\"name\":\"类目3\"},{\"name\":\"类目4\"},{\"name\":\"类目5\"},{\"name\":\"类目6\"},{\"name\":\"类目7\"},{\"name\":\"类目8\"}]}]}\n\noption.series[0].data.forEach(function (item) {\n    item.x /= 5;\n    item.y /= 5;\n});\n", "name": "graph", "title": "复杂关系图"}]}, "series-sankey": {"desc": "<p><strong> 桑基图 </strong></p>\n<p>是一种特殊的流图（可以看作是有向无环图）。 它主要用来表示原材料、能量等如何从最初形式经过中间过程的加工或转化达到最终状态。</p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=sankey-energy&edit=1&reset=1\" width=\"700\" height=\"580\"><iframe />\n\n\n<p><br>\n<strong>可视编码：</strong></p>\n<p>桑基图将原数据中的每个<code class=\"codespan\">node</code>编码成一个小矩形，不同的节点尽量用不同的颜色展示，小矩形旁边的<code class=\"codespan\">label</code>编码的是节点的名称。</p>\n<p>此外，图中每两个小矩形之间的边编码的是原数据中的<code class=\"codespan\">link</code>，边的粗细编码的是<code class=\"codespan\">link</code>中的<code class=\"codespan\">value</code>。</p>\n<p><br>\n<strong>排序：</strong></p>\n<p>如果想指定每层节点的顺序是按照 <a href=\"#series-sankey.data\">data</a> 中的顺序排列的。可以设置 <a href=\"#series-sankey.layoutIterations\">layoutIterations</a> 为 <code class=\"codespan\">0</code>。</p>\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\"tooltip\":{\"trigger\":\"item\",\"triggerOn\":\"mousemove\"},\"series\":[{\"type\":\"sankey\",\"data\":[{\"name\":\"Agricultural 'waste'\"},{\"name\":\"Bio-conversion\"},{\"name\":\"Liquid\"},{\"name\":\"Losses\"},{\"name\":\"Solid\"},{\"name\":\"Gas\"},{\"name\":\"Biofuel imports\"},{\"name\":\"Biomass imports\"},{\"name\":\"Coal imports\"},{\"name\":\"Coal\"},{\"name\":\"Coal reserves\"},{\"name\":\"District heating\"},{\"name\":\"Industry\"},{\"name\":\"Heating and cooling - commercial\"},{\"name\":\"Heating and cooling - homes\"},{\"name\":\"Electricity grid\"},{\"name\":\"Over generation / exports\"},{\"name\":\"H2 conversion\"},{\"name\":\"Road transport\"},{\"name\":\"Agriculture\"},{\"name\":\"Rail transport\"},{\"name\":\"Lighting & appliances - commercial\"},{\"name\":\"Lighting & appliances - homes\"},{\"name\":\"Gas imports\"},{\"name\":\"Ngas\"},{\"name\":\"Gas reserves\"},{\"name\":\"Thermal generation\"},{\"name\":\"Geothermal\"},{\"name\":\"H2\"},{\"name\":\"Hydro\"},{\"name\":\"International shipping\"},{\"name\":\"Domestic aviation\"},{\"name\":\"International aviation\"},{\"name\":\"National navigation\"},{\"name\":\"Marine algae\"},{\"name\":\"Nuclear\"},{\"name\":\"Oil imports\"},{\"name\":\"Oil\"},{\"name\":\"Oil reserves\"},{\"name\":\"Other waste\"},{\"name\":\"Pumped heat\"},{\"name\":\"Solar PV\"},{\"name\":\"Solar Thermal\"},{\"name\":\"Solar\"},{\"name\":\"Tidal\"},{\"name\":\"UK land based bioenergy\"},{\"name\":\"Wave\"},{\"name\":\"Wind\"}],\"links\":[{\"source\":\"Agricultural 'waste'\",\"target\":\"Bio-conversion\",\"value\":124.729},{\"source\":\"Bio-conversion\",\"target\":\"Liquid\",\"value\":0.597},{\"source\":\"Bio-conversion\",\"target\":\"Losses\",\"value\":26.862},{\"source\":\"Bio-conversion\",\"target\":\"Solid\",\"value\":280.322},{\"source\":\"Bio-conversion\",\"target\":\"Gas\",\"value\":81.144},{\"source\":\"Biofuel imports\",\"target\":\"Liquid\",\"value\":35},{\"source\":\"Biomass imports\",\"target\":\"Solid\",\"value\":35},{\"source\":\"Coal imports\",\"target\":\"Coal\",\"value\":11.606},{\"source\":\"Coal reserves\",\"target\":\"Coal\",\"value\":63.965},{\"source\":\"Coal\",\"target\":\"Solid\",\"value\":75.571},{\"source\":\"District heating\",\"target\":\"Industry\",\"value\":10.639},{\"source\":\"District heating\",\"target\":\"Heating and cooling - commercial\",\"value\":22.505},{\"source\":\"District heating\",\"target\":\"Heating and cooling - homes\",\"value\":46.184},{\"source\":\"Electricity grid\",\"target\":\"Over generation / exports\",\"value\":104.453},{\"source\":\"Electricity grid\",\"target\":\"Heating and cooling - homes\",\"value\":113.726},{\"source\":\"Electricity grid\",\"target\":\"H2 conversion\",\"value\":27.14},{\"source\":\"Electricity grid\",\"target\":\"Industry\",\"value\":342.165},{\"source\":\"Electricity grid\",\"target\":\"Road transport\",\"value\":37.797},{\"source\":\"Electricity grid\",\"target\":\"Agriculture\",\"value\":4.412},{\"source\":\"Electricity grid\",\"target\":\"Heating and cooling - commercial\",\"value\":40.858},{\"source\":\"Electricity grid\",\"target\":\"Losses\",\"value\":56.691},{\"source\":\"Electricity grid\",\"target\":\"Rail transport\",\"value\":7.863},{\"source\":\"Electricity grid\",\"target\":\"Lighting & appliances - commercial\",\"value\":90.008},{\"source\":\"Electricity grid\",\"target\":\"Lighting & appliances - homes\",\"value\":93.494},{\"source\":\"Gas imports\",\"target\":\"Ngas\",\"value\":40.719},{\"source\":\"Gas reserves\",\"target\":\"Ngas\",\"value\":82.233},{\"source\":\"Gas\",\"target\":\"Heating and cooling - commercial\",\"value\":0.129},{\"source\":\"Gas\",\"target\":\"Losses\",\"value\":1.401},{\"source\":\"Gas\",\"target\":\"Thermal generation\",\"value\":151.891},{\"source\":\"Gas\",\"target\":\"Agriculture\",\"value\":2.096},{\"source\":\"Gas\",\"target\":\"Industry\",\"value\":48.58},{\"source\":\"Geothermal\",\"target\":\"Electricity grid\",\"value\":7.013},{\"source\":\"H2 conversion\",\"target\":\"H2\",\"value\":20.897},{\"source\":\"H2 conversion\",\"target\":\"Losses\",\"value\":6.242},{\"source\":\"H2\",\"target\":\"Road transport\",\"value\":20.897},{\"source\":\"Hydro\",\"target\":\"Electricity grid\",\"value\":6.995},{\"source\":\"Liquid\",\"target\":\"Industry\",\"value\":121.066},{\"source\":\"Liquid\",\"target\":\"International shipping\",\"value\":128.69},{\"source\":\"Liquid\",\"target\":\"Road transport\",\"value\":135.835},{\"source\":\"Liquid\",\"target\":\"Domestic aviation\",\"value\":14.458},{\"source\":\"Liquid\",\"target\":\"International aviation\",\"value\":206.267},{\"source\":\"Liquid\",\"target\":\"Agriculture\",\"value\":3.64},{\"source\":\"Liquid\",\"target\":\"National navigation\",\"value\":33.218},{\"source\":\"Liquid\",\"target\":\"Rail transport\",\"value\":4.413},{\"source\":\"Marine algae\",\"target\":\"Bio-conversion\",\"value\":4.375},{\"source\":\"Ngas\",\"target\":\"Gas\",\"value\":122.952},{\"source\":\"Nuclear\",\"target\":\"Thermal generation\",\"value\":839.978},{\"source\":\"Oil imports\",\"target\":\"Oil\",\"value\":504.287},{\"source\":\"Oil reserves\",\"target\":\"Oil\",\"value\":107.703},{\"source\":\"Oil\",\"target\":\"Liquid\",\"value\":611.99},{\"source\":\"Other waste\",\"target\":\"Solid\",\"value\":56.587},{\"source\":\"Other waste\",\"target\":\"Bio-conversion\",\"value\":77.81},{\"source\":\"Pumped heat\",\"target\":\"Heating and cooling - homes\",\"value\":193.026},{\"source\":\"Pumped heat\",\"target\":\"Heating and cooling - commercial\",\"value\":70.672},{\"source\":\"Solar PV\",\"target\":\"Electricity grid\",\"value\":59.901},{\"source\":\"Solar Thermal\",\"target\":\"Heating and cooling - homes\",\"value\":19.263},{\"source\":\"Solar\",\"target\":\"Solar Thermal\",\"value\":19.263},{\"source\":\"Solar\",\"target\":\"Solar PV\",\"value\":59.901},{\"source\":\"Solid\",\"target\":\"Agriculture\",\"value\":0.882},{\"source\":\"Solid\",\"target\":\"Thermal generation\",\"value\":400.12},{\"source\":\"Solid\",\"target\":\"Industry\",\"value\":46.477},{\"source\":\"Thermal generation\",\"target\":\"Electricity grid\",\"value\":525.531},{\"source\":\"Thermal generation\",\"target\":\"Losses\",\"value\":787.129},{\"source\":\"Thermal generation\",\"target\":\"District heating\",\"value\":79.329},{\"source\":\"Tidal\",\"target\":\"Electricity grid\",\"value\":9.452},{\"source\":\"UK land based bioenergy\",\"target\":\"Bio-conversion\",\"value\":182.01},{\"source\":\"Wave\",\"target\":\"Electricity grid\",\"value\":19.013},{\"source\":\"Wind\",\"target\":\"Electricity grid\",\"value\":289.366}]}]}\n", "name": "sankey", "title": "桑基图", "title-en": "<PERSON><PERSON>"}]}, "series-funnel": {"desc": "<p><strong>漏斗图</strong></p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=funnel&reset=1&edit=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n", "exampleBaseOptions": [{"code": "\noption = {\n    legend: {\n        data: ['Display','Click','Visit','Consulting','Order']\n    },\n    series: [\n        {\n            name:'漏斗图',\n            type:'funnel',\n            data: [\n                {value: 60, name: 'Visit'},\n                {value: 40, name: 'Consulting'},\n                {value: 20, name: 'Order'},\n                {value: 80, name: 'Click'},\n                {value: 100, name: 'Display'}\n            ]\n        }\n    ]\n};\n\n", "name": "funnel", "tilte": "基础漏斗图", "title-en": "Basic Funnel"}]}, "series-gauge": {"desc": "<p><strong>仪表盘</strong></p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=gauge&reset=1&edit=1\" width=\"600\" height=\"500\"><iframe />\n\n\n\n", "exampleBaseOptions": [{"code": "\nconst option = {\n    series: [\n        {\n            name: 'Indicator',\n            type: 'gauge',\n            detail: {formatter: '{value}%'},\n            data: [{value: 50, name: 'Percent'}]\n        }\n    ]\n};\n", "name": "gauge", "title": "基础仪表盘", "title-en": "Basic Gauge"}]}, "series-pictorialBar": {"desc": "<p><strong>象形柱图</strong></p>\n<p>象形柱图是可以设置各种具象图形元素（如图片、<a href=\"http://www.w3.org/TR/SVG/paths.html#PathData\" target=\"_blank\">SVG PathData</a> 等）的柱状图。往往用在信息图中。用于有至少一个类目轴或时间轴的<a href=\"#grid\">直角坐标系</a>上。</p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=pictorialBar-hill&reset=1&edit=1\" width=\"800\" height=\"400\"><iframe />\n\n\n\n<p><strong>布局</strong></p>\n<p>象形柱图可以被想象为：它首先是个柱状图，但是柱状图的柱子并不显示。这些柱子我们称为『基准柱（reference bar）』，根据基准柱来定位和显示各种象形图形（包括图片）。</p>\n<p>每个象形图形根据基准柱的定位，是通过 <a href=\"#series-pictorialBar.symbolPosition\">symbolPosition</a>、<a href=\"#series-pictorialBar.symbolOffset\">symbolOffset</a> 来调整其于基准柱的相对位置。</p>\n<p>参见例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/pictorialBar-position&reset=1&edit=1\" width=\"800\" height=\"600\"><iframe />\n\n\n<p>可以使用 <a href=\"#series-pictorialBar.symbolSize\">symbolSize</a> 调整大小，从而形成各种视图效果。</p>\n<p>参见例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/pictorialBar-symbolSize&reset=1&edit=1\" width=\"800\" height=\"600\"><iframe />\n\n\n\n<p><strong>象形图形类型</strong></p>\n<p>每个图形可以配置成『单独』和『重复』两种类型，即通过 <a href=\"#series-pictorialBar.symbolRepeat\">symbolRepeat</a> 来设置。</p>\n<ul>\n<li>设置为 <code class=\"codespan\">false</code>（默认），则一个图形来代表一个数据项。</li>\n<li>设置为 <code class=\"codespan\">true</code>，则一组重复的图形来代表一个数据项。</li>\n</ul>\n<p>参见例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/pictorialBar-repeat&reset=1&edit=1\" width=\"800\" height=\"400\"><iframe />\n\n\n<p>每个象形图形可以是基本图形（如 <code class=\"codespan\">&#39;circle&#39;</code>, <code class=\"codespan\">&#39;rect&#39;</code>, ...）、<a href=\"http://www.w3.org/TR/SVG/paths.html#PathData\" target=\"_blank\">SVG PathData</a>、图片，参见：<a href=\"#series-pictorialBar.symbolType\">symbolType</a>。</p>\n<p>参见例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/pictorialBar-graphicType&reset=1&edit=1\" width=\"800\" height=\"400\"><iframe />\n\n\n<p>可以使用 <a href=\"#series-pictorialBar.symbolClip\">symbolClip</a> 对图形进行剪裁。</p>\n<p>参见例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/pictorialBar-clip&reset=1&edit=1\" width=\"800\" height=\"600\"><iframe />\n\n\n\n", "exampleBaseOptions": [{"code": "\nvar spirit = 'image://data:image/png;base64,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';\n\nvar maxData = 2000;\n\noption = {\n    tooltip: {\n    },\n    xAxis: {\n        max: maxData,\n        splitLine: {show: false},\n        offset: 10,\n        axisLine: {\n            lineStyle: {\n                color: '#999'\n            }\n        },\n        axisLabel: {\n            margin: 10\n        }\n    },\n    yAxis: {\n        data: ['2013', '2014', '2015', '2016'],\n        inverse: true,\n        axisTick: {show: false},\n        axisLine: {show: false},\n        axisLabel: {\n            margin: 10,\n            color: '#999',\n            fontSize: 16\n        }\n    },\n    grid: {\n        top: 'center',\n        height: 200,\n        left: 70,\n        right: 100\n    },\n    series: [{\n        // current data\n        type: 'pictorialBar',\n        symbol: spirit,\n        symbolRepeat: 'fixed',\n        symbolMargin: '5%',\n        symbolClip: true,\n        symbolSize: 30,\n        symbolBoundingData: maxData,\n        data: [891, 1220, 660, 1670],\n        z: 10\n    }]\n};\n", "name": "pictorial-bar", "title": "基础象形柱图", "title-en": "Basic Pictorial Bar"}]}, "series-themeRiver": {"desc": "<p><strong> 主题河流 </strong></p>\n<p>是一种特殊的流图, 它主要用来表示事件或主题等在一段时间内的变化。</p>\n<p><strong>示例：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=themeRiver-lastfm&edit=1&reset=1\" width=\"700\" height=\"580\"><iframe />\n\n\n\n<p><br>\n<strong>可视编码：</strong></p>\n<p>主题河流中不同颜色的条带状河流分支编码了不同的事件或主题，河流分支的宽度编码了原数据集中的value值。</p>\n<p>此外，原数据集中的时间属性，映射到单个时间轴上。</p>\n\n", "exampleBaseOptions": [{"code": "\n\nconst option = {\n    singleAxis: {\n        top: 50,\n        bottom: 50,\n        axisTick: {},\n        axisLabel: {},\n        type: 'time',\n        axisPointer: {\n            animation: true,\n            label: {\n                show: true\n            }\n        },\n        splitLine: {\n            show: true,\n            lineStyle: {\n                type: 'dashed',\n                opacity: 0.2\n            }\n        }\n    },\n    series: [\n        {\n            type: 'themeRiver',\n            data: [['2015/11/08',10,'DQ'],['2015/11/09',15,'DQ'],['2015/11/10',35,'DQ'],\n            ['2015/11/11',38,'DQ'],['2015/11/12',22,'DQ'],['2015/11/13',16,'DQ'],\n            ['2015/11/14',7,'DQ'],['2015/11/15',2,'DQ'],['2015/11/16',17,'DQ'],\n            ['2015/11/17',33,'DQ'],['2015/11/18',40,'DQ'],['2015/11/19',32,'DQ'],\n            ['2015/11/20',26,'DQ'],['2015/11/21',35,'DQ'],['2015/11/22',40,'DQ'],\n            ['2015/11/23',32,'DQ'],['2015/11/24',26,'DQ'],['2015/11/25',22,'DQ'],\n            ['2015/11/26',16,'DQ'],['2015/11/27',22,'DQ'],['2015/11/28',10,'DQ'],\n            ['2015/11/08',35,'TY'],['2015/11/09',36,'TY'],['2015/11/10',37,'TY'],\n            ['2015/11/11',22,'TY'],['2015/11/12',24,'TY'],['2015/11/13',26,'TY'],\n            ['2015/11/14',34,'TY'],['2015/11/15',21,'TY'],['2015/11/16',18,'TY'],\n            ['2015/11/17',45,'TY'],['2015/11/18',32,'TY'],['2015/11/19',35,'TY'],\n            ['2015/11/20',30,'TY'],['2015/11/21',28,'TY'],['2015/11/22',27,'TY'],\n            ['2015/11/23',26,'TY'],['2015/11/24',15,'TY'],['2015/11/25',30,'TY'],\n            ['2015/11/26',35,'TY'],['2015/11/27',42,'TY'],['2015/11/28',42,'TY'],\n            ['2015/11/08',21,'SS'],['2015/11/09',25,'SS'],['2015/11/10',27,'SS'],\n            ['2015/11/11',23,'SS'],['2015/11/12',24,'SS'],['2015/11/13',21,'SS'],\n            ['2015/11/14',35,'SS'],['2015/11/15',39,'SS'],['2015/11/16',40,'SS'],\n            ['2015/11/17',36,'SS'],['2015/11/18',33,'SS'],['2015/11/19',43,'SS'],\n            ['2015/11/20',40,'SS'],['2015/11/21',34,'SS'],['2015/11/22',28,'SS'],\n            ['2015/11/23',26,'SS'],['2015/11/24',37,'SS'],['2015/11/25',41,'SS'],\n            ['2015/11/26',46,'SS'],['2015/11/27',47,'SS'],['2015/11/28',41,'SS'],\n            ['2015/11/08',10,'QG'],['2015/11/09',15,'QG'],['2015/11/10',35,'QG'],\n            ['2015/11/11',38,'QG'],['2015/11/12',22,'QG'],['2015/11/13',16,'QG'],\n            ['2015/11/14',7,'QG'],['2015/11/15',2,'QG'],['2015/11/16',17,'QG'],\n            ['2015/11/17',33,'QG'],['2015/11/18',40,'QG'],['2015/11/19',32,'QG'],\n            ['2015/11/20',26,'QG'],['2015/11/21',35,'QG'],['2015/11/22',40,'QG'],\n            ['2015/11/23',32,'QG'],['2015/11/24',26,'QG'],['2015/11/25',22,'QG'],\n            ['2015/11/26',16,'QG'],['2015/11/27',22,'QG'],['2015/11/28',10,'QG'],\n            ['2015/11/08',10,'SY'],['2015/11/09',15,'SY'],['2015/11/10',35,'SY'],\n            ['2015/11/11',38,'SY'],['2015/11/12',22,'SY'],['2015/11/13',16,'SY'],\n            ['2015/11/14',7,'SY'],['2015/11/15',2,'SY'],['2015/11/16',17,'SY'],\n            ['2015/11/17',33,'SY'],['2015/11/18',40,'SY'],['2015/11/19',32,'SY'],\n            ['2015/11/20',26,'SY'],['2015/11/21',35,'SY'],['2015/11/22',4,'SY'],\n            ['2015/11/23',32,'SY'],['2015/11/24',26,'SY'],['2015/11/25',22,'SY'],\n            ['2015/11/26',16,'SY'],['2015/11/27',22,'SY'],['2015/11/28',10,'SY'],\n            ['2015/11/08',10,'DD'],['2015/11/09',15,'DD'],['2015/11/10',35,'DD'],\n            ['2015/11/11',38,'DD'],['2015/11/12',22,'DD'],['2015/11/13',16,'DD'],\n            ['2015/11/14',7,'DD'],['2015/11/15',2,'DD'],['2015/11/16',17,'DD'],\n            ['2015/11/17',33,'DD'],['2015/11/18',4,'DD'],['2015/11/19',32,'DD'],\n            ['2015/11/20',26,'DD'],['2015/11/21',35,'DD'],['2015/11/22',40,'DD'],\n            ['2015/11/23',32,'DD'],['2015/11/24',26,'DD'],['2015/11/25',22,'DD'],\n            ['2015/11/26',16,'DD'],['2015/11/27',22,'DD'],['2015/11/28',10,'DD']]\n        }\n    ]\n};\n", "name": "themeRiver", "title": "主题河流图", "title-en": "Basic Theme River"}]}, "series-custom": {"desc": "<p><strong>自定义系列</strong></p>\n<p>自定义系列可以自定义系列中的图形元素渲染。从而能扩展出不同的图表。</p>\n<p>同时，echarts 会统一管理图形的创建删除、动画、与其他组件（如 <a href=\"#dataZoom\">dataZoom</a>、<a href=\"#visualMap\">visualMap</a>）的联动，使开发者不必纠结这些细节。</p>\n<p><strong>例如，下面的例子使用 custom series 扩展出了 x-range 图：</strong></p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=custom-profile&reset=1&edit=1\" width=\"800\" height=\"500\"><iframe />\n\n\n<p><strong>更多的例子参见：<a href=\"http://localhost/incubator-echarts-website/examples/zh/index.html#chart-type-custom\" target=\"_blank\">custom examples</a></strong></p>\n<p><strong><a href=\"tutorial.html#%E8%87%AA%E5%AE%9A%E4%B9%89%E7%B3%BB%E5%88%97\" target=\"_blank\">这里是个教程</a></strong></p>\n<p><br>\n<strong>开发者自定义渲染逻辑（renderItem 函数）</strong></p>\n<p>custom 系列需要开发者自己提供图形渲染的逻辑。这个渲染逻辑一般命名为 <a href=\"#series-custom.renderItem\">renderItem</a>。例如：</p>\n<pre><code class=\"lang-js\">var option = {\n    ...,\n    series: [{\n        type: &#39;custom&#39;,\n        renderItem: function (params, api) {\n            var categoryIndex = api.value(0);\n            var start = api.coord([api.value(1), categoryIndex]);\n            var end = api.coord([api.value(2), categoryIndex]);\n            var height = api.size([0, 1])[1] * 0.6;\n\n            var rectShape = echarts.graphic.clipRectByRect({\n                x: start[0],\n                y: start[1] - height / 2,\n                width: end[0] - start[0],\n                height: height\n            }, {\n                x: params.coordSys.x,\n                y: params.coordSys.y,\n                width: params.coordSys.width,\n                height: params.coordSys.height\n            });\n\n            return rectShape &amp;&amp; {\n                type: &#39;rect&#39;,\n                shape: rectShape,\n                style: api.style()\n            };\n        },\n        data: data\n    }]\n}\n</code></pre>\n<p>对于 <code class=\"codespan\">data</code> 中的每个数据项（为方便描述，这里称为 <code class=\"codespan\">dataItem</code>)，会调用此 <a href=\"#series-custom.renderItem\">renderItem</a> 函数。</p>\n<p><a href=\"#series-custom.renderItem\">renderItem</a> 函数提供了两个参数：</p>\n<ul>\n<li><a href=\"#series-custom.renderItem.arguments.params\">params</a>：包含了当前数据信息和坐标系的信息。</li>\n<li><a href=\"#series-custom.renderItem.arguments.api\">api</a>：是一些开发者可调用的方法集合。</li>\n</ul>\n<p><a href=\"#series-custom.renderItem\">renderItem</a> 函数须返回根据此 <code class=\"codespan\">dataItem</code> 绘制出的图形元素的定义信息，参见 <a href=\"#series-custom.renderItem.return\">renderItem.return</a>。</p>\n<p>一般来说，<a href=\"#series-custom.renderItem\">renderItem</a> 函数的主要逻辑，是将 <code class=\"codespan\">dataItem</code> 里的值映射到坐标系上的图形元素。这一般需要用到 <a href=\"#series-custom.renderItem.arguments.api\">renderItem.arguments.api</a> 中的两个函数：</p>\n<ul>\n<li><a href=\"#series-custom.renderItem.arguments.api.value\">api.value(...)</a>，意思是取出 <code class=\"codespan\">dataItem</code> 中的数值。例如 <code class=\"codespan\">api.value(0)</code> 表示取出当前 <code class=\"codespan\">dataItem</code> 中第一个维度的数值。</li>\n<li><a href=\"#series-custom.renderItem.arguments.api.coord\">api.coord(...)</a>，意思是进行坐标转换计算。例如 <code class=\"codespan\">var point = api.coord([api.value(0), api.value(1)])</code> 表示 <code class=\"codespan\">dataItem</code> 中的数值转换成坐标系上的点。</li>\n</ul>\n<p>有时候还需要用到 <a href=\"#series-custom.renderItem.arguments.api.size\">api.size(...)</a> 函数，表示得到坐标系上一段数值范围对应的长度。</p>\n<p>返回值中样式的设置可以使用 <a href=\"#series-custom.renderItem.arguments.api.style\">api.style(...)</a> 函数，他能得到 <a href=\"#series-custom.itemStyle\">series.itemStyle</a> 中定义的样式信息，以及视觉映射的样式信息。也可以用这种方式覆盖这些样式信息：<code class=\"codespan\">api.style({fill: &#39;green&#39;, stroke: &#39;yellow&#39;})</code>。</p>\n<p><br>\n<strong>维度的映射（encode 和 dimensions 属性）</strong></p>\n<p><code class=\"codespan\">custom</code> 系列往往需要定义 <a href=\"#series-custom.encode\">series.encode</a>，主要用于指明 <code class=\"codespan\">data</code> 的哪些维度映射到哪些数轴上。从而，echarts 能根据这些维度的值的范围，画出合适的数轴刻度。\n同时，encode.tooltip 和 encode.label 也可以被指定，指明默认的 tooltip 和 label 显示什么内容。<a href=\"#series-custom.dimensions\">series.dimensions</a> 也可以被指定，指明显示在 tooltip 中的维度名称，或者维度的类型。</p>\n<p>例如：</p>\n<pre><code class=\"lang-js\">series: {\n    type: &#39;custom&#39;,\n    renderItem: function () {\n        ...\n    },\n    encode: {\n        x: [2, 4, 3],\n        y: 1,\n        label: 0,\n        tooltip: [2, 4, 3]\n    }\n}\n</code></pre>\n<p><br>\n<strong>与 dataZoom 组件的结合</strong></p>\n<p>与 <a href=\"#dataZoom\">dataZoom</a> 结合使用的时候，常常使用会设置 <a href=\"#dataZoom.filterMode\">dataZoom.filterMode</a> 为 &#39;weakFilter&#39;，从而让 <code class=\"codespan\">dataItem</code> 部分超出坐标系边界的时候，不会整体被过滤掉。</p>\n<p><br>\n<strong>关于 <code class=\"codespan\">dataIndex</code> 和 <code class=\"codespan\">dataIndexInside</code> 的区别</strong></p>\n<ul>\n<li><code class=\"codespan\">dataIndex</code> 指的 <code class=\"codespan\">dataItem</code> 在原始数据中的 index。</li>\n<li><code class=\"codespan\">dataIndexInside</code> 指的是 <code class=\"codespan\">dataItem</code> 在当前数据窗口（参见 <a href=\"#dataZoom\">dataZoom</a>）中的 index。</li>\n</ul>\n<p><a href=\"#series-custom.renderItem.arguments.api\">renderItem.arguments.api</a> 中使用的参数都是 <code class=\"codespan\">dataIndexInside</code> 而非 <code class=\"codespan\">dataIndex</code>，因为从 <code class=\"codespan\">dataIndex</code> 转换成 <code class=\"codespan\">dataIndexInside</code> 需要时间开销。</p>\n<p><br>\n<strong>Event listener</strong></p>\n<pre><code class=\"lang-js\">chart.setOption({\n    // ...\n    series: {\n        type: &#39;custom&#39;,\n        renderItem: function () {\n            // ...\n            return {\n                type: &#39;group&#39;,\n                children: [{\n                    type: &#39;circle&#39;\n                    // ...\n                }, {\n                    type: &#39;circle&#39;,\n                    name: &#39;aaa&#39;,\n                    // 用户指定的信息，可以在 event handler 访问到。\n                    info: 12345,\n                    // ...\n                }]\n            };\n        }\n    }\n});\nchart.on(&#39;click&#39;, {element: &#39;aaa&#39;}, function (params) {\n    // 当 name 为 &#39;aaa&#39; 的图形元素被点击时，此回调被触发。\n    console.log(params.info);\n});\n</code></pre>\n"}, "darkMode": {"desc": "<p>是否是暗黑模式，默认会根据背景色 <a href=\"#backgroundColor\">backgroundColor</a> 的亮度自动设置。\n如果是设置了容器的背景色而无法判断到，就可以使用该配置手动指定，echarts 会根据是否是暗黑模式调整文本等的颜色。</p>\n<p>该配置通常会被用于主题中。</p>\n"}, "color": {"desc": "<p>调色盘颜色列表。如果系列没有设置颜色，则会依次循环从该列表中取颜色作为系列颜色。</p>\n<p>默认为：</p>\n<pre><code class=\"lang-js\">[&#39;#c23531&#39;,&#39;#2f4554&#39;, &#39;#61a0a8&#39;, &#39;#d48265&#39;, &#39;#91c7ae&#39;,&#39;#749f83&#39;,  &#39;#ca8622&#39;, &#39;#bda29a&#39;,&#39;#6e7074&#39;, &#39;#546570&#39;, &#39;#c4ccd3&#39;]\n</code></pre>\n"}, "backgroundColor": {"desc": "<p>背景色，默认无背景。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n"}, "textStyle": {"desc": "<p>全局的字体样式。</p>\n"}, "animation": {"desc": "\n\n<p>是否开启动画。</p>\n", "uiControl": {"type": "boolean", "default": "true", "clean": "true"}}, "animationThreshold": {"desc": "<p>是否开启动画的阈值，当单个系列显示的图形数量大于这个阈值时会关闭动画。</p>\n"}, "animationDuration": {"desc": "\n\n<p>初始动画的时长，支持回调函数，可以通过每个数据返回不同的时长实现更戏剧的初始动画效果：</p>\n<pre><code class=\"lang-js\">animationDuration: function (idx) {\n    // 越往后的数据时长越大\n    return idx * 100;\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "default": "1000", "step": "20", "clean": "true"}}, "animationEasing": {"desc": "\n\n<p>初始动画的缓动效果。不同的缓动效果可以参考 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=line-easing\" target=\"_blank\">缓动示例</a>。</p>\n", "uiControl": {"type": "enum", "options": "linear,quadraticIn,quadraticOut,quadraticInOut,cubicIn,cubicOut,cubicInOut,quarticIn,quarticOut,quarticInOut,quinticIn,quinticOut,quinticInOut,sinusoidalIn,sinusoidalOut,sinusoidalInOut,exponentialIn,exponentialOut,exponentialInOut,circularIn,circularOut,circularInOut,elasticIn,elasticOut,elasticInOut,backIn,backOut,backInOut,bounceIn,bounceOut,bounceInOut", "clean": "true"}}, "animationDelay": {"desc": "<p>初始动画的延迟，支持回调函数，可以通过每个数据返回不同的 delay 时间实现更戏剧的初始动画效果。</p>\n<p>如下示例：</p>\n<pre><code class=\"lang-js\">animationDelay: function (idx) {\n    // 越往后的数据延迟越大\n    return idx * 100;\n}\n</code></pre>\n<p>也可以看<a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=bar-animation-delay\" target=\"_blank\">该示例</a></p>\n"}, "animationDurationUpdate": {"desc": "\n\n<p>数据更新动画的时长。</p>\n<p>支持回调函数，可以通过每个数据返回不同的时长实现更戏剧的更新动画效果：</p>\n<pre><code class=\"lang-js\">animationDurationUpdate: function (idx) {\n    // 越往后的数据时长越大\n    return idx * 100;\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "default": "1000", "step": "20"}}, "animationEasingUpdate": {"desc": "\n\n<p>数据更新动画的缓动效果。</p>\n", "uiControl": {"type": "enum", "options": "linear,quadraticIn,quadraticOut,quadraticInOut,cubicIn,cubicOut,cubicInOut,quarticIn,quarticOut,quarticInOut,quinticIn,quinticOut,quinticInOut,sinusoidalIn,sinusoidalOut,sinusoidalInOut,exponentialIn,exponentialOut,exponentialInOut,circularIn,circularOut,circularInOut,elasticIn,elasticOut,elasticInOut,backIn,backOut,backInOut,bounceIn,bounceOut,bounceInOut"}}, "animationDelayUpdate": {"desc": "<p>数据更新动画的延迟，支持回调函数，可以通过每个数据返回不同的 delay 时间实现更戏剧的更新动画效果。</p>\n<p>如下示例：</p>\n<pre><code class=\"lang-js\">animationDelayUpdate: function (idx) {\n    // 越往后的数据延迟越大\n    return idx * 100;\n}\n</code></pre>\n<p>也可以看<a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=bar-animation-delay\" target=\"_blank\">该示例</a></p>\n"}, "stateAnimation": {"desc": "<p>状态切换的动画配置，支持在每个系列里设置单独针对该系列的配置。</p>\n"}, "blendMode": {"desc": "<p>图形的混合模式，不同的混合模式见 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\" target=\"_blank\">https://developer.mozilla.org/zh-CN/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation</a> 。</p>\n<p>默认为 <code class=\"codespan\">&#39;source-over&#39;</code>。 支持每个系列单独设置。</p>\n<p><code class=\"codespan\">&#39;lighter&#39;</code> 也是比较常见的一种混合模式，该模式下图形数量集中的区域会颜色叠加成高亮度的颜色（白色）。常常能起到突出该区域的效果。见示例 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=lines-airline\" target=\"_blank\">全球飞行航线</a></p>\n"}, "hoverLayerThreshold": {"desc": "<p>图形数量阈值，决定是否开启单独的 hover 层，在整个图表的图形数量大于该阈值时开启单独的 hover 层。</p>\n<p>单独的 hover 层主要是为了在高亮图形的时候不需要重绘整个图表，只需要把高亮的图形放入单独的一个 canvas 层进行绘制，防止在图形数量很多的时候因为高亮重绘所有图形导致卡顿。</p>\n<p>ECharts 2 里是底层强制使用单独的层绘制高亮图形，但是会带来很多问题，比如高亮的图形可能会不正确的遮挡所有其它图形，还有图形有透明度因为高亮和正常图形叠加导致不正确的透明度显示，还有移动端上因为每个图表都要多一个 canvas 带来的额外内存开销。因此 3 里默认不会开启该优化，只有在图形数量特别多，有必要做该优化时才会自动开启。</p>\n"}, "useUTC": {"desc": "<p>是否使用 UTC 时间。</p>\n<ul>\n<li><code class=\"codespan\">true</code>: 表示 <code class=\"codespan\">axis.type</code> 为 <code class=\"codespan\">&#39;time&#39;</code> 时，依据 UTC 时间确定 tick 位置，并且 <code class=\"codespan\">axisLabel</code> 和 <code class=\"codespan\">tooltip</code> 默认展示的是 UTC 时间。</li>\n<li><code class=\"codespan\">false</code>: 表示 <code class=\"codespan\">axis.type</code> 为 <code class=\"codespan\">&#39;time&#39;</code> 时，依据本地时间确定 tick 位置，并且 <code class=\"codespan\">axisLabel</code> 和 <code class=\"codespan\">tooltip</code> 默认展示的是本地时间。</li>\n</ul>\n<p>默认取值为false，即使用本地时间。因为考虑到：</p>\n<ul>\n<li>很多情况下，需要展示为本地时间（无论服务器存储的是否为 <code class=\"codespan\">UTC</code> 时间）。</li>\n<li>如果 data 中的时间为 &#39;2012-01-02&#39; 这样的没有指定时区的时间表达式，往往意为本地时间。默认情况下，时间被展示时需要和输入一致而非有时差。</li>\n</ul>\n<p>注意，这个参数实际影响的是『展示』，而非用户输入的时间值的解析。\n关于用户输入的时间值（例如 <code class=\"codespan\">1491339540396</code>, <code class=\"codespan\">&#39;2013-01-04&#39;</code> 等）的解析，参见 <a href=\"#series-line.data\">date 中时间相关部分</a>。</p>\n"}}