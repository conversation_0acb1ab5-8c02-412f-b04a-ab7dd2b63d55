.resource-card {
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
  height: 100%;
  
  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .card-image {
    width: 100%;
    height: 147px;
    object-fit: cover;
  }
  
  .card-title {
    font-size: 14px;
    font-weight: 500;
    color: #151B26;
    margin-bottom: 4px;
  }
  
  .card-desc {
    font-size: 12px;
    color: #84878C;
    margin-bottom: 10px;
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .card-tags {
    display: flex;
    gap: 6px;
  }
  
  .card-tag {
    display: inline-block;
    padding: 1.5px 8px;
    background-color: #F7F8FA;
    color: #4E5969;
    font-size: 12px;
    border-radius: 2px;
  }
}

.category-button {
  margin-right: 10px;
}

.search-input {
  width: 100%;
} 