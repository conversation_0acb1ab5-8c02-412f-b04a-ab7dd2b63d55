const CreateDataPagePopup = (porps: any) => {
  console.log('porps', porps);
  let options: any = [];
  if (porps.group) {
    porps.group.forEach((item: any) => {
      if(item.type===2){
        options.push({
          label: item.name,
          value: item.id
        });
      }
    });
  }

  return {
    type: 'dialog',
    body: [
      {
        id: 'u:d717906b428d',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'input-text',
            label: '页面名称',
            name: 'page_name',
            row: 0,
            id: 'u:7f7cd9abac87',
            placeholder: '请输入页面名称',
            value: porps.pageName,
            required: true
          },
          {
            type: 'select',
            label: '选择分组',
            name: 'select',
            options,
            row: 1,
            id: 'u:82cf215496b9',
            placeholder: '请选择分组',
            multiple: false,
          },
          {
            type: 'checkbox',  // 移动到这里
            name: 'hide_in_nav',
            option: '隐藏导航中表单页面',  // 使用 option 而不是 label
            row: 2,
            id: 'u:hideInNav',
            value: false
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'u:d717906b428d'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: true
      }
    ],
    title: '新建数据管理页面',
    id: 'u:2aac8a329856',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:d2f9d50b6428'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确认',
        primary: true,
        id: 'u:50f63b7790e7',
        onEvent: {
          click: {
            weight: 0,
            actions: []
          }
        }
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '<b>新建数据管理页面</b>'
    },
    $$ref: 'modal-ref-2'
  };
};

export {CreateDataPagePopup};
