// 模块级别的变量

export const EditEntryIcon = (props: {logo?: any}) => {
  console.log('EditIcon接收到的props.logo', props.logo);

  // 处理logo参数，可能是字符串或对象
  let logoData = {icon: '', iconColor: '', iconBg: ''};

  // 如果logo是对象，直接使用对象中的数据
  if (typeof props.logo === 'object' && props.logo !== null) {
    logoData = {
      icon: props.logo.icon ? props.logo.icon.replace(/^['"]|['"]$/g, '') : '',
      iconColor: props.logo.iconColor || '',
      iconBg: props.logo.iconBg || ''
    };
  }

  // 确保SVG正确显示的模板
  const getLogoContent = () => {
    return `
  <% if (data.icon || data.iconColor || data.iconBg) { %>
    <span style="color:<%= data.iconColor || '${logoData.iconColor}' %>;background:<%= data.iconBg || '${logoData.iconBg}' %>;width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle">
      <% if (typeof data.icon === 'string' && data.icon.length >= 2 && data.icon.startsWith("'") && data.icon.endsWith("'")) { %>
        <%= data.icon.slice(1, -1) %>
      <% } else if (typeof data.icon === 'string') { %>
        <%= data.icon %>
      <% } %>
    </span>
  <% } else { %>
    <!-- 默认灰色背景块 -->
    <span style="background:#f5f7fa;width:36px;height:36px;display:inline-block;border-radius:8px;vertical-align:middle"></span>
  <% } %>
  `;
  };
  

  return {
    type: 'dialog',
    title: '修改图标样式',
    size: 'md',
    data: {
      icon: logoData.icon || '',
      iconColor: logoData.iconColor || '',
      iconBg: logoData.iconBg || ''
    },
    closeOnEsc: false,
    closeOnOutside: false,
    body: [
      {
        type: 'form',
        id: 'icon-form',
        body: [
          {
            type: 'tpl',
            tpl: getLogoContent(),
          },
          {
            type: 'button',
            label: '重置图标',
            level: 'default',
            className: 'reset-btn text-right',
            wrapperComponent: 'div',
            style: {
              height: '36px',
              background: '#fff',
              border: '1px solid #E5E6EB',
              float: 'right',
              color: '#1D2129',
              borderRadius: '6px',
              fontWeight: 500,
              fontSize: '14px',
              verticalAlign: 'middle'
            },
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'setValue',
                    componentId: 'icon-form',
                    args: {
                      value: {
                        icon: '',
                        iconColor: '',
                        iconBg: '',
                        resetIcon: true
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            type: 'icon-selector',
            name: 'icon',
            label: '图标',
            placeholder: '请选择图标',
            returnSvg: true,
            style: {
              marginTop: '16px',
            },
          },
          {
            type: 'input-color',
            name: 'iconColor',
            value: logoData.iconColor,
            label: '图标颜色',
            placeholder: '请选择颜色'
          },
          {
            type: 'input-color',
            name: 'iconBg',
            value: logoData.iconBg,
            label: '图标背景',
            placeholder: '请选择背景颜色'
          }
        ]
      }
    ],
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        // 不使用默认的提交行为
        actionType: 'custom',
        onEvent: {
          click: {
            actions: [
              {
                actionType: 'custom',
                // 在按钮点击事件中自定义处理提交逻辑
                script: function (context: any) {
                  // 只在点击确认按钮时才触发提交
                  const formData = context.props.data;
                  console.log('点击确认按钮，提交数据:', formData);

                  // 使用Action.closeDialog行为关闭对话框，并传递数据
                  context.props.onAction(null, {
                    actionType: 'submit',
                    // 确保数组格式与handleIconSave函数期望的格式一致
                    value: [
                      {
                        icon: formData.icon ? formData.icon.replace(/^['"]|['"]$/g, '') : '',
                        iconColor: formData.iconColor || '',
                        iconBg: formData.iconBg || ''
                      }
                    ]
                  });
                }
              }
            ]
          }
        }
      }
    ]
  };
};
