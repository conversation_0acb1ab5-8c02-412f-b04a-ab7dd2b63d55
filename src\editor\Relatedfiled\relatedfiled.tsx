import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';
import {union} from 'lodash';

// 轮播图选项类型
export interface SelectOption {
  title: string;
}

export class RelatedFieldControlPlugin extends BasePlugin {
  private selectPageId = '';
  // 缓存数据选项
  private _dataOptions: any[] = [];
  private _dataFieldOptions: any[] = [];
  static id = 'RelatedFieldControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'relatedfiled';
  $schema = '/schemas/RelatedFieldSchema.json';

  // 组件基本信息
  name = '关联字段';
  panelTitle = '关联字段';
  icon = 'fa fa-link';
  panelIcon = 'fa fa-link';
  pluginIcon = 'icon-link-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于在表单中展示其他字段的关联数据';
  docLink = '/amis/zh-CN/components/form/relatedfiled';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'relatedfiled',
    label: '关联字段',
    name: 'relatedfiled',
    linkType: 'internal',
    placeholder: '请选择关联项',
    clearable: true,
    searchable: true,
    // 添加默认选项，防止组件消失
    options: [
      { label: '请先配置关联字段', value: '' }
    ]
  };

  // 添加事件定义
  events = [
    {
      eventName: 'change',
      eventLabel: '值变化',
      description: '输入内容变化时触发'
    },
    {
      eventName: 'focus',
      eventLabel: '获取焦点',
      description: '输入框获得焦点时触发'
    },
    {
      eventName: 'blur',
      eventLabel: '失去焦点',
      description: '输入框失去焦点时触发'
    },
    {
      eventName: 'add',
      eventLabel: '确认新增',
      description: '新增选项时触发'
    },
    {
      eventName: 'edit',
      eventLabel: '确认编辑',
      description: '编辑选项时触发'
    },
    {
      eventName: 'delete',
      eventLabel: '确认删除',
      description: '删除选项时触发'
    },
    {
      eventName: 'validateSuccess',
      eventLabel: '校验成功',
      description: '校验成功时触发'
    },
    {
      eventName: 'validateFail',
      eventLabel: '校验失败',
      description: '校验失败时触发'
    }
  ];

  // 预览界面
  previewSchema = {
    type: 'relatedfiled',
    label: '关联字段',
    name: 'relatedfiled',
    placeholder: '请选择关联项',
    options: [
      { label: '预览选项1', value: 'preview1' },
      { label: '预览选项2', value: 'preview2' }
    ]
  };

  // 设置动态数据源
  setupDynamicSource = (context: any, schema: any) => {
    // 构建动态source URL，包含必要的参数
    const sourceUrl = `/admin-api/system/form-field-value/page/${this.selectPageId}?pageNo=1&pageSize=100`;

    // 创建source配置，使用字符串格式的adaptor
    const source = {
      method: 'GET',
      url: sourceUrl,
      adaptor: `
        if (!payload.data || !payload.data.list) {
          return {
            data: {
              options: []
            },
            status: 0,
            msg: ''
          };
        }

        // 根据配置的字段名生成选项，过滤空值
        const fieldName = '${schema.fieldName}';
        const options = payload.data.list
          .filter((item) => {
            const fieldValue = item[fieldName];
            // 过滤掉空值、null、undefined、空字符串和纯空格
            return fieldValue != null &&
                   fieldValue !== '' &&
                   String(fieldValue).trim() !== '';
          })
          .map((item, index) => ({
            label: String(item[fieldName]).trim(),
            value: String(item[fieldName]).trim()
          }))
          // 去重，避免重复选项
          .filter((option, index, self) =>
            index === self.findIndex(o => o.value === option.value)
          );

        return {
          ...payload,
          data: {
            options: options
          }
        };
      `
    };

    // 更新schema，使用动态source而不是静态options
    const newSchema = {
      ...schema,
      type: 'relatedfiled', // 确保类型正确
      source: source,
      // 保留一些基本配置，避免被覆盖
      placeholder: schema.placeholder || '请选择关联项',
      clearable: schema.clearable !== false,
      searchable: schema.searchable !== false,
      // 移除静态options，使用动态source
      options: undefined,
      // 保存字段名以便后续使用
      fieldName: schema.fieldName,
      // 保存关联表单ID
      relatedFormId: this.selectPageId,
      // 保存关联配置，确保不丢失
      linkType: schema.linkType || 'internal',
      relatedForm: schema.relatedForm,
      relatedField: schema.relatedField
    };

    console.log('setupDynamicSource - 更新schema:', newSchema);

    // 安全更新schema
    this.safeChangeValue(context, newSchema);
  };

  // 安全更新schema
  safeChangeValue(context: any, newSchema: any) {
    console.log('safeChangeValue - 尝试更新schema:', {
      nodeId: context?.node?.id,
      newSchema: newSchema,
      hasManager: !!this.manager,
      hasStore: !!this.manager?.store,
      hasChangeValueById: typeof this.manager?.store?.changeValueById === 'function'
    });

    try {
      if (
        context &&
        context.node &&
        !context.node.disposedNow &&
        this.manager &&
        this.manager.store &&
        typeof this.manager.store.changeValueById === 'function'
      ) {
        this.manager.store.changeValueById(context.node.id, newSchema);
        console.log('safeChangeValue - schema更新成功');
        return true;
      } else {
        console.warn('safeChangeValue - 更新条件不满足');
      }
    } catch (error) {
      console.error('safeChangeValue - 更新schema失败:', error);
    }
    return false;
  }

    // 根据API返回的数据生成轮播项
    generateCarouselItems = (
      dataList: any[],
      fieldName: string,
    ): SelectOption[] => {
      if (!Array.isArray(dataList) || dataList.length === 0) {
        return [];
      }
  
      // 遍历数据列表，生成轮播项
      return dataList.map(item => {
        // 创建轮播项
        return {
          title: item[fieldName] || ''
        };
      });
    };

  // 面板配置
  panelBodyCreator = (context: any) => {
    // 从URL中提取应用ID
    let applicationId = ''; // 默认值
    let applicationPageId = ''; // 默认值
    try {
      // 获取URL路径
      const pathname = window.location.hash;
      // 方法1：正则表达式匹配portletEditor后面的数字
      const matches = pathname.match(/\/app(\d+)/);
      if (matches && matches[1]) {
        applicationId = matches[1];
      }
      // 方法1：正则表达式匹配portletEditor后面的数字
      const pageMatches = pathname.match(/editCode=(\d+)/);
      if (pageMatches && pageMatches[1]) {
        applicationPageId = pageMatches[1];
      }
    } catch (e) {
      console.error('提取应用ID时出错:', e);
    }
    
    const initAssociatedDataTable = () => {
      try {
        // 如果已经有contentSource但没有__associatedDataTable
        if (
          this._dataOptions &&
          this._dataOptions.length
        ) {
          // 查找匹配的数据源
          const selectedOption = this._dataOptions.find(
            item => item.value == context.schema.relatedForm
          );
          console.log('selectedOption',selectedOption)
          this.selectPageId = selectedOption.value;

          setTimeout(() => {
            const selectedFieldOption = this._dataFieldOptions.find(
              item => item.value == context.schema.relatedField
            );

            console.log('this._dataFieldOptions:', this._dataFieldOptions);
            console.log('this.selectedLinkOption:', selectedFieldOption);

            if (
              selectedOption &&
              selectedOption.associatedDataTable &&
              selectedFieldOption
            ) {
              // 更新schema
              const newSchema = {
                ...context.schema,
                __associatedDataTable: selectedOption.associatedDataTable,
                fieldName: selectedFieldOption.name
              };
              context.schema.__associatedDataTable =
                selectedOption.associatedDataTable;
              context.schema.fieldName = selectedFieldOption.name;
              // 安全更新schema
              this.safeChangeValue(context, newSchema);
              return true;
            } else {
              return false;
            }
          }, 2000); // 延迟1秒后尝试再次初始化

          if (selectedOption && selectedOption.associatedDataTable) {
            // 更新schema
            const newSchema = {
              ...context.schema,
              __associatedDataTable: selectedOption.associatedDataTable
            };
            context.schema.__associatedDataTable =
              selectedOption.associatedDataTable;
            // 安全更新schema
            this.safeChangeValue(context, newSchema);
            return true;
          }
        }
      } catch (error) {
        console.error('初始化关联数据表出错:', error);
      }
      return false;
    };

    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl('collapseGroup', [
          {
            title: '基本',
            body: [
              getSchemaTpl('formItemName', {
                required: true
              }),
              getSchemaTpl('label'),
              getSchemaTpl('labelRemark'),
              getSchemaTpl('remark'),
              getSchemaTpl('placeholder'),
              getSchemaTpl('description')
            ]
          },
          {
            title: '关联属性',
            body: [
              {
                type: 'radios',
                name: 'linkType',
                label: '关联应用',
                value: 'internal',
                options: [
                  {label: '本应用', value: 'internal'},
                  {label: '跨应用', value: 'cross'}
                ],

                onChange: (
                  value: any,
                  _oldValue: any,
                  _model: any,
                  form: any
                ) => {
                  if (!value) return;
                  context.schema.linkType = value;

                  form.setValueByName('linkType', value);
                }
              },
              {
                type: 'select',
                name: 'relatedForm',
                label: '关联表单',
                placeholder: '请选择要关联的表单',
                source: {
                  url: '${linkType==="cross"?`/admin-api/system/application-page-and-class/list?applicantOrBackend=1`:`/admin-api/system/application-page-and-class/list?applicationId=${applicationId}&applicantOrBackend=1`}',
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  adaptor: (payload: any) => {
                    const list = payload?.data ?? [];
                    // 过滤出类型为1的项
                    let filteredData = Array.isArray(list)
                      ? list.filter((item: any) => item.type === 1)
                      : [];

                    // 获取当前选择的linkType
                    const schema = context.schema || {};
                    const formAppType = schema.linkType || 'internal';
                    // 如果是internal模式，只显示当前应用ID的数据
                    if (formAppType === 'internal' && applicationId) {
                      filteredData = filteredData.filter(
                        (item: any) =>
                          String(item.applicationId) === String(applicationId)
                      );
                    }

                    // 转换为select需要的options格式
                    const options = filteredData.map((item: any) => ({
                      label: item.name,
                      value: item.id
                    }));

                    // 保存选项以供后续使用
                    this._dataOptions = options;

                    setTimeout(() => {
                      initAssociatedDataTable();
                    }, 100);
                    // 返回符合AMIS select组件要求的格式
                    return {
                      status: 0,
                      msg: '',
                      data: {
                        options: options
                      }
                    };
                  }
                },
                clearable: true,
                searchable: true,
                onChange: (
                  value: any,
                  _oldValue: any,
                  _model: any,
                  form: any
                ) => {
                  if (!value) return;

                  // 查找选中的数据源项
                  let selectedOption = this._dataOptions.find(
                    item => item.value == value
                  );
                  if (selectedOption && selectedOption.label) {
                    console.log(selectedOption);
                    this.selectPageId = selectedOption.value;
                    // 保存关联的表单名称
                    form.setValueByName(
                      'relatedFormName',
                      selectedOption.label
                    );

                    // 更新schema，保存关联表单ID
                    const schema = context.schema;
                    schema.relatedFormId = selectedOption.value;
                    schema.relatedForm = selectedOption.value; // 同时保存到relatedForm字段

                    // 确保基本配置不丢失
                    const updatedSchema = {
                      ...schema,
                      type: 'relatedfiled',
                      relatedFormId: selectedOption.value,
                      relatedForm: selectedOption.value,
                      placeholder: schema.placeholder || '请选择关联项',
                      clearable: schema.clearable !== false,
                      searchable: schema.searchable !== false
                    };

                    this.safeChangeValue(context, updatedSchema);
                  }
                }
              },
              {
                type: 'select',
                name: 'relatedField',
                label: '关联字段',
                placeholder: '请选择要关联的字段',
                source: {
                  method: 'GET',
                  url: `/admin-api/system/form-field/page?applicationPageId=\${relatedForm}&pageNo=1&pageSize=100`,
                  adaptor: (payload: any) => {
                    if (!payload.data || !payload.data.list) {
                      return {
                        data: {
                          options: []
                        },
                        status: 0,
                        msg: ''
                      };
                    }
                    const options = payload.data.list.map((item: any) => ({
                      label: item.label,
                      name: item.name,
                      value: item.id
                    }));

                    this._dataFieldOptions = options;
                    return {
                      ...payload,
                      data: {
                        options: options
                      }
                    };
                  }
                },
                onChange: (
                  value: any,
                  _oldValue: any,
                  _model: any,
                  form: any
                ) => {
                  if (!value) return;

                  let selectedOption = this._dataFieldOptions.find(
                    item => item.value == value
                  );
                  const schema = context.schema;
                  // 获取当前schema
                  schema.fieldName = selectedOption.name;
                  schema.relatedField = value; // 保存选择的字段ID

                  // 先保存基本配置，确保不丢失
                  const basicSchema = {
                    ...schema,
                    fieldName: selectedOption.name,
                    relatedField: value,
                    relatedFormId: this.selectPageId
                  };
                  this.safeChangeValue(context, basicSchema);

                  // 然后设置动态source
                  setTimeout(() => {
                    this.setupDynamicSource(context, basicSchema);
                  }, 100);
                },
                visibleOn: 'this.relatedForm'
              },

              getSchemaTpl('clearable'),
              getSchemaTpl('searchable')
            ]
          },
          {
            title: '高级',
            body: [
              getSchemaTpl('switch', {
                name: 'showInvalidMatch',
                label: '选项值检查',
                value: false
              }),
              getSchemaTpl('maxLength', {
                name: 'virtualThreshold',
                label: '虚拟列表阈值',
                step: 1
              }),
              getSchemaTpl('maxLength', {
                name: 'itemHeight',
                label: '选项高度',
                step: 1
              })
            ]
          },
          getSchemaTpl('status', {
            isFormItem: true,
            readonly: true
          }),
          {
            title: '校验',
            body: [
              getSchemaTpl('required', {
                name: 'required',
                label: '必填',
                value: false
              }),
              getSchemaTpl('validationApiControl'),
              {
                type: 'select',
                name: 'validateOnChange',
                label: '校验触发',
                value: 'default',
                clearable: true,
                options: [
                  {
                    label: '提交后每次修改修改即触发',
                    value: 'default'
                  },
                  {
                    label: '修改即触发',
                    value: true
                  },
                  {
                    label: '提交触发',
                    value: false
                  }
                ],
                pipeIn: (value: any) => {
                  if (value === undefined || value === null) {
                    return 'default';
                  }
                  return value;
                },
                pipeOut: (value: any) => {
                  if (value === 'default') {
                    return undefined;
                  }
                  return value;
                }
              }
            ]
          }
        ])
      },
      {
        title: '外观',
        body: getSchemaTpl('collapseGroup', [
          getSchemaTpl('style:common', {
            exclude: ['layout']
          })
        ])
      },
      {
        title: '事件',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(RelatedFieldControlPlugin);
