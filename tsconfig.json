{
  "compilerOptions": {
    "baseUrl": "./",
    "module": "esnext",
    "target": "esnext",
    "lib": ["es6", "dom"],
    "sourceMap": true,
    "jsx": "react",
    "moduleResolution": "node",
    "rootDir": "./src",
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "typeRoots": ["./node_modules/@types", "./typings"],
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["**/*", "src/**/*"],
  "exclude": [
    "node_modules",
    "dist",
    "src/renderer/*.js" // 排除特定的 JS 文件
  ],
  "types": ["typePatches"]
}
