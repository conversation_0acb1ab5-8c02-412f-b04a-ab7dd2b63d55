import React from 'react';
import {Renderer, RendererProps} from 'amis';
import {FormControlProps} from 'amis-core';
import {SpinnerExtraProps} from 'amis-ui';
import {BadgeObject} from 'amis-ui';

export type NavItemSchema = {
  /**
   * 文字说明
   */
  label?: string;
  /**
   * 图标类名，参考 fontawesome 4。
   */
  icon?: string | Array<{icon?: string; position: string}>;
  /**
   * 链接地址
   */
  to?: string;
  /**
   * 链接打开方式
   */
  target?: string;
  /**
   * 是否展开
   */
  unfolded?: boolean;
  /**
   * 是否激活
   */
  active?: boolean;
  /**
   * 是否懒加载
   */
  defer?: boolean;
  /**
   * 懒加载API
   */
  deferApi?: string;
  /**
   * 子菜单
   */
  children?: Array<NavItemSchema>;
  /**
   * 唯一标识
   */
  key?: string;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 禁用提示
   */
  disabledTip?: string;
  /**
   * 自定义类名
   */
  className?: string;
  /**
   * 菜单模式
   */
  mode?: string;
};

export interface NavOverflow {
  /**
   * 是否开启响应式收纳
   */
  enable: boolean;
  /**
   * 菜单触发按钮的文字
   */
  overflowLabel?: string;
  /**
   * 菜单触发按钮的图标
   * @default "fa fa-ellipsis-h"
   */
  overflowIndicator?: string;
  /**
   * 菜单触发按钮CSS类名
   */
  overflowClassName?: string;
  /**
   * Popover浮层CSS类名
   */
  overflowPopoverClassName?: string;
  /**
   * 菜单外层CSS类名
   */
  overflowListClassName?: string;
  /**
   * 导航横向布局时，开启开启响应式收纳后最大可显示数量，超出此数量的导航将被收纳到下拉菜单中
   */
  maxVisibleCount?: number;
  /**
   * 包裹导航的外层标签名，可以使用其他标签渲染
   * @default "ul"
   */
  wrapperComponent?: string;
  /**
   * 导航项目宽度
   * @default 160
   */
  itemWidth?: number;
  /**
   * 导航列表后缀节点
   */
  overflowSuffix?: any;
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 导航超出后响应式收纳方案。
   * @default "popup"
   * popup 导航被收纳到下拉菜单中
   * swipe 导航展示在一个可左右滑动的菜单中，通过左右箭头滚动查看。只在横向布局有效
   */
  mode?: 'popup' | 'swipe';
}

export interface MyNavProps extends RendererProps {
  /**
   * 指定为 my-nav 类型
   */
  type?: 'my-json-schema';
  
  /**
   * 链接地址集合
   */
  links?: Array<NavItemSchema>;
  
  /**
   * 缩进大小
   * @default 16
   */
  indentSize?: number;
  
  /**
   * 可以通过 API 拉取。
   */
  source?: string;
  
  /**
   * 懒加载 api，如果不配置复用 source 接口。
   */
  deferApi?: string;
  
  /**
   * true 为垂直排列，false 为水平排列类似如 tabs。
   */
  stacked?: boolean;
  
  /**
   * 更多操作菜单列表
   */
  itemActions?: any;
  
  /**
   * 可拖拽
   */
  draggable?: boolean;
  
  /**
   * 保存排序的 api
   */
  saveOrderApi?: string;
  
  /**
   * 角标
   */
  itemBadge?: BadgeObject;
  
  /**
   * 角标
   */
  badge?: BadgeObject;
  
  /**
   * 仅允许同层级拖拽
   */
  dragOnSameLevel?: boolean;
  
  /**
   * 横向导航时自动收纳配置
   */
  overflow?: NavOverflow;
  
  /**
   * 最多展示多少层级
   */
  level?: number;
  
  /**
   * 默认展开层级 小于等于该层数的节点默认全部打开
   */
  defaultOpenLevel?: number;
  
  /**
   * 控制仅展示指定key菜单下的子菜单项
   */
  showKey?: string;
  
  /**
   * 控制菜单缩起
   */
  collapsed?: boolean;
  
  /**
   * 垂直模式 非折叠状态下 控制菜单打开方式
   */
  mode?: 'panel' | 'float' | 'inline';
  
  /**
   * 自定义展开图标
   */
  expandIcon?: string;
  
  /**
   * 自定义展开图标位置 默认在前面 before after
   */
  expandPosition?: string;
  
  /**
   * 主题配色 默认light
   */
  themeColor?: 'light' | 'dark';
  
  /**
   * 手风琴展开 仅垂直inline模式支持
   */
  accordion?: boolean;
  
  /**
   * 子菜单项展开浮层样式
   */
  popupClassName?: string;
  
  /**
   * 是否开启搜索
   */
  searchable?: boolean;
  
  /**
   * 搜索框相关配置
   */
  searchConfig?: {
    /**
     * 搜索框外层CSS样式类
     */
    className?: string;
    /**
     * 搜索匹配函数
     */
    matchFunc?: string | any;
    /**
     * 占位符
     */
    placeholder?: string;
    /**
     * 是否为 Mini 样式。
     */
    mini?: boolean;
    /**
     * 是否为加强样式
     */
    enhance?: boolean;
    /**
     * 是否可清除
     */
    clearable?: boolean;
    /**
     * 是否立马搜索。
     */
    searchImediately?: boolean;
    /**
     * 指定唯一标识字段
     */
    valueField?: string;
  };
  
  /**
   * 是否可见
   */
  visible?: boolean;
  /**
   * 是否隐藏
   */
  hidden?: boolean;
  
  /**
   * CSS类名
   */
  className?: string;
   
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
}

@Renderer({
  type: 'my-json-schema',
  name: 'my-json-schema'
})
export class MyNavRenderer extends React.Component<MyNavProps> {
  static defaultProps: Partial<MyNavProps> = {
    className: '',
    indentSize: 16,
    stacked: false,
    mode: 'inline',
    themeColor: 'light'
  };

  render() {
    const { render } = this.props;

    // 直接将所有属性传递给原生nav组件
    if (render) {
      return render('my-json-schema', {
        ...this.props,
        type: 'nav'
      } as any);
    }
    return <></>;
  }
}
