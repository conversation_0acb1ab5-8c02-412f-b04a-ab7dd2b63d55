import React, {FC, useContext} from 'react';
import {observer} from 'mobx-react';
import AMISRenderer from '@/component/AMISRenderer';
import {IMainStore} from '@/store';
import {MobXProviderContext} from 'mobx-react';
import './index.scss';

const BuildPermission: FC<any> = () => {
  function useStore(): IMainStore {
    const {store} = useContext(MobXProviderContext);
    return store as IMainStore;
  }

  const store = useStore();

  // 创建通用的CRUD配置生成函数
  const createCrudConfig = (title: string, type: number) => {
    return {
      type: 'crud',
      api: {
        url: `/admin-api/system/build-authorization/page?userId=${store.userInfo.id}&type=${type}`,
        method: 'get',
        // 数据处理适配器
        adaptor: (payload: any) => {
          if (payload.code === 0 && payload.data) {
            // 获取创建者ID，如果API没有直接提供isCreator字段
            const creatorId = store.userInfo.companyId; // 假设当前用户ID就是创建者ID

            return {
              total: payload.data.total,
              items: payload.data.list.map((item: any) => ({
                ...item,
                // 假设userId为1的是创建者，或者根据其他规则判断
                isCreator: item.userId == creatorId
              }))
            };
          }
          return {
            total: 0,
            items: []
          };
        }
      },
      headerToolbar: [
        {
          type: 'button',
          label: `添加${title}`,
          icon: 'fa fa-plus',
          level: 'primary',
          actionType: 'dialog',
          dialog: {
            title: '',
            size: 'md',
            body: {
              type: 'form',
              api: {
                url: '/admin-api/system/build-authorization/batch-create',
                method: 'post',
                data: {
                  '&': '$$',
                  type: type
                }
              },
              body: [
                {
                  label: '',
                  type: 'transfer',
                  name: 'userIds',
                  selectMode: 'tree',
                  resultListModeFollowSelect: false,
                  onlyChildren: true,
                  autoCheckChildren: true,
                  source: '/admin-api/system/user/list-dept-user',
                  labelField: 'name',
                  valueField: 'id',
                  searchable: true,
                  statistics: true,
                  required: true,
                  placeholder: '请选择需要授权的成员'
                }
              ]
            }
          }
        },
        {
          type: 'tpl',
          tpl: '成员（${total}）',
          className: 'auth-count-label'
        }
      ],
      columns: [
        {
          name: 'username',
          label: '姓名',
          type: 'tpl',
          tpl: '${username} ${isCreator ? "<span class=\\"org-creator\\">组织创建者</span>" : ""}',
        },
        {
          name: 'nickname',
          label: '昵称',
        },
        {
          name: 'deptName',
          label: '部门',
        },
        {
          name: 'status',
          label: '账号状态',
          type: 'mapping',
          map: {
            '1': '<span class="badge-status badge-success">已启用</span>',
            '2': '<span class="badge-status badge-danger">已停用</span>'
          }
        },
        {
          name: 'updater',
          label: '编辑人',
        },
        {
          name: 'updateTime',
          label: '编辑时间',
          type: 'input-datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          static: true,
        },
        {
          name: 'creator',
          label: '创建人',
        },
        {
          name: 'createTime',
          label: '创建时间',
          type: 'input-datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          static: true,
        },
        {
          type: 'operation',
          label: '操作',
          buttons: [
            {
              type: 'dropdown-button',
              icon: 'fa fa-ellipsis-h',
              hideCaret: true,
              visibleOn: '${!isCreator}', // 如果是组织创建者则不显示操作按钮
              buttons: [
                {
                  type: 'button',
                  label: "${status == 1 ? '停用账号' : '启用账号'}",
                  confirmTitle:
                    "${status == 1 ? '确定要停用吗？' : '确定要启用吗？'}",
                  confirmText: "${status == 1 ? '停用后，该账号将' : '启用后，该账号'}" + 
                    (type == 1 ? 
                      "${status == 1 ? '不能访问搭建管理后台，确定要停用吗？' : '可以访问搭建管理后台，确定要启用吗？'}" : 
                      "${status == 1 ? '不能创建团队、创建项目、创建应用，确定要停用吗？' : '可以创建团队、创建项目、创建应用，确定要启用吗？'}")
                    ,
                  actionType: 'ajax',
                  api: {
                    method: 'PUT',
                    url: '/admin-api/system/build-authorization/update',
                    data: {
                      id: '${id}',
                      userId: '${userId}',
                      type: '${type}',
                      status: "${status == 1 ? '2' : '1'}"
                    }
                  }
                },
                {
                  type: 'button',
                  label: '移除成员',
                  className: 'text-danger',
                  confirmTitle: '确定要移除吗？',
                  confirmText: '移除后，该账号将' + 
                    (type == 1 ? 
                      '不能访问搭建管理后台，确定要移除吗？' : 
                      '不能创建团队、创建项目、创建应用，确定要移除吗？')
                    ,
                  actionType: 'ajax',
                  api: 'DELETE:/admin-api/system/build-authorization/delete?id=${id}'
                }
              ]
            }
          ]
        }
      ],
      bulkActions: [],
      itemActions: [],
      footerToolbar: ['statistics', 'pagination', 'switch-per-page'],
      autoGenerateFilter: false,
      syncLocation: false,
      affixHeader: true,
      initFetch: true,
      className: 'build-permission-crud-table'
    };
  };

  // 组织管理员权限管理Schema
  const buildPermissionSchema = {
    type: 'page',
    body: [
      {
        type: 'html',
        html: '<div class="info-alert">组织管理员 具有组织后台的所有管理权限。</div>'
      },
      createCrudConfig('组织管理员', 1)
    ]
  };

  return (
    <div className="build-permission-container">
      <div className="page-header">
        <div className="title">组织管理员</div>
        <div className="subtitle">组织管理员的管理和维护</div>
      </div>

      <AMISRenderer
        schema={buildPermissionSchema}
        onAction={(type: string, data: any) => {
          console.log(type, data);
        }}
      />
    </div>
  );
};

export default observer(BuildPermission);
