import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import CarouselDataInteraction from './CarouselDataInteraction';
import {getFormFieldValuePage} from '@/utils/api/api';
// 轮播图选项类型
export interface CarouselOption {
  image: string;
  title?: string;
  href?: string;
}

export class CarouselPlugin extends BasePlugin {
  static id = 'CarouselPlugin';
  static scene = ['layout'];
  // 缓存数据选项
  private _dataOptions: any[] = [];
  private _dataFieldOptions: any[] = [];
  private selectPageId = '';

  // 关联渲染器名字
  rendererName = 'my-carousel';
  $schema = '/schemas/CarouselSchema.json';

  // 组件基本信息
  name = '轮播图';
  panelTitle = '轮播图';
  icon = 'fa fa-image';
  panelIcon = 'fa fa-image';
  pluginIcon = 'icon-carousel-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '轮播图组件';
  docLink = '/amis/zh-CN/components/carousel';
  tags = ['门户'];

  // 临时保存的数据，用于恢复状态
  private savedOptions: CarouselOption[] = [];

  // 组件默认配置
  scaffold = {
    type: 'my-carousel',
    dataSource: 'static', // 默认为静态数据源
    options: [
      {
        image: '',
        title: '标题1'
      }
    ],
    auto: true,
    interval: 3000,
    duration: 500,
    width: '100%',
    height: 300,
    controls: ['dots', 'arrows'],
    controlsTheme: 'light',
    animation: 'fade'
  };

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 添加动态数据加载方法
  fetchDynamicData = (context: any, schema: any) => {
    // 检查是否配置了必要的字段
    // if (schema.dataSource !== 'dynamic' || !schema.imageFieldName) {
    //   console.warn('动态数据源配置不完整，无法加载数据');
    //   return;
    // }

    // 获取显示条数，默认为5
    const pageSize = schema.displayCount || 5;

    let data = {
      applicationPageId: this.selectPageId,
      pageNo: 1,
      pageSize: pageSize
    };
    getFormFieldValuePage(data).then((res: any) => {
      if (res.code === 0) {
        // 根据配置的字段生成轮播项
        const carouselItems = this.generateCarouselItems(
          res.data.list,
          schema.imageFieldName,
          schema.hrefFieldName
        );

        console.log('生成的轮播项:', carouselItems);

        // 更新schema中的options
        // if (carouselItems.length > 0) {
        // const newSchema = {
        //   ...schema,
        //   options: carouselItems
        // };

        // 保存到插件实例中，避免状态丢失
        this.savedOptions = JSON.parse(JSON.stringify(carouselItems));

        // 安全更新schema
        // this.safeChangeValue(context, newSchema);
        const newSchema = {
          // ...schema,
          options: carouselItems
        };
        // 安全更新schema
        this.safeChangeValue(context, newSchema);
        // 更新schema
        // if (this.manager && this.manager.store) {
        //   this.manager.store.changeValueById(
        //     this.manager.store.activeId,
        //     newSchema
        //   );
        // }
        // }
      }
    });
  };

  // 根据API返回的数据生成轮播项
  generateCarouselItems = (
    dataList: any[],
    imageFieldName: string,
    hrefFieldName: string
  ): CarouselOption[] => {
    if (!Array.isArray(dataList) || dataList.length === 0) {
      return [];
    }

    // 遍历数据列表，生成轮播项
    return dataList.map(item => {
      // 创建轮播项
      return {
        image: item[imageFieldName] || '',
        // title: `轮播项 ${index + 1}`,
        title: '',
        href: hrefFieldName ? item[hrefFieldName] : '',
        linkType: 'external',
        internalPage: '',
        internalName: ''
      };
    });
  };

  // 安全更新schema
  safeChangeValue(context: any, newSchema: any) {
    try {
      if (
        context &&
        context.node &&
        !context.node.disposedNow &&
        this.manager &&
        this.manager.store &&
        typeof this.manager.store.changeValueById === 'function'
      ) {
        console.warn('安全更新schemacg:');
        this.manager.store.changeValueById(context.node.id, newSchema);
        return true;
      }
    } catch (error) {
      console.warn('安全更新schema失败:', error);
    }
    return false;
  }

  // 安全获取选项
  getSafeOptions(form: any): CarouselOption[] {
    try {
      return (form && form.data && form.data.options) || [];
    } catch (error) {
      console.warn('获取options失败:', error);
      return [];
    }
  }

  // 安全设置表单值
  safeSetFormValue(form: any, name: string, value: any): boolean {
    try {
      if (form && typeof form.setValueByName === 'function') {
        form.setValueByName(name, value);
        return true;
      }
    } catch (error) {
      console.warn(`设置表单值 ${name} 失败:`, error);
    }
    return false;
  }

  // 面板配置
  panelBodyCreator = (context: any) => {
    // 从URL中提取应用ID
    let applicationId = ''; // 默认值
    let applicationPageId = ''; // 默认值
    try {
      // 获取URL路径
      const pathname = window.location.hash;
      // 方法1：正则表达式匹配portletEditor后面的数字
      const matches = pathname.match(/\/portletEditor(\d+)/);
      if (matches && matches[1]) {
        applicationId = matches[1];
      }
      // 方法1：正则表达式匹配portletEditor后面的数字
      const pageMatches = pathname.match(/\/editCode=(\d+)/);
      if (pageMatches && pageMatches[1]) {
        applicationPageId = pageMatches[1];
      }
    } catch (e) {
      console.error('提取应用ID时出错:', e);
    }

    // 在面板创建时检查是否需要初始化关联数据表
    const initAssociatedDataTable = () => {
      try {
        // 如果已经有contentSource但没有__associatedDataTable
        if (
          context.schema.contentSource &&
          !context.schema.__associatedDataTable &&
          this._dataOptions &&
          this._dataOptions.length
        ) {
          // 查找匹配的数据源
          const selectedOption = this._dataOptions.find(
            item => item.value == context.schema.contentSource
          );
          this.selectPageId = selectedOption.associatedDataTable;

          setTimeout(() => {
            const selectedImageOption = this._dataFieldOptions.find(
              item => item.value == context.schema.imageField
            );

            const selectedLinkOption = this._dataFieldOptions.find(
              item => item.value == context.schema.hrefField
            );

            console.log('this._dataFieldOptions:', this._dataFieldOptions);
            console.log('this.selectedLinkOption:', selectedLinkOption);

            if (
              selectedOption &&
              selectedOption.associatedDataTable &&
              selectedLinkOption &&
              selectedImageOption
            ) {
              // 更新schema
              const newSchema = {
                ...context.schema,
                __associatedDataTable: selectedOption.associatedDataTable,
                hrefFieldName: selectedLinkOption.name,
                imageFieldName: selectedImageOption.name
              };
              context.schema.__associatedDataTable =
                selectedOption.associatedDataTable;
              context.schema.hrefFieldName = selectedLinkOption.name;
              context.schema.imageFieldName = selectedImageOption.name;
              // 安全更新schema
              this.safeChangeValue(context, newSchema);
              return true;
            } else {
              return false;
            }
          }, 2000); // 延迟1秒后尝试再次初始化

          if (selectedOption && selectedOption.associatedDataTable) {
            // 更新schema
            const newSchema = {
              ...context.schema,
              __associatedDataTable: selectedOption.associatedDataTable
            };
            context.schema.__associatedDataTable =
              selectedOption.associatedDataTable;
            // 安全更新schema
            this.safeChangeValue(context, newSchema);
            return true;
          }
        }
      } catch (error) {
        console.error('初始化关联数据表出错:', error);
      }
      return false;
    };

    const handleOptions = (options: CarouselOption[]) => {
      // 如果options无效则停止处理
      if (!Array.isArray(options)) {
        console.warn('handleOptions收到无效数据:', options);
        return;
      }

      try {
        // 保存到插件实例中，避免状态丢失
        this.savedOptions = JSON.parse(JSON.stringify(options));

        // 创建新的schema
        const newSchema = {
          // ...context.schema,
          options: options
        };

        // 安全更新schema
        this.safeChangeValue(context, newSchema);
      } catch (error) {
        console.error('处理选项时出错:', error);
      }
    };

    // 直接返回schema，不使用错误边界包装
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '轮播内容',
              body: [
                {
                  type: 'radios',
                  name: 'dataSource',
                  label: '数据来源',
                  options: [
                    {
                      label: '动态数据源',
                      value: 'dynamic'
                    },
                    {
                      label: '静态数据源',
                      value: 'static'
                    }
                  ],
                  value: 'static',
                  onChange: (
                    value: any,
                    oldValue: any,
                    model: any,
                    form: any
                  ) => {
                    // 切换数据源类型时的处理逻辑
                    if (value === 'static' && oldValue === 'dynamic') {
                      try {
                        // 从动态切换到静态，清空动态相关配置
                        // this.safeSetFormValue(form, 'displayCount', context.schema.displayCount||5);
                        // this.safeSetFormValue(form, 'contentSource', context.schema.contentSource||'');
                        // this.safeSetFormValue(form, 'imageField', context.schema.imageField||'');
                        // this.safeSetFormValue(
                        //   form,
                        //   'imageFieldName',
                        //   context.schema.imageFieldName||''
                        // );
                        // this.safeSetFormValue(form, 'titleField', context.schema.titleField||'');
                        // this.safeSetFormValue(form, 'hrefField', context.schema.hrefField||'');
                        // this.safeSetFormValue(form, 'hrefFieldName', context.schema.hrefFieldName||'');

                        // this.safeSetFormValue(form, 'displayCount', 5);
                        // this.safeSetFormValue(form, 'contentSource', '');
                        // this.safeSetFormValue(form, 'imageField', '');
                        // this.safeSetFormValue(
                        //   form,
                        //   'imageFieldName',
                        //   ''
                        // );
                        // this.safeSetFormValue(form, 'titleField', '');
                        // this.safeSetFormValue(form, 'hrefField', '');
                        // this.safeSetFormValue(form, 'hrefFieldName', '');
                        // this.safeSetFormValue(form, 'options', []);

                        // // 恢复之前保存的静态选项数据
                        let optionsToRestore: CarouselOption[] = [];

                        // 先尝试从form中获取
                        const savedFormOptions =
                          form && form.data && form.data.__savedStaticOptions;
                        if (
                          Array.isArray(savedFormOptions) &&
                          savedFormOptions.length
                        ) {
                          optionsToRestore = JSON.parse(
                            JSON.stringify(savedFormOptions)
                          );
                          console.log(
                            '从form中恢复静态选项数据:',
                            optionsToRestore
                          );
                        }

                        if (optionsToRestore.length) {
                          // 先在表单中设置
                          this.safeSetFormValue(
                            form,
                            'options',
                            optionsToRestore
                          );

                          context.schema.options = optionsToRestore;

                          setTimeout(() => {
                            // 然后尝试更新schema
                            if (context && context.schema) {
                              console.log('尝试更新schema');
                              const newSchema = {
                                ...context.schema,
                                dataSource: 'static', // 切换到静态数据源
                                options: optionsToRestore,
                                __savedStaticOptions: undefined // 清除保存的数据，避免重复使用
                              };

                              this.safeChangeValue(context, newSchema);
                            }
                          }, 1000);
                        }
                      } catch (error) {
                        console.error('从动态切换到静态时出错:', error);
                      }
                    } else if (value === 'dynamic' && oldValue === 'static') {
                      try {
                        // 从静态切换到动态，保存静态数据作为备用
                        const currentOptions = this.getSafeOptions(form);
                        if (currentOptions.length) {
                          // 保存到两个地方以增加冗余
                          const optionsCopy = JSON.parse(
                            JSON.stringify(currentOptions)
                          );

                          // 保存到表单中
                          this.safeSetFormValue(form, 'options', optionsCopy);

                          // 同时保存到插件实例中
                          this.savedOptions = optionsCopy;

                          console.log('已保存静态选项数据:', optionsCopy);
                        }

                        setTimeout(() => {
                          // 然后尝试更新schema
                          if (context && context.schema) {
                            console.log('尝试更新schema');
                            const newSchema = {
                              ...context.schema,
                              dataSource: 'dynamic'
                            };

                            this.safeChangeValue(context, newSchema);
                          }
                        }, 1000);
                      } catch (error) {
                        console.error('从静态切换到动态时出错:', error);
                      }
                    }
                  }
                },
                {
                  type: 'container',
                  visibleOn: "this.dataSource === 'static'",
                  body: [
                    // 直接使用组件，不包装在错误边界中
                    <CarouselDataInteraction
                      appId={applicationId}
                      context={context}
                      options={context.schema.options || []}
                      handleOptions={handleOptions}
                    />
                  ]
                },
                {
                  type: 'container',
                  visibleOn: "this.dataSource === 'dynamic'",
                  body: [
                    {
                      type: 'select',
                      name: 'contentSource',
                      label: '内容来源',
                      value: '',
                      source: {
                        method: 'GET',
                        url: `/admin-api/system/data-set/page?applicationId=${applicationId}&applicantOrBackend=1&pageNo=1&pageSize=100`,
                        adaptor: (payload: any) => {
                          if (!payload.data || !payload.data.list) {
                            return {
                              data: {
                                options: []
                              },
                              status: 0,
                              msg: ''
                            };
                          }

                          const options = payload.data.list.map(
                            (item: any) => ({
                              label: item.name,
                              value: item.id,
                              associatedDataTable: item.associatedDataTable
                            })
                          );

                          // 缓存options以便在onChange中使用
                          this._dataOptions = options;
                          // 数据加载完成后尝试初始化关联数据表
                          setTimeout(() => {
                            initAssociatedDataTable();
                          }, 100);
                          return {
                            ...payload,
                            data: {
                              options: options
                            }
                          };
                        }
                      },
                      description: '选择轮播图内容来源',
                      onChange: (
                        value: any,
                        oldValue: any,
                        model: any,
                        form: any
                      ) => {
                        if (!value) return;

                        // 查找选中的数据源项
                        let selectedOption = this._dataOptions.find(
                          item => item.value == value
                        );
                        if (
                          selectedOption &&
                          selectedOption.associatedDataTable
                        ) {
                          // 获取当前schema
                          const schema = context.schema;
                          schema.__associatedDataTable = value;
                          this.selectPageId =
                            selectedOption.associatedDataTable;
                          // 保存关联的数据表ID
                          form.setValueByName(
                            '__associatedDataTable',
                            selectedOption.associatedDataTable
                          );

                          // 清空已选择的字段，避免字段不匹配
                          form.setValueByName('imageFieldName', '');
                          form.setValueByName('hrefFieldName', '');
                        }
                      }
                    },
                    {
                      type: 'select',
                      name: 'imageField',
                      label: '轮播图',
                      value: '',
                      source: {
                        method: 'GET',
                        url: "${__associatedDataTable ? `/admin-api/system/form-field/page?applicationPageId=${__associatedDataTable}&pageNo=1&pageSize=100` : ''}",
                        adaptor: (payload: any) => {
                          if (!payload.data || !payload.data.list) {
                            return {
                              data: {
                                options: []
                              },
                              status: 0,
                              msg: ''
                            };
                          }
                          const options = payload.data.list.map(
                            (item: any) => ({
                              label: item.label,
                              name: item.name,
                              value: item.id
                            })
                          );
                          this._dataFieldOptions = options;
                          return {
                            ...payload,
                            data: {
                              options: options
                            }
                          };
                        }
                      },
                      description: '选择轮播图字段',
                      visibleOn: 'this.__associatedDataTable',
                      onChange: (
                        value: any,
                        oldValue: any,
                        model: any,
                        form: any
                      ) => {
                        if (!value) return;

                        let selectedOption = this._dataFieldOptions.find(
                          item => item.value == value
                        );

                        console.log('设置轮播图');
                        // 获取当前schema
                        const schema = context.schema;
                        schema.imageFieldName = selectedOption.name;

                        // 自动加载数据
                        setTimeout(() => {
                          this.fetchDynamicData(context, schema);
                        }, 100);
                      }
                    },
                    {
                      type: 'select',
                      name: 'hrefField',
                      label: '链接地址',
                      value: '',
                      source: {
                        method: 'GET',
                        url: "${__associatedDataTable ? `/admin-api/system/form-field/page?applicationPageId=${__associatedDataTable}&pageNo=1&pageSize=100` : ''}",
                        adaptor: (payload: any) => {
                          if (!payload.data || !payload.data.list) {
                            return {
                              data: {
                                options: []
                              },
                              status: 0,
                              msg: ''
                            };
                          }
                          const options = payload.data.list.map(
                            (item: any) => ({
                              label: item.label,
                              name: item.name,
                              value: item.id
                            })
                          );
                          this._dataFieldOptions = options;
                          return {
                            ...payload,
                            data: {
                              options: options
                            }
                          };
                        }
                      },
                      description: '选择链接地址字段',
                      visibleOn: 'this.__associatedDataTable',
                      onChange: (
                        value: any,
                        oldValue: any,
                        model: any,
                        form: any
                      ) => {
                        if (!value) return;
                        let selectedOption = this._dataFieldOptions.find(
                          item => item.value == value
                        );

                        console.log('设置链接');
                        // 获取当前schema
                        const schema = context.schema;
                        schema.hrefFieldName = selectedOption.name;

                        // 如果已经有imageField，则重新加载数据
                        if (schema.imageField) {
                          setTimeout(() => {
                            this.fetchDynamicData(context, schema);
                          }, 100);
                        }
                      }
                    },
                    {
                      type: 'input-number',
                      name: 'displayCount',
                      label: '显示条数',
                      value: 5,
                      min: 1,
                      max: 100,
                      description: '设置轮播图显示的数据条数',
                      onChange: (
                        value: any,
                        oldValue: any,
                        model: any,
                        form: any
                      ) => {
                        if (!value) return;
                        const schema = context.schema;
                        schema.displayCount = value;
                        // 如果已经有imageField，则重新加载数据
                        if (schema.imageField) {
                          setTimeout(() => {
                            this.fetchDynamicData(context, schema);
                          }, 100);
                        }
                      }
                    },
                    {
                      type: 'hidden',
                      name: '__associatedDataTable'
                    }
                  ]
                }
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false,
              readonly: true
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                getSchemaTpl('layout:originPosition', {
                  value: 'left-top'
                }),
                getSchemaTpl('switch', {
                  name: 'auto',
                  label: '自动轮播',
                  value: true
                }),
                getSchemaTpl('maxLength', {
                  name: 'interval',
                  label: '动画间隔(ms)',
                  value: 3000,
                  min: 1000,
                  step: 100,
                  unit: 'ms',
                  visibleOn: 'this.auto'
                }),
                getSchemaTpl('maxLength', {
                  name: 'duration',
                  label: '动画时长(ms)',
                  value: 500,
                  step: 100,
                  unit: 'ms',
                  description: '动画持续时间，单位为毫秒'
                }),
                {
                  type: 'select',
                  name: 'animation',
                  label: '动画效果',
                  options: [
                    {
                      label: '淡入淡出',
                      value: 'fade'
                    },
                    {
                      label: '滑动',
                      value: 'slide'
                    },
                    {
                      label: '跑马灯',
                      value: 'marquee'
                    }
                  ],
                  value: 'fade'
                },
                {
                  type: 'select',
                  name: 'direction',
                  label: '轮播方向',
                  options: [
                    {
                      label: '水平',
                      value: 'horizontal'
                    },
                    {
                      label: '垂直',
                      value: 'vertical'
                    }
                  ],
                  value: 'horizontal'
                },
                getSchemaTpl('switch', {
                  name: 'loop',
                  label: '循环播放',
                  value: true
                })
                // {
                //   type: 'switch',
                //   name: 'mouseEvent',
                //   label: '鼠标事件',
                //   value: true,
                //   description: '是否支持鼠标事件'
                // }
              ]
            },
            {
              title: '显示设置',
              body: [
                // getSchemaTpl('layout:width:v2', {
                //   name: 'width',
                //   label: '宽度',
                //   value: '100%'
                // }),
                // getSchemaTpl('layout:width:v2', {
                //   name: 'height',
                //   label: '高度',
                //   value: '300'
                // })
                getSchemaTpl('style:widthHeight')
              ]
            },
            {
              title: '控件设置',
              body: [
                getSchemaTpl('layout:sorption', {
                  name: 'controlsTheme',
                  label: '控制按钮主题',
                  options: [
                    {
                      label: 'light',
                      value: 'light'
                    },
                    {
                      label: 'dark',
                      value: 'dark'
                    }
                  ],
                  value: 'light'
                }),
                getSchemaTpl('layout:sorption', {
                  name: 'controls',
                  label: '控制显示',
                  multiple: true,
                  options: [
                    {
                      label: '底部圆点',
                      value: 'dots'
                    },
                    {
                      label: '左右箭头',
                      value: 'arrows'
                    }
                  ],
                  value: ['dots', 'arrows'],
                  onChange: (
                    value: any,
                    oldValue: any,
                    model: any,
                    form: any
                  ) => {
                    // 检查是否取消了箭头选项
                    if (
                      oldValue &&
                      oldValue.includes('arrows') &&
                      (!value || !value.includes('arrows'))
                    ) {
                      // 如果取消了箭头选项，则将 alwaysShowArrow 设置为 false
                      form.setValueByName('alwaysShowArrow', false);
                    }

                    // form.setValueByName('controls', []);
                    // 确保value不为undefined，如果用户取消了所有选项，设置为空数组
                    if (!value) {
                      form.setValueByName('controls', []);
                    }
                  }
                }),
                getSchemaTpl('switch', {
                  name: 'alwaysShowArrow',
                  label: '始终显示箭头',
                  value: false,
                  visibleOn:
                    'this.controls && this.controls.indexOf("arrows") > -1'
                }),
                {
                  type: 'select',
                  name: 'thumbMode',
                  label: '图片展示模式',
                  options: [
                    {
                      label: '包含',
                      value: 'contain'
                    },
                    {
                      label: '覆盖',
                      value: 'cover'
                    }
                  ],
                  value: 'contain'
                },
                // 添加多图模式设置
                getSchemaTpl('switch', {
                  type: 'switch',
                  name: 'multiple.enable',
                  label: '启用多图模式',
                  value: false,
                  onChange: (
                    value: boolean,
                    oldValue: boolean,
                    model: any,
                    form: any
                  ) => {
                    if (!value) {
                      console.log('关闭多图模式');
                      form.setValueByName('multiple', undefined);
                      setTimeout(() => {
                        form.setValueByName('multiple.enable', false);
                      }, 0);
                      form.setValueByName('multiple.count', 1);
                    }
                  }
                }),
                getSchemaTpl('maxLength', {
                  name: 'multiple.count',
                  label: '每屏图片数',
                  value: 2,
                  min: 2,
                  max: 6,
                  visibleOn: 'this.multiple && this.multiple.enable === true',
                })
              ]
            },

            {
              title: '轮播图',
              body: [
                getSchemaTpl('theme:border', {
                  name: 'themeCss.baseControlClassName.border:default',
                  label: '边框'
                })
              ]
            },
            {
              title: '圆角设置',
              body: [
                getSchemaTpl('theme:radius', {
                  name: 'themeCss.baseControlClassName.radius:default',
                  label: '圆角'
                })
              ]
            },
            {
              title: '内外边距',
              body: [
                getSchemaTpl('theme:paddingAndMargin', {
                  name: 'themeCss.baseControlClassName.padding-and-margin:default',
                  label: '内外边距'
                })
              ]
            },
            {
              title: '背景与阴影',
              body: [
                getSchemaTpl('theme:colorPicker', {
                  name: 'themeCss.baseControlClassName.background:default',
                  label: '背景颜色'
                }),
                getSchemaTpl('theme:shadow', {
                  name: 'themeCss.baseControlClassName.boxShadow:default'
                })
              ]
            },
            {
              title: '其他',
              body: [
                {
                  type: 'icon-selector',
                  name: 'themeCss.baseControlClassName.--image-images-prev-icon',
                  label: '左切换图标',
                  description: '左切换按钮的SVG图标'
                },
                {
                  type: 'icon-selector',
                  name: 'themeCss.baseControlClassName.--image-images-next-icon',
                  label: '右切换图标',
                  description: '右切换按钮的SVG图标'
                },
                getSchemaTpl('layout:width:v2', {
                  name: 'themeCss.galleryControlClassName.width:default',
                  label: '切换图标大小',
                  value: '30',
                  unitOptions: ['px', 'rem']
                })
              ]
            },
            {
              title: '自定义样式',
              body: [
                getSchemaTpl('menuTpl', {
                  name: 'style',
                })
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      }
    ]);
  };
}

registerEditorPlugin(CarouselPlugin);
