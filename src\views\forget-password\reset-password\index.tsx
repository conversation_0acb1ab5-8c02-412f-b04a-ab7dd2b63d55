import React, {useState} from 'react';
import {RouteComponentProps} from 'react-router-dom';
import {Button, Input, toast} from 'amis-ui';
import './index.scss';

export default function ResetPassword({
  history,
  location
}: RouteComponentProps) {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const urlParams = new URLSearchParams(location.search);
  const username = urlParams.get('username');


  return (
    <div className="reset-password-page">
      <div className="form-content">
        <div className="page-title">找回密码</div>
        <div className="form-item">
          <div className="password-input">
            <Input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={e => setPassword(e.target.value)}
              placeholder="新密码"
              className="form-control"
              maxLength={32}
            />
            <i 
              className={`fa fa-${showPassword ? 'eye' : 'eye-slash'}`}
              onClick={() => setShowPassword(!showPassword)}
            />
          </div>
        </div>
        <div className="form-item">
          <div className="password-input">
            <Input
              maxLength={32}
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPassword}
              onChange={e => setConfirmPassword(e.target.value)}
              placeholder="再次输入新密码"
              className="form-control"
            />
            <i 
              className={`fa fa-${showConfirmPassword ? 'eye' : 'eye-slash'}`}
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            />
          </div>
        </div>

        <Button
          type="submit"
          level="primary"
          className="confirm-btn"
          block
          onClick={() => {
            if (!password || !confirmPassword) {
              toast.error('请输入新密码');
              return;
            }
            if (password !== confirmPassword) {
              toast.error('两次输入的密码不一致');
              return;
            }
            // TODO: 调用重置密码接口
            history.push('/forget-password/reset-success');
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );
} 