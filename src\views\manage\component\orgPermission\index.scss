.build-permission-container {
  position: relative;
  width: calc(100% - 1.5rem);
  height: calc(100vh - 4.625rem);
  margin: 0.75rem;
  overflow: auto;

  /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
  -webkit-scrollbar {
    display: none;
  }
  /* 隐藏Firefox浏览器的滚动条 */
  scrollbar-width: none;

  .page-header {
    margin-bottom: 24px;

    .title {
      font-size: 20px;
      font-weight: bold;
      color: var(--text-color);
    }

    .subtitle {
      margin-top: 4px;
      color: var(--text--muted-color);
      font-size: 14px;
    }
  }

  .info-alert {
    background-color: #f0f6ff;
    border-radius: 4px;
    padding: 8px 16px;
    margin-bottom: 20px;
    margin-top: 20px;
    color: #333;
    font-size: 12px;
    display: flex;
    align-items: center;
  }

  .info-alert::before {
    content: 'i';
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 18px;
    height: 18px;
    background-color: #2468f2;
    color: white;
    border-radius: 50%;
    margin-right: 8px;
    font-style: italic;
    font-weight: bold;
    font-size: 12px;
  }

  .auth-count-label {
    margin-left: 16px;
    color: #666;
  }
}

.status-green {
  color: #52c41a;
}

.status-red {
  color: #ff4d4f;
}

.build-permission-crud-table {
  .cxd-Table-table {
    th {
      background-color: #f7f7f7;
    }
  }
}

// 全局样式覆盖
:global {
  .tabs-wrapper {
    .cxd-Tabs-links {
      border-bottom: 1px solid var(--borderColor);
      margin-bottom: 16px;
    }

    .cxd-Tabs-link {
      padding: 12px 16px;
      font-size: 14px;
      cursor: pointer;
      color: var(--text--muted-color);
      position: relative;

      &.is-active {
        color: var(--primary);
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: var(--primary);
        }
      }
    }
  }

  .info-tip {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: rgba(var(--primary-rgb), 0.1);
    border-radius: 4px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-size: 14px;
  }

  .build-permission-crud-table {
    background-color: var(--light-bg);
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-top: 16px;

    .cxd-Table-thead > tr > th {
      background-color: var(--light-bg);
      color: var(--text--muted-color);
      font-weight: normal;
    }

    .cxd-Table-tbody > tr > td {
      color: var(--text-color);
    }

    .cxd-Table-tbody > tr:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .cxd-Button--primary {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px;
      height: 36px;

      .fa {
        margin-right: 4px;
      }
    }

    .cxd-Operation-buttons {
      .cxd-Button {
        color: var(--text--muted-color);

        &:hover {
          color: var(--text-color);
        }
      }
    }

    .cxd-Pagination {
      justify-content: center;
    }
  }
}

.dark-Tabs-pane,
.cxd-Tabs-pane {
  padding: 0;
}

.badge-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
}

.badge-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.badge-danger {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.org-creator {
  color: #2468f2;
  font-size: 12px;
  margin-left: 8px;
  border-radius: 2px;
  background: #e8f3ff;
  padding: 4px 8px;
}
