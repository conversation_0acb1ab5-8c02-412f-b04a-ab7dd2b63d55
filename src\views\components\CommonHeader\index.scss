/* 通用头部样式 */
.common-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 50px;
  background-color: var(--background);
  border-bottom: 1px solid var(--border-bottom-line-color);
  color: var(--text-color);
  width: 100%;
  position: relative;
  z-index: 10;

  /* 中间导航区域 */
  .header-middle {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 20px;

    &-nav {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-bottom: 8px;

      &-item {
        padding: 6px 16px;
        font-size: 14px;
        color: var(--text-color);
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          background-color: var(--light-bg);
        }

        &.active {
          color: var(--primary);
          background-color: var(--primary-bg);

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background-color: var(--primary);
            border-radius: 1px;
          }
        }
      }
    }

    &-tools {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 12px;

      &-left {
        color: var(--text-color);
        font-weight: 500;
      }

      &-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      &-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background-color: var(--light-bg);
        }

        i {
          font-size: 12px;
          color: var(--text--muted-color);
          transition: all 0.3s ease;

          &.collapsed {
            transform: rotate(180deg);
          }
        }

        &:hover i {
          color: var(--primary);
        }
      }
    }
  }

  .padding-left-20 {
    padding-left: 20px;
  }

  /* 头部左侧部分 */
  .header-left {
    width: 200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    position: relative;

    .btn-group {
      button {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: var(--text-color);
        padding: 0;

        img {
          width: 24px;
          height: 24px;
          margin-right: 10px;
        }
      }
    }

    &-content {
      display: flex;
      align-items: center;
      margin-right: 4px;

      &-nickname {
        margin-left: 8px;
        font-size: 14px;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    &-right {
      i {
        font-size: 12px;
        color: var(--text--muted-color);
        margin-left: 4px;
      }
    }

    /* 应用信息部分样式 */
    &-jg {
      width: 1px;
      height: 20px;
      background-color: rgba(0, 0, 0, 0.1);
      margin: 0 12px;
    }

    &-appname {
      display: flex;
      align-items: center;
      min-width: 300px;

      .app-logo-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        margin-right: 10px;
        flex-shrink: 0;
      }

      &-icon {
        font-size: 18px;
        color: var(--text-color);
        margin-right: 8px;
      }

      &-name {
        font-size: 15px;
        font-weight: 500;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }

      &-iconDown {
        font-size: 12px;
        color: var(--text--muted-color);
        margin-left: 6px;
      }
    }
  }

  /* 头部右侧部分 */
  .header-right {
    display: flex;
    align-items: center;

    &-switchViewsBtn {
      display: flex;
      align-items: center;
      margin-right: 16px;
      background-color: transparent;
      border: 1px solid var(--border-bottom-line-color);
      color: var(--text-color);
      border-radius: 4px;
      padding: 4px 12px;
      font-size: 14px;
      transition: all 0.3s;

      img {
        width: 20px;
        height: 20px;
      }

      span {
        margin-left: 10px;
      }

      &:hover {
        background-color: rgba(var(--primary), 0.1);
      }
    }

    .settings-button {
      background-color: transparent;
      border: 1px solid var(--text--muted-color);
      color: var(--text-color);
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(var(--primary), 0.1);
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s;

      .business-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: var(--primary);
        color: white;
        font-size: 14px;
        font-weight: 600;
      }

      span {
        margin-left: 8px;
        font-size: 14px;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  /* 输入框样式 */
  .pagesNav-classification-input {
    border: 1px solid var(--primary);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px;
    width: 200px;
    outline: none;

    &:focus {
      box-shadow: 0 0 0 2px rgba(var(--primary), 0.2);
    }
  }
}

/* 企业下拉菜单样式 */
.tenant-dropdown-modal {
  position: fixed !important;
  top: 0 !important;
  left: 1rem !important;
  right: auto !important;



  .cxd-Modal-overlay,
  .dark-Modal-overlay {
    background-color: transparent !important;
  }

  .cxd-Modal-content,
  .dark-Modal-content {
    min-height: auto;
    padding: 0;
  }

  .tenant-dropdown-content {
    background: var(--light-bg);
    width: 220px;
  }

  .create-tenant-btn {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 10px;
    cursor: pointer;

    i {
      margin-right: 8px;
      color: var(--primary);
    }

    span {
      font-size: 14px;
    }
  }

  .tenant-list {
    max-height: 300px;
    overflow-y: auto;
    /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
    -webkit-scrollbar {
      display: none;
    }
    /* 隐藏Firefox浏览器的滚动条 */
    scrollbar-width: none;
    
    .tenant-item {
      display: flex;
      align-items: center;
      padding: 10px 12px;
      cursor: pointer;
      border-radius: 4px;
      position: relative;
      transition: all 0.3s;

      &.selected {
        background-color: rgba(var(--primary), 0.1);
      }

      .tenant-logo {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f2f5;
        margin-right: 12px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .tenant-logo-placeholder {
        width: 100%;
        height: 100%;
        background-color: #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .tenant-name {
        font-size: 14px;
        color: var(--text-color);
        flex: 1;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .selected-icon {
        position: absolute;
        right: 12px;
        color: var(--primary);
      }
    }
  }
}

// 用户信息弹窗样式
.user-profile-modal {
  position: fixed !important;
  top: -1rem !important;
  right: 1rem !important;
  left: auto !important;
  margin: 0 !important;

  .cxd-Modal-overlay,
  .dark-Modal-overlay {
    background-color: transparent !important;
  }

  .cxd-Modal-content,
  .dark-Modal-content {
    min-height: auto;
    padding: 0;
  }

  // 用户信息区域
  .flex.items-center {
    .font-bold {
      color: var(--text-color);
    }

    .text-muted {
      color: var(--text--muted-color);
    }
  }

  // 按钮样式
  .cxd-Button--link,
  .dark-Button--link {
    color: var(--text-color);
    display: flex;
    justify-content: flex-start;
    margin-left: 4px;

    i {
      color: var(--text-color);
      margin-right: 8px;
      transition: color 0.3s ease;
    }

    &:hover i {
      color: var(--primary);
    }
  }
}

/* 创建企业模态框样式 */
.create-tenant-modal {
  .cxd-Modal-content,
  .dark-Modal-content {
    min-height: auto;
    padding: 0;
  }
  .create-tenant-content {
    padding: 20px;
  }

  .create-tenant-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 16px;

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
    }

    .form-input {
      width: 100%;
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      font-size: 14px;
      transition: all 0.3s;

      &:focus {
        border-color: var(--primary);
        outline: none;
      }
    }
  }

  .logo-upload-area {
    .logo-upload-label {
      display: block;
      width: 140px;
      height: 82px;
      border: 1px dashed #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      margin-bottom: 8px;

      .uploaded-logo {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .upload-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;

        i {
          font-size: 20px;
          margin-bottom: 4px;
        }

        span {
          font-size: 12px;
        }
      }
    }

    .upload-hint {
      font-size: 12px;
      color: #909399;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;

    button {
      margin-left: 12px;
    }
  }
}
