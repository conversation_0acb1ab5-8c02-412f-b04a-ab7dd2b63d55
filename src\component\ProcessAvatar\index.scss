.head {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  margin-bottom: 6px;
}



.show-y {
  display: flex;
  flex-direction: column !important;
  justify-content: center;
  align-items: center;
  .name {
    margin-left: 0 !important;
  }
}

.a-img {
  display: flex;
  border-radius: 50%;
  flex-direction: column;
  justify-content: center;
  background: white;
  position: relative;
  width: fit-content;
  height: fit-content;
}

.a-img > div,
.a-img > img {
  display: inline-block;
  text-align: center;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  width: var(--size, 40px);
  height: var(--size, 40px);
  line-height: var(--size, 40px);
  font-size: 14px;
  object-fit: cover;
}

.a-img > img {
  background: #f0f0f0;
}

.close {
  position: absolute;
  top: 0;
  right: 0;
  color: white;
  cursor: pointer;
  border-radius: 50%;
  background: black;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  z-index: 2;
  transition: background 0.2s;
}
.close:hover {
  background: #ffeaea;
  color: #ff4d4f;
}

.name {
  text-align: center;
  color: var(--text-color);
  font-size: 14px;
  margin-left: 10px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status {
  position: absolute;
  bottom: -4px;
  right: -8px;
  border-radius: 50%;
  font-size: 15px;
  background: white;
  border: 2px solid white;
}

.error {
  color: #ff4d4f;
}
.leader {
  font-size: 12px;
  color: #faad14;
}
.pending {
  color: #faad14;
}
.success {
  color: #52c41a;
}
.transfer {
  color: white;
  background: #7a939d;
  font-size: 12px;
  padding: 1px;
}
.comment {
  color: #1677ff;
}
.cc {
  color: white;
  font-size: 12px;
  padding: 1px;
  background: #1677ff;
}
.cancel {
  color: #838383;
}
.recall {
  color: #f56c6c;
}