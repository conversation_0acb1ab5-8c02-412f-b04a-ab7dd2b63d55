import React, {FC, useRef, useContext, useState} from 'react';
import './index.scss';
import {toast, Avatar} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {IMainStore} from '@/store';
import { MobXProviderContext } from 'mobx-react';

import {javaApplicationUpdate, updateIcon} from '@/utils/api/api';

// 图片
import app_default_icon from '@/image/common_icons//app_default_icon.png';

import {EditEntryIcon} from '@/utils/schemaDataSet/EditEntryIcon';

// 引入修改应用名称弹窗
import {modifyApplicationNamePopup} from '@/views/applySettings/common/modifyApplicationNamePopup';

import {modifyApplicationDescPopup} from '@/views/applySettings/common/modifyApplicationDescPopup';

import { RendererEvent, IScopedContext } from 'amis';


const BasicSetting: FC<any> = (props: any) => {
  /* data 数据 */
  // 打开修改应用名称弹窗
  const [openApplicationNamePopup, setOpenApplicationNamePopup] = React.useState(false);
  // 打开修改应用描述弹窗
  const [openApplicationDescPopup, setOpenApplicationDescPopup] = React.useState(false);
  /* data 数据 end */
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [showEditIconDialog, setShowEditIconDialog] = useState(false);

  // 解析应用图标内容，提取SVG、颜色和背景色
  const parseAppIcon = (iconHtml: string) => {
    if (!iconHtml) {
      return {
        icon: '',
        iconColor: '#000000',
        iconBg: '#F5F7FA'
      };
    }

    // 如果是完整的span元素，需要解析其中的内容
    if (iconHtml.includes('<span')) {
      // 提取style属性中的颜色和背景色
      const colorMatch = iconHtml.match(/color:\s*([^;]+)/);
      const bgMatch = iconHtml.match(/background:\s*([^;]+)/);

      // 提取SVG内容或图标类名
      const svgMatch = iconHtml.match(/<svg[^>]*>.*?<\/svg>/);
      const iconMatch = iconHtml.match(/<i class="([^"]+)"/);

      return {
        icon: svgMatch ? svgMatch[0] : (iconMatch ? iconMatch[1] : ''),
        iconColor: colorMatch ? colorMatch[1] : '#000000',
        iconBg: bgMatch ? bgMatch[1] : '#F5F7FA'
      };
    }

    // 如果是纯SVG或图标类名，直接返回
    return {
      icon: iconHtml,
      iconColor: '#000000',
      iconBg: '#F5F7FA'
    };
  };

  // 处理图标保存
  const handleIconSave = (values: any) => {
    console.log('处理图标保存:', values);

    if (!values || !Array.isArray(values) || values.length === 0) {
      console.log('无效的数据格式，取消更新');
      return;
    }

    const iconData = values[0];
    if (!iconData) {
      console.log('无图标数据，取消更新');
      return;
    }

    // 清理图标数据，移除可能的引号
    let cleanIcon = iconData.icon || '';
    if (typeof cleanIcon === 'string') {
      cleanIcon = cleanIcon.replace(/^['"]|['"]$/g, '').trim();
    }

    // 使用新的图标数据
    const finalIconColor = iconData.iconColor || '#000000';
    const finalIconBg = iconData.iconBg || '#F5F7FA';

    // 构建新的图标HTML结构
    let newLogo = '';
    if (cleanIcon) {
      if (cleanIcon.includes('<svg')) {
        // SVG图标：构建完整的span结构
        newLogo = `<span style="color:${finalIconColor};background:${finalIconBg};width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:24px;vertical-align:middle">${cleanIcon}</span>`;
      } else {
        // 字体图标：构建完整的span结构
        newLogo = `<span style="color:${finalIconColor};background:${finalIconBg};width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:24px;vertical-align:middle"><i class="${cleanIcon}"></i></span>`;
      }
    }

    const data = {
      id: props.applyData.id,
      logo: newLogo
    };

    javaApplicationUpdate(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('修改成功');
        setShowEditIconDialog(false);

        // 更新父窗口的store（如果存在）
        if (window.parent && window.parent.storeInstance) {
          window.parent.storeInstance.setApplyLogo(data.logo);
        }

        // 更新当前页面的store
        if (store) {
          store.setApplyLogo(data.logo);
        }

        // 触发自定义事件通知其他组件更新
        const updateEvent = new CustomEvent('appInfoUpdate', {
          detail: {
            type: 'logo',
            value: data.logo
          }
        });
        window.dispatchEvent(updateEvent);

        props.update();
      } else {
        toast.error(res.msg);
      }
    });
  };


  // 新增：AMIS弹框schema
  // const editIconDialogSchema = {
  //   type: 'dialog',
  //   title: '修改应用图标',
  //   body: {
  //     type: 'form',
  //     id: 'icon-form',
  //     body: [
  //       {
  //         type: 'tpl',
  //         tpl: iconPreviewTemplate,
  //         className: 'text-left',
  //       },
  //       {
  //         type: 'button',
  //         label: '重置图标',
  //         level: 'default',
  //         className: 'reset-btn text-right',
  //         wrapperComponent: 'div',
  //         style: {
  //           height: '36px',
  //           background: '#fff',
  //           border: '1px solid #E5E6EB',
  //           float: 'right',
  //           color: '#1D2129',
  //           borderRadius: '6px',
  //           fontWeight: 500,
  //           fontSize: '14px',
  //           marginBottom: '16px',
  //           verticalAlign: 'middle'
  //         },
  //         onEvent: {
  //           click: {
  //             actions: [
  //               {
  //                 actionType: 'setValue',
  //                 componentId: 'icon-form',
  //                 args: {
  //                   value: {
  //                     icon: '',
  //                     iconColor: '',
  //                     iconBg: ''
  //                   }
  //                 }
  //               }
  //             ]
  //           }
  //         }
  //       },

  //       {
  //         type: 'icon-selector',
  //         name: 'icon',
  //         label: '图标',
  //         placeholder: '请选择图标',
  //         labelClassName: 'font-normal text-left',
  //         className: 'text-left',
  //         returnSvg: true,
  //       },
  //       {
  //         type: 'input-color',
  //         name: 'iconColor',
  //         label: '图标颜色',
  //         placeholder: '请选择颜色',
  //         labelClassName: 'font-normal text-left',
  //         className: 'text-left'
  //       },
  //       {
  //         type: 'input-color',
  //         name: 'iconBg',
  //         label: '图标背景',
  //         placeholder: '请选择背景颜色',
  //         labelClassName: 'font-normal text-left',
  //         className: 'text-left'
  //       }
  //     ]
  //   },
  //   actions: [
  //     {
  //       type: 'button',
  //       label: '取消',
  //       actionType: 'cancel'
  //     },
  //     {
  //       type: 'button',
  //       label: '保存',
  //       level: 'primary',
  //       actionType: 'submit'
  //     }
  //   ]
  // };
 
  


  /* methods 方法 */
  const modifyApplicationName = (val: any) => {
    if(props.applyData.name == val[0].name){
      setOpenApplicationNamePopup(false);
      return
    }
    let data = props.applyData;
    data.name = val[0].name;
    javaApplicationUpdate(data).then((res: any) => {
      if (res.code === 0) {
        // 更新父窗口的store（如果存在）
        if (window.parent && window.parent.storeInstance) {
          window.parent.storeInstance.setApplyName(val[0].name);
        }

        // 更新当前页面的store
        if (store) {
          store.setApplyName(val[0].name);
        }

        // 触发自定义事件通知其他组件更新
        const updateEvent = new CustomEvent('appInfoUpdate', {
          detail: {
            type: 'name',
            value: val[0].name
          }
        });
        window.dispatchEvent(updateEvent);

        setTimeout(() => {
          setOpenApplicationNamePopup(false);
          toast.success('修改成功');
          props.update();
        }, 100);
      } else {
        toast.error(res.msg);
      }
    });
  };
  const modifyApplicationDesc = (val: any) => {
    if(props.applyData.description == val[0].name){
      setOpenApplicationDescPopup(false);
      return
    }
    let data = props.applyData;
    data.description = val[0].name;
    javaApplicationUpdate(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('修改成功');
        setOpenApplicationDescPopup(false);
        props.update();
      } else {
        toast.error(res.msg);
      }
    });
  };
  /* methods 方法 end */

  /* created 初始化 */
  React.useEffect(() => {
    console.log('BasicSetting props', props.applyData);
  }, []);

  function useStore(): IMainStore {
    const {store} = useContext(MobXProviderContext);
    return store as IMainStore;
  }
  const store = useStore();
  


  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadFile(
        file,
        'file',
        updateIcon,
        {
          allowedTypes: /^image\/(jpeg|png)$/,
          maxSize: 2 * 1024 * 1024,
          errorMessages: {
            typeError: '只支持 jpg、png 格式的图片',
            sizeError: '图片大小不能超过 2M'
          },

        }
      );
    }
    // 清空 input 的值，这样同一个文件可以重复选择
    event.target.value = '';
  };

  // 通用的文件上传方法
  const uploadFile = (
    file: File, 
    fieldName: string, 
    uploadApi: (formData: FormData) => Promise<any>,
    options: {
      maxSize?: number;
      allowedTypes?: RegExp;
      errorMessages?: {
        typeError?: string;
        sizeError?: string;
      };
    
    } = {}
  ) => {
    // 默认配置
  const defaultOptions = {
    maxSize: 2 * 1024 * 1024, // 默认2MB
    allowedTypes: /^image\/(jpeg|png)$/, // 默认jpg和png
    errorMessages: {
      typeError: '只支持 jpg、png 格式的图片',
      sizeError: '图片大小不能超过 2M'
    }
  };
  
  const config = { ...defaultOptions, ...options };
  
  // 验证文件类型
  if (!file.type.match(config.allowedTypes)) {
    toast.error(config.errorMessages.typeError || '文件类型不支持');
    return;
  }
  
  // 验证文件大小
  if (file.size > config.maxSize) {
    toast.error(config.errorMessages.sizeError || '文件大小超出限制');
    return;
  }
  
  // 创建 FormData
  const formData = new FormData();
  formData.append(fieldName, file);
  console.log('file:', file);

  // 打印FormData内容的正确方法
  console.log('FormData内容:');
  // 使用更兼容的方式打印FormData
  console.log(`${fieldName}: File(${file.name}, ${file.type}, ${file.size} bytes)`);
  
  // 临时预览（如果是图片）
  if (file.type.startsWith('image/')) {
    const reader = new FileReader();
    reader.onload = e => {
      // setcompanyInfo({
      //   ...companyInfo,
      //   [fieldName]: e.target?.result as string
      // });
      let data = props.applyData;
      data.logo=e.target?.result as string;
      props.update();
    };
    reader.readAsDataURL(file);
  }
  
  // 调用上传API
  return uploadApi(formData)
    .then(res => {
      console.log('update user profile:', res);
      if (res.code === 0) {
        let data = props.applyData;
        data.logo=res.data   //res.data
        javaApplicationUpdate(data).then((res: any) => {
          if (res.code === 0) {
            // 更新父窗口的store（如果存在）
            if (window.parent && window.parent.storeInstance) {
              window.parent.storeInstance.setApplyLogo(data.logo);
            }

            // 更新当前页面的store
            if (store) {
              store.setApplyLogo(data.logo);
            }

            // 触发自定义事件通知其他组件更新
            const updateEvent = new CustomEvent('appInfoUpdate', {
              detail: {
                type: 'logo',
                value: data.logo
              }
            });
            window.dispatchEvent(updateEvent);

            toast.success('修改成功');
            props.update();
          } else {
            toast.error(res.msg);
          }
        });
        // setcompanyInfo({
        //   ...companyInfo,
        //   //icon: res.data.url
        // });
        // store.setUserInfo({
        //   ...store.userInfo,
        //   companyLogo: res.data
        // });
      } else {
        console.log('上传失败')
        toast.error(res.message);
      }   
      // console.log(`upload ${fieldName}:`, res);
      // return res;
    })
    .catch(err => {
      toast.error(err.message);
      throw err;
    });
};


  /* created 初始化 end */
  return (
    <div className="apply_basicSetting">
      {/* 基础设置 */}
      <div className="apply_basicSetting-title">
        <div className="apply_basicSetting-title-name">基础设置</div>
        <div className="apply_basicSetting-title-desc">
          设置应用名称、应用图标等
        </div>
      </div>
      {/* 应用图标 */}
      <div className="apply_basicSetting-item">
        <div className="apply_basicSetting-item-left">
          <div className="apply_basicSetting-item-left-name">应用LOGO</div>
          <div className="apply_basicSetting-item-left-icon" >
            
            {props.applyData.logo ? (
              props.applyData.logo.includes('<') ? (
                // 如果是HTML格式的图标（SVG或span），直接渲染
                <div
                  className="apply_basicSetting-item-left-icon-html"
                  dangerouslySetInnerHTML={{ __html: props.applyData.logo }}
                />
              ) : props.applyData.logo.startsWith('http') ? (
                // 如果是图片URL，使用Avatar组件
                <Avatar
                  src={props.applyData.logo}
                  size={36}
                  className="cursor-pointer"
                />
              ) : (
                // 如果是字体图标类名，使用i标签
                <div className="apply_basicSetting-item-left-icon-font">
                  <i className={props.applyData.logo} style={{ fontSize: '24px' }} />
                </div>
              )
            ) : (
              <img
                className="apply_basicSetting-item-left-icon-img"
                src={app_default_icon}
              />
            )}


          </div>
        </div>
        <div className="apply_basicSetting-item-right">
        <input
                             ref={fileInputRef}
                             type="file"
                             accept="image/*"
                             style={{display: 'none'}}
                             onChange={handleAvatarUpload}
                        />

          <div className="apply_basicSetting-item-right-btn" onClick={() => setShowEditIconDialog(true)}>修改图标</div>  
          {/* <div className="apply_basicSetting-item-right-btn" onClick={() => fileInputRef.current?.click()}>修改图标</div>   */}
          
        </div>
      </div>
      {/* 应用名称 */}
      <div className="apply_basicSetting-item">
        <div className="apply_basicSetting-item-left">
          <div className="apply_basicSetting-item-left-name">应用名称</div>
          <div className="apply_basicSetting-item-left-content">
            {props.applyData.name}
          </div>
        </div>
        <div className="apply_basicSetting-item-right">
          <div
            className="apply_basicSetting-item-right-btn"
            onClick={() => setOpenApplicationNamePopup(true)}
          >
            编辑名称
          </div>
        </div>
      </div>
      {/* 应用描述 */}
      <div className="apply_basicSetting-item">
        <div className="apply_basicSetting-item-left">
          <div className="apply_basicSetting-item-left-name">应用描述</div>
          <div className="apply_basicSetting-item-left-content">
            {props.applyData.description}
          </div>
        </div>
        <div className="apply_basicSetting-item-right">
          <div
            className="apply_basicSetting-item-right-btn"
            onClick={() => setOpenApplicationDescPopup(true)}
          >
            修改描述
          </div>
        </div>
      </div>
      {openApplicationNamePopup && (
        <AMISRenderer
          show={openApplicationNamePopup}
          schema={modifyApplicationNamePopup(props.applyData.name)}
          onClose={() => setOpenApplicationNamePopup(false)}
          onConfirm={(val: any) => modifyApplicationName(val)}
        />
      )}
      {openApplicationDescPopup && (
        <AMISRenderer
          show={openApplicationDescPopup}
          schema={modifyApplicationDescPopup(props.applyData.description)}
          onClose={() => setOpenApplicationDescPopup(false)}
          onConfirm={(val: any) => modifyApplicationDesc(val)}
        />
      )}

      {showEditIconDialog && (
          <AMISRenderer
            show={showEditIconDialog}
            onClose={() => setShowEditIconDialog(false)}
            onConfirm={handleIconSave}
            schema={EditEntryIcon({
              logo: (() => {
                // 解析当前应用图标，获取正确的图标数据
                const parsedIcon = parseAppIcon(props.applyData?.logo || '');
                return {
                  icon: parsedIcon.icon,
                  iconColor: parsedIcon.iconColor,
                  iconBg: parsedIcon.iconBg
                };
              })()
            })}
          />
      )}



    </div>
  );
};

export default BasicSetting;
