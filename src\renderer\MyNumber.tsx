import React from 'react';
import {Renderer} from 'amis';
import {RendererProps} from 'amis-core';
import {getPropValue, numberFormatter, stripNumber} from 'amis-core';
import {toFixed, num2str} from '@rc-component/mini-decimal';

export interface MyNumberProps extends RendererProps {
  /**
   * 精度，用来控制小数点位数
   */
  precision?: number;
  /**
   * 前缀
   */
  prefix?: string;
  /**
   * 后缀
   */
  suffix?: string;
  /**
   * 是否千分分隔
   */
  kilobitSeparator?: boolean;
  /**
   * 百分比显示
   */
  percent?: boolean | number;
  /**
   * 占位符
   */
  placeholder?: string;
  /**
   * 单位列表
   */
  unitOptions?: any;
  /**
   * 后缀（同suffix）
   */
  affix?: string;
}

@Renderer({
  type: 'my-number',
  name: 'my-number'
})
export class MyNumberRenderer extends React.Component<MyNumberProps> {
  static defaultProps = {
    placeholder: '-',
    kilobitSeparator: true
  };

  constructor(props: MyNumberProps) {
    super(props);
  }

  render() {
    const {
      placeholder,
      kilobitSeparator,
      precision,
      prefix,
      affix,
      suffix,
      percent,
      unitOptions,
      className,
      style,
      classnames: cx,
      translate: __
    } = this.props;

    let viewValue: React.ReactNode = <span className="text-muted">{placeholder}</span>;
    let value = getPropValue(this.props);
    let unit = '';

    // 处理单位选项
    if (typeof value === 'string' && unitOptions && unitOptions.length) {
      const units = (Array.isArray(unitOptions) ? unitOptions : [unitOptions])
        .map((v: any) => (typeof v === 'string' ? v : v.value));
      
      unit = units.find((item: string) => value.endsWith(item)) || '';
      if (unit) {
        value = value.replace(unit, '');
      }
    }

    if (typeof value === 'number' || typeof value === 'string') {
      // 设置了精度，但是原始数据是字符串，需要转成 float 之后再处理
      if (typeof value === 'string' && precision) {
        value = stripNumber(parseFloat(value));
      }

      if (isNaN(value as number)) {
        viewValue = null;
      } else if (percent) {
        // 如果是百分比展示
        value = parseFloat(value as string) || 0;
        const decimals = typeof percent === 'number' ? percent : 0;
        const whole = value * 100;
        const multiplier = Math.pow(10, decimals);
        value = (Math.round(whole * multiplier) / multiplier).toFixed(decimals) + '%';
        viewValue = <span>{value}</span>;
      } else {
        if (typeof value === 'number' && precision) {
          value = toFixed(num2str(value), '.', precision);
        }
        
        if (kilobitSeparator) {
          value = numberFormatter(value, precision);
        }
        
        viewValue = <span>{value}</span>;
      }
    }

    viewValue = !viewValue ? (
      <span className="text-danger">{__('Number.invalid')}</span>
    ) : (
      <React.Fragment>
        {prefix}
        {viewValue}
        {unit}
        {affix !== undefined ? affix : suffix}
      </React.Fragment>
    );

    return (
      <span className={cx('MyNumberField', className)} style={style}>
        {viewValue}
      </span>
    );
  }
}
