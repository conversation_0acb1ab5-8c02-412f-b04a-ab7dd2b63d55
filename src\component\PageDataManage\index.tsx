import React, {FC, useRef} from 'react';
import './index.scss';
import {<PERSON><PERSON>, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
// 接口引入
import {
  getDataSet,
  getFormDataPage // 获取表单数据
} from '@/utils/api/api';

// 组件：页面设置
import PageSettings from '@/component/PageSettings/index';
import PageDataSettings from '@/component/PageDataSettings/index';
const PageContent: FC<any> = (props: any) => {
  // 选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState<any>(() => {
    // 从 URL 查询参数中获取 activeTabKey，如果没有则默认为 'manage'
    const urlParams = new URLSearchParams(props.history.location.search);
    return urlParams.get('activeTabKey') || 'manage';
  });
  // 表单数据
  const [formData, setFormData] = React.useState<any>({});
  const columns = useRef<any>([]);
  const [dataSetDetail, setDataSetDetail] = React.useState<any>({});

  interface SearchConfig {
    type: string;
    name: string;
    label: string;
    placeholder: string;
    options?: any[];
    multiple?: boolean;
  }

  const addSearchableToColumns = (columns: any[]) => {
    // 定义不需要搜索配置的字段类型
    const excludeTypes = ['input-file', 'input-image', 'input-excel', 'input-city', 'input-rich-text', 'location-picker', 'input-signature', 'text', 'datetime', 'operation'];

    return columns.map(column => {
      if (excludeTypes.includes(column.type)) {
        return column;
      }
      // 根据不同的字段类型设置不同的搜索配置
      let searchConfig: SearchConfig = {
        type: column.type,
        name: column.name,
        label: column.label,
        placeholder: `请输入${column.label}`
      };

      if (column.options) {
        searchConfig = {
          ...searchConfig,
          options: column.options
        };
      }
      // 返回添加了 searchable 字段的新对象
      return {
        ...column,
        searchable: searchConfig
      };
    });
  };

  const getDataSetCurd = (columns: any) => {
    console.log('columns', columns);

    const dataSetCurd = {
      id: 'u:6be5133b77f5',
      type: 'page',
      title: '数据管理',
      regions: ['body'],
      pullRefresh: {disabled: true},
      body: [
        // {
        //   type: 'button',
        //   label: '批量删除',
        //   actionType: 'ajax',
        //   api: 'delete:/amis/api/mock2/sample/${ids|raw}',
        //   confirmText: '确定要批量删除?'
        // },
        // {
        //   type: 'button',
        //   label: '批量修改',
        //   actionType: 'dialog',
        //   dialog: {
        //     title: '批量编辑',
        //     body: {
        //       type: 'form',
        //       api: '/amis/api/mock2/sample/bulkUpdate2',
        //       body: [
        //         {
        //           type: 'hidden',
        //           name: 'ids'
        //         },
        //         {
        //           type: 'input-text',
        //           name: 'engine',
        //           label: 'Engine'
        //         }
        //       ]
        //     }
        //   }
        // },
        {
          type: 'crud',
          syncLocation: false,
          api: {
            method: 'get',
            url: `/admin-api/system/form-field-value/page/${props.pageData.id}?dateSetId=${dataSetDetail.dataSetId}&pageNo=1&pageSize=10`,
            messages: {},
            requestAdaptor: '',
            adaptor: ``
          },
          autoGenerateFilter: {
            columnsNum: 2,
            showBtnToolbar: false
          },
          // filter: {
          //   "title": "条件搜索",
          //   "body": [
          //     {
          //       "type": "input-text",
          //       "name": "keywords",
          //       "placeholder": "通过关键字搜索"
          //     }
          //   ]
          // },
          id: 'u:f1f24e58104f',
          perPageAvailable: [5, 10, 20, 50, 100],
          draggable: true,
          bulkActions: [
            {
              label: '批量删除',
              actionType: 'ajax',
              // api: 'delete:/amis/api/mock2/sample/${ids|raw}',
              api: 'delete:/admin-api/system/form-field-value/batch-delete/'+props.pageData.id+'/${ids|raw}',
              confirmText: '确定要批量删除?'
            },
            {
              label: '批量修改',
              actionType: 'dialog',
              dialog: {
                title: '批量编辑',
                body: {
                  type: 'form',
                  api: '/amis/api/mock2/sample/bulkUpdate2',
                  body: [
                    ...columns
                  ]
                }
              }
            }
          ],
          "itemActions": [
            {
              "label": "按钮",
              "type": "button",
              "id": "u:932bece73200"
            },
            {
              "type": "button",
              "label": "删除",
              "actionType": "ajax",
              "api": {
                "method": "post",
                "url": "/amis/api/xxxx/$id",
                "data": {
                  "&": "$$",
                  "op": "delete"
                }
              },
              "confirmText": "确定要删除？"
            }
          ],
          // filterTogglable: true,
          headerToolbar: [
            'reload',
            // 'filter-toggler',
            {
              type: 'button',
              label: '新增',
              id: 'u:63bcbfc69ed8',
              disabledOnAction: false,
              onEvent: {
                click: {
                  actions: [
                    {
                      actionType: 'custom',
                      expression: 'getToCreateDataSetPage()'
                    }
                  ]
                }
              }
            },
            'load-more',
            // {
            //   type: 'export-csv',
            //   tpl: '内容',
            //   wrapperComponent: '',
            //   id: 'u:30129f3c0998'
            // },
            // {
            //   type: 'export-excel',
            //   tpl: '内容',
            //   wrapperComponent: '',
            //   id: 'u:6a63d52666a1',
            //   disabledOnAction: false
            // },
            'bulkActions',
            'statistics'
          ],
          syncResponse2Query: false,
          pageField: 'pageNo',
          orderField: 'id',
          perPageField: 'pageSize',
          columns: columns,
          alwaysShowPagination: true,
          footerToolbar: [
            {
              type: 'statistics'
            },
            {
              type: 'switch-per-page',
              tpl: '切换页码',
              wrapperComponent: '',
              id: 'u:bda5e402486b'
            },
            {
              type: 'pagination',
              tpl: '分页',
              wrapperComponent: '',
              id: 'u:b2ec821b8884',
              __editorStatebaseControlClassName: 'default'
            }
          ]
        }
      ],
      asideResizor: false
    };
    return dataSetCurd;
  };

  // 编辑情况获取详情信息
  const handleGetDataSet = (dataSetId: any) => {
    let data = {
      id: dataSetId
    };
    getDataSet(data).then((res: any) => {
      if (res.code === 0) {
        console.log('handleGetDataSet res', res);
        setDataSetDetail(res.data);
      } else {
        toast.error(res.msg);
      }
    });
  };

  // 获取表单数据 formData
  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: props.pageData.id,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      console.log('handleGetFormDataPage res', res);
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            res.data.list[0].appId = props.computedMatch.params.appId;
            columns.current = addSearchableToColumns(
              res.data.list[0].data.body[0].columns
            );
            setFormData(res.data.list[0]);
            handleGetDataSet(res.data.list[0].dataSetId);
          } else {
            toast.success('暂无表单数据');
          }
        } else {
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  const visitForm = () => {
    props.history.push(
      `/app${props.computedMatch.params.appId}/admin/page${dataSetDetail.associatedDataTable}`
    );
  };

  // 访问页面内容 - 在新窗口打开
  const visitPage = () => {
    const url = `#/app${props.computedMatch.params.appId}/DataSubmission/${props.computedMatch.params.form}?editCode=${props.pageData.id}&pageName=${props.pageData.name}`;
    console.log('url', url);
    const baseUrl = window.location.origin + window.location.pathname;
    window.open(baseUrl + url, '_blank');
  };

  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);

    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);

    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }

    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }

    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  React.useEffect(() => {
    console.log('PageContent props', props);
    if (props.pageData?.id) {
      handleGetFormDataPage();
    }
    // 从 URL 查询参数中获取 activeTabKey，如果没有则默认为 'manage'
    const urlParams = new URLSearchParams(props.history.location.search);
    let tabkey = urlParams.get('activeTabKey') || 'manage';
    setActiveKey(tabkey);
  }, [props.pageData?.id, props.history.location]);

  return (
    <div className="pageBox">
      {props.match.params.playType == 'admin' && <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-6">
            <Button size="lg" level="default" onClick={() => visitForm()}>
              查看表单
            </Button>
          </div>
          <div className="pl-1">
            <Button size="lg" level="default" onClick={() => visitPage()}>
              访问
            </Button>
          </div>
        </div>
      </div>}
      <div className="cxd-pageTabsForm">
        {/* activeKey */}
        <Tabs
          mode="line"
          onSelect={(key: any) => handleTabChange(key)}
          linksClassName="pageTabsForm-tabsTitle"
          activeKey={activeKey}
        >
          <Tab title="数据管理" eventKey="manage">
            <div className="pageTabsForm-tabsContent">
  
              <AMISRenderer
                schema={(formData.data)}
              />
            </div>
          </Tab>
          <Tab title="数据页设置" eventKey="dataset">
            <PageDataSettings
              pageData={formData}
              history={props.history}
              store={props.store}
              update={() => {
                props.updatePage();
                handleGetFormDataPage();
              }}
            />
          </Tab>
         {props.match.params.playType == 'admin' &&  <Tab title="页面设置" eventKey="setting">
            <PageSettings
              formData={formData}
              history={props.history}
              pageData={props.pageData}
              store={props.store}
              update={() => {
                props.updatePage();
              }}
            />
          </Tab>}
        </Tabs>
      </div>
    </div>
  );
};

export default PageContent;
