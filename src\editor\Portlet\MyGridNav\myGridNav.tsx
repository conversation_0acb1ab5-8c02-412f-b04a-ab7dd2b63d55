import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class MyGridNavPlugin extends BasePlugin {
  static id = 'MyGridNavPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'grid-nav';
  $schema = '/schemas/MyGridNavSchema.json';

  // 组件基本信息
  name = 'GridNav 宫格导航';
  panelTitle = 'GridNav 宫格导航';
  icon = 'fa fa-th';
  panelIcon = 'fa fa-th';
  pluginIcon = 'fa fa-th';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '宫格导航，适合做导航菜单';
  docLink = '/amis/zh-CN/components/grid-nav';
  tags = ['门户'];

  // 组件默认配置
  scaffold = {
    type: 'grid-nav',
    options: [
      {
        icon: 'fa fa-user',
        text: '用户管理',
        link: '/user'
      },
      {
        icon: 'fa fa-file',
        text: '文档管理',
        link: '/doc'
      },
      {
        icon: 'fa fa-cog',
        text: '系统设置',
        link: '/settings'
      }
    ],
    square: true,
    center: true,
    border: true,
    gutter: 16,
    columnNum: 3,
    direction: 'vertical'
  };

  // 预览界面
  previewSchema = {
    type: 'grid-nav',
    className: 'text-left',
    options: [
      {
        icon: 'fa fa-user',
        text: '用户管理'
      },
      {
        icon: 'fa fa-file',
        text: '文档管理'
      }
    ]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                {
                  type: 'input-text',
                  name: 'className',
                  label: 'CSS类名',
                  description: '自定义CSS类名'
                },
                {
                  type: 'input-text',
                  name: 'itemClassName',
                  label: '列表项类名',
                  description: '列表项的CSS类名'
                },
                {
                  type: 'input-text',
                  name: 'contentClassName',
                  label: '内容类名',
                  description: '列表项内容的CSS类名'
                },
                {
                  type: 'switch',
                  name: 'square',
                  label: '正方形',
                  value: true,
                  description: '是否将列表项固定为正方形'
                },
                {
                  type: 'switch',
                  name: 'center',
                  label: '居中显示',
                  value: true,
                  description: '是否将列表项内容居中显示'
                },
                {
                  type: 'switch',
                  name: 'border',
                  label: '显示边框',
                  value: true,
                  description: '是否显示列表项边框'
                },
                {
                  type: 'input-number',
                  name: 'gutter',
                  label: '间距',
                  value: 16,
                  description: '列表项之间的间距，默认单位为px'
                },
                {
                  type: 'input-number',
                  name: 'iconRatio',
                  label: '图标宽度占比',
                  value: 60,
                  min: 1,
                  max: 100,
                  description: '图标宽度占比, 1-100'
                },
                {
                  type: 'select',
                  name: 'direction',
                  label: '排列方向',
                  value: 'vertical',
                  options: [
                    {label: '垂直排列', value: 'vertical'},
                    {label: '水平排列', value: 'horizontal'}
                  ],
                  description: '列表项内容排列的方向'
                },
                {
                  type: 'input-number',
                  name: 'columnNum',
                  label: '列数',
                  value: 3,
                  min: 1,
                  max: 12,
                  description: '一行显示的列数'
                }
              ]
            },
            {
              title: '数据设置',
              body: [
                {
                  type: 'input-text',
                  name: 'source',
                  label: '数据源',
                  description: '数据源: 绑定当前环境变量，如${items}'
                },
                {
                  type: 'combo',
                  name: 'options',
                  label: '选项列表',
                  multiple: true,
                  draggable: true,
                  addButtonText: '添加选项',
                  items: [
                    {
                      type: 'input-text',
                      name: 'text',
                      label: '文本',
                      required: true,
                      description: '显示的文本'
                    },
                    {
                      type: 'icon-picker',
                      name: 'icon',
                      label: '图标',
                      placeholder: '请选择图标',
                      description: '显示的图标'
                    },
                    {
                      type: 'input-text',
                      name: 'link',
                      label: '链接',
                      description: '点击跳转的链接'
                    },
                    {
                      type: 'select',
                      name: 'blank',
                      label: '打开方式',
                      options: [
                        {label: '当前窗口', value: ''},
                        {label: '新窗口', value: '_blank'}
                      ],
                      description: '链接打开方式'
                    },
                    getSchemaTpl('actionControl', {
                      name: 'clickAction',
                      label: '点击动作',
                      description: '点击时触发的动作'
                    }),
                    {
                      type: 'combo',
                      name: 'badge',
                      label: '徽标',
                      multiLine: true,
                      visibleOn: 'this.badge',
                      items: [
                        {
                          type: 'input-text',
                          name: 'text',
                          label: '文本',
                          description: '徽标显示的文本'
                        },
                        {
                          type: 'select',
                          name: 'mode',
                          label: '模式',
                          options: [
                            {label: '点', value: 'dot'},
                            {label: '文字', value: 'text'}
                          ],
                          description: '徽标的显示模式'
                        },
                        {
                          type: 'input-color',
                          name: 'color',
                          label: '颜色',
                          description: '徽标的颜色'
                        },
                        {
                          type: 'input-text',
                          name: 'position',
                          label: '位置',
                          description: '徽标的位置，如：top-right'
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '样式设置',
              body: [
          
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(MyGridNavPlugin);

