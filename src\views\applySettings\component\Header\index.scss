.applySettingsHeader {
  width: 100%;
  height: 100%;
  background-color: var(--body-bg);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--borderColor);

  &-left {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 1.875rem;

    &-gzt {
      width: 2rem;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;

      &-icon {
        font-size: 1rem;
        color: var(--text-color);
      }
    }

    &-appInfo {
      display: flex;
      align-items: center;

      &-icon {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0.5rem;

        &-img {
            width: 100%;
            height: 100%;
            display: block;
        }
      }

      &-name {
        font-size: 0.875rem;
        color: var(--text-color);
        cursor: pointer;
      }

      &-gan{
        font-size: 0.875rem;
        color: var(--text--muted-color);
        margin: 0 0.625rem;
      }

    }

    &-appSet{
        color: var(--text-color);
        font-size: 0.875rem;
    }
  }
}
