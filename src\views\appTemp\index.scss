.pageBox {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: auto;
  /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
  -webkit-scrollbar {
    display: none;
  }
  /* 隐藏Firefox浏览器的滚动条 */
  scrollbar-width: none;
}

.pageTop {
  position: relative;
  width: 100%;
  height: 4.125rem;
  padding: 0 1.875rem;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-title {
    flex: 1;
    color: var(--text-color);
    font-size: 1.25rem;
  }

  &-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.component-header {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 0 16px;
  background-color: var(--background);
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.search-area {
  width: 100%;
  display: flex;
  align-items: center;
  
  .cxd-Form,
  .dark-Form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    
    .cxd-Form-item,
    .dark-Form-item {
      margin-right: 32px;
      margin-bottom: 0;
      
      .cxd-Form-label,
      .dark-Form-label {
        color: var(--text-color);
        font-size: 14px;
        min-width: 70px;
      }
      
      .cxd-Form-control,
      .dark-Form-control {
        min-width: 180px;
      }
    }
    
    .cxd-Form-btnToolbar {
      margin-left: auto;
      
      .cxd-Button {
        margin-left: 8px;
      }
    }
  }
}

// .search-box {
  // width: 100%;
// }

.action-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.component-container {
  display: flex;
  margin: 0 16px;
  height: calc(100vh - 200px);
  background-color: var(--background);
  border-radius: 4px;
  overflow: hidden;
}

.category-sidebar {
  width: 220px;
  border-right: 1px solid var(--border-bottom-line-color);
  background-color: var(--background);
  height: 100%;
  overflow-y: auto;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-weight: 500;
  border-bottom: 1px solid var(--border-bottom-line-color);;
}

.category-list {
  padding: 8px 0;
}

.category-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.category-item:hover {
  background-color: var(--light-bg);
}

.category-item.active {
  background-color: var(--light-bg);
  border-right: 2px solid var(--primary);
}

.category-item-content {
  display: flex;
  align-items: center;
}

.category-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.category-name {
  flex: 1;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.rename-input {
  flex: 1;
  width: 100%;
}

.content-area {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.search-form {
  margin-bottom: 16px;
  background-color: #f7f8fa;
  border-radius: 4px;
  padding: 16px;
}

.search-form .cxd-Form-title {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 16px;
}

.search-form .cxd-Form-body {
  padding-bottom: 0;
}

.search-form .cxd-Form-actions {
  margin-top: 16px;
  border-top: none;
  background: transparent;
}

.mode-switch {
  display: flex;
  align-items: center;
}

.mode-switch .cxd-Button {
  margin-left: 8px;
}

.mode-switch .cxd-Button.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-color: #1890ff;
}

.customContextMenu {
  position: fixed;
  background: white;
  border: 1px solid #e8e9eb;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 1000;
  min-width: 120px;
}

.customContextMenu-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.customContextMenu-item:hover {
  background-color: #f5f5f5;
}

.add-category-btn {
  padding: 0;
  min-width: auto;
}

.pageTabsLink {
  position: relative;
  width: 100%;


  &-tabsTitle {
    padding: 0 1.875rem;
  }

  &-tabsContent{
    width: 100%;
    height: calc(100vh - 3.125rem );
    
    padding: 1.25rem  1.125rem;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  margin-top: 40px;
}

.name-color {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.remark-color {
  font-size: 13px;
  margin-top: 4px;
  color: var(--text-color);
}
