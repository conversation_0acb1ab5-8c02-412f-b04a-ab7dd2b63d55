# 工作台菜单导航逻辑优化说明

## 修改概述

本次修改主要针对工作台（Workbench）页面的菜单导航逻辑进行了优化，解决了团队列表功能中的路由匹配和菜单选择问题。

## 修改信息

- **修改时间**：2023年11月15日
- **涉及页面**：工作台主页面 (Workbench)
- **涉及文件**：`src/views/workbench/index.tsx`

## 代码修改对比

### 1. findRouteByPath 方法重构

**修改前**：
```typescript
const findRouteByPath = () => {
  let params = match.params;
  console.log(params);
  if (params.team) {
    if (teamList.length == 0) return;
    for (let i = 0; teamList.length > i; i++) {
      if (params.team == teamList[i].id) {
        setActiveTeamItem(teamList[i]);
        if (openTeamIds.length == 0) {
          isOpenTeamMenu(teamList[i].id);
        }
        setMianMenuBar(false);
        if (params.project) {
          for (let a = 0; teamList[i].children.length > a; a++) {
            if (params.project == teamList[i].children[a].id) {
              setActiveProjectItem(teamList[i].children[a]);
            }
          }
        } else {
          setActiveProjectItem(false);
        }
      }else{
        setMianMenuBar(params.team);
        setActiveProjectItem(false);
        setActiveTeamItem(false);
      }
    }
  } else {
    setActiveProjectItem(false);
    setActiveTeamItem(false);
    setMianMenuBar('workbench');
    history.push('/platform/workbench');
  }
};
```

**修改后**：
```typescript
const findRouteByPath = () => {
  let params = match.params;
  console.log(params);
  
  // 如果没有团队参数，设置为工作台
  if (!params.team) {
    setActiveProjectItem(false);
    setActiveTeamItem(false);
    setMianMenuBar('workbench');
    history.push('/platform/workbench');
    return;
  }
  
  // 如果是特殊页面路由
  if (['workbench', 'approval', 'message', 'manage', 'template', 'icons', 'fontawesome', 'account'].includes(params.team)) {
    setMianMenuBar(params.team);
    setActiveProjectItem(false);
    setActiveTeamItem(false);
    return;
  }
  
  // 如果是团队路由
  if (teamList.length > 0) {
    // 查找匹配的团队
    const teamId = params.team.replace('team', '');
    const matchedTeam = teamList.find((team: any) => team.id == teamId);
    
    if (matchedTeam) {
      setActiveTeamItem(matchedTeam);
      setMianMenuBar(false);
      
      // 确保团队菜单是展开的
      if (!openTeamIds.includes(matchedTeam.id)) {
        let newOpenIds = [...openTeamIds, matchedTeam.id];
        setOpenTeamIds(newOpenIds);
      }
      
      // 如果有项目参数，查找匹配的项目
      if (params.project) {
        const projectId = params.project.replace('project', '');
        if (matchedTeam.children && matchedTeam.children.length > 0) {
          const matchedProject = matchedTeam.children.find((project: any) => project.id == projectId);
          if (matchedProject) {
            setActiveProjectItem(matchedProject);
          } else {
            setActiveProjectItem(false);
          }
        } else {
          setActiveProjectItem(false);
        }
      } else {
        setActiveProjectItem(false);
      }
    } else {
      // 如果没有匹配的团队，设置为工作台
      setMianMenuBar('workbench');
      setActiveProjectItem(false);
      setActiveTeamItem(false);
    }
  }
};
```

### 2. 团队点击事件优化

**修改前**：
```typescript
onClick={() => {
  setActiveTeamItem(item);
  setActiveProjectItem(false);
  setMianMenuBar(false);
  history.push(`/platform/team${item.id}`);
}}
```

**修改后**：
```typescript
onClick={() => {
  setActiveTeamItem(item);
  setActiveProjectItem(false);
  setMianMenuBar(false);
  
  // 确保团队菜单是展开的
  if (!openTeamIds.includes(item.id)) {
    let newOpenIds = [...openTeamIds, item.id];
    setOpenTeamIds(newOpenIds);
  }
  
  history.push(`/platform/team${item.id}`);
}}
```

### 3. 项目点击事件优化

**修改前**：
```typescript
onClick={() => {
  history.push(
    `/platform/team${item.id}/project${childrenItem.id}`
  );
  setActiveProjectItem(childrenItem);
  setActiveTeamItem(item);
  setMianMenuBar(false);
}}
```

**修改后**：
```typescript
onClick={() => {
  history.push(
    `/platform/team${item.id}/project${childrenItem.id}`
  );
  setActiveProjectItem(childrenItem);
  setActiveTeamItem(item);
  setMianMenuBar(false);
  
  // 确保团队菜单是展开的
  if (!openTeamIds.includes(item.id)) {
    let newOpenIds = [...openTeamIds, item.id];
    setOpenTeamIds(newOpenIds);
  }
}}
```

### 4. 环境适配优化

**修改前**：
```typescript
<AMISRenderer
  schema={createIframeObj(
    `https://javaddm.fjpipixia.com/wflow/#/workspace/notice?userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
  )}
/>
```

**修改后**：
```typescript
<AMISRenderer
  schema={createIframeObj(
    `${process.env.NODE_ENV === 'development'?'http://localhost:88/#/workspace/notice':'https://javaddm.fjpipixia.com/wflow/#/workspace/notice'}?userID=${store.userInfo?.id}&tenantId=${store.tenant_id}&authorization=${store.access_token}`
  )}
/>
```

## 主要技术点

1. **React 状态管理**：优化了组件状态更新逻辑，确保UI与路由状态保持一致
2. **React Router**：改进了路由参数解析和匹配逻辑
3. **条件渲染**：优化了基于当前路由和状态的UI渲染逻辑
4. **事件处理**：改进了菜单项点击事件的处理流程

## 功能改进点

### 1. 路由参数解析优化

- 重构了`findRouteByPath`方法，使其能够正确解析URL中的团队ID和项目ID
- 添加了对特殊页面路由（如工作台、审批等）的专门处理逻辑
- 使用`replace`方法从路由参数中提取纯数字ID，提高了代码的健壮性

### 2. 菜单状态管理优化

- 实现了更精确的菜单项激活状态控制
- 添加了自动展开当前选中团队子菜单的逻辑
- 优化了团队和项目选择状态的同步更新

### 3. 团队菜单展开逻辑

- 在团队和项目点击事件中添加了自动展开团队菜单的逻辑
- 确保用户点击团队或项目时，相关子菜单自动展开，提升用户体验

### 4. 环境适配

- 优化了消息页面的iframe URL生成逻辑，根据当前环境（开发/生产）动态生成正确的URL
- 使用`process.env.NODE_ENV`判断当前环境，适配不同环境下的API地址

## 代码优化

1. 移除了冗余的调试日志输出
2. 简化了团队和项目查找逻辑，使用数组`find`方法替代循环遍历
3. 优化了条件判断结构，提高代码可读性
4. 统一了状态更新和路由跳转的顺序，避免潜在的状态不一致问题

## 效果

通过本次优化，工作台页面的菜单导航逻辑更加稳定和可靠，用户在不同团队和项目间切换时，UI状态能够正确反映当前路由，提升了整体用户体验。

## 功能更新：根据URL参数显示对应的团队或项目标签页

### 功能描述
- 实现了根据URL查询参数`activeTabKey`自动切换到对应的标签页
- 支持团队和项目两种场景下的标签页切换
- 点击标签页时自动更新URL，使页面状态可以通过URL分享和保存

### 实现细节
1. 添加了从URL查询参数中获取`activeTabKey`的功能
   - 支持的参数值：app, myapp, member, setting, trash
   - 映射到对应的标签页索引：1, 2, 3, 4, 5

2. 实现了标签页切换时更新URL的功能
   - 使用React Router的history API更新URL
   - 使用replace而不是push，避免在历史记录中创建多余的条目

3. 添加了URL变化监听，确保URL与UI状态同步
   - 监听路由变化，自动更新标签页状态
   - 添加条件检查，避免不必要的状态更新和渲染循环

### 路由格式
- 团队：
  - 应用：/platform/team_600001?activeTabKey=app
  - 我创建的：/platform/team_600001?activeTabKey=myapp
  - 成员：/platform/team_600001?activeTabKey=member
  - 设置：/platform/team_600001?activeTabKey=setting
  - 回收站：/platform/team_600001?activeTabKey=trash

- 项目：
  - 应用：/platform/team_600001/project_600001?activeTabKey=app
  - 我创建的：/platform/team_600001/project_600001?activeTabKey=myapp
  - 成员：/platform/team_600001/project_600001?activeTabKey=member
  - 设置：/platform/team_600001/project_600001?activeTabKey=setting
  - 回收站：/platform/team_600001/project_600001?activeTabKey=trash

### 修复的问题
1. 修复了URL参数位置问题
   - 将查询参数正确附加到路由路径后面
   - 从错误格式：http://localhost:89/?activeTabKey=myapp#/platform/team35
   - 到正确格式：http://localhost:89/#/platform/team35?activeTabKey=myapp

2. 修复了需要点击两次标签页才能切换的问题
   - 先更新本地状态，确保UI立即响应
   - 使用条件检查避免不必要的状态更新和渲染循环
   - 添加单独的初始化effect，确保组件挂载时正确设置标签页

### 优化点
- 使用React Router的history和location对象，更好地与React Router集成
- 添加了回退机制，当props.history或props.location不可用时使用window.location
- 使用可选链操作符避免空引用错误
- 添加了依赖数组中的activeTab，确保监听器能够正确响应状态变化 

## 功能更新：添加更新检查功能

### 功能描述
- 在用户菜单中添加了"更新检查"按钮
- 点击按钮后执行以下操作：
  - 清除浏览器本地存储(localStorage)
  - 清除会话存储(sessionStorage)
  - 清除所有cookies
  - 退出当前用户登录
  - 强制刷新页面并添加缓存清除参数

### 实现细节
1. 在用户下拉菜单中添加了"更新检查"按钮
   - 使用`fa-sync-alt`图标表示刷新操作
   - 按钮位置在"个人设置"和"退出登录"之间

2. 实现了`handleForceRefresh`函数处理更新检查逻辑
   - 添加确认对话框，防止用户误操作
   - 使用`localStorage.clear()`和`sessionStorage.clear()`清除本地存储
   - 使用循环清除所有cookies
   - 调用`store.logout()`执行退出登录操作
   - 使用带时间戳参数的URL强制刷新页面，确保浏览器不使用缓存

### 使用场景
- 当用户遇到界面异常或功能失效时，可以使用此功能重置应用状态
- 当系统更新后，用户可以使用此功能确保获取最新版本
- 当用户需要彻底清除本地数据时，可以使用此功能一键完成

### 技术实现
- 使用浏览器原生API清除各类缓存数据
- 使用URL参数技术强制浏览器刷新而不使用缓存
- 使用确认对话框增强用户体验，防止误操作

2025-04-04 14:30:00
