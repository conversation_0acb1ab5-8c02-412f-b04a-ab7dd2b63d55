.account-settings {
  position: relative;
  width: 100%;
  height: 100vh;
  padding-top: 40px;
  overflow: auto;
  
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;

  &-header {
    position: relative;
    max-width: 67.5rem;
    width: 100%;
    margin: 0 auto;
    padding: 40px 0 0 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    &-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: var(--text-color);
    }

    &-tabsToolbar {
      display: flex;
      gap: 32px;
      border: null;

      .tab-item {
        font-size: 14px;
        color: var(--text-color);
        font-weight: bold;
        cursor: pointer;
        padding: 19px 0 12px 0;
        position: relative;

        &:hover {
          color: var(--primary);
        }

        &.active {
          color: var(--primary);

          &:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary);
          }
        }
      }
    }
  }

  &-content {
    position: relative;
    max-width: 67.5rem;
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .settings-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--borderColor);

    &:last-child {
      margin-bottom: 0;
      border-bottom: 0px solid var(--colors-neutral-fill-none);
    }

    .avatar-wrapper {
      margin: 4px 0;
      margin-right: 16px;
    }

    .settings-main {
      flex: 1;
      margin-right: 16px;

      .settings-label {
        font-weight: bold;
        color: var(--text-color);
        font-size: 14px;
        margin-bottom: 8px;
      }

      .settings-content {
        display: flex;
        align-items: center;

        .settings-value {

          .settings-text {
            color: var(--text--muted-color);
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }

    .settings-action {
        
      .cxd-Button--link,
      .dark-Button--link {
        margin-left: 16px;
        border: 1px solid var(--borderColor);
        padding: 6px 12px;
        border-radius: 4px;
        color: var(--text-color);
        font-size: 14px;
        height: 32px;
        line-height: 18px;

        &:hover {
          border-color: var(--primary);
          color: var(--primary);
        }
      }
    }
  }

  .default-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary);
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }
}

.bind-mobile-form {
  padding: 20px;

  .form-item {
    margin-bottom: 16px;

    input {
      width: 100%;
      height: 32px;
      padding: 6px 12px;
      border: 1px solid var(--borderColor);
      border-radius: 4px;
      font-size: 14px;
      
      &:focus {
        border-color: var(--primary);
        outline: none;
      }
    }

    &.code-item {
      display: flex;
      gap: 12px;

      input {
        flex: 1;
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;

    button {
      height: 32px;
      padding: 6px 12px;
      border: 1px solid var(--borderColor);
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;

      &:last-child {
        background-color: var(--primary);
        color: var(--text-color);
        border-color: var(--primary);
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.verify-code-btn {
  border: 1px solid var(--borderColor);
  border-radius: 4px;
  color: var(--text-color);
  font-size: 14px;
  height: 32px;
  padding: 6px 12px;
  cursor: pointer;

  &:hover:not(:disabled) {
    border-color: var(--primary);
    color: var(--primary);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 编辑姓名弹窗样式
.edit-name-dialog {
  .cxd-Modal-content {
    padding: 0;
  }

  .cxd-Modal-header {
    padding: 16px 24px;
    border-bottom: none;

    .cxd-Modal-title b {
      font-size: 14px;
      color: var(--text-color);
    }
  }

  .edit-name-form {
    padding: 0 24px;

    .name-input {
      width: 100%;
      height: 40px;
      padding: 10px;
      border: 1px solid var(--borderColor);
      border-radius: 4px;
      font-size: 14px;
      color: var(--text-color);

      &::placeholder {
        color: var(--text--muted-color);
      }

      &:focus {
        border-color: var(--primary);
        outline: none;
      }
    }
  }

  .cxd-Modal-footer {
    padding: 16px 24px;
    border-top: none;
    margin-top: 0;

    .cancel-btn {
      height: 32px;
      padding: 6px 21px;
      border: 1px solid var(--borderColor);
      border-radius: 4px;
      font-size: 14px;
      color: var(--text-color);
      margin-right: 8px;

      &:hover {
        border-color: var(--primary);
        color: var(--primary);
      }
    }

    .confirm-btn {
      height: 32px;
      padding: 6px 21px;
      background: var(--primary);
      border-radius: 4px;
      font-size: 14px;
      color: var(--text-color);
      border: none;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.cxd-Tabs-pane,
.dark-Tabs-pane {
  padding: 0;
}

.pageTabsLink {
  .pageTabsLink-tabsTitle {
    margin-top: 20px;
  }
}
