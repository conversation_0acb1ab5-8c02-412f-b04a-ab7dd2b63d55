.amis-icons-showcase {
  padding: 20px;
  padding-top: 40px;
  .page-title {
    font-size: 24px;
    margin-bottom: 30px;
    color: #333;
  }

  .search-container {
    margin-bottom: 20px;
    
    .search-input {
      width: 100%;
      max-width: 300px;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
      }
    }
  }

  .icon-group {
    margin-bottom: 40px;

    .group-header {
      cursor: pointer;
      user-select: none;
      
      &:hover .group-title {
        color: #1890ff;
      }
    }

    .group-title {
      font-size: 18px;
      margin-bottom: 20px;
      color: #666;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
      display: flex;
      align-items: center;
      
      .expand-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        transition: transform 0.3s ease;
        
        &.expanded {
          transform: rotate(90deg);
        }
      }
    }

    .icon-grid-container {
      overflow: hidden;
      max-height: 0;
      transition: max-height 0.3s ease-in-out;
      
      &.expanded {
        max-height: 2000px;
      }
    }

    .icon-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 20px;
    }

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 4px;
      transition: all 0.3s;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
        background-color: rgba(0, 0, 0, 0.05);
      }

      .icon-display {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;

        .amis-icon {
          width: 16px;
          height: 16px;
          color: #666;
          transform: scale(2); // 添加 2 倍缩放
          transform-origin: center; // 确保从中心点缩放
        }
      }

      .icon-name {
        font-size: 12px;
        color: #999;
        text-align: center;
        word-break: break-all;
      }
    }
  }
}

.search-container {
  margin: 20px 0;
  padding: 0 20px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 4px;
  outline: none;

  &:focus {
    border-color: #1a7ced;
    box-shadow: 0 0 0 2px rgba(26, 124, 237, 0.2);
  }
}
