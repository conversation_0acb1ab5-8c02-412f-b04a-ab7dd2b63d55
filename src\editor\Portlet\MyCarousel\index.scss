// 添加以下样式到您的SCSS文件中

.carousel-data-box {
  background-color: #fff;
  border-radius: 4px;
  padding: 0;
  width: 100%;
}

.carousel-items-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}


.carousel-items-list {
  width: 100%;
  
  .carousel-item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:0 10px;
    border-radius: 2px;
    border: 1px solid #E8E9EB;
    margin-bottom: 12px;
    
    &:hover {
      background-color: #f9f9f9;
    }
    
    .carousel-item-info {
      display: flex;
      align-items: center;

      
      .drag-handle {
        color: #ccc;
        cursor: move;
      }
      
      .item-title {
        font-size: 14px;
      }
    }
    
    .carousel-item-actions {
      display: flex;
      
      button {
        color: #666;
        
        &:hover {
          color: #108cee;
        }
      }
    }
  }
}

.carousel-footer {
  display: flex;
  gap: 10px;
  
  button {
    flex: 1;
    padding: 10px 0;
    border: 1px solid #ddd;
    background-color: #fff;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
}


