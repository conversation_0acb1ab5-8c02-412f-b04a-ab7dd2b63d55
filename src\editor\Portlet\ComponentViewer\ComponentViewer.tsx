import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {showRenderers} from '../../../editor/disabledRenderers';

export class ComponentViewerPlugin extends BasePlugin {
  static id = 'ComponentViewerPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'component-viewer';
  $schema = '/schemas/ComponentViewerSchema.json';

  // 组件基本信息
  name = '组件配置查看器';
  panelTitle = '组件配置查看器';
  icon = 'fa fa-list';
  panelIcon = 'fa fa-list';
  pluginIcon = 'fa fa-list';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于查看所有组件的配置项';
  docLink = '/amis/zh-CN/components/component-viewer';
  tags = ['工具'];

  // 组件默认配置
  scaffold = {
    type: 'component-viewer',
    label: '组件配置查看器',
    selectedComponent: ''
  };

  // 预览界面
  previewSchema = {
    type: 'component-viewer',
    label: '组件配置查看器'
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    console.log("getSchemaTpl('title'",getSchemaTpl('title', {
      name: 'label',
      label: '标题',
      value: '组件配置查看器',
      description: '组件标题通过getSchemaTpl配置'
    }));

    console.log("getSchemaTpl('pageTitle'",getSchemaTpl('pageTitle', {
      name: 'label',
      label: '标题',
      value: '组件配置查看器',
      description: '组件标题通过getSchemaTpl配置'
    }));
    return getSchemaTpl('tabs', [
      // {
      //   title: '常规',
      //   body: [
      //     getSchemaTpl('title', {
      //       name: 'label',
      //       label: '标题title',
      //       value: '组件配置查看器',
      //       description: '组件标题通过getSchemaTpl配置'
      //     }),
      //     getSchemaTpl('pageTitle', {
      //       name: 'label',
      //       label: '标题pageTitle',
      //       value: '组件配置查看器',
      //       description: '组件标题通过getSchemaTpl配置'
      //     }),
      //     getSchemaTpl(
      //       'collapseGroup',
      //       [
      //         // getSchemaTpl('collapse'),
      //         {
      //           title: '基本',
      //           body: [
      //             {
      //               type: 'input-text',
      //               name: 'label',
      //               label: '标题',
      //               value: '组件配置查看器',
      //               description: '组件标题'
      //             }
      //           ]
      //         }
      //       ],
      //       {...context?.schema, configTitle: 'props'}
      //     )
      //   ]
      // }
    ]);
  };
}

// 注册插件
registerEditorPlugin(ComponentViewerPlugin);
