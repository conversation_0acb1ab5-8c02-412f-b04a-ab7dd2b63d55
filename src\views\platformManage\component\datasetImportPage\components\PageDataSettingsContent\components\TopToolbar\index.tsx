import React from 'react';
import AMISRenderer from '@/component/AMISRenderer';
import './index.scss';

export interface TopToolbarProps {
  dataSetInfo: any;
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;
  };
}

const TopToolbar: React.FC<TopToolbarProps> = ({ dataSetInfo, initPathItem }) => {
  return (
    <div className="top-toolbar">
      <AMISRenderer 
        schema={{
          type: 'form',
          initApi: `/admin-api/system/data-set/get-toolbar-config/${dataSetInfo?.id}`,
          api: `/admin-api/system/data-set/update-toolbar-config/${dataSetInfo?.id}`,
          body: [
            {
              type: 'switch',
              name: 'enableAdd',
              label: '显示新增按钮',
              onText: '是',
              offText: '否'
            },
            {
              type: 'switch',
              name: 'enableExport',
              label: '显示导出按钮',
              onText: '是',
              offText: '否'
            },
            {
              type: 'switch',
              name: 'enableColumnToggler',
              label: '显示列设置',
              onText: '是',
              offText: '否'
            }
          ]
        }}
      />
    </div>
  );
};

export default TopToolbar; 