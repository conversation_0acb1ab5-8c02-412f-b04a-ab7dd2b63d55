/**
 * 数据转换示例
 */

import { transformControlDictToTree } from './dataTransform';

// 示例数据（基于你提供的数据格式）
const exampleResponse = {
  "code": 0,
  "data": {
    "list": [
      {
        "applicationId": null,
        "type": 1,
        "classificationId": 4,
        "defaultClassificationId": 4,
        "classificationName": "展示",
        "id": 240,
        "fieldType": "icon-show",
        "controlType": "图标展示",
        "amisControlType": "icon-show",
        "classification": null,
        "json": "{\"type\":\"icon-show\"}",
        "status": 1,
        "sort": null,
        "createTime": 1753260028000
      },
      {
        "applicationId": null,
        "type": 1,
        "classificationId": 4,
        "defaultClassificationId": 4,
        "classificationName": "展示",
        "id": 239,
        "fieldType": "plain",
        "controlType": "纯文本",
        "amisControlType": "plain",
        "classification": null,
        "json": "{\"type\":\"plain\"}",
        "status": 1,
        "sort": null,
        "createTime": 1753260028000
      },
      {
        "applicationId": null,
        "type": 1,
        "classificationId": 5,
        "defaultClassificationId": 5,
        "classificationName": "输入",
        "id": 241,
        "fieldType": "input-text",
        "controlType": "文本输入",
        "amisControlType": "input-text",
        "classification": null,
        "json": "{\"type\":\"input-text\"}",
        "status": 1,
        "sort": null,
        "createTime": 1753260028000
      }
    ]
  }
};

// 使用示例
export function exampleUsage() {
  const result = transformControlDictToTree(exampleResponse);
  
  console.log('转换结果：', JSON.stringify(result, null, 2));
  
  // 期望输出：
  // [
  //   {
  //     "label": "系统应用",
  //     "children": [
  //       {
  //         "label": "展示",
  //         "children": [
  //           {
  //             "label": "图标展示",
  //             "value": "240"
  //           },
  //           {
  //             "label": "纯文本",
  //             "value": "239"
  //           }
  //         ]
  //       },
  //       {
  //         "label": "输入",
  //         "children": [
  //           {
  //             "label": "文本输入",
  //             "value": "241"
  //           }
  //         ]
  //       }
  //     ]
  //   }
  // ]
  
  return result;
}

// 如果你需要直接运行示例
// exampleUsage();