.pageReleaseBox {
  position: relative;
  width: 100%;
  padding: 1.5rem;
  display: flex;

  &-aside {
    width: 13.5rem;
    border-right: 1px solid var(--borderColor);

    &-item {
      width: 12.5rem;
      height: 2.5rem;
      border-radius: 0.5rem;
      margin: 0 0.5rem;
      display: flex;
      align-items: center;
      cursor: pointer;
      padding-left: 1rem;

      span {
        color: var(--text-color);
        font-size: 0.875rem;
      }

      &.active {
        background-color: var(--light-bg);
      }
    }
  }

  &-content {
    flex: 1;
    min-height: 80vh;
    padding: 0 1.5rem;

    h2 {
      font-size: 1.25rem;
      color: var(--text-color);
      margin-bottom: 0.75rem;
    }

    p {
      font-size: 0.875rem;
      color: var(--text--muted-color);
      margin-bottom: 1.5rem;
    }

    .address-container {
      display: flex;
      align-items: center;
      justify-content: flex-start; /* 修改为从左排列 */
      gap: 10px; /* 调整元素之间的间距 */
      margin-top: 20px;

      label {
        font-size: 0.875rem;
        color: var(--text-color);
        margin-bottom: 0;
      }

      input {
        flex: 1;
        height: 2rem;
        background-color: var(--light-bg);
        font-size: 0.875rem;
        color: var(--text-color);
        border: none;
        outline: none;
        padding: 0 1rem;

        ::placeholder {
          color: var(--text--muted-color);
          font-size: 0.875rem;
        }
      }

      button {
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 2rem;
        border-radius: 0.5rem;
        border: 1px solid var(--borderColor);
        color: var(--text-color);
        font-size: 0.875rem;
        background-color: transparent;

        &:hover {
          opacity: 0.8;
          background-color: var(--light-bg);
        }
      }
    }
  }
}
