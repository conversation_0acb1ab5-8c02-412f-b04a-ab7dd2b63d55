// 应用图标组件样式
.input-appicon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.3s;

  // 标题样式（"应用图标"这个标题）
  .input-appicon-title {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
    transition: all 0.3s;
  }

  .input-appicon {
    margin: 16px 16px 16px 0;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;

    // 确保背景图片能正常显示
    &[style*="background-image"] {
      background-color: transparent !important;
    }

    // 悬停效果
    &:hover {
      opacity: 0.8;
      transform: scale(1.02);
    }

    .input-appicon-plus {
      font-size: 28px;
      color: #999;
      transition: all 0.3s;
    }

    .input-appicon-add-btn {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      // 悬停效果
      &:hover {
        opacity: 0.8;
      }
    }
  }

  .input-appicon-display {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  // 静态模式样式
  .input-appicon-static {
    transition: all 0.3s;

    &:hover {
      opacity: 0.9;
    }
  }
}

// 水平布局模式
.input-appicon-wrapper.horizontal {
  .input-appicon-display {
    flex-direction: row;
    align-items: center;

    .input-appicon-title {
      margin-bottom: 0;
      margin-right: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .input-appicon-wrapper {
    .input-appicon {
      margin: 8px 8px 8px 0;
    }

    .input-appicon-title {
      font-size: 13px;
    }
  }
}