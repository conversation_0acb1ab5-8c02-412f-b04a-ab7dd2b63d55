import React, {useEffect, useState, useCallback} from 'react';
import <PERSON><PERSON><PERSON>ender<PERSON> from '@/component/AMISRenderer';
import {
  listAllControlDicts,
  listAllDicts,
  GetControlDictPage
} from '@/utils/api/api';
import {
  getComponentClassifications,
  fetchAndTransformComponentClassifications
} from '@/utils/dataTransform';

interface AmisFieldTableProps {
  itemsList: any[];
  onDataChange?: (data: any) => void;
  disabledType?: boolean;
  dataSetdetail?: any;
  match: any;
}

const AmisFieldTable: React.FC<AmisFieldTableProps> = ({
  itemsList,
  onDataChange,
  disabledType,
  dataSetdetail,
  match
}) => {
  // 状态管理集中在一起
  const [schema, setSchema] = useState<any>({});
  const [schemaKey, setSchemaKey] = useState(0);
  const [controlTypeList, setControlTypeList] = useState<any>([]);
  const [typeList, setTypeList] = useState<any>([]);
  const [itemsListData, setItemsListData] = useState<any>([]);
  const [shouldUpdateSchema, setShouldUpdateSchema] = useState(false);
  const [componentClassificationOptions, setComponentClassificationOptions] = useState<any>([]);
  const readonlyFieldList = [
    'create_time',
    'update_time',
    'creator',
    'updater',
    'creatTime',
    'updateTime'
  ];
  const idFieldList = ['id'];


  // 方法集中在一起
  // 获取控件字典数据
  const handlelistAllControlDicts = useCallback(() => {
    listAllControlDicts().then(res => {
      if (res.code == 0) {
        setControlTypeList(res.data);
      }
    });
  }, []);

  // 获取控件字典数据
  const handlelistAllDicts = useCallback(() => {
    listAllDicts().then(res => {
      if (res.code == 0) {
        setTypeList(res.data.list);
      }
    });
  }, []);

  // 获取组件分类数据
  const fetchComponentClassifications = useCallback(async (componentClassifications: any[], applicationId?: string | null) => {
    try {
      const results = await fetchAndTransformComponentClassifications(
        componentClassifications,
        (params) => GetControlDictPage({
          ...params,
          ...(applicationId && params.type === 3 ? { applicationId } : {})
        })
      );
      console.log('results', results);
      setComponentClassificationOptions(results);
    } catch (error) {
      console.error('获取组件分类数据失败：', error);
      setComponentClassificationOptions([]);
    }
  }, []);

  // 更新数据并触发重新渲染
  const updateTableData = useCallback(
    (newData: any[]) => {
      setItemsListData(newData);
      if (onDataChange) {
        onDataChange(newData);
      }
      // setShouldUpdateSchema(true);
    },
    [onDataChange]
  );

  // 处理初始数据加载
  useEffect(() => {
    if (controlTypeList.length === 0) {
      handlelistAllControlDicts();
    }
    if (typeList.length === 0) {
      handlelistAllDicts();
    }

    // 安全获取applicationId，避免崩溃
    const getApplicationId = () => {
      try {
        const hash = window.location.hash;
        if (!hash) return null;

        const pathSegments = hash.split('/');
        if (pathSegments.length < 2) return null;

        const segment = pathSegments[1];
        if (!segment || !segment.includes('app')) return null;

        return segment.replace('app', '');
      } catch (error) {
        console.error('Error extracting applicationId:', error);
        return null;
      }
    };

    const applicationId = getApplicationId();
    console.log('applicationId', applicationId);

    // 根据applicationId条件初始化分类数组
    const componentClassifications = getComponentClassifications(applicationId);

    console.log('componentClassifications', componentClassifications);
    
    // 获取对应分类数据
    if (componentClassifications.length > 0) {
      fetchComponentClassifications(componentClassifications, applicationId);
    }

    if (itemsList.length > 0 && controlTypeList.length > 0) {
      const newItemsList = itemsList.map((item: any) => {
        if (item?.controlId) {
          return item;
        } else {
          return {
            ...item,
            // fieldType: item.columnType,
            controlId: ''
          };
        }
      });
      setItemsListData(newItemsList);
      setShouldUpdateSchema(true);
    }
  }, [
    itemsList,
    controlTypeList,
    handlelistAllControlDicts,
    typeList,
    handlelistAllDicts,
    fetchComponentClassifications
  ]);

  // 处理 schema 更新
  useEffect(() => {
    console.log('page');
    if (shouldUpdateSchema && itemsListData.length > 0) {
      // 增加 schemaKey 以强制重新渲染
      const newSchemaKey = schemaKey + 1;

      const columns = [
        {
          label: '字段值',
          name: 'columnName',
          sortable: true,
          type: 'text',
          width: 180
        },
        {
          label: '字段名称',
          name: 'columnComment',
          sortable: true,
          type: 'input-text',
          width: 180,
          disabled: disabledType,
          onChange: (value: string, old: string, row: any) => {
            if (old !== 'columnComment') {
              const index = row.rowIndex;
              // 使用 map 方法创建新的数组和对象
              const newData = itemsListData.map((item: any, idx: number) => {
                if (idx === index) {
                  return {
                    ...item,
                    columnComment: value
                  };
                }
                return item;
              });

              // 使用 updateTableData 方法更新数据并触发回调
              updateTableData(newData);
            }
          },
          onBlur: () => {
            setShouldUpdateSchema(true);
          }
        },
        {
          label: '字段类型',
          name: 'columnType',
          sortable: true,
          type: 'text',
          width: 180
        },
        {
          label: '控件类型',
          name: 'controlId',
          sortable: true,
          type: 'select',
          "selectMode": "chained",
          searchable: true,
          disabled: disabledType,
          options: componentClassificationOptions,
          onChange: (value: string, old: string, row: any) => {
            // 更新 itemsListData 中对应行的 controlId
            if (old !== 'controlId') {
              const index = row.rowIndex;
              console.log('itemsListData', itemsListData);
              const newData = itemsListData.map((item: any, idx: number) => {
                if (idx === index) {
                  return {
                    ...item,
                    controlId: value,
                    fieldType: controlTypeList.find(
                      (control: any) => control.id === value
                    )?.fieldType
                  };
                }
                return item;
              });

              updateTableData(newData);
              setShouldUpdateSchema(true);
            }
          }
          // ,
          // onBlur: () => {
          //   setShouldUpdateSchema(true);
          // }
        },
        // {
        //   label: '字段名称',
        //   name: 'columnNametmp',
        //   type: 'formula',
        //   formula: '${columnName=="id"}'
        // },
        {
          label: '只读',
          name: 'isRead',
          type: 'checkbox',
          // disabled: '${columnNametmp}',
          debug: true,
          onChange: (value: string, old: string, row: any) => {
            const index = row.rowIndex;
            const newData = itemsListData.map((item: any, idx: number) => {
              if (idx === index) {
                return {
                  ...item,
                  isRead: value ? 1 : 0
                };
              }
              return item;
            });

            updateTableData(newData);
            setShouldUpdateSchema(true);
          }
        },
        {
          label: '新增',
          name: 'isCreate',
          type: 'checkbox',
          // disabled: (row: any) => idFieldList.includes(row.columnName),
          onChange: (value: string, old: string, row: any) => {
            const index = row.rowIndex;
            const newData = itemsListData.map((item: any, idx: number) => {
              if (idx === index) {
                return {
                  ...item,
                  isCreate: value ? 1 : 0
                };
              }
              return item;
            });

            updateTableData(newData);
            setShouldUpdateSchema(true);
          }
        },
        {
          label: '编辑',
          name: 'isUpdate',
          type: 'checkbox',
          // disabled: (row: any) => idFieldList.includes(row.columnName),
          onChange: (value: string, old: string, row: any) => {
            const index = row.rowIndex;
            const newData = itemsListData.map((item: any, idx: number) => {
              if (idx === index) {
                return {
                  ...item,
                  isUpdate: value ? 1 : 0
                };
              }
              return item;
            });

            updateTableData(newData);
            setShouldUpdateSchema(true);
          }
        }
      ];

      // 构建schema对象
      const newSchema = {
        type: 'page',
        title: '表数据',
        key: newSchemaKey,
        regions: ['body'],
        pullRefresh: {
          disabled: true
        },
        syncLocation: false,
        api: false, // 不需要远程加载数据
        loadDataOnce: true, // 一次性加载
        body: [
          {
            type: 'crud',
            syncLocation: false,
            autoGenerateFilter: true,
            autoRefresh: true,
            bulkActions: [],
            itemActions: [],
            __filterColumnCount: 3,
            __features: [],
            __LastFeatures: [],
            __step: 0,
            columns: columns,
            id: 'u:7db41ebb7f3b',
            perPageAvailable: [5, 10, 20, 50, 100],
            messages: {},
            pageField: 'pageNo',
            perPageField: 'pageSize',
            alwaysShowPagination: true,
            footerToolbar: [
              {
                type: 'statistics'
              },
              {
                type: 'pagination'
              },
              {
                type: 'switch-per-page',
                tpl: '切换页码',
                wrapperComponent: '',
                id: 'u:a3243b41e34c'
              }
            ]
          }
        ],
        __editorStatebaseControlClassName: 'default',
        __editorStatebodyControlClassName: 'default'
      };

      setSchemaKey(newSchemaKey);
      setSchema(newSchema);
      setShouldUpdateSchema(false);
    }
  }, [
    shouldUpdateSchema,
    itemsListData,
    controlTypeList,
    componentClassificationOptions,
    disabledType,
    schemaKey,
    onDataChange,
    updateTableData
  ]);

  // 渲染
  return <AMISRenderer schema={schema} data={{items: itemsListData}} />;
};

export default AmisFieldTable;
