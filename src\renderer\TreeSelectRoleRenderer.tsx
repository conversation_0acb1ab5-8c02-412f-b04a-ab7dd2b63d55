import React from 'react';
import {Renderer, TreeSelection} from 'amis';
import {FormControlProps} from 'amis-core';

export interface RoleTreeSelectorProps extends FormControlProps {
  label?: string;
  name: string;
  clearable?: boolean;
  placeholder?: string;
  value?: string;
  static?: boolean;
}

@Renderer({
  type: 'role-select',
  name: 'role-select'
})
export class RoleTreeSelectRenderer extends React.PureComponent<RoleTreeSelectorProps> {
  state = {
    options: JSON.parse(localStorage.getItem('roleList') || '[]'),
    loading: false
  };

  constructor(props: RoleTreeSelectorProps) {
    super(props);

    // 如果localStorage中没有数据，尝试获取
    if (
      !localStorage.getItem('roleList')) {
      this.fetchOptions();
    }
  }

  // // 扁平化选项，便于快速查找
  flattenOptions = (options: any[] = []): Record<string, string> => {
    const result: Record<string, string> = {};

    const process = (items: any[] = []) => {
      items.forEach(item => {
        if (item.id) {
          result[item.id.toString()] =
            item.name || item.nickname || item.label || '未命名';
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          process(item.children);
        }
      });
    };

    process(options);
    return result;
  };

  fetchOptions() {
    const {source, env} = this.props as any;

    if (!source || !env) {
      return;
    }

    this.setState({loading: true, error: null});

    env
      .fetcher(source, this.props.data)
      .then(this.handleFetchSuccess)
      .catch(this.handleFetchError);
  }

  handleFetchSuccess = (response: any) => {
    let options = response.data || [];

    // 保存到localStorage
    localStorage.setItem('roleList', JSON.stringify(options));
    
    this.setState(
      {
        options,
        loading: false
      },
      () => {
        this.forceUpdate();
      }
    );
  };

  handleFetchError = () => {
    this.setState({
      loading: false
    });
  };
  
  // 通过扁平化选项快速查找ID对应的标签
  findLabelById = (id: string | number): string => {
    const {options} = this.state;
    const item = options.find((opt: any) => opt.id == id);
    return item ? item.name : id.toString();
  };

  processComplexValue = (value: any): string => {
    if (!value) return '--';

    // 处理字符串类型
    if (typeof value === 'string') {
      // 如果包含逗号，按逗号分割处理
      if (value.includes(',')) {
        return value
          .split(',')
          .map(v => this.processComplexValue(v.trim()))
          .filter(Boolean)
          .join(',');
      }

      // 简单ID值，直接查找
      return this.findLabelById(value);
    }

    // 兜底，直接返回字符串化的值
    return String(value);
  };

  render() {
    const {
      static: isStatic,
      name,
      label,
      placeholder,
      enableNodePath,
      render
    } = this.props as any;

    const {options, loading} = this.state;
    const finalOptions = options || this.props.options;

    // 安全地获取值，不修改原始数据
    const {data = {}} = this.props;
    const realValue = data[name]; // 从表格行数据中取值

    // 处理值但不修改原始data对象
    const displayValue = this.processComplexValue(realValue);

    // 处理静态展示
    if (isStatic) {
      // 如果有render函数，使用它渲染
      if (render) {
        return render('static', {
          type: 'static',
          label,
          name,
          tpl: displayValue
        });
      }

      // 备用渲染方法：直接使用div
      return (
        <div className="static-value">
          <div className="static-label">{label || name}</div>
          <div className="static-content">{displayValue}</div>
        </div>
      );
    }

    if (finalOptions) {
      // 处理 themeCss，将 selectControlClassName 映射到 tree-select 组件
      const {themeCss} = this.props as any;
      let processedThemeCss = themeCss;

      if (themeCss && themeCss.selectControlClassName) {
        processedThemeCss = {
          ...themeCss,
          selectControlClassName: themeCss.selectControlClassName
        };
      }

      const treeProps = {
        ...this.props,
        type: 'tree-select',
        options: finalOptions || [],
        onlyLeaf: true,
        joinValues: true,
        enableNodePath,
        extractValue: true,
        hideRoot: true,
        placeholder,
        loading,
        themeCss: processedThemeCss
      };

      if (render) {
        return render('role-select', treeProps);
      }
      return <TreeSelection {...treeProps} />;
    } else {
      return <></>;
    }
  }
}
