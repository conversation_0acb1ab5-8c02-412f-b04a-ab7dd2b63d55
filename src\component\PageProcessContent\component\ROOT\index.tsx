import React, {<PERSON>} from 'react';
import './index.scss';
import root_icon from './image/root_icon.png';

const ROOT: FC<any> = (props: any) => {
  console.log('ROOT props', props.item);
  return (
    <div className="process_root">
      <div className="process_root-info">
        <div className="process_root-info-box">
          <img className="process_root-info-box-icon" src={root_icon} />
        </div>

        <div className="process_root-info-text">
          <div className="process_root-info-text-name">{props.item.name}</div>
          <div className="process_root-info-text-desc">{props.item.desc}</div>
        </div>

        <div className="process_root-info-member">
          {props.item.props.assignedUser.map((userItem: any) => {
            return (
              <div className="process_root-info-member-item" key={userItem.id}>
                <div className="process_root-info-member-item-header">
                {userItem.avatar ? (
                    <img
                      className="process_cc-info-member-item-header-img"
                      src={userItem.avatar}
                    />
                  ) : (
                    <span>{userItem.name}</span>
                  )}
                </div>
                <div className="process_root-info-member-item-name">{userItem.name}</div>
              </div>
            );
          })}
        </div>
      </div>
      <div className="process_root-line"></div>
    </div>
  );
};

export default ROOT;
