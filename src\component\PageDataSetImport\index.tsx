import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {
  findCreateBody,
  replacedCreateBody,
  replacedUpdateBody,
  replacedcolumns,
  replacedViewBody
} from '@/utils/schemaDataSet/commonEditor';
import {
  getFormDataPage //获取表单数据
} from '@/utils/api/api';
// 组件：页面设置
import PageDataSettings from '@/component/PageDataSettings/index';
import PageSettings from '@/component/PageSettings/index';

const PageContent: FC<any> = (props: any) => {
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('manage');
  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };
  
  const [pageData, setPageData] = React.useState<any>({});

  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsForm-tabsToolbar"></div>;
  };

  // 表单预览的schema
  const formPreviewSchema = (schema: any) => {
    let data = {
      type: 'page',
      regions: ['body'],
      pullRefresh: {
        disabled: true
      },
      body: findCreateBody(schema)
    };
    return data;
  };

  const editForm = () => {
    const appId = props.computedMatch.params.appId;
    const form = props.computedMatch.params.form;
    const pageId = props.pageData.id;
    
    if (!appId || !form || !pageId) {
      toast.error('缺少必要的参数');
      return;
    }
    
    props.history.push(
      `/app${appId}/design/${form}?editCode=${pageId}`
    );
  };
    // 获取表单数据 pageData
    const handleGetFormDataPage = () => {
      let data = {
        applicationPageId: props.pageData.id,
        pageNo: 1,
        pageSize: 10
      };
      getFormDataPage(data).then((res: any) => {
        if (res.code == 0) {
          if (res.data.list.length > 0) {
            if (res.data.list[0]) {
              // toast.success('获取表单数据成功');
              res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
              res.data.list[0].pageType = 13;
              res.data.list[0].appId = props.computedMatch.params.appId;
              setPageData(res.data.list[0]);
            }else{
              toast.success('暂无表单数据');
            }
          }else{
            toast.success('暂无表单数据');
          }
        } else {
          toast.error(res.msg);
        }
      });
    };

  React.useEffect(() => {
    if (props.pageData?.id) {
      handleGetFormDataPage();
    }
    // 只有在admin模式下才处理tab切换逻辑
    if (props.match.params.playType == 'admin') {
      const urlParams = new URLSearchParams(props.history.location.search);
      let tabKey = urlParams.get('activeTabKey') || 'manage';
      setActiveKey(tabKey);
    }
  }, [props.pageData?.id, props.history.location, props.match.params.playType]);


  return (
    <div className="pageBox">
      {props.match.params.playType == 'admin' && <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-6">
            <Button size="lg" level="default">
              访问
            </Button>
          </div>
          <div className="pl-1">
            <Button size="lg" level="default">
              生成数据页
            </Button>
          </div>
          <div className="pl-1">
            <Button size="lg" level="primary" onClick={() => editForm()}>
              编辑表单
            </Button>
          </div>
        </div>
      </div>}
      <div className="pageTabsForm">
        {props.match.params.playType == 'admin' ? (
          // admin模式：显示完整的tab标签栏
          <Tabs
            mode="line"
            onSelect={(key: any) => handleTabChange(key)}
            toolbar={TabsToolbar()}
            linksClassName="pageTabsForm-tabsTitle"
            activeKey={activeKey}
          >
            <Tab title="数据管理" eventKey="manage">
              <div className="pageTabsForm-tabsContent">
                <AMISRenderer
                  schema={(pageData.data)}
                />
              </div>
            </Tab>
            <Tab title="数据页设置" eventKey="dataset">
              <PageDataSettings
                pageData={pageData}
                history={props.history}
                store={props.store}
                update={() => {
                  props.updatePage();
                  handleGetFormDataPage();
                }}
              />
            </Tab>
            <Tab title="页面设置" eventKey="setting">
              <PageSettings
              history={props.history}
                pageData={props.pageData}
                store={props.store}
                update={() => {
                  props.updatePage();
                }}
              />
            </Tab>
            <Tab title="页面发布" eventKey="publish"></Tab>
            <Tab title="表单预览" eventKey="preview">
              <div className="pageTabsForm-tabsContent">
                <div className="pageTabsForm-tabsContent-form">
                  <AMISRenderer
                    schema={formPreviewSchema(pageData.data)}
                    embedMode={true}
                  />
                </div>
              </div>
            </Tab>
          </Tabs>
        ) : (
          // 非admin模式：只显示数据管理内容，不显示tab标签栏
          <div className="pageTabsForm-tabsContent">
            <AMISRenderer
              schema={(pageData.data)}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PageContent;
