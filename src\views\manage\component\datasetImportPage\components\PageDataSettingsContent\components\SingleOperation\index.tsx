import React from 'react';
import AMISRenderer from '@/component/AMISRenderer';
import './index.scss';

interface SingleOperationProps {
  dataSetInfo: any;
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;  // 为其他可能的属性添加索引签名
  };
}

const SingleOperation: React.FC<SingleOperationProps> = ({ dataSetInfo,initPathItem }) => {
  return (
    <div className="single-operation">
      <AMISRenderer 
        schema={{
          type: 'form',
          initApi: `/admin-api/system/data-set/get-row-operation/${dataSetInfo?.id}`,
          api: `/admin-api/system/data-set/update-row-operation/${dataSetInfo?.id}`,
          body: [
            {
              type: 'switch',
              name: 'enableView',
              label: '启用查看',
              onText: '是',
              offText: '否'
            },
            {
              type: 'switch',
              name: 'enableEdit',
              label: '启用编辑',
              onText: '是',
              offText: '否'
            },
            {
              type: 'switch',
              name: 'enableDelete',
              label: '启用删除',
              onText: '是',
              offText: '否'
            }
          ]
        }}
      />
    </div>
  );
};

export default SingleOperation; 