import React, {useState} from 'react';
import {RouteComponentProps} from 'react-router-dom';
import {Button, Input, Link, toast} from 'amis-ui';
import './index.scss';

export default function ForgetPassword({history}: RouteComponentProps) {
  const [username, setUsername] = useState('');

  return (
    <div className="forget-password-page">
      <div className="form-content">
        <div className="page-title">找回密码</div>
        <div className="form-item">
          <Input
            maxLength={32}
            value={username}
            onChange={e => setUsername(e.target.value)}
            placeholder="请输入手机号/邮箱"
            className="form-control"
          />
        </div>

        <Button
          type="submit"
          level="primary"
          className="next-step-btn"
          block
          onClick={() => {
            if (!username) {
              toast.error('请输入手机号/邮箱');
              return;
            }

            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(username) && !/^1\d{10}$/.test(username)) {
              toast.error('请输入正确的手机号/邮箱');
              return;
            }

            // TODO: 验证手机号/邮箱格式
            history.push(`/forget-password/verify-code?username=${username}`
            );
          }}
        >
          下一步
        </Button>

        <div className="login-divider">或</div>

        <div className="bottom-links">
          <Link
            onClick={() => {
              history.push(`/login?isRegister=true`
              );
            }}
          >
            注册账号
          </Link>
          <Link onClick={() => history.push('/login')}>登录已有账号</Link>
        </div>
      </div>
    </div>
  );
}
