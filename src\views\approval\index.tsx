import React, {FC} from 'react';
import './index.scss';
import AMISRenderer from '@/component/AMISRenderer';
import {Tabs, Tab} from 'amis';
import cityCodeUtil from '@/utils/cityCodeUtil/cityCodeUtil';
const approval: FC<any> = (props: any) => {
  /* data 数据 */
  const menuList = [
    {
      title: '全部',
      path: `/app${props.applyData.id || ''}/admin/approval/allapprovals`,
      url: '',
      api: '/admin-api/wflow/process/submittedList'
    },
    {
      title: '待我处理',
      path: `/app${props.applyData.id || ''}/admin/approval/pending`,
      url: `${
        process.env.NODE_ENV === 'development'
          ? 'http://localhost:88/#/workspace/unfinished'
          : 'https://javaddm.fjpipixia.com/wflow/#/workspace/unfinished'
      }`,
      api: '/admin-api/wflow/process/task/todoList'
    },
    {
      title: '我已处理',
      path: `/app${props.applyData.id || ''}/admin/approval/transactors`,
      url: `${
        process.env.NODE_ENV === 'development'
          ? 'http://localhost:88/#/workspace/finished'
          : 'https://javaddm.fjpipixia.com/wflow/#/workspace/finished'
      }`,
      api: '/admin-api/wflow/process/task/idoList'
    },
    {
      title: '我创建的',
      path: `/app${props.applyData.id || ''}/admin/approval/create`,
      url: `${
        process.env.NODE_ENV === 'development'
          ? 'http://localhost:88/#/workspace/submit'
          : 'https://javaddm.fjpipixia.com/wflow/#/workspace/submit'
      }`,
      api: '/admin-api/wflow/process/mySubmitted'
    },
    {
      title: '抄送我的',
      path: `/app${props.applyData.id || ''}/admin/approval/ccusers`,
      url: `${
        process.env.NODE_ENV === 'development'
          ? 'http://localhost:88/#/workspace/cc'
          : 'https://javaddm.fjpipixia.com/wflow/#/workspace/cc'
      }`,
      api: '/admin-api/wflow/process/ccMe'
    }
  ];
  // 选中的path
  const [activeItem, setActiveItem] = React.useState<any>(menuList[0]);

  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState<any>(
    '/platform/approval/ccusers'
  );

  /* methods 方法 */
  // 选中菜单的item.path ,并跳转路由
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsForm-tabsToolbar"></div>;
  };

  const formResult = JSON.parse(localStorage.getItem('formResult') || '[]');

  const formColumns = formResult
    .filter((item: any) => item.name != null && item.name.trim() !== '')
    .map((item: any) => {
      if (item.name === '开关') {
        return {
          name: item.name,
          label: item.name,
          falseValue: 'false',
          trueValue: 'true',
          type: 'switch',
          static: true
        };
      }

      if (item.name === '图片上传') {
        return {
          name: item.name,
          label: item.name,
          type: 'image',
          enlargeAble: true
        };
      }

      if (item.name === 'Excel') {
        return {
          name: item.name,
          label: item.name,
          type: 'tpl',
          tpl: `
            <% if (Array.isArray(data.Excel) && data.Excel.length > 0) { %>
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #f5f5f5;">
                    <% for (let key in data.Excel[0]) { %>
                      <th style="border: 1px solid #ddd; padding: 8px;"><%= key %></th>
                    <% } %>
                  </tr>
                </thead>
                <tbody>
                  <% for (let i = 0; i < data.Excel.length; i++) { %>
                    <tr>
                      <% for (let key in data.Excel[i]) { %>
                        <td style="border: 1px solid #ddd; padding: 8px;"><%= data.Excel[i][key] %></td>
                      <% } %>
                    </tr>
                  <% } %>
                </tbody>
              </table>
            <% } else { %>
            <% } %>
          `
        };
      }

      if (item.name === '城市选择') {
        return {
          name: item.name,
          label: item.name,
          type: 'input-city',
          static: true
        };
      }

      if (item.name === '富文本') {
        return {
          name: item.name,
          label: item.name,
          type: 'tpl'
        };
      }

      if (item.name === '位置选择') {
        return {
          name: item.name,
          label: item.name,
          type: 'text'
        };
      }

      if (item.name === '签名') {
        return {
          name: item.name,
          label: item.name,
          type: 'image',
          enlargeAble: true
        };
      }

      return {
        name: item.name,
        label: item.name,
        type: 'text'
      };
    });

  const columns = [
    {
      name: 'instanceName',
      label: '审批类型',
      type: 'text',
      visible:
        activeItem.title === '我创建的' ||
        activeItem.title === '抄送我的' ||
        activeItem.title === '全部'
    },
    {
      name: 'processDefName',
      label: '审批类型',
      type: 'text',
      visible:
        activeItem.title === '待我处理' || activeItem.title === '我已处理'
    },
    {
      name: 'formAbstracts',
      label: '摘要信息',
      type: 'tpl',
      tpl: `
    <%
      // 先准备好 localStorage 中的 deptFlattenedOptions 和 userFlattenedOptions
      var deptOptions = {};
      var userOptions = {};
      var roleOptions = [];
      var posOptions = [];
      try {
        deptOptions = JSON.parse(localStorage.getItem('deptFlattenedOptions')) || {};
        userOptions = JSON.parse(localStorage.getItem('userFlattenedOptions')) || {};
        roleOptions = JSON.parse(localStorage.getItem('roleList')) || [];
        posOptions = JSON.parse(localStorage.getItem('posList')) || [];
      } catch(e) {
        deptOptions = {};
        userOptions = {};
        roleOptions = [];
        posOptions = [];
      }
    %>
    
    <% if (Array.isArray(data.formAbstracts)) { %>
      <% for (let i = 0; i < data.formAbstracts.length; i++) {
           const abstract = data.formAbstracts[i];
           const value = abstract.value;
           const type = abstract.type;
           const name = abstract.name;
           if (!value) continue;
      %>
    
        <% if (type.startsWith('excel_') && Array.isArray(value) && value.length > 0 && typeof value[0] === 'object') { %>
          <div>
            <strong><%= name %>：</strong>
            <table style="width: 100%; border-collapse: collapse; font-size: 14px; margin-bottom: 12px;">
              <thead>
                <tr style="background: #f5f5f5;">
                  <% for (let key in value[0]) { %>
                    <th style="border: 1px solid #ddd; padding: 8px;"><%= key %></th>
                  <% } %>
                </tr>
              </thead>
              <tbody>
                <% for (let row of value) { %>
                  <tr>
                    <% for (let key in row) { %>
                      <td style="border: 1px solid #ddd; padding: 8px;"><%= row[key] %></td>
                    <% } %>
                  </tr>
                <% } %>
              </tbody>
            </table>
          </div>
    
        <% } else if (type.startsWith('signature_') || type.startsWith('image_') || type.startsWith('avatar_')) { %>
          <div><strong><%= name %>：</strong>
            <a href="<%= value %>" target="_blank">
              <img src="<%= value %>" style="max-width: 100px; max-height: 40px; cursor: pointer;" />
            </a>
          </div>
    
        <% } else if (type.startsWith('file_')) {
              const fileUrl = value;
              const fileName = fileUrl.split('/').pop();
              const fileExt = fileName.split('.').pop().toLowerCase();
        %>
          <div><strong><%= name %>：</strong>
            <% if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)) { %>
              <a href="<%= fileUrl %>" target="_blank">
                <img src="<%= fileUrl %>" style="max-width: 100px; max-height: 40px; cursor: pointer;" />
              </a>
            <% } else { %>
              <a href="<%= fileUrl %>" target="_blank" style="color: blue; text-decoration: underline;">
                <%= fileUrl %>
              </a>
            <% } %>
          </div>
    
        <% } else if (type.startsWith('address_') || type.startsWith('city_')) {
              const addressParts = value.split('|');
              let cityCode = '';
              let detailAddress = '';
              if (addressParts.length >= 1) {
                cityCode = addressParts[0];
              }
              if (addressParts.length === 2) {
                detailAddress = addressParts[1] || '';
              }
              if (addressParts.length > 2) {
                detailAddress = addressParts.slice(1).join('');
              }
        %>
          <div><strong><%= name %>：</strong>
            <div>
              <span class="amis-city-code" data-code="<%= cityCode %>"><%= cityCode %></span>
              <% if (detailAddress) { %>
                <span class="address-separator"> </span>
                <span class="detail-address"><%= detailAddress %></span>
              <% } %>
            </div>
          </div>
    
        <% } else if (type.startsWith('departments_') || type.startsWith('users_')) {
              const paths = value.split(',');
        %>
          <div><strong><%= name %>：</strong>
            <% let displayPaths = [];
              for (let j = 0; j < paths.length; j++) {
                const path = paths[j];
                const ids = path.split('/');
                let deptNames = ids.map(function(id, idx) {
                  return deptOptions[id] || id;
                });
                if (type.startsWith('users_')) {
                  const userId = ids[ids.length - 1];
                  deptNames[deptNames.length - 1] = userOptions[userId] || userId;
                }
                displayPaths.push(deptNames.join(' / '));
              }
            %>
            <div><%= displayPaths.join(', ') %></div>
          </div>
    
        <% } else if (type.startsWith('poss_')) {
              const ids = value.split(',');
              const displayNames = ids.map(function(id) {
                const item = posOptions.find(function(opt) { return opt.id == id; });
                return item ? item.name : id;
              });
        %>
          <div><strong><%= name %>：</strong>
            <div><%= displayNames.join(', ') %></div>
          </div>
    
        <% } else if (type.startsWith('roles_')) {
              const ids = value.split(',');
              const displayNames = ids.map(function(id) {
                const item = roleOptions.find(function(opt) { return opt.id == id; });
                return item ? item.name : id;
              });
        %>

          <div><strong><%= name %>：</strong>
            <div><%= displayNames.join(', ') %></div>
          </div>

                  <% } else if (type.startsWith('appIcon_')) {
                      let appIconData;
                      try {
                        appIconData = JSON.parse(value);
                      } catch (e) {
                        appIconData = { icon: value };
                      }
                      
                      const iconSvg = appIconData.icon || '';
                      const iconColor = appIconData.iconColor || '#000000';
                      const iconBg = appIconData.iconBg || '#f5f5f5';
                  %>
                      <div><strong><%= name %>：</strong>
                        <div style="display: inline-flex; align-items: center; justify-content: center; width: 32px; height: 32px; border-radius: 4px; background-color: <%= iconBg %>; color: <%= iconColor %>;">
                          <%= iconSvg %>
                        </div>
                      </div>

    
        <% } else { %>
          <div><strong><%= name %>：</strong> <%= value %></div>
        <% } %>
    
      <% } %>
    <% } %>
    `
    },

    {
      name: 'staterUser',
      label: '发起人',
      type: 'tpl',
      tpl: `
      <div style="display: flex; align-items: center;">
        <img src="\${staterUser.avatar}" style="width: 24px; height: 24px; border-radius: 50%; margin-right: 8px;" />
        <span>\${staterUser.name}</span>
      </div>
    `,
      visible:
        activeItem.title === '我创建的' ||
        activeItem.title === '抄送我的' ||
        activeItem.title === '全部'
    },

    {
      name: 'owner',
      label: '发起人',
      type: 'tpl',
      tpl: `
      <div style="display: flex; align-items: center;">
        <img src="\${owner.avatar}" style="width: 24px; height: 24px; border-radius: 50%; margin-right: 8px;" />
        <span>\${owner.name}</span>
      </div>
    `,
      visible:
        activeItem.title === '待我处理' || activeItem.title === '我已处理'
    },

    {
      name: 'taskName',
      label: '任务节点名',
      type: 'text',
      visible: activeItem.title === '我已处理'
    },
    {
      name: 'startTime',
      label: '提交时间',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      visible:
        activeItem.title === '我创建的' ||
        activeItem.title === '抄送我的' ||
        activeItem.title === '全部'
    },
    {
      name: 'finishTime',
      label: '结束时间',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      visible:
        activeItem.title === '我创建的' ||
        activeItem.title === '抄送我的' ||
        activeItem.title === '全部'
    },

    {
      name: 'createTime',
      label: `${activeItem.title === '我已处理' ? '任务开始时间' : '提交时间'}`,
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      visible:
        activeItem.title === '待我处理' || activeItem.title === '我已处理'
    },
    {
      name: 'taskCreateTime',
      label: '任务到达时间',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      visible: activeItem.title === '待我处理'
    },

    {
      name: 'taskEndTime',
      label: '处理完成时间',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      visible: activeItem.title === '我已处理'
    },
    {
      name: 'taskName',
      label: '当前节点',
      type: 'text',
      visible: activeItem.title !== '我已处理'
    },
    {
      label: '处理结果',
      type: 'tpl',
      visible: activeItem.title === '我已处理',
      tpl: `
      <% if (data.isFuture) { %>
        <span class="label label-info">等待中...</span>
      <% } else if (data.taskResult === 'agree') { %>
        <span class="label label-success">已同意</span>
      <% } else if (data.taskResult === 'complete') { %>
        <span class="label label-success">已办理</span>
      <% } else if (data.taskResult === 'refuse') { %>
        <span class="label label-danger">已拒绝</span>
      <% } else if (data.taskResult === 'transfer') { %>
        <span class="label label-primary">已转交</span>
      <% } else if (data.taskResult === 'recall') { %>
        <span class="label label-warning">已退回</span>
      <% } else if (!data.taskResult && data.finishTime) { %>
        <span class="label label-info">已取消</span>
      <% } else { %>
        <span class="label label-primary">处理中</span>
      <% } %>
    `
    },

    {
      name: 'status',
      label: '审批状态',
      type: 'text',
      visible:
        activeItem.title === '我创建的' ||
        activeItem.title === '抄送我的' ||
        activeItem.title === '全部'
    },
    {
      label: '审批状态',
      type: 'tpl',
      value: '待处理',
      visible: activeItem.title === '待我处理'
    },
    {
      name: '',
      label: `${activeItem.title === '我已处理' ? '处理耗时' : '已耗时'}`,
      type: 'tpl',
      tpl: `
        <% 
          var duration;
          if (data.taskEndTime) {
          // 我已处理的情况
          duration = data.taskEndTime - data.createTime;
          } else {
          // 其他情况
          duration = data.finishTime ? (data.finishTime - data.startTime) : (Math.floor(new Date().getTime()/1000) - data.startTime);
          }
          var days = Math.floor(duration / (24 * 60 * 60));
          var hours = Math.floor((duration % (24 * 60 * 60)) / (60 * 60));
          var minutes = Math.floor((duration % (60 * 60)) / 60);
          print(days + '天 ' + hours + '小时 ' + minutes + '分钟');
          %>
      `,
      visible:
        activeItem.title === '我创建的' ||
        activeItem.title === '抄送我的' ||
        activeItem.title === '我已处理' ||
        activeItem.title === '全部'
    }
    // {
    //   type: 'operation',
    //   label: '操作',
    //   fixed: 'right', // 确保列固定在最右侧
    //   buttons: [
    //     {
    //       type: 'button',
    //       label: '再次提交',
    //       actionType: 'dialog',
    //       dialog: {
    //         title: '再次提交',
    //         body: {
    //           type: 'form',
    //           body: [
    //             {type: 'static', name: 'instanceName', label: '流程名称'},
    //             {type: 'static', name: 'startUserName', label: '发起人'},
    //             {type: 'static', name: 'startTime', label: '提交时间'},
    //             {
    //               type: 'input-text',
    //               name: 'remark',
    //               label: '备注',
    //               placeholder: '填写备注信息'
    //             }
    //           ]
    //         }
    //       }
    //     }
    //   ],
    //   visible:
    //     activeItem.title === '我创建的'
    // }
  ];

  const getDataSetCurd = (path: any) => {
    console.log('path', props.applyData);
    const dataSetCurd = {
      id: 'u:6be5133b77f5',
      type: 'page',
      title: '数据管理',
      regions: ['body'],
      pullRefresh: {disabled: true},
      body: [
        {
          type: 'crud',
          syncLocation: false,
          api: {
            method: 'get',
            url: `${activeItem.api}?tenantId=${props.store.tenant_id}&pageNo=1&pageSize=10&applicationId=${props.applyData.id}`,
            messages: {},
            requestAdaptor: '',
            adaptor: `
  const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000); // 将秒级时间戳转换为毫秒级
    return date.getFullYear() + '-' + 
           String(date.getMonth() + 1).padStart(2, '0') + '-' +
           String(date.getDate()).padStart(2, '0') + ' ' +
           String(date.getHours()).padStart(2, '0') + ':' +
           String(date.getMinutes()).padStart(2, '0') + ':' +
           String(date.getSeconds()).padStart(2, '0');
  };

  if (payload.records) {
    payload.records = payload.records.map(record => {
      const newRecord = {
        ...record,
         startTime: record.startTime / 1000,  // 转换为秒
              finishTime: record.finishTime / 1000, // 转换为秒
              createTime : record.createTime  / 1000, // 转换为秒
              taskEndTime  : record.taskEndTime   / 1000, // 转换为秒
              taskCreateTime  : record.taskCreateTime   / 1000 // 转换为秒
      };

      if (record.formAbstracts && Array.isArray(record.formAbstracts)) {
        newRecord.formAbstracts = record.formAbstracts.map(abstract => {
          let newAbstract = {...abstract};
          if (abstract.type && abstract.type.startsWith('icon_') && typeof abstract.value === 'string') {
            let cleanedValue = abstract.value;
            if (cleanedValue.startsWith("'") && cleanedValue.endsWith("'")) {
              cleanedValue = cleanedValue.slice(1, -1);
            }
            newAbstract.value = cleanedValue;
          } else {
              newAbstract.value = abstract.value;
          }
          // 处理日期类型
          if (abstract.type && abstract.type.startsWith('date_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          if (abstract.type && abstract.type.startsWith('year_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          if (abstract.type && abstract.type.startsWith('time_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          if (abstract.type && abstract.type.startsWith('month_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          if (abstract.type && abstract.type.startsWith('datetime_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          // 处理日期范围类型
          if (abstract.type && abstract.type.startsWith('range_')&&abstract.value) {
            const [start, end] = abstract.value.split(',');
            if (start && end) {
              newAbstract.value = \`\${formatDate(parseInt(start))},\${formatDate(parseInt(end))}\`;
            }
          }
          // 使用字段 NAME 作为 key
          // newRecord[\`\${abstract.name}\`] = newAbstract.value;
          return newAbstract;
        });
      }
      return newRecord;
    });
  }

    const event = new CustomEvent('cityReplaceNeeded',{});
window.dispatchEvent(event);

  return payload;
`
          },
          // 配置行点击事件
          itemAction: {
            actionType: 'drawer', // 打开抽屉
            data: {
              instanceId: '${instanceId}',
              nodeId: '${nodeId}',
              formAbstracts: '${formAbstracts | json}'
            },
            drawer: {
              resizable: true,
              showCloseButton: true,
              closeOnOutside: true,
              position: 'right', // 从右侧打开
              title: '审批详情',
              style: {
                width: 'auto', // 或者你可以设置为 'fit-content'
                maxWidth: '90vw', // 防止内容太大撑满屏幕
                zIndex: 9999 // 增加 z-index，确保抽屉内容位于顶部
              },
              body: {
                type: 'panel',
                body: [
                  {
                    type: 'iframe',
                    src: `${
                      process.env.NODE_ENV === 'development'
                        ? 'http://localhost:88/#/workspace/ProcessInstancePreview'
                        : 'https://javaddm.fjpipixia.com/wflow/#/workspace/ProcessInstancePreview'
                    }?instanceId=\${instanceId}&nodeId=\${nodeId}&userID=${
                      props.store.userInfo?.id
                    }&tenantId=${props.store.tenant_id}&authorization=${
                      props.store.access_token
                    }`, // 可以传递参数到外部链接
                    height: '100vh', // 计算视口高度减去头部和其他元素的高度
                    frameBorder: 0,
                    style: {
                      width: '100%',
                      border: 'none',
                      overflow: 'hidden',
                      display: 'block'
                    },
                    allowFullscreen: true
                  }
                ]
              },
              actions: []
            }
          },
          autoGenerateFilter: {
            columnsNum: 2,
            showBtnToolbar: false
          },
          // filter: {
          //   "title": "条件搜索",
          //   "body": [
          //     {
          //       "type": "input-text",
          //       "name": "keywords",
          //       "placeholder": "通过关键字搜索"
          //     }
          //   ]
          // },
          id: 'u:f1f24e58104f',
          perPageAvailable: [5, 10, 20, 50, 100],
          // draggable: true,
          bulkActions: [],
          // "itemActions": [
          //   {
          //     "label": "按钮",
          //     "type": "button",
          //     "id": "u:932bece73200"
          //   },
          //   {
          //     "type": "button",
          //     "label": "删除",
          //     "actionType": "ajax",
          //     "api": {
          //       "method": "post",
          //       "url": "/amis/api/xxxx/$id",
          //       "data": {
          //         "&": "$$",
          //         "op": "delete"
          //       }
          //     },
          //     "confirmText": "确定要删除？"
          //   }
          // ],
          // filterTogglable: true,
          headerToolbar: [
            {
              type: 'columns-toggler',
              draggable: true
            },
            'reload'
            // {
            //   type: 'button',
            //   label: '新增',
            //   id: 'u:63bcbfc69ed8',
            //   disabledOnAction: false,
            //   onEvent: {
            //     click: {
            //       actions: [
            //         {
            //           actionType: 'custom',
            //           expression: 'getToCreateDataSetPage()'
            //         }
            //       ]
            //     }
            //   }
            // },
            // 'load-more',
            // {
            //   type: 'export-csv',
            //   tpl: '内容',
            //   wrapperComponent: '',
            //   id: 'u:30129f3c0998'
            // },
            // {
            //   type: 'export-excel',
            //   tpl: '内容',
            //   wrapperComponent: '',
            //   id: 'u:6a63d52666a1',
            //   disabledOnAction: false
            // },
            // 'bulkActions',
            // 'statistics'
          ],
          syncResponse2Query: false,
          pageField: 'pageNo',
          orderField: 'id',
          perPageField: 'pageSize',
          // columns: [...columns,...formColumns],
          columns: columns,
          alwaysShowPagination: true,
          footerToolbar: [
            {
              type: 'statistics'
            },
            {
              type: 'switch-per-page',
              tpl: '切换页码',
              wrapperComponent: '',
              id: 'u:bda5e402486b'
            },
            {
              type: 'pagination',
              tpl: '分页',
              wrapperComponent: '',
              id: 'u:b2ec821b8884',
              __editorStatebaseControlClassName: 'default'
            }
          ]
        }
      ],
      asideResizor: false
    };
    return dataSetCurd;
  };

  React.useEffect(() => {
    window.addEventListener('cityReplaceNeeded', () => {
      setTimeout(() => {
        // 页面渲染后替换城市码为名称
        document.querySelectorAll('.amis-city-code').forEach(el => {
          const code = el.getAttribute('data-code');
          if (code) {
            try {
              const cityName = cityCodeUtil.getFullAddressByCode(code);
              el.textContent = cityName;
            } catch (e) {
              el.textContent = code;
            }
          }
        });
      }, 3000);
    });
    // 初始化时，获取当前路由的path,并设置activeItem
    console.log('props location', props.location);
    const path = props.location.pathname;
    const item = menuList.find((item: any) => item.path == path);
    if (item) {
      setActiveItem(item);
      setActiveKey(item.path);
    } else {
      setActiveItem(menuList[0]);
      setActiveKey(menuList[0].path);
    }
    console.log('props', props);
  }, [props.applyData]);

  /* created 初始化 end */
  return (
    <div className="approvalCenterBox">
      <div className="approvalCenterBox-top" style={{justifyContent: 'center'}}>
        <div className="approvalCenterBox-top-name">审批</div>
      </div>
      <div className="pageTabsForm">
        <Tabs
          mode="line"
          onSelect={(key: any) => {
            props.history.push(key);
            setActiveKey(key);
            // 同时更新activeItem
            const item = menuList.find((item: any) => item.path == key);
            if (item) {
              setActiveItem(item);
            }
          }}
          toolbar={TabsToolbar()}
          linksClassName="pageTabsForm-tabsTitle"
          activeKey={activeKey}
        >
          {menuList.map((item: any) => {
            return (
              <Tab title={item.title} eventKey={item.path}>
                <div className="pageTabsForm-tabsContent">
                  <AMISRenderer schema={getDataSetCurd(item.path)} />
                </div>
              </Tab>
            );
          })}
        </Tabs>
      </div>
    </div>
  );
};

export default approval;
