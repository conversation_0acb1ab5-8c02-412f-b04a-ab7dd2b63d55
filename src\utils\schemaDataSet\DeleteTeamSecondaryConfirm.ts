// 二次删除团队
const DeleteTeamSecondaryConfirm = (porps: any) => {
  // console.log('porps DeleteTeamSecondaryConfirm',porps);
  return {
    type: 'dialog',
    body: [
      {
        type: 'tpl',
        tpl: '删除团队后，团队内所有应用将立即被永久删除，此操作不可撤销，请输入团队名称以确认。',
        id: 'u:7f3b04171a29',
        inline: true,
        wrapperComponent: '',
        themeCss: {
          baseControlClassName: {
            'font:default': {
              fontSize: '0.875rem',
              color: '#888888'
            }
          }
        }
      },
      {
        id: 'u:a32d4ec7e73b',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'input-text',
            label: '',
            name: 'text',
            id: 'u:297a75b08867',
            wrapperCustomStyle: {
              root: {
                'padding-top': "'0.5rem'"
              }
            },
            themeCss: {
              inputControlClassName: {
                'font:default': {
                  fontSize: '0.875rem',
                  fontWeight: 'var(--fonts-weight-4)'
                }
              }
            },
            value: '',
            placeholder: '请输入团队名称',
            colSize: '1',
            $$tempLabel: '隐藏标题'
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'u:a32d4ec7e73b'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: false,
        themeCss: {
          panelClassName: {
            'border:default': {
              'top-border-width': 'var(--borders-width-1)',
              'left-border-width': 'var(--borders-width-1)',
              'right-border-width': 'var(--borders-width-1)',
              'bottom-border-width': 'var(--borders-width-1)'
            },
            'padding-and-margin:default': {
              marginTop: 'var(--sizes-size-0)',
              marginRight: 'var(--sizes-size-0)',
              marginBottom: 'var(--sizes-size-0)',
              marginLeft: 'var(--sizes-size-0)'
            }
          },
          headerControlClassName: {
            'border:default': {
              'top-border-width': 'var(--borders-width-1)',
              'left-border-width': 'var(--borders-width-1)',
              'right-border-width': 'var(--borders-width-1)',
              'bottom-border-width': 'var(--borders-width-1)'
            },
            'padding-and-margin:default': {
              paddingTop: 'var(--sizes-size-0)',
              paddingRight: 'var(--sizes-size-0)',
              paddingBottom: 'var(--sizes-size-0)',
              paddingLeft: 'var(--sizes-size-0)'
            }
          },
          bodyControlClassName: {
            'padding-and-margin:default': {
              paddingTop: 'var(--sizes-size-0)',
              paddingRight: 'var(--sizes-size-0)',
              paddingBottom: 'var(--sizes-size-0)',
              paddingLeft: 'var(--sizes-size-0)'
            }
          },
          itemClassName: {
            'padding-and-margin': {
              marginTop: 'var(--sizes-size-0)',
              marginRight: 'var(--sizes-size-0)',
              marginBottom: 'var(--sizes-size-0)',
              marginLeft: 'var(--sizes-size-0)'
            }
          },
          actionsControlClassName: {
            'padding-and-margin:default': {
              paddingTop: 'var(--sizes-size-0)',
              paddingRight: 'var(--sizes-size-0)',
              paddingBottom: 'var(--sizes-size-0)',
              paddingLeft: 'var(--sizes-size-0)'
            }
          }
        },
        className: '',
        panelClassName: '',
        wrapperCustomStyle: {
          '.cxd-Form-flex-col': {
            padding: '0'
          }
        }
      }
    ],
    title: '你确定要删除团队吗？',
    id: 'u:ee0ebf8f8b5c',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:223292bb0a3e'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'u:e1c9d835ca2c',
        level: 'danger',
        onEvent: {
          click: {
            weight: 0,
            actions: [
              {
                ignoreError: false,
                actionType: 'custom',
                args: {},
                script: 'return context.props.scope.text;\n\n'
              }
            ]
          }
        }
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '二次确认删除团队'
    },
    themeCss: {
      dialogTitleClassName: {
        font: {
          fontWeight: 'var(--fonts-weight-4)',
          fontSize: '0.875rem'
        }
      }
    },
    actionType: 'dialog',
    $$ref: 'modal-ref-1',
    data: {
      text: ''
    }
  };
};

export {DeleteTeamSecondaryConfirm};
