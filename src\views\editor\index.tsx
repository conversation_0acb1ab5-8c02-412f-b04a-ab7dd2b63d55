import React, {useState, useRef, useCallback} from 'react';
import {
  Editor,
  ShortcutKey,
  setSchemaTpl,
  AppRendererProcessor
} from 'amis-editor';
import {inject, observer} from 'mobx-react';
import {RouteComponentProps} from 'react-router-dom';
import {toast, Select} from 'amis';
import {currentLocale} from 'i18n-runtime';
import {IMainStore} from '@/store';
import {
  configureManagerEditorPlugin,
  getAppRendererConfigs
} from '@/editor/DisabledEditorPlugin'; // 用于隐藏一些不需要的Editor预置组件
import {showRenderers} from '@/editor/disabledRenderers';
import '@/renderer/MyRenderer';
import {cxdThemeVars} from '@/component/ThemeSwitch/theme-vars';
import './index.scss';
import {getAmisJsonFormValue} from '@/utils/amisJsonForm/index';
import VersionHistory from '@/component/VersionHistory';
import {uuid} from 'amis';
import {GetControlDictPage} from '@/utils/api/api';
// import './renderer/InputTextI18n';
// import './renderer/TextareaI18n';
// import './utils/overwriteSchemaTpl';

import {
  findCreateBody,
  replacedCreateBody,
  replacedUpdateBody,
  replacedcolumns,
  replacedViewBody
} from '@/utils/schemaDataSet/commonEditor';

// 应用form配置到CRUD结构中的新增和编辑表单
const applyFormConfig = (obj: any, formConfig: any) => {
  if (!formConfig || Object.keys(formConfig).length === 0) {
    return obj;
  }

  if (obj && obj.body && obj.body.length > 0) {
    const firstBodyItem = obj.body[0];

    // 处理新增表单配置
    if (firstBodyItem.headerToolbar && firstBodyItem.headerToolbar.length > 0) {
      for (const toolbarItem of firstBodyItem.headerToolbar) {
        if (
          toolbarItem.actionType &&
          toolbarItem.editorSetting &&
          toolbarItem.editorSetting.behavior == 'create'
        ) {
          for (const formitem of toolbarItem[toolbarItem.actionType].body) {
            if (formitem.type === 'form') {
              // 应用form配置，但保留现有的body和其他重要属性
              Object.assign(formitem, formConfig);
            }
          }
        }
      }
    }

    // 处理编辑表单配置
    if (firstBodyItem.columns && firstBodyItem.columns.length > 0) {
      for (const columnItem of firstBodyItem.columns) {
        if (
          columnItem.type == 'operation' &&
          columnItem.buttons &&
          columnItem.buttons.length > 0
        ) {
          for (const buttonItem of columnItem.buttons) {
            if (
              buttonItem.actionType &&
              buttonItem.editorSetting &&
              buttonItem.editorSetting.behavior == 'update'
            ) {
              for (const formitem of buttonItem[buttonItem.actionType].body) {
                if (formitem.type === 'form') {
                  // 应用form配置，但保留现有的body和其他重要属性
                  Object.assign(formitem, formConfig);
                }
              }
            }
          }
        }
      }
    }
  }
  return obj;
};
import EditorDialog from '@/component/EditorDialog';
import {
  getFormDataPage, //获取表单数据
  getApplicationPageAndClass, //获取页面信息
  updateFormData //更新表单数据
} from '@/utils/api/api';

const editorLanguages = [
  {
    label: '简体中文',
    value: 'zh-CN'
  },
  {
    label: 'English',
    value: 'en-US'
  }
];

export interface DSField {
  value: string;
  label: string;
  [propKey: string]: any;
}

setSchemaTpl('formItemName', {
  label: '字段名',
  name: 'name',
  disabled: true,
  type: 'ae-DataBindingControl',
  onBindingChange(field: DSField, onBulkChange: (value: any) => void) {
    onBulkChange(field.resolveEditSchema?.() || {label: field.label});
  }
});

setSchemaTpl('inputType', {
  require: false,
  label: '输入类型',
  name: 'type',
  type: 'select',
  disabled: true,
  creatable: false,
  options: [
    {
      label: '文本',
      value: 'input-text'
    },
    {
      label: '密码',
      value: 'input-password'
    },
    {
      label: '邮箱',
      value: 'input-email'
    },
    {
      label: 'URL',
      value: 'input-url'
    }
  ]
});

// validation

export default inject('store')(
  observer(function ({
    store,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<{
    appId: string;
    form?: string;
  }>) {
    const curLanguage = currentLocale(); // 获取当前语料类型
    // 是否schema发生改变
    const isChange = React.useRef<boolean>(false);
    const [isPreview, setIsPreview] = React.useState<boolean>(false);
    const [exitIsOpen, setExitIsOpen] = React.useState<boolean>(false);
    const [pageId, setPageId] = React.useState<string>('');
    const [versionHistoryOpen, setVersionHistoryOpen] =
      React.useState<boolean>(false);
    // 添加editorKey状态变量用于强制刷新Editor组件
    const [editorKey, setEditorKey] = React.useState<number>(0);

    React.useEffect(() => {
      // 更新所有主题相关的类名
      updateElementsWithPrefix('dark', 'cxd');

      // 更新 CSS 变量
      updateCSSVariables();

      // 更健壮的 pageId 提取逻辑
      let page_id = '';
      if (match.params.form) {
        // 用正则提取最后的数字
        const matchResult = match.params.form.match(/page(\d+)$/);
        if (matchResult) {
          page_id = matchResult[1];
        } else {
          // 如果没有匹配到，直接用 form 参数
          page_id = match.params.form;
        }
      }

      if (page_id) {
        setPageId(page_id);
      } else {
      }
    }, [match.params.form]);

    // 监听 pageId 变化
    React.useEffect(() => {}, [pageId]);

    // 在组件加载时添加样式
    React.useEffect(() => {
      // 创建唯一的 style id
      const styleId = 'editor-custom-styles';

      // 如果已存在则移除
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        existingStyle.remove();
      }

      // 创建新的 style 标签
      const styleElement = document.createElement('style');
      styleElement.id = styleId;
      styleElement.textContent = `
          .cxd-TextControl-input,
          .cxd-DatePicker,
          .cxd-DateRangePicker,
          .cxd-TextareaControl-input,
          .cxd-Number,
          .cxd-Select,
          .cxd-ResultBox,
          .cxd-LocationPicker,
          // .cxd-ImageControl-addBtn,
          .cxd-FileControl-selectBtn ,
          .cxd-Checkbox--checkbox--default > i,
          .cxd-Checkbox--radio--default > i,
          .cxd-ExcelControl-dropzone ,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugs,
          .cxd-DateRangePicker-rangers{
            background: #fff!important;
          }

          .cxd-CalendarMobile-footer .cxd-DateRangePicker-rangers,
          .cxd-CalendarMobile-footer .date-range-confirm,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugsItem:not(.is-mobile):hover{
            background: #f7f8fa!important;
          }

          .cxd-Checkbox--radio--default,
          .cxd-Checkbox--radio--default:hover:not(.disabled),
          .cxd-Checkbox--checkbox--default--checked:hover:not(.disabled),
          .cxd-Checkbox--checkbox--default:hover:not(.disabled):not(.checked),
          .cxd-Checkbox--checkbox--default,
          .cxd-Cascader-option--text,
          .cxd-PickerColumns-columnItem,
          .cxd-DatePicker-input,
          .cxd-ResultBox-singleValue,
          .cxd-Select-valueWrap ,
          .cxd-DateRangePicker .cxd-DateRangePicker-input,
          .cxd-CalendarMobile-title, .cxd-CalendarMobile-subtitle,
          .cxd-PickerColumns-header,
          .rdt thead tr:first-child th,
          .cxd-CalendarMobile-range-text,
          .cxd-CalendarMobile-body .calendar-wrap,
          .cxd-CalendarMobile-footer .date-range-confirm,
          .rdtSwitch,
          .cxd-DateRangePicker-ranger a,
          .cxd-CalendarMobile-weekdays,
          .cxd-TimeRangeHeaderWrapper,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugsItem,
          td.rdtMonth > span, td.rdtYear > span, td.rdtQuarter > span,
          .cxd-CalendarMobile-time-title{
            color: #000!important;
          }

          .cxd-Button--primary,
          .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeStart .calendar-wrap, .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeStart:hover .calendar-wrap, .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeEnd .calendar-wrap, .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeEnd:hover .calendar-wrap,
          {
          color: #fff!important;
          }

          .rdt .rdtPicker td.is-disabled,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugsItem.is-highlight{
            background: #e6f0ff!important;
          }

          /* 强制使用亮色主题 */
          .Editor-Demo {
            --colors-neutral-fill-11: #fff !important;
          }

          /* 确保子元素使用亮色主题 */
          .Editor-Demo [class*="dark-"] {
            background-color: var(--colors-neutral-fill-11) !important;
          }
      `;

      // 添加到 head
      document.head.appendChild(styleElement);

      // 清理函数
      return () => {
        const style = document.getElementById(styleId);
        if (style) {
          style.remove();
        }
      };
    }, []);

    // 更新所有带有特定前缀的元素的类名
    const updateElementsWithPrefix = (oldTheme: string, newTheme: string) => {
      // 获取所有元素
      const allElements = document.getElementsByTagName('*');

      // 遍历所有元素
      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];
        const classList = element.classList;

        // 遍历元素的所有类名
        for (let j = 0; j < classList.length; j++) {
          const className = classList[j];
          // 如果类名以旧主题为前缀
          if (className.startsWith(`${oldTheme}-`)) {
            // 移除旧主题类名
            classList.remove(className);
            // 添加新主题类名
            classList.add(
              `${newTheme}-${className.slice(oldTheme.length + 1)}`
            );
            // 因为classList是实时的，所以我们需要调整索引
            j--;
          }
        }
      }
    };

    // 更新 CSS 变量
    const updateCSSVariables = () => {
      const root = document.documentElement;
      Object.entries(cxdThemeVars).forEach(([key, value]) => {
        root.style.setProperty(key, value as string);
      });
    };

    // 页面的 schema
    const [schema, setSchema] = useState<any>('');
    // 页面信息
    const [pageData, setPageData] = useState<any>(false);

    // 添加refreshEditor函数用于刷新Editor组件
    const refreshEditor = (
      schemaData: any,
      rendererType: string,
      sysConfigs?: any[],
      orgConfigsParam?: any[],
      appConfigsParam?: any[]
    ) => {
      setSchema(schemaData);
      configureEditorWithAllConfigs(
        rendererType,
        sysConfigs,
        orgConfigsParam,
        appConfigsParam
      );
    };

    // 获取页面的信息
    const handleGetApplicationPageAndClass = (
      sysConfigs?: any[],
      orgConfigsParam?: any[],
      appConfigsParam?: any[]
    ) => {
      let data = {
        id: pageId
      };
      getApplicationPageAndClass(data).then((res: any) => {
        if (res.code == 0) {
          setPageData(res.data);
          handleGetFormDataPage(
            res.data.pageType,
            sysConfigs,
            orgConfigsParam,
            appConfigsParam
          );
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 存储组件配置
    const [systemConfigs, setSystemConfigs] = useState<any[]>([]);
    const [orgConfigs, setOrgConfigs] = useState<any[]>([]);
    const [appConfigs, setAppConfigs] = useState<any[]>([]);

    // 组合渲染器处理器，处理组织级和应用级配置
    const orgRendererProcessor = useCallback(
      (renderer: any, context: any) => {
        if (Array.isArray(renderer)) {
          return renderer;
        }

        const baseRenderer = renderer;
        const activeConfigs = orgConfigs.filter(config => config.status !== 0);

        const renderers = activeConfigs
          .filter(config => config.amisControlType === renderer.rendererName)
          .map((config, index) => {
            // 构建 scaffold 配置
            let scaffold = {...baseRenderer.scaffold};

            if (config.json) {
              try {
                const jsonConfig =
                  typeof config.json === 'string'
                    ? JSON.parse(config.json)
                    : config.json;
                scaffold = {...scaffold, ...jsonConfig};
              } catch (e) {
                console.warn('解析组织级 JSON 配置失败:', e);
              }
            }

            return {
              ...baseRenderer,
              name: config.controlType || `组织选择器(${index + 1})`,
              order: config.sort || index + 1,
              tags: [config.classificationName],
              scaffold
            };
          });

        return renderers;
      },
      [orgConfigs]
    );

    // 处理应用级渲染器配置
    const appRendererProcessor = useCallback(
      (renderer: any, context: any) => {
        if (Array.isArray(renderer)) {
          return renderer;
        }

        const baseRenderer = renderer;
        const activeConfigs = appConfigs.filter(config => config.status !== 0);

        const renderers = activeConfigs
          .filter(config => config.amisControlType === renderer.rendererName)
          .map((config, index) => {
            // 构建 scaffold 配置
            let scaffold = {...baseRenderer.scaffold};

            if (config.json) {
              try {
                const jsonConfig =
                  typeof config.json === 'string'
                    ? JSON.parse(config.json)
                    : config.json;
                scaffold = {...scaffold, ...jsonConfig};
              } catch (e) {
                console.warn('解析应用级 JSON 配置失败:', e);
              }
            }

            return {
              ...baseRenderer,
              name: config.controlType || `应用选择器(${index + 1})`,
              order: config.sort || index + 1,
              tags: [config.classificationName],
              scaffold
            };
          });

        return renderers;
      },
      [appConfigs]
    );

    const handlegetSystemComponentList = async () => {
      let data = {
        pageNo: 1,
        pageSize: 999,
        type: 1
      };
      try {
        const res: any = await GetControlDictPage(data);
        if (res.code == 0) {
          const configs = res.data.list || [];
          setSystemConfigs(configs);
          return configs;
        } else {
          toast.error(res.msg);
          return [];
        }
      } catch (error) {
        toast.error('获取系统组件配置失败');
        return [];
      }
    };

    const handlegetOrgComponentList = async () => {
      let data = {
        pageNo: 1,
        pageSize: 999,
        type: 2
      };
      try {
        const res: any = await GetControlDictPage(data);
        if (res.code == 0) {
          const configs = res.data.list || [];
          setOrgConfigs(configs);
          return configs;
        } else {
          toast.error(res.msg);
          return [];
        }
      } catch (error) {
        toast.error('获取组织组件配置失败');
        return [];
      }
    };

    const handlegetAppComponentList = async () => {
      let data = {
        pageNo: 1,
        pageSize: 999,
        type: 3,
        applicationId: Number(match.params.appId)
      };
      try {
        const res: any = await GetControlDictPage(data);
        if (res.code == 0) {
          const configs = res.data.list || [];
          setAppConfigs(configs);
          return configs;
        } else {
          toast.error(res.msg);
          return [];
        }
      } catch (error) {
        toast.error('获取app组件配置失败');
        return [];
      }
    };

    // 统一配置编辑器插件
    const configureEditorWithAllConfigs = (
      rendererType: string,
      sysConfigs?: any[],
      orgConfigsParam?: any[],
      appConfigsParam?: any[]
    ) => {
      let renderers: any = showRenderers(rendererType);

      // 更新 appConfigs 状态
      if (appConfigsParam) {
        setAppConfigs(appConfigsParam);
      }

      // 配置编辑器插件，这会更新 DisabledEditorPlugin 中的全局变量
      configureManagerEditorPlugin(
        renderers,
        sysConfigs || systemConfigs,
        orgConfigsParam || orgConfigs,
        appConfigsParam || appConfigs
      );

      setEditorKey(prevKey => prevKey + 1); // 强制刷新Editor组件
    };

    const [formData, setFormData] = useState<any>(false);
    // 获取表单信息
    const handleGetFormDataPage = (
      pageType: number | string,
      sysConfigs?: any[],
      orgConfigsParam?: any[],
      appConfigsParam?: any[]
    ) => {
      let data = {
        applicationPageId: Number(pageId),
        pageNo: 1,
        pageSize: 10
      };

      getFormDataPage(data).then((res: any) => {
        if (res.code == 0) {
          if (res.data.list.length > 0) {
            if (res.data.list[0]) {
              res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
              let form_data = res.data.list[0];
              setFormData(form_data);
              if (pageType == 1 || pageType == 2) {
                let value = findCreateBody(form_data.data);
                let data;

                // 判断最外层是否已经是form，避免重复嵌套
                if (
                  form_data.data.body &&
                  form_data.data.body.length > 0 &&
                  form_data.data.body[0].type === 'form'
                ) {
                  // 如果最外层已经是form，直接使用原始数据
                  data = form_data.data;
                } else {
                  // 如果不是form，则包装在form中，并从CRUD结构中提取form配置

                  // 提取form配置（如columnCount等）
                  let formConfig: any = {
                    wrapWithPanel: false,
                    className: 'w-full h-full'
                  };

                  // 从CRUD结构中的新增表单提取配置
                  if (
                    form_data.data &&
                    form_data.data.body &&
                    form_data.data.body.length > 0
                  ) {
                    const firstBodyItem = form_data.data.body[0];
                    if (
                      firstBodyItem.headerToolbar &&
                      firstBodyItem.headerToolbar.length > 0
                    ) {
                      for (const toolbarItem of firstBodyItem.headerToolbar) {
                        if (
                          toolbarItem.actionType &&
                          toolbarItem.editorSetting &&
                          toolbarItem.editorSetting.behavior == 'create'
                        ) {
                          for (const formItem of toolbarItem[
                            toolbarItem.actionType
                          ].body) {
                            if (formItem.type === 'form') {
                              // 提取form配置，排除body和一些不需要的属性
                              const {body, api, actions, id, feat, ...config} =
                                formItem;
                              formConfig = {
                                ...formConfig,
                                ...config
                              };
                              break;
                            }
                          }
                        }
                      }
                    }
                  }

                  data = {
                    type: 'page',
                    regions: ['body'],
                    pullRefresh: {
                      disabled: true
                    },
                    body: {
                      type: 'form',
                      body: value,
                      ...formConfig // 应用提取的form配置（包括columnCount）
                    }
                  };
                }
                // 使用refreshEditor替换原来的代码
                refreshEditor(
                  data,
                  'form',
                  sysConfigs,
                  orgConfigsParam,
                  appConfigsParam
                );
              } else if (pageType == 7 || pageType == 16) {
                // 使用refreshEditor替换原来的代码
                refreshEditor(
                  form_data.data,
                  'dashboard',
                  sysConfigs,
                  orgConfigsParam,
                  appConfigsParam
                );
              } else if (pageType == 12) {
                // 使用refreshEditor替换原来的代码
                refreshEditor(
                  form_data.data,
                  'crud',
                  sysConfigs,
                  orgConfigsParam,
                  appConfigsParam
                );
              } else if (pageType == 17) {
                // 使用refreshEditor替换原来的代码
                refreshEditor(
                  form_data.data,
                  'portlet',
                  sysConfigs,
                  orgConfigsParam,
                  appConfigsParam
                );
              } else {
                // 使用refreshEditor替换原来的代码
                refreshEditor(
                  form_data.data,
                  'page',
                  sysConfigs,
                  orgConfigsParam,
                  appConfigsParam
                );
              }
            } else {
              toast.success('暂无表单数据');
            }
          } else {
            if (pageType == 1 || pageType == 2) {
              // 表单页面：创建默认的表单结构
              let data = {
                type: 'page',
                regions: ['body'],
                pullRefresh: {
                  disabled: true
                },
                body: {
                  type: 'form',
                  body: [],
                  wrapWithPanel: false,
                  className: 'w-full h-full'
                }
              };
              refreshEditor(
                data,
                'form',
                sysConfigs,
                orgConfigsParam,
                appConfigsParam
              );
            } else if (pageType == 16) {
              // 使用refreshEditor替换原来的代码
              refreshEditor(
                {},
                'dashboard',
                sysConfigs,
                orgConfigsParam,
                appConfigsParam
              );
            } else {
              toast.success('暂无表单数据');
            }
          }
        } else {
          toast.error(res.msg);
        }
      });
    };
    React.useEffect(() => {
      if (pageId) {
        // 并行加载三个组件配置，全部完成后再加载页面数据
        const loadData = async () => {
          try {
            // 并行请求三个接口
            const [sysConfigs, orgConfigsData, appConfigsData] =
              await Promise.all([
                handlegetSystemComponentList(),
                handlegetOrgComponentList(),
                handlegetAppComponentList()
              ]);

            // 三个接口都完成后，再加载页面数据，并传入最新的配置数据
            handleGetApplicationPageAndClass(
              sysConfigs,
              orgConfigsData,
              appConfigsData
            );
          } catch (error) {
            // 即使配置加载失败，也要加载页面数据
            handleGetApplicationPageAndClass();
          }
        };
        loadData();
      } else {
      }
    }, [pageId, match.params.appId]);

    //切换中英文
    const changeLocale = (value: string) => {
      localStorage.setItem('suda-i18n-locale', value);
      window.location.reload();
    };
    // 编辑变动
    const onChangeEditor = (value: any) => {
      setSchema(value);
      isChange.current = true;
    };
    // 插入节点前事件
    const beforeInsert = (event: any) => {
      if (event.context.data.name) {
        let nameArr = event.context.data.name.split('-');
        event.context.data.name =
          nameArr[nameArr.length - 1] + `_${Date.now()}`;
      }
    };
    // 是否为隐藏属性，隐藏属性是在配置中有，但是在代码编辑器中不可见。
    const isHiddenProps = () => {
      return false;
    };
    // 面板里面编辑修改前的事件。可通过 event.preventDefault() 阻止。
    const beforeUpdate = () => {};

    // 表单的话，更新字段
    const onFieldSaveTable = async () => {
      // 根据schema结构获取表单字段
      let schemaList: any;
      if (schema.body && schema.body.type === 'form') {
        // 如果body直接是form，从form.body获取字段
        schemaList = JSON.parse(JSON.stringify(schema.body.body));
      } else if (
        schema.body &&
        Array.isArray(schema.body) &&
        schema.body[0] &&
        schema.body[0].type === 'form'
      ) {
        // 如果body是数组且第一个元素是form，从第一个form获取字段
        schemaList = JSON.parse(JSON.stringify(schema.body[0].body));
      } else {
        // 其他情况，直接使用body作为字段数组
        schemaList = JSON.parse(JSON.stringify(schema.body));
      }

      // 获取form的配置（如columnCount等）
      let formConfig: any = {};
      if (schema.body && schema.body.type === 'form') {
        formConfig = JSON.parse(JSON.stringify(schema.body));
        delete formConfig.body; // 移除body，只保留配置
      } else if (
        schema.body &&
        Array.isArray(schema.body) &&
        schema.body[0] &&
        schema.body[0].type === 'form'
      ) {
        formConfig = JSON.parse(JSON.stringify(schema.body[0]));
        delete formConfig.body; // 移除body，只保留配置
      }
      for (let i = 0; i < schemaList.length; i++) {
        let resulit = getAmisJsonFormValue(schemaList[i].type);
        if (resulit) {
          // 保留用户的所有配置，只用模板补充缺失的属性
          const originalField = JSON.parse(JSON.stringify(schemaList[i]));
          schemaList[i] = {
            ...resulit, // 模板的默认值
            ...originalField // 用户的配置覆盖模板（保留columnCount等用户设置）
          };
        }
      }

      let schemaData: any = [];
      schemaData = formData.data;

      schemaData = await replacedCreateBody(schemaData, schemaList);
      schemaData = await replacedUpdateBody(schemaData, schemaList);

      // 应用form配置到新增和编辑表单
      schemaData = await applyFormConfig(schemaData, formConfig);
      // 使用相同的逻辑获取lookSchemaList
      let lookSchemaList: any;
      if (schema.body && schema.body.type === 'form') {
        lookSchemaList = JSON.parse(JSON.stringify(schema.body.body));
      } else if (
        schema.body &&
        Array.isArray(schema.body) &&
        schema.body[0] &&
        schema.body[0].type === 'form'
      ) {
        lookSchemaList = JSON.parse(JSON.stringify(schema.body[0].body));
      } else {
        lookSchemaList = JSON.parse(JSON.stringify(schema.body));
      }
      for (let cx = 0; cx < lookSchemaList.length; cx++) {
        if (
          lookSchemaList[cx].type == 'input-image' ||
          lookSchemaList[cx].type == 'input-signature'
        ) {
          let resulit = getAmisJsonFormValue('image');
          if (resulit) {
            lookSchemaList[cx] = {
              ...resulit,
              ...lookSchemaList[cx] // 保留用户的所有配置
            };
          }
        }

        if (lookSchemaList[cx].type == 'input-file') {
          let resulit = getAmisJsonFormValue('link');
          if (resulit) {
            lookSchemaList[cx] = {
              ...resulit,
              ...lookSchemaList[cx] // 保留用户的所有配置
            };
          }
        }
      }
      schemaData = await replacedcolumns(schemaData, lookSchemaList);
      schemaData = await replacedViewBody(schemaData, lookSchemaList);

      let data = formData;
      data.data = window.JSON.stringify(schemaData);
      data.field = window.JSON.stringify(schemaList);

      // return;
      updateFormData(data).then((res: any) => {
        if (res.code == 0) {
          handleGetApplicationPageAndClass();
          toast.success('保存成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    const replaceText = {
      'i18n:1189fb5d-ac5b-4558-b363-068ce5decc99': uuid()
    };
    // 同用保存
    const onUpdateFormDataSave = () => {
      let data: any = {};
      data.id = formData.id;
      data.applicationPageId = formData.applicationPageId;
      data.data = window.JSON.stringify(schema);
      data.field = '';
      updateFormData(data).then((res: any) => {
        if (res.code == 0) {
          handleGetApplicationPageAndClass();
          isChange.current = false;
          toast.success('保存成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 保存
    const onSave = () => {
      if (isChange.current) {
        isChange.current = false;
        if (pageData.pageType == 1 || pageData.pageType == 2) {
          onFieldSaveTable();
        } else {
          onUpdateFormDataSave();
        }
      }
    };
    // 退出
    const exit = () => {
      history.push(`/app${match.params.appId}/admin/${match.params.form}`);
    };

    // 处理版本选择
    const handleVersionSelect = () => {
      // 刷新数据
      setVersionHistoryOpen(false);
      handleGetApplicationPageAndClass();
    };

    return (
      <div className="Editor-Demo">
        <div className="Editor-header">
          <div className="Editor-title">{pageData.name}</div>

          <div className="Editor-header-actions">
            <ShortcutKey />
            <Select
              className="margin-left-space"
              options={editorLanguages}
              value={curLanguage}
              clearable={false}
              onChange={(e: any) => changeLocale(e.value)}
            />
            {isChange.current && (
              <div
                className={`header-action-btn m-1 save-btn`}
                onClick={() => onSave()}
              >
                保存
              </div>
            )}
            <div
              className={`header-action-btn m-1 ${isPreview ? 'primary' : ''}`}
              onClick={() => {
                setIsPreview(!isPreview);
              }}
            >
              {isPreview ? '编辑' : '预览'}
            </div>
            {!isPreview && (
              <div
                className={`header-action-btn exit-btn`}
                onClick={() =>
                  isChange.current ? setExitIsOpen(true) : exit()
                }
              >
                退出
              </div>
            )}
          </div>
        </div>
        <div className="Editor-inner">
          <Editor
            key={editorKey}
            i18nEnabled={false}
            appRendererProcessor={appRendererProcessor}
            orgRendererProcessor={orgRendererProcessor}
            theme={'cxd'}
            preview={isPreview}
            isMobile={store.isMobile}
            value={schema}
            onChange={e => onChangeEditor(e)}
            onHistoryRecord={() => {
              setVersionHistoryOpen(true);
            }}
            onPreview={() => {
              setIsPreview(true);
            }}
            onSave={() => onSave()}
            className="is-fixed"
            $schemaUrl={''}
            showCustomRenderersPanel={true}
            amisEnv={{
              fetcher: store.fetcher,
              notify: store.notify,
              alert: store.alert,
              copy: store.copy,
              replaceText: replaceText
            }}
            beforeInsert={event => beforeInsert(event)}
            isHiddenProps={() => isHiddenProps()}
            beforeUpdate={() => beforeUpdate()}
          />
          <EditorDialog
            show={exitIsOpen}
            onClose={() => setExitIsOpen(false)}
            onConfirm={exit}
          />
          <VersionHistory
            show={versionHistoryOpen}
            onClose={() => setVersionHistoryOpen(false)}
            onRollback={handleVersionSelect}
            pageId={pageId}
            formDataId={pageData.formDataId}
            pageSchema={schema}
            store={store}
          />
        </div>
      </div>
    );
  })
);
