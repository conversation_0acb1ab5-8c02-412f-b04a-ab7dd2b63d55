import { width } from "@/editor/EChartsEditor/Common";
import { clear } from "console";

export const editInterest = () => {
    return {
        "type": "dialog",
        "title": "编辑权限组",
        "width": "800px",
        "size": "xs",
        "body": {
            "type": "form",
            
            "mode": "horizontal",
            "horizontal": {
                "left": 1,
                "right": 8
            },
            "body": [
                {
                    "type": "input-text",
                    "name": "name",
                    "label": "名称",
                    "required": true,
                    "placeholder": "请输入权限组名称",
                    "value": "全部成员可提交数据"
                },
                {
                    "type": "textarea",
                    "name": "description",
                    "label": "描述",
                    "placeholder": "请输入描述信息",
                    "value": "可以提交当前表单的权限"
                },
                {
                    "type": "radios",
                    "name": "memberType",
                    "label": "权限成员",
         
                    "options": [
                        {
                            "label": "全部成员",
                            "value": "all",
                            
                        },
                        {
                            "label": "自定义",
                            "value": "memberTypeCustom",
                        }
                    ],
                    
                },
                {
                    "type": "tpl",
                    "tpl": "<% if (data.memberType && data.memberType.includes('memberTypeCustom')) { %><span class='link-button text-primary' style='position: relative;top: -45px;left: 250px;color: var(--primary); border: none; background: none; padding: 0; cursor: pointer;'>添加成员(0)</span><% } %>",
                    "inline": true,
                    "className": "custom-department-setting",
                    "onEvent": {
                        "click": {
                            "actions": [
                                {
                                    "actionType": "dialog",
                                    "dialog": {
                                        "type": "dialog",
                                        //"initApi": "/admin-api/system/team-and-project-members/get-selected-users?teamOrProjectId=${teamOrProjectId}",
                                        "title": "邀请成员",
                                            "body": [
                                                  {
                                            "label": "分组",
                                            "type": "transfer",
                                            "name": "transfer",
                                            "selectMode": "tree",
                                            "resultListModeFollowSelect": false,
                                            "id": "u:d1646dc0a71c",
                                            "onlyChildren": true,
                                            "autoCheckChildren": true,
                                            "source": "/admin-api/system/user/list-dept-user?teamOrProjectOrApplicationId=${teamOrProjectId}&type=${type}",
                                            "labelField": "name",
                                            "valueField": "id",
                                                      "searchable": true,
                                            "statistics": true,
                                            // "value":"${selectMemberIds}",
                                    
                                          }
                                        ],
                                        "actions": [
                                          {
                                            type: 'button',
                                            actionType: 'cancel',
                                            label: '取消',
                                            id: 'u:d2f9d50b6428'
                                          },
                                          {
                                            "type": "button",
                                            "label": "确定",
                                            "close": true,
                                            "primary": true,
                                            "actionType": "ajax",
                                            "api": {
                                              "method": "post",
                                              "url": "/admin-api/system/team-project-application-members/batch-create",
                                              "data": {
                                              //  "teamOrProjectOrApplicationId": "${teamOrProjectId}",
                                              //  "userIds": "${transfer}",
                                              //  "type": "${type}"
                                              }
                                            },
                                          }
                                        ],
                                          "size": "md"
                                      }
                                }
                            ]
                        }
                    }
                },
                
                {
                    "type": "checkboxes",
                    "name": "operationPermissions",
                    "label": "操作权限",
                    "options": [
                        {
                            "label": "提交",
                            "value": "submit"
                        }
                    ],
                    "value": ["submit"],
                    "style": {  // 单独设置某个选项的样式
                        "clear": "both"
                    },
                },
                {
                    "type": "radios",
                    "name": "fieldPermissionType",
                    "label": "字段权限",
                    "options": [
                        {
                            "label": "继承表单设计中维护的状态",
                            "value": "inherit"
                        },
                        {
                            "label": "自定义",
                            "value": "custom"
                        }
                    ],
                    "value": "inherit"
                }
            ],
            "actions": [
                {
                    "type": "button",
                    "label": "取消",
                    "actionType": "close"
                },
                {
                    "type": "button",
                    "label": "保存",
                    "level": "primary",
                    "actionType": "submit"
                }
            ]
        }
    };
};