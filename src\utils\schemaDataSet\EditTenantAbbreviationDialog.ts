export const EditTenantAbbreviationDialog = (show: boolean, currentName?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑企业简称</b>'
    },
    showCloseButton: false,
    data: {
      abbreviation: currentName
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入企业简称'
      },
      body: [
        {
          type: 'input-text',
          name: 'abbreviation',
          placeholder: '企业简称',
          required: true,
          maxLength: 50,
          validations: {
            maxLength: 50
          },
          inputClassName: 'abbreviation-input',
          labelClassName: 'abbreviation-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 