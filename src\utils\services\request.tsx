// 引入 axios 和类型定义
import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';

const nodeEnv = process.env.NODE_ENV;

// https://m.thinkddm.fjpipixia.com /apiTest /javaApi
const baseURL =
  nodeEnv == 'development' ? '/apiTest' : 'https://javaddm.fjpipixia.com';
const instance: AxiosInstance = axios.create({
  baseURL: baseURL,
  timeout: 10000
});

// 是否正在刷新token的标记
let isRefreshing = false;
// 重试队列，每一项将是一个待执行的函数形式
let retryRequests: Array<(token: string) => void> = [];

// Add a request interceptor
// 全局请求拦截，发送请求之前执行
instance.interceptors.request.use(
  (config: AxiosRequestConfig): AxiosRequestConfig => {
    // 设置请求的 token 等等
    // config.headers["authorization"] = "Bearer " + getToken();
    let store: any = localStorage.getItem('store');
    store = JSON.parse(store);
    // config.headers['http-user-id'] = store?.userInfo ? store.userInfo?.id : '';
    config.headers['tenant-id'] = store?.tenant_id ? store.tenant_id : '';
    let access_token = store?.access_token;
    config.headers['authorization'] = access_token
      ? 'Bearer ' + access_token
      : '';

    return config;
  },
  (error: any): Promise<any> => {
    // Do something with request error
    return Promise.reject(error);
  }
);

// 清除登录信息并重定向到登录页面
const handleLogout = () => {
  // 清除localStorage中的登录信息
  localStorage.removeItem('store');
  // 获取当前路径，保存起来以便登录后可以重定向回来
  const currentPath = window.location.pathname;
  if (currentPath !== '/login') {
    localStorage.setItem('redirectAfterLogin', currentPath);
  }

  // 如果是在iframe中，尝试通知父窗口退出登录
  if (window !== window.parent) {
    try {
      window.parent.postMessage({type: 'logout', message: '登录已过期'}, '*');
    } catch (e) {
      console.error('无法通知父窗口登出', e);
    }
  }

  // 重置axios默认头信息
  instance.defaults.headers.common['authorization'] = '';

  // 重定向到登录页面
  window.location.href = '/#/login';
  window.location.reload();
};

// 刷新 Token 并处理队列中的请求
const refreshTokenHandler = async (config: AxiosRequestConfig) => {
  try {
    // 如果已经在刷新，则将请求加入队列
    if (isRefreshing) {
      return new Promise(resolve => {
        // 将resolve放入队列中，用新token解决
        retryRequests.push((token: string) => {
          // 更新token
          if (config.headers) {
            config.headers['authorization'] = 'Bearer ' + token;
          }
          resolve(instance(config));
        });
      });
    }

    // 标记正在刷新
    isRefreshing = true;

    // 从localStorage获取refreshToken
    let store: any = localStorage.getItem('store');
    console.log('登录过期store', store);
    store = JSON.parse(store || '{}');
    if (!store.refresh_token) {
      throw new Error('No refresh token available');
    }

    // 创建 URLSearchParams 对象，用于 x-www-form-urlencoded 格式
    const params = new URLSearchParams();
    params.append('refreshToken', store.refresh_token);
    params.append('tenant-id', store?.tenant_id ? store.tenant_id : '');
    params.append(
      'Authorization',
      store?.access_token ? 'Bearer ' + store.access_token : ''
    );
    // 创建一个特殊的axios实例，专门用于刷新token
    const refreshTokenRequest = axios.create({
      baseURL: baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization':
          'Bearer ' + (store?.access_token ? store.access_token : ''),
        'tenant-id': store?.tenant_id ? store.tenant_id : '1'
      }
    });

    // 调用刷新token的API，使用x-www-form-urlencoded格式
    const response = await refreshTokenRequest.post(
      '/admin-api/system/auth/refresh-token',
      params
    );
    const res = response.data;

    if (res.code === 0 && res.data) {
      const {accessToken, refreshToken: newRefreshToken} = res.data;

      // 更新本地存储中的token
      store.access_token = accessToken;
      store.refresh_token = newRefreshToken; // 保持字段名一致
      localStorage.setItem('store', JSON.stringify(store));

      // 更新请求的Authorization头
      if (config.headers) {
        config.headers['authorization'] = 'Bearer ' + accessToken;
      }

      // 执行队列中的所有请求
      retryRequests.forEach(cb => cb(accessToken));
      retryRequests = [];

      // 返回新配置的请求
      return instance(config);
    } else {
      // 刷新token失败，清除登录信息并跳转
      handleLogout();
      return Promise.reject(new Error('Token refresh failed'));
    }
  } catch (error) {
    // 任何错误都会导致登出
    handleLogout();
    return Promise.reject(error);
  } finally {
    // 重置刷新状态
    isRefreshing = false;
  }
};

// Add a response interceptor
// 请求返回之后执行
instance.interceptors.response.use(
  (response: AxiosResponse): any => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    const res = response.data;

    // 检查返回的数据中是否包含code=401，表示未登录或登录已过期
    if (res && res.code === 401) {
      if (JSON.parse(localStorage.getItem('store') || '{}').userInfo) {
        // 尝试刷新token并重试请求
        return refreshTokenHandler(response.config);
      } else {
        // handleLogout();
        // 清除localStorage中的登录信息
        localStorage.removeItem('store');
        // 重置axios默认头信息
        instance.defaults.headers.common['authorization'] = '';
        // 重定向到登录页面
        window.location.href = '/#/login';
        return Promise.reject(new Error('Token refresh failed'));
      }
      // 尝试刷新token并重试请求
      // return refreshTokenHandler(response.config);
    }

    return res;
  },
  (error: any): Promise<any> => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // 检查错误响应中是否包含401状态码
    if (error.response) {
      const {status, data} = error.response;

      if (status === 401 || (data && data.code === 401)) {
        if (JSON.parse(localStorage.getItem('store') || '{}').userInfo) {
          // 尝试刷新token并重试请求
          return refreshTokenHandler(error.config);
        } else {
          // handleLogout();
          // 清除localStorage中的登录信息
          localStorage.removeItem('store');
          // 重置axios默认头信息
          instance.defaults.headers.common['authorization'] = '';
          // 重定向到登录页面
          window.location.href = '/#/login';
          return Promise.reject(new Error('Token refresh failed'));
        }
        // // 尝试刷新token并重试请求
        // return refreshTokenHandler(error.config);
      }
    }

    // Do something with response error
    return Promise.reject(error);
  }
);

/**
 * get 请求
 * @param url 请求地址
 * @param params 参数
 */
export function get(url: string, params?: any): Promise<any> {
  return instance.get(url, {params});
}

/**
 * post 请求
 * @param url 请求地址
 * @param data 数据
 */
export function post(url: string, data?: any): Promise<any> {
  return instance.post(url, data);
}

/**
 * put 请求
 * @param url 请求地址
 * @param data 数据
 */
export function put(url: string, data?: any): Promise<any> {
  return instance.put(url, data);
}

/**
 * delete 请求
 * @param url 请求地址
 */
export function del(url: string, params?: any): Promise<any> {
  return instance.delete(url, {params});
}
