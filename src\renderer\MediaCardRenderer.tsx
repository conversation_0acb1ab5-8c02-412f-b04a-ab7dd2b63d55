import {
  Render<PERSON>,
} from 'amis-core';
import { CardProps } from './UserCardRenderer';

import React from 'react';

import {filter, evalExpression, RendererProps, CustomStyle, setThemeClassName} from 'amis-core';
import {Checkbox, Card, Icon} from 'amis-ui';
import {findDOMNode} from 'react-dom';

import {
  resolveVariableAndFilter,
  filterClassNameObject
} from 'amis-core';
import {padArr, isVisible, isDisabled, noop, hashCode} from 'amis-core';

import {SchemaNode, Schema, ActionObject, PlainObject} from 'amis-core';

@Renderer({
    type: 'media-card'
  })
  export class MediaCardRenderer extends React.Component<CardProps> {
    static defaultProps = {
      className: '',
      avatarClassName: '',
      headerClassName: '',
      footerClassName: '',
      secondaryClassName: '',
      avatarTextClassName: '',
      bodyClassName: '',
      actionsCount: 4,
      titleClassName: '',
      highlightClassName: '',
      subTitleClassName: '',
      descClassName: '',
      descriptionClassName: '',
      imageClassName: '',
      highlight: false,
      blank: true,
      dragging: false,
      selectable: false,
      checkable: true,
      selected: false,
      hideCheckToggler: false,
      useCardLabel: true
    };
  
    static propsList: Array<string> = [
      'avatarClassName',
      'avatarTextClassName',
      'bodyClassName',
      'actionsCount',
      'titleClassName',
      'highlightClassName',
      'subTitleClassName',
      'descClassName',
      'descriptionClassName',
      'imageClassName',
      'hideCheckToggler'
    ];
  
    constructor(props: CardProps) {
      super(props);
  
      this.handleClick = this.handleClick.bind(this);
      this.handleAction = this.handleAction.bind(this);
      this.handleCheck = this.handleCheck.bind(this);
      this.getPopOverContainer = this.getPopOverContainer.bind(this);
      this.handleQuickChange = this.handleQuickChange.bind(this);
    }
  
    isHaveLink() {
      const {href, itemAction, onCheck, checkOnItemClick, checkable} = this.props;
      return href || itemAction || onCheck || (checkOnItemClick && checkable);
    }
  
    handleClick(e: React.MouseEvent<HTMLDivElement>) {
      const {
        item,
        href,
        data,
        env,
        blank,
        itemAction,
        onAction,
        onCheck,
        onClick,
        selectable,
        checkOnItemClick
      } = this.props;
  
      if (href) {
        env.jumpTo(
          filter(href, data),
          {
            type: 'button',
            actionType: 'url',
            blank
          },
          data
        );
        return;
      }
  
      if (itemAction) {
        onAction && onAction(e, itemAction, item?.data || data);
        return;
      }
  
      selectable && checkOnItemClick && onCheck?.(item);
      onClick && onClick(item);
    }
  
    handleAction(e: React.UIEvent<any>, action: ActionObject, ctx: object) {
      const {onAction, item} = this.props;
  
      onAction && onAction(e, action, ctx || item.data);
    }
  
    handleCheck() {
      const item = this.props.item;
      this.props.onCheck && this.props.onCheck(item);
    }
  
    getPopOverContainer() {
      return findDOMNode(this);
    }
  
    handleQuickChange(
      values: object,
      saveImmediately?: boolean,
      savePristine?: boolean,
      options?: {
        resetOnFailed?: boolean;
        reload?: string;
      }
    ) {
      const {onQuickChange, item} = this.props;
      onQuickChange &&
        onQuickChange(item, values, saveImmediately, savePristine, options);
    }
  
    renderToolbar() {
      const {
        selectable,
        checkable,
        selected,
        multiple,
        hideCheckToggler,
        classnames: cx,
        toolbar,
        render,
        dragging,
        data,
        header,
        headerToolbarVisible
      } = this.props;
  
      const toolbars: Array<JSX.Element> = [];
      if (header) {
        const {highlightClassName, highlight} = header;
        if (
          typeof highlight === 'string'
            ? evalExpression(highlight, data)
            : highlight
        ) {
          toolbars.push(
            <i
              key="highlight"
              className={cx('Card-highlight', highlightClassName)}
            />
          );
        }
      }
  
      if (selectable && !hideCheckToggler) {
        toolbars.push(
          <Checkbox
            key="check"
            className={cx('Card-checkbox')}
            type={multiple !== false ? 'checkbox' : 'radio'}
            disabled={!checkable}
            checked={selected}
            onChange={this.handleCheck}
          />
        );
      }
  
      if (Array.isArray(toolbar) && headerToolbarVisible) {
        toolbar.forEach((action, index) =>
          toolbars.push(
            render(
              `toolbar/${index}`,
              {
                type: 'button',
                level: 'link',
                size: 'sm',
                ...(action as any)
              },
              {
                key: index,
                onClick: undefined
              }
            )
          )
        );
      }
  
      if (dragging) {
        toolbars.push(
          <div className={cx('Card-dragBtn')}>
            <Icon icon="drag-bar" className="icon" />
          </div>
        );
      }
  
      return toolbars.length ? (
        <div className={cx('Card-toolbar')}>{toolbars}</div>
      ) : null;
    }
  
    renderActions() {
      const {
        actions,
        render,
        dragging,
        actionsCount,
        data,
        classnames: cx,
        footerToolbarVisible
      } = this.props;

      if (!footerToolbarVisible) {
        return null;
      }
      if (Array.isArray(actions)) {
        const group = padArr(
          actions.filter(item => isVisible(item, data)),
          actionsCount
        );
        return group.map((actions, groupIndex) => (
          <div key={groupIndex} className={cx('Card-actions')}>
            {actions.map((action, index) => {
              const size = action.size || 'sm';
  
              return render(
                `action/${index}`,
                {
                  level: 'link',
                  type: 'button',
                  ...action,
                  testid: action.testid ? filter(action.testid, data) : index,
                  size
                },
                {
                  isMenuItem: true,
                  key: index,
                  index,
                  disabled: dragging || isDisabled(action, data),
                  className: cx(
                    'Card-action',
                    filterClassNameObject(
                      action.className || `${size ? `Card-action--${size}` : ''}`,
                      data
                    ),
                    {
                      'is-disabled': isDisabled(action, data)
                    }
                  ),
                  componentClass: 'a',
                  onAction: this.handleAction,
                  onClick: undefined
                }
              );
            })}
          </div>
        ));
      }
      return;
    }
  
    renderChild(
      node: SchemaNode,
      region: string = 'body',
      key: any = 0
    ): React.ReactNode {
      const {render} = this.props;
  
      if (typeof node === 'string' || typeof node === 'number') {
        return render(region, node, {key}) as JSX.Element;
      }
  
      const childNode: Schema = node as Schema;
  
      if (childNode.type === 'hbox' || childNode.type === 'grid') {
        return render(region, node, {
          key,
          itemRender: this.itemRender.bind(this)
        }) as JSX.Element;
      }
  
      return this.renderField(region, childNode, key, this.props);
    }
  
    itemRender(field: any, index: number, props: any) {
      return this.renderField(`column/${index}`, field, index, props);
    }
  
    renderField(region: string, field: Schema, key: any, props: any) {
      const {render, classnames: cx, itemIndex} = props;
      const useCardLabel = props?.useCardLabel !== false;
      const data = this.props.data;
      if (!field || !isVisible(field, data)) {
        return;
      }
  
      const $$id = field.$$id ? `${field.$$id}-field` : '';
  
      return (
        <div className={cx('Card-field')} key={key}>
          {useCardLabel && field.label ? (
            <label className={cx('Card-fieldLabel', field.labelClassName)}>
              {field.label}
            </label>
          ) : null}
  
          {
            render(
              region,
              {
                ...field,
                field: field,
                $$id,
                type: 'card-item-field'
              },
              {
                useCardLabel,
                className: cx(
                  'Card-fieldValue',
                  filterClassNameObject(field.className, data)
                ),
                rowIndex: itemIndex,
                colIndex: key,
                // 同 cell 里面逻辑一样，不要下发 value
                // value: field.name ? resolveVariable(field.name, data) : undefined,
                popOverContainer: this.getPopOverContainer,
                onAction: this.handleAction,
                onQuickChange: this.handleQuickChange
              }
            ) as JSX.Element
          }
        </div>
      );
    }
  
    renderBody() {
      const {body, contentVisible} = this.props;
  
      if (!body) {
        return null;
      }
      if (!contentVisible) {
        return null;
      }
      if (Array.isArray(body)) {
        return body.map((child, index) =>
          this.renderChild(child, `body/${index}`, index)
        );
      }
  
      return this.renderChild(body, 'body');
    }
  
    rederTitle() {
      const {render, data, header} = this.props;
      if (header) {
        const {title: titleTpl} = header || {};
        const title = filter(titleTpl, data);
        return header.titleVisible ? render('title', titleTpl!) : undefined;
      }
      return;
    }
  
    renderSubTitle() {
      const {render, data, header} = this.props;
      if (header) {
        const {subTitle: subTitleTpl} = header || {};
  
        return header.subTitleVisible && subTitleTpl
          ? render('sub-title', subTitleTpl, {
              data
            })
          : undefined;
      }
      return;
    }
  
    renderSubTitlePlaceholder() {
      const {render, header, classnames: cx} = this.props;
      if (header) {
        const {subTitlePlaceholder} = header || {};
  
        return subTitlePlaceholder
          ? render('sub-title', subTitlePlaceholder, {
              className: cx('Card-placeholder')
            })
          : undefined;
      }
      return;
    }
  
    renderDesc() {
      const {render, data, header} = this.props;
  
      if (header) {
        const {desc: descTpl, description: descriptionTpl} = header || {};
        const desc = filter(descriptionTpl! || descTpl!, data);
        return header.descVisible
          ? render('desc', descriptionTpl! || descTpl!, {
              className: !desc ? 'text-muted' : null
            })
          : undefined;
      }
      return;
    }
  
    renderDescPlaceholder() {
      const {render, header} = this.props;
  
      if (header) {
        const descPlaceholder =
          header.descriptionPlaceholder || header.descPlaceholder;
        return descPlaceholder
          ? render('desc', descPlaceholder, {
              className: !descPlaceholder ? 'text-muted' : null
            })
          : undefined;
      }
      return;
    }
  
    renderAvatar() {
      const {data, header} = this.props;
      if (header) {
        const {avatar: avatarTpl} = header || {};
        const avatar = filter(avatarTpl, data, '| raw');
        return avatar ? avatar : undefined;
      }
      return;
    }
  
    renderAvatarText() {
      const {render, data, header} = this.props;
      if (header) {
        const {avatarText: avatarTextTpl} = header || {};
  
        const avatarText = filter(avatarTextTpl, data);
  
        return avatarText ? render('avatarText', avatarTextTpl!) : undefined;
      }
      return;
    }
  
    renderSecondary() {
      const {render, data, secondary: secondaryTextTpl} = this.props;
  
      const secondary = filter(secondaryTextTpl, data);
      return secondary ? render('secondary', secondaryTextTpl!) : undefined;
    }
  
    renderAvatarTextStyle() {
      const {header, data} = this.props;
      if (header) {
        const {avatarText: avatarTextTpl, avatarTextBackground} = header;
        const avatarText = filter(avatarTextTpl, data);
        const avatarTextStyle: PlainObject = {};
        if (avatarText && avatarTextBackground && avatarTextBackground.length) {
          avatarTextStyle['background'] =
            avatarTextBackground[
              Math.abs(hashCode(avatarText)) % avatarTextBackground.length
            ];
        }
        return avatarTextStyle;
      }
      return;
    }
  
    renderMedia() {
      const {media, classnames: cx, render, region, data} = this.props;
      if (media) {
        const {type, url, className, autoPlay, isLive, poster} = media;
        const mediaUrl = resolveVariableAndFilter(url, data, '| raw');
  
        if (type === 'image' && mediaUrl) {
          return (
            <img
              className={cx('Card-multiMedia-img', className)}
              src={mediaUrl}
              style={{width: media.width, height: media.height}}
            />
          );
        } else if (type === 'video' && mediaUrl) {
          return (
            <div className={cx('Card-multiMedia-video', className)} style={{width: media.width, height: media.height}}>
              {
                render(region, {
                  type: type,
                  autoPlay: autoPlay,
                  poster: poster,
                  src: mediaUrl,
                  isLive: isLive
                }) as JSX.Element
              }
            </div>
          );
        }
      }
      return;
    }
  
    render() {
      const {
        header,
        className,
        avatarClassName,
        avatarTextClassName,
        descClassName,
        descriptionClassName,
        titleClassName,
        subTitleClassName,
        bodyClassName,
        imageClassName,
        headerClassName,
        secondaryClassName,
        footerClassName,
        mediaClassName,
        media,
        themeCss,
        wrapperCustomStyle,
        id,
        env,
        classnames: cx,
        ...rest
      } = this.props;
      const ctx = this.props.data;
      const headerCn =
        filterClassNameObject(header?.className, ctx) || headerClassName;
      const titleCn =
        filterClassNameObject(header?.titleClassName, ctx) || titleClassName;
      const subTitleCn =
        filterClassNameObject(header?.subTitleClassName, ctx) ||
        subTitleClassName;
      const descCn =
        filterClassNameObject(header?.descClassName, ctx) || descClassName;
      const descriptionCn =
        filterClassNameObject(header?.descriptionClassName, ctx) ||
        descriptionClassName ||
        descCn;
      const avatarTextCn =
        filterClassNameObject(header?.avatarTextClassName, ctx) ||
        avatarTextClassName;
      const avatarCn =
        filterClassNameObject(header?.avatarClassName, ctx) || avatarClassName;
      const imageCn =
        filterClassNameObject(header?.imageClassName, ctx) || imageClassName;
      const mediaPosition = media?.position;
  
      // avatarBorderRadius: '50%',
      // avatarWidth: 64,
      // avatarHeight: 64,

      return (
        <Card
          {...rest}
          title={this.rederTitle()}
          subTitle={this.renderSubTitle()}
          subTitlePlaceholder={this.renderSubTitlePlaceholder()}
          description={this.renderDesc()}
          descriptionPlaceholder={this.renderDescPlaceholder()}
          children={
            <>
              {this.renderBody()}
              <CustomStyle
                {...this.props}
                config={{
                  wrapperCustomStyle,
                  id,
                  themeCss,
                  classNames: [
                    {
                      key: 'baseControlClassName',
                      weights: {
                        default: {
                          important: true
                        },
                        hover: {
                          important: true
                        },
                        active: {
                          important: true
                        }
                      }
                    },
                    {
                      key: 'bodyControlClassName'
                    },
                    {
                      key: 'titleControlClassName'
                    },
                    {
                      key: 'subTitleControlClassName'
                    },
                    {
                      key: 'descriptionControlClassName'
                    },
                    {
                      key: 'headerControlClassName'
                    }
                  ]
                }}
                env={env}
              />
            </>
          }
          // children={this.renderBody()}
          actions={this.renderActions()}
          avatar={this.renderAvatar()}
          avatarText={this.renderAvatarText()}
          // secondary={this.renderSecondary()}
          toolbar={this.renderToolbar()}
          avatarClassName={avatarCn}
          avatarTextStyle={this.renderAvatarTextStyle()}
          avatarTextClassName={avatarTextCn}
          className={cx(
            className,
            setThemeClassName({
              name: 'baseControlClassName',
              id,
              themeCss,
              ...this.props
            }),
            setThemeClassName({
              name: 'wrapperCustomStyle',
              id,
              themeCss: wrapperCustomStyle,
              ...this.props
            })
          )}
          titleClassName={cx(
            titleCn,
            setThemeClassName({
              ...this.props,
              name: 'titleControlClassName',
              id,
              themeCss
            })
          )}
          subTitleClassName={cx(
            subTitleCn,
            setThemeClassName({
              ...this.props,
              name: 'subTitleControlClassName',
              id,
              themeCss
            })
          )}
          descriptionClassName={cx(
            descriptionCn,
            setThemeClassName({
              ...this.props,
              name: 'descriptionControlClassName',
              id,
              themeCss
            })
          )}
          media={this.renderMedia()}
          mediaPosition={mediaPosition}
          imageClassName={imageCn}
          // headerClassName={headerCn}
          headerClassName={cx(  
            headerCn,
            setThemeClassName({
              ...this.props,
              name: 'headerControlClassName',
              id,
              themeCss
            })
          )}
          footerClassName={footerClassName}
          secondaryClassName={secondaryClassName}
          bodyClassName={cx(
            bodyClassName,
            setThemeClassName({
              ...this.props,
              name: 'bodyControlClassName',
              id,
              themeCss
            })
          )}
          onClick={this.isHaveLink() ? this.handleClick : this.handleCheck}
          avatarBorderRadius={header?.avatarBorderRadius}
          avatarWidth={header?.avatarWidth}
          avatarHeight={header?.avatarHeight}
        ></Card>
      );
    }
  }
  