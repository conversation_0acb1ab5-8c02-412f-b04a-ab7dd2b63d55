{"byFile": {"api.js": ["actionApiControl", "apiControl", "apiString", "initFetch", "interval", "loadingConfig", "proxy", "silentPolling", "source", "stopAutoRefreshWhen"], "common.js": ["DataPickerControl", "actionFinishLabel", "actionNextLabel", "actionNextSaveLabel", "actionPrevLabel", "addOnLabel", "anchorNavTitle", "anchorTitle", "app-page", "app-page-args", "audioUrl", "autoFill", "autoFillApi", "avatarText", "backgroundImageUrl", "badge", "borderMode", "bos", "btnLabel", "button-manager", "buttonLevel", "caption", "cardDesc", "cardSubTitle", "cardTitle", "cardsPlaceholder", "className", "clearValueOnHidden", "clearable", "closable", "collapse", "collapseGroup", "collapseOpenHeader", "combo-container", "conditionFormulaControl", "data", "dateShortCutControl", "deferField", "deleteConfirmText", "description", "disabled", "divider", "draggableTip", "embed", "endPlaceholder", "eventControl", "expression", "expressionFormulaControl", "fetchFailed", "fetchSuccess", "fieldSet", "fileUrl", "formItemExtraName", "formItemInline", "formItemMode", "formItemName", "formItemSize", "formulaControl", "hidden", "hint", "icon", "iconLink", "imageCaption", "imageDesc", "imageTitle", "imageUrl", "imgCaption", "inputArrayItem", "inputBody", "inputForbid", "inputType", "kilobitSeparator", "label", "labelHide", "markdownBody", "matrixColumnLabel", "matrixRowLabel", "matrixRowTitle", "max<PERSON><PERSON><PERSON>", "maximum", "menuTpl", "<PERSON><PERSON><PERSON><PERSON>", "minimum", "name", "nav-badge", "nav-default-active", "numberSwitchDefaultValue", "offText", "onText", "onlyClassNameTab", "<PERSON><PERSON><PERSON><PERSON>", "operationLabel", "optionsLabel", "optionsMenuTpl", "optionsTip", "pageData", "pageSubTitle", "pageTitle", "placeholder", "prefix", "primaryField", "propertyContent", "propertyLabel", "propertyTitle", "quickSaveFailed", "quickSaveSuccess", "readonly", "reload", "<PERSON><PERSON><PERSON><PERSON>", "required", "richText", "saveFailed", "saveOrderFailed", "saveOrderSuccess", "saveSuccess", "searchable", "selectDateRangeType", "selectDateType", "showCounter", "signBtn", "size", "sortable", "sourceBindControl", "startPlaceholder", "static", "status", "statusLabel", "stepDescription", "stepSubTitle", "submitText", "suffix", "switch", "switchDefaultValue", "switchOption", "tableCellPlaceholder", "tableCellRemark", "tablePlaceholder", "tabs", "taskNameLabel", "taskRemark", "textareaDefaultValue", "textareaFormulaControl", "theme:labelHide", "title", "tooltip", "tpl:btnLabel", "tplFormulaControl", "unit", "uploadType", "utc", "validateFailed", "valueFormula", "virtualItemHeight", "virtualThreshold", "visible", "with<PERSON><PERSON><PERSON>"], "horizontal.js": ["horizontal", "horizontal-align", "labelAlign", "leftFixed", "leftRate", "subFormHorizontal", "subFormHorizontalMode", "subFormItemMode"], "layout.js": ["layout:alignItems", "layout:display", "layout:flex", "layout:flex-basis", "layout:flex-grow", "layout:flex-layout", "layout:flex-setting", "layout:flex-wrap", "layout:flexDirection", "layout:height", "layout:inset", "layout:isFixedHeight", "layout:isFixedWidth", "layout:justify<PERSON><PERSON>nt", "layout:margin-center", "layout:max-height", "layout:max-width", "layout:min-height", "layout:min-width", "layout:originPosition", "layout:overflow-x", "layout:overflow-y", "layout:padding", "layout:position", "layout:sorption", "layout:sticky", "layout:stickyPosition", "layout:textAlign", "layout:width", "layout:width:v2", "layout:wrapper-contanier", "layout:z-index"], "options.js": ["addApi", "checkAll", "checkAllLabel", "creatable", "createBtnLabel", "dataMap", "deleteApi", "delimiter", "editApi", "editInitApi", "editable", "extractValue", "hideNodePathLabel", "joinValues", "mapSourceControl", "multiple", "navControl", "optionAddControl", "optionControl", "optionControlV2", "optionDeleteControl", "optionEditControl", "options", "ref", "removable", "selectFirst", "strictMode", "timelineItemControl", "tree", "treeOptionControl"], "remark.js": ["labelRemark", "remark"], "style.js": ["animation", "style:classNames", "style:common", "style:formItem", "style:others", "style:widthHeight", "theme:base", "theme:border", "theme:colorPicker", "theme:common", "theme:cssCode", "theme:font", "theme:form-description", "theme:form-label", "theme:formItem", "theme:icon", "theme:padding<PERSON>ndMargin", "theme:radius", "theme:select", "theme:shadow", "theme:singleCssCode", "theme:size"], "validations.js": ["submitOnChange", "validateOnChange", "validation", "validationApiControl", "validationControl", "validationErrors", "validations"]}, "byCategory": {"接口配置": ["actionApiControl", "apiControl", "apiString", "initFetch", "interval", "loadingConfig", "proxy", "silentPolling", "source", "stopAutoRefreshWhen"], "通用配置": ["DataPickerControl", "actionFinishLabel", "actionNextLabel", "actionNextSaveLabel", "actionPrevLabel", "addOnLabel", "anchorNavTitle", "anchorTitle", "app-page", "app-page-args", "audioUrl", "autoFill", "autoFillApi", "avatarText", "backgroundImageUrl", "badge", "borderMode", "bos", "btnLabel", "button-manager", "buttonLevel", "caption", "cardDesc", "cardSubTitle", "cardTitle", "cardsPlaceholder", "className", "clearValueOnHidden", "clearable", "closable", "collapse", "collapseGroup", "collapseOpenHeader", "combo-container", "conditionFormulaControl", "data", "dateShortCutControl", "deferField", "deleteConfirmText", "description", "disabled", "divider", "draggableTip", "embed", "endPlaceholder", "eventControl", "expression", "expressionFormulaControl", "fetchFailed", "fetchSuccess", "fieldSet", "fileUrl", "formItemExtraName", "formItemInline", "formItemMode", "formItemName", "formItemSize", "formulaControl", "hidden", "hint", "icon", "iconLink", "imageCaption", "imageDesc", "imageTitle", "imageUrl", "imgCaption", "inputArrayItem", "inputBody", "inputForbid", "inputType", "kilobitSeparator", "label", "labelHide", "markdownBody", "matrixColumnLabel", "matrixRowLabel", "matrixRowTitle", "max<PERSON><PERSON><PERSON>", "maximum", "menuTpl", "<PERSON><PERSON><PERSON><PERSON>", "minimum", "name", "nav-badge", "nav-default-active", "numberSwitchDefaultValue", "offText", "onText", "onlyClassNameTab", "<PERSON><PERSON><PERSON><PERSON>", "operationLabel", "optionsLabel", "optionsMenuTpl", "optionsTip", "pageData", "pageSubTitle", "pageTitle", "placeholder", "prefix", "primaryField", "propertyContent", "propertyLabel", "propertyTitle", "quickSaveFailed", "quickSaveSuccess", "readonly", "reload", "<PERSON><PERSON><PERSON><PERSON>", "required", "richText", "saveFailed", "saveOrderFailed", "saveOrderSuccess", "saveSuccess", "searchable", "selectDateRangeType", "selectDateType", "showCounter", "signBtn", "size", "sortable", "sourceBindControl", "startPlaceholder", "static", "status", "statusLabel", "stepDescription", "stepSubTitle", "submitText", "suffix", "switch", "switchDefaultValue", "switchOption", "tableCellPlaceholder", "tableCellRemark", "tablePlaceholder", "tabs", "taskNameLabel", "taskRemark", "textareaDefaultValue", "textareaFormulaControl", "theme:labelHide", "title", "tooltip", "tpl:btnLabel", "tplFormulaControl", "unit", "uploadType", "utc", "validateFailed", "valueFormula", "virtualItemHeight", "virtualThreshold", "visible", "with<PERSON><PERSON><PERSON>"], "水平布局配置": ["horizontal", "horizontal-align", "labelAlign", "leftFixed", "leftRate", "subFormHorizontal", "subFormHorizontalMode", "subFormItemMode"], "布局配置": ["layout:alignItems", "layout:display", "layout:flex", "layout:flex-basis", "layout:flex-grow", "layout:flex-layout", "layout:flex-setting", "layout:flex-wrap", "layout:flexDirection", "layout:height", "layout:inset", "layout:isFixedHeight", "layout:isFixedWidth", "layout:justify<PERSON><PERSON>nt", "layout:margin-center", "layout:max-height", "layout:max-width", "layout:min-height", "layout:min-width", "layout:originPosition", "layout:overflow-x", "layout:overflow-y", "layout:padding", "layout:position", "layout:sorption", "layout:sticky", "layout:stickyPosition", "layout:textAlign", "layout:width", "layout:width:v2", "layout:wrapper-contanier", "layout:z-index"], "选项配置": ["addApi", "checkAll", "checkAllLabel", "creatable", "createBtnLabel", "dataMap", "deleteApi", "delimiter", "editApi", "editInitApi", "editable", "extractValue", "hideNodePathLabel", "joinValues", "mapSourceControl", "multiple", "navControl", "optionAddControl", "optionControl", "optionControlV2", "optionDeleteControl", "optionEditControl", "options", "ref", "removable", "selectFirst", "strictMode", "timelineItemControl", "tree", "treeOptionControl"], "备注配置": ["labelRemark", "remark"], "样式配置": ["animation", "style:classNames", "style:common", "style:formItem", "style:others", "style:widthHeight", "theme:base", "theme:border", "theme:colorPicker", "theme:common", "theme:cssCode", "theme:font", "theme:form-description", "theme:form-label", "theme:formItem", "theme:icon", "theme:padding<PERSON>ndMargin", "theme:radius", "theme:select", "theme:shadow", "theme:singleCssCode", "theme:size"], "验证配置": ["submitOnChange", "validateOnChange", "validation", "validationApiControl", "validationControl", "validationErrors", "validations"]}, "allTypes": ["DataPickerControl", "actionApiControl", "actionFinishLabel", "actionNextLabel", "actionNextSaveLabel", "actionPrevLabel", "addApi", "addOnLabel", "anchorNavTitle", "anchorTitle", "animation", "apiControl", "apiString", "app-page", "app-page-args", "audioUrl", "autoFill", "autoFillApi", "avatarText", "backgroundImageUrl", "badge", "borderMode", "bos", "btnLabel", "button-manager", "buttonLevel", "caption", "cardDesc", "cardSubTitle", "cardTitle", "cardsPlaceholder", "checkAll", "checkAllLabel", "className", "clearValueOnHidden", "clearable", "closable", "collapse", "collapseGroup", "collapseOpenHeader", "combo-container", "conditionFormulaControl", "creatable", "createBtnLabel", "data", "dataMap", "dateShortCutControl", "deferField", "deleteApi", "deleteConfirmText", "delimiter", "description", "disabled", "divider", "draggableTip", "editApi", "editInitApi", "editable", "embed", "endPlaceholder", "eventControl", "expression", "expressionFormulaControl", "extractValue", "fetchFailed", "fetchSuccess", "fieldSet", "fileUrl", "formItemExtraName", "formItemInline", "formItemMode", "formItemName", "formItemSize", "formulaControl", "hidden", "hideNodePathLabel", "hint", "horizontal", "horizontal-align", "icon", "iconLink", "imageCaption", "imageDesc", "imageTitle", "imageUrl", "imgCaption", "initFetch", "inputArrayItem", "inputBody", "inputForbid", "inputType", "interval", "joinValues", "kilobitSeparator", "label", "labelAlign", "labelHide", "labelRemark", "layout:alignItems", "layout:display", "layout:flex", "layout:flex-basis", "layout:flex-grow", "layout:flex-layout", "layout:flex-setting", "layout:flex-wrap", "layout:flexDirection", "layout:height", "layout:inset", "layout:isFixedHeight", "layout:isFixedWidth", "layout:justify<PERSON><PERSON>nt", "layout:margin-center", "layout:max-height", "layout:max-width", "layout:min-height", "layout:min-width", "layout:originPosition", "layout:overflow-x", "layout:overflow-y", "layout:padding", "layout:position", "layout:sorption", "layout:sticky", "layout:stickyPosition", "layout:textAlign", "layout:width", "layout:width:v2", "layout:wrapper-contanier", "layout:z-index", "leftFixed", "leftRate", "loadingConfig", "mapSourceControl", "markdownBody", "matrixColumnLabel", "matrixRowLabel", "matrixRowTitle", "max<PERSON><PERSON><PERSON>", "maximum", "menuTpl", "<PERSON><PERSON><PERSON><PERSON>", "minimum", "multiple", "name", "nav-badge", "nav-default-active", "navControl", "numberSwitchDefaultValue", "offText", "onText", "onlyClassNameTab", "<PERSON><PERSON><PERSON><PERSON>", "operationLabel", "optionAddControl", "optionControl", "optionControlV2", "optionDeleteControl", "optionEditControl", "options", "optionsLabel", "optionsMenuTpl", "optionsTip", "pageData", "pageSubTitle", "pageTitle", "placeholder", "prefix", "primaryField", "propertyContent", "propertyLabel", "propertyTitle", "proxy", "quickSaveFailed", "quickSaveSuccess", "readonly", "ref", "reload", "remark", "<PERSON><PERSON><PERSON><PERSON>", "removable", "required", "richText", "saveFailed", "saveOrderFailed", "saveOrderSuccess", "saveSuccess", "searchable", "selectDateRangeType", "selectDateType", "selectFirst", "showCounter", "signBtn", "silentPolling", "size", "sortable", "source", "sourceBindControl", "startPlaceholder", "static", "status", "statusLabel", "stepDescription", "stepSubTitle", "stopAutoRefreshWhen", "strictMode", "style:classNames", "style:common", "style:formItem", "style:others", "style:widthHeight", "subFormHorizontal", "subFormHorizontalMode", "subFormItemMode", "submitOnChange", "submitText", "suffix", "switch", "switchDefaultValue", "switchOption", "tableCellPlaceholder", "tableCellRemark", "tablePlaceholder", "tabs", "taskNameLabel", "taskRemark", "textareaDefaultValue", "textareaFormulaControl", "theme:base", "theme:border", "theme:colorPicker", "theme:common", "theme:cssCode", "theme:font", "theme:form-description", "theme:form-label", "theme:formItem", "theme:icon", "theme:labelHide", "theme:padding<PERSON>ndMargin", "theme:radius", "theme:select", "theme:shadow", "theme:singleCssCode", "theme:size", "timelineItemControl", "title", "tooltip", "tpl:btnLabel", "tplFormulaControl", "tree", "treeOptionControl", "unit", "uploadType", "utc", "validateFailed", "validateOnChange", "validation", "validationApiControl", "validationControl", "validationErrors", "validations", "valueFormula", "virtualItemHeight", "virtualThreshold", "visible", "with<PERSON><PERSON><PERSON>"]}