# Excel导入页面组件

## 概述

ExcelImportPage 是一个用于替换原有 AMISRenderer Excel导入弹窗的新组件。该组件提供了完整的Excel文件导入功能，包括文件上传、数据预览、字段映射和数据导入等步骤。

## 功能特性

### 1. 文件上传
- 支持 `.xlsx` 和 `.xls` 格式的Excel文件
- 文件大小限制：2MB
- 拖拽上传支持
- 文件格式和大小验证

### 2. 数据解析
- 自动解析Excel文件的第一个工作表
- 提取表头作为列名
- 过滤空行数据
- 显示解析结果统计

### 3. 字段映射
- 可视化的字段映射界面
- 支持设置目标字段名
- 字段类型选择（文本、数字、日期、布尔值）
- 必填字段标记

### 4. 数据预览
- 显示前10行数据预览
- 按照映射关系展示数据
- 确认导入前的最后检查

### 5. 数据验证
- 必填字段验证
- 数据格式检查
- 错误提示和处理

## 使用方法

```tsx
import ExcelImportPage from '@/component/ExcelImportPage';

// 在组件中使用
<ExcelImportPage
  show={openExcelImportPopup}
  onClose={() => setOpenExcelImportPopup(false)}
  onConfirm={(data) => handleExcelImport(data)}
/>
```

## Props

| 属性名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| show | boolean | 是 | 控制弹窗显示/隐藏 |
| onClose | () => void | 是 | 关闭弹窗的回调函数 |
| onConfirm | (data: any) => void | 是 | 确认导入的回调函数 |

## 回调数据格式

`onConfirm` 回调函数接收的数据格式：

```typescript
{
  fileName: string;           // 文件名
  columnMappings: Array<{     // 字段映射配置
    excelColumn: string;      // Excel列名
    targetField: string;      // 目标字段名
    fieldType: string;        // 字段类型
    required: boolean;        // 是否必填
  }>;
  data: Array<{              // 转换后的数据
    [fieldName: string]: any;
  }>;
  totalRows: number;         // 总行数
}
```

## 技术实现

### 依赖库
- `xlsx`: Excel文件解析
- `amis`: UI组件库
- `React`: 前端框架

### 核心功能
1. **文件解析**: 使用 `xlsx` 库解析Excel文件
2. **UI渲染**: 使用 amis 的 schema 方式渲染界面
3. **状态管理**: 使用 React Hooks 管理组件状态
4. **数据转换**: 将Excel数据转换为目标格式

## 文件结构

```
src/component/ExcelImportPage/
├── index.tsx          # 主组件文件
├── index.scss         # 样式文件
└── README.md          # 说明文档
```

## 样式说明

组件使用了 amis 的样式类名，支持：
- 响应式设计
- 暗色主题
- 自定义样式覆盖

## 注意事项

1. **Excel文件要求**:
   - 不能包含合并单元格
   - 第一行必须是表头
   - 表头不能有空单元格

2. **浏览器兼容性**:
   - 需要支持 FileReader API
   - 需要支持 ArrayBuffer

3. **性能考虑**:
   - 大文件可能导致解析缓慢
   - 建议限制文件大小和行数

## 更新日志

### v1.1.0 (最新)
- **修改第四步显示内容**: 将第四步从"数据预览"改为"数据导入完成"状态
- **流程优化**: 在第三步点击"下一步"时就创建页面并上传数据
- **接口预留**: 新增 `uploadExcelData` 函数，预留完整的数据上传接口
- **UI改进**: 第四步显示绿色成功图标和导入统计信息

#### 预留接口说明
```
POST /admin-api/system/excel-data/upload
```

请求参数:
```json
{
  "pageId": 123,
  "data": [{"field1": "value1"}],
  "totalRows": 100,
  "fileName": "example.xlsx"
}
```

### v1.0.0
- 初始版本
- 基本的Excel导入功能
- 支持字段映射和数据预览
- 替换原有的AMISRenderer实现
