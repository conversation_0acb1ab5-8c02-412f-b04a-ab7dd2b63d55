import React, {useState, useEffect, useRef} from 'react';
import {<PERSON><PERSON><PERSON>, RendererProps} from 'amis';
import {getSchemaTpl} from 'amis-editor-core';
import {showRenderers} from '../editor/disabledRenderers';
import {
  getSchemaClassification,
  getConfigCategoryMap,
  getSchemaByFile
} from '../utils/componentUtil/componentUtil';
import AMISRenderer from '@/component/AMISRenderer';

interface ComponentViewerProps extends RendererProps {
  label?: string;
  selectedComponent?: string;
}

// 存储所有获取到的schema类型
const ALL_SCHEMA_TYPES: string[] = [];
try {
  // 尝试从文件中获取所有可能的schema类型
  const schemaFiles = getSchemaByFile();
  if (schemaFiles) {
    Object.entries(schemaFiles).forEach(([file, schemas]) => {
      if (Array.isArray(schemas)) {
        schemas.forEach(schema => {
          if (
            typeof schema === 'string' &&
            !ALL_SCHEMA_TYPES.includes(schema)
          ) {
            ALL_SCHEMA_TYPES.push(schema);
          }
        });
      }
    });
  }
} catch (error) {
  console.error('获取schema类型失败:', error);
}

// 配置项分类
const CONFIG_CATEGORIES = {
  接口配置: [
    'actionApiControl',
    'apiControl',
    'apiString',
    'initFetch',
    'interval',
    'loadingConfig',
    'proxy',
    'silentPolling',
    'source',
    'stopAutoRefreshWhen'
  ],
  通用配置: [
    'DataPickerControl',
    'actionFinishLabel',
    'actionNextLabel',
    'actionNextSaveLabel',
    'actionPrevLabel',
    'addOnLabel',
    'anchorNavTitle',
    'anchorTitle',
    'app-page',
    'app-page-args',
    'audioUrl',
    'autoFill',
    'autoFillApi',
    'avatarText',
    'backgroundImageUrl',
    'badge',
    'borderMode',
    'bos',
    'btnLabel',
    'button-manager',
    'buttonLevel',
    'caption',
    'cardDesc',
    'cardSubTitle',
    'cardTitle',
    'cardsPlaceholder',
    'className',
    'clearValueOnHidden',
    'clearable',
    'closable',
    'collapse',
    'collapseGroup',
    'collapseOpenHeader',
    'combo-container',
    'conditionFormulaControl',
    'data',
    'dateShortCutControl',
    'deferField',
    'deleteConfirmText',
    'description',
    'disabled',
    'divider',
    'draggableTip',
    'embed',
    'endPlaceholder',
    'eventControl',
    'expression',
    'expressionFormulaControl',
    'fetchFailed',
    'fetchSuccess',
    'fieldSet',
    'fileUrl',
    'formItemExtraName',
    'formItemInline',
    'formItemMode',
    'formItemName',
    'formItemSize',
    'formulaControl',
    'hidden',
    'hint',
    'icon',
    'iconLink',
    'imageCaption',
    'imageDesc',
    'imageTitle',
    'imageUrl',
    'imgCaption',
    'inputArrayItem',
    'inputBody',
    'inputForbid',
    'inputType',
    'kilobitSeparator',
    'label',
    'labelHide',
    'markdownBody',
    'matrixColumnLabel',
    'matrixRowLabel',
    'matrixRowTitle',
    'maxLength',
    'maximum',
    'menuTpl',
    'minLength',
    'minimum',
    'name',
    'nav-badge',
    'nav-default-active',
    'numberSwitchDefaultValue',
    'offText',
    'onText',
    'onlyClassNameTab',
    'onlyLeaf',
    'operationLabel',
    'optionsLabel',
    'optionsMenuTpl',
    'optionsTip',
    'pageData',
    'pageSubTitle',
    'pageTitle',
    'placeholder',
    'prefix',
    'primaryField',
    'propertyContent',
    'propertyLabel',
    'propertyTitle',
    'quickSaveFailed',
    'quickSaveSuccess',
    'readonly',
    'reload',
    'remarkLabel',
    'required',
    'richText',
    'saveFailed',
    'saveOrderFailed',
    'saveOrderSuccess',
    'saveSuccess',
    'searchable',
    'selectDateRangeType',
    'selectDateType',
    'showCounter',
    'signBtn',
    'size',
    'sortable',
    'sourceBindControl',
    'startPlaceholder',
    'static',
    'status',
    'statusLabel',
    'stepDescription',
    'stepSubTitle',
    'submitText',
    'suffix',
    'switch',
    'switchDefaultValue',
    'switchOption',
    'tableCellPlaceholder',
    'tableCellRemark',
    'tablePlaceholder',
    'tabs',
    'taskNameLabel',
    'taskRemark',
    'textareaDefaultValue',
    'textareaFormulaControl',
    'theme:labelHide',
    'title',
    'tooltip',
    'tpl:btnLabel',
    'tplFormulaControl',
    'unit',
    'uploadType',
    'utc',
    'validateFailed',
    'valueFormula',
    'virtualItemHeight',
    'virtualThreshold',
    'visible',
    'withUnit'
  ],
  水平布局配置: [
    'horizontal',
    'horizontal-align',
    'labelAlign',
    'leftFixed',
    'leftRate',
    'subFormHorizontal',
    'subFormHorizontalMode',
    'subFormItemMode'
  ],
  布局配置: [
    'layout:alignItems',
    'layout:display',
    'layout:flex',
    'layout:flex-basis',
    'layout:flex-grow',
    'layout:flex-layout',
    'layout:flex-setting',
    'layout:flex-wrap',
    'layout:flexDirection',
    'layout:height',
    'layout:inset',
    'layout:isFixedHeight',
    'layout:isFixedWidth',
    'layout:justifyContent',
    'layout:margin-center',
    'layout:max-height',
    'layout:max-width',
    'layout:min-height',
    'layout:min-width',
    'layout:originPosition',
    'layout:overflow-x',
    'layout:overflow-y',
    'layout:padding',
    'layout:position',
    'layout:sorption',
    'layout:sticky',
    'layout:stickyPosition',
    'layout:textAlign',
    'layout:width',
    'layout:width:v2',
    'layout:wrapper-contanier',
    'layout:z-index'
  ],
  选项配置: [
    'addApi',
    'checkAll',
    'checkAllLabel',
    'creatable',
    'createBtnLabel',
    'dataMap',
    'deleteApi',
    'delimiter',
    'editApi',
    'editInitApi',
    'editable',
    'extractValue',
    'hideNodePathLabel',
    'joinValues',
    'mapSourceControl',
    'multiple',
    'navControl',
    'optionAddControl',
    'optionControl',
    'optionControlV2',
    'optionDeleteControl',
    'optionEditControl',
    'options',
    'ref',
    'removable',
    'selectFirst',
    'strictMode',
    'timelineItemControl',
    'tree',
    'treeOptionControl'
  ],
  备注配置: ['labelRemark', 'remark'],
  样式配置: [
    'animation',
    'style:classNames',
    'style:common',
    'style:formItem',
    'style:others',
    'style:widthHeight',
    'theme:base',
    'theme:border',
    'theme:colorPicker',
    'theme:common',
    'theme:cssCode',
    'theme:font',
    'theme:form-description',
    'theme:form-label',
    'theme:formItem',
    'theme:icon',
    'theme:paddingAndMargin',
    'theme:radius',
    'theme:select',
    'theme:shadow',
    'theme:singleCssCode',
    'theme:size'
  ],
  验证配置: [
    'submitOnChange',
    'validateOnChange',
    'validation',
    'validationApiControl',
    'validationControl',
    'validationErrors',
    'validations'
  ]
};

// 配置项分类映射
const CONFIG_CATEGORY_MAP = getConfigCategoryMap(CONFIG_CATEGORIES);

// 从文件中提取名称，例如 action.tsx => action
const extractFileName = (path: string): string => {
  if (!path) return '';
  const parts = path.split('/');
  const fileName = parts[parts.length - 1];
  return fileName.split('.')[0];
};

const ComponentViewer: React.FC<ComponentViewerProps> = props => {
  const {label} = props;
  const [loading, setLoading] = useState<boolean>(true);
  const [categorizedConfigs, setCategorizedConfigs] = useState<
    Record<string, Record<string, any[]>>
  >({});
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {}
  );
  const [searchText, setSearchText] = useState<string>('');
  const [totalConfigCount, setTotalConfigCount] = useState<number>(0);
  const configsRef = useRef<Record<string, any>>({});
  // 当前显示的配置项引用
  const currentItemsRef = useRef<{type: string; schema: any}[]>([]);
  // 子分类的展开状态
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  // 清理对象以便安全显示（移除循环引用等）
  const cleanObjectForDisplay = (obj: any): any => {
    if (!obj) return obj;

    // 处理基本类型
    if (typeof obj !== 'object') return obj;

    // 处理数组
    if (Array.isArray(obj)) {
      return obj.map(item => cleanObjectForDisplay(item));
    }

    // 处理对象
    try {
      // 尝试序列化和反序列化来移除非JSON内容
      const cleaned = JSON.parse(JSON.stringify(obj));
      return cleaned;
    } catch (e) {
      // 如果序列化失败，手动复制安全属性
      const result: Record<string, any> = {};

      Object.keys(obj).forEach(key => {
        try {
          // 跳过函数和其他不可序列化的内容
          if (typeof obj[key] === 'function') return;
          if (key === 'parent' || key === 'root') return; // 通常导致循环引用

          const value = obj[key];
          if (typeof value === 'object' && value !== null) {
            result[key] = cleanObjectForDisplay(value);
          } else if (value !== undefined) {
            result[key] = value;
          }
        } catch {
          // 忽略任何处理属性时的错误
        }
      });

      return result;
    }
  };

  // 获取全部配置项
  useEffect(() => {
    setLoading(true);
    const allConfigTypes = [
      ...Object.values(CONFIG_CATEGORIES).reduce(
        (acc: string[], val) => acc.concat(val),
        []
      ),
      ...ALL_SCHEMA_TYPES.filter(type => type.length > 0 && !type.includes(':'))
    ];

    // 按文件分类的配置
    const fileCategories: Record<string, string[]> = {};
    const schemaFiles = getSchemaByFile();

    if (schemaFiles) {
      Object.entries(schemaFiles).forEach(([file, schemas]) => {
        if (Array.isArray(schemas)) {
          const fileName = extractFileName(file);
          fileCategories[`文件: ${fileName}`] = schemas.filter(Boolean);
        }
      });
    }

    const allSchemas: Record<string, any> = {};
    const allCategorized: Record<string, Record<string, any[]>> = {
      按文件分类: {},
      按类型分类: {}
    };

    // 确保文件类别初始化
    Object.keys(fileCategories).forEach(category => {
      allCategorized['按文件分类'][category] = [];
    });

    // 初始化类型分类
    Object.keys(CONFIG_CATEGORIES).forEach(category => {
      allCategorized['按类型分类'][category] = [];
    });

    // 添加其他类型分类
    allCategorized['按类型分类']['其他配置'] = [];

    // 处理所有可能的配置类型
    const uniqueTypes = Array.from(new Set(allConfigTypes));

    // 获取并处理所有配置项
    Promise.all(
      uniqueTypes.map(async configType => {
        try {
          if (!configType) return;

          const schema = getSchemaTpl(configType);
          if (!schema) return;

          // 存储所有获取到的配置项
          allSchemas[configType] = cleanObjectForDisplay(schema);

          // 按类型分类
          const category = CONFIG_CATEGORY_MAP[configType] || '其他配置';
          if (allCategorized['按类型分类'][category]) {
            allCategorized['按类型分类'][category].push({
              type: configType,
              schema: allSchemas[configType]
            });
          } else {
            allCategorized['按类型分类'][category] = [
              {
                type: configType,
                schema: allSchemas[configType]
              }
            ];
          }

          // 按文件分类（如果可以确定文件）
          for (const [fileCategory, types] of Object.entries(fileCategories)) {
            if (types.includes(configType)) {
              if (allCategorized['按文件分类'][fileCategory]) {
                allCategorized['按文件分类'][fileCategory].push({
                  type: configType,
                  schema: allSchemas[configType]
                });
              }
              break;
            }
          }
        } catch (error) {
          console.error(`获取配置项 ${configType} 失败:`, error);
        }
      })
    ).finally(() => {
      // 过滤掉空类别
      Object.keys(allCategorized).forEach(mainCategory => {
        Object.keys(allCategorized[mainCategory]).forEach(subCategory => {
          if (allCategorized[mainCategory][subCategory].length === 0) {
            delete allCategorized[mainCategory][subCategory];
          }
        });

        // 如果主类别为空，删除它
        if (Object.keys(allCategorized[mainCategory]).length === 0) {
          delete allCategorized[mainCategory];
        }
      });

      // 更新状态
      configsRef.current = allSchemas;
      setCategorizedConfigs(allCategorized);
      setLoading(false);

      // 计算总配置项数量
      const totalCount = Object.keys(allSchemas).length;
      setTotalConfigCount(totalCount);

      // 选择第一个可用的类别
      if (Object.keys(allCategorized).length > 0) {
        const firstCategory = Object.keys(allCategorized)[0];
        setActiveCategory(firstCategory);
        
        // 初始化所有子分类为展开状态
        const initialSectionState: Record<string, boolean> = {};
        Object.keys(allCategorized).forEach(category => {
          if (allCategorized[category]) {
            Object.keys(allCategorized[category]).forEach(section => {
              initialSectionState[section] = true;
            });
          }
        });
        setExpandedSections(initialSectionState);
      }
    });
  }, []);

  // 切换子分类的展开状态
  const toggleSectionExpand = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 全部展开子分类
  const expandAllSections = () => {
    if (!activeCategory || !categorizedConfigs[activeCategory]) return;
    
    const newSectionState: Record<string, boolean> = {};
    Object.keys(categorizedConfigs[activeCategory]).forEach(section => {
      newSectionState[section] = true;
    });
    
    setExpandedSections(prev => ({
      ...prev,
      ...newSectionState
    }));
  };

  // 全部收起子分类
  const collapseAllSections = () => {
    if (!activeCategory || !categorizedConfigs[activeCategory]) return;
    
    const newSectionState: Record<string, boolean> = {};
    Object.keys(categorizedConfigs[activeCategory]).forEach(section => {
      newSectionState[section] = false;
    });
    
    setExpandedSections(prev => ({
      ...prev,
      ...newSectionState
    }));
  };

  // 全部展开配置项
  const expandAllItems = () => {
    const newExpandedItems: Record<string, boolean> = {};
    // 获取当前显示的所有配置项
    const currentItems = getCurrentVisibleItems();
    
    // 将所有项设置为展开状态
    currentItems.forEach(item => {
      const itemId = `config-${item.type}`;
      newExpandedItems[itemId] = true;
    });
    
    setExpandedItems(newExpandedItems);
  };

  // 全部收起配置项
  const collapseAllItems = () => {
    const newExpandedItems: Record<string, boolean> = {};
    // 获取当前显示的所有配置项
    const currentItems = getCurrentVisibleItems();
    
    // 将所有项设置为收起状态
    currentItems.forEach(item => {
      const itemId = `config-${item.type}`;
      newExpandedItems[itemId] = false;
    });
    
    setExpandedItems(newExpandedItems);
  };

  // 获取当前可见的配置项
  const getCurrentVisibleItems = (): {type: string; schema: any}[] => {
    // 如果是搜索模式
    if (searchText) {
      // 检查对象是否包含搜索文本的递归函数
      const containsSearchText = (obj: any, text: string): boolean => {
        if (!obj) return false;
        
        // 先尝试将整个对象转换为JSON字符串进行搜索
        try {
          const jsonString = JSON.stringify(obj, null, 2);
          if (jsonString.toLowerCase().includes(text.toLowerCase())) {
            return true;
          }
        } catch (e) {
          // 忽略JSON转换错误
        }
        
        // 如果是字符串，直接检查
        if (typeof obj === 'string') {
          return obj.toLowerCase().includes(text.toLowerCase());
        }
        
        // 如果是数字，转为字符串再检查
        if (typeof obj === 'number') {
          return obj.toString().includes(text);
        }
        
        // 如果是布尔值，转为字符串再检查
        if (typeof obj === 'boolean') {
          return obj.toString().toLowerCase().includes(text.toLowerCase());
        }
        
        // 如果是数组，递归检查每个元素
        if (Array.isArray(obj)) {
          return obj.some(item => containsSearchText(item, text));
        }
        
        // 如果是对象，递归检查每个属性名和属性值
        if (typeof obj === 'object') {
          return Object.entries(obj).some(([key, value]) => {
            return (
              key.toLowerCase().includes(text.toLowerCase()) || 
              containsSearchText(value, text)
            );
          });
        }
        
        return false;
      };
      
      return Object.entries(configsRef.current)
        .filter(([configType, schema]) => {
          // 搜索配置项名称 - 首先检查，如果是精确的类型名称则直接返回匹配
          if (configType === searchText) {
            return true;
          }
          
          const nameMatch = configType.toLowerCase().includes(searchText.toLowerCase());
          
          // 搜索JSON内容
          const contentMatch = containsSearchText(schema, searchText);
          
          return nameMatch || contentMatch;
        })
        .map(([configType, schema]) => ({
          type: configType,
          schema
        }));
    }

    // 如果是分类浏览模式
    if (activeCategory && categorizedConfigs[activeCategory]) {
      const items: {type: string; schema: any}[] = [];
      Object.values(categorizedConfigs[activeCategory]).forEach(categoryItems => {
        items.push(...categoryItems);
      });
      return items;
    }

    return [];
  };

  // 导出所有配置项到JSON文件
  const exportAllConfigs = () => {
    try {
      // 格式化JSON
      const configsJSON = JSON.stringify(configsRef.current, null, 2);

      // 创建Blob对象
      const blob = new Blob([configsJSON], {type: 'application/json'});

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'amis-config-items.json';

      // 添加到DOM并触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 提示用户
      alert(`成功导出 ${totalConfigCount} 个配置项到JSON文件`);
    } catch (error) {
      console.error('导出配置项失败:', error);
      alert('导出失败，请查看控制台了解详情');
    }
  };

  // 渲染导航
  const renderNavigation = () => {
    return (
      <div className="config-viewer-nav">
        <div className="nav-header">
          <div className="total-count">共 {totalConfigCount} 个配置项</div>
          <button
            className="export-button"
            onClick={exportAllConfigs}
            disabled={loading || totalConfigCount === 0}
          >
            导出全部
          </button>
        </div>
        <div className="nav-search">
          <input
            type="text"
            className="search-input"
            placeholder="搜索配置项..."
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />
        </div>
        <div className="nav-categories">
          {Object.entries(categorizedConfigs).map(
            ([category, subCategories]) => {
              // 计算该主类别下的所有配置项总数
              const itemCount = Object.values(subCategories).reduce(
                (total, items) => total + items.length,
                0
              );

              return (
                <div
                  key={category}
                  className={`nav-category ${
                    activeCategory === category ? 'active' : ''
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  <span>{category}</span>
                  <span className="category-count">{itemCount}</span>
                </div>
              );
            }
          )}
        </div>
      </div>
    );
  };

  // 渲染配置项详情
  const renderConfigDetail = (item: any) => {
    const toggleExpand = (id: string) => {
      setExpandedItems(prev => ({
        ...prev,
        [id]: !prev[id]
      }));
    };

    const configType = item.type;
    const itemId = `config-${configType}`;
    const isExpanded = expandedItems[itemId] || false;

    // 格式化JSON内容，便于显示和复制
    const formattedJSON = isExpanded
      ? JSON.stringify(item.schema, null, 2)
      : '';

    // 复制内容到剪贴板
    const copyToClipboard = (e: React.MouseEvent) => {
      e.stopPropagation();
      try {
        navigator.clipboard.writeText(formattedJSON).then(err => {
          console.error('复制失败:', err);
          // 回退方案：使用选择文本复制
          const textArea = document.createElement('textarea');
          textArea.value = formattedJSON;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
        });
      } catch (err) {
        console.error('复制失败:', err);
      }
    };

    // 创建可预览的渲染上下文
    const renderPreview = () => {
      if (!props.render || !isExpanded) return null;

      try {
        // 获取配置项中的类型信息，为可预览做准备
        const schemaType = (item.schema && item.schema.type) || '';
        const label = (item.schema && item.schema.label) || configType;

        // 构建完整的预览方案
        let previewSchema = {
          type: 'container',
          body: [
            item.schema
          ],
          style: {
            width: '100%',
            margin: '0'
          }
        };

        // 尝试提取schema信息
        if (typeof item.schema === 'object' && schemaType) {
          previewSchema = {
            ...item.schema,
            style: {
              ...(item.schema.style || {}),
              width: '100%',
              margin: '0'
            }
          };
        }else if (typeof item.schema === 'object' && !schemaType) {
          previewSchema = {
            type: 'container',
            ...item.schema,
            style: {
              ...(item.schema.style || {}),
              width: '100%',
              margin: '0'
            }
          }
        }

        return (
          <div className="config-preview">
            <div className="preview-content">
              <AMISRenderer schema={previewSchema}></AMISRenderer>
            </div>
          </div>
        );
      } catch (error) {
        console.error('渲染预览失败:', error);
        return (
          <div className="config-preview">
            <div className="preview-error">
              <div>无法预览此配置项</div>
              <small>可能是不完整的配置或不支持直接渲染</small>
            </div>
          </div>
        );
      }
    };

    return (
      <div key={configType} className="config-item">
        <div
          className="config-item-header"
          onClick={() => toggleExpand(itemId)}
        >
          <div className="config-item-title">{configType}</div>
          <div className="config-item-actions">
            {isExpanded && (
              <button
                className="copy-button"
                onClick={copyToClipboard}
                title="复制到剪贴板"
              >
                复制
              </button>
            )}
            <div className="config-item-toggle">
              {isExpanded ? '收起' : '展开'}
            </div>
          </div>
        </div>
        {isExpanded && (
          <div className="config-item-content">
            <div className="content-split">
              <div className="json-panel">
                <pre className="config-json" tabIndex={0}>
                  <code>{formattedJSON}</code>
                </pre>
              </div>
              <div className="preview-panel">{renderPreview()}</div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 渲染主内容区域
  const renderContent = () => {
    if (loading) {
      return <div className="loading-indicator">加载配置项中...</div>;
    }

    // 计算当前显示的配置项
    const currentItems = getCurrentVisibleItems();
    currentItemsRef.current = currentItems;

    if (searchText) {
      // 搜索模式
      if (currentItems.length === 0) {
        return <div className="empty-message">没有找到匹配的配置项</div>;
      }

      return (
        <div className="config-viewer-content">
          <div className="content-header">
            <h3 className="content-title">
              搜索结果: {currentItems.length}个匹配项
            </h3>
            <div className="content-actions">
              <button
                className="action-button"
                onClick={expandAllItems}
                disabled={currentItems.length === 0}
              >
                全部展开
              </button>
              <button
                className="action-button"
                onClick={collapseAllItems}
                disabled={currentItems.length === 0}
              >
                全部收起
              </button>
            </div>
          </div>
          <div className="config-items">
            {currentItems.map(item => renderConfigDetail(item))}
          </div>
        </div>
      );
    }

    // 分类浏览模式
    if (!activeCategory || !categorizedConfigs[activeCategory]) {
      return <div className="empty-message">请选择一个分类</div>;
    }

    return (
      <div className="config-viewer-content">
        <div className="content-header">
          <h3 className="content-title">{activeCategory}</h3>
          <div className="content-actions">
            <div className="section-actions">
              <button
                className="action-button"
                onClick={expandAllSections}
              >
                展开分类
              </button>
              <button
                className="action-button"
                onClick={collapseAllSections}
              >
                收起分类
              </button>
            </div>
            <div className="item-actions">
              <button
                className="action-button"
                onClick={expandAllItems}
                disabled={currentItems.length === 0}
              >
                展开配置
              </button>
              <button
                className="action-button"
                onClick={collapseAllItems}
                disabled={currentItems.length === 0}
              >
                收起配置
              </button>
            </div>
          </div>
        </div>
        {Object.entries(categorizedConfigs[activeCategory]).map(
          ([subCategory, items]) => {
            const isSectionExpanded = expandedSections[subCategory] !== false;
            
            return (
              <div key={subCategory} className="config-section" id={`section-${subCategory}`}>
                <div className="section-header" onClick={() => toggleSectionExpand(subCategory)}>
                  <h4 className="section-title">
                    {subCategory} ({items.length})
                  </h4>
                  <div className="section-toggle">
                    {isSectionExpanded ? '收起' : '展开'}
                  </div>
                </div>
                {isSectionExpanded && (
                  <div className="config-items">
                    {items.map(item => renderConfigDetail(item))}
                  </div>
                )}
              </div>
            );
          }
        )}
      </div>
    );
  };

  return (
    <div className="component-config-viewer">
      {/* <span>{label}</span> */}
      <div className="component-config-viewer">
        {renderNavigation()}
        {renderContent()}
      </div>
      <style>
        {`
          .component-config-viewer {
            display: flex;
            height: 100%;
            min-height: 600px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          }
          
          .config-viewer-nav {
            width: 250px;
            background-color: #f8f9fa;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            padding: 1rem 0;
            display: flex;
            flex-direction: column;
          }
          
          .nav-header {
            padding: 0 1rem 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 1rem;
          }
          
          .total-count {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
          }
          
          .export-button {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          
          .export-button:hover {
            background-color: #218838;
          }
          
          .export-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
          }
          
          .nav-search {
            padding: 0 1rem 1rem;
          }
          
          .search-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
          }
          
          .nav-categories {
            display: flex;
            flex-direction: column;
          }
          
          .nav-category {
            padding: 0.75rem 1rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          
          .nav-category:hover {
            background-color: #e9ecef;
          }
          
          .nav-category.active {
            background-color: #0d6efd;
            color: white;
          }
          
          .category-count {
            background-color: #dee2e6;
            color: #212529;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 0.75rem;
          }
          
          .nav-category.active .category-count {
            background-color: #0a58ca;
            color: white;
          }
          
          .config-viewer-content {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
          }
          
          .content-title {
            margin-top: 0;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            font-weight: 500;
          }
          
          .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
          }
          
          .content-actions {
            display: flex;
            gap: 10px;
          }
          
          .section-actions, .item-actions {
            display: flex;
            gap: 5px;
          }
          
          .action-button {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          
          .action-button:hover {
            background-color: #5a6268;
          }
          
          .action-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
          
          .config-section {
            margin-bottom: 2rem;
          }
          
          .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 0.5rem 0;
          }
          
          .section-header:hover {
            background-color: #f8f9fa;
          }
          
          .section-toggle {
            font-size: 0.875rem;
            color: #6c757d;
            background-color: #e9ecef;
            padding: 3px 8px;
            border-radius: 4px;
          }
          
          .section-title {
            margin-top: 0;
            margin-bottom: 0;
            font-size: 1.25rem;
            font-weight: 500;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
            flex: 1;
          }
          
          .config-items {
            display: flex;
            flex-direction: column;
            gap: 1rem;
          }
          
          .config-item {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
          }
          
          .config-item-header {
            display: flex;
            justify-content: space-between;
            padding: 0.75rem 1rem;
            background-color: #f8f9fa;
            cursor: pointer;
            font-weight: 500;
          }
          
          .config-item-header:hover {
            background-color: #e9ecef;
          }
          
          .config-item-actions {
            display: flex;
            gap: 10px;
            align-items: center;
          }
          
          .copy-button {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 3px 8px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          
          .copy-button:hover {
            background-color: #5a6268;
          }
          
          .config-item-content {
            padding: 1rem;
            background-color: #fff;
            border-top: 1px solid #dee2e6;
            max-height: 500px;
            overflow: auto;
          }
          
          .content-split {
            display: flex;
            flex-direction: row;
            width: 100%;
            height: 100%;
            gap: 1rem;
          }
          
          .json-panel {
            flex: 1.5;
            overflow: auto;
            border-right: 1px solid #dee2e6;
            padding-right: 1rem;
            min-width: 0;
          }
          
          .preview-panel {
            flex: 1;
            overflow: auto;
            min-height: 200px;
            min-width: 300px;
            display: flex;
            flex-direction: column;
          }
          
          .config-preview {
            padding: 0.5rem;
            display: flex;
            flex-direction: column;
            height: 100%;
          }
          
          .preview-content {
            flex: 1;
            overflow: auto;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            background-color: #f8f9fa;
          }
          
          /* 确保AMIS渲染的内容左对齐 */
          .preview-content .cxd-Page {
            margin: 0 !important;
            padding: 0 !important;
            min-width: auto !important;
          }
          
          .preview-content .cxd-Page-content {
            margin: 0 !important;
            padding: 0 !important;
          }
          
          .preview-content .cxd-Page-main {
            margin: 0 !important;
            padding: 0 !important;
          }
          
          .theme-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
          }
          
          .theme-sample {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            padding: 1rem;
            border: 1px dashed #ced4da;
            border-radius: 4px;
            background-color: white;
          }
          
          .preview-icon {
            font-size: 1.5rem;
            color: #28a745;
          }
          
          .preview-text {
            font-size: 1rem;
          }
          
          .preview-error {
            padding: 1rem;
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            text-align: center;
          }
          
          .preview-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            font-size: 0.875rem;
            color: #6c757d;
          }
          
          .preview-example {
            padding: 1rem;
            border: 1px dashed #ced4da;
            border-radius: 4px;
            background-color: white;
            margin-top: 0.5rem;
          }
          
          .config-json {
            margin: 0;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            overflow-x: auto;
            width: 100%;
            position: relative;
            user-select: text; /* 允许部分选择内容 */
          }
          
          .config-json code {
            display: block;
            width: 100%;
            cursor: text; /* 显示文本选择光标 */
          }
          
          .loading-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 1.25rem;
            color: #6c757d;
          }
          
          .empty-message {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 1.25rem;
            color: #6c757d;
          }
          
          .config-viewer-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 1.5rem;
          }
          
          .config-viewer-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
          }
          
          @media (max-width: 1200px) {
            .content-split {
              flex-direction: column;
            }
            
            .json-panel {
              border-right: none;
              border-bottom: 1px solid #dee2e6;
              padding-right: 0;
              padding-bottom: 1rem;
              margin-bottom: 1rem;
            }
          }
        `}
      </style>
    </div>
  );
};

// 注册渲染器
Renderer({
  type: 'component-viewer'
})(ComponentViewer);

export default ComponentViewer;
