// src/component/ProcessRender/index.scss
.process-render {
  padding: 10px 24px;
  
  .process-timeline {
    padding: 0;
    margin: 0;
    
    .timeline-item {
      position: relative;
      padding-left: 52px;
      padding-bottom: 40px;
      list-style: none;
      
      &:before {
        content: '';
        position: absolute;
        top: 40px;
        bottom: 0;
        left: 20px;
        width: 1px;
        background-color: var(--text--muted-color);
        z-index: 1;
        height: auto;
      }
      
      &:last-child {
        padding-bottom: 0;
        
        &:before {
          display: none;
        }
      }
      
      &.completed .node-icon {
        background-color: var(--primary);
      }
      
      &.active .node-icon {
        background-color: var(--primary);
      }
      
      .timeline-item-icon {
        position: absolute;
        left: 0;
        top: 0;
        
        .node-icon {
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: var(--primary);
          border-radius: 4px;
          color: #fff;
          font-size: 18px;
          z-index: 2;
          // box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
      }
      
      .timeline-item-content {
        padding-top: 0;
        min-height: 40px; /* 改为min-height，允许内容扩展 */
        display: flex;
        flex-direction: column;
        justify-content: flex-start; /* 改为flex-start，让内容从顶部开始排列 */
        
        .branch-title {
          font-weight: 500;
          margin-bottom: 4px; /* 增加标题下方间距 */
          font-size: 14px;
          line-height: 19px;
          color: var(--text-color);
        }
        
        .branch-desc {
          font-size: 12px;
          line-height: 17px;
          color: var(--text--muted-color);
          margin-bottom: 12px; /* 增加描述下方间距 */
        }
        
        .branch-content {
          margin-top: 12px;
          
          .branch-item {
            margin-bottom: 16px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .branch-item-title {
              font-size: 13px;
              color: var(--text--muted-color);
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
}

// 隐藏滚动条样式
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

.loading-container {
  position: relative;
  min-height: 100px;
  
  .loading-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }
}

/* 分支节点样式优化 */
.branch-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 10px 0 15px 0; /* 增加上下间距 */
}

.branch-option-btn {
  position: relative;
  min-width: 80px;
  margin-bottom: 5px; /* 按钮下方增加间距 */
  padding: 4px 12px; /* 调整按钮内边距 */
}

.branch-option-btn.active {
  background-color: var(--primary);
  color: white;
}

/* 修改跳过标签样式，使用中划线而不是标签 */
.branch-option-btn.skip {
  opacity: 0.6;
  text-decoration: line-through; /* 添加中划线 */
}

/* 移除原来的跳过标签 */
.skip-label {
  display: none; /* 隐藏原来的跳过标签 */
}

/* 修改分支内容的样式，使其与主时间轴对齐 */
.branch-content {
  margin-left: 0; /* 移除左侧缩进 */
  margin-top: 15px;
  margin-bottom: 15px;
  border-left: none; /* 移除左侧边框 */
  padding-left: 0; /* 移除左侧内边距 */
  padding-top: 5px;
  padding-bottom: 5px;
}

/* 确保分支内容中的时间线连续 */
.branch-content .timeline-item:before {
  left: 20px; /* 与主时间轴保持一致 */
}

/* 确保分支内容中的图标位置正确 */
.branch-content .timeline-item .timeline-item-icon {
  left: 0;
}

/* 确保分支内容中的内容位置正确 */
.branch-content .timeline-item {
  padding-left: 52px; /* 与主时间轴保持一致的左侧间距 */
}

/* 分支节点内部的timeline-item样式调整 */
.branch-content .timeline-item {
  padding-bottom: 30px; /* 减小嵌套节点的底部间距 */
}

/* 确保分支内容有足够的空间展示 */
.timeline-item:has(.branch-options, .branch-content) {
  padding-bottom: 60px; /* 为有分支的节点增加更多底部空间 */
}

.timeline-item-end {
  min-height: 60px; // 可根据实际流程节点高度调整
  display: flex;
  align-items: center;
  .timeline-item-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
  .end-node-content {
    width: 100%;
    text-align: left; // 或 center，根据你的UI风格
    font-size: 14px;
    font-weight: 500;
  }
}