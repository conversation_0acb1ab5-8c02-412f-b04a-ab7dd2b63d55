.fontawesome-showcase {
  height: 100%;
  overflow: hidden;
  position: relative;
  padding: 20px;
  padding-top: 40px;
  &-inner {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
  }

  .virtual-list-container {
    flex: 1;
    position: relative;
    margin: 0 -20px;  // 抵消父元素padding
  }

  .virtual-list {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
      
      &:hover {
        background: #555;
      }
    }
  }

  .icon-group {
    padding: 0 20px;
  
    .group-title {
      font-size: 18px;
      padding: 10px;
      margin-bottom: 20px;
      background: #f5f5f5;
      border-left: 4px solid #1890ff;
      display: flex;
      align-items: center;
      cursor: pointer;
      user-select: none;

      i {
        margin-right: 8px;
        font-size: 14px;
        transition: transform 0.3s ease;
      }

      &.collapsed i {
        transform: rotate(0deg);
      }

      .title-text {
        flex: 1;
      }
    }
  
    .icon-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 16px;
      transition: all 0.3s ease;
      opacity: 1;
      overflow: hidden;

      &.hidden {
        max-height: 0;
        opacity: 0;
        margin: 0;
        padding: 0;
      }

      .icon-item {
        width: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px;
        border: 1px solid #eee;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
  
        &:hover {
          background: #f5f5f5;
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
          i {
            color: #1890ff;
            transform: scale(1.2);
          }
        }
  
        .icon-display {
          i {
            font-size: 24px;
            transition: all 0.2s ease;
            color: inherit;
          }
        }
  
        .icon-name {
          margin-top: 8px;
          font-size: 12px;
          color: #666;
          text-align: center;
          word-break: break-all;
        }
      }
    }
  }

  &.loading,
  &.error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    
    p {
      margin: 16px 0;
      color: #666;
    }
  }

  &.error {
    h2 {
      color: #ff4d4f;
    }

    button {
      padding: 8px 16px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: #fff;
      cursor: pointer;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }
}

.icon-count {
  font-size: 14px;
  color: #999;
  margin-left: 8px;
  font-weight: normal;
}

.search-box {
  margin: 20px 0;
  display: flex;
  justify-content: center;
  
  .search-input {
    width: 300px;
    padding: 8px 12px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    outline: none;
    
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.color-picker {
  margin: 0 20px;
  display: flex;
  align-items: center;
  
  .color-label {
    margin-right: 10px;
    color: #666;
  }
  
  .color-input {
    width: 100px;
    height: 32px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      border-color: #1890ff;
    }
  }
}

.search-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}

// 返回顶部按钮样式
.back-to-top {
  position: fixed;
  right: 40px;
  bottom: 40px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0,0,0,0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  opacity: 0;
  visibility: hidden;
  z-index: 999;

  &.visible {
    opacity: 1;
    visibility: visible;
  }

  &:hover {
    background: rgba(0,0,0,0.5);
    transform: translateY(-3px);
  }

  i {
    font-size: 20px;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
  gap: 10px;

  button {
    background: #fff;
    border: 1px solid #ddd;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover:not(:disabled) {
      background: #f0f0f0;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  span {
    font-size: 14px;
    color: #666;
  }
}