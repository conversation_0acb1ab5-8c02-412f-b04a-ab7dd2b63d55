{"id": {"desc": "<p>组件 ID。默认不指定。指定则可用于在 option 或者 API 中引用组件。</p>\n"}, "show": {"desc": "\n\n<p>是否显示 x 轴。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "gridIndex": {"desc": "<p>x 轴所在的 grid 的索引，默认位于第一个 grid。</p>\n"}, "position": {"desc": "\n\n<p>x 轴的位置。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;top&#39;</code></li>\n<li><code class=\"codespan\">&#39;bottom&#39;</code></li>\n</ul>\n<p>默认 grid 中的第一个 x 轴在 grid 的下方（<code class=\"codespan\">&#39;bottom&#39;</code>），第二个 x 轴视第一个 x 轴的位置放在另一侧。</p>\n", "uiControl": {"type": "enum", "options": "top,bottom", "default": "bottom"}}, "offset": {"desc": "\n\n<p>X 轴相对于默认位置的偏移，在相同的 <code class=\"codespan\">position</code> 上有多个 X 轴的时候有用。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "realtimeSort": {"desc": "<p>用于制作动态排序柱状图。设为 <code class=\"codespan\">true</code> 时，表示 X 轴开启实时排序效果，仅当 X 轴的 <a href=\"#xAxis.type\">type</a> 是 <code class=\"codespan\">&#39;value&#39;</code> 时有效。</p>\n<p>需要配合其他配置项共同实现动态排序柱状图效果，具体参见<a href=\"tutorial.html#%E5%8A%A8%E6%80%81%E6%8E%92%E5%BA%8F%E6%9F%B1%E7%8A%B6%E5%9B%BE\" target=\"_blank\">动态排序柱状图</a>教程。</p>\n"}, "sortSeriesIndex": {"desc": "<p>动态排序柱状图用于排序的系列 id。目前只支持一个系列的柱状图排序效果，所以这个值只能取 0。仅当 X 轴 <a href=\"#xAxis.realtimeSort\">realtimeSort</a> 为 <code class=\"codespan\">true</code> 并且 <a href=\"#xAxis.type\">type</a> 是 <code class=\"codespan\">&#39;value&#39;</code> 时有效。</p>\n<p>需要配合其他配置项共同实现动态排序柱状图效果，具体参见<a href=\"tutorial.html#%E5%8A%A8%E6%80%81%E6%8E%92%E5%BA%8F%E6%9F%B1%E7%8A%B6%E5%9B%BE\" target=\"_blank\">动态排序柱状图</a>教程。</p>\n"}, "type": {"desc": "<p>坐标轴类型。</p>\n<p>可选：</p>\n<ul>\n<li><p><code class=\"codespan\">&#39;value&#39;</code>\n  数值轴，适用于连续数据。</p>\n</li>\n<li><p><code class=\"codespan\">&#39;category&#39;</code>\n  类目轴，适用于离散的类目数据。为该类型时类目数据可自动从 <a href=\"#series.data\">series.data</a> 或 <a href=\"#dataset.source\">dataset.source</a> 中取，或者可通过 <a href=\"#xAxis.data\">xAxis.data</a> 设置类目数据。</p>\n</li>\n<li><p><code class=\"codespan\">&#39;time&#39;</code>\n  时间轴，适用于连续的时序数据，与数值轴相比时间轴带有时间的格式化，在刻度计算上也有所不同，例如会根据跨度的范围来决定使用月，星期，日还是小时范围的刻度。</p>\n</li>\n<li><p><code class=\"codespan\">&#39;log&#39;</code>\n  对数轴。适用于对数数据。</p>\n</li>\n</ul>\n"}, "name": {"desc": "\n\n<p>坐标轴名称。</p>\n", "uiControl": {"type": "text"}}, "nameLocation": {"desc": "\n\n<p>坐标轴名称显示位置。</p>\n<p><strong>可选：</strong></p>\n<ul>\n<li><code class=\"codespan\">&#39;start&#39;</code></li>\n<li><code class=\"codespan\">&#39;middle&#39;</code> 或者 <code class=\"codespan\">&#39;center&#39;</code></li>\n<li><code class=\"codespan\">&#39;end&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "options": "start,middle,end", "default": "end"}}, "nameTextStyle": {"desc": "<p>坐标轴名称的文字样式。</p>\n"}, "nameTextStyle.color": {"desc": "\n\n<p>坐标轴名称的颜色，默认取 <a href=\"#xAxis.axisLine.lineStyle.color\">axisLine.lineStyle.color</a>。</p>\n", "uiControl": {"type": "color"}}, "nameTextStyle.fontStyle": {"desc": "\n\n<p>坐标轴名称文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "nameTextStyle.fontWeight": {"desc": "\n\n<p>坐标轴名称文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "nameTextStyle.fontFamily": {"desc": "\n\n<p>坐标轴名称文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "nameTextStyle.fontSize": {"desc": "\n\n<p>坐标轴名称文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "nameTextStyle.align": {"desc": "\n\n<p>文字水平对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;left&#39;</code></li>\n<li><code class=\"codespan\">&#39;center&#39;</code></li>\n<li><code class=\"codespan\">&#39;right&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">align</code>，则会取父层级的 <code class=\"codespan\">align</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    align: right,\n    rich: {\n        a: {\n            // 没有设置 `align`，则 `align` 为 right\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "left,center,right"}}, "nameTextStyle.verticalAlign": {"desc": "\n\n<p>文字垂直对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;top&#39;</code></li>\n<li><code class=\"codespan\">&#39;middle&#39;</code></li>\n<li><code class=\"codespan\">&#39;bottom&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">verticalAlign</code>，则会取父层级的 <code class=\"codespan\">verticalAlign</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    verticalAlign: bottom,\n    rich: {\n        a: {\n            // 没有设置 `verticalAlign`，则 `verticalAlign` 为 bottom\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "top,middle,bottom"}}, "nameTextStyle.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "nameTextStyle.backgroundColor": {"desc": "\n\n<p>文字块背景色。</p>\n<p>可以使用颜色值，例如：<code class=\"codespan\">&#39;#123234&#39;</code>, <code class=\"codespan\">&#39;red&#39;</code>, <code class=\"codespan\">&#39;rgba(0,23,11,0.3)&#39;</code>。</p>\n<p>也可以直接使用图片，例如：</p>\n<pre><code class=\"lang-js\">backgroundColor: {\n    image: &#39;xxx/xxx.png&#39;\n    // 这里可以是图片的 URL，\n    // 或者图片的 dataURI，\n    // 或者 HTMLImageElement 对象，\n    // 或者 HTMLCanvasElement 对象。\n}\n</code></pre>\n<p>当使用图片的时候，可以使用 <code class=\"codespan\">width</code> 或 <code class=\"codespan\">height</code> 指定高宽，也可以不指定自适应。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "nameTextStyle.borderColor": {"desc": "\n\n<p>文字块边框颜色。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "nameTextStyle.borderWidth": {"desc": "\n\n<p>文字块边框宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.borderRadius": {"desc": "\n\n<p>文字块的圆角。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "LT,RT, RB, LB"}}, "nameTextStyle.padding": {"desc": "\n\n<p>文字块的内边距。例如：</p>\n<ul>\n<li><code class=\"codespan\">padding: [3, 4, 5, 6]</code>：表示 <code class=\"codespan\">[上, 右, 下, 左]</code> 的边距。</li>\n<li><code class=\"codespan\">padding: 4</code>：表示 <code class=\"codespan\">padding: [4, 4, 4, 4]</code>。</li>\n<li><code class=\"codespan\">padding: [3, 4]</code>：表示 <code class=\"codespan\">padding: [3, 4, 3, 4]</code>。</li>\n</ul>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "T,R,B,L"}}, "nameTextStyle.shadowColor": {"desc": "\n\n<p>文字块的背景阴影颜色。</p>\n", "uiControl": {"type": "color"}}, "nameTextStyle.shadowBlur": {"desc": "\n\n<p>文字块的背景阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.shadowOffsetX": {"desc": "\n\n<p>文字块的背景阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameTextStyle.shadowOffsetY": {"desc": "\n\n<p>文字块的背景阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameTextStyle.width": {"desc": "\n\n<p>文本显示宽度。</p>\n", "uiControl": {"type": "number", "default": "100", "min": "1", "max": "500", "step": "1"}}, "nameTextStyle.height": {"desc": "\n\n<p>文本显示高度。</p>\n", "uiControl": {"type": "number", "default": "50", "min": "1", "max": "500", "step": "1"}}, "nameTextStyle.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "nameTextStyle.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "nameTextStyle.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameTextStyle.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameTextStyle.overflow": {"desc": "\n\n<p>文字超出宽度是否截断或者换行。配置<code class=\"codespan\">width</code>时有效</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 截断，并在末尾显示<code class=\"codespan\">ellipsis</code>配置的文本，默认为<code class=\"codespan\">...</code></li>\n<li><code class=\"codespan\">&#39;break&#39;</code> 换行</li>\n<li><code class=\"codespan\">&#39;breakAll&#39;</code> 换行，跟<code class=\"codespan\">&#39;break&#39;</code>不同的是，在英语等拉丁文中，<code class=\"codespan\">&#39;breakAll&#39;</code>还会强制单词内换行</li>\n</ul>\n", "uiControl": {"type": "enum", "options": "truncate,break,breakAll"}}, "nameTextStyle.ellipsis": {"desc": "<p>在<code class=\"codespan\">overflow</code>配置为<code class=\"codespan\">&#39;truncate&#39;</code>的时候，可以通过该属性配置末尾显示的文本。</p>\n"}, "nameTextStyle.lineOverflow": {"desc": "<p>文本超出高度部分是否截断，配置<code class=\"codespan\">height</code>时有效。</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 在文本行数超出高度部分截断。</li>\n</ul>\n"}, "nameTextStyle.rich": {"desc": "<p>在 <code class=\"codespan\">rich</code> 里面，可以自定义富文本样式。利用富文本样式，可以在标签中做出非常丰富的效果。</p>\n<p>例如：</p>\n<pre><code class=\"lang-js\">label: {\n    // 在文本中，可以对部分文本采用 rich 中定义样式。\n    // 这里需要在文本中使用标记符号：\n    // `{styleName|text content text content}` 标记样式名。\n    // 注意，换行仍是使用 &#39;\\n&#39;。\n    formatter: [\n        &#39;{a|这段文本采用样式a}&#39;,\n        &#39;{b|这段文本采用样式b}这段用默认样式{x|这段用样式x}&#39;\n    ].join(&#39;\\n&#39;),\n\n    rich: {\n        a: {\n            color: &#39;red&#39;,\n            lineHeight: 10\n        },\n        b: {\n            backgroundColor: {\n                image: &#39;xxx/xxx.jpg&#39;\n            },\n            height: 40\n        },\n        x: {\n            fontSize: 18,\n            fontFamily: &#39;Microsoft YaHei&#39;,\n            borderColor: &#39;#449933&#39;,\n            borderRadius: 4\n        },\n        ...\n    }\n}\n</code></pre>\n<p>详情参见教程：<a href=\"tutorial.html#%E5%AF%8C%E6%96%87%E6%9C%AC%E6%A0%87%E7%AD%BE\" target=\"_blank\">富文本标签</a></p>\n"}, "nameTextStyle.rich.<style_name>.color": {"desc": "\n\n<p>文字的颜色。</p>\n", "uiControl": {"type": "color", "default": "null"}}, "nameTextStyle.rich.<style_name>.fontStyle": {"desc": "\n\n<p>文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "nameTextStyle.rich.<style_name>.fontWeight": {"desc": "\n\n<p>文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "nameTextStyle.rich.<style_name>.fontFamily": {"desc": "\n\n<p>文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "nameTextStyle.rich.<style_name>.fontSize": {"desc": "\n\n<p>文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "nameTextStyle.rich.<style_name>.align": {"desc": "\n\n<p>文字水平对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;left&#39;</code></li>\n<li><code class=\"codespan\">&#39;center&#39;</code></li>\n<li><code class=\"codespan\">&#39;right&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">align</code>，则会取父层级的 <code class=\"codespan\">align</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    align: right,\n    rich: {\n        a: {\n            // 没有设置 `align`，则 `align` 为 right\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "left,center,right"}}, "nameTextStyle.rich.<style_name>.verticalAlign": {"desc": "\n\n<p>文字垂直对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;top&#39;</code></li>\n<li><code class=\"codespan\">&#39;middle&#39;</code></li>\n<li><code class=\"codespan\">&#39;bottom&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">verticalAlign</code>，则会取父层级的 <code class=\"codespan\">verticalAlign</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    verticalAlign: bottom,\n    rich: {\n        a: {\n            // 没有设置 `verticalAlign`，则 `verticalAlign` 为 bottom\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "top,middle,bottom"}}, "nameTextStyle.rich.<style_name>.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "nameTextStyle.rich.<style_name>.backgroundColor": {"desc": "\n\n<p>文字块背景色。</p>\n<p>可以使用颜色值，例如：<code class=\"codespan\">&#39;#123234&#39;</code>, <code class=\"codespan\">&#39;red&#39;</code>, <code class=\"codespan\">&#39;rgba(0,23,11,0.3)&#39;</code>。</p>\n<p>也可以直接使用图片，例如：</p>\n<pre><code class=\"lang-js\">backgroundColor: {\n    image: &#39;xxx/xxx.png&#39;\n    // 这里可以是图片的 URL，\n    // 或者图片的 dataURI，\n    // 或者 HTMLImageElement 对象，\n    // 或者 HTMLCanvasElement 对象。\n}\n</code></pre>\n<p>当使用图片的时候，可以使用 <code class=\"codespan\">width</code> 或 <code class=\"codespan\">height</code> 指定高宽，也可以不指定自适应。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "nameTextStyle.rich.<style_name>.borderColor": {"desc": "\n\n<p>文字块边框颜色。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "nameTextStyle.rich.<style_name>.borderWidth": {"desc": "\n\n<p>文字块边框宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.rich.<style_name>.borderRadius": {"desc": "\n\n<p>文字块的圆角。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "LT,RT, RB, LB"}}, "nameTextStyle.rich.<style_name>.padding": {"desc": "\n\n<p>文字块的内边距。例如：</p>\n<ul>\n<li><code class=\"codespan\">padding: [3, 4, 5, 6]</code>：表示 <code class=\"codespan\">[上, 右, 下, 左]</code> 的边距。</li>\n<li><code class=\"codespan\">padding: 4</code>：表示 <code class=\"codespan\">padding: [4, 4, 4, 4]</code>。</li>\n<li><code class=\"codespan\">padding: [3, 4]</code>：表示 <code class=\"codespan\">padding: [3, 4, 3, 4]</code>。</li>\n</ul>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "T,R,B,L"}}, "nameTextStyle.rich.<style_name>.shadowColor": {"desc": "\n\n<p>文字块的背景阴影颜色。</p>\n", "uiControl": {"type": "color"}}, "nameTextStyle.rich.<style_name>.shadowBlur": {"desc": "\n\n<p>文字块的背景阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.rich.<style_name>.shadowOffsetX": {"desc": "\n\n<p>文字块的背景阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameTextStyle.rich.<style_name>.shadowOffsetY": {"desc": "\n\n<p>文字块的背景阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameTextStyle.rich.<style_name>.width": {"desc": "<p>文字块的宽度。一般不用指定，不指定则自动是文字的宽度。在想做表格项或者使用图片（参见 <code class=\"codespan\">backgroundColor</code>）时，可能会使用它。</p>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n<p><code class=\"codespan\">width</code> 也可以是百分比字符串，如 <code class=\"codespan\">&#39;100%&#39;</code>。表示的是所在文本块的 <code class=\"codespan\">contentWidth</code>（即不包含文本块的 <code class=\"codespan\">padding</code>）的百分之多少。之所以以 <code class=\"codespan\">contentWidth</code> 做基数，因为每个文本片段只能基于 <code class=\"codespan\">content box</code> 布局。如果以 <code class=\"codespan\">outerWidth</code> 做基数，则百分比的计算在实用中不具有意义，可能会超出。</p>\n<p>注意，如果不定义 <code class=\"codespan\">rich</code> 属性，则不能指定 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code>。</p>\n"}, "nameTextStyle.rich.<style_name>.height": {"desc": "<p>文字块的高度。一般不用指定，不指定则自动是文字的高度。在使用图片（参见 <code class=\"codespan\">backgroundColor</code>）时，可能会使用它。</p>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n<p>注意，如果不定义 <code class=\"codespan\">rich</code> 属性，则不能指定 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code>。</p>\n"}, "nameTextStyle.rich.<style_name>.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "nameTextStyle.rich.<style_name>.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.rich.<style_name>.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "nameTextStyle.rich.<style_name>.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "nameTextStyle.rich.<style_name>.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameTextStyle.rich.<style_name>.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n\n", "uiControl": {"type": "number", "step": "0.5"}}, "nameGap": {"desc": "\n\n<p>坐标轴名称与轴线之间的距离。</p>\n", "uiControl": {"type": "number", "step": "0.5", "default": "15"}}, "nameRotate": {"desc": "\n\n<p>坐标轴名字旋转，角度值。</p>\n", "uiControl": {"type": "angle", "min": "-360", "max": "360", "step": "1"}}, "inverse": {"desc": "\n\n<p>是否是反向坐标轴。</p>\n", "uiControl": {"type": "boolean"}}, "boundaryGap": {"desc": "\n\n<p>坐标轴两边留白策略，类目轴和非类目轴的设置和表现不一样。</p>\n<p>类目轴中 <code class=\"codespan\">boundaryGap</code> 可以配置为 <code class=\"codespan\">true</code> 和 <code class=\"codespan\">false</code>。默认为 <code class=\"codespan\">true</code>，这时候<a href=\"#xAxis.axisTick\">刻度</a>只是作为分隔线，标签和数据点都会在两个<a href=\"#xAxis.axisTick\">刻度</a>之间的带(band)中间。</p>\n<p>非类目轴，包括时间，数值，对数轴，<code class=\"codespan\">boundaryGap</code>是一个两个值的数组，分别表示数据最小值和最大值的延伸范围，可以直接设置数值或者相对的百分比，在设置 <a href=\"#xAxis.min\">min</a> 和 <a href=\"#xAxis.max\">max</a> 后无效。\n<strong>示例：</strong></p>\n<pre><code class=\"lang-js\">boundaryGap: [&#39;20%&#39;, &#39;20%&#39;]\n</code></pre>\n", "uiControl": {"type": "boolean"}}, "min": {"desc": "\n\n<p>坐标轴刻度最小值。</p>\n<p>可以设置成特殊值 <code class=\"codespan\">&#39;dataMin&#39;</code>，此时取数据在该轴上的最小值作为最小刻度。</p>\n<p>不设置时会自动计算最小值保证坐标轴刻度的均匀分布。</p>\n<p>在类目轴中，也可以设置为类目的序数（如类目轴 <code class=\"codespan\">data: [&#39;类A&#39;, &#39;类B&#39;, &#39;类C&#39;]</code> 中，序数 <code class=\"codespan\">2</code> 表示 <code class=\"codespan\">&#39;类C&#39;</code>。也可以设置为负数，如 <code class=\"codespan\">-3</code>）。</p>\n<p>当设置成 <code class=\"codespan\">function</code> 形式时，可以根据计算得出的数据最大最小值设定坐标轴的最小值。如：</p>\n<pre><code class=\"lang-js\">min: function (value) {\n    return value.min - 20;\n}\n</code></pre>\n<p>其中 <code class=\"codespan\">value</code> 是一个包含 <code class=\"codespan\">min</code> 和 <code class=\"codespan\">max</code> 的对象，分别表示数据的最大最小值，这个函数可返回坐标轴的最小值，也可返回 <code class=\"codespan\">null</code>/<code class=\"codespan\">undefined</code> 来表示“自动计算最小值”（返回 <code class=\"codespan\">null</code>/<code class=\"codespan\">undefined</code> 从 <code class=\"codespan\">v4.8.0</code> 开始支持）。</p>\n", "uiControl": {"type": "number"}}, "max": {"desc": "\n\n<p>坐标轴刻度最大值。</p>\n<p>可以设置成特殊值 <code class=\"codespan\">&#39;dataMax&#39;</code>，此时取数据在该轴上的最大值作为最大刻度。</p>\n<p>不设置时会自动计算最大值保证坐标轴刻度的均匀分布。</p>\n<p>在类目轴中，也可以设置为类目的序数（如类目轴 <code class=\"codespan\">data: [&#39;类A&#39;, &#39;类B&#39;, &#39;类C&#39;]</code> 中，序数 <code class=\"codespan\">2</code> 表示 <code class=\"codespan\">&#39;类C&#39;</code>。也可以设置为负数，如 <code class=\"codespan\">-3</code>）。</p>\n<p>当设置成 <code class=\"codespan\">function</code> 形式时，可以根据计算得出的数据最大最小值设定坐标轴的最小值。如：</p>\n<pre><code class=\"lang-js\">max: function (value) {\n    return value.max - 20;\n}\n</code></pre>\n<p>其中 <code class=\"codespan\">value</code> 是一个包含 <code class=\"codespan\">min</code> 和 <code class=\"codespan\">max</code> 的对象，分别表示数据的最大最小值，这个函数可返回坐标轴的最大值，也可返回 <code class=\"codespan\">null</code>/<code class=\"codespan\">undefined</code> 来表示“自动计算最大值”（返回 <code class=\"codespan\">null</code>/<code class=\"codespan\">undefined</code> 从 <code class=\"codespan\">v4.8.0</code> 开始支持）。</p>\n", "uiControl": {"type": "number"}}, "scale": {"desc": "\n\n<p>只在数值轴中（<a href=\"#xAxis.type\">type</a>: &#39;value&#39;）有效。</p>\n<p>是否是脱离 0 值比例。设置成 <code class=\"codespan\">true</code> 后坐标刻度不会强制包含零刻度。在双数值轴的散点图中比较有用。</p>\n<p>在设置 <a href=\"#xAxis.min\">min</a> 和 <a href=\"#xAxis.max\">max</a> 之后该配置项无效。</p>\n", "uiControl": {"type": "boolean"}}, "splitNumber": {"desc": "\n\n<p>坐标轴的分割段数，需要注意的是这个分割段数只是个预估值，最后实际显示的段数会在这个基础上根据分割后坐标轴刻度显示的易读程度作调整。</p>\n<p>在类目轴中无效。</p>\n", "uiControl": {"type": "number", "min": "1", "step": "1", "default": "5"}}, "minInterval": {"desc": "\n\n<p>自动计算的坐标轴最小间隔大小。</p>\n<p>例如可以设置成<code class=\"codespan\">1</code>保证坐标轴分割刻度显示成整数。</p>\n<pre><code class=\"lang-js\">{\n    minInterval: 1\n}\n</code></pre>\n<p>只在数值轴或时间轴中（<a href=\"#xAxis.type\">type</a>: &#39;value&#39; 或 &#39;time&#39;）有效。</p>\n", "uiControl": {"type": "number"}}, "maxInterval": {"desc": "\n\n<p>自动计算的坐标轴最大间隔大小。</p>\n<p>例如，在时间轴（（<a href=\"#xAxis.type\">type</a>: &#39;time&#39;））可以设置成 <code class=\"codespan\">3600 * 24 * 1000</code> 保证坐标轴分割刻度最大为一天。</p>\n<pre><code class=\"lang-js\">{\n    maxInterval: 3600 * 24 * 1000\n}\n</code></pre>\n<p>只在数值轴或时间轴中（<a href=\"#xAxis.type\">type</a>: &#39;value&#39; 或 &#39;time&#39;）有效。</p>\n", "uiControl": {"type": "number"}}, "interval": {"desc": "\n\n<p>强制设置坐标轴分割间隔。</p>\n<p>因为 <a href=\"#xAxis.splitNumber\">splitNumber</a> 是预估的值，实际根据策略计算出来的刻度可能无法达到想要的效果，这时候可以使用 interval 配合 <a href=\"#xAxis.min\">min</a>、<a href=\"#xAxis.max\">max</a> 强制设定刻度划分，一般不建议使用。</p>\n<p>无法在类目轴中使用。在时间轴（<a href=\"#xAxis.type\">type</a>: &#39;time&#39;）中需要传时间戳，在对数轴（<a href=\"#xAxis.type\">type</a>: &#39;log&#39;）中需要传指数值。</p>\n", "uiControl": {"type": "number"}}, "logBase": {"desc": "\n\n<p>对数轴的底数，只在对数轴中（<a href=\"#xAxis.type\">type</a>: &#39;log&#39;）有效。</p>\n", "uiControl": {"type": "number", "default": "10"}}, "silent": {"desc": "<p>坐标轴是否是静态无法交互。</p>\n"}, "triggerEvent": {"desc": "<p>坐标轴的标签是否响应和触发鼠标事件，默认不响应。</p>\n<p>事件参数如下：</p>\n<pre><code class=\"lang-js\">{\n    // 组件类型，xAxis, yAxis, radiusAxis, angleAxis\n    // 对应组件类型都会有一个属性表示组件的 index，例如 xAxis 就是 xAxisIndex\n    componentType: string,\n    // 未格式化过的刻度值, 点击刻度标签有效\n    value: &#39;&#39;,\n    // 坐标轴名称, 点击坐标轴名称有效\n    name: &#39;&#39;\n}\n</code></pre>\n"}, "axisLine": {"desc": "<p>坐标轴轴线相关设置。</p>\n"}, "axisLine.show": {"desc": "\n\n<p>是否显示坐标轴轴线。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "axisLine.onZero": {"desc": "\n\n<p>X 轴或者 Y 轴的轴线是否在另一个轴的 0 刻度上，只有在另一个轴为数值轴且包含 0 刻度时有效。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "axisLine.onZeroAxisIndex": {"desc": "<p>当有双轴时，可以用这个属性手动指定，在哪个轴的 0 刻度上。</p>\n"}, "axisLine.symbol": {"desc": "\n\n<p>轴线两边的箭头。可以是字符串，表示两端使用同样的箭头；或者长度为 2 的字符串数组，分别表示两端的箭头。默认不显示箭头，即 <code class=\"codespan\">&#39;none&#39;</code>。两端都显示箭头可以设置为 <code class=\"codespan\">&#39;arrow&#39;</code>，只在末端显示箭头可以设置为 <code class=\"codespan\">[&#39;none&#39;, &#39;arrow&#39;]</code>。</p>\n", "uiControl": {"type": "icon", "default": "none"}}, "axisLine.symbolSize": {"desc": "\n\n<p>轴线两边的箭头的大小，第一个数字表示宽度（垂直坐标轴方向），第二个数字表示高度（平行坐标轴方向）。</p>\n", "uiControl": {"type": "vector", "default": "10,15"}}, "axisLine.symbolOffset": {"desc": "\n\n<p>轴线两边的箭头的偏移，如果是数组，第一个数字表示起始箭头的偏移，第二个数字表示末端箭头的偏移；如果是数字，表示这两个箭头使用同样的偏移。</p>\n", "uiControl": {"type": "vector", "default": "0,0"}}, "axisLine.lineStyle.color": {"desc": "\n\n<p>坐标轴线线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "axisLine.lineStyle.width": {"desc": "\n\n<p>坐标轴线线宽。</p>\n", "uiControl": {"type": "number", "value": "1", "min": "0", "step": "0.5"}}, "axisLine.lineStyle.type": {"desc": "\n\n<p>坐标轴线线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "axisLine.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "axisLine.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "axisLine.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisLine.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisLine.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "axisTick": {"desc": "<p>坐标轴刻度相关设置。</p>\n"}, "axisTick.show": {"desc": "\n\n<p>是否显示坐标轴刻度。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "axisTick.alignWithLabel": {"desc": "\n\n<p>类目轴中在 <code class=\"codespan\">boundaryGap</code> 为 <code class=\"codespan\">true</code> 的时候有效，可以保证刻度线和标签对齐。如下图：</p>\n<p><img width=\"600\" height=\"auto\" src=\"documents/asset/img/axis-align-with-label.png\"></p>\n", "uiControl": {"type": "boolean", "default": "false"}}, "axisTick.interval": {"desc": "\n\n\n\n<p>坐标轴刻度的显示间隔，在类目轴中有效。默认同 <a href=\"#xAxis.axisLabel.interval\">axisLabel.interval</a> 一样。</p>\n<p>默认会采用标签不重叠的策略间隔显示标签。</p>\n<p>可以设置成 0 强制显示所有标签。</p>\n<p>如果设置为 <code class=\"codespan\">1</code>，表示『隔一个标签显示一个标签』，如果值为 <code class=\"codespan\">2</code>，表示隔两个标签显示一个标签，以此类推。</p>\n<p>可以用数值表示间隔的数据，也可以通过回调函数控制。回调函数格式如下：</p>\n<pre><code class=\"lang-js\">(index:number, value: string) =&gt; boolean\n</code></pre>\n<p>第一个参数是类目的 index，第二个值是类目名称，如果跳过则返回 <code class=\"codespan\">false</code>。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "1"}}, "axisTick.inside": {"desc": "\n\n<p>坐标轴刻度是否朝内，默认朝外。</p>\n", "uiControl": {"type": "boolean"}}, "axisTick.length": {"desc": "\n\n<p>坐标轴刻度的长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5", "default": "5"}}, "axisTick.lineStyle": {"desc": "<p>刻度线的样式设置。</p>\n"}, "axisTick.lineStyle.color": {"desc": "<p>刻度线的颜色，默认取 <a href=\"#xAxis.axisTick.lineStyle.color\">axisTick.lineStyle.color</a>。</p>\n"}, "axisTick.lineStyle.width": {"desc": "\n\n<p>坐标轴刻度线宽。</p>\n", "uiControl": {"type": "number", "value": "1", "min": "0", "step": "0.5"}}, "axisTick.lineStyle.type": {"desc": "\n\n<p>坐标轴刻度线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "axisTick.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "axisTick.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "axisTick.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisTick.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisTick.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "minorTick": {"desc": "<blockquote>\n<p>从 <code class=\"codespan\">v4.6.0</code> 开始支持</p>\n</blockquote>\n<p>坐标轴次刻度线相关设置。</p>\n<p>注意：次刻度线无法在类目轴（<a href=\"#xAxis.type\">type</a>: <code class=\"codespan\">&#39;category&#39;</code>）中使用。</p>\n<p>示例：</p>\n<p>1) 函数绘图中使用次刻度线</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-function&edit=1&reset=1\" width=\"600\" height=\"350\"><iframe />\n\n\n<p>2) 在对数轴中使用次刻度线</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=line-log&edit=1&reset=1\" width=\"600\" height=\"350\"><iframe />\n\n"}, "minorTick.show": {"desc": "\n\n<p>是否显示次刻度线。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "minorTick.splitNumber": {"desc": "\n\n<p>次刻度线分割数，默认会分割成 5 段</p>\n", "uiControl": {"type": "number", "min": "1", "step": "1", "default": "5"}}, "minorTick.length": {"desc": "\n\n<p>次刻度线的长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5", "default": "3"}}, "minorTick.lineStyle.color": {"desc": "\n\n<p>刻度线的颜色，默认取 <a href=\"#xAxis.axisLine.lineStyle.color\">axisLine.lineStyle.color</a>。</p>\n", "uiControl": {"type": "color"}}, "minorTick.lineStyle.width": {"desc": "\n\n<p>次刻度线线宽。</p>\n", "uiControl": {"type": "number", "value": "1", "min": "0", "step": "0.5"}}, "minorTick.lineStyle.type": {"desc": "\n\n<p>次刻度线线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "minorTick.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "minorTick.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "minorTick.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "minorTick.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "minorTick.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "axisLabel": {"desc": "<p>坐标轴刻度标签的相关设置。</p>\n"}, "axisLabel.show": {"desc": "\n\n<p>是否显示刻度标签。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "axisLabel.interval": {"desc": "\n\n\n\n<p>坐标轴刻度标签的显示间隔，在类目轴中有效。</p>\n<p>默认会采用标签不重叠的策略间隔显示标签。</p>\n<p>可以设置成 0 强制显示所有标签。</p>\n<p>如果设置为 <code class=\"codespan\">1</code>，表示『隔一个标签显示一个标签』，如果值为 <code class=\"codespan\">2</code>，表示隔两个标签显示一个标签，以此类推。</p>\n<p>可以用数值表示间隔的数据，也可以通过回调函数控制。回调函数格式如下：</p>\n<pre><code class=\"lang-js\">(index:number, value: string) =&gt; boolean\n</code></pre>\n<p>第一个参数是类目的 index，第二个值是类目名称，如果跳过则返回 <code class=\"codespan\">false</code>。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "1"}}, "axisLabel.inside": {"desc": "\n\n<p>刻度标签是否朝内，默认朝外。</p>\n", "uiControl": {"type": "boolean"}}, "axisLabel.rotate": {"desc": "\n\n<p>刻度标签旋转的角度，在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。</p>\n<p>旋转的角度从 -90 度到 90 度。</p>\n", "uiControl": {"type": "angle", "min": "-90", "max": "90", "step": "1"}}, "axisLabel.margin": {"desc": "\n\n<p>刻度标签与轴线之间的距离。</p>\n", "uiControl": {"type": "number", "default": "8", "step": "0.5"}}, "axisLabel.formatter": {"desc": "<p>刻度标签的内容格式器，支持字符串模板和回调函数两种形式。</p>\n<p>示例:</p>\n<pre><code class=\"lang-js\">// 使用字符串模板，模板变量为刻度默认标签 {value}\nformatter: &#39;{value} kg&#39;\n</code></pre>\n<p>对于时间轴（<a href=\"#.type\">type</a>: <code class=\"codespan\">&#39;time&#39;</code>），<code class=\"codespan\">formatter</code> 的字符串模板支持多种形式：</p>\n<ul>\n<li><strong>字符串模板</strong>：简单快速实现常用日期时间模板，<code class=\"codespan\">string</code> 类型</li>\n<li><strong>回调函数</strong>：自定义 formatter，可以用来实现复杂高级的格式，<code class=\"codespan\">Function</code> 类型</li>\n<li><strong>分级模板</strong>：为不同时间粒度的标签使用不同的 formatter，<code class=\"codespan\">object</code> 类型</li>\n</ul>\n<p>下面我们分别介绍这三种形式。</p>\n<p><strong> 字符串模板 </strong></p>\n<p>使用字符串模板是一种方便实现常用日期时间格式化方式的形式。如果字符串模板可以实现你的效果，那我们优先推荐使用此方式；如果无法实现，再考虑其他两种更复杂的方式。支持的模板如下：</p>\n<table>\n<thead>\n<tr>\n<th>分类</th>\n<th>模板</th>\n<th>取值（英文）</th>\n<th>取值（中文）</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>Year</td>\n<td>{yyyy}</td>\n<td>e.g., 2020, 2021, ...</td>\n<td>例：2020, 2021, ...</td>\n</tr>\n<tr>\n<td></td>\n<td>{yy}</td>\n<td>00-99</td>\n<td>00-99</td>\n</tr>\n<tr>\n<td>Quarter</td>\n<td>{Q}</td>\n<td>1, 2, 3, 4</td>\n<td>1, 2, 3, 4</td>\n</tr>\n<tr>\n<td>Month</td>\n<td>{MMMM}</td>\n<td>e.g., January, February, ...</td>\n<td>一月、二月、……</td>\n</tr>\n<tr>\n<td></td>\n<td>{MMM}</td>\n<td>e.g., Jan, Feb, ...</td>\n<td>1月、2月、……</td>\n</tr>\n<tr>\n<td></td>\n<td>{MM}</td>\n<td>01-12</td>\n<td>01-12</td>\n</tr>\n<tr>\n<td></td>\n<td>{M}</td>\n<td>1-12</td>\n<td>1-12</td>\n</tr>\n<tr>\n<td>Day of Month</td>\n<td>{dd}</td>\n<td>01-31</td>\n<td>01-31</td>\n</tr>\n<tr>\n<td></td>\n<td>{d}</td>\n<td>1-31</td>\n<td>1-31</td>\n</tr>\n<tr>\n<td>Day of Week</td>\n<td>{eeee}</td>\n<td>Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday</td>\n<td>星期日、星期一、星期二、星期三、星期四、星期五、星期六</td>\n</tr>\n<tr>\n<td></td>\n<td>{ee}</td>\n<td>Sun, Mon, Tues, Wed, Thu, Fri, Sat</td>\n<td>日、一、二、三、四、五、六</td>\n</tr>\n<tr>\n<td></td>\n<td>{e}</td>\n<td>1-54</td>\n<td>1-54</td>\n</tr>\n<tr>\n<td>Hour</td>\n<td>{HH}</td>\n<td>00-23</td>\n<td>00-23</td>\n</tr>\n<tr>\n<td></td>\n<td>{H}</td>\n<td>0-23</td>\n<td>0-23</td>\n</tr>\n<tr>\n<td></td>\n<td>{hh}</td>\n<td>01-12</td>\n<td>01-12</td>\n</tr>\n<tr>\n<td></td>\n<td>{h}</td>\n<td>1-12</td>\n<td>1-12</td>\n</tr>\n<tr>\n<td>Minute</td>\n<td>{mm}</td>\n<td>00-59</td>\n<td>00-59</td>\n</tr>\n<tr>\n<td></td>\n<td>{m}</td>\n<td>0-59</td>\n<td>0-59</td>\n</tr>\n<tr>\n<td>Second</td>\n<td>{ss}</td>\n<td>00-59</td>\n<td>00-59</td>\n</tr>\n<tr>\n<td></td>\n<td>{s}</td>\n<td>0-59</td>\n<td>0-59</td>\n</tr>\n<tr>\n<td>Millisecond</td>\n<td>{SSS}</td>\n<td>000-999</td>\n<td>000-999</td>\n</tr>\n<tr>\n<td></td>\n<td>{S}</td>\n<td>0-999</td>\n<td>0-999</td>\n</tr>\n</tbody>\n</table>\n<blockquote>\n<p>其他语言请参考相应<a href=\"https://github.com/apache/incubator-echarts/tree/master/src/i18n\" target=\"_blank\">语言包</a>中的定义，语言包可以通过 <a href=\"api.html#echarts.registerLocale\" target=\"_blank\">echarts.registerLocale</a> 注册。</p>\n</blockquote>\n<p>示例:</p>\n<pre><code class=\"lang-js\">formatter: &#39;{yyyy}-{MM}-{dd}&#39; // 得到的 label 形如：&#39;2020-12-02&#39;\nformatter: &#39;{d}日&#39; // 得到的 label 形如：&#39;2日&#39;\n</code></pre>\n<p><strong> 回调函数 </strong></p>\n<p>回调函数可以根据刻度值返回不同的格式，如果有复杂的时间格式化需求，也可以引用第三方的日期时间相关的库（如 <a href=\"https://momentjs.com/\" target=\"_blank\">Moment.js</a>、<a href=\"https://date-fns.org/\" target=\"_blank\">date-fns</a> 等），返回显示的文本。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">// 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引\nformatter: function (value, index) {\n    // 格式化成月/日，只在第一个刻度显示年份\n    var date = new Date(value);\n    var texts = [(date.getMonth() + 1), date.getDate()];\n    if (index === 0) {\n        texts.unshift(date.getYear());\n    }\n    return texts.join(&#39;/&#39;);\n}\n</code></pre>\n<p><strong> 分级模板 </strong></p>\n<p>有时候，我们希望对不同的时间粒度采用不同的格式化策略。例如，在季度图表中，我们可能希望对每个月的第一天显示月份，而其他日期显示日期。我们可以使用以下方式实现该效果：</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">formatter: {\n    month: &#39;{MMMM}&#39;, // 一月、二月、……\n    day: &#39;{d}日&#39; // 1日、2日、……\n}\n</code></pre>\n<p>支持的分级以及各自默认的取值为：</p>\n<pre><code class=\"lang-js\">{\n    year: &#39;{yyyy}&#39;,\n    month: &#39;{MMM}&#39;,\n    day: &#39;{d}&#39;,\n    hour: &#39;{HH}:{mm}&#39;,\n    minute: &#39;{HH}:{mm}&#39;,\n    second: &#39;{HH}:{mm}:{ss}&#39;,\n    millisecond: &#39;{hh}:{mm}:{ss} {SSS}&#39;,\n    none: &#39;{yyyy}-{MM}-{dd} {hh}:{mm}:{ss} {SSS}&#39;\n}\n</code></pre>\n<p>以 <code class=\"codespan\">day</code> 为例，当一个刻度点的值的小时、分钟、秒、毫秒都为 <code class=\"codespan\">0</code> 时，将采用 <code class=\"codespan\">day</code> 的分级值作为模板。<code class=\"codespan\">none</code> 表示当其他规则都不适用时采用的模板，也就是带有毫秒值的刻度点的模板。</p>\n<p><strong> 富文本 </strong></p>\n<p>以上这三种形式的 formatter 都支持富文本，所以可以做成一些复杂的效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">xAxis: {\n    type: &#39;time&#39;,\n    axisLabel: {\n        formatter: {\n            // 一年的第一个月显示年度信息和月份信息\n            year: &#39;{yearStyle|{yyyy}}\\n{monthStyle|{MMM}}&#39;,\n            month: &#39;{monthStyle|{MMM}}&#39;\n        },\n        rich: {\n            yearStyle: {\n                // 让年度信息更醒目\n                color: &#39;#000&#39;,\n                fontWeight: &#39;bold&#39;\n            },\n            monthStyle: {\n                color: &#39;#999&#39;\n            }\n        }\n    }\n},\n</code></pre>\n<p>使用回调函数形式实现上面例子同样的效果：</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">xAxis: {\n    type: &#39;time&#39;,\n    axisLabel: {\n        formatter: function (value) {\n            const date = new Date(value);\n            const yearStart = new Date(value);\n            yearStart.setMonth(0);\n            yearStart.setDate(1);\n            yearStart.setHours(0);\n            yearStart.setMinutes(0);\n            yearStart.setSeconds(0);\n            yearStart.setMilliseconds(0);\n            // 判断一个刻度值知否为一年的开始\n            if (date.getTime() === yearStart.getTime()) {\n                return &#39;{year|&#39; + date.getFullYear() + &#39;}\\n&#39;\n                    + &#39;{month|&#39; + (date.getMonth() + 1) + &#39;月}&#39;;\n            }\n            else {\n                return &#39;{month|&#39; + (date.getMonth() + 1) + &#39;月}&#39;\n            }\n        },\n        rich: {\n            year: {\n                color: &#39;#000&#39;,\n                fontWeight: &#39;bold&#39;\n            },\n            month: {\n                color: &#39;#999&#39;\n            }\n        }\n    }\n},\n</code></pre>\n"}, "axisLabel.showMinLabel": {"desc": "\n\n<p>是否显示最小 tick 的 label。可取值 <code class=\"codespan\">true</code>, <code class=\"codespan\">false</code>, <code class=\"codespan\">null</code>。默认自动判定（即如果标签重叠，不会显示最小 tick 的 label）。</p>\n", "uiControl": {"type": "boolean"}}, "axisLabel.showMaxLabel": {"desc": "\n\n<p>是否显示最大 tick 的 label。可取值 <code class=\"codespan\">true</code>, <code class=\"codespan\">false</code>, <code class=\"codespan\">null</code>。默认自动判定（即如果标签重叠，不会显示最大 tick 的 label）。</p>\n", "uiControl": {"type": "boolean"}}, "axisLabel.color": {"desc": "\n\n<p>刻度标签文字的颜色，默认取 <a href=\"#xAxis.axisLine.lineStyle.color\">axisLine.lineStyle.color</a>。支持回调函数，格式如下</p>\n<pre><code class=\"lang-js\">(val: string) =&gt; Color\n</code></pre>\n<p>参数是标签的文本，返回颜色值，如下示例：</p>\n<pre><code class=\"lang-js\">textStyle: {\n    color: function (value, index) {\n        return value &gt;= 0 ? &#39;green&#39; : &#39;red&#39;;\n    }\n}\n</code></pre>\n", "uiControl": {"type": "color"}}, "axisLabel.fontStyle": {"desc": "\n\n<p>文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "axisLabel.fontWeight": {"desc": "\n\n<p>文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "axisLabel.fontFamily": {"desc": "\n\n<p>文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "axisLabel.fontSize": {"desc": "\n\n<p>文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "axisLabel.align": {"desc": "\n\n<p>文字水平对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;left&#39;</code></li>\n<li><code class=\"codespan\">&#39;center&#39;</code></li>\n<li><code class=\"codespan\">&#39;right&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">align</code>，则会取父层级的 <code class=\"codespan\">align</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    align: right,\n    rich: {\n        a: {\n            // 没有设置 `align`，则 `align` 为 right\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "left,center,right"}}, "axisLabel.verticalAlign": {"desc": "\n\n<p>文字垂直对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;top&#39;</code></li>\n<li><code class=\"codespan\">&#39;middle&#39;</code></li>\n<li><code class=\"codespan\">&#39;bottom&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">verticalAlign</code>，则会取父层级的 <code class=\"codespan\">verticalAlign</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    verticalAlign: bottom,\n    rich: {\n        a: {\n            // 没有设置 `verticalAlign`，则 `verticalAlign` 为 bottom\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "top,middle,bottom"}}, "axisLabel.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "axisLabel.backgroundColor": {"desc": "\n\n<p>文字块背景色。</p>\n<p>可以使用颜色值，例如：<code class=\"codespan\">&#39;#123234&#39;</code>, <code class=\"codespan\">&#39;red&#39;</code>, <code class=\"codespan\">&#39;rgba(0,23,11,0.3)&#39;</code>。</p>\n<p>也可以直接使用图片，例如：</p>\n<pre><code class=\"lang-js\">backgroundColor: {\n    image: &#39;xxx/xxx.png&#39;\n    // 这里可以是图片的 URL，\n    // 或者图片的 dataURI，\n    // 或者 HTMLImageElement 对象，\n    // 或者 HTMLCanvasElement 对象。\n}\n</code></pre>\n<p>当使用图片的时候，可以使用 <code class=\"codespan\">width</code> 或 <code class=\"codespan\">height</code> 指定高宽，也可以不指定自适应。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "axisLabel.borderColor": {"desc": "\n\n<p>文字块边框颜色。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "axisLabel.borderWidth": {"desc": "\n\n<p>文字块边框宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.borderRadius": {"desc": "\n\n<p>文字块的圆角。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "LT,RT, RB, LB"}}, "axisLabel.padding": {"desc": "\n\n<p>文字块的内边距。例如：</p>\n<ul>\n<li><code class=\"codespan\">padding: [3, 4, 5, 6]</code>：表示 <code class=\"codespan\">[上, 右, 下, 左]</code> 的边距。</li>\n<li><code class=\"codespan\">padding: 4</code>：表示 <code class=\"codespan\">padding: [4, 4, 4, 4]</code>。</li>\n<li><code class=\"codespan\">padding: [3, 4]</code>：表示 <code class=\"codespan\">padding: [3, 4, 3, 4]</code>。</li>\n</ul>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "T,R,B,L"}}, "axisLabel.shadowColor": {"desc": "\n\n<p>文字块的背景阴影颜色。</p>\n", "uiControl": {"type": "color"}}, "axisLabel.shadowBlur": {"desc": "\n\n<p>文字块的背景阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.shadowOffsetX": {"desc": "\n\n<p>文字块的背景阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisLabel.shadowOffsetY": {"desc": "\n\n<p>文字块的背景阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisLabel.width": {"desc": "\n\n<p>文本显示宽度。</p>\n", "uiControl": {"type": "number", "default": "100", "min": "1", "max": "500", "step": "1"}}, "axisLabel.height": {"desc": "\n\n<p>文本显示高度。</p>\n", "uiControl": {"type": "number", "default": "50", "min": "1", "max": "500", "step": "1"}}, "axisLabel.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "axisLabel.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "axisLabel.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisLabel.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisLabel.overflow": {"desc": "\n\n<p>文字超出宽度是否截断或者换行。配置<code class=\"codespan\">width</code>时有效</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 截断，并在末尾显示<code class=\"codespan\">ellipsis</code>配置的文本，默认为<code class=\"codespan\">...</code></li>\n<li><code class=\"codespan\">&#39;break&#39;</code> 换行</li>\n<li><code class=\"codespan\">&#39;breakAll&#39;</code> 换行，跟<code class=\"codespan\">&#39;break&#39;</code>不同的是，在英语等拉丁文中，<code class=\"codespan\">&#39;breakAll&#39;</code>还会强制单词内换行</li>\n</ul>\n", "uiControl": {"type": "enum", "options": "truncate,break,breakAll"}}, "axisLabel.ellipsis": {"desc": "<p>在<code class=\"codespan\">overflow</code>配置为<code class=\"codespan\">&#39;truncate&#39;</code>的时候，可以通过该属性配置末尾显示的文本。</p>\n"}, "axisLabel.lineOverflow": {"desc": "<p>文本超出高度部分是否截断，配置<code class=\"codespan\">height</code>时有效。</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 在文本行数超出高度部分截断。</li>\n</ul>\n"}, "axisLabel.rich": {"desc": "<p>在 <code class=\"codespan\">rich</code> 里面，可以自定义富文本样式。利用富文本样式，可以在标签中做出非常丰富的效果。</p>\n<p>例如：</p>\n<pre><code class=\"lang-js\">label: {\n    // 在文本中，可以对部分文本采用 rich 中定义样式。\n    // 这里需要在文本中使用标记符号：\n    // `{styleName|text content text content}` 标记样式名。\n    // 注意，换行仍是使用 &#39;\\n&#39;。\n    formatter: [\n        &#39;{a|这段文本采用样式a}&#39;,\n        &#39;{b|这段文本采用样式b}这段用默认样式{x|这段用样式x}&#39;\n    ].join(&#39;\\n&#39;),\n\n    rich: {\n        a: {\n            color: &#39;red&#39;,\n            lineHeight: 10\n        },\n        b: {\n            backgroundColor: {\n                image: &#39;xxx/xxx.jpg&#39;\n            },\n            height: 40\n        },\n        x: {\n            fontSize: 18,\n            fontFamily: &#39;Microsoft YaHei&#39;,\n            borderColor: &#39;#449933&#39;,\n            borderRadius: 4\n        },\n        ...\n    }\n}\n</code></pre>\n<p>详情参见教程：<a href=\"tutorial.html#%E5%AF%8C%E6%96%87%E6%9C%AC%E6%A0%87%E7%AD%BE\" target=\"_blank\">富文本标签</a></p>\n"}, "axisLabel.rich.<style_name>.color": {"desc": "\n\n<p>文字的颜色。</p>\n", "uiControl": {"type": "color", "default": "null"}}, "axisLabel.rich.<style_name>.fontStyle": {"desc": "\n\n<p>文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "axisLabel.rich.<style_name>.fontWeight": {"desc": "\n\n<p>文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "axisLabel.rich.<style_name>.fontFamily": {"desc": "\n\n<p>文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "axisLabel.rich.<style_name>.fontSize": {"desc": "\n\n<p>文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "axisLabel.rich.<style_name>.align": {"desc": "\n\n<p>文字水平对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;left&#39;</code></li>\n<li><code class=\"codespan\">&#39;center&#39;</code></li>\n<li><code class=\"codespan\">&#39;right&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">align</code>，则会取父层级的 <code class=\"codespan\">align</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    align: right,\n    rich: {\n        a: {\n            // 没有设置 `align`，则 `align` 为 right\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "left,center,right"}}, "axisLabel.rich.<style_name>.verticalAlign": {"desc": "\n\n<p>文字垂直对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;top&#39;</code></li>\n<li><code class=\"codespan\">&#39;middle&#39;</code></li>\n<li><code class=\"codespan\">&#39;bottom&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">verticalAlign</code>，则会取父层级的 <code class=\"codespan\">verticalAlign</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    verticalAlign: bottom,\n    rich: {\n        a: {\n            // 没有设置 `verticalAlign`，则 `verticalAlign` 为 bottom\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "top,middle,bottom"}}, "axisLabel.rich.<style_name>.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "axisLabel.rich.<style_name>.backgroundColor": {"desc": "\n\n<p>文字块背景色。</p>\n<p>可以使用颜色值，例如：<code class=\"codespan\">&#39;#123234&#39;</code>, <code class=\"codespan\">&#39;red&#39;</code>, <code class=\"codespan\">&#39;rgba(0,23,11,0.3)&#39;</code>。</p>\n<p>也可以直接使用图片，例如：</p>\n<pre><code class=\"lang-js\">backgroundColor: {\n    image: &#39;xxx/xxx.png&#39;\n    // 这里可以是图片的 URL，\n    // 或者图片的 dataURI，\n    // 或者 HTMLImageElement 对象，\n    // 或者 HTMLCanvasElement 对象。\n}\n</code></pre>\n<p>当使用图片的时候，可以使用 <code class=\"codespan\">width</code> 或 <code class=\"codespan\">height</code> 指定高宽，也可以不指定自适应。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "axisLabel.rich.<style_name>.borderColor": {"desc": "\n\n<p>文字块边框颜色。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "axisLabel.rich.<style_name>.borderWidth": {"desc": "\n\n<p>文字块边框宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.rich.<style_name>.borderRadius": {"desc": "\n\n<p>文字块的圆角。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "LT,RT, RB, LB"}}, "axisLabel.rich.<style_name>.padding": {"desc": "\n\n<p>文字块的内边距。例如：</p>\n<ul>\n<li><code class=\"codespan\">padding: [3, 4, 5, 6]</code>：表示 <code class=\"codespan\">[上, 右, 下, 左]</code> 的边距。</li>\n<li><code class=\"codespan\">padding: 4</code>：表示 <code class=\"codespan\">padding: [4, 4, 4, 4]</code>。</li>\n<li><code class=\"codespan\">padding: [3, 4]</code>：表示 <code class=\"codespan\">padding: [3, 4, 3, 4]</code>。</li>\n</ul>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "T,R,B,L"}}, "axisLabel.rich.<style_name>.shadowColor": {"desc": "\n\n<p>文字块的背景阴影颜色。</p>\n", "uiControl": {"type": "color"}}, "axisLabel.rich.<style_name>.shadowBlur": {"desc": "\n\n<p>文字块的背景阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.rich.<style_name>.shadowOffsetX": {"desc": "\n\n<p>文字块的背景阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisLabel.rich.<style_name>.shadowOffsetY": {"desc": "\n\n<p>文字块的背景阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisLabel.rich.<style_name>.width": {"desc": "<p>文字块的宽度。一般不用指定，不指定则自动是文字的宽度。在想做表格项或者使用图片（参见 <code class=\"codespan\">backgroundColor</code>）时，可能会使用它。</p>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n<p><code class=\"codespan\">width</code> 也可以是百分比字符串，如 <code class=\"codespan\">&#39;100%&#39;</code>。表示的是所在文本块的 <code class=\"codespan\">contentWidth</code>（即不包含文本块的 <code class=\"codespan\">padding</code>）的百分之多少。之所以以 <code class=\"codespan\">contentWidth</code> 做基数，因为每个文本片段只能基于 <code class=\"codespan\">content box</code> 布局。如果以 <code class=\"codespan\">outerWidth</code> 做基数，则百分比的计算在实用中不具有意义，可能会超出。</p>\n<p>注意，如果不定义 <code class=\"codespan\">rich</code> 属性，则不能指定 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code>。</p>\n"}, "axisLabel.rich.<style_name>.height": {"desc": "<p>文字块的高度。一般不用指定，不指定则自动是文字的高度。在使用图片（参见 <code class=\"codespan\">backgroundColor</code>）时，可能会使用它。</p>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n<p>注意，如果不定义 <code class=\"codespan\">rich</code> 属性，则不能指定 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code>。</p>\n"}, "axisLabel.rich.<style_name>.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "axisLabel.rich.<style_name>.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.rich.<style_name>.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "axisLabel.rich.<style_name>.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisLabel.rich.<style_name>.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisLabel.rich.<style_name>.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n\n", "uiControl": {"type": "number", "step": "0.5"}}, "splitLine": {"desc": "<p>坐标轴在 <a href=\"#grid\">grid</a> 区域中的分隔线。</p>\n"}, "splitLine.show": {"desc": "\n\n<p>是否显示分隔线。默认数值轴显示，类目轴不显示。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "splitLine.interval": {"desc": "\n\n\n\n<p>坐标轴分隔线的显示间隔，在类目轴中有效。默认同 <a href=\"#xAxis.axisLabel.interval\">axisLabel.interval</a> 一样。</p>\n<p>默认会采用标签不重叠的策略间隔显示标签。</p>\n<p>可以设置成 0 强制显示所有标签。</p>\n<p>如果设置为 <code class=\"codespan\">1</code>，表示『隔一个标签显示一个标签』，如果值为 <code class=\"codespan\">2</code>，表示隔两个标签显示一个标签，以此类推。</p>\n<p>可以用数值表示间隔的数据，也可以通过回调函数控制。回调函数格式如下：</p>\n<pre><code class=\"lang-js\">(index:number, value: string) =&gt; boolean\n</code></pre>\n<p>第一个参数是类目的 index，第二个值是类目名称，如果跳过则返回 <code class=\"codespan\">false</code>。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "1"}}, "splitLine.lineStyle.color": {"desc": "\n\n<p>分隔线颜色，可以设置成单个颜色。</p>\n<p>也可以设置成颜色数组，分隔线会按数组中颜色的顺序依次循环设置颜色。</p>\n<p>示例</p>\n<pre><code>splitLine: {\n    lineStyle: {\n        // 使用深浅的间隔色\n        color: [&#39;#aaa&#39;, &#39;#ddd&#39;]\n    }\n}\n</code></pre>", "uiControl": {"type": "color"}}, "splitLine.lineStyle.width": {"desc": "\n\n<p>分隔线线宽。</p>\n", "uiControl": {"type": "number", "value": "1", "min": "0", "step": "0.5"}}, "splitLine.lineStyle.type": {"desc": "\n\n<p>分隔线线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "splitLine.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "splitLine.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "splitLine.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "splitLine.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "splitLine.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "minorSplitLine": {"desc": "<blockquote>\n<p>从 <code class=\"codespan\">v4.6.0</code> 开始支持</p>\n</blockquote>\n<p>坐标轴在 <a href=\"#grid\">grid</a> 区域中的次分隔线。次分割线会对齐次刻度线 <a href=\"#xAxis.minorTick\">minorTick</a></p>\n"}, "minorSplitLine.show": {"desc": "\n\n<p>是否显示次分隔线。默认不显示。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "minorSplitLine.lineStyle.color": {"desc": "\n\n<p>次分隔线线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "minorSplitLine.lineStyle.width": {"desc": "\n\n<p>次分隔线线宽。</p>\n", "uiControl": {"type": "number", "value": "1", "min": "0", "step": "0.5"}}, "minorSplitLine.lineStyle.type": {"desc": "\n\n<p>次分隔线线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "minorSplitLine.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "minorSplitLine.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "minorSplitLine.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "minorSplitLine.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "minorSplitLine.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "splitArea": {"desc": "<p>坐标轴在 <a href=\"#grid\">grid</a> 区域中的分隔区域，默认不显示。</p>\n"}, "splitArea.interval": {"desc": "\n\n\n\n<p>坐标轴分隔区域的显示间隔，在类目轴中有效。默认同 <a href=\"#xAxis.axisLabel.interval\">axisLabel.interval</a> 一样。</p>\n<p>默认会采用标签不重叠的策略间隔显示标签。</p>\n<p>可以设置成 0 强制显示所有标签。</p>\n<p>如果设置为 <code class=\"codespan\">1</code>，表示『隔一个标签显示一个标签』，如果值为 <code class=\"codespan\">2</code>，表示隔两个标签显示一个标签，以此类推。</p>\n<p>可以用数值表示间隔的数据，也可以通过回调函数控制。回调函数格式如下：</p>\n<pre><code class=\"lang-js\">(index:number, value: string) =&gt; boolean\n</code></pre>\n<p>第一个参数是类目的 index，第二个值是类目名称，如果跳过则返回 <code class=\"codespan\">false</code>。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "1"}}, "splitArea.show": {"desc": "\n\n<p>是否显示分隔区域。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "splitArea.areaStyle": {"desc": "<p>分隔区域的样式设置。</p>\n"}, "splitArea.areaStyle.color": {"desc": "<p>分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。</p>\n"}, "splitArea.areaStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "splitArea.areaStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "splitArea.areaStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "splitArea.areaStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "splitArea.areaStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "data": {"desc": "<p>类目数据，在类目轴（<a href=\"#xAxis.type\">type</a>: <code class=\"codespan\">&#39;category&#39;</code>）中有效。</p>\n<p>如果没有设置 <a href=\"#xAxis.type\">type</a>，但是设置了 <code class=\"codespan\">axis.data</code>，则认为 <code class=\"codespan\">type</code> 是 <code class=\"codespan\">&#39;category&#39;</code>。</p>\n<p>如果设置了 <a href=\"#xAxis.type\">type</a> 是 <code class=\"codespan\">&#39;category&#39;</code>，但没有设置 <code class=\"codespan\">axis.data</code>，则 <code class=\"codespan\">axis.data</code> 的内容会自动从 <a href=\"#series.data\">series.data</a> 中获取，这会比较方便。不过注意，<code class=\"codespan\">axis.data</code> 指明的是 <code class=\"codespan\">&#39;category&#39;</code> 轴的取值范围。如果不指定而是从 <a href=\"#series.data\">series.data</a> 中获取，那么只能获取到 <a href=\"#series.data\">series.data</a> 中出现的值。比如说，假如 <a href=\"#series.data\">series.data</a> 为空时，就什么也获取不到。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">// 所有类目名称列表\ndata: [&#39;周一&#39;, &#39;周二&#39;, &#39;周三&#39;, &#39;周四&#39;, &#39;周五&#39;, &#39;周六&#39;, &#39;周日&#39;]\n// 每一项也可以是具体的配置项，此时取配置项中的 `value` 为类目名\ndata: [{\n    value: &#39;周一&#39;,\n    // 突出周一\n    textStyle: {\n        fontSize: 20,\n        color: &#39;red&#39;\n    }\n}, &#39;周二&#39;, &#39;周三&#39;, &#39;周四&#39;, &#39;周五&#39;, &#39;周六&#39;, &#39;周日&#39;]\n</code></pre>\n"}, "data.value": {"desc": "<p>单个类目名称。</p>\n"}, "data.textStyle": {"desc": "<p>类目标签的文字样式。</p>\n"}, "data.textStyle.color": {"desc": "\n\n<p>文字的颜色。</p>\n", "uiControl": {"type": "color", "default": "null"}}, "data.textStyle.fontStyle": {"desc": "\n\n<p>文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "data.textStyle.fontWeight": {"desc": "\n\n<p>文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "data.textStyle.fontFamily": {"desc": "\n\n<p>文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "data.textStyle.fontSize": {"desc": "\n\n<p>文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "data.textStyle.align": {"desc": "\n\n<p>文字水平对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;left&#39;</code></li>\n<li><code class=\"codespan\">&#39;center&#39;</code></li>\n<li><code class=\"codespan\">&#39;right&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">align</code>，则会取父层级的 <code class=\"codespan\">align</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    align: right,\n    rich: {\n        a: {\n            // 没有设置 `align`，则 `align` 为 right\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "left,center,right"}}, "data.textStyle.verticalAlign": {"desc": "\n\n<p>文字垂直对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;top&#39;</code></li>\n<li><code class=\"codespan\">&#39;middle&#39;</code></li>\n<li><code class=\"codespan\">&#39;bottom&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">verticalAlign</code>，则会取父层级的 <code class=\"codespan\">verticalAlign</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    verticalAlign: bottom,\n    rich: {\n        a: {\n            // 没有设置 `verticalAlign`，则 `verticalAlign` 为 bottom\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "top,middle,bottom"}}, "data.textStyle.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "data.textStyle.backgroundColor": {"desc": "\n\n<p>文字块背景色。</p>\n<p>可以使用颜色值，例如：<code class=\"codespan\">&#39;#123234&#39;</code>, <code class=\"codespan\">&#39;red&#39;</code>, <code class=\"codespan\">&#39;rgba(0,23,11,0.3)&#39;</code>。</p>\n<p>也可以直接使用图片，例如：</p>\n<pre><code class=\"lang-js\">backgroundColor: {\n    image: &#39;xxx/xxx.png&#39;\n    // 这里可以是图片的 URL，\n    // 或者图片的 dataURI，\n    // 或者 HTMLImageElement 对象，\n    // 或者 HTMLCanvasElement 对象。\n}\n</code></pre>\n<p>当使用图片的时候，可以使用 <code class=\"codespan\">width</code> 或 <code class=\"codespan\">height</code> 指定高宽，也可以不指定自适应。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "data.textStyle.borderColor": {"desc": "\n\n<p>文字块边框颜色。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "data.textStyle.borderWidth": {"desc": "\n\n<p>文字块边框宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.borderRadius": {"desc": "\n\n<p>文字块的圆角。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "LT,RT, RB, LB"}}, "data.textStyle.padding": {"desc": "\n\n<p>文字块的内边距。例如：</p>\n<ul>\n<li><code class=\"codespan\">padding: [3, 4, 5, 6]</code>：表示 <code class=\"codespan\">[上, 右, 下, 左]</code> 的边距。</li>\n<li><code class=\"codespan\">padding: 4</code>：表示 <code class=\"codespan\">padding: [4, 4, 4, 4]</code>。</li>\n<li><code class=\"codespan\">padding: [3, 4]</code>：表示 <code class=\"codespan\">padding: [3, 4, 3, 4]</code>。</li>\n</ul>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "T,R,B,L"}}, "data.textStyle.shadowColor": {"desc": "\n\n<p>文字块的背景阴影颜色。</p>\n", "uiControl": {"type": "color"}}, "data.textStyle.shadowBlur": {"desc": "\n\n<p>文字块的背景阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.shadowOffsetX": {"desc": "\n\n<p>文字块的背景阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "data.textStyle.shadowOffsetY": {"desc": "\n\n<p>文字块的背景阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "data.textStyle.width": {"desc": "\n\n<p>文本显示宽度。</p>\n", "uiControl": {"type": "number", "default": "100", "min": "1", "max": "500", "step": "1"}}, "data.textStyle.height": {"desc": "\n\n<p>文本显示高度。</p>\n", "uiControl": {"type": "number", "default": "50", "min": "1", "max": "500", "step": "1"}}, "data.textStyle.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "data.textStyle.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "data.textStyle.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "data.textStyle.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "data.textStyle.overflow": {"desc": "\n\n<p>文字超出宽度是否截断或者换行。配置<code class=\"codespan\">width</code>时有效</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 截断，并在末尾显示<code class=\"codespan\">ellipsis</code>配置的文本，默认为<code class=\"codespan\">...</code></li>\n<li><code class=\"codespan\">&#39;break&#39;</code> 换行</li>\n<li><code class=\"codespan\">&#39;breakAll&#39;</code> 换行，跟<code class=\"codespan\">&#39;break&#39;</code>不同的是，在英语等拉丁文中，<code class=\"codespan\">&#39;breakAll&#39;</code>还会强制单词内换行</li>\n</ul>\n", "uiControl": {"type": "enum", "options": "truncate,break,breakAll"}}, "data.textStyle.ellipsis": {"desc": "<p>在<code class=\"codespan\">overflow</code>配置为<code class=\"codespan\">&#39;truncate&#39;</code>的时候，可以通过该属性配置末尾显示的文本。</p>\n"}, "data.textStyle.lineOverflow": {"desc": "<p>文本超出高度部分是否截断，配置<code class=\"codespan\">height</code>时有效。</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 在文本行数超出高度部分截断。</li>\n</ul>\n"}, "data.textStyle.rich": {"desc": "<p>在 <code class=\"codespan\">rich</code> 里面，可以自定义富文本样式。利用富文本样式，可以在标签中做出非常丰富的效果。</p>\n<p>例如：</p>\n<pre><code class=\"lang-js\">label: {\n    // 在文本中，可以对部分文本采用 rich 中定义样式。\n    // 这里需要在文本中使用标记符号：\n    // `{styleName|text content text content}` 标记样式名。\n    // 注意，换行仍是使用 &#39;\\n&#39;。\n    formatter: [\n        &#39;{a|这段文本采用样式a}&#39;,\n        &#39;{b|这段文本采用样式b}这段用默认样式{x|这段用样式x}&#39;\n    ].join(&#39;\\n&#39;),\n\n    rich: {\n        a: {\n            color: &#39;red&#39;,\n            lineHeight: 10\n        },\n        b: {\n            backgroundColor: {\n                image: &#39;xxx/xxx.jpg&#39;\n            },\n            height: 40\n        },\n        x: {\n            fontSize: 18,\n            fontFamily: &#39;Microsoft YaHei&#39;,\n            borderColor: &#39;#449933&#39;,\n            borderRadius: 4\n        },\n        ...\n    }\n}\n</code></pre>\n<p>详情参见教程：<a href=\"tutorial.html#%E5%AF%8C%E6%96%87%E6%9C%AC%E6%A0%87%E7%AD%BE\" target=\"_blank\">富文本标签</a></p>\n"}, "data.textStyle.rich.<style_name>.color": {"desc": "\n\n<p>文字的颜色。</p>\n", "uiControl": {"type": "color", "default": "null"}}, "data.textStyle.rich.<style_name>.fontStyle": {"desc": "\n\n<p>文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "data.textStyle.rich.<style_name>.fontWeight": {"desc": "\n\n<p>文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "data.textStyle.rich.<style_name>.fontFamily": {"desc": "\n\n<p>文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "data.textStyle.rich.<style_name>.fontSize": {"desc": "\n\n<p>文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "data.textStyle.rich.<style_name>.align": {"desc": "\n\n<p>文字水平对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;left&#39;</code></li>\n<li><code class=\"codespan\">&#39;center&#39;</code></li>\n<li><code class=\"codespan\">&#39;right&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">align</code>，则会取父层级的 <code class=\"codespan\">align</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    align: right,\n    rich: {\n        a: {\n            // 没有设置 `align`，则 `align` 为 right\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "left,center,right"}}, "data.textStyle.rich.<style_name>.verticalAlign": {"desc": "\n\n<p>文字垂直对齐方式，默认自动。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;top&#39;</code></li>\n<li><code class=\"codespan\">&#39;middle&#39;</code></li>\n<li><code class=\"codespan\">&#39;bottom&#39;</code></li>\n</ul>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">verticalAlign</code>，则会取父层级的 <code class=\"codespan\">verticalAlign</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    verticalAlign: bottom,\n    rich: {\n        a: {\n            // 没有设置 `verticalAlign`，则 `verticalAlign` 为 bottom\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "enum", "options": "top,middle,bottom"}}, "data.textStyle.rich.<style_name>.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "data.textStyle.rich.<style_name>.backgroundColor": {"desc": "\n\n<p>文字块背景色。</p>\n<p>可以使用颜色值，例如：<code class=\"codespan\">&#39;#123234&#39;</code>, <code class=\"codespan\">&#39;red&#39;</code>, <code class=\"codespan\">&#39;rgba(0,23,11,0.3)&#39;</code>。</p>\n<p>也可以直接使用图片，例如：</p>\n<pre><code class=\"lang-js\">backgroundColor: {\n    image: &#39;xxx/xxx.png&#39;\n    // 这里可以是图片的 URL，\n    // 或者图片的 dataURI，\n    // 或者 HTMLImageElement 对象，\n    // 或者 HTMLCanvasElement 对象。\n}\n</code></pre>\n<p>当使用图片的时候，可以使用 <code class=\"codespan\">width</code> 或 <code class=\"codespan\">height</code> 指定高宽，也可以不指定自适应。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "data.textStyle.rich.<style_name>.borderColor": {"desc": "\n\n<p>文字块边框颜色。</p>\n", "uiControl": {"type": "color", "default": "#fff"}}, "data.textStyle.rich.<style_name>.borderWidth": {"desc": "\n\n<p>文字块边框宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.rich.<style_name>.borderRadius": {"desc": "\n\n<p>文字块的圆角。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "LT,RT, RB, LB"}}, "data.textStyle.rich.<style_name>.padding": {"desc": "\n\n<p>文字块的内边距。例如：</p>\n<ul>\n<li><code class=\"codespan\">padding: [3, 4, 5, 6]</code>：表示 <code class=\"codespan\">[上, 右, 下, 左]</code> 的边距。</li>\n<li><code class=\"codespan\">padding: 4</code>：表示 <code class=\"codespan\">padding: [4, 4, 4, 4]</code>。</li>\n<li><code class=\"codespan\">padding: [3, 4]</code>：表示 <code class=\"codespan\">padding: [3, 4, 3, 4]</code>。</li>\n</ul>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n", "uiControl": {"type": "vector", "min": "0", "dims": "T,R,B,L"}}, "data.textStyle.rich.<style_name>.shadowColor": {"desc": "\n\n<p>文字块的背景阴影颜色。</p>\n", "uiControl": {"type": "color"}}, "data.textStyle.rich.<style_name>.shadowBlur": {"desc": "\n\n<p>文字块的背景阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.rich.<style_name>.shadowOffsetX": {"desc": "\n\n<p>文字块的背景阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "data.textStyle.rich.<style_name>.shadowOffsetY": {"desc": "\n\n<p>文字块的背景阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "data.textStyle.rich.<style_name>.width": {"desc": "<p>文字块的宽度。一般不用指定，不指定则自动是文字的宽度。在想做表格项或者使用图片（参见 <code class=\"codespan\">backgroundColor</code>）时，可能会使用它。</p>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n<p><code class=\"codespan\">width</code> 也可以是百分比字符串，如 <code class=\"codespan\">&#39;100%&#39;</code>。表示的是所在文本块的 <code class=\"codespan\">contentWidth</code>（即不包含文本块的 <code class=\"codespan\">padding</code>）的百分之多少。之所以以 <code class=\"codespan\">contentWidth</code> 做基数，因为每个文本片段只能基于 <code class=\"codespan\">content box</code> 布局。如果以 <code class=\"codespan\">outerWidth</code> 做基数，则百分比的计算在实用中不具有意义，可能会超出。</p>\n<p>注意，如果不定义 <code class=\"codespan\">rich</code> 属性，则不能指定 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code>。</p>\n"}, "data.textStyle.rich.<style_name>.height": {"desc": "<p>文字块的高度。一般不用指定，不指定则自动是文字的高度。在使用图片（参见 <code class=\"codespan\">backgroundColor</code>）时，可能会使用它。</p>\n<p>注意，文字块的 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code> 指定的是内容高宽，不包含 <code class=\"codespan\">padding</code>。</p>\n<p>注意，如果不定义 <code class=\"codespan\">rich</code> 属性，则不能指定 <code class=\"codespan\">width</code> 和 <code class=\"codespan\">height</code>。</p>\n"}, "data.textStyle.rich.<style_name>.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "data.textStyle.rich.<style_name>.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.rich.<style_name>.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "data.textStyle.rich.<style_name>.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "data.textStyle.rich.<style_name>.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "data.textStyle.rich.<style_name>.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisPointer": {"desc": "<p>坐标轴指示器配置项。</p>\n"}, "axisPointer.show": {"desc": "\n\n<p>默认不显示。但是如果 <a href=\"#tooltip.trigger\">tooltip.trigger</a> 设置为 <code class=\"codespan\">&#39;axis&#39;</code> 或者 <a href=\"#tooltip.axisPointer.type\">tooltip.axisPointer.type</a> 设置为 <code class=\"codespan\">&#39;cross&#39;</code>，则自动显示 axisPointer。坐标系会自动选择显示显示哪个轴的 axisPointer，也可以使用 <a href=\"#tooltip.axisPointer.axis\">tooltip.axisPointer.axis</a> 改变这种选择。</p>\n", "uiControl": {"type": "boolean"}}, "axisPointer.type": {"desc": "\n\n<p>指示器类型。</p>\n<p>可选</p>\n<ul>\n<li><p><code class=\"codespan\">&#39;line&#39;</code> 直线指示器</p>\n</li>\n<li><p><code class=\"codespan\">&#39;shadow&#39;</code> 阴影指示器</p>\n</li>\n<li><p><code class=\"codespan\">&#39;none&#39;</code> 无指示器</p>\n</li>\n</ul>\n", "uiControl": {"type": "enum", "options": "line,shadow,none"}}, "axisPointer.snap": {"desc": "<p>坐标轴指示器是否自动吸附到点上。默认自动判断。</p>\n<p>这个功能在数值轴和时间轴上比较有意义，可以自动寻找细小的数值点。</p>\n"}, "axisPointer.z": {"desc": "<p>坐标轴指示器的 z 值。控制图形的前后顺序。<code class=\"codespan\">z</code>值小的图形会被<code class=\"codespan\">z</code>值大的图形覆盖。</p>\n"}, "axisPointer.label": {"desc": "<p>坐标轴指示器的文本标签。</p>\n"}, "axisPointer.label.show": {"desc": "<p>是否显示文本标签。如果 <a href=\"#tooltip.axisPointer.type\">tooltip.axisPointer.type</a> 设置为 <code class=\"codespan\">&#39;cross&#39;</code> 则默认显示标签，否则默认不显示。</p>\n"}, "axisPointer.label.precision": {"desc": "<p>文本标签中数值的小数点精度。默认根据当前轴的值自动判断。也可以指定如 <code class=\"codespan\">2</code> 表示保留两位小数。</p>\n"}, "axisPointer.label.formatter": {"desc": "<p>文本标签文字的格式化器。</p>\n<p>如果为 <code class=\"codespan\">string</code>，可以是例如：<code class=\"codespan\">formatter: &#39;some text {value} some text</code>，其中 <code class=\"codespan\">{value}</code> 会被自动替换为轴的值。</p>\n<p>如果为 <code class=\"codespan\">function</code>，可以是例如：</p>\n<p><strong>参数：</strong></p>\n<p><code class=\"codespan\">{Object}</code> params: 含有：</p>\n<p><code class=\"codespan\">{Object}</code> params.value: 轴当前值，如果 axis.type 为 &#39;category&#39; 时，其值为 axis.data 里的数值。如果 axis.type 为 <code class=\"codespan\">&#39;time&#39;</code>，其值为时间戳。</p>\n<p><code class=\"codespan\">{Array.&lt;Object&gt;}</code> params.seriesData: 一个数组，是当前 axisPointer 最近的点的信息，每项内容为</p>\n<p><code class=\"codespan\">{string}</code> params.axisDimension: 轴的维度名，例如直角坐标系中是 <code class=\"codespan\">&#39;x&#39;</code>、<code class=\"codespan\">&#39;y&#39;</code>，极坐标系中是 <code class=\"codespan\">&#39;radius&#39;</code>、<code class=\"codespan\">&#39;angle&#39;</code>。</p>\n<p><code class=\"codespan\">{number}</code> params.axisIndex: 轴的 index，<code class=\"codespan\">0</code>、<code class=\"codespan\">1</code>、<code class=\"codespan\">2</code>、...</p>\n<pre><code class=\"lang-js\">{\n    componentType: &#39;series&#39;,\n    // 系列类型\n    seriesType: string,\n    // 系列在传入的 option.series 中的 index\n    seriesIndex: number,\n    // 系列名称\n    seriesName: string,\n    // 数据名，类目名\n    name: string,\n    // 数据在传入的 data 数组中的 index\n    dataIndex: number,\n    // 传入的原始数据项\n    data: Object,\n    // 传入的数据值。在多数系列下它和 data 相同。在一些系列下是 data 中的分量（如 map、radar 中）\n    value: number|Array|Object,\n    // 坐标轴 encode 映射信息，\n    // key 为坐标轴（如 &#39;x&#39; &#39;y&#39; &#39;radius&#39; &#39;angle&#39; 等）\n    // value 必然为数组，不会为 null/undefied，表示 dimension index 。\n    // 其内容如：\n    // {\n    //     x: [2] // dimension index 为 2 的数据映射到 x 轴\n    //     y: [0] // dimension index 为 0 的数据映射到 y 轴\n    // }\n    encode: Object,\n    // 维度名列表\n    dimensionNames: Array&lt;String&gt;,\n    // 数据的维度 index，如 0 或 1 或 2 ...\n    // 仅在雷达图中使用。\n    dimensionIndex: number,\n    // 数据图形的颜色\n    color: string,\n\n\n\n}\n</code></pre>\n<p>注：encode 和 dimensionNames 的使用方式，例如：</p>\n<p>如果数据为：</p>\n<pre><code class=\"lang-js\">dataset: {\n    source: [\n        [&#39;Matcha Latte&#39;, 43.3, 85.8, 93.7],\n        [&#39;Milk Tea&#39;, 83.1, 73.4, 55.1],\n        [&#39;Cheese Cocoa&#39;, 86.4, 65.2, 82.5],\n        [&#39;Walnut Brownie&#39;, 72.4, 53.9, 39.1]\n    ]\n}\n</code></pre>\n<p>则可这样得到 y 轴对应的 value：</p>\n<pre><code class=\"lang-js\">params.value[params.encode.y[0]]\n</code></pre>\n<p>如果数据为：</p>\n<pre><code class=\"lang-js\">dataset: {\n    dimensions: [&#39;product&#39;, &#39;2015&#39;, &#39;2016&#39;, &#39;2017&#39;],\n    source: [\n        {product: &#39;Matcha Latte&#39;, &#39;2015&#39;: 43.3, &#39;2016&#39;: 85.8, &#39;2017&#39;: 93.7},\n        {product: &#39;Milk Tea&#39;, &#39;2015&#39;: 83.1, &#39;2016&#39;: 73.4, &#39;2017&#39;: 55.1},\n        {product: &#39;Cheese Cocoa&#39;, &#39;2015&#39;: 86.4, &#39;2016&#39;: 65.2, &#39;2017&#39;: 82.5},\n        {product: &#39;Walnut Brownie&#39;, &#39;2015&#39;: 72.4, &#39;2016&#39;: 53.9, &#39;2017&#39;: 39.1}\n    ]\n}\n</code></pre>\n<p>则可这样得到 y 轴对应的 value：</p>\n<pre><code class=\"lang-js\">params.value[params.dimensionNames[params.encode.y[0]]]\n</code></pre>\n<p>每项内容还包括轴的信息：</p>\n<pre><code class=\"lang-js\">{\n    axisDim: &#39;x&#39;, // &#39;x&#39;, &#39;y&#39;, &#39;angle&#39;, &#39;radius&#39;, &#39;single&#39;\n    axisId: &#39;xxx&#39;,\n    axisName: &#39;xxx&#39;,\n    axisIndex: 3,\n    axisValue: 121, // 当前 axisPointer 对应的 value。\n    axisValueLabel: &#39;文本&#39;\n}\n</code></pre>\n<p><strong>返回值：</strong></p>\n<p>显示的 string。</p>\n<p>例如：</p>\n<pre><code class=\"lang-js\">formatter: function (params) {\n    // 假设此轴的 type 为 &#39;time&#39;。\n    return &#39;some text&#39; + echarts.format.formatTime(params.value);\n}\n</code></pre>\n"}, "axisPointer.label.margin": {"desc": "<p>label 距离轴的距离。</p>\n"}, "axisPointer.label.color": {"desc": "\n\n<p>文字的颜色。</p>\n", "uiControl": {"type": "color", "default": "'#fff'"}}, "axisPointer.label.fontStyle": {"desc": "\n\n<p>文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "axisPointer.label.fontWeight": {"desc": "\n\n<p>文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "axisPointer.label.fontFamily": {"desc": "\n\n<p>文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "axisPointer.label.fontSize": {"desc": "\n\n<p>文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "axisPointer.label.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "axisPointer.label.width": {"desc": "\n\n<p>文本显示宽度。</p>\n", "uiControl": {"type": "number", "default": "100", "min": "1", "max": "500", "step": "1"}}, "axisPointer.label.height": {"desc": "\n\n<p>文本显示高度。</p>\n", "uiControl": {"type": "number", "default": "50", "min": "1", "max": "500", "step": "1"}}, "axisPointer.label.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "axisPointer.label.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisPointer.label.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "axisPointer.label.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "axisPointer.label.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisPointer.label.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "axisPointer.label.overflow": {"desc": "\n\n<p>文字超出宽度是否截断或者换行。配置<code class=\"codespan\">width</code>时有效</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 截断，并在末尾显示<code class=\"codespan\">ellipsis</code>配置的文本，默认为<code class=\"codespan\">...</code></li>\n<li><code class=\"codespan\">&#39;break&#39;</code> 换行</li>\n<li><code class=\"codespan\">&#39;breakAll&#39;</code> 换行，跟<code class=\"codespan\">&#39;break&#39;</code>不同的是，在英语等拉丁文中，<code class=\"codespan\">&#39;breakAll&#39;</code>还会强制单词内换行</li>\n</ul>\n", "uiControl": {"type": "enum", "options": "truncate,break,breakAll"}}, "axisPointer.label.ellipsis": {"desc": "<p>在<code class=\"codespan\">overflow</code>配置为<code class=\"codespan\">&#39;truncate&#39;</code>的时候，可以通过该属性配置末尾显示的文本。</p>\n"}, "axisPointer.label.lineOverflow": {"desc": "<p>文本超出高度部分是否截断，配置<code class=\"codespan\">height</code>时有效。</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 在文本行数超出高度部分截断。</li>\n</ul>\n"}, "axisPointer.label.padding": {"desc": "\n\n<p>axisPointer内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距。</p>\n<p>使用示例：</p>\n<pre><code class=\"lang-js\">// 设置内边距为 5\npadding: 5\n// 设置上下的内边距为 5，左右的内边距为 10\npadding: [5, 10]\n// 分别设置四个方向的内边距\npadding: [\n    5,  // 上\n    10, // 右\n    5,  // 下\n    10, // 左\n]\n</code></pre>\n", "uiControl": {"type": "vector", "min": "0", "dims": "T,R,B,L"}}, "axisPointer.label.backgroundColor": {"desc": "<p>文本标签的背景颜色，默认是和 <a href=\"#xAxis.axisLine.lineStyle.color\">axis.axisLine.lineStyle.color</a> 相同。</p>\n"}, "axisPointer.label.borderColor": {"desc": "<p>文本标签的边框颜色。</p>\n"}, "axisPointer.label.borderWidth": {"desc": "<p>文本标签的边框宽度。</p>\n"}, "axisPointer.label.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "3", "min": "0", "step": "0.5"}}, "axisPointer.label.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": "#aaa"}}, "axisPointer.label.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisPointer.label.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisPointer.lineStyle": {"desc": "<p><a href=\"#tooltip.axisPointer.type\">axisPointer.type</a> 为 <code class=\"codespan\">&#39;line&#39;</code> 时有效。</p>\n"}, "axisPointer.lineStyle.color": {"desc": "\n\n<p>线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "axisPointer.lineStyle.width": {"desc": "\n\n<p>线宽。</p>\n", "uiControl": {"type": "number", "value": "1", "min": "0", "step": "0.5"}}, "axisPointer.lineStyle.type": {"desc": "\n\n<p>线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "axisPointer.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "axisPointer.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "axisPointer.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisPointer.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisPointer.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "axisPointer.shadowStyle": {"desc": "<p><a href=\"#tooltip.axisPointer.type\">axisPointer.type</a> 为 <code class=\"codespan\">&#39;shadow&#39;</code> 时有效。</p>\n"}, "axisPointer.shadowStyle.color": {"desc": "\n\n<p>填充的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "axisPointer.shadowStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "axisPointer.shadowStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "axisPointer.shadowStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisPointer.shadowStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "axisPointer.shadowStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "axisPointer.triggerTooltip": {"desc": "\n\n<p>是否触发 tooltip。如果不想触发 tooltip 可以关掉。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "axisPointer.value": {"desc": "<p>当前的 value。在使用 <a href=\"xAxisPointer.handle\" target=\"_blank\">axisPointer.handle</a> 时，可以设置此值进行初始值设定，从而决定 axisPointer 的初始位置。</p>\n"}, "axisPointer.status": {"desc": "\n\n<p>当前的状态，可取值为 <code class=\"codespan\">&#39;show&#39;</code> 和 <code class=\"codespan\">&#39;hide&#39;</code>。</p>\n", "uiControl": {"type": "enum", "options": "show,hide"}}, "axisPointer.handle": {"desc": "<p>拖拽手柄，适用于触屏的环境。参见 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=line-tooltip-touch&amp;edit=1&amp;reset=1\" target=\"_blank\">例子</a>。</p>\n"}, "axisPointer.handle.show": {"desc": "\n\n<p>当 show 设为 <code class=\"codespan\">true</code> 时开启，这时显示手柄，并且 axisPointer 会一直显示。</p>\n", "uiControl": {"type": "boolean"}}, "axisPointer.handle.icon": {"desc": "\n\n<p>手柄的图标。</p>\n<p>可以通过 <code class=\"codespan\">&#39;image://url&#39;</code> 设置为图片，其中 URL 为图片的链接，或者 <code class=\"codespan\">dataURI</code>。</p>\n<p>URL 为图片链接例如：</p>\n<pre><code>&#39;image://http://xxx.xxx.xxx/a/b.png&#39;\n</code></pre><p>URL 为 <code class=\"codespan\">dataURI</code> 例如：</p>\n<pre><code>&#39;image://data:image/gif;base64,R0lGODlhEAAQAMQAAORHHOVSKudfOulrSOp3WOyDZu6QdvCchPGolfO0o/XBs/fNwfjZ0frl3/zy7////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAkAABAALAAAAAAQABAAAAVVICSOZGlCQAosJ6mu7fiyZeKqNKToQGDsM8hBADgUXoGAiqhSvp5QAnQKGIgUhwFUYLCVDFCrKUE1lBavAViFIDlTImbKC5Gm2hB0SlBCBMQiB0UjIQA7&#39;\n</code></pre><p>可以通过 <code class=\"codespan\">&#39;path://&#39;</code> 将图标设置为任意的矢量路径。这种方式相比于使用图片的方式，不用担心因为缩放而产生锯齿或模糊，而且可以设置为任意颜色。路径图形会自适应调整为合适的大小。路径的格式参见 <a href=\"http://www.w3.org/TR/SVG/paths.html#PathData\" target=\"_blank\">SVG PathData</a>。可以从 Adobe Illustrator 等工具编辑导出。</p>\n<p>例如：</p>\n<pre><code>&#39;path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z&#39;\n</code></pre><p>参见 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=doc-example/axisPointer-handle-image&amp;edit=1&amp;reset=1\" target=\"_blank\">使用图片的例子</a></p>\n", "uiControl": {"type": "icon", "clean": "true"}}, "axisPointer.handle.size": {"desc": "\n\n<p>手柄的尺寸，可以设置单值，如 <code class=\"codespan\">45</code>，也可以设置为数组：<code class=\"codespan\">[width, height]</code>。</p>\n", "uiControl": {"type": "vector", "default": "45,45", "min": "0", "step": "0.5", "dims": "width,height"}}, "axisPointer.handle.margin": {"desc": "\n\n<p>手柄与轴的距离。注意，这是手柄中心点和轴的距离。</p>\n", "uiControl": {"type": "number", "default": "50", "min": "0", "step": "0.5"}}, "axisPointer.handle.color": {"desc": "\n\n<p>手柄颜色。</p>\n", "uiControl": {"type": "color"}}, "axisPointer.handle.throttle": {"desc": "\n\n<p>手柄拖拽时触发视图更新周期，单位毫秒，调大这个数值可以改善性能，但是降低体验。</p>\n", "uiControl": {"type": "number", "default": "40", "min": "0", "step": "10"}}, "axisPointer.handle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "3", "min": "0", "step": "0.5"}}, "axisPointer.handle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": "#aaa"}}, "axisPointer.handle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "2", "step": "0.5"}}, "axisPointer.handle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "zlevel": {"desc": "<p>X 轴所有图形的 zlevel 值。</p>\n<p><code class=\"codespan\">zlevel</code>用于 Canvas 分层，不同<code class=\"codespan\">zlevel</code>值的图形会放置在不同的 Canvas 中，Canvas 分层是一种常见的优化手段。我们可以把一些图形变化频繁（例如有动画）的组件设置成一个单独的<code class=\"codespan\">zlevel</code>。需要注意的是过多的 Canvas 会引起内存开销的增大，在手机端上需要谨慎使用以防崩溃。</p>\n<p><code class=\"codespan\">zlevel</code> 大的 Canvas 会放在 <code class=\"codespan\">zlevel</code> 小的 Canvas 的上面。</p>\n"}, "z": {"desc": "<p>X 轴组件的所有图形的<code class=\"codespan\">z</code>值。控制图形的前后顺序。<code class=\"codespan\">z</code>值小的图形会被<code class=\"codespan\">z</code>值大的图形覆盖。</p>\n<p><code class=\"codespan\">z</code>相比<code class=\"codespan\">zlevel</code>优先级更低，而且不会创建新的 Canvas。</p>\n"}}