import React from 'react';
import {Renderer} from 'amis';
import {RendererProps} from 'amis-core';
import './appicon.scss';

// 定义图标数据类型
interface IconData {
  icon: string;
  iconColor: string;
  iconBg: string;
  [key: string]: any; // 允许其他属性
}

export interface AppIconProps extends RendererProps {
  label?: string;
  name?: string;
  value?: string | IconData;
  static?: boolean;
  width?: number;
  height?: number;
  borderRadius?: number | string;
  frameImage?: string; // 背景图片地址
}

@Renderer({
  type: 'appicon',
  name: 'appicon'
})
export class AppIconRenderer extends React.PureComponent<AppIconProps> {
  private componentId = Math.random().toString(36).substring(2, 11);

  constructor(props: AppIconProps) {
    super(props);
    console.log(`AppIcon 组件创建，ID: ${this.componentId}`);
  }
  // 辅助函数：从HTML字符串中提取SVG并转换为Data URL，优化兼容性
  extractSvgFromHtml(htmlString: string): string | null {
    try {
      if (!htmlString || typeof htmlString !== 'string') return null;

      // 使用正则表达式提取SVG内容
      const svgMatch = htmlString.match(/<svg[^>]*>.*?<\/svg>/i);
      if (!svgMatch) return null;

      let svgContent = svgMatch[0];

      // 从HTML span中提取背景色信息
      let backgroundColor = '#f5f5f5'; // 默认背景色
      const spanMatch = htmlString.match(/background:\s*([^;]+)/);
      if (spanMatch) {
        backgroundColor = spanMatch[1].trim();
      }

      // 替换currentColor为具体颜色，使用对比色确保可见性
      let iconColor = '#ffffff'; // 默认白色
      if (backgroundColor !== '#f5f5f5') {
        // 如果有背景色，使用白色作为图标颜色确保对比度
        iconColor = '#ffffff';
      } else {
        // 如果没有背景色，使用深色
        iconColor = '#666666';
      }
      svgContent = svgContent.replace(/currentColor/g, iconColor);

      console.log(`[${this.componentId}] 颜色处理:`, {
        backgroundColor: backgroundColor,
        iconColor: iconColor,
        originalSvg: svgContent.substring(0, 100) + '...'
      });

      // 确保SVG有正确的尺寸，使用更大的尺寸以适应背景显示
      if (!svgContent.includes('width=') || !svgContent.includes('height=')) {
        svgContent = svgContent.replace('<svg', '<svg width="100" height="100"');
      } else {
        // 如果已有尺寸，确保足够大
        svgContent = svgContent.replace(/width="[^"]*"/, 'width="100"');
        svgContent = svgContent.replace(/height="[^"]*"/, 'height="100"');
      }

      // 为SVG添加背景，创建一个带背景的SVG
      const innerSvgContent = svgContent.replace(/<svg[^>]*>|<\/svg>/g, '');
      const wrappedSvg = `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <rect width="100" height="100" fill="${backgroundColor}" rx="8"/>
        <g transform="translate(50,50) translate(-8,-8) scale(2)">
          ${innerSvgContent}
        </g>
      </svg>`;

      console.log(`[${this.componentId}] 生成的包装SVG:`, wrappedSvg);

      // 转换为Data URL
      const encodedSvg = encodeURIComponent(wrappedSvg);
      return `data:image/svg+xml,${encodedSvg}`;
    } catch (error) {
      console.error('提取SVG失败:', error);
      return null;
    }
  }

  // 辅助函数：处理frameImage，支持变量替换和SVG提取
  processFrameImage(frameImage: string, data: any): string | null {
    try {
      if (!frameImage) return null;

      // 处理变量替换
      let processedImage = frameImage;
      if (frameImage.includes('${') && data) {
        // 简单的变量替换
        processedImage = frameImage.replace(/\$\{(\w+)\}/g, (match, varName) => {
          return data[varName] || match;
        });
      }

      console.log(`[${this.componentId}] 处理后的frameImage:`, processedImage);

      // 如果处理后的内容包含HTML/SVG，尝试提取SVG
      if (processedImage.includes('<svg') || processedImage.includes('<span')) {
        const svgDataUrl = this.extractSvgFromHtml(processedImage);
        if (svgDataUrl) {
          console.log(`[${this.componentId}] 从HTML中提取的SVG Data URL:`, svgDataUrl);
          return svgDataUrl;
        }
      }

      // 如果是普通的图片URL，直接返回
      if (processedImage.startsWith('http') || processedImage.startsWith('data:image/') || processedImage.startsWith('/')) {
        return processedImage;
      }

      console.warn(`[${this.componentId}] 无法处理的frameImage格式:`, processedImage);
      return null;
    } catch (error) {
      console.error(`[${this.componentId}] 处理frameImage时出错:`, error);
      return null;
    }
  }





  renderIcon() {
    try {
      const {value, data, name, width = 80, height = 80, borderRadius = '8px', themeCss, classnames: cx, frameImage} = this.props as any;

      // 获取当前值，支持多种数据源
      let currentValue = value;

      // 如果还没有值，尝试从 data[name] 获取（用于展示模式）
      if (!currentValue && data && name) {
        currentValue = data[name];
      }

      // 添加详细的调试信息
      console.log(`[${this.componentId}] renderIcon 调试信息:`, {
        frameImage: frameImage,
        frameImageType: typeof frameImage,
        propsValue: value,
        dataValue: data ? data[name] : undefined,
        currentValue: currentValue,
        currentValueType: typeof currentValue,
        name: name,
        themeCss: themeCss,
        data: data,
        hasFrameImage: !!frameImage
      });

      // 如果frameImage为空，尝试直接测试
      if (!frameImage) {
        console.warn(`[${this.componentId}] frameImage 为空，请检查配置`);
      }

      // 解析当前值
      let iconData: IconData = {icon: '', iconColor: '', iconBg: ''};
      if (currentValue) {
        if (typeof currentValue === 'string') {
          try {
            const parsedValue = JSON.parse(currentValue);
            iconData = {
              icon: parsedValue.icon || '',
              iconColor: parsedValue.iconColor || '',
              iconBg: parsedValue.iconBg || ''
            };
            console.log(`[${this.componentId}] 从字符串解析的图标数据:`, iconData);
          } catch (e) {
            console.log(`[${this.componentId}] JSON 解析失败:`, e);
            // 解析失败，可能是直接的图标字符串
            iconData = { icon: currentValue, iconColor: '', iconBg: '' };
          }
        } else if (typeof currentValue === 'object') {
          iconData = {
            icon: (currentValue as IconData).icon || '',
            iconColor: (currentValue as IconData).iconColor || '',
            iconBg: (currentValue as IconData).iconBg || ''
          };
          console.log(`[${this.componentId}] 从对象获取的图标数据:`, iconData);
        }
      }

      console.log(`[${this.componentId}] 最终图标数据:`, iconData);

      // 基础样式 - 移除cursor: 'pointer'，因为不再可点击
      let style: any = {
        width: width,
        height: height,
        borderRadius: borderRadius,
        background: iconData.iconBg || '#F5F7FA',
        color: iconData.iconColor || '#666',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '24px',
        border: '1px solid #ddd'
      };

      // 处理 frameImage，支持变量替换和SVG提取
      let processedFrameImage = this.processFrameImage(frameImage, data);

      // 临时测试：如果没有frameImage，使用测试图片
      if (!processedFrameImage && frameImage === 'TEST') {
        processedFrameImage = 'https://via.placeholder.com/100x100/b8e986/ffffff?text=TEST';
        console.log(`[${this.componentId}] 使用测试图片:`, processedFrameImage);
      }

      if (processedFrameImage) {
        console.log(`[${this.componentId}] 应用背景图片:`, processedFrameImage);

        // 根据图片类型优化显示样式
        const isSvgDataUrl = processedFrameImage.startsWith('data:image/svg+xml');

        if (isSvgDataUrl) {
          // SVG背景优化显示 - 改为使用cover模式
          style.backgroundImage = `url(${processedFrameImage})`;
          style.backgroundSize = 'cover'; // 改为cover确保填满容器
          style.backgroundPosition = 'center';
          style.backgroundRepeat = 'no-repeat';
          style.background = `url(${processedFrameImage}) center/cover no-repeat`;
          console.log(`[${this.componentId}] SVG背景样式:`, {
            backgroundImage: style.backgroundImage,
            backgroundSize: style.backgroundSize,
            backgroundPosition: style.backgroundPosition,
            backgroundRepeat: style.backgroundRepeat,
            svgDataUrlPreview: processedFrameImage.substring(0, 200) + '...'
          });
        } else {
          // 普通图片背景
          style.backgroundImage = `url(${processedFrameImage})`;
          style.backgroundSize = 'cover';
          style.backgroundPosition = 'center';
          style.backgroundRepeat = 'no-repeat';
          style.background = `url(${processedFrameImage}) center/cover no-repeat`;
          console.log(`[${this.componentId}] 图片背景样式:`, {
            backgroundImage: style.backgroundImage,
            backgroundSize: style.backgroundSize,
            background: style.background
          });
        }

        // 如果有背景图，调整边框样式
        style.border = '1px solid #ddd';

        // 强制设置背景图片，确保显示
        style.backgroundImage = `url("${processedFrameImage}")`;
        style.backgroundSize = isSvgDataUrl ? 'cover' : 'cover';
        style.backgroundPosition = 'center';
        style.backgroundRepeat = 'no-repeat';

        // 添加一个测试背景色来验证容器是否可见
        if (!style.backgroundImage || style.backgroundImage === 'none') {
          style.backgroundColor = '#ff0000'; // 红色测试背景
          console.log(`[${this.componentId}] 应用测试红色背景`);
        }

        console.log(`[${this.componentId}] 最终样式:`, {
          width: style.width,
          height: style.height,
          backgroundImage: style.backgroundImage ? style.backgroundImage.substring(0, 50) + '...' : 'none',
          backgroundSize: style.backgroundSize,
          backgroundColor: style.backgroundColor
        });

        // 添加图片加载错误处理（只对非SVG Data URL进行检测）
        if (!isSvgDataUrl) {
          const img = new Image();
          img.onload = () => {
            console.log(`[${this.componentId}] 背景图片加载成功:`, processedFrameImage);
          };
          img.onerror = () => {
            console.error(`[${this.componentId}] 背景图片加载失败:`, processedFrameImage);
          };
          img.src = processedFrameImage;
        } else {
          console.log(`[${this.componentId}] SVG背景图片已应用:`, processedFrameImage.substring(0, 100) + '...');
        }
      }

      // 应用样式配置
      if (themeCss && themeCss.addBtnControlClassName) {
        const uploadAreaStyles = themeCss.addBtnControlClassName;

        // 应用各种状态的样式
        if (uploadAreaStyles['background:default']) {
          style.background = uploadAreaStyles['background:default'];
        }

        if (uploadAreaStyles['border:default']) {
          const borderConfig = uploadAreaStyles['border:default'];
          if (borderConfig['top-border-width']) {
            style.borderWidth = borderConfig['top-border-width'];
          }
          if (borderConfig['top-border-style']) {
            style.borderStyle = borderConfig['top-border-style'];
          }
          if (borderConfig['top-border-color']) {
            style.borderColor = borderConfig['top-border-color'];
          }
        }

        if (uploadAreaStyles['radius:default']) {
          const radiusConfig = uploadAreaStyles['radius:default'];
          if (radiusConfig['top-left-border-radius']) {
            style.borderRadius = radiusConfig['top-left-border-radius'];
          }
        }

        if (uploadAreaStyles['padding-and-margin:default']) {
          const paddingConfig = uploadAreaStyles['padding-and-margin:default'];
          if (paddingConfig.paddingTop) style.paddingTop = paddingConfig.paddingTop;
          if (paddingConfig.paddingRight) style.paddingRight = paddingConfig.paddingRight;
          if (paddingConfig.paddingBottom) style.paddingBottom = paddingConfig.paddingBottom;
          if (paddingConfig.paddingLeft) style.paddingLeft = paddingConfig.paddingLeft;
          if (paddingConfig.marginTop) style.marginTop = paddingConfig.marginTop;
          if (paddingConfig.marginRight) style.marginRight = paddingConfig.marginRight;
          if (paddingConfig.marginBottom) style.marginBottom = paddingConfig.marginBottom;
          if (paddingConfig.marginLeft) style.marginLeft = paddingConfig.marginLeft;
        }
      }

      // 生成 CSS 类名
      let className = 'appicon';
      if (cx && themeCss && themeCss.addBtnControlClassName) {
        className = cx('appicon', themeCss.addBtnControlClassName);
      }

      // 如果没有图标，显示占位符
      if (!iconData.icon) {
        return (
          <div
            className={className}
            style={style}
          >
            <span style={{color: '#999', fontSize: '12px'}}>无图标</span>
          </div>
        );
      }

      return (
        <div
          className={className}
          style={style}
        >
          <div
            dangerouslySetInnerHTML={{ __html: iconData.icon }}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              // 如果有背景图，添加一些样式确保图标清晰可见
              ...(processedFrameImage ? {
                textShadow: '0 0 3px rgba(255,255,255,0.8), 0 0 6px rgba(255,255,255,0.6)',
                filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.3))'
              } : {})
            }}
          />
        </div>
      );
    } catch (error) {
      console.error('renderIcon 错误:', error);
      return (
        <div style={{
          width: 80,
          height: 80,
          background: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid red'
        }}>
          错误
        </div>
      );
    }
  }





  render() {
    try {
      const {render, static: isStatic, name, value, label, themeCss, classnames: cx, data} = this.props as any;

      console.log(`[${this.componentId}] AppIcon render - 纯展示组件`);

      if (isStatic) {
        // 静态模式：使用amis的static组件渲染，保持与其他组件一致
        if (render) {
          // 获取当前值
          let actualValue = value;
          if (!actualValue && data && name) {
            actualValue = data[name];
          }

          // 解析图标数据用于显示
          let displayContent = '';
          if (actualValue) {
            try {
              if (typeof actualValue === 'string') {
                const parsedValue = JSON.parse(actualValue);
                if (parsedValue.icon) {
                  // 返回图标的HTML内容作为tpl
                  displayContent = parsedValue.icon;
                }
              } else if (typeof actualValue === 'object' && actualValue.icon) {
                displayContent = actualValue.icon;
              }
            } catch (e) {
              console.warn('解析图标数据失败:', e);
            }
          }

          // 如果没有图标内容，显示占位符
          if (!displayContent) {
            displayContent = '<span style="color: #999; font-size: 12px;">无图标</span>';
          }

          return render('static', {
            type: 'static',
            label,
            name,
            tpl: displayContent
          });
        }

        // 备用渲染方法：直接使用div（与其他组件保持一致）
        let actualValue = value;
        if (!actualValue && data && name) {
          actualValue = data[name];
        }

        let displayContent = '无图标';
        if (actualValue) {
          try {
            if (typeof actualValue === 'string') {
              const parsedValue = JSON.parse(actualValue);
              if (parsedValue.icon) {
                displayContent = parsedValue.icon;
              }
            } else if (typeof actualValue === 'object' && actualValue.icon) {
              displayContent = actualValue.icon;
            }
          } catch (e) {
            console.warn('解析图标数据失败:', e);
          }
        }

        return (
          <div className="static-value">
            <div className="static-label">{label || name}</div>
            <div
              className="static-content"
              dangerouslySetInnerHTML={{ __html: displayContent }}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-start',
                minHeight: '20px'
              }}
            />
          </div>
        );
      }

      // 处理整个组件的样式 (labelClassName)
      let wrapperStyle: any = {};
      let wrapperClassName = 'appicon-wrapper';

      if (themeCss && themeCss.labelClassName) {
        const wrapperConfig = themeCss.labelClassName;

        // 应用字体样式
        if (wrapperConfig['font:default']) {
          const fontConfig = wrapperConfig['font:default'];
          if (fontConfig.fontSize) wrapperStyle.fontSize = fontConfig.fontSize;
          if (fontConfig.fontWeight) wrapperStyle.fontWeight = fontConfig.fontWeight;
          if (fontConfig.lineHeight) wrapperStyle.lineHeight = fontConfig.lineHeight;
          if (fontConfig['font-family']) wrapperStyle.fontFamily = fontConfig['font-family'];
        }

        // 应用其他样式
        if (wrapperConfig['background:default']) {
          wrapperStyle.background = wrapperConfig['background:default'];
        }

        if (wrapperConfig['border:default']) {
          const borderConfig = wrapperConfig['border:default'];
          if (borderConfig['top-border-width']) {
            wrapperStyle.borderWidth = borderConfig['top-border-width'];
          }
          if (borderConfig['top-border-style']) {
            wrapperStyle.borderStyle = borderConfig['top-border-style'];
          }
          if (borderConfig['top-border-color']) {
            wrapperStyle.borderColor = borderConfig['top-border-color'];
          }
        }

        if (wrapperConfig['padding-and-margin:default']) {
          const paddingConfig = wrapperConfig['padding-and-margin:default'];
          if (paddingConfig.paddingTop) wrapperStyle.paddingTop = paddingConfig.paddingTop;
          if (paddingConfig.paddingRight) wrapperStyle.paddingRight = paddingConfig.paddingRight;
          if (paddingConfig.paddingBottom) wrapperStyle.paddingBottom = paddingConfig.paddingBottom;
          if (paddingConfig.paddingLeft) wrapperStyle.paddingLeft = paddingConfig.paddingLeft;
          if (paddingConfig.marginTop) wrapperStyle.marginTop = paddingConfig.marginTop;
          if (paddingConfig.marginRight) wrapperStyle.marginRight = paddingConfig.marginRight;
          if (paddingConfig.marginBottom) wrapperStyle.marginBottom = paddingConfig.marginBottom;
          if (paddingConfig.marginLeft) wrapperStyle.marginLeft = paddingConfig.marginLeft;
        }

        // 生成 CSS 类名
        if (cx) {
          wrapperClassName = cx('appicon-wrapper', themeCss.labelClassName);
        }
      }

      // 处理标题样式 (titleClassName)
      let titleStyle: any = {};
      let titleClassName = 'appicon-title';

      if (themeCss && themeCss.titleClassName) {
        const titleConfig = themeCss.titleClassName;

        // 应用字体样式
        if (titleConfig['font:default']) {
          const fontConfig = titleConfig['font:default'];
          if (fontConfig.fontSize) titleStyle.fontSize = fontConfig.fontSize;
          if (fontConfig.fontWeight) titleStyle.fontWeight = fontConfig.fontWeight;
          if (fontConfig.lineHeight) titleStyle.lineHeight = fontConfig.lineHeight;
          if (fontConfig['font-family']) titleStyle.fontFamily = fontConfig['font-family'];
        }

        // 应用其他样式
        if (titleConfig['background:default']) {
          titleStyle.background = titleConfig['background:default'];
        }

        if (titleConfig['border:default']) {
          const borderConfig = titleConfig['border:default'];
          if (borderConfig['top-border-width']) {
            titleStyle.borderWidth = borderConfig['top-border-width'];
          }
          if (borderConfig['top-border-style']) {
            titleStyle.borderStyle = borderConfig['top-border-style'];
          }
          if (borderConfig['top-border-color']) {
            titleStyle.borderColor = borderConfig['top-border-color'];
          }
        }

        // 生成 CSS 类名
        if (cx) {
          titleClassName = cx('appicon-title', themeCss.titleClassName);
        }
      }

      // 返回纯展示界面
      return (
        <div
          className={wrapperClassName}
          style={wrapperStyle}
        >
          <div className="appicon-display">
            {label && (
              <label
                className={titleClassName}
                style={titleStyle}
              >
                {label}
              </label>
            )}
            {this.renderIcon()}
          </div>
        </div>
      );
    } catch (error) {
      console.error('AppIcon render 错误:', error);
      return (
        <div className="appicon-wrapper">
          <div style={{color: 'red', padding: '10px', border: '1px solid red'}}>
            图标组件渲染错误，请检查控制台
          </div>
        </div>
      );
    }
  }


}
