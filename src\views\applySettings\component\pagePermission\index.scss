.auto-button{
    border-radius: 0.25rem; 
}
.projectMain {
  position: relative;
  width: calc(100% - 1.5rem);
  height: calc(100vh - 4.625rem);
  margin: 0.75rem;
  padding-top: 40px;
  overflow: auto;
  -webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;

  &-top_s {
    position: relative;
    //width: 67.5rem;
    width: 100%;
    height: 7.5rem;
    margin: auto;
    padding: 0 1.25rem;
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--borderColor);

    &-left {
      &-name {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.375rem;
        font-weight: bold;
      }

      &-describe {
        font-size: 0.875rem;
        color: #999999;
        margin-top: 0.375rem;
      }
    }
  }

  &-main_s {
    position: relative;
    //width: 67.5rem;
    width: 100%;
    margin: auto;
    padding: 0 1.25rem;

    .permission-tabs {
      display: flex;
      margin-bottom: 16px;
      border-bottom: 1px solid #e9e9e9;

      .cxd-Tabs-link {
        font-size: 14px;
        padding: 8px 16px;
        
        &.is-active {
          color: #2468f2;
        }
      }
    }

    .permission-content {
      min-height: 200px;
      padding: 1.25rem;
      background-color: var(--light-bg);
      border-radius: 0.25rem;
    }

    .itemList {
      margin-bottom: 1rem;
    }

    .permission-item {
      display: block;
      margin-bottom: 1rem;
      padding: 1rem;
      border: 1px solid #E8E9EB;
      border-radius: 0.25rem;
      background-color: #fff;

      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #E8E9EB;
      }

      &-title {
        font-size: 14px;
        font-weight: 500;
        color: #151B26;
      }

      &-remark {
        font-size: 12px;
        color: #84878C;
      }

      &-content {
        padding: 0.5rem 0;
      }

      .permission-row {
        display: flex;
        margin-bottom: 0.5rem;
      }

      .permission-label {
        width: 80px;
        font-size: 14px;
        color: #84878C;
      }

      .permission-value {
        font-size: 14px;
        color: #151B26;
      }

      &-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          min-width: 52px;
          height: 32px;
          padding: 6px 12px;
          font-size: 14px;
          line-height: 20px;
          color: #151B26;
          background: transparent;
          border: 1px solid #E8E9EB;
          border-radius: 2px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background-color: rgba(0, 0, 0, 0.04);
          }
        }
      }
    }
  }
}

.view-mode-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  // margin-bottom: 16px;
  
  .mode-btn {
    margin-right: 8px;
    padding: 4px 12px;
    border-radius: 4px;
    
    &.active {
      background-color: #2468f2;
      color: #fff;
    }
  }
}
