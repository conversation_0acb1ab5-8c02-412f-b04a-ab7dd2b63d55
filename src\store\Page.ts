import {types, getEnv} from 'mobx-state-tree';
export const PageStore = types
  .model('Page', {
    id: types.union(types.string, types.number),
    icon: '',
    path: '',
    label: '',
    schema: types.frozen({}),
    table_name:'',
    create_user:'',
    parent_id: types.union(types.string, types.number,types.undefined), //分组id
    type: types.union(types.string, types.number,types.undefined), //页面类型
  })
  .views(self => ({}))
  .actions(self => {
    function updateSchema(schema: any) {
      self.schema = schema;
    }

    return {
      updateSchema
    };
  });

export type IPageStore = typeof PageStore.Type;
