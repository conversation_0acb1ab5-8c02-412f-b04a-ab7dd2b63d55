import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class AppIconControlPlugin extends BasePlugin {
  static id = 'AppIconControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'appicon';
  $schema = '/schemas/AppIconControlSchema.json';

  // 组件基本信息
  name = '应用图标';
  panelTitle = '应用图标';
  icon = 'fa fa-picture-o';
  panelIcon = 'fa fa-picture-o';
  pluginIcon = 'icon-image-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '展示应用图标';
  docLink = '/amis/zh-CN/components/static';
  tags = ['展示'];

  // 组件默认配置
  scaffold = {
    type: 'appicon',
    label: '应用图标',
    name: 'appIcon',
    width: 80,
    height: 80,
    borderRadius: '8px',
    frameImage: '', // 背景图片
  };

  // 事件定义 - 展示组件不需要交互事件
  events = [];

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                getSchemaTpl('formItemName', {
                  required: false
                }),
                getSchemaTpl('label'),
                getSchemaTpl('textareaFormulaControl', {
                  name: 'frameImage',
                  label: '背景图片地址',
                  placeholder: '请输入图片URL地址',
                  description: '设置图标容器的背景图片，图标将显示在背景图片上方'
                }),
                // getSchemaTpl('switch', {
                //   name: 'crop',
                //   label: '裁剪功能',
                //   value: false,
                // }),
                // getSchemaTpl('switch', {
                //   name: 'clearable',
                //   label: '可清除',
                //   value: true,
                // })
              ]
            },
            getSchemaTpl('status', {
              isFormItem: true,
              readonly: true
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '图标设置',
              body: [
                getSchemaTpl('layout:width:v2', {
                  name: 'borderRadius',
                  label: '圆角设置',
                  value: '8px',
                  unitOptions: ['px', '%'],
                }),
                getSchemaTpl('style:widthHeight', {
                  widthSchema: {
                    label: '图标宽度',
                    value: 80
                  },
                  heightSchema: {
                    label: '图标高度',
                    value: 80
                  }
                }),
              ]
            },
            // getSchemaTpl('theme:form-label'),
            // getSchemaTpl('theme:form-description'),
            {
              title: '标签样式',
              body: [
                ...inputStateTpl(
                  'themeCss.labelClassName',
                  '--form-item-base'
                )
              ]
            },
            {
              title: '标题样式',
              body: [
                ...inputStateTpl(
                  'themeCss.titleClassName',
                  '--form-label-base'
                )
              ]
            },
            {
              title: '上传区域样式',
              body: [
                ...inputStateTpl(
                  'themeCss.addBtnControlClassName',
                  '--inputImage-base'
                ),
                // 图标选择
                {
                  type: 'icon-select',
                  name: 'themeCss.addBtnControlClassName.--inputImage-base-default-icon',
                  label: '图标选择',
                  description: '设置组件的图标',
                  returnSvg: true
                },
                // 图标颜色
                getSchemaTpl('theme:colorPicker', {
                  name: 'themeCss.addBtnControlClassName.icon-color:default',
                  label: '图标颜色'
                }),
                // 图标大小
                getSchemaTpl('layout:width:v2', {
                  name: 'themeCss.addBtnControlClassName.iconSize',
                  label: '图标大小',
                  value: '24px',
                  unitOptions: ['px', 'rem', 'em']
                })
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(AppIconControlPlugin);
