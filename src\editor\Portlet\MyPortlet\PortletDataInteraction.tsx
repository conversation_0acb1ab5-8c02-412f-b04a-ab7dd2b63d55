import React, {FC, useEffect, useRef} from 'react';
import './index.scss';
import {Button, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {DndProvider, useDrag, useDrop} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';
import {title} from 'process';
import {Tpl} from 'amis/lib/renderers/Tpl';

// 定义拖拽项类型
const ItemTypes = {
  PORTLET_TAB: 'portletTab'
};

interface PortletTab {
  title: string;
  icon?: string;
  iconPosition?: 'left' | 'right';
  body?: any;
  reload?: boolean;
  id?: number;
  mountOnEnter?: boolean;
  unmountOnExit?: boolean;
}

interface PortletDataInteractionProps {
  appId: any;
  context: any;
  tabs: PortletTab[];
  handleTabs: (tabs: PortletTab[]) => void;
}

// 分组组件
const PortletTabRow: FC<any> = ({item, index, onEdit, onDelete, onMove}) => {
  // 设置拖拽源
  const [{isDragging}, drag] = useDrag({
    type: ItemTypes.PORTLET_TAB,
    item: () => ({
      index
    }),
    collect: monitor => ({
      isDragging: monitor.isDragging()
    })
  });

  // 设置放置目标
  const [{isOver, canDrop}, drop] = useDrop({
    accept: ItemTypes.PORTLET_TAB,
    drop: (draggedItem: any, monitor) => {
      // 只处理直接放置在此项上的操作，忽略冒泡的事件
      if (monitor.didDrop()) {
        return;
      }

      if (draggedItem.index !== index) {
        onMove(draggedItem.index, index);
      }
    },
    canDrop: (draggedItem: any) => {
      // 不能将项目拖放到自己上
      return draggedItem.index !== index;
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  // 合并拖放引用
  const ref = useRef(null);
  drag(drop(ref));

  // 应用样式
  const opacity = isDragging ? 0.4 : 1;
  const backgroundColor =
    isOver && canDrop ? 'rgba(0, 255, 0, 0.1)' : 'transparent';

  return (
    <div
      ref={ref}
      className="portlet-tab-row"
      style={{opacity, backgroundColor}}
      key={`portlet-tab-${index}`}
    >
      <div className="portlet-tab-info">
        <i
          className="fa fa-bars"
          style={{cursor: 'move', marginRight: '8px'}}
        ></i>
        <span className="item-title">{`${item.title || '未命名'}`}</span>
      </div>
      <div className="portlet-tab-actions">
        <Button level="link" size="sm" onClick={() => onEdit(index)}>
          <i className="fa fa-edit"></i>
        </Button>
        <Button level="link" size="sm" onClick={() => onDelete(index)}>
          <i className="fa fa-trash"></i>
        </Button>
      </div>
    </div>
  );
};

const PortletDataInteraction: FC<PortletDataInteractionProps> = (
  props: PortletDataInteractionProps
) => {
  const [refreshListKey, setRefreshListKey] = React.useState(0);
  const [portletTabs, setPortletTabs] = React.useState<PortletTab[]>([]);
  const [currentEditIndex, setCurrentEditIndex] = React.useState<number>(-1);
  const [showEditDialog, setShowEditDialog] = React.useState<boolean>(false);
  const [isAddMode, setIsAddMode] = React.useState<boolean>(false);
  // 批量编辑状态
  const [showBatchEditDialog, setShowBatchEditDialog] =
    React.useState<boolean>(false);
  const [batchItems, setBatchItems] = React.useState<PortletTab[]>([]);
  const isMounted = useRef(true);
  const isInitialized = useRef(false);

  // 添加状态
  const [showConfirmDialog, setShowConfirmDialog] = React.useState(false);
  const [deleteIndex, setDeleteIndex] = React.useState(-1);
  const [deleteItemId, setDeleteItemId] = React.useState<any>(null);
  const [deleteRow, setDeleteRow] = React.useState<any>(null);

  // 组件挂载状态跟踪
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
      // 清理对话框状态
      setShowEditDialog(false);
      setCurrentEditIndex(-1);
      setIsAddMode(false);
    };
  }, []);

  // 初始化时从props中读取已保存的数据
  React.useEffect(() => {
    if (!isMounted.current) return;

    try {
      // 防止重复初始化，但允许数据更新
      if (props.tabs && props.tabs.length) {
        console.log('PortletDataInteraction接收到新的tabs数据:', props.tabs);
        // 深拷贝选项，避免直接引用
        const safeTabs = JSON.parse(JSON.stringify(props.tabs));
        setPortletTabs(safeTabs);

        // 如果是首次初始化，设置标志
        if (!isInitialized.current) {
          isInitialized.current = true;
        }
        return;
      }

      if (!isInitialized.current) {
        // 检查是否有保存的静态选项数据
        const savedTabs = props.context?.schema?.__savedStaticTabs;
        if (savedTabs && savedTabs.length) {
          console.log('PortletDataInteraction从schema中恢复数据:', savedTabs);
          const safeTabs = JSON.parse(JSON.stringify(savedTabs));
          setPortletTabs(safeTabs);
          // 更新到主数据中，使用setTimeout避免状态冲突
          setTimeout(() => {
            if (isMounted.current) {
              props.handleTabs(safeTabs);
            }
          }, 0);
          isInitialized.current = true;
        } else {
          // 设置默认分组
          // const defaultTabs = [
          //   {
          //     title: '分组1',
          //     groupId: '1',
          //     body: {
          //       type: 'tpl',
          //       tpl: '暂无分组内容'
          //     }
          //   }
          // ];
          setPortletTabs([]);
          // 更新到主数据中，使用setTimeout避免状态冲突
          setTimeout(() => {
            if (isMounted.current) {
              props.handleTabs([]);
            }
          }, 0);
          isInitialized.current = true;
        }
      }
    } catch (error) {
      console.error('初始化分组数据失败:', error);
      // 设置默认分组作为备用
      setPortletTabs([]);
    }
  }, [props.tabs]);

  // 添加分组
  const handleAddItem = () => {
    if (!isMounted.current) return;

    try {
      // 设置为添加模式
      setIsAddMode(true);

      // 临时设置编辑索引为数组长度，表示添加到末尾
      setCurrentEditIndex(portletTabs.length);

      // 使用setTimeout延迟显示对话框，避免状态树更新冲突
      setTimeout(() => {
        if (isMounted.current) {
          setShowEditDialog(true);
        }
      }, 50);
    } catch (error) {
      console.error('打开添加对话框失败:', error);
      toast.error('打开添加对话框失败');
    }
  };

  // 确认删除
  const handleConfirmDelete = () => {
    if (deleteIndex < 0&&!deleteItemId) return;

    if (deleteItemId) {
      const newItems = batchItems.filter(item => item.id !== deleteRow.id);
      setBatchItems(newItems);
      setDeleteItemId(null);
      setDeleteRow(null);
      setRefreshListKey(prev => prev + 1);
    }else{
      const newTabs = portletTabs.filter((_, i) => i !== deleteIndex);

      props.handleTabs(JSON.parse(JSON.stringify(newTabs)));
      props.context.schema.tabs = newTabs;
      // 先更新本地状态
      setPortletTabs(newTabs);
      setDeleteIndex(-1);
    }

    setShowConfirmDialog(false);
    toast.success('删除成功');
  };

  // 删除分组
  const handleDeleteItem = (index: number) => {
    if (!isMounted.current) return;
    setDeleteIndex(index);
    setShowConfirmDialog(true);
  };

  // 编辑分组
  const handleEditItem = (index: number) => {
    if (!isMounted.current) return;

    try {
      // 关闭添加模式
      setIsAddMode(false);

      // 先设置索引
      setCurrentEditIndex(index);

      // 使用setTimeout延迟显示对话框，避免状态树更新冲突
      setTimeout(() => {
        if (isMounted.current) {
          setShowEditDialog(true);
        }
      }, 50);
    } catch (error) {
      console.error('打开编辑对话框失败:', error);
      toast.error('打开编辑对话框失败');
    }
  };

  // 保存编辑
  const handleSaveEdit = (values: any) => {
    if (!isMounted.current) return;

    try {
      // 确保图标使用正确的fontawesome格式
      if (values.icon && typeof values.icon === 'string') {
        let iconStr = values.icon.trim();
        // 步骤 1: 清理字符串，移除可能存在的外层单引号或双引号
        if (
          (iconStr.startsWith("'") && iconStr.endsWith("'")) ||
          (iconStr.startsWith('"') && iconStr.endsWith('"'))
        ) {
          iconStr = iconStr.substring(1, iconStr.length - 1).trim(); // 移除引号并再次 trim
        }

        console.log('清理后的 iconStr:', iconStr);
        console.log('检测到 SVG 字符串:', iconStr);
        values.icon = iconStr;
      }

      // 处理JSON格式的body数据
      let bodyContent = values.body;
      if (typeof bodyContent === 'string') {
        try {
          bodyContent = JSON.parse(bodyContent);
        } catch (e) {
          console.error('解析标签内容JSON失败:', e);
          toast.error('标签内容格式不正确，请检查JSON格式');
          return;
        }
      }

      let newTabs = [...portletTabs];

      if (isAddMode) {
        // 添加模式：创建新分组并添加
        const newTab = {
          title: values.title || `分组${portletTabs.length + 1}`,
          id: values.id, // 添加groupId属性，使用title作为默认值
          icon: values.icon || '',
          iconPosition: values.iconPosition || 'left',
          reload: values.reload || false,
          mountOnEnter: values.mountOnEnter !== false,
          unmountOnExit: values.unmountOnExit || false,
          body: bodyContent || {
            type: 'tpl',
            tpl: `分组${portletTabs.length + 1}内容`
          }
        };

        newTabs.push(newTab);
      } else {
        // 编辑模式：更新现有分组
        if (currentEditIndex >= 0 && currentEditIndex < portletTabs.length) {
          newTabs = portletTabs.map((tab, index) => {
            if (index === currentEditIndex) {
              return {
                ...tab,
                ...values,
                // 如果修改了标题，同时更新groupId
                id: values.id,
                body: bodyContent || tab.body
              };
            }
            return tab;
          });
        }
      }

      // 先更新本地状态
      setPortletTabs(newTabs);

      // 关闭对话框
      setShowEditDialog(false);

      // 使用setTimeout延迟更新全局状态和重置索引，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          // 深拷贝，避免引用问题
          props.handleTabs(JSON.parse(JSON.stringify(newTabs)));
          setCurrentEditIndex(-1);
          setIsAddMode(false);
          props.context.schema.tabs = newTabs;
          toast.success(isAddMode ? '添加成功' : '编辑成功');
        }
      }, 50);
    } catch (error) {
      console.error('编辑保存失败:', error);
      toast.error('编辑保存失败');

      // 关闭对话框并重置索引
      setShowEditDialog(false);
      setTimeout(() => {
        if (isMounted.current) {
          setCurrentEditIndex(-1);
          setIsAddMode(false);
        }
      }, 50);
    }
  };

  // 打开批量编辑对话框
  const handleBatchEdit = () => {
    if (!isMounted.current) return;

    try {
      // 准备批量编辑数据，为每个项目添加序号
      const batchData = portletTabs.map((item, index) => ({
        ...item,
        id: index + 1 // 添加ID用于CRUD
        // 转换blank为下拉选择的值
        // linkType: item.blank ? 'external' : 'internal'
      }));
      console.log('准备批量编辑数据:', batchData);

      // 先设置批量编辑数据
      setBatchItems(batchData);

      // 使用setTimeout延迟显示对话框，延长时间以确保状态更新完成
      setTimeout(() => {
        if (isMounted.current) {
          // 再次检查确保batchItems已正确设置
          console.log('打开对话框时的数据状态:', batchData.length);
          setShowBatchEditDialog(true);
        }
      }, 100); // 增加延时确保状态更新
    } catch (error) {
      console.error('打开批量编辑对话框失败:', error);
      toast.error('打开批量编辑对话框失败');
    }
  };

  // 批量编辑对话框配置
  const getBatchEditDialogSchema = () => {
    let dataOption: any[] = [];
    console.log('准备获取批量编辑对话框配置', batchItems);
    try {
      // 复制一份数据，避免直接引用state可能导致的问题
      const safeItems = JSON.parse(JSON.stringify(batchItems || []));
      console.log('将要传递给CRUD的安全数据:', safeItems);

      return {
        type: 'dialog',
        title: '批量编辑分组',
        size: 'lg',
        closeOnEsc: true,
        closeOnOutside: false,
        className: 'carousel-batch-edit-dialog',
        data: {
          items: safeItems
          // 添加一个时间戳，强制AMIS每次打开对话框时重新渲染数据
          // _timestamp: Date.now()
        },
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'close'
          },
          {
            type: 'button',
            label: '确认',
            level: 'primary',
            actionType: 'submit',
            // 直接将确认按钮与表单关联
            target: 'batch-edit-form'
          }
        ],
        body: {
          type: 'form',
          id: 'batch-edit-form',
          submitOnChange: false,
          submitText: '',
          wrapWithPanel: false,
          onSubmit: (values: any) => {
            // 获取表单中的数据
            const formData = values.items || [];
            if (Array.isArray(formData) && formData.length > 0) {
              // 直接转换并保存数据
              const newPortletTabs = formData.map((item: any) => {
                // 处理图标格式，与handleSaveEdit中的逻辑保持一致
                let processedIcon = item.icon || '';
                if (processedIcon && typeof processedIcon === 'string') {
                  let iconStr = processedIcon.trim();
                  // 清理字符串，移除可能存在的外层单引号或双引号
                  if (
                    (iconStr.startsWith("'") && iconStr.endsWith("'")) ||
                    (iconStr.startsWith('"') && iconStr.endsWith('"'))
                  ) {
                    iconStr = iconStr.substring(1, iconStr.length - 1).trim();
                  }
                  processedIcon = iconStr;
                }

                return {
                  title: item.title || '',
                  id: item.id || '',
                  icon: processedIcon,
                  iconPosition: item.iconPosition || 'left',
                  reload: item.reload || false,
                  mountOnEnter: item.mountOnEnter !== false,
                  unmountOnExit: item.unmountOnExit || false,
                  body: item.body || {
                    type: 'tpl',
                    tpl: '暂无分组内容'
                  }
                };
              });

              // 先关闭对话框
              setShowBatchEditDialog(false);

              // 更新当前组件状态
              setPortletTabs(newPortletTabs);

              // 更新父组件状态
              props.handleTabs(JSON.parse(JSON.stringify(newPortletTabs)));
              props.context.schema.tabs = newPortletTabs;
              toast.success('批量更新成功');
            } else {
              // toast.error('没有获取到有效的表单数据');
               // 先关闭对话框
               setShowBatchEditDialog(false);

               // 更新当前组件状态
               setPortletTabs([]);
 
               // 更新父组件状态
               props.handleTabs(JSON.parse(JSON.stringify([])));
               props.context.schema.tabs = [];
               toast.success('批量更新成功');
            }

            return false;
          },
          body: [
            {
              type: 'crud',
              name: 'items',
              source: '${items}',
              syncLocation: false,
              api: null,
              id: 'batch-edit-crud',
              perPage: 100,
              headerToolbar: [],
              footerToolbar: [
                {
                  type: 'pagination',
                  align: 'right'
                }
              ],
              columns: [
                {
                  name: 'id',
                  label: '序号',
                  width: 60
                },
                {
                  name: 'title',
                  label: '分组标题',
                  type: 'static',
                  mode: 'inline'
                },
                {
                  type: 'tpl',
                  name: 'icon',
                  label: '图标',
                  returnSvg: true,
                  static: true
                },
                {
                  name: 'iconPosition',
                  label: '图标位置',
                  type: 'static',
                  tpl: '${iconPosition === "right" ? "右侧" : "左侧"}'
                },
                {
                  type: 'static',
                  name: 'reload',
                  label: '每次重新渲染',
                  tpl: '${reload? "是" : "否"}'
                },
                {
                  type: 'static',
                  name: 'mountOnEnter',
                  label: '点开加载',
                  tpl: '${mountOnEnter? "是" : "否"}'
                },
                {
                  type: 'static',
                  name: 'unmountOnExit',
                  label: '隐藏销毁',
                  tpl: '${unmountOnExit? "是" : "否"}'
                },
                {
                  type: 'operation',
                  label: '操作',
                  width: 180,
                  buttons: [
                    {
                      type: 'button',
                      label: '查看',
                      level: 'link',
                      actionType: 'dialog',
                      dialog: {
                        title: '查看分组',
                        body: {
                          type: 'form',
                          body: [
                            {
                              type: 'static',
                              name: 'title',
                              label: '分组标题'
                            },
                            {
                              type: 'static-tpl',
                              name: 'icon',
                              label: '图标'
                            },
                            {
                              type: 'static',
                              name: 'iconPosition',
                              label: '图标位置',
                              tpl: '${iconPosition === "right" ? "右侧" : "左侧"}'
                            },
                            {
                              type: 'static',
                              name: 'reload',
                              label: '每次重新渲染',
                              tpl: '${reload ? "是" : "否"}'
                            },
                            {
                              type: 'static',
                              name: 'mountOnEnter',
                              label: '点开加载',
                              tpl: '${mountOnEnter ? "是" : "否"}'
                            },
                            {
                              type: 'static',
                              name: 'unmountOnExit',
                              label: '隐藏销毁',
                              tpl: '${unmountOnExit ? "是" : "否"}'
                            }
                          ]
                        }
                      }
                    },
                    {
                      type: 'button',
                      label: '编辑',
                      level: 'link',
                      actionType: 'dialog',
                      dialog: {
                        title: '编辑分组',
                        size: 'md',
                        body: {
                          type: 'form',
                          wrapWithPanel: false,
                          submitText: '确定',
                          body: [
                            {
                              type: 'hidden',
                              name: 'id'
                            },
                            {
                              type: 'input-text',
                              name: 'title',
                              label: '分组标题',
                              required: true
                            },
                            {
                              type: 'icon-selector',
                              name: 'icon',
                              label: '图标',
                              placeholder: '请选择图标',
                              returnSvg: true
                            },
                            {
                              type: 'hidden',
                              name: 'icon'
                            },
                            {
                              type: 'select',
                              name: 'iconPosition',
                              label: '图标位置',
                              value: 'left',
                              options: [
                                {
                                  label: '左侧',
                                  value: 'left'
                                },
                                {
                                  label: '右侧',
                                  value: 'right'
                                }
                              ]
                            },
                            {
                              type: 'switch',
                              name: 'reload',
                              label: '每次重新渲染',
                              description: '是否每次重新渲染组件'
                            },
                            {
                              type: 'switch',
                              name: 'mountOnEnter',
                              label: '点开加载',
                              value: true,
                              description: '是否在显示时加载组件'
                            },
                            {
                              type: 'switch',
                              name: 'unmountOnExit',
                              label: '隐藏销毁',
                              description: '是否在隐藏时销毁组件'
                            }
                          ],
                          onSubmit: (values: any, action: any) => {
                            try {
                              console.log('编辑表单提交:', values);
                              // 本地更新数据
                              const updatedItems = batchItems.map(item => {
                                if (item.id === values.id) {
                                  return {...item, ...values};
                                }
                                return item;
                              });

                              // 更新状态
                              setBatchItems(updatedItems);

                              console.log('编辑成功后的数据:', updatedItems);
                              setRefreshListKey(prev => prev + 1);
                              // toast.success('编辑成功');

                              // 关闭对话框
                              return true;
                            } catch (error) {
                              console.error('编辑处理失败:', error);
                              toast.error('编辑失败');
                              return false;
                            }
                          }
                        }
                      }
                    },
                    {
                      type: 'button',
                      label: '删除',
                      level: 'link',
                      className: 'text-danger',
                      // confirmText: '确认要删除该轮播图吗？',
                      onClick: (e: any, props: any) => {
                        try {
                          console.log('删除操作，数据:', props);
                          const row = props?.data;

                          if (!row || row.id === undefined) {
                            toast.error('无法获取行数据');
                            return;
                          }

                          console.log('删除操作，数据:', row);

                          setDeleteItemId(row.id);
                          setDeleteRow(row);
                          setShowConfirmDialog(true);
                          // 检查是否至少保留一项
                          // if (batchItems.length <= 1) {
                          //   toast.error('至少保留一个轮播项');
                          //   return;
                          // }

                          // 使用React状态更新方法来修改数据
                          // const newItems = batchItems.filter(
                          //   item => item.id !== row.id
                          // );
                          // setBatchItems(newItems);
                          // setRefreshListKey(prev => prev + 1);

                          // toast.success('删除成功');
                        } catch (error) {
                          console.error('删除操作失败:', error);
                          toast.error('删除失败：' + (error as Error).message);
                        }
                      }
                    }
                  ]
                }
              ]
            }
          ]
        }
      };
    } catch (error) {
      console.error('获取批量编辑对话框配置失败:', error);
      return {};
    }
  };

  // 安全关闭对话框
  const handleCloseDialog = () => {
    if (!isMounted.current) return;

    try {
      // 先关闭对话框
      setShowEditDialog(false);

      // 延迟重置编辑索引，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          setCurrentEditIndex(-1);
          setIsAddMode(false);
        }
      }, 50);
    } catch (error) {
      console.error('关闭对话框失败:', error);
    }
  };

  // 处理拖拽排序
  const handleMoveItem = (dragIndex: number, hoverIndex: number) => {
    if (!isMounted.current) return;

    try {
      const dragItem = portletTabs[dragIndex];
      if (!dragItem) return;

      // 创建新数组，避免直接修改原数组
      const newTabs = [...portletTabs];

      // 删除拖拽项
      newTabs.splice(dragIndex, 1);

      // 在新位置插入拖拽项
      newTabs.splice(hoverIndex, 0, dragItem);

      // 先更新本地状态
      setPortletTabs(newTabs);

      // 使用setTimeout延迟更新全局状态，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          // 深拷贝，避免引用问题
          props.handleTabs(JSON.parse(JSON.stringify(newTabs)));
          props.context.schema.tabs = newTabs;
          toast.success('排序成功');
        }
      }, 0);
    } catch (error) {
      console.error('拖拽排序失败:', error);
      toast.error('拖拽排序失败');
    }
  };

  // 编辑对话框配置
  const getEditDialogSchema = () => {
    try {
      // 每次获取时创建新的schema对象，避免引用问题
      let currentTab = null;

      if (
        !isAddMode &&
        currentEditIndex >= 0 &&
        currentEditIndex < portletTabs.length
      ) {
        currentTab = JSON.parse(JSON.stringify(portletTabs[currentEditIndex]));
      }

      if (!currentTab) {
        currentTab = {
          title: '',
          icon: '',
          iconPosition: 'left',
          reload: false,
          mountOnEnter: true,
          unmountOnExit: false,
          body: {
            type: 'tpl',
            tpl: '分组内容'
          }
        };
      }

      return {
        type: 'dialog',
        title: isAddMode ? '添加分组' : '编辑分组',
        size: 'lg',
        closeOnEsc: true,
        closeOnOutside: false,
        body: {
          type: 'form',
          mode: 'horizontal',
          submitText: '确认',
          onSubmit: handleSaveEdit,
          body: [
            {
              type: 'input-text',
              name: 'title',
              label: '分组标题',
              required: true,
              placeholder: '请输入分组标题',
              value: currentTab.title || ''
            },
            {
              type: 'icon-selector',
              name: 'icon',
              label: '图标　　',
              placeholder: '请选择图标',
              returnSvg: true,
              value: currentTab.icon
            },
            {
              type: 'hidden',
              name: 'icon',
              value: currentTab.icon
            },
            {
              type: 'select',
              name: 'iconPosition',
              label: '图标位置',
              options: [
                {
                  label: '左侧',
                  value: 'left'
                },
                {
                  label: '右侧',
                  value: 'right'
                }
              ],
              value: currentTab.iconPosition || 'left'
            },
            // {
            //   type: 'static',
            //   label: '',
            //   tpl:'行为设置',
            //   value:''
            // },
            {
              type: 'switch',
              name: 'reload',
              label: '每次重新渲染',
              value: currentTab.reload || false,
              className: 'text-right'
            },
            {
              type: 'switch',
              name: 'mountOnEnter',
              label: '点开加载',
              value: currentTab.mountOnEnter !== false,
              className: 'text-right'
            },
            {
              type: 'switch',
              name: 'unmountOnExit',
              label: '隐藏销毁',
              value: currentTab.unmountOnExit || false,
              className: 'text-right'
            }
            // {
            //   type: 'editor',
            //   name: 'body',
            //   label: '标签内容',
            //   language: 'json',
            //   size: 'lg',
            //   placeholder: '请输入标签内容的JSON配置',
            //   value: bodyStr
            // }
          ]
        }
      };
    } catch (error) {
      console.error('获取编辑对话框配置失败:', error);
      return {};
    }
  };

  // 渲染前检查数据有效性
  const safePortletTabs = Array.isArray(portletTabs) ? portletTabs : [];

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="portlet-data-box">
        <div className="portlet-tabs-container">
          <div className="portlet-tabs-list">
            {safePortletTabs.map((tab, index) => (
              <PortletTabRow
                key={`portlet-tab-${index}`}
                item={tab}
                index={index}
                onEdit={handleEditItem}
                onDelete={handleDeleteItem}
                onMove={handleMoveItem}
              />
            ))}
          </div>

          <div className="portlet-footer">
            <Button level="default" size="sm" onClick={handleAddItem}>
              <i className="fa fa-plus"></i> 添加分组
            </Button>
            <Button level="default" size="sm" onClick={handleBatchEdit}>
              <i className="fa fa-list"></i> 批量编辑
            </Button>
          </div>
        </div>

        {showEditDialog && (
          <AMISRenderer
            schema={getEditDialogSchema()}
            show={showEditDialog}
            onClose={handleCloseDialog}
          />
        )}

        {showBatchEditDialog && (
          <AMISRenderer
            key={refreshListKey}
            schema={getBatchEditDialogSchema()}
            show={showBatchEditDialog}
            onClose={() => setShowBatchEditDialog(false)}
          />
        )}

        {showConfirmDialog && (
          <AMISRenderer
            show={showConfirmDialog}
            schema={{
              type: 'dialog',
              title: '删除分组',
              body: {
                type: 'tpl',
                tpl: '确定要删除该分组吗？删除后该分组下的入口都会被删除!'
              },
              actions: [
                {
                  type: 'button',
                  label: '取消',
                  actionType: 'close'
                },
                {
                  type: 'button',
                  label: '确认',
                  level: 'danger',
                  onClick: handleConfirmDelete
                }
              ]
            }}
            onClose={() => setShowConfirmDialog(false)}
          />
        )}
      </div>
    </DndProvider>
  );
};

export default PortletDataInteraction;
