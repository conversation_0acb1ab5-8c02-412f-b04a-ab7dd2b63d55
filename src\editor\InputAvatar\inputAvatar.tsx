import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';
import { union } from 'lodash';

export class AvatarControlPlugin extends BasePlugin {
  static id = 'AvatarControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'input-avatar';
  $schema = '/schemas/AvatarControlSchema.json';

  // 组件基本信息
  name = '上传头像';
  panelTitle = '上传头像';
  icon = 'fa fa-user-circle';
  panelIcon = 'fa fa-user-circle';
  pluginIcon = 'icon-image-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '上传头像';
  docLink = '/amis/zh-CN/components/form/input-image';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'input-avatar',
    label: '上传头像',
    name: 'avatar',
    clearable: true,
    placeholder: '请选择头像',
    accept: 'image/jpeg,image/jpg,image/png',
    crop: false,
    uploadBtnText: ' ',
    width: 100,
    height: 100,
    borderRadius: '50%', // 默认为圆形
    uploadType: 'fileReceptor',
    autoUpload: true,
    proxy: true,
    multiple: false,
    frameImage: '', // 添加占位图片
  };

    // 添加事件定义
    events = [
      {
        eventName: 'click',
        eventLabel: '菜单项点击',
        description: '点击导航项时触发'
        // ...
      },
      {
        eventName: 'select',
        eventLabel: '菜单项选中',
        description: '选中导航项时触发'
        // ...
      },
      {
        eventName: 'expand',
        eventLabel: '菜单展开',
        description: '菜单展开时触发'
        // ...
      },
      {
        eventName: 'collapse',
        eventLabel: '菜单折叠',
        description: '菜单折叠时触发'
        // ...
      },
      {
        eventName: 'loaded',
        eventLabel: '数据加载完成',
        description: '数据加载完成时触发'
        // ...
      }
    ];
  

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                // getSchemaTpl('layout:originPosition', {
                //   value: 'left-top'
                // }),
                // getSchemaTpl('formItemName', {
                //   required: true
                // }),
                // getSchemaTpl('label'),
                getSchemaTpl('required'),
                getSchemaTpl('title', {
                  name: 'accept',
                  label: '图片类型',
                  value: 'image/jpeg,image/jpg,image/png',
                }),
                getSchemaTpl('title', {
                  name: 'frameImage',
                  label: '占位图片地址',
                  value: '占位地址',
                }),
                getSchemaTpl('switch', {
                  name: 'crop',
                  label: '裁剪功能',
                  value: true,
                })
              ]
            },
            getSchemaTpl('status', {
              isFormItem: true,
              readonly: true
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '图片设置',
              body: [
                getSchemaTpl('layout:width:v2', {
                  name: 'borderRadius',
                  label: '圆角设置',
                  value: '50%',
                  unitOptions: ['px', '%'],
                }),
                getSchemaTpl('style:widthHeight', {
                  widthSchema: {
                    label: '图片宽度'
                  },
                  heightSchema: {
                    label: '图片高度',
                  }
                }),
              ]
            },
            getSchemaTpl('theme:form-label'),
            getSchemaTpl('theme:form-description'),
            
            {
              title: '选择框样式',
              body: [
                ...inputStateTpl(
                  'themeCss.addBtnControlClassName',
                  '--inputImage-base'
                ),
                // 图标选择
                {
                  type: 'icon-select',
                  name: 'themeCss.addBtnControlClassName.--inputImage-base-default-icon',
                  label: '图标选择',
                  description: '设置组件的图标',
                  returnSvg: true
                },
                // 图标颜色
                getSchemaTpl('theme:colorPicker', {
                  name: 'themeCss.addBtnControlClassName.icon-color:default',
                  label: '图标颜色'
                }),
                // 图标大小
                getSchemaTpl('layout:width:v2', {
                  name: 'themeCss.iconControlClassName.iconSize',
                  label: '图标大小',
                  value: '30',
                  unitOptions: ['px', 'rem']
                })
              ]
            }
            
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(AvatarControlPlugin);
