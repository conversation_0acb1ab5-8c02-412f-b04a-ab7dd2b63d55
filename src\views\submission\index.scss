.submission-layout {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: auto;

  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  padding-bottom: 60px;
}

.submission-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  height: 50px;
  color: var(--text-color);
  background-color: var(--body-bg);
  border-bottom: 1px solid var(--borderColor);

  &-left {
    display: flex;
    align-items: center;

    &-appname {
      display: flex;
      align-items: center;

      &-icon {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }

      &-name {
        font-size: 14px;
        margin-right: 6px;
      }
    }
  }

  &-right {
    display: flex;
    align-items: center;

    &-user {
      display: flex;
      align-items: center;
      cursor: pointer;

      &-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        overflow: hidden;
      }
    }
  }
}

.submission-page-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  margin: 0 auto;
  width: 100%;

  .submission-form-box {
    margin: 0 auto;
    margin-top: 12px;
    width: 1080px;
    border: none;
    border-radius: 4px;
  }

  .submission-form-content {
    padding: 26px 30px;
  }
}

.submission-header-right-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.submission-success-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 150px);
  width: 100%;

  .success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .success-icon {
      width: 53px;
      height: 53px;
      border-radius: 50%;
      background-color: var(--primary);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 13px;

      .check-icon {
        color: var(--text-color);
        width: 24px;
        height: 24px;
      }
    }

    .success-title {
      font-size: 18px;
      font-weight: bold;
      color: var(--text-color);
      margin-bottom: 4px;
    }

    .success-desc {
      font-size: 14px;
      color: var(--text--muted-color);
      margin-bottom: 12px;
    }

    .success-actions {
      display: flex;
      justify-content: center;
    }
  }
}

.cxd-Panel,
.dark-Panel {
  border: none;
  box-shadow: none;
  margin: 0;
  padding: 15px;
}

.cxd-Panel-heading,
.dark-Panel-heading {
  border: none;
}

.cxd-Panel-footer,
.dark-Panel-footer {
  border: none;
}

.cxd-Panel--default > .cxd-Panel-heading,
.dark-Panel--default > .dark-Panel-heading {
  background-color: transparent;
}

// 表单详情页样式
.submission-detail-container {
  width: 100%;
  min-height: 100%;

  .dark-Form-label {
    width: 0 !important;
  }

  // .dark-Form-item--horizontal > .dark-Form-label {
  //   text-align: left;
  //   // margin-left: 30px;
  //   width: 0;
  //   white-space: nowrap;
  //   flex-shrink: 0;
  //   min-width: fit-content !important;
  // }

  // 头部信息样式
  .submission-detail-header {
    padding-top: 20px;

    .header-main {
      width: 1080px;
      margin: 0 auto;
      padding: 0 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .submission-detail-title {
        font-size: 18px;
        font-weight: bold;
        color: var(--text-color);
      }

      .header-actions {
        display: flex;
        gap: 6px;

        .action-btn {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          border: 1px solid var(--borderColor);
          border-radius: 4px;
          color: var(--text-color);

          &:hover {
            border-color: var(--primary);
            color: var(--primary);
          }
        }
      }
    }

    .submission-detail-info {
      width: 1080px;
      margin: 0 auto;
      padding: 0 30px;
      display: flex;

      .info-item {
        flex: 1;
        margin-right: 0;
        margin-bottom: 24px;

        .info-label,
        .info-value {
          display: block;
        }

        .info-label {
          font-size: 14px;
          color: var(--text--muted-color);
          margin-bottom: 6px;
        }

        .info-value {
          font-size: 14px;
          color: var(--text-color);
          font-weight: 500;
        }
      }
    }
  }

  .submission-detail-content {
    width: 1080px;
    margin: 0 auto;
    margin-top: 12px;
    padding: 0 30px;
  }

  // 底部内容样式
  .submission-detail-footer {
    width: 1080px;
    margin: 0 auto;
    margin-top: 12px;
    padding: 26px 30px;

    .footer-section {
      .section-title {
        font-size: 18px;
        font-weight: bold;
        color: var(--text-color);
        margin-bottom: 20px;
      }

      // 使用 amis Tabs 的样式
      .section-tabs {
        .cxd-Tabs-link,
        .dark-Tabs-link {
          font-size: 14px;
          color: var(--text--muted-color);
          padding: 8px 16px;

          &:hover {
            color: var(--primary);
          }

          &.is-active {
            color: var(--primary);

            &:after {
              background-color: var(--primary);
            }
          }
        }
      }

      .empty-placeholder {
        text-align: center;
        color: var(--text--muted-color);
        padding: 24px 0;
      }
    }
  }

  // 底部操作按钮
  .submission-detail-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin-top: 12px;
    padding: 10px 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
    z-index: 100;

    .actions-content {
      display: flex;
      justify-content: center;
      gap: 10px;

      .action-btn {
        font-size: 14px;

        &.cxd-Button--primary,
        .dark-Button--primary {
          background-color: var(--primary);
          border-color: var(--primary);
        }

        &.cxd-Button--default,
        .dark-Button--default {
          border: 1px solid var(--borderColor);
          color: var(--text-color);

          &:hover {
            border-color: var(--primary);
            color: var(--primary);
          }
        }
      }
    }
  }
}

// 确保详情页面不显示默认提交按钮
.submission-detail-container .cxd-Form-actions,
.submission-detail-container .dark-Form-actions {
  display: none !important;
}

// .cxd-Form-item--horizontal .cxd-Form-itemColumn--normal {
//   width: 0 !important;
// }

// .cxd-Form-item--horizontal > .cxd-Form-label {
//   text-align: left;
//   // margin-left: 30px;
//   width: 0;
//   white-space: nowrap;
//   flex-shrink: 0;
//   min-width: fit-content !important;
// }

// .dark-Form-item--horizontal [class*='dark-Form-itemColumn--'] {
//   flex: 0;
//   flex-shrink: 0;
//   white-space: nowrap;
//   min-width: fit-content !important;
// }

// .dark-Form-value {
//   width: 1000px;
// }

// .cxd-Form-static,
// .dark-Form-static {
//   display: flex;
//   align-items: center;
// }

// .cxd-Page,
// .dark-Page {
//   margin-left: 30px;
// }


.submission-detail-content {
  padding-top: 26px;
  padding-bottom: 26px;
  padding-right: 30px;
}

.cxd-Panel-body,
.dark-Panel-body {
  padding: 0 !important;
}

.cxd-Panel-heading,
.dark-Panel-heading {
  padding: 0 !important;
  margin-bottom: 20px;
}

// .cxd-Panel-footer,
// .dark-Panel-footer {
//   padding: 0 !important;
//   margin-top: 60px;
// }

// .dark-Panel,
// .cxd-Panel {
//   background-color: var(--body-bg);
//   margin: 0;
// }

// .dark-Tabs-content,
// .cxd-Tabs-content {
//   background-color: var(--light-bg);
//   border: none;
// }
