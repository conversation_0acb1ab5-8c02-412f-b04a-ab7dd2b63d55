import React, {FC} from 'react';
import './index.scss';
import {toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createReportObj} from '@/utils/schemaPageTemplate/createPageObjs';

// 图片
import pending_icon from './image/pending_icon.png';
import ccusers_icon from './image/ccusers_icon.png';
import transactors_icon from './image/transactors_icon.png';
import create_icon from './image/create_icon.png';
import { url } from 'inspector';

const WorkbenchApprovalCenter: FC<any> = (props: any) => {
  /* data 数据 */
  const menuList = [
    {
      title: '待我处理',
      icon: pending_icon,
      path: '/platform/workbench/pending',
      url: `${process.env.NODE_ENV === 'development'?'http://localhost:88/#/workspace/unfinished':'https://javaddm.fjpipixia.com/wflow/#/workspace/unfinished'}`
    },
    {
      title: '我已处理',
      icon: ccusers_icon,
      path: '/platform/workbench/transactors',
      url: `${process.env.NODE_ENV === 'development'?'http://localhost:88/#/workspace/finished':'https://javaddm.fjpipixia.com/wflow/#/workspace/finished'}`
    },
    {
      title: '我创建的',
      icon: transactors_icon,
      path: '/platform/workbench/create',
      url: `${process.env.NODE_ENV === 'development'?'http://localhost:88/#/workspace/submit':'https://javaddm.fjpipixia.com/wflow/#/workspace/submit'}`
    },
    {
      title: '抄送我的',
      icon: create_icon,
      path: '/platform/workbench/ccusers',
      url: `${process.env.NODE_ENV === 'development'?'http://localhost:88/#/workspace/cc':'https://javaddm.fjpipixia.com/wflow/#/workspace/cc'}`
    }
  ];
  // 选中的path
  const [activeItem, setActiveItem] = React.useState<any>(menuList[0]);
  /* data 数据 end */

  /* methods 方法 */
  // 选中菜单的item.path ,并跳转路由
  const handleSelectMenuItem = (item: any) => {
    setActiveItem(item);
    props.history.push(item.path);
  };

  /* methods 方法 end */

  /* created 初始化 */
  React.useEffect(() => {
    // 初始化时，获取当前路由的path,并设置activeItem
    const path = props.location.pathname;
    const item = menuList.find((item: any) => item.path == path);
    if (item) {
      setActiveItem(item);
    }
  }, [props.location.pathname]);

  /* created 初始化 end */
  return (
    <div className="approvalCenterBox">
      <div className="approvalCenterBox-top">
        <div className="approvalCenterBox-top-name">工作台审批中心</div>
        <div className="approvalCenterBox-top-desc">
          一站式呈现与管理企业内所有的工单事务，便捷高效助力企业流程运转。{' '}
        </div>
      </div>
      <div className="approvalCenterBox-main">
        <div className="approvalCenterBox-main-sidebar">
          <div className="approvalCenterBox-main-sidebar-list">
            {menuList.map((item: any) => {
              return (
                <div onClick={()=>handleSelectMenuItem(item)} className={activeItem.path == item.path  ? "approvalCenterBox-main-sidebar-list-item item-click" : "approvalCenterBox-main-sidebar-list-item"} key={item.path}>
                  <img
                    className="approvalCenterBox-main-sidebar-list-item-img"
                    src={item.icon}
                  />
                  <span className="approvalCenterBox-main-sidebar-list-item-text">
                    {item.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
        <div className="approvalCenterBox-main-content">
          <AMISRenderer
            schema={createReportObj(activeItem.url+`?userID=${props.store.userInfo?.id}&tenantId=${props.store.tenant_id}&authorization=${props.store.access_token}`)}
          />
        </div>
      </div>
    </div>
  );
};

export default WorkbenchApprovalCenter;
