import type {CSSProperties, FC} from 'react';
import type {DragSourceMonitor} from 'react-dnd';
import {useDrag} from 'react-dnd';

import React from 'react';

const style: CSSProperties = {};

export interface BoxProps {
  fieldItem: any;
  handleMoveInBoxItem: (dropResult: any, item: any) => void;
}

interface DropResult {
  allowedDropEffect: string;
  dropEffect: string;
  fieldItem: string;
}

export const FieldItemBox: FC<BoxProps> = ({
  fieldItem,
  handleMoveInBoxItem
}) => {
  const [{opacity}, drag] = useDrag(
    () => ({
      type: 'fieldItem',
      item: {...fieldItem},
      end(item, monitor) {
        const dropResult = monitor.getDropResult() as DropResult;
        if (item && dropResult) {
          handleMoveInBoxItem(dropResult, item);
        }
      },
      collect: (monitor: DragSourceMonitor) => ({
        opacity: monitor.isDragging() ? 0.8 : 1
      })
    }),
    [fieldItem]
  );

  const typeField: any = {
    timestamp: '23',
    int: '123',
    varchar: 'abc'
  };

  return (
    <div ref={drag} style={{...style, opacity}} className="dataSetData-field">
      {fieldItem.controlLabel != '' ? (
        <div className="dataSetData-field-type">{fieldItem.controlLabel}</div>
      ) : (
        <div className="dataSetData-field-type">
          {typeField[fieldItem.type]}*
        </div>
      )}
      <div className="dataSetData-field-name">{fieldItem.label}</div>
    </div>
  );
};
