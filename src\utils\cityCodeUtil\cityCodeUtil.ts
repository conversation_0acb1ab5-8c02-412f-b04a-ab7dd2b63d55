// 导入城市编码数据
// @ts-ignore
import cityCodeData from './city_code.json';

/**
 * 根据城市编码查询完整地址
 * @param {string} code 城市编码，如"110101"
 * @returns {string|null} 完整地址，如"北京市/市辖区/东城区"，未找到则返回null
 */
export function getFullAddressByCode(code: string) {
  if (!code) return null;
  
  // 存储查找路径
  const path = [];
  // 存储找到的每一级地址信息
  const foundItems: { code: string; name: string; children?: any[] }[] = [];
  
  // 递归查找函数
  function findInData(data: any[], targetCode: string, currentPath: { code: string; name: string; children?: any[] }[] = []) {
    if (!data || !Array.isArray(data)) return false;
    
    for (const item of data) {
      // 直接匹配完整编码
      if (item.code === targetCode) {
        foundItems.push(...currentPath, item);
        return true;
      }
      
      // 检查当前项的编码是否匹配目标编码的前缀
      // 例如，查找"110101"时，会匹配"11"、"1101"和"110101"
      if (targetCode.startsWith(item.code) && targetCode.length >= item.code.length) {
        const newPath = [...currentPath, item];
        
        // 如果完全匹配，则找到了目标
        if (item.code === targetCode) {
          foundItems.push(...newPath);
          return true;
        }
        
        // 如果有子项，继续在子项中查找
        if (item.children && findInData(item.children, targetCode, newPath)) {
          return true;
        }
        
        // 如果编码是目标编码的前缀，但没有子项或在子项中未找到，
        // 则当前项可能是最接近的匹配项
        if (targetCode.startsWith(item.code) && (!item.children || item.children.length === 0)) {
          foundItems.push(...newPath);
          return true;
        }
      }
      
      // 递归查找所有子项，不管前缀是否匹配
      if (item.children && findInData(item.children, targetCode, [...currentPath, item])) {
        return true;
      }
    }
    
    return false;
  }
  
  // 开始查找
  findInData(cityCodeData, code);
  
  // 如果找到了匹配项，构建完整地址
  if (foundItems.length > 0) {
    return foundItems.map(item => item.name).join('/');
  }
  
  return null;
}



/**
 * 获取城市编码对应的名称
 * @param {string} code 城市编码
 * @returns {string|null} 城市名称
 */
export function getCityNameByCode(code: string) {
  if (!code) return null;
  
  let result = null;
  
  function findInData(data: { code: string; name: string; children?: any[] }[], targetCode: string) {
    if (!data || !Array.isArray(data)) return false;
    
    for (const item of data) {
      if (item.code === targetCode) {
        result = item.name;
        return true;
      }
      
      if (item.children && findInData(item.children, targetCode)) {
        return true;
      }
    }
    
    return false;
  }
  
  findInData(cityCodeData, code);
  return result;
}



/**
 * 根据城市名称查询城市编码
 * @param {string} cityName 城市名称，支持多种格式：
 *                          1. 单级：如"湖北省"
 *                          2. 两级：如"湖北省/武汉市"
 *                          3. 三级：如"湖北省/武汉市/洪山区"
 * @returns {string|null} 城市编码，未找到则返回null
 */
export function getCodeByCityName(cityName: string) {
  if (!cityName) return null;
  
  // 分割城市名称
  const nameParts = cityName.split('/').map(part => part.trim());
  
  // 存储查找结果
  let result = null;
  
  // 递归查找函数
  function findInData(data: any[], nameToFind: string, level: number = 0): string | null {
    if (!data || !Array.isArray(data)) return null;
    
    for (const item of data) {
      // 检查当前项的名称是否匹配
      if (item.name === nameToFind) {
        // 如果是最后一级或者没有更多级别要查找，返回当前编码
        if (level === nameParts.length - 1 || !item.children) {
          return item.code;
        }
        
        // 继续查找下一级
        if (item.children) {
          const nextLevelCode = findInData(item.children, nameParts[level + 1], level + 1);
          if (nextLevelCode) {
            return nextLevelCode;
          }
          // 如果下一级没找到，但用户只想要到当前级别的编码
          if (level + 1 === nameParts.length - 1) {
            return item.code;
          }
        }
        
        return item.code;
      }
    }
    
    // 尝试模糊匹配（去掉"省"、"市"、"区"等后缀）
    for (const item of data) {
      const simplifiedName = nameToFind
        .replace(/省$/, '')
        .replace(/市$/, '')
        .replace(/区$/, '')
        .replace(/县$/, '')
        .replace(/自治区$/, '')
        .replace(/自治州$/, '')
        .replace(/特别行政区$/, '');
      
      const simplifiedItemName = item.name
        .replace(/省$/, '')
        .replace(/市$/, '')
        .replace(/区$/, '')
        .replace(/县$/, '')
        .replace(/自治区$/, '')
        .replace(/自治州$/, '')
        .replace(/特别行政区$/, '');
      
      if (simplifiedItemName === simplifiedName) {
        // 如果是最后一级或者没有更多级别要查找，返回当前编码
        if (level === nameParts.length - 1 || !item.children) {
          return item.code;
        }
        
        // 继续查找下一级
        if (item.children) {
          const nextLevelCode = findInData(item.children, nameParts[level + 1], level + 1);
          if (nextLevelCode) {
            return nextLevelCode;
          }
          // 如果下一级没找到，但用户只想要到当前级别的编码
          if (level + 1 === nameParts.length - 1) {
            return item.code;
          }
        }
        
        return item.code;
      }
    }
    
    return null;
  }
  
  // 开始查找
  if (nameParts.length > 0) {
    result = findInData(cityCodeData, nameParts[0]);
  }
  
  return result;
}


// 导出默认对象，包含所有方法
export default {
  getFullAddressByCode,
  getCityNameByCode,
  getCodeByCityName
};