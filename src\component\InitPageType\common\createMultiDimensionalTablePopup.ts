// 创建多维表格页面页面弹窗
const multiDimensionalTablePopup = () => {
    return {
      type: 'dialog',
      title: '创建多维表格页面',
      body: [
        {
          id: 'u:945e0976447b',
          type: 'form',
          title: '表单',
          mode: 'flex',
          labelAlign: 'top',
          dsType: 'api',
          feat: 'Insert',
          body: [
            {
              type: 'input-text',
              label: '',
              name: 'name',
              row: 0,
              id: 'u:f373b3160f0c',
              placeholder: '请输入需要创建的多维表格页面名称',
              inputClassName: 'name-input',
            labelClassName: 'name-label'
            }
          ],
          actions: [
            {
              type: 'button',
              label: '提交',
              onEvent: {
                click: {
                  actions: [
                    {
                      actionType: 'submit',
                      componentId: 'u:945e0976447b'
                    }
                  ]
                }
              },
              level: 'primary'
            }
          ],
          resetAfterSubmit: false,
          closeDialogOnSubmit: true
        }
      ],
      id: 'u:be54fbc63e4a',
      actions: [
        {
          type: 'button',
          actionType: 'cancel',
          label: '取消',
          id: 'u:1835cdeb9cf0'
        },
        {
          type: 'button',
          actionType: 'confirm',
          label: '确定',
          primary: true,
          id: 'u:5f6513bbc44e'
        }
      ],
      showCloseButton: false,
      closeOnOutside: false,
      closeOnEsc: false,
      showErrorMsg: true,
      showLoading: true,
      draggable: false,
      editorSetting: {
        displayName: '创建页面'
      },
      themeCss: {
        dialogClassName: {
          radius: {
            'top-left-border-radius': '8px',
            'top-right-border-radius': '8px',
            'bottom-left-border-radius': '8px',
            'bottom-right-border-radius': '8px'
          }
        }
      }
    };
  };
  
  export default multiDimensionalTablePopup;
  