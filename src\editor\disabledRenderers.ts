const componentAll: string[] = [
    // ===============> '布局容器'
    // Flex 布局:
    "flex",
    // 分栏:
    "grid",
    // 容器:
    "container",
    // 自由容器:
    "container",
    // 状态容器:
    "switch-container",
    "grid-2d",
    "cusgrid",
    // 折叠面板:
    "collapse-group",
    // 面板:
    "panel",
    // 选项卡:
    "tabs",
    // ===============> '数据容器'
    // 表格2.0:
    "crud2",
    // 表单:
    "form",
    // 服务Service:
    "service",
    // 增删改查:
    "crud",
    "crud-aside",
    "crud-dataset",
    "crud2-dataset",
    "relatedform",
    // ===============>  功能
    // 按钮:
    "button",
    // 重置:
    "reset",
    // 提交:
    "submit",
    // 按钮组:
    "button-group",
    // 导航:
    "nav",
    // 锚点导航:
    "anchor-nav",
    // 文字提示:
    "tooltip-wrapper",
    // 提示:
    "alert",
    // 向导:
    "wizard",
    // 表格视图:
    "table-view",
    // Web Component:
    "web-component",
    // 音频:
    "audio",
    // 视频:
    "video",
    // 自定义代码:
    "custom",
    // 异步任务:
    "tasks",
    // 循环 Each:
    "each",
    // 属性表:
    "property",
    // iFrame:
    "iframe",
    // 二维码:
    "qrcode",
    // ===============>  展示
    // 文字:
    "tpl",
    // 图标:
    "icon",
    // 链接:
    "link",
    // 列表:
    "list",
    // 列表:
    "cards",
    // 映射:
    "mapping",
    // 头像:
    "avatar",
    // 卡片:
    "card",
    // 媒体卡片:
    "media-card",
    // 用户卡片:
    "user-card",
    // 图标卡片:
    "icon-card",
    // 卡片:
    "card2",
    // 卡片列表:
    "cards",
    // 原子表格:
    "table",
    // 图表:
    "chart",
    // 走势图:
    "sparkline",
    // 轮播图:
    "carousel",
    // 图片展示:
    "image",
    // 图片集:
    "images",
    // 日期展示:
    "date",
    // 时间展示:
    "time",
    // 日期时间展示:
    "datetime",
    // 日历日程:
    "calendar",
    // 标签:
    "tag",
    // JSON展示:
    "json",
    // 进度展示:
    "progress",
    // 状态显示:
    "status",
    // 步骤条:
    "steps",
    // 时间轴:
    "timeline",
    // 分隔线:
    "divider",
    // 代码高亮:
    "code",
    // Markdown:
    "markdown",
    // 折叠器:
    "collapse",
    // 文档预览:
    "office-viewer",
    // PDF预览:
    "pdf-viewer",
    // 日志:
    "log",
    // 分页组件:
    "pagination",
    // 纯文本:
    "plain",
    //图标展示
    "icon-show",
    // 上传 Excel:
    // "input-excel",
    // ===============>  表单项
    // 文本框:
    "input-text",
    // 邮箱框:
    "input-email",
    // 密码框:
    "input-password",
    // URL输入框:
    "input-url",
    // 多行文本框:
    "textarea",
    // 数字框:
    "input-number",
    // 下拉框:
    "select",
    // 级联选择器:
    "nested-select",
    // 链式下拉框:
    "chained-select",
    // 下拉按钮:
    "dropdown-button",
    // 复选框:
    "checkboxes",
    // 单选框:
    "radios",
    // 勾选框:
    "checkbox",
    // 日期:
    "input-date",
    // 日期范围:
    "input-date-range",
    // 文件上传:
    "input-file",
    // 图片上传:
    "input-image",
    'icon-selector',
    "department-select",
    "user-select",
    "role-select",
    "pos-select",
    "department-multi-select",
    "user-multi-select",
    "role-multi-select",
    "pos-multi-select",
    "input-avatar",
    "input-appicon",
    "appicon",
    "input-address",
    "my-portlet",
    "my-carousel",
    "my-container",
    "component-viewer",
    "app",
    "my-app",
    "my-drawer",
    "my-dialog",
    "radio",
    "my-radio",
    "my-amis",
    "amis",
    "drawer",
    "dialog",
    "grid-nav",
    "my-grid-nav",
    "input-verification-code",
    "my-input-verification-code",
    "my-shape",
    "shape",
    "spinner",
    "my-spinner",
    "my-nav",
    "my-nav-page",
    "json-schema",
    "json-schema-editor",
    "my-json-schema",
    "my-json-schema-editor",
    "input-phone",
    "my-number",
    "my-input-excel",
    "serial",
    "relatedfiled",
    // 树组件:
    "input-tree",
    // 标签选择:
    "input-tag",
    // 列表选择:
    "list-select",
    // 按钮点选:
    "button-group-select",
    // 按钮工具栏:
    "button-toolbar",
    // 列表选取:
    "picker",
    // 开关:
    "switch",
    // 滑块:
    "input-range",
    // 评分:
    "input-rating",
    // 城市选择:
    "input-city",
    // 穿梭器:
    "transfer",
    // 穿梭选择器:
    "transfer-picker",
    // 组合穿梭器:
    "tabs-transfer",
    // 颜色框:
    "input-color",
    // 条件组件:
    "condition-builder",
    // 字段集:
    "fieldset",
    // 组合输入:
    "combo",
    // 输入组合:
    "input-group",
    // 表格编辑框:
    "input-table",
    // 矩阵开关:
    "matrix-checkboxes",
    // 富文本编辑器:
    "input-rich-text",
    // Diff编辑器:
    "diff-editor",
    // 代码编辑器:
    "editor",
    // 搜索框:
    "search-box",
    // KV 键值对:
    "input-kv",
    // 重复周期选择:
    "input-repeat",
    // UUID:
    "uuid",
    // 地理位置选择:
    "location-picker",
    // 子表单项:
    "input-sub-form",
    // 隐藏域:
    "hidden",
    // 手写签:
    "input-signature",
    // 静态展示框:
    "static",
    // 数组输入框:
    "input-array",
    // 日期时间:
    "input-datetime",
    // 日期时间范围:
    "input-datetime-range",
    // 公式:
    "formula",
    // 表单组:
    "group",
    // 日期:
    "input-month",
    // 月份范围:
    "input-month-range",
    // 季度:
    "input-quarter",
    // 季度范围:
    "input-quarter-range",
    // 时间框:
    "input-time",
    // 日期范围:
    "input-time-range",
    // 树组件:
    "tree-select",
    // Year:
    "input-year",
    // 日期范围:
    "input-year-range",
    // ===============> 容器
    // 表单项容器:
    "control",
    // HBox:
    "hbox",
    // 包裹:
    "wrapper",
    // ===============> 其他
    // 面包屑:
    "breadcrumb",
    // ===============> 自定义显示列
    // 自定义显示列:
    "column-toggler",
    // ===============> ['功能', '容器']
    // 自定义容器:
    "custom",
    //  ===============> 容器 
    // 页面:
    "page",
    // ===============> undefined 
    // 表格:
    "table2"

]

const componentForm: string[] = [
    // 文本框:
    "input-text",
    // 邮箱框:
    "input-email",
    // 密码框:
    "input-password",
    // URL输入框:
    "input-url",
    // 多行文本框:
    "textarea",
    // 数字框:
    "input-number",
    // 年:
    "input-year",
    // 季度
    "input-quarter",
    // 月:
    "input-month",
    // 时间:
    "input-time",
    // 日期:
    "input-date",
    // 日期时间:
    "input-datetime",
    // 年范围:
    "input-year-range",
    // 季度范围
    "input-quarter-range",
    // 月范围:
    "input-month-range",
    // 时间范围:
    "input-time-range",
    // 日期范围:
    "input-date-range",
    // 日期时间范围:
    "input-datetime-range",
    // 文件上传:
    "input-file",
    // 图片上传:
    "input-image",
    'icon-selector',
    "department-select",
    "user-select",
    "role-select",
    "pos-select",
    "department-multi-select",
    "user-multi-select",
    "role-multi-select",
    "pos-multi-select",
    "input-avatar",
    "input-appicon",
    "appicon",
    "input-address",
    "input-phone",
    "my-number",
    "my-input-excel",
    "serial",
    "my-drawer",
    "my-dialog",
    "radio",
    "my-radio",
    "input-verification-code",
    "my-input-verification-code",
    "my-amis",
    "amis",
    "drawer",
    "dialog",
    "relatedfiled",
    // 上传 Excel:
    // "input-excel",
    // 手写签
    'input-signature',
    // 地理位置
    'location-picker',
    // 城市选择
    'input-city',
    // 编辑器
    'input-rich-text',
    // 级联选择器
    'nested-select',
    // 单选框
    'radios',
    // 复选框
    'checkboxes',
    // 下拉框
    'select',
    // 开关
    'switch',
]


const componentDashboard: string[] = [
     // Flex 布局:
     "flex",
     "grid-2d",
     "cusgrid",
     "hbox",
     // 分栏:
     "grid",
     // 容器:
     "container",
     // 自由容器:
     "container",
    //  图表
    "line-chart",
    "bar-chart",
    "pie-chart",
    "word-cloud-chart",
    "calendar-heatmap-chart",

    // "boxplotChart",
    // "calendarHeatmapChart",
    // "candlestickChart",
    // "calendarHeatmapChart",
    // "wordCloudChart",
    // "treemapChart",
    // "treeChart",
    // "themeRiverChart",
    // "sunburstChart",
    // "scatterChart",
    // "sankeyChart",
    "radar-chart",
    // "pieChart",
    // "parallelChart",
    // "mixLineBarChart",
    "funnel-chart",
    // "graphChart",
    "gauge-chart",
    // "heatmapChart",
    "map-chart",
    // "my-renderer",
    "tpl",
    "image",
    "link"
]

const componentCRUD: string[] = [
    // 表格:
    "crud",
    "crud-aside",
    "crud-dataset",
    "crud2-dataset",
    "crud2",
    "relatedform"
]

const componentPortlet: string[] = [
    // 门户:
    "my-carousel",
    "my-container",
    "my-portlet",
    "grid-nav",
    "app",
    "shape",
    "spinner",
    "nav",
    "my-nav",
    "my-nav-page",
    "json-schema",
    "json-schema-editor",
    "my-json-schema",
    "my-json-schema-editor",
    "component-viewer",
    // "my-app",
    // "my-grid-nav",
    // "my-shape",
    // "my-spinner",
]

/**
 * 根据类型获取组件列表。
 * @param type - 类型字符串，如果为 'all'，则返回所有组件列表。
 * @returns 组件列表数组，如果类型不匹配，则返回 undefined。
 */
const showRenderers = (type: string): string[] | undefined => {

    if (type == 'form') {

        return componentForm

    } else if (type == 'dashboard') {
        return componentDashboard
    } else if (type == 'crud') {
        return componentCRUD
    }else if (type == 'portlet') {
        return componentPortlet
    } else {
        return componentAll
    }
};

export { showRenderers }