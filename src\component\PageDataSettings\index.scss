.pageSettingsBox {
  position: relative;
  width: 100%;
  
  padding: 1.5rem;
  display: flex;

  &-aside {
    width: 13.5rem;
    border-right: 1px solid var(--borderColor);

    &-item {
      width: 12.5rem;
      height: 2.5rem;
      border-radius: 0.5rem;
      margin: 0 0.5rem;
      display: flex;
      align-items: center;

      &-img {
        width: 2rem;
        height: 2rem;
        display: block;
        padding: 0.5rem;
      }

      span {
        color: var(--text-color);
        font-size: 0.875rem;
      }
    }
  }

  &-content {
    flex: 1;
    min-height: 80vh;

    &-title {
      width: calc(100% - 3rem);
      min-height: 4.5rem;
      padding: 0.75rem 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin: 0 1.25rem;
      margin-top: 1.875rem;

      &-name {
        font-size: 1.25rem;
        color: var(--text-color);
      }
      &-desc {
        color: var(--text--muted-color);
        font-size: 0.875rem;
      }
    }

    &-item {
      width: calc(100% - 3rem);
      min-height: 6.875rem;
      padding: 1.25rem 0;
      margin: 0 1.25rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--borderColor);

      &-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &-name {
          font-size: 0.875rem;
          color: var(--text-color);
          padding-bottom: 0.375rem;
        }

        &-content {
          font-size: 0.875rem;
          color: var(--text--muted-color);
          padding-top: 0.375rem;

          &-input {
            width: 20rem;
            height: 2rem;
            background-color: var(--light-bg);
            font-size: 0.875rem;
            color: var(--text-color);
            border: none;
            outline: none;
            padding: 0;
            margin: 0;
            padding: 0 1rem;


            ::placeholder {
              color: var(--text--muted-color);
              font-size: 0.875rem;
            }
          }

          &-dataSetItem {
            // width: 100%;
            width: 4rem;
            height: 2rem;
            background-color: var(--light-bg);
            font-size: 0.875rem;
            border-radius: 0.5rem;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            // padding: 0 1rem;
          }
        }

        &-icon {
          padding-top: 0.375rem;
          width: 2.625rem;
          height: 3rem;
          overflow: hidden;
          border-radius: 0.5rem;

          &-img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        &-hint {
          // font-size: 0.875rem;
          margin-top: 0.375rem;
          width: 3.25rem;
          height: 1.5rem;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          border-radius: 0.15rem;
          color: var(--text-color);
          font-size: 0.8rem;
        }
      }

      &-right {
        width: 6.875rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;

        &-btn {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 6rem;
          height: 2.5rem;
          border-radius: 0.5rem;
          border: 1px solid var(--borderColor);
          color: var(--text-color);
          font-size: 1rem;

          &:hover {
            opacity: 0.8;
            background-color: var(--light-bg);
          }
        }
      }
    }
  }
}

.active_item {
  background-color: var(--light-bg);
}

.transfer-container {
  flex-direction: column !important;
  align-items: flex-start !important;
  
  .transfer-header {
    width: 100%;
    margin-bottom: 1rem;
    display: block;
    white-space: normal;

    &-left {
      &-name {
        font-size: 0.875rem;
        color: var(--text-color);
      }

      &-hint {
        color: var(--text--muted-color);
        font-size: 0.8rem;
      }
    }
  }

  .transfer-body {
    width: 100%;

    // 修改amis Transfer组件的样式
    :global {
      .cxd-Transfer {
        flex-direction: column;
        
        &-select {
          width: 100% !important;
          margin: 0.5rem 0;
        }

        &-arrow {
          transform: rotate(90deg);
          margin: 0.5rem 0;
        }
      }
    }
  }
}
