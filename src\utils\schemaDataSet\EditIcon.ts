// 模块级别的变量

// 设置当前Logo的函数


export const EditIcon = (props: { id?: number; logo?: string }) => {
  var   count=100
  let   iconTemplate= `<span style="color:<%= data.iconColor || "#000000" %>;background:<%= data.iconBg || "#F5F7FA" %>;width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle"><%= data.icon.slice(1, -1) %></span>`;

  const getLogoContent = () => {
    // 从表单数据中获取 iconColor

    if (!props.logo ){
        return iconTemplate;
    }
    return props.logo.startsWith("http")
      ? `<% if( (data.resetIcon || data.icon || data.iconColor || data.iconBg)) { %><span style="color:<%= data.iconColor || "#000000" %>;background:<%= data.iconBg || "#F5F7FA" %>;width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle"><%= data.icon.slice(1, -1) %></span><% }else{ %><img src="${props.logo}" style="width:36px;height:36px;border-radius:8px;"/><% } %>`
      : `<% if( (data.resetIcon || data.icon || data.iconColor || data.iconBg)) { %><span style="color:<%= data.iconColor || "#000000" %>;background:<%= data.iconBg || "#F5F7FA" %>;width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle"><%= data.icon.slice(1, -1) %></span><% }else{ %>${props.logo}<% } %>`;
  };
  

  return {
    type: 'dialog',
    title: '修改应用图标',
    data: {
      id: props.id,
      logo: props.logo
    },
    body: {
      type: 'form',
      id: 'icon-form',
      body: [
        {
          type: 'tpl',
          tpl: getLogoContent(),
          className: 'text-left',
        },
        {
          type: 'button',
          label: '重置图标',
          level: 'default',
          className: 'reset-btn text-right',
          wrapperComponent: 'div',
          style: {
            height: '36px',
            background: '#fff',
            border: '1px solid #E5E6EB',
            float: 'right',
            color: '#1D2129',
            borderRadius: '6px',
            fontWeight: 500,
            fontSize: '14px',
            marginBottom: '16px',
            verticalAlign: 'middle'
          },
          onEvent: {
            click: {
              actions: [
                {
                  actionType: 'setValue',
                  componentId: 'icon-form',
                  args: {
                    value: {
                      icon: '',
                      iconColor: '',
                      iconBg: '',
                      resetIcon: true
                    }
                  }
                }
              ]
            }
          }
        },

        {
          type: 'icon-selector',
          name: 'icon',
          label: '图标',
          placeholder: '请选择图标',
          labelClassName: 'font-normal text-left',
          className: 'text-left',
          returnSvg: true,
        },
        {
          type: 'input-color',
          name: 'iconColor',
          //value: '#000000',
          label: '图标颜色',
          placeholder: '请选择颜色',
          labelClassName: 'font-normal text-left',
          className: 'text-left'
        },
        {
          type: 'input-color',
          name: 'iconBg',
          label: '图标背景',
          placeholder: '请选择背景颜色',
          labelClassName: 'font-normal text-left',
          className: 'text-left'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel'
      },
      {
        type: 'button',
        label: '保存',
        level: 'primary',
        actionType: 'submit'
      }
    ]
  };
};