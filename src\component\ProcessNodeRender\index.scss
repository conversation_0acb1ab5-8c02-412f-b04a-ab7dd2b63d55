.process-node-render {
  padding: 0 0 6px 0;
  position: relative;

  .process-node-error {
    color: #ff4d4f;
  }

  .node-title {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;

    .required {
      color: #ff4d4f;
      margin-right: 4px;
    }
  }

  .node-desc {
    font-size: 12px;
    color: #a8adaf;
    max-width: 150px; // 根据实际UI调整
    overflow-x: auto;
  }

  .user-list {
    position: absolute;
    overflow-x: auto; // 建议用 auto
    right: -5px;
    top: -8px;
    max-width: 250px;
    padding: 0;
    margin: 0;

    &.has-add-btn {
      right: 43px;
    }

    .avatar-list {
      position: static;
      right: -5px;
      top: -10px;
      display: flex;
      align-items: center;
      z-index: 2;

      &.has-add-btn {
        right: 43px;
      }
    }
  }

  .add-user {
    position: absolute;
    right: 0;
    top: -10px;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    i {
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      border-radius: 50%;
      border: 1px dashed #1677ff;
      color: #1677ff;
      transition: all 0.3s;

      &:hover {
        background-color: #e6f4ff;
      }
    }

    div {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }
}

.shake-tip {
  animation: shake 1s ease-in-out;
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }
  30%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
  50% {
    transform: translate3d(-4px, 0, 0);
  }
}

.node-users {
  display: flex;
  align-items: center;
  position: absolute;
  right: 24px;
  top: 0;
  height: 40px;
}

.node-user-avatar-wrap {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.node-user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  background: #e6e6e6;
}

.node-user-nickname {
  margin-left: 6px;
  color: #333;
  font-size: 14px;
  max-width: 80px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
