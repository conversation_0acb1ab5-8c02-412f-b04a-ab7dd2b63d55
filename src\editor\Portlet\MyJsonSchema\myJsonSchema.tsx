import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class MyJsonSchemaPlugin extends BasePlugin {
  static id = 'MyJsonSchemaPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'json-schema';
  $schema = '/schemas/JSONSchemaSchema.json';

  // 组件基本信息
  name = 'JSON Schema';
  panelTitle = 'JSON Schema';
  icon = 'fa fa-code';
  panelIcon = 'fa fa-code';
  pluginIcon = 'fa fa-code';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于展示和编辑 JSON Schema 数据';
  docLink = '/amis/zh-CN/components/form/json-schema';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'json-schema',
    name: 'jsonSchema',
    label: 'JSON Schema'
  };

  // 预览界面
  previewSchema = {
    type: 'json-schema',
    name: 'jsonSchema',
    label: 'JSON Schema'
  };

  // 添加事件定义
  events = [
    {
      eventName: 'change',
      eventLabel: '值变化',
      description: '当值变化时触发',
      // ...
    }
  ];

  // 面板配置
  // panelBodyCreator = (context: any) => {
  //   return getSchemaTpl('tabs', [
  //     {
  //       title: '属性',
  //       body: getSchemaTpl(
  //         'collapseGroup',
  //         [
  //           {
  //             title: '基本',
  //             body: [
  //               getSchemaTpl('formItemName', {
  //                 required: true
  //               }),
  //               getSchemaTpl('label'),
  //               {
  //                 type: 'textarea',
  //                 name: 'schema',
  //                 label: 'JSON Schema',
  //                 description: '支持关联上下文数据',
  //                 visibleOn: '!this.schemaApi'
  //               },
  //               {
  //                 type: 'input-text',
  //                 name: 'schemaApi',
  //                 label: 'Schema 接口',
  //                 description: '通过接口获取 Schema，接口返回的是对象',
  //                 visibleOn: '!this.schema'
  //               },
  //               {
  //                 type: 'switch',
  //                 name: 'formula.useFormula',
  //                 label: '启用公式编辑器',
  //                 value: false
  //               },
  //               {
  //                 type: 'combo',
  //                 name: 'formula',
  //                 visibleOn: 'this.formula && this.formula.useFormula',
  //                 label: '公式编辑器配置',
  //                 items: [
  //                   {
  //                     type: 'input-text',
  //                     name: 'placeholder',
  //                     label: '占位符'
  //                   },
  //                   {
  //                     type: 'switch',
  //                     name: 'evalMode',
  //                     label: '表达式模式'
  //                   }
  //                 ]
  //               }
  //             ]
  //           },
  //           {
  //             title: '状态',
  //             body: [
  //               ...getSchemaTpl('status').body
  //             ]
  //           }
  //         ],
  //         {...context?.schema, configTitle: 'props'}
  //       )
  //     },
  //     {
  //       title: '外观',
  //       body: getSchemaTpl(
  //         'collapseGroup',
  //         getSchemaTpl('style:common', {
  //           exclude: ['layout']
  //         }),
  //         {...context?.schema, configTitle: 'style'}
  //       )
  //     },
  //     {
  //       title: '事件',
  //       className: 'p-none',
  //       body: [
  //         getSchemaTpl('eventControl', {
  //           name: 'onEvent',
  //           ...getEventControlConfig(this.manager, context)
  //         })
  //       ]
  //     }
  //   ]);
  // };
}

// 注册插件
registerEditorPlugin(MyJsonSchemaPlugin);
