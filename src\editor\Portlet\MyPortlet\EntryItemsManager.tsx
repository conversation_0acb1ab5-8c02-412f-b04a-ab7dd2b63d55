import React, {FC, useEffect, useRef, useState} from 'react';
import './index.scss';
import {Button, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {DndProvider, useDrag, useDrop} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';
import {EditEntryIcon} from '@/utils/schemaDataSet/EditEntryIcon';
// 定义拖拽项类型
const ItemTypes = {
  ENTRY_ITEM: 'entryItem'
};

// 入口项接口定义
interface EntryItem {
  title: string;
  icon?: string;
  iconBg?: string;
  iconColor?: string;
  href?: string;
  openType?: string; // 允许任意字符串类型
  internalPage?: string;
  id?: number;
  group?: string;
}

interface EntryItemsManagerProps {
  appId: any;
  context: any;
  entries: EntryItem[];
  handleEntries: (entries: EntryItem[]) => void;
}

let iconTemplate = `<span style="color:<%= data.iconColor || "#000000" %>;background:<%= data.iconBg || "#F5F7FA" %>;width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle">
            <% if (typeof data.icon === 'string' && data.icon.length >= 2&& data.icon.startsWith("'") && data.icon.endsWith("'")) { %>
  <%= data.icon.slice(1, -1) %>
<% } else if (typeof data.icon === 'string' && data.icon.length >= 2&& !data.icon.startsWith("'")&& !data.icon.endsWith("'")) { %>
  <%= data.icon %>
<% }  %>
</span>`;

const getLogoContent = () => {
  // 从表单数据中获取 iconColor
  return iconTemplate;
};

// 入口项组件
const EntryItemRow: FC<any> = ({item, index, onEdit, onDelete, onMove}) => {
  // 设置拖拽源
  const [{isDragging}, drag] = useDrag({
    type: ItemTypes.ENTRY_ITEM,
    item: () => ({
      index
    }),
    collect: monitor => ({
      isDragging: monitor.isDragging()
    })
  });

  // 设置放置目标
  const [{isOver, canDrop}, drop] = useDrop({
    accept: ItemTypes.ENTRY_ITEM,
    drop: (draggedItem: any, monitor) => {
      if (monitor.didDrop()) {
        return;
      }

      if (draggedItem.index !== index) {
        onMove(draggedItem.index, index);
      }
    },
    canDrop: (draggedItem: any) => {
      return draggedItem.index !== index;
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  // 合并拖放引用
  const ref = useRef(null);
  drag(drop(ref));

  // 渲染图标函数
  const renderIcon = () => {
    if (!item.icon) return null;

    // 检查是否是SVG字符串
    if (typeof item.icon === 'string' && item.icon.includes('<svg')) {
      // 使用dangerouslySetInnerHTML渲染SVG
      return (
        <span
          className="item-icon"
          style={{
            color: item.iconColor || '',
            background: item.iconBg || '',
            width: '24px',
            height: '24px',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '4px',
            fontSize: '18px',
            marginLeft: '8px'
          }}
          dangerouslySetInnerHTML={{__html: item.icon}}
        />
      );
    }

    // 其他类型的图标
    return (
      <span className="item-icon">
        <i
          className={item.icon}
          style={{
            color: item.iconColor || '',
            background: item.iconBg || '',
            width: '24px',
            height: '24px',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '4px',
            fontSize: '18px',
            marginLeft: '8px'
          }}
        ></i>
      </span>
    );
  };

  // 应用样式
  const opacity = isDragging ? 0.4 : 1;
  const backgroundColor =
    isOver && canDrop ? 'rgba(0, 255, 0, 0.1)' : 'transparent';

  return (
    <div
      ref={ref}
      className="entry-item-row"
      style={{opacity, backgroundColor}}
      key={`entry-item-${index}`}
    >
      <div className="entry-item-info">
        <i
          className="fa fa-bars"
          style={{cursor: 'move', marginRight: '8px'}}
        ></i>
        <span className="item-title">
          {`${item.title || '未命名入口'}`}
          {/* {item.group && <span className="item-group" style={{color: '#86909c', fontSize: '12px', marginLeft: '8px'}}>
            ({item.group})
          </span>} */}
        </span>
        {/* {item.icon && renderIcon()} */}
      </div>
      <div className="entry-item-actions">
        <Button level="link" size="sm" onClick={() => onEdit(index)}>
          <i className="fa fa-edit"></i>
        </Button>
        <Button level="link" size="sm" onClick={() => onDelete(index)}>
          <i className="fa fa-trash"></i>
        </Button>
      </div>
    </div>
  );
};

const EntryItemsManager: FC<EntryItemsManagerProps> = (
  props: EntryItemsManagerProps
) => {
  const [refreshListKey, setRefreshListKey] = React.useState(0);
  const [refreshInnerListKey, setRefreshInnerListKey] = React.useState(0);
  const [entryItems, setEntryItems] = React.useState<EntryItem[]>([]);
  const [currentEditIndex, setCurrentEditIndex] = React.useState<number>(-1);
  const [currentEditId, setCurrentEditId] = React.useState<string>('');
  const [showEditDialog, setShowEditDialog] = React.useState<boolean>(false);
  const [isAddMode, setIsAddMode] = React.useState<boolean>(false);
  const [tabOptions, setTabOptions] = useState<
    Array<{label: string; value: string}>
  >([]);
  // 批量编辑状态
  const [showBatchEditDialog, setShowBatchEditDialog] =
    React.useState<boolean>(false);
  const [batchItems, setBatchItems] = React.useState<EntryItem[]>([]);
  const isMounted = useRef(true);
  const isInitialized = useRef(false);

  // 添加状态
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [deleteIndex, setDeleteIndex] = useState(-1);
  const [deleteItemId, setDeleteItemId] = React.useState<any>(null);
  const [deleteRow, setDeleteRow] = React.useState<any>(null);

  // 添加图标样式编辑对话框状态
  const [showIconStyleDialog, setShowIconStyleDialog] =
    React.useState<boolean>(false);
  const [currentIconStyle, setCurrentIconStyle] = React.useState<any>({});

  // 添加一个新状态来保存当前表单的所有值
  const [currentFormValues, setCurrentFormValues] = React.useState<any>({});

  // 处理图标保存
  const handleIconSave = (values: any) => {
    console.log('处理图标保存:', values);

    // 如果没有values或者是初始化调用，不做处理
    if (!values || !Array.isArray(values) || values.length === 0) {
      console.log('无效的数据格式，取消更新');
      return;
    }

    const iconData = values[0];

    console.log('handleIconSave 接收到的数据:', JSON.stringify(iconData));

    // 确保获取到有效的图标数据
    if (!iconData) {
      console.log('无图标数据，取消更新');
      return;
    }

    // 处理图标文本，移除可能的引号
    let iconValue = '';
    if (typeof iconData.icon === 'string') {
      // 移除可能的外部引号
      iconValue = iconData.icon.replace(/^['"]|['"]$/g, '');
      console.log('处理后的图标值:', iconValue);
    } else if (iconData.icon) {
      iconValue = iconData.icon;
      console.log('非字符串图标:', iconValue);
    }

    const iconBgValue = iconData.iconBg || '';
    const iconColorValue = iconData.iconColor || '';

    console.log('处理后的图标数据:', {
      icon: iconValue,
      iconBg: iconBgValue,
      iconColor: iconColorValue
    });

    // 更新当前图标样式状态
    setCurrentIconStyle({
      icon: iconValue,
      iconBg: iconBgValue,
      iconColor: iconColorValue
    });

    // 检查是否在批量编辑模式下
    if (currentEditId && showBatchEditDialog) {
      console.log('批量编辑模式 - 更新图标样式, ID:', currentEditId);
      
      // 更新批量编辑项
      const updatedBatchItems = batchItems.map(item => {
        if (item.id === Number(currentEditId)) {
          console.log(`找到匹配的批量编辑项 ID:${item.id}`, item);
          return {
            ...item,
            icon: iconValue,
            iconBg: iconBgValue,
            iconColor: iconColorValue
          };
        }
        return item;
      });
      
      // 更新批量编辑状态
      setBatchItems(updatedBatchItems);
      
      // 关闭图标样式对话框
      setShowIconStyleDialog(false);
      
      // 更新刷新键，触发表格重新渲染
      setRefreshListKey(prev => prev + 1);
      
      
      // 重置当前编辑ID
      setCurrentEditId('');
      
      return; // 提前返回，不执行后面的逻辑
    }

    // 编辑当前正在处理的入口项，更新其图标样式
    if (currentEditIndex >= 0 && currentEditIndex < entryItems.length) {
      // 创建一个全新的数组，确保引用变化
      const updatedEntryItems = entryItems.map((item, idx) => {
        if (idx === currentEditIndex) {
          // 为当前编辑的项创建一个全新的对象
          return {
            ...item,
            icon: iconValue,
            iconBg: iconBgValue,
            iconColor: iconColorValue
          };
        }
        return {...item}; // 为其他项也创建新对象，确保引用变化
      });

      console.log(
        '更新前的entryItems[currentEditIndex]:',
        entryItems[currentEditIndex]
      );
      console.log(
        '更新后的updatedEntryItems[currentEditIndex]:',
        updatedEntryItems[currentEditIndex]
      );
  

      // 更新本地状态
      setEntryItems(updatedEntryItems);

      // 更新全局状态
      props.handleEntries(JSON.parse(JSON.stringify(updatedEntryItems)));
      
    } else if (currentEditId) {
      console.log('普通编辑模式 - 更新图标样式, ID:', currentEditId);
      // 创建一个全新的数组，确保引用变化
      const updatedEntryItems = entryItems.map((item, idx) => {
        // 直接比较ID和索引+1，因为批量编辑中ID是从1开始的
        if (idx === Number(currentEditId) - 1) {
          console.log('找到匹配的项:', item);
          return {
            ...item,
            icon: iconValue,
            iconBg: iconBgValue,
            iconColor: iconColorValue
          };
        }
        return { ...item };
      });

      // 更新本地状态
      setEntryItems(updatedEntryItems);

      // 更新全局状态
      props.handleEntries(JSON.parse(JSON.stringify(updatedEntryItems)));
      
    } else if (
      isAddMode ||
      (currentEditIndex >= 0 && currentEditIndex >= entryItems.length)
    ) {
      // 新增模式 - 当用户正在新增一条记录时
      console.log('新增入口项模式 - 设置图标');

      // 保存图标数据到当前样式，这样在后续提交新增表单时能获取到
      setCurrentIconStyle({
        icon: iconValue,
        iconBg: iconBgValue,
        iconColor: iconColorValue
      });

      // 如果编辑对话框已经打开，可以尝试直接更新对话框中的表单数据
      if (showEditDialog) {
        console.log('编辑对话框已打开，准备刷新');
        // 强制刷新编辑对话框，确保新数据能应用最新的图标
        setRefreshListKey(prev => prev + 1);
      }
      
    } else {
      console.log('无效的编辑状态，无法更新图标');
    }

    // 关闭图标样式对话框
    setShowIconStyleDialog(false);

    // 如果编辑对话框是打开的，强制刷新编辑对话框
    if (showEditDialog) {
      // 保存当前状态
      const currentIdx = currentEditIndex;
      const wasInAddMode = isAddMode;
      const savedFormValues = {...currentFormValues}; // 保存当前表单值的副本

      console.log('保存的表单值(包括分组):', savedFormValues);

      // 关闭对话框
      setShowEditDialog(false);

      // 短暂延迟后重新打开对话框
      setTimeout(() => {
        setCurrentEditIndex(currentIdx);
        setIsAddMode(wasInAddMode);
        // 更新当前表单值，保留之前的值但更新图标相关字段
        setCurrentFormValues({
          ...savedFormValues,
          icon: iconValue,
          iconBg: iconBgValue,
          iconColor: iconColorValue
        });

        console.log('重新打开对话框时的表单值:', {
          ...savedFormValues,
          icon: iconValue,
          iconBg: iconBgValue,
          iconColor: iconColorValue
        });

        setShowEditDialog(true);
      }, 100);
    }
    
    // 重置当前编辑ID
    setCurrentEditId('');
  };

  // 确认删除
  const handleConfirmDelete = () => {
    if (deleteIndex < 0 && !deleteItemId) return;
    if (deleteItemId) {
      const newItems = batchItems.filter(item => item.id !== deleteRow.id);
      setBatchItems(newItems);
      setDeleteItemId(null);
      setDeleteRow(null);
      setRefreshListKey(prev => prev + 1);
    } else {
      // 删除操作
      const newEntryItems = entryItems.filter((_, i) => i !== deleteIndex);
      // 更新本地状态
      setEntryItems(newEntryItems);
      // 更新全局状态
      props.handleEntries(JSON.parse(JSON.stringify(newEntryItems)));
      setDeleteIndex(-1);
    }
    setShowConfirmDialog(false);
    toast.success('删除成功');
  };
  // 组件挂载状态跟踪
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
      setShowEditDialog(false);
      setCurrentEditIndex(-1);
      setIsAddMode(false);
    };
  }, []);

  // 打开批量编辑对话框
  const handleBatchEdit = () => {
    if (!isMounted.current) return;

    try {
      // 准备批量编辑数据，为每个项目添加序号
      const batchData = entryItems.map((item, index) => ({
        ...item,
        id: index + 1 // 添加ID用于CRUD
        // 转换blank为下拉选择的值
        // openType: item.blank ? 'external' : 'internal'
      }));
      console.log('准备批量编辑数据:', batchData);

      // 先设置批量编辑数据
      setBatchItems(batchData);

      // 使用setTimeout延迟显示对话框，延长时间以确保状态更新完成
      setTimeout(() => {
        if (isMounted.current) {
          // 再次检查确保batchItems已正确设置
          console.log('打开对话框时的数据状态:', batchData.length);
          setShowBatchEditDialog(true);
        }
      }, 100); // 增加延时确保状态更新
    } catch (error) {
      console.error('打开批量编辑对话框失败:', error);
      toast.error('打开批量编辑对话框失败');
    }
  };

  // 批量编辑对话框配置
  const getBatchEditDialogSchema = () => {
    let dataOption: any[] = [];
    console.log('准备获取批量编辑对话框配置', batchItems);
    try {
      // 复制一份数据，避免直接引用state可能导致的问题
      const safeItems = JSON.parse(JSON.stringify(batchItems || []));
      console.log('将要传递给CRUD的安全数据:', safeItems);

      // 生成一个唯一的时间戳，确保每次渲染都是全新的
      const timestamp = Date.now();

      return {
        type: 'dialog',
        title: '批量编辑入口',
        size: 'lg',
        closeOnEsc: true,
        closeOnOutside: false,
        className: 'carousel-batch-edit-dialog',
        id: `batch-edit-dialog-${timestamp}`, // 添加唯一ID
        data: {
          items: safeItems,
          _timestamp: timestamp // 添加时间戳到数据中，确保AMIS重新渲染
        },
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'close'
          },
          {
            type: 'button',
            label: '确认',
            level: 'primary',
            actionType: 'submit',
            // 直接将确认按钮与表单关联
            target: 'batch-edit-form'
          }
        ],
        body: {
          type: 'form',
          id: 'batch-edit-form',
          submitOnChange: false,
          submitText: '',
          wrapWithPanel: false,
          onSubmit: (values: any) => {
            // 获取表单中的数据
            const formData = values.items || [];
            if (Array.isArray(formData) && formData.length > 0) {
              // 直接转换并保存数据
              const newEntryItems = formData.map((item: any) => {
                let processedIcon = item.icon || '';
                if (processedIcon && typeof processedIcon === 'string') {
                  let iconStr = processedIcon.trim();
                  // 清理字符串，移除可能存在的外层单引号或双引号
                  if (
                    (iconStr.startsWith("'") && iconStr.endsWith("'")) ||
                    (iconStr.startsWith('"') && iconStr.endsWith('"'))
                  ) {
                    iconStr = iconStr.substring(1, iconStr.length - 1).trim();
                  }
                  processedIcon = iconStr;
                }
                return {
                  title: item.title || '',
                  icon: processedIcon,
                  iconBg: item.iconBg || '',
                  iconColor: item.iconColor || '',
                  href: item.href || '',
                  openType: item.openType || '',
                  internalPage: item.internalPage || '',
                  group: item.group || ''
                };
              });
              // 先关闭对话框
              setShowBatchEditDialog(false);

              // 更新当前组件状态
              setEntryItems(newEntryItems);

              // 更新父组件状态
              props.handleEntries(JSON.parse(JSON.stringify(newEntryItems)));

              toast.success('批量更新成功');
            } else {
              // toast.error('没有获取到有效的表单数据');

              // 先关闭对话框
              setShowBatchEditDialog(false);

              // 更新当前组件状态
              setEntryItems([]);

              // 更新父组件状态
              props.handleEntries(JSON.parse(JSON.stringify([])));
              toast.success('批量更新成功');
            }

            return false;
          },
          body: [
            {
              type: 'crud',
              name: 'items',
              source: '${items}',
              syncLocation: false,
              api: null,
              id: 'batch-edit-crud',
              perPage: 100,
              headerToolbar: [],
              footerToolbar: [
                {
                  type: 'pagination',
                  align: 'right'
                }
              ],
              columns: [
                {
                  name: 'id',
                  label: '序号',
                  width: 60
                },
                {
                  type: 'tpl',
                  name: 'icon',
                  label: '图标',
                  tpl: `
                  <div style="color:<%= data.iconColor || '#000000' %>;background:<%= data.iconBg || '#F5F7FA' %>;width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:24px;">
                    <% if (typeof data.icon === 'string' && data.icon.includes('<svg')) { %>
                      <%= data.icon %>
                    <% } else if (data.icon) { %>
                      <i class="<%= data.icon %>"></i>
                    <% } %>
                  </div>
                  `,
                },
                {
                  name: 'title',
                  label: '入口名称',
                  type: 'static',
                  mode: 'inline'
                },
                {
                  name: 'group',
                  label: '分组',
                  type: 'static',
                  mode: 'inline'
                },
                {
                  type: 'static',
                  name: 'openType',
                  label: '入口类型',
                  tpl: '${openType === "internal" ? "打开内部页面" : "打开外部链接"}'
                },
                {
                  type: 'static',
                  name: 'href',
                  label: '链接地址',
                  tpl: '${openType === "external"? href : internalName}'
                },
                {
                  type: 'operation',
                  label: '操作',
                  width: 180,
                  buttons: [
                    {
                      type: 'button',
                      label: '查看',
                      level: 'link',
                      actionType: 'dialog',
                      dialog: {
                        title: '查看入口',
                        body: {
                          type: 'form',
                          body: [
                            {
                              type: 'static-tpl',
                              name: 'icon',
                              label: '图标'
                            },
                            {
                              type: 'static',
                              name: 'title',
                              label: '入口名称'
                            },
                            {
                              type: 'static',
                              name: 'group',
                              label: '分组'
                            },
                            {
                              type: 'static',
                              name: 'openType',
                              label: '入口类型',
                              tpl: '${openType === "internal" ? "打开内部页面" : "打开外部链接"}'
                            },
                            {
                              type: 'static',
                              name: 'href',
                              label: '链接地址',
                              tpl: '${openType === "external"? href : internalName}'
                            }
                          ]
                        }
                      }
                    },
                    {
                      type: 'button',
                      label: '编辑',
                      level: 'link',
                      actionType: 'dialog',
                      dialog: {
                        title: '编辑入口',
                        size: 'md',
                        body: {
                          type: 'form',
                          wrapWithPanel: false,
                          submitText: '确定',
                          body: [
                            {
                              type: 'hidden',
                              name: 'id'
                            },
                            {
                              type: 'tpl',
                              tpl: getLogoContent(),
                              className: 'text-left'
                            },
                            {
                              type: 'button',
                              label: '修改图标样式',
                              level: 'default',
                              className: 'reset-btn text-right',
                              wrapperComponent: 'div',
                              style: {
                                height: '36px',
                                background: '#fff',
                                border: '1px solid #E5E6EB',
                                float: 'right',
                                color: '#1D2129',
                                borderRadius: '6px',
                                fontWeight: 500,
                                fontSize: '14px',
                                marginBottom: '16px',
                                verticalAlign: 'middle'
                              },
                              onClick: (e: any, props: any) => {
                                console.log(
                                  '修改图标样式按钮被点击, 当前表单数据:',
                                  props.data
                                );

                                // 关闭添加模式
                                setIsAddMode(false);

                                // 先设置索引
                                setCurrentEditId(props.data.id);

                                // 保存当前表单的所有值
                                setCurrentFormValues(props.data);

                                // 设置当前图标样式并打开对话框
                                setCurrentIconStyle({
                                  icon: props.data.icon || '',
                                  iconBg: props.data.iconBg || '',
                                  iconColor: props.data.iconColor || ''
                                });

                                // 延迟打开图标样式对话框，确保数据已设置
                                setTimeout(() => {
                                  setShowIconStyleDialog(true);
                                }, 0);
                              }
                            },
                            {
                              type: 'icon-selector',
                              name: 'icon',
                              label: '图标',
                              placeholder: '请选择图标',
                              returnSvg: true
                            },
                            {
                              type: 'hidden',
                              name: 'icon'
                            },
                            {
                              type: 'hidden',
                              name: 'iconBg'
                            },
                            {
                              type: 'hidden',
                              name: 'iconColor'
                            },
                            {
                              type: 'input-text',
                              name: 'title',
                              label: '入口名称',
                              placeholder: '请输入入口名称'
                            },
                            {
                              type: 'select',
                              name: 'group',
                              label: '分组',
                              value: 'group',
                              options: tabOptions
                            },
                            {
                              type: 'select',
                              name: 'openType',
                              label: '入口类型',
                              value: 'external',
                              options: [
                                {
                                  label: '打开内部页面',
                                  value: 'internal'
                                },
                                {
                                  label: '打开外部链接',
                                  value: 'external'
                                }
                              ]
                            },
                            {
                              type: 'select',
                              name: 'internalPage',
                              label: '选择页面',
                              source: {
                                method: 'GET',
                                url: `/admin-api/system/application-page-and-class/list?applicationId=${props.appId}&applicantOrBackend=1`,
                                adaptor: (payload: any) => {
                                  // 过滤type=1的数据
                                  const filteredData = payload.data.filter(
                                    (item: any) => item.type === 1
                                  );
                                  const options = filteredData.map(
                                    (item: any) => ({
                                      label: item.name,
                                      value: item.id
                                    })
                                  );
                                  dataOption = options;
                                  return {
                                    ...payload,
                                    data: {
                                      options: options
                                    }
                                  };
                                }
                              },
                              onChange: (
                                value: any,
                                oldValue: any,
                                model: any,
                                form: any
                              ) => {
                                if (!value) return;

                                // 查找选中的数据源项
                                let selectedOption = dataOption.find(
                                  item => item.value == value
                                );
                                console.log('选中的数据源项2:', selectedOption);
                                if (selectedOption && selectedOption.label) {
                                  // 保存关联的数据表ID
                                  form.setValueByName(
                                    'internalName',
                                    selectedOption.label
                                  );
                                }
                              },
                              visibleOn: 'this.openType === "internal"'
                            },
                            {
                              type: 'input-text',
                              name: 'href',
                              label: '链接地址',
                              placeholder: '请输入链接地址',
                              visibleOn: 'this.openType !== "internal"'
                            },
                            {
                              type: 'hidden',
                              name: 'internalName'
                            }
                          ],
                          onSubmit: (values: any, action: any) => {
                            try {
                              console.log('编辑表单提交:', values);
                              // 本地更新数据
                              const updatedItems = batchItems.map(item => {
                                if (item.id === values.id) {
                                  return {...item, ...values};
                                }
                                return item;
                              });

                              // 更新状态
                              setBatchItems(updatedItems);

                              setRefreshListKey(prev => prev + 1);
                              // 关闭对话框
                              return true;
                            } catch (error) {
                              console.error('编辑处理失败:', error);
                              toast.error('编辑失败');
                              return false;
                            }
                          }
                        }
                      }
                    },
                    {
                      type: 'button',
                      label: '删除',
                      level: 'link',
                      className: 'text-danger',
                      // confirmText: '确认要删除该轮播图吗？',
                      onClick: (e: any, props: any) => {
                        try {
                          console.log('删除操作，数据:', props);
                          const row = props?.data;

                          if (!row || row.id === undefined) {
                            toast.error('无法获取行数据');
                            return;
                          }

                          console.log('删除操作，数据:', row);

                          // 检查是否至少保留一项
                          // if (batchItems.length <= 1) {
                          //   toast.error('至少保留一个轮播项');
                          //   return;
                          // }

                          setDeleteItemId(row.id);
                          setDeleteRow(row);
                          setShowConfirmDialog(true);
                          // 使用React状态更新方法来修改数据
                          // const newItems = batchItems.filter(
                          //   item => item.id !== row.id
                          // );
                          // setBatchItems(newItems);
                          // setRefreshListKey(prev => prev + 1);

                          // toast.success('删除成功');
                        } catch (error) {
                          console.error('删除操作失败:', error);
                          toast.error('删除失败：' + (error as Error).message);
                        }
                      }
                    }
                  ]
                }
              ]
            }
          ]
        }
      };
    } catch (error) {
      console.error('获取批量编辑对话框配置失败:', error);
      return {};
    }
  };

  // 直接从 amis-editor 的 context 中获取最新的标签页数据
  const getLatestTabsFromContext = () => {
    let latestTabs = [];
    try {
      // 尝试获取当前节点
      const node = props.context?.node;
      if (node && node.id) {
        // 从 store 中获取最新的值
        if (node.getComponent && props.context?.manager?.store) {
          const store = props.context.manager.store;
          const data = store.data;

          // 查找当前节点的最新数据
          if (data && data.components) {
            const currentComponent = data.components[node.id];
            if (
              currentComponent &&
              currentComponent.schema &&
              Array.isArray(currentComponent.schema.tabs)
            ) {
              latestTabs = currentComponent.schema.tabs;
              console.log('通过store获取到最新标签页数据:', latestTabs);
            }
          }
        }
      }

      // 如果上面的方法无法获取，尝试从当前上下文中获取
      if (!latestTabs.length && props.context?.schema?.tabs) {
        latestTabs = props.context.schema.tabs;
        console.log('通过context获取到标签页数据:', latestTabs);
      }

      return latestTabs;
    } catch (error) {
      console.error('获取最新标签页数据失败:', error);
      return props.context?.schema?.tabs || [];
    }
  };

  // 强制刷新标签页选项，并且从上下文中获取最新的标签页数据
  const forceUpdateTabOptions = () => {
    try {
      const latestTabs = getLatestTabsFromContext();
      console.log('强制刷新获取到的标签页数据:', latestTabs);

      // 转换为选项格式，使用索引作为值，方便准确匹配
      const options = latestTabs.map((tab: any, index: number) => ({
        label: tab.title || `标签页${index + 1}`,
        value: index.toString() // 使用索引作为值，确保唯一性
      }));

      console.log('生成的标签页选项:', options);
      setTabOptions([...options]);
    } catch (error) {
      console.error('强制更新标签页选项失败:', error);
      setTabOptions([]);
    }
  };

  // 监听标签页变化，更新标签页选项
  useEffect(() => {
    if (!isMounted.current) return;

    // 每当 context 或者 schema 变化时，重新获取标签页选项
    forceUpdateTabOptions();
  }, [props.context?.schema?.tabs]);

  // 初始化时从props中读取已保存的数据
  React.useEffect(() => {
    if (!isMounted.current) return;

    try {
      // 防止重复初始化，但允许数据更新
      if (props.entries && props.entries.length) {
        console.log('EntryItemsManager接收到新的entries数据:', props.entries);
        // 深拷贝选项，避免直接引用
        const safeEntries = JSON.parse(JSON.stringify(props.entries));
        setEntryItems(safeEntries);

        // 如果是首次初始化，设置标志
        if (!isInitialized.current) {
          isInitialized.current = true;
        }
        return;
      }

      // 如果没有传入数据，检查是否有缓存数据
      if (!isInitialized.current) {
        // 检查是否有保存的静态选项数据
        const savedEntries = props.context?.schema?.entries;
        if (savedEntries && savedEntries.length) {
          console.log('EntryItemsManager从schema中恢复数据:', savedEntries);
          const safeEntries = JSON.parse(JSON.stringify(savedEntries));
          setEntryItems(safeEntries);
          // 更新到主数据中，使用setTimeout避免状态冲突
          setTimeout(() => {
            if (isMounted.current) {
              props.handleEntries(safeEntries);
            }
          }, 0);
          isInitialized.current = true;
        } else {
          // 设置默认入口项
          const defaultEntries: EntryItem[] = [];
          setEntryItems(defaultEntries);
          // 更新到主数据中，使用setTimeout避免状态冲突
          setTimeout(() => {
            if (isMounted.current) {
              props.handleEntries(defaultEntries);
            }
          }, 0);
          isInitialized.current = true;
        }
      }

      // 初始化时也强制更新标签页选项
      forceUpdateTabOptions();
    } catch (error) {
      console.error('初始化入口项数据失败:', error);
      // 设置默认入口项作为备用
      const defaultEntries = [
        {
          title: '默认入口项',
          icon: '',
          iconBg: '',
          iconColor: '',
          openType: 'external'
        }
      ];
      setEntryItems(defaultEntries);
    }
  }, [props.entries]);

  // 添加入口项 - 修改为直接打开编辑对话框
  const handleAddItem = () => {
    if (!isMounted.current) return;

    try {
      // 设置为添加模式
      setIsAddMode(true);

      // 临时放入一个新条目在数组末尾，索引就是当前数组长度
      setCurrentEditIndex(entryItems.length);

      // 重置表单值和图标样式
      setCurrentFormValues({});
      setCurrentIconStyle({});

      // 打开对话框前强制更新标签页选项
      forceUpdateTabOptions();

      // 使用setTimeout延迟显示对话框，避免状态树更新冲突
      setTimeout(() => {
        if (isMounted.current) {
          setShowEditDialog(true);
        }
      }, 50);
    } catch (error) {
      console.error('打开添加对话框失败:', error);
      toast.error('打开添加对话框失败');
    }
  };

  // 删除入口项
  const handleDeleteItem = (index: number) => {
    if (!isMounted.current) return;
    setDeleteIndex(index);
    setShowConfirmDialog(true);
  };

  // 编辑入口项
  const handleEditItem = (index: number) => {
    if (!isMounted.current) return;

    try {
      // 关闭添加模式
      setIsAddMode(false);

      // 先设置索引
      setCurrentEditIndex(index);

      // 打开对话框前强制更新标签页选项
      forceUpdateTabOptions();

      // 使用setTimeout延迟显示对话框，避免状态树更新冲突
      setTimeout(() => {
        if (isMounted.current) {
          setShowEditDialog(true);
        }
      }, 50);
    } catch (error) {
      console.error('打开编辑对话框失败:', error);
      toast.error('打开编辑对话框失败');
    }
  };

  // 保存编辑
  const handleSaveEdit = (values: any) => {
    if (!isMounted.current) return;

    try {
      console.log('保存编辑，接收到的数据:', values);
      console.log('当前图标样式:', currentIconStyle);

      // 确保图标使用正确的格式并与当前图标样式同步
      // 如果表单中没有提交图标数据，则使用当前的图标样式
      if (!values.icon && currentIconStyle.icon) {
        console.log('表单中没有图标数据，使用当前图标样式');
        values.icon = currentIconStyle.icon;
      }

      if (typeof values.icon === 'string') {
        // 移除可能的引号
        values.icon = values.icon.replace(/^['"]|['"]$/g, '');
      }

      // 确保图标背景和颜色与当前样式同步
      if (!values.iconBg && currentIconStyle.iconBg) {
        values.iconBg = currentIconStyle.iconBg;
      }

      if (!values.iconColor && currentIconStyle.iconColor) {
        values.iconColor = currentIconStyle.iconColor;
      }

      // 处理分组数据，将索引转换为标签页标题，方便在渲染时使用
      let groupValue = values.group || '';
      if (groupValue && groupValue !== '') {
        // 尝试将字符串索引转换为数字
        const tabIndex = parseInt(groupValue, 10);
        if (!isNaN(tabIndex)) {
          // 获取最新的标签页数据
          const latestTabs = getLatestTabsFromContext();
          // 检查索引是否有效
          if (tabIndex >= 0 && tabIndex < latestTabs.length) {
            // 使用对应标签页的标题作为分组值
            const tabTitle =
              latestTabs[tabIndex].title || `标签页${tabIndex + 1}`;
            console.log(`将分组索引 ${tabIndex} 转换为标题: ${tabTitle}`);
            values.group = tabTitle;
          }
        }
      }

      let newEntries = [...entryItems];

      if (isAddMode) {
        // 添加模式：将新项添加到数组
        const newEntry = {
          title: values.title || '未命名入口',
          icon: values.icon || '',
          iconBg: values.iconBg || '',
          iconColor: values.iconColor || '',
          openType: values.openType || 'external',
          internalPage: values.internalPage || '',
          href: values.href || '',
          group: values.group || ''
        };

        console.log('添加新入口:', newEntry);
        newEntries.push(newEntry);
      } else {
        // 编辑模式：更新现有项
        if (currentEditIndex >= 0 && currentEditIndex < entryItems.length) {
          newEntries = entryItems.map((entry, index) => {
            if (index === currentEditIndex) {
              const updatedEntry = {...entry, ...values};
              console.log('更新入口项:', updatedEntry);
              return updatedEntry;
            }
            return entry;
          });
        }
      }

      // 先更新本地状态
      setEntryItems(newEntries);

      // 关闭对话框
      setShowEditDialog(false);

      // 使用setTimeout延迟更新全局状态和重置索引，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          // 深拷贝，避免引用问题
          props.handleEntries(JSON.parse(JSON.stringify(newEntries)));
          setCurrentEditIndex(-1);
          setIsAddMode(false);
          // 清空保存的表单值
          setCurrentFormValues({});
          // 强制刷新列表
          setRefreshListKey(prev => prev + 1);
          toast.success(isAddMode ? '添加成功' : '编辑成功');
        }
      }, 50);
    } catch (error) {
      console.error(isAddMode ? '添加失败:' : '编辑保存失败:', error);
      toast.error(isAddMode ? '添加失败' : '编辑保存失败');

      // 关闭对话框并重置索引
      setShowEditDialog(false);
      setTimeout(() => {
        if (isMounted.current) {
          setCurrentEditIndex(-1);
          setIsAddMode(false);
        }
      }, 50);
    }
  };

  // 安全关闭对话框
  const handleCloseDialog = () => {
    if (!isMounted.current) return;

    try {
      // 先关闭对话框
      setShowEditDialog(false);

      // 延迟重置编辑索引和添加模式，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          setCurrentEditIndex(-1);
          setIsAddMode(false);
          // 清空保存的表单值
          setCurrentFormValues({});
        }
      }, 50);
    } catch (error) {
      console.error('关闭对话框失败:', error);
    }
  };

  // 处理拖拽排序
  const handleMoveItem = (dragIndex: number, hoverIndex: number) => {
    if (!isMounted.current) return;

    try {
      const dragItem = entryItems[dragIndex];
      if (!dragItem) return;

      // 创建新数组，避免直接修改原数组
      const newEntries = [...entryItems];

      // 删除拖拽项
      newEntries.splice(dragIndex, 1);

      // 在新位置插入拖拽项
      newEntries.splice(hoverIndex, 0, dragItem);

      // 先更新本地状态
      setEntryItems(newEntries);

      // 使用setTimeout延迟更新全局状态，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          // 深拷贝，避免引用问题
          props.handleEntries(JSON.parse(JSON.stringify(newEntries)));
        }
      }, 0);
    } catch (error) {
      console.error('拖拽排序失败:', error);
      toast.error('拖拽排序失败');
    }
  };

  // 编辑对话框配置
  const getEditDialogSchema = () => {
    try {
      // 获取当前编辑的入口项
      let currentEntry: EntryItem | null = null;

      // 生成一个刷新键，确保每次打开对话框时都能显示最新的图标
      const refreshKey = Date.now();

      // 对于编辑模式，从现有条目中获取数据
      if (
        !isAddMode &&
        currentEditIndex >= 0 &&
        currentEditIndex < entryItems.length
      ) {
        currentEntry = JSON.parse(JSON.stringify(entryItems[currentEditIndex]));
        // 确保图标字符串没有多余的引号
        if (
          currentEntry &&
          currentEntry.icon &&
          typeof currentEntry.icon === 'string'
        ) {
          currentEntry.icon = currentEntry.icon.replace(/^['"]|['"]$/g, '');
        }
      } else {
        // 对于新增模式，使用保存的表单值或默认值
        const defaultValues = {
          title: '',
          icon: currentIconStyle.icon || '',
          iconBg: currentIconStyle.iconBg || '',
          iconColor: currentIconStyle.iconColor || '',
          openType: 'external',
          internalPage: '',
          href: '',
          group: ''
        };

        // 使用保存的表单值覆盖默认值
        currentEntry = {...defaultValues, ...currentFormValues};
        console.log('新增模式，使用保存的表单值:', currentEntry);
      }

      // 找到当前分组对应的标签页索引
      let selectedGroupIndex = '';

      // 先检查保存的表单值是否直接包含分组索引
      if (currentFormValues && currentFormValues.group) {
        console.log('从表单值中获取分组信息:', currentFormValues.group);
        // 如果保存的表单值中的分组已经是索引形式，直接使用
        if (/^\d+$/.test(currentFormValues.group)) {
          selectedGroupIndex = currentFormValues.group;
          console.log('使用保存的表单值中的分组索引:', selectedGroupIndex);
        }
      }

      // 如果没有从表单值中获取到索引，则尝试从分组名称转换
      if (!selectedGroupIndex && currentEntry && currentEntry.group) {
        // 获取最新的标签页数据
        const latestTabs = getLatestTabsFromContext();
        // 查找匹配的标签页
        const tabIndex = latestTabs.findIndex(
          (tab: any, index: number) =>
            tab.title === currentEntry?.group ||
            `标签页${index + 1}` === currentEntry?.group
        );
        if (tabIndex >= 0) {
          selectedGroupIndex = tabIndex.toString();
          console.log('从分组名称转换为索引:', selectedGroupIndex);
        }
      }

      // 确保 currentEntry 不为 null（为了类型安全）
      const safeEntry = currentEntry || {
        title: '',
        icon: '',
        iconBg: '',
        iconColor: '',
        openType: 'external',
        internalPage: '',
        href: '',
        group: ''
      };

      return {
        type: 'dialog',
        title: isAddMode ? '添加入口' : '编辑入口',
        size: 'md',
        closeOnEsc: true,
        closeOnOutside: false,
        data: {
          icon: safeEntry.icon || '',
          iconBg: safeEntry.iconBg || '',
          iconColor: safeEntry.iconColor || '',
          title: safeEntry.title || '',
          openType: safeEntry.openType || 'external',
          internalPage: safeEntry.internalPage || '',
          href: safeEntry.href || '',
          group: selectedGroupIndex || '',
          refreshKey: refreshKey // 添加刷新键，确保每次打开都会刷新
        },
        body: {
          type: 'form',
          mode: 'horizontal',
          submitText: '确认',
          onSubmit: handleSaveEdit,
          body: [
            {
              type: 'tpl',
              tpl: getLogoContent(),
              className: 'text-left'
            },
            {
              type: 'button',
              label: '修改图标样式',
              level: 'default',
              className: 'reset-btn text-right',
              wrapperComponent: 'div',
              style: {
                height: '36px',
                background: '#fff',
                border: '1px solid #E5E6EB',
                float: 'right',
                color: '#1D2129',
                borderRadius: '6px',
                fontWeight: 500,
                fontSize: '14px',
                marginBottom: '16px',
                verticalAlign: 'middle'
              },
              onClick: (e: any, props: any) => {
                console.log(
                  '修改图标样式按钮被点击, 当前表单数据:',
                  props.data
                );

                // 保存当前表单的所有值
                setCurrentFormValues(props.data);

                // 设置当前图标样式并打开对话框
                setCurrentIconStyle({
                  icon: props.data.icon || '',
                  iconBg: props.data.iconBg || '',
                  iconColor: props.data.iconColor || ''
                });

                // 延迟打开图标样式对话框，确保数据已设置
                setTimeout(() => {
                  setShowIconStyleDialog(true);
                }, 0);
              }
            },
            {
              type: 'icon-selector',
              name: 'icon',
              label: '图标　　',
              returnSVG: true,
              placeholder: '请选择图标',
              value: safeEntry.icon
            },
            {
              type: 'hidden',
              name: 'icon',
              value: safeEntry.icon
            },
            {
              type: 'hidden',
              name: 'iconBg',
              value: safeEntry.iconBg || ''
            },
            {
              type: 'hidden',
              name: 'iconColor',
              value: safeEntry.iconColor || ''
            },
            {
              type: 'input-text',
              name: 'title',
              label: '入口名称',
              required: true,
              placeholder: '请输入名称',
              value: safeEntry.title
            },
            // {
            //   type: 'input-text',
            //   name: 'description',
            //   label: '辅助描述',
            //   placeholder: '请输入辅助描述'
            // },
            {
              type: 'select',
              name: 'group',
              label: '分组　　',
              clearable: true,
              options: tabOptions,
              value: selectedGroupIndex
            },
            {
              type: 'radios',
              name: 'openType',
              label: '入口类型',
              options: [
                {label: '打开内部页面', value: 'internal'},
                {label: '打开外部链接', value: 'external'}
              ],
              value: safeEntry.openType || 'external'
            },
            {
              type: 'select',
              name: 'internalPage',
              label: '选择页面',
              value: safeEntry.internalPage,
              source: {
                method: 'GET',
                url: `/admin-api/system/application-page-and-class/list?applicationId=${props.appId}&applicantOrBackend=1`,
                adaptor: (payload: any) => {
                  // 过滤type=1的数据
                  const filteredData = payload.data.filter(
                    (item: any) => item.type === 1
                  );
                  const options = filteredData.map((item: any) => ({
                    label: item.name,
                    value: item.id
                  }));
                  return {
                    ...payload,
                    data: {
                      options: options
                    }
                  };
                }
              },
              onChange: (value: any, oldValue: any, model: any, form: any) => {
                if (!value) return;

                // 查找选中的数据源项
                const options = form.data.options || [];
                let selectedOption = options.find(
                  (item: any) => item.value == value
                );
                console.log('选中的数据源项:', selectedOption);
                if (selectedOption && selectedOption.label) {
                  // 保存关联的数据表ID
                  form.setValueByName('internalName', selectedOption.label);
                }
              },
              visibleOn: 'this.openType === "internal"'
            },
            {
              type: 'input-text',
              name: 'href',
              label: '链接地址',
              placeholder: '请输入链接地址',
              visibleOn: 'this.openType !== "internal"',
              value: safeEntry.href
            },
            {
              type: 'hidden',
              name: 'internalName'
            }
          ]
        },
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'close'
          },
          {
            type: 'submit',
            label: '确认',
            level: 'primary'
          }
        ]
      };
    } catch (error) {
      console.error('获取编辑对话框配置失败:', error);
      return {};
    }
  };

  // 渲染前检查数据有效性
  const safeEntryItems = Array.isArray(entryItems) ? entryItems : [];

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="entry-items-box">
        <div className="entry-items-container">
          <div className="entry-items-list">
            {safeEntryItems.map((item, index) => (
              <EntryItemRow
                key={`entry-item-${index}`}
                item={item}
                index={index}
                onEdit={handleEditItem}
                onDelete={handleDeleteItem}
                onMove={handleMoveItem}
              />
            ))}
          </div>

          <div className="entry-items-footer">
            <Button level="default" size="sm" onClick={handleAddItem}>
              <i className="fa fa-plus"></i> 添加入口
            </Button>
            <Button level="default" size="sm" onClick={handleBatchEdit}>
              <i className="fa fa-list"></i> 批量编辑
            </Button>
          </div>
        </div>
        {showEditDialog && (
          <AMISRenderer
            key={refreshListKey}
            schema={getEditDialogSchema()}
            show={showEditDialog}
            onClose={handleCloseDialog}
          />
        )}
        {showBatchEditDialog && (
          <AMISRenderer
            key={refreshListKey}
            schema={getBatchEditDialogSchema()}
            show={showBatchEditDialog}
            onClose={() => setShowBatchEditDialog(false)}
          />
        )}

        {showConfirmDialog && (
          <AMISRenderer
            show={showConfirmDialog}
            schema={{
              type: 'dialog',
              title: '删除入口',
              body: {
                type: 'tpl',
                tpl: '确定要删除该入口吗？删除后数据不可恢复!'
              },
              actions: [
                {
                  type: 'button',
                  label: '取消',
                  actionType: 'close'
                },
                {
                  type: 'button',
                  label: '确认',
                  level: 'danger',
                  onClick: handleConfirmDelete
                }
              ]
            }}
            onClose={() => setShowConfirmDialog(false)}
          />
        )}

        {showIconStyleDialog && (
          <AMISRenderer
            key="icon-style-dialog"
            show={showIconStyleDialog}
            onClose={() => setShowIconStyleDialog(false)}
            onConfirm={handleIconSave}
            schema={EditEntryIcon({
              logo: currentIconStyle
            })}
          />
        )}
      </div>
    </DndProvider>
  );
};

export default EntryItemsManager;
