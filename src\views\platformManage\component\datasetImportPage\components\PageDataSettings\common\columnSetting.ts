const columnSettingObj = (schema: any, id: any, appId: any, field: any): any => {
  //过滤type为operation的列
  const columns = schema?.body[0]?.columns || [];
  const operationColumns = columns.filter((column: any) => column.type !== 'operation');

    return {
      type: 'page',
      regions: ['body'],
      id: 'u:06ba71552faa',
      pullRefresh: {
        disabled: true
      },
      asideResizor: false,
      title: '',
      data: {
        "items": operationColumns
      },
      body: [
        {
          type: 'crud',
          syncLocation: false,
          // showIndex: true,
          // draggable: true,
          quickSaveItemApi: {
            method: 'put',
            url: '/admin-api/system/form-data/update',
            data: {
              "id": id,
              "applicationPageId": appId,
              "field": field
            },
            requestAdaptor: function (api: any, context: any) {
              const nameToMatch = context.name;
              const indexToUpdate = columns.findIndex((column: any) => column.name === nameToMatch);
              if (context.needWordBreak) {
                context.textOverflow = 'default'
              } else {
                context.textOverflow = 'ellipsis'
              }
              if (indexToUpdate !== -1) {
                columns[indexToUpdate] = context
              } 

              schema.body[0].columns = columns
              //context替换columns中的值
              const jsonString = JSON.stringify(schema)


              return {
                ...api,
                data: {
                  ...api.data, // 获取暴露的 api 中的 data 变量
                  data: jsonString // 新添加数据
                }
              };
            }
          },
          perPageAvailable: [5, 10, 20, 50, 100],
          columns: [
            {
              label: '列标题',
              type: 'text',
              name: 'label',

            },
            {
              label: '排序',
              name: 'sortable',
              quickEdit: {
                type: "checkbox",
                saveImmediately: true,
                mode: 'inline'
              }
            },
            {
              label: '搜索',
              name: 'searchable',
              quickEdit: {
                type: "checkbox",
                saveImmediately: true,
                mode: 'inline'
              }
            },
            {
              label: '自动换行',
              name: 'needWordBreak',
              quickEdit: {
                type: "checkbox",
                saveImmediately: true,
                mode: 'inline'
              }
            },
            // {//需指定选项
            //   label: '列过滤',
            //   name: 'filterable',
            //   type: "checkbox",
            // },
          ]
        }
      ],
      themeCss: {
        baseControlClassName: {
          'background:default': '#f6f6f6'
        }
      }
    };
  };
  
  export {columnSettingObj};
  
  