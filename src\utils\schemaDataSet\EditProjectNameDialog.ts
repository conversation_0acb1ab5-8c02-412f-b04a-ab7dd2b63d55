export const EditProjectNameDialog = (show: boolean, currentName?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑项目名称</b>'
    },
    showCloseButton: false,
    data: {
      name: currentName
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入项目名称'
      },
      body: [
        {
          type: 'input-text',
          name: 'name',
          placeholder: '项目名称',
          required: true,
          maxLength: 50,
          validations: {
            maxLength: 50
          },
          inputClassName: 'name-input',
          labelClassName: 'name-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 