import React, {useState, useEffect, useRef} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store/index';
import {RouteComponentProps} from 'react-router-dom';
import {
  accountLogin,
  smsLogin,
  sendSms,
  getUserProfile,
  socialLogin,
  register,
  getTenant,
  getTenantList
} from '@/utils/api/api';
import {toast, Button, Form, Link, Input, Modal} from 'amis-ui';
import ThemeSwitch from '@/component/ThemeSwitch';
import './index.scss';

interface FormData {
  username: string;
  password?: string;
  code?: string;
  agreement: boolean;
  loginType: 'password' | 'code' | 'email';
  [key: string]: any;
}

declare global {
  interface Window {
    WxLogin: any;
  }
}

export default inject('store')(
  observer(function ({
    store,
    history,
    location
  }: {store: IMainStore} & RouteComponentProps) {
    const wxLoginContainerRef = useRef<HTMLDivElement>(null);
    const [isRegister, setIsRegister] = useState(false);
    const [isWxScanLogin, setIsWxScanLogin] = useState(false);
    const [loginType, setLoginType] = useState<'password' | 'code' | 'email'>(
      'password'
    );
    const [countdown, setCountdown] = useState(0);
    const [showPassword, setShowPassword] = useState(false);
    const [isAgreementChecked, setIsAgreementChecked] = useState(false);
    const [refreshKey, setRefreshKey] = useState(0);
    const formRef = useRef<any>(null);

    // 组织选择相关状态
    const [showTenantSelection, setShowTenantSelection] = useState(false);
    const [tenantList, setTenantList] = useState<any[]>([]);
    const [pendingLoginData, setPendingLoginData] = useState<any>(null); // 保存待登录的数据

    const urlParams = new URLSearchParams(location.search);
    const authCode = urlParams.get('code');
    const authState = urlParams.get('state');

    useEffect(() => {
      console.log('urlParams', urlParams);
      console.log(
        'urlPath',
        window.location.origin + window.location.pathname + '#/login'
      );
      if (authCode && authState) {
        // 先显示组织选择，再登录
        handlePreLogin({
          type: 'wx',
          authCode: authCode,
          authState: authState
        });

        // handleWxLoginCallback(authCode, authState);
      }
      // 动态加载微信登录脚本
      const script = document.createElement('script');
      script.src = '//res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js';
      document.body.appendChild(script);

      script.onload = () => {
        // 仅当处于微信登录状态时才初始化
        if (isWxScanLogin) {
          initWxLogin();
        }
      };

      return () => {
        if (script.parentNode) {
          document.body.removeChild(script);
        }
      };
    }, [location.search, authCode, authState]);

    // 监听微信登录状态变化，初始化二维码
    useEffect(() => {
      if (isWxScanLogin && window.WxLogin) {
        // 延迟一下初始化，确保DOM已渲染
        setTimeout(() => {
          initWxLogin();
        }, 100);
      }
    }, [isWxScanLogin]);

    const handleWxLoginCallback = async (code: string, state: string) => {
      try {
        const res = await socialLogin({code, type: '32', state: state});
        if (res.code === 0) {
          handleLoginSuccess(res);
        } else {
          handleLoginError(res);
        }
      } catch (err: any) {
        toast.error('微信登录失败：' + (err.msg || '未知错误'));
      }
    };

    const initWxLogin = () => {
      if (!wxLoginContainerRef.current) {
        return;
      }

      // 清空容器内容
      wxLoginContainerRef.current.innerHTML = '';

      if (window.WxLogin) {
        const customStyle = `
          .impowerBox .title {display: none !important;}
          .impowerBox .info {display: none !important;}
          .impowerBox .qrcode {width: 270px !important; height: 270px !important; margin-top: 0 !important;border: none !important;box-sizing: border-box;}
        `;

        // 转换为Base64
        const base64Style = btoa(customStyle);

        console.log(
          'redirect_uri',
          window.location.origin + window.location.pathname + '#/login'
        );
        new window.WxLogin({
          self_redirect: false,
          id: 'wxLoginContainer',
          appid: 'wx01325991cf0dcafa', //wx01325991cf0dcafa
          scope: 'snsapi_login',
          redirect_uri: encodeURIComponent(
            window.location.origin + window.location.pathname + '#/login'
          ),
          state: Math.random().toString(),
          style: `${store.theme === 'dark' ? 'black' : 'white'}`,
          href: 'data:text/css;base64,' + base64Style
        });
      }
    };

    // 自定义 setLoginType 函数，同时更新隐藏字段
    const updateLoginType = (
      type: 'password' | 'code' | 'email',
      methods?: any
    ) => {
      console.log('更新登录类型为:', type);
      setLoginType(type);
      if (methods) {
        methods.setValue('loginType', type);
        // 同步勾选状态
        const isChecked = methods.getValues().agreement || false;
        setIsAgreementChecked(isChecked);
      }
      // 强制刷新UI
      setRefreshKey(prevKey => prevKey + 1);
    };

    // 切换注册/登录模式
    const toggleRegisterMode = (register: boolean, methods?: any) => {
      setIsRegister(register);
      // 重置表单
      if (methods) {
        methods.reset();
        methods.setValue('loginType', loginType);
      }
      // 强制刷新UI
      setRefreshKey(prevKey => prevKey + 1);
    };

    useEffect(() => {}, [loginType, isRegister]);

    const handleSubmit = (values: FormData) => {
      console.log('提交的表单数据:', values);
      console.log('组件中的登录类型:', loginType);
      console.log('表单中的登录类型:', values.loginType);
      console.log('isRegister:', isRegister);

      // 使用表单中存储的登录类型
      const currentLoginType = values.loginType || loginType;
      console.log('最终使用的登录类型:', currentLoginType);

      if (isRegister) {
        // 处理注册逻辑
        if (!values.agreement) {
          toast.error('请阅读并同意服务协议和隐私协议');
          return;
        }

        if (!values.username) {
          toast.error('请输入手机号/邮箱');
          return;
        }

        if (
          !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(values.username) &&
          !/^1\d{10}$/.test(values.username)
        ) {
          toast.error('请输入正确的手机号/邮箱');
          return;
        }

        if (!values.code) {
          toast.error('请输入验证码');
          return;
        }

        register({
          username: values.username,
          code: values.code
        })
          .then(handleLoginSuccess)
          .catch(handleLoginError);
        // 调用注册API
      } else {
        // 处理登录逻辑
        if (!values.agreement) {
          toast.error('请阅读并同意服务协议和隐私协议');
          return;
        }

        if (!values.username) {
          toast.error(
            currentLoginType === 'email' ? '请输入邮箱' : '请输入手机号'
          );
          return;
        }

        // 验证码登录或邮箱登录
        if (currentLoginType === 'code' || currentLoginType === 'email') {
          if (!values.code) {
            toast.error('请输入验证码');
            return;
          }

          // 先显示组织选择，再登录
          handlePreLogin({
            type: 'sms',
            mobile: values.username,
            code: values.code
          });
        }
        // 密码登录
        else if (currentLoginType === 'password') {
          if (!values.password) {
            toast.error('请输入密码');
            return;
          }

          // 先显示组织选择，再登录
          handlePreLogin({
            type: 'password',
            username: values.username,
            password: values.password
          });
        }
      }
    };

    // 预登录 - 先获取组织列表
    const handlePreLogin = async (loginData: any) => {
      try {
        let params = {};

        if (loginData.type === 'sms') {
          params = {mobile: loginData.mobile};
        } else if (loginData.type === 'password') {
          params = {username: loginData.username};
        }else if (loginData.type === 'wx') {
          params = {type: '32',code: loginData.authCode,state: loginData.authState};
        }

        const tenantResponse = await getTenantList(params);
        if (
          tenantResponse.code === 0 &&
          tenantResponse.data &&
          tenantResponse.data.length > 0
        ) {
          setTenantList(tenantResponse.data);
          setPendingLoginData(loginData); // 保存登录数据
          setShowTenantSelection(true); // 显示组织选择弹窗
        }else{
          toast.error(tenantResponse.msg);
        }
      } catch (error) {
        console.error('获取组织列表失败:', error);
        // 如果获取组织列表失败，临时使用默认值并登录（不保存到store）
      }
    };

    // 执行实际登录（使用已选择的tenantId）
    const performLogin = (loginData: any) => {
      if (loginData.type === 'password') {
        accountLogin({
          username: loginData.username,
          password: loginData.password
        })
          .then(handleLoginSuccess)
          .catch(handleLoginError);
      } else if (loginData.type === 'sms') {
        smsLogin({
          mobile: loginData.mobile,
          code: loginData.code
        })
          .then(handleLoginSuccess)
          .catch(handleLoginError);
      }else if (loginData.type === 'wx') {
        handleWxLoginCallback(loginData.authCode, loginData.authState);
      }
    };

    const handleLoginSuccess = (res: any) => {
      if (res.code === 0) {
        store.setLastTenantId(store.tenant_id);
        store.setAccessToken(res.data.accessToken);
        store.setRefreshToken(res.data.refreshToken);
        getLoginUserProfile();
      } else {
        store.setTenantId('');
        store.setAccessToken('');
        store.setRefreshToken('');
        toast.error(res.msg);
      }
    };

    const handleLoginError = (err: any) => {
      console.error(err);
      setPendingLoginData(null); // 清空待登录数据
      store.setTenantId('');
      toast.error(err.msg || '登录失败');
    };

    // 处理组织选择
    const handleTenantSelect = (tenant: any) => {
      store.setTenantId(tenant.id);
      setShowTenantSelection(false);

      // 选择组织后执行登录
      if (pendingLoginData) {
        performLogin(pendingLoginData);
        setPendingLoginData(null); // 清空待登录数据
      }
    };

    const toggleWxScanLogin = () => {
      if (!isWxScanLogin) {
        setIsWxScanLogin(true);
        // 当切换到微信登录时，延迟一下再加载二维码
        setTimeout(() => {
          initWxLogin();
        }, 300);
      }
    };

    // 获取租户信息
    const getLoginUserProfile = () => {
      getUserProfile()
        .then(res => {
          if (res.code === 0) {
            const user = res.data;
                      // 处理账号权限
          if (res.data.accountAuthorization) {
            user.hasAccountAuth = res.data.accountAuthorization.status === 1;
          } else {
            user.hasAccountAuth = false;
          }

          // 处理搭建权限
          if (res.data.buildAuthorizations && res.data.buildAuthorizations.length > 0) {
            // 组织搭建权限 (type 1)
            user.hasOrgAuth = res.data.buildAuthorizations.some(
              (auth: any) => auth.type === 1 && auth.status === 1
            );
            
            // 应用搭建权限 (type 2)
            user.hasAppBuildAuth = res.data.buildAuthorizations.some(
              (auth: any) => auth.type === 2 && auth.status === 1
            );
          } else {
            user.hasOrgAuth = false;
            user.hasAppBuildAuth = false;
          }

          // 处理资源权限
          if (res.data.resourcesAuthorizations && res.data.resourcesAuthorizations.length > 0) {
            // 应用模板管理员 (type 1)
            user.hasAppTemplateAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 1 && auth.status === 1
            );
            
            // 表单模板管理员 (type 2)
            user.hasFormTemplateAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 2 && auth.status === 1
            );
            
            // 组件管理员 (type 3)
            user.hasComponentAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 3 && auth.status === 1
            );
            
            // 图标管理员 (type 4)
            user.hasIconAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 4 && auth.status === 1
            );
            
            // 图片管理员 (type 5)
            user.hasImageAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 5 && auth.status === 1
            );
          } else {
            user.hasAppTemplateAuth = false;
            user.hasFormTemplateAuth = false;
            user.hasComponentAuth = false;
            user.hasIconAuth = false;
            user.hasImageAuth = false;
          }
            getTenant({id: store.tenant_id})
              .then(res => {
                if (res.code === 0) {
                  user.companyLogo = res.data.logo;
                  user.companyName = res.data.name;
                  user.companyId = res.data.contactUserId;
                  store.setUserInfo(user);
                  history.push('/');
                  toast.success('登录成功');
                } else {
                  store.setTenantId('');
                  store.setAccessToken('');
                  store.setRefreshToken('');
                  toast.error(res.msg);
                }
              })
              .catch(err => toast.error(err.msg || '获取租户信息失败'));
          }
        })
        .catch(err => toast.error(err.msg || '获取租户信息失败'));
    };

    const handleGetCode = (username: string) => {
      if (countdown > 0) return;

      if (!username) {
        toast.error(loginType === 'email' ? '请输入邮箱' : '请输入手机号');
        return;
      }

      // TODO: 调用发送验证码接口
      sendSms({
        mobile: username,
        scene: isRegister ? '22' : '21'
      })
        .then(res => {
          if (res.code === 0) {
            setCountdown(60);
            const timer = setInterval(() => {
              setCountdown(time => {
                if (time <= 1) {
                  clearInterval(timer);
                  return 0;
                }
                return time - 1;
              });
            }, 1000);
          } else {
            toast.error(res.msg || '获取验证码失败');
          }
        })
        .catch(err => toast.error(err.msg || '获取验证码失败'));
    };

    const renderLoginForm = (methods: any) => {
      switch (loginType) {
        case 'code':
        case 'email':
          return (
            <div className="login-input code-input">
              <Input
                maxLength={6}
                value={methods.getValues().code}
                onChange={e => {
                  methods.setValue('code', e.target.value);
                  // 保存表单数据
                  localStorage.setItem(
                    'loginFormData',
                    JSON.stringify(methods.getValues())
                  );
                }}
                placeholder="验证码"
                className="form-control"
              />
              <Button
                type="button"
                className="get-code-btn"
                disabled={countdown > 0}
                onClick={() => handleGetCode(methods.getValues().username)}
              >
                {countdown > 0 ? `${countdown}秒后重试` : '获取验证码'}
              </Button>
            </div>
          );
        default:
          return (
            <div className="login-input">
              <div className="password-input">
                <Input
                  maxLength={32}
                  type={showPassword ? 'text' : 'password'}
                  value={methods.getValues().password}
                  onChange={e => {
                    methods.setValue('password', e.target.value);
                    // 保存表单数据
                    localStorage.setItem(
                      'loginFormData',
                      JSON.stringify(methods.getValues())
                    );
                  }}
                  placeholder="密码"
                  className="form-control"
                />
                <i
                  className={`fa fa-${showPassword ? 'eye' : 'eye-slash'}`}
                  onClick={() => setShowPassword(!showPassword)}
                />
              </div>
            </div>
          );
      }
    };

    return (
      <div className="login-page">
        <div
          className="theme-switch-container"
          style={{position: 'absolute', top: '20px', right: '20px'}}
        >
          <ThemeSwitch store={store} />
        </div>

        {isWxScanLogin ? (
          <div className="page-title">
            <i
              className="fa fa-arrow-left mr-4"
              onClick={() => setIsWxScanLogin(false)}
            ></i>
            微信安全登录
          </div>
        ) : (
          <div className="page-title">
            {isRegister ? '创建账号' : '欢迎回来'}
          </div>
        )}

        <div className="form-content">
          <Form
            key={`login-form-${refreshKey}`}
            ref={formRef}
            className="login-form"
            mode="horizontal"
            wrapWithPanel={false}
            resetAfterSubmit={false}
            autoFocus={false}
            submitOnChange={false}
            submitText=""
            onChange={(values: any) => {
              console.log('表单值改变:', values);
              // 同步勾选状态
              if (values.agreement !== isAgreementChecked) {
                setIsAgreementChecked(values.agreement);
              }
              // 保存表单值到localStorage
              localStorage.setItem('loginFormData', JSON.stringify(values));
            }}
            onSubmit={handleSubmit}
          >
            {methods => (
              <>
                {console.log(
                  'Form methods初始化，当前值:',
                  methods.getValues()
                )}
                <div className="login-methods">
                  <Button
                    type="button"
                    className="wx-login-btn"
                    onClick={toggleWxScanLogin}
                  >
                    <i className="fa fa-weixin" />
                    微信登录
                  </Button>
                  <div className="other-login">
                    <Button
                      type="button"
                      className="login-btn"
                      onClick={() => console.log('企业微信登录')}
                    >
                      <i className="fa fa-wechat" />
                    </Button>
                    <Button
                      type="button"
                      className="login-btn"
                      onClick={() => console.log('钉钉登录')}
                    >
                      <i className="fa fa-comments" />
                    </Button>
                  </div>
                </div>

                <div className="login-divider">或</div>

                {isWxScanLogin ? (
                  <div className="wxlogin-container">
                    <div
                      className="wxlogin-container-code"
                      id="wxLoginContainer"
                      ref={wxLoginContainerRef}
                    ></div>
                    <div className="wxlogin-container-tips">
                      微信扫描上方的二维码快速登录
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="login-input">
                      <Input
                        maxLength={
                          isRegister ? 32 : loginType === 'email' ? 32 : 11
                        }
                        value={methods.getValues().username}
                        onChange={e => {
                          methods.setValue('username', e.target.value);
                          // 保存最新数据到localStorage
                          localStorage.setItem(
                            'loginFormData',
                            JSON.stringify(methods.getValues())
                          );
                        }}
                        placeholder={
                          isRegister
                            ? '手机号/邮箱'
                            : loginType === 'email'
                            ? '邮箱'
                            : '手机号'
                        }
                        className="form-control"
                      />
                    </div>

                    {isRegister ? (
                      <div className="login-input code-input">
                        <Input
                          maxLength={6}
                          value={methods.getValues().code}
                          onChange={e => {
                            methods.setValue('code', e.target.value);
                            // 保存表单数据
                            localStorage.setItem(
                              'loginFormData',
                              JSON.stringify(methods.getValues())
                            );
                          }}
                          placeholder="验证码"
                          className="form-control"
                        />
                        <Button
                          type="button"
                          className="get-code-btn"
                          disabled={countdown > 0}
                          onClick={() =>
                            handleGetCode(methods.getValues().username)
                          }
                        >
                          {countdown > 0
                            ? `${countdown}秒后重试`
                            : '获取验证码'}
                        </Button>
                      </div>
                    ) : (
                      renderLoginForm(methods)
                    )}

                    <div className="login-agreement" key={refreshKey}>
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={isAgreementChecked}
                          onChange={e => {
                            const checked = e.target.checked;
                            console.log('原生勾选状态改变:', checked);
                            setIsAgreementChecked(checked);
                            methods.setValue('agreement', checked);
                          }}
                        />
                        <span className="checkbox-text">
                          我已阅读并同意
                          <a
                            href="#"
                            onClick={e => {
                              e.preventDefault();
                              // 跳转到服务协议页面
                              history.push('/service');
                            }}
                          >
                            服务协议
                          </a>
                          {' 和 '}
                          <a
                            href="#"
                            onClick={e => {
                              e.preventDefault();
                              // 跳转到隐私协议页面
                              history.push('/privacy');
                            }}
                          >
                            隐私协议
                          </a>
                        </span>
                      </label>
                    </div>

                    <Button
                      type="submit"
                      level="primary"
                      className="login-submit"
                      block
                    >
                      {isRegister ? '注册账号' : '开始使用'}
                    </Button>

                    {!isRegister && (
                      <div className="login-links">
                        {loginType === 'password' && (
                          <Link
                            onClick={() => history.push('/forget-password')}
                            className="forget-password"
                          >
                            忘记密码
                          </Link>
                        )}
                        <div className="other-logins">
                          {loginType === 'email' ? (
                            <>
                              <Link
                                to="#"
                                onClick={(
                                  e: React.MouseEvent<HTMLAnchorElement>
                                ) => {
                                  e.preventDefault();
                                  // 直接设置登录类型为 'code'
                                  console.log('强制切换登录类型为code');
                                  setIsAgreementChecked(false);
                                  methods.setValue('agreement', false);
                                  updateLoginType('code', methods);
                                }}
                              >
                                验证码登录
                              </Link>
                              <span className="divider"></span>
                              <Link
                                to="#"
                                onClick={(
                                  e: React.MouseEvent<HTMLAnchorElement>
                                ) => {
                                  e.preventDefault();
                                  // 直接设置登录类型为 'password'
                                  setIsAgreementChecked(false);
                                  methods.setValue('agreement', false);
                                  console.log('强制切换登录类型为password');
                                  updateLoginType('password', methods);
                                }}
                              >
                                密码登录
                              </Link>
                            </>
                          ) : (
                            <>
                              <Link
                                to="#"
                                onClick={(
                                  e: React.MouseEvent<HTMLAnchorElement>
                                ) => {
                                  e.preventDefault();
                                  // 根据当前登录类型切换
                                  if (loginType === 'password') {
                                    console.log('从password切换到code');
                                    updateLoginType('code', methods);
                                  } else {
                                    console.log('从code切换到password');
                                    updateLoginType('password', methods);
                                  }
                                  setIsAgreementChecked(false);
                                  methods.setValue('agreement', false);
                                }}
                              >
                                {loginType === 'password'
                                  ? '验证码登录'
                                  : '密码登录'}
                              </Link>
                              <span className="divider"></span>
                              <Link
                                to="#"
                                onClick={(
                                  e: React.MouseEvent<HTMLAnchorElement>
                                ) => {
                                  e.preventDefault();
                                  console.log('切换到邮箱登录');
                                  setIsAgreementChecked(false);
                                  methods.setValue('agreement', false);
                                  updateLoginType('email', methods);
                                }}
                              >
                                邮箱登录
                              </Link>
                            </>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="register-link">
                      {isRegister ? (
                        <>
                          已有账号？
                          <Link
                            onClick={() => toggleRegisterMode(false, methods)}
                          >
                            返回登录
                          </Link>
                        </>
                      ) : (
                        <>
                          没有账号？
                          <Link
                            onClick={() => toggleRegisterMode(true, methods)}
                          >
                            前往注册
                          </Link>
                        </>
                      )}
                    </div>
                  </>
                )}
              </>
            )}
          </Form>
        </div>

        {/* 组织选择Modal */}
        <Modal
          show={showTenantSelection}
          onClose={() => setShowTenantSelection(false)}
          onHide={() => setShowTenantSelection(false)}
          closeOnOutside={false}
          closeOnEsc={false}
          className="tenant-selection-modal"
        >
          <div className="tenant-selection-content">
            <h3 className="tenant-selection-title">选择你已加入的组织</h3>

            <div className="tenant-selection-list">
              {tenantList.map((tenant: any, index: number) => {
                // 判断是否是上次登录的组织（这里可以根据实际情况调整判断逻辑）
                const isLastLogin = store.last_tenant_id === tenant.id; // 假设第一个是上次登录的组织

                return (
                  <div
                    key={tenant.id || index}
                    className="tenant-selection-item"
                    onClick={() => handleTenantSelect(tenant)}
                  >
                    <div className="tenant-selection-logo">
                      {tenant.logo ? (
                        <img src={tenant.logo} alt={tenant.name} />
                      ) : (
                        <div className="tenant-selection-logo-placeholder">
                          <i className="fa fa-users"></i>
                        </div>
                      )}
                    </div>
                    <div className="tenant-selection-info">
                      <div className="tenant-selection-name">{tenant.name}</div>
                    </div>
                    <div className="tenant-selection-right">
                      {isLastLogin && (
                        <span className="last-login-badge">上次登录</span>
                      )}
                      <div className="tenant-selection-arrow">
                        <i className="fa fa-chevron-right"></i>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Modal>
      </div>
    );
  })
);
