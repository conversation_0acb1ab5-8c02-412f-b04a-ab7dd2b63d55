const singleOperationObj = (path: string, showFieldData: any, pageId: string, saveAction: any): any => {

    const fieldData = showFieldData.map((item: any) => {
        return {
            label: item.label,
            value: item.name
        }
    })

    return {
      type: 'page',
      regions: ['body'],
      id: 'u:06ba71552faa',
      pullRefresh: {
        disabled: true
      },
      asideResizor: false,
      title: '',
      body: [
        {
          type: 'crud',
          syncLocation: false,
          draggable: true,
          quickSaveItemApi: {
            method: 'put',
            url: '/admin-api/system/page-one-operate/update',
            data: {
              "isEnabled": "${isEnabled ? 1 : 0}",
              "applicationPageId": "${applicationPageId}",
              "id": "${id}"
            }
          },
          api: {
            method: 'get',
            url: `/admin-api/system/page-one-operate/page?applicationPageId=${pageId}`,
            adaptor: function (payload: any, response: any, api: any, context: any) {
                saveAction(response.data.data);
                return payload;
              }
          },
          pipeOut: (data : any) => {
            console.log("pipeOut", data);
          },
          onChange: (data : any) => {
            console.log("onChange", data);
          },
          //多选actions
          bulkActions: [
            // {
            //     label: "按钮",
            //     type: "button",
            //     id: "u:c50cef36da22"
            //   }
          ],
          //单选actions
          itemActions: [
            // {
            //     label: "按钮",
            //     type: "button",
            //     id: "u:c50cef36da22"
            //   }
          ],
          id: 'u:67582b11dcf4',
          perPageAvailable: [5, 10, 20, 50, 100],
          messages: {},
          filterSettingSource: ['id', 'create_user', 'createTime'],
          headerToolbar: [
            {
              label: '新增单条操作',
              type: 'button',
              actionType: 'dialog',
              level: 'primary',
              editorSetting: {
                behavior: 'create'
              },
              id: 'u:215cf1bb50d5',
              dialog: {
                type: 'dialog',
                title: '新增单条操作',
                body: [
                  {
                    type: 'form',
                    api: {
                        method: 'post',
                        url: '/admin-api/system/page-one-operate/create'
                    },
                    body: [
                        {
                            type: 'input-text',
                            label: '按钮名称',
                            name: 'buttonName',
                            required: true
                        },
                        {
                            type: 'input-text',
                            label: '按钮描述',
                            name: 'buttonDepict'
                        },
                        {
                            type: 'select',
                            label: '弹窗方式',
                            name: 'popWinWay',
                            required: true,
                            options: [
                                {
                                    label: '弹窗',
                                    value: '弹窗'
                                },
                                {
                                    label: '抽屉',
                                    value: '抽屉'
                                }
                            ]
                        },
                        {
                            type: 'select',
                            label: '字段选择',
                            name: 'fieldName',
                            required: true,
                            options: fieldData
                        },
                        {
                            type: 'input-text',
                            label: '页面ID',
                            name: 'applicationPageId',
                            value: pageId,
                            hidden: true
                        }
                    ],
                    id: 'u:6dc50e8d3d85',
                    actions: [
                      {
                        type: 'submit',
                        label: '提交',
                        primary: true,
                        onClick: (e: any, props: any) => {
                            console.log("submit", e, props);
                        }
                      }
                    ],
                    // feat: 'Insert'
                  }
                ],
                actionType: 'dialog',
                id: 'u:360e7093b381',
                actions: [
                  {
                    type: 'button',
                    actionType: 'cancel',
                    label: '取消',
                    id: 'u:b43c0a83e65a'
                  },
                  {
                    type: 'button',
                    actionType: 'confirm',
                    label: '确定',
                    primary: true,
                    id: 'u:973eb72462b5'
                  }
                ],
                showCloseButton: true,
                closeOnOutside: false,
                closeOnEsc: false,
                showErrorMsg: true,
                showLoading: true,
                draggable: false,
                size: 'lg',
                resizable: false,
                editorSetting: {
                  displayName: '新增'
                }
              }
            },
            {
              type: 'reload',
            //   tpl: '内容',
              wrapperComponent: ''
            },
            // {
            //   type: 'export-excel',
            //   tpl: '内容',
            //   wrapperComponent: ''
            // }
          ],
          columns: [
            {
              label: 'ID',
              type: 'text',
              name: 'id',
              sortable: true
            },
            {
              label: '按钮性质',
              type: 'text',
              // name: 'buttonNature',
              tpl: "自定义"
            },
            {
              label: '按钮名称',
              type: 'text',
              name: 'buttonName',
            },
            {
              label: '按钮描述',
              type: 'text',
              name: 'buttonDepict',
            },
            {
              label: '动作类型',
              type: 'text',
              // name: 'actionType',
              tpl: "组件"
            },
            {
              label: '弹窗方式',
              type: 'text',
              name: 'popWinWay',
            },
            {
              label: '启用状态',
              // type: 'switch',
              name: 'isEnabled',
              quickEdit: {
                mode: "inline",
                type: "switch",
                onText: '启用',
                offText: '停用',
                saveImmediately: true,
                resetOnFailed: true
              }

            },
            {
                label: '是否审批',
                type: 'switch',
                name: 'isApprove',
                onText: '启用',
                offText: '停用'
            },
            {
              label: '创建者',
              type: 'text',
              name: 'createUser',
              sortable: true
            },
            {
              label: '创建时间',
              type: 'text',
              name: 'createTime',
              sortable: true,
              tpl: "${createTime / 1000 | date}"
            },
            {
              type: 'operation',
              label: '操作',
              buttons: [
                {
                  label: '编辑',
                  type: 'button',
                  actionType: 'dialog',
                  level: 'link',
                  editorSetting: {
                    behavior: 'update'
                  },
                  id: 'u:4d423864d6c0',
                  dialog: {
                    type: 'dialog',
                    title: '编辑',
                    body: [
                      {
                        type: 'form',
                        api: {
                          method: 'put',
                          url: '/admin-api/system/page-one-operate/update'
                        },
                        // initApi: `/apiTest/${path}/` + '${id}/edit',
                        body: [
                            {
                                type: 'input-text',
                                label: '按钮名称',
                                name: 'buttonName',
                                required: true
                            },
                            {
                                type: 'input-text',
                                label: '按钮描述',
                                name: 'buttonDepict'
                            },
                            {
                                type: 'select',
                                label: '弹窗方式',
                                name: 'popWinWay',
                                required: true,
                                options: [
                                    {
                                        label: '弹窗',
                                        value: '弹窗'
                                    },
                                    {
                                        label: '抽屉',
                                        value: '抽屉'
                                    }
                                ]
                            },
                            {
                                type: 'select',
                                label: '字段选择',
                                name: 'fieldName',
                                required: true,
                                options: fieldData
                            },
                            {
                                type: 'input-text',
                                label: '页面ID',
                                name: 'applicationPageId',
                                value: pageId,
                                hidden: true
                            },
                            {
                                type: 'input-text',
                                label: 'id',
                                name: 'id',
                                value: 'id',
                                hidden: true
                            },
                        ],
                        id: 'u:1fb5ccb04730',
                        actions: [
                          {
                            type: 'button',
                            label: '取消',
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'cancel',
                                    componentId: 'u:1fb5ccb04730'
                                  }
                                ]
                              }
                            },
                            level: 'default'
                          },
                          {
                            type: 'button',
                            label: '提交',
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'submit',
                                    componentId: 'u:1fb5ccb04730'
                                  }
                                ]
                              }
                            },
                            level: 'primary'
                          }
                        ],
                        feat: 'Edit',
                        dsType: 'api',
                        labelAlign: 'top',
                        title: '表单',
                        mode: 'flex',
                        resetAfterSubmit: true
                      }
                    ],
                    actionType: 'drawer',
                    id: 'u:6fbb412d9abe',
                    actions: [
                      {
                        type: 'button',
                        actionType: 'cancel',
                        label: '取消',
                        id: 'u:2e86c554e48e'
                      },
                      {
                        type: 'button',
                        actionType: 'confirm',
                        label: '确定',
                        primary: true,
                        id: 'u:392213d59e8d'
                      }
                    ],
                    showCloseButton: true,
                    closeOnOutside: false,
                    closeOnEsc: false,
                    showErrorMsg: true,
                    showLoading: true,
                    draggable: false,
                    size: 'lg',
                    resizable: false,
                    editorSetting: {
                      displayName: '编辑'
                    }
                  }
                },
                {
                  label: '查看',
                  type: 'button',
                  actionType: 'drawer',
                  level: 'link',
                  editorSetting: {
                    behavior: 'view'
                  },
                  id: 'u:88e384769ff8',
                  drawer: {
                    type: 'drawer',
                    title: '查看详情',
                    body: [
                      {
                        type: 'form',
                        // initApi: `/apiTest/${path}/` + '${id}',
                        body: [
                          {
                            name: 'id',
                            label: 'id',
                            type: 'static'
                          },
                          {
                            name: 'buttonNature',
                            label: '按钮性质',
                            type: 'static',
                            tpl: "自定义"
                          },
                          {
                            name: 'buttonName',
                            label: '按钮名称',
                            type: 'static'
                          },
                          {
                            name: 'buttonDepict',
                            label: '按钮描述',
                            type: 'static'
                          },
                          {
                            name: 'actionType',
                            label: '动作类型',
                            type: 'static',
                            tpl: "组件"
                          },
                          {
                            name: 'popWinWay',
                            label: '弹窗方式',
                            type: 'static'
                          },
                          {
                            name: 'isEnabled',
                            label: '启用状态',
                            type: 'static',
                            tpl: "${isEnabled ? '启用' : '停用'}"
                          },
                          {
                            name: 'isApprove',
                            label: '是否审批',
                            type: 'static',
                            tpl: "${isApprove ? '启用' : '停用'}"
                          },
                          {
                            name: 'createUser',
                            label: '创建者',
                            type: 'static'
                          },
                          {
                            name: 'createTime',
                            label: '创建时间',
                            type: 'static',
                            tpl: "${createTime / 1000 | date}"
                          }
                        ],
                        id: 'u:2af2f2b5c2fd',
                        feat: 'View',
                        dsType: 'api',
                        labelAlign: 'top',
                        title: '表单',
                        mode: 'flex',
                        static: true,
                        actions: [
                          {
                            type: 'button',
                            label: '取消',
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'cancel',
                                    componentId: 'u:e9f0e2b1f53a'
                                  }
                                ]
                              }
                            },
                            level: 'default'
                          },
                          {
                            type: 'button',
                            label: '提交',
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'submit',
                                    componentId: 'u:e9f0e2b1f53a'
                                  }
                                ]
                              }
                            },
                            level: 'primary'
                          }
                        ]
                      }
                    ],
                    actionType: 'drawer',
                    id: 'u:f202e0cd0692',
                    actions: [
                      {
                        type: 'button',
                        actionType: 'cancel',
                        label: '取消',
                        id: 'u:c1bc83c9be43'
                      },
                      {
                        type: 'button',
                        actionType: 'confirm',
                        label: '确定',
                        primary: true,
                        id: 'u:25ec3026c1b7'
                      }
                    ],
                    showCloseButton: true,
                    closeOnOutside: false,
                    closeOnEsc: false,
                    showErrorMsg: true,
                    showLoading: true,
                    draggable: false,
                    size: 'lg',
                    resizable: false,
                    editorSetting: {
                      displayName: '查看详情'
                    }
                  }
                },
                {
                  type: 'button',
                  label: '删除',
                  actionType: 'dialog',
                  level: 'link',
                  className: 'text-danger',
                  id: 'u:afacccd6355e',
                  dialog: {
                    type: 'dialog',
                    title: '',
                    className: 'py-2',
                    actions: [
                      {
                        type: 'action',
                        actionType: 'cancel',
                        label: '取消',
                        id: 'u:bf2ded085c52'
                      },
                      {
                        type: 'action',
                        actionType: 'submit',
                        label: '删除',
                        level: 'danger',
                        id: 'u:6f5c3305ac5b'
                      }
                    ],
                    body: [
                      {
                        type: 'form',
                        wrapWithPanel: false,
                        api: {
                            method: 'delete',
                            url: '/admin-api/system/page-one-operate/delete',
                            data: {
                                id: '${id}'
                            }
                        },
                        body: [
                          {
                            type: 'tpl',
                            className: 'py-2',
                            tpl: '确认删除选中项？',
                            id: 'u:b5980d190942'
                          }
                        ],
                        id: 'u:130a702e34da',
                        feat: 'Insert',
                        dsType: 'api',
                        labelAlign: 'left'
                      }
                    ],
                    actionType: 'dialog',
                    id: 'u:3c6bb0356c6a',
                    showCloseButton: true,
                    closeOnOutside: false,
                    closeOnEsc: false,
                    showErrorMsg: true,
                    showLoading: true,
                    draggable: false,
                    editorSetting: {
                      displayName: '删除'
                    }
                  }
                }
              ],
              id: 'u:0d6e1a5d6a6d'
            }
          ]
        }
      ],
      themeCss: {
        baseControlClassName: {
          'background:default': '#f6f6f6'
        }
      }
    };
  };
  
  export {singleOperationObj};
  
  