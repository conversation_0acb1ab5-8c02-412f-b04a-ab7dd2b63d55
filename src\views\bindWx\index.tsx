import React, {useEffect, useState, useRef} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {RouteComponentProps} from 'react-router-dom';
import {
  socialBind,
  accountLogin,
  getUserProfile,
  getTenant
} from '@/utils/api/api';
import {toast} from 'amis-ui';
import './index.scss';

interface BindWxProps extends RouteComponentProps {
  store: IMainStore;
}

declare global {
  interface Window {
    WxLogin: any;
  }
}

export default inject('store')(
  observer(function BindWx({store, history, location}: BindWxProps) {
    const wxBindContainerRef = useRef<HTMLDivElement>(null);

    const urlParams = new URLSearchParams(location.search);
    const authCode = urlParams.get('code');
    const authState = urlParams.get('state');

    // useEffect(() => {
    //   // 处理浏览器返回按钮事件
    //   const handlePopState = () => {
    //     console.log('handlePopState', location.pathname);
    //     // 如果是从 bindWx 页面返回
    //     if (location.pathname === '/bindWx') {
    //       // 触发自定义事件
    //       const event = new CustomEvent('switchToAccount', {
    //         detail: {target: 'account'}
    //       });

    //       history.push('/platform/account');

    //       setTimeout(() => {
    //         window.dispatchEvent(event);
    //       }, 100);
    //     }
    //   };

    //   window.addEventListener('popstate', handlePopState);
    //   return () => {
    //     window.removeEventListener('popstate', handlePopState);
    //   };
    // }, []);

    useEffect(() => {
      console.log('urlParams', urlParams);
      console.log(
        'urlPath',
        window.location.origin + window.location.pathname + '#/bindWx'
      );
      if (authCode && authState) {
        handleWxBindCallback(authCode, authState);
      }
      // 动态加载微信登录脚本
      const script = document.createElement('script');
      script.src = '//res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js';
      document.body.appendChild(script);

      script.onload = () => {
        // 仅当处于微信登录状态时才初始化
        initWxLogin();
      };

      return () => {
        if (script.parentNode) {
          document.body.removeChild(script);
        }
      };
    }, [location.search, authCode, authState]);

    // 监听微信登录状态变化，初始化二维码
    useEffect(() => {
      if (window.WxLogin) {
        // 延迟一下初始化，确保DOM已渲染
        setTimeout(() => {
          initWxLogin();
        }, 100);
      }
    }, []);

    // 获取租户信息
    const getLoginUserProfile = () => {
      getUserProfile()
        .then(res => {
          if (res.code === 0) {
            const user = res.data;
                      // 处理账号权限
          if (res.data.accountAuthorization) {
            user.hasAccountAuth = res.data.accountAuthorization.status === 1;
          } else {
            user.hasAccountAuth = false;
          }

          // 处理搭建权限
          if (res.data.buildAuthorizations && res.data.buildAuthorizations.length > 0) {
            // 组织搭建权限 (type 1)
            user.hasOrgAuth = res.data.buildAuthorizations.some(
              (auth: any) => auth.type === 1 && auth.status === 1
            );
            
            // 应用搭建权限 (type 2)
            user.hasAppBuildAuth = res.data.buildAuthorizations.some(
              (auth: any) => auth.type === 2 && auth.status === 1
            );
          } else {
            user.hasOrgAuth = false;
            user.hasAppBuildAuth = false;
          }

          // 处理资源权限
          if (res.data.resourcesAuthorizations && res.data.resourcesAuthorizations.length > 0) {
            // 应用模板管理员 (type 1)
            user.hasAppTemplateAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 1 && auth.status === 1
            );
            
            // 表单模板管理员 (type 2)
            user.hasFormTemplateAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 2 && auth.status === 1
            );
            
            // 组件管理员 (type 3)
            user.hasComponentAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 3 && auth.status === 1
            );
            
            // 图标管理员 (type 4)
            user.hasIconAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 4 && auth.status === 1
            );
            
            // 图片管理员 (type 5)
            user.hasImageAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 5 && auth.status === 1
            );
          } else {
            user.hasAppTemplateAuth = false;
            user.hasFormTemplateAuth = false;
            user.hasComponentAuth = false;
            user.hasIconAuth = false;
            user.hasImageAuth = false;
          }
            getTenant({id: store.tenant_id})
              .then(res => {
                if (res.code === 0) {
                  user.companyLogo = res.data.logo;
                  user.companyName = res.data.name;
                  user.companyId = res.data.contactUserId;
                  store.setUserInfo(user);
                  history.push('/platform/account');
                }
              })
              .catch(err => toast.error(err.msg || '获取租户信息失败'));
          }
        })
        .catch(err => toast.error(err.msg || '获取租户信息失败'));
    };

    const handleWxBindCallback = async (code: string, state: string) => {
      try {
        const res = await accountLogin({
          socialCode: code,
          socialType: '32',
          socialState: state,
          username: store.userInfo?.username,
          userId: store.userInfo?.id
        });
        if (res.code === 0) {
          toast.success('微信绑定成功');
          store.setAccessToken(res.data.accessToken);
          store.setRefreshToken(res.data.refreshToken);
          // store.setTenantId(res.data.userId);
          // store.setTenantId(1);
          getLoginUserProfile();
        } else {
          toast.error('微信绑定失败：' + (res.msg || '未知错误'));
        }
      } catch (err: any) {
        toast.error('微信绑定失败：' + (err.msg || '未知错误'));
      }
    };

    const initWxLogin = () => {
      if (!wxBindContainerRef.current) {
        return;
      }

      // 清空容器内容
      wxBindContainerRef.current.innerHTML = '';

      if (window.WxLogin) {
        const customStyle = `
            .impowerBox .title {display: none !important;}
            .impowerBox .info {display: none !important;}
            .impowerBox .qrcode {width: 154px !important; height: 154px !important; margin-top: 0 !important;border: none !important;box-sizing: border-box;}
          `;

        // 转换为Base64
        const base64Style = btoa(customStyle);

        console.log('redirect_uri', window.location.origin + '#/bindWx');
        new window.WxLogin({
          self_redirect: false,
          id: 'wxBindContainer',
          appid: 'wx01325991cf0dcafa', //wx01325991cf0dcafa
          scope: 'snsapi_login',
          redirect_uri: encodeURIComponent(
            window.location.origin + '/#/bindWx'
          ),
          state: Math.random().toString(),
          style: `${store.theme === 'dark' ? 'black' : 'white'}`,
          href: 'data:text/css;base64,' + base64Style
        });
      }
    };

    return (
      <div className="bind-wx-page">
        <div className="bind-wx-container">
          <div className="bind-wx-header">
            <img
              src="https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico"
              alt="WeChat"
              className="wx-logo"
            />
            <h2 className="bind-title">使用微信扫码</h2>
            <p className="bind-subtitle">绑定低代码</p>
          </div>
          <div className="bind-container">
            <div
              className="bind-container-code"
              id="wxBindContainer"
              ref={wxBindContainerRef}
            ></div>
          </div>
          <div className="bind-tips">
            <p>扫码上方二维码快速绑定账号</p>
          </div>
        </div>
      </div>
    );
  })
);
