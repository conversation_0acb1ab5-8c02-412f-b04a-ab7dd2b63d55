import {Button} from 'amis';
import React from 'react';
import {
  registerEditorPlugin,
  BaseEventContext,
  BasePlugin,
  RendererPluginAction,
  diff,
  defaultValue,
  getSchemaTpl,
  CodeEditor as AmisCodeEditor,
  RendererPluginEvent,
  tipedLabel
} from 'amis-editor-core';

import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
// import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import ChartDataInteraction from '@/editor/component/ChartDataInteraction';

const ChartConfigEditor = ({value, onChange}: any) => {
  return (
    <div className="ae-JsonEditor">
      <AmisCodeEditor value={value} onChange={onChange} />
    </div>
  );
};

const chartDefaultConfig = {
  xAxis: {
    type: 'category',
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    show: true,
    axisLine: {
      show: true
    },
    axisTick: {
      show: true
    },
    splitLine: {
      show: false
    },
    axisLabel: {
      show: true,
      fontSize: 12,
      color: '#171A1D66'
    }
  },
  yAxis: {
    type: 'value',
    show: true,
    axisLine: {
      show: true
    },
    axisTick: {
      show: true
    },
    splitLine: {
      show: false
    },
    axisLabel: {
      show: true,
      fontSize: 12,
      color: '#171A1D66'
    },
    splitNumber: 5,
    
  },
  series: [
    {
      name: '柱状数据',
      data: [820, 932, 901, 934, 1290, 1330, 1320],
      type: 'bar',
      label: {
        show: false,
        fontSize: 12,
        color: '#000'
      },
      show: true,
      borderRadiusLeftTop: 4,
      borderRadiusRightTop: 4,
      borderRadiusRightBottom: 0,
      borderRadiusLeftBottom: 0,
      itemStyle: {
        borderRadius: [4, 4, 0, 0]
      }
    }
  ],
  tooltip: {
    show: true
  },
  legend: {
    show: false
  },
  dataZoom: [
    {
      show: false,
      start: 0,
      end: 100,
      cusStart: 0,
      cusEnd: 100
    }
  ],
  color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
  backgroundColor: 'transparent'
};

const otherConfig = {
  title: '其他',
  className: 'p-none',
  data: {
    config: chartDefaultConfig
  },
  body: getSchemaTpl('collapseGroup', [
    {
      title: '标题配置',
      body: [
        {
          type: 'switch',
          label: '标题显示',
          name: 'config.title.show',
          value: "${config.title.show}",
        },
        getSchemaTpl('label', {
          label: '标题文本',
          hiddenOn: '!config.title.show',
          name: 'config.title.text',
          value: "${config.title.text}",
        }),
        {
          type: 'switch',
          label: '标题提示',
          hiddenOn: '!config.title.show',
          name: 'config.title.showSubtext',
          value: "${config.title.showSubtext}",
        },
        {
          type: "formula",
          name: "config.title.subtextStyle.fontSize",
          formula: "${config.title.showSubtext ? 14 : 0}"
        },
        getSchemaTpl('label', {
          label: '提示内容',
          hiddenOn: '!config.title.show || !config.title.showSubtext',
          name: 'config.title.subtext',
          value: "${config.title.subtext}",
        }),
      ]
    },
    {
      title: '工具栏配置',
      body: [
        {
          type: 'switch',
          label: '工具栏显示',
          name: 'config.toolbox.show',
          value: "${config.toolbox.show}",
        },
        {
          type: 'switch',
          label: '复制为图片',
          hiddenOn: '!config.toolbox.show',
          name: 'config.toolbox.feature.saveAsImage.show',
          value: "${config.toolbox.feature.saveAsImage.show}",
        }
      ]
    }
  ])
};

const normalConfig = {
  title: '常用配置',
  body: [
    {
      label: '主题',
      type: 'select',
      name: 'chartThemeName',
      value: "${chartThemeName}",
      options: [
        {label: '默认', value: ''},
        {label: 'vintage', value: 'vintage'},
        {label: 'westeros', value: 'westeros'},
        {label: 'wonderland', value: 'wonderland'},
        {label: 'chalk', value: 'chalk'},
        {label: 'macarons', value: 'macarons'},
        {label: 'shine', value: 'shine'},
        {label: 'dark', value: 'dark'},
        {label: 'essos', value: 'essos'},
        {label: 'walden', value: 'walden'},
        {label: 'infographic', value: 'infographic'},
        {label: 'roma', value: 'roma'},
        {label: 'purple-passion', value: 'purple-passion'},        
      ]
    },
    {
      label: '自定义-柱形粗细',
      type: "input-number",
      name: "config.series[0].barWidth",
      value: "${config.series[0].barWidth}",
    },
    {
      label: '自定义-最大宽度',
      type: "input-number",
      name: "config.series[0].barMaxWidth",
      value: "${config.series[0].barMaxWidth}",
    },
    {
      label: '自定义-最小宽度',
      type: "input-number",
      name: "config.series[0].barMinWidth",
      value: "${config.series[0].barMinWidth}",
    },
    {
      label: '自定义-柱形背景',
      type: "input-color",
      name: "config.series[0].backgroundStyle.color",
      value: "${config.series[0].backgroundStyle.color}",
    },
    {
      type: "formula",
      name: "config.series[0].showBackground",
      formula: "${config.series[0].backgroundStyle.color ? true : false}",
    },
    {
      label: '自定义-柱形圆角左上',
      type: "input-number",
      name: "config.series[0].borderRadiusLeftTop",
      value: "${config.series[0].borderRadiusLeftTop}",
    },
    {
      label: '自定义-柱形圆角右上',
      type: "input-number",
      name: "config.series[0].borderRadiusRightTop",
      value: "${config.series[0].borderRadiusRightTop}",
    },
    {
      label: '自定义-柱形圆角右下',
      type: "input-number",
      name: "config.series[0].borderRadiusRightBottom",
      value: "${config.series[0].borderRadiusRightBottom}",
    },
    {
      label: '自定义-柱形圆角左下',
      type: "input-number",
      name: "config.series[0].borderRadiusLeftBottom",
      value: "${config.series[0].borderRadiusLeftBottom}",
    },
    {
      type: "formula",
      name: "config.series[0].itemStyle.borderRadius",
      formula: "${[config.series[0].borderRadiusLeftTop, config.series[0].borderRadiusRightTop, config.series[0].borderRadiusRightBottom, config.series[0].borderRadiusLeftBottom]}",
    },
  ]
}

const xAxisConfig = {
  title: '坐标轴-横轴',
  body: [
    {
      type: 'switch',
      label: '显示横轴',
      name: 'config.xAxis.show',
      value: "${config.xAxis.show}",
    },
    getSchemaTpl('label', {
      label: '标题内容',
      hiddenOn: '!config.xAxis.show',
      name: 'config.xAxis.name',
      value: "${config.xAxis.name}",
    }),
    {
      type: 'switch',
      label: '轴线',
      hiddenOn: '!config.xAxis.show',
      name: 'config.xAxis.axisLine.show',
      value: "${config.xAxis.axisLine.show}",
    },
    {
      type: 'switch',
      label: '刻度线',
      hiddenOn: '!config.xAxis.show',
      name: 'config.xAxis.axisTick.show',
      value: "${config.xAxis.axisTick.show}",
    },
    {
      type: 'switch',
      label: '网格线',
      hiddenOn: '!config.xAxis.show',
      name: 'config.xAxis.splitLine.show',
      value: "${config.xAxis.splitLine.show}",
    },
    {
      type: 'switch',
      label: '轴标签',
      hiddenOn: '!config.xAxis.show',
      name: 'config.xAxis.axisLabel.show',
      value: "${config.xAxis.axisLabel.show}",
    },
    {
      label: '文本颜色',
      type: "input-color",
      hiddenOn: '!config.xAxis.show || !config.xAxis.axisLabel.show',
      name: 'config.xAxis.axisLabel.color',
      value: "${config.xAxis.axisLabel.color}",
    },
    {
      label: '自定义-大小',
      type: "input-number",
      name: "config.xAxis.axisLabel.fontSize",
      value: "${config.xAxis.axisLabel.fontSize}",
      hiddenOn: '!config.xAxis.show || !config.xAxis.axisLabel.show',
    },
    {
      type: 'switch',
      label: '自定义-旋转',
      name: 'config.xAxis.axisLabel.rotate',
      value: "${config.xAxis.axisLabel.rotate}",
      hiddenOn: '!config.xAxis.show || !config.xAxis.axisLabel.show',
    },
    {
      type: 'switch',
      label: '自定义-稀疏',
      name: 'config.xAxis.axisLabel.interval',
      value: "${config.xAxis.axisLabel.interval}",
      hiddenOn: '!config.xAxis.show || !config.xAxis.axisLabel.show',
    },
  ]
};

const yAxisConfig = {
  title: '坐标轴-纵轴',
  body: [
    {
      type: 'switch',
      label: '显示纵轴',
      name: 'config.yAxis.show',
      value: "${config.yAxis.show}",
    },
    getSchemaTpl('label', {
      label: '标题内容',
      hiddenOn: '!config.yAxis.show',
      name: 'config.yAxis.name',
      value: "${config.yAxis.name}",
    }),
    {
      type: 'switch',
      label: '轴线',
      hiddenOn: '!config.yAxis.show',
      name: 'config.yAxis.axisLine.show',
      value: "${config.yAxis.axisLine.show}",
    },
    {
      type: 'switch',
      label: '刻度线',
      hiddenOn: '!config.yAxis.show',
      name: 'config.yAxis.axisTick.show',
      value: "${config.yAxis.axisTick.show}",
    },
    {
      type: 'switch',
      label: '网格线',
      hiddenOn: '!config.yAxis.show',
      name: 'config.yAxis.splitLine.show',
      value: "${config.yAxis.splitLine.show}",
    },
    {
      type: 'switch',
      label: '轴标签',
      hiddenOn: '!config.yAxis.show',
      name: 'config.yAxis.axisLabel.show',
      value: "${config.yAxis.axisLabel.show}",
    },
    {
      label: '自定义-颜色',
      type: "input-color",
      name: 'config.yAxis.axisLabel.color',
      value: "${config.yAxis.axisLabel.color}",
      hiddenOn: '!config.yAxis.show || !config.yAxis.axisLabel.show',
    },
    {
      label: '自定义-大小',
      type: "input-number",
      name: "config.yAxis.axisLabel.fontSize",
      value: "${config.yAxis.axisLabel.fontSize}",
      hiddenOn: '!config.yAxis.show || !config.yAxis.axisLabel.show',
    },
    {
      type: 'switch',
      label: '自定义-旋转',
      name: 'config.yAxis.axisLabel.rotate',
      value: "${config.yAxis.axisLabel.rotate}",
      hiddenOn: '!config.yAxis.show || !config.yAxis.axisLabel.show',
    },
    {
      type: 'switch',
      label: '自定义-稀疏',
      name: 'config.yAxis.axisLabel.interval',
      value: "${config.yAxis.axisLabel.interval}",
      hiddenOn: '!config.yAxis.show || !config.yAxis.axisLabel.show',
    },
    {
      label: '最小值',
      type: "input-number",
      name: "config.yAxis.min",
      value: "${config.yAxis.min}",
      hiddenOn: '!config.yAxis.show',
    },
    {
      label: '最大值',
      type: "input-number",
      name: "config.yAxis.max",
      value: "${config.yAxis.max}",
      hiddenOn: '!config.yAxis.show',
    },
    {
      label: '刻度数',
      type: "input-number",
      name: "config.yAxis.splitNumber",
      value: "${config.yAxis.splitNumber}",
      hiddenOn: '!config.yAxis.show',
    },
  ]
};

const legendConfig = {
  title: '图例',
  body: [
    {
      type: 'switch',
      label: '显示图例',
      name: 'config.legend.show',
      value: "${config.legend.show}",
    },
    {
      type: 'switch',
      label: '是否分页',
      hiddenOn: '!config.legend.show',
      name: 'config.legend.pageShow',
      value: "${config.legend.pageShow}",
    },
    {
      type: "formula",
      name: "config.legend.type",
      formula: "${config.legend.pageShow ? 'scroll' : 'plain'}"
    },
  ]
}

const labelConfig = {
  title: '数据标签',
  body: [
    {
      type: 'switch',
      label: '显示标签',
      name: 'config.series[0].label.show',
      value: "${config.series[0].label.show}",
    },
    {
      label: '标签字号',
      type: "input-number",
      name: "config.series[0].label.fontSize",
      value: "${config.series[0].label.fontSize}",
      hiddenOn: '!config.series[0].label.show',
    },
    {
      label: '文本颜色',
      type: "input-color",
      name: 'config.series[0].label.color',
      value: "${config.series[0].label.color}",
      hiddenOn: '!config.series[0].label.show',
    },
  ]
}

const dataZoomConfig = {
  title: '缩略轴',
  body: [
    {
      type: 'switch',
      label: '显示缩略轴',
      name: 'config.dataZoom[0].show',
      value: "${config.dataZoom[0].show}",
    },
    {
      type: "formula",
      name: "config.dataZoom[0].end",
      formula: "${config.dataZoom[0].show ? config.dataZoom[0].cusEnd : 100}"
    },
    {
      type: "formula",
      name: "config.dataZoom[0].start",
      formula: "${config.dataZoom[0].show ? config.dataZoom[0].cusStart : 0}"
    },
    {
      label: '起始范围',
      type: "input-number",
      name: "config.dataZoom[0].cusStart",
      value: "${config.dataZoom[0].cusStart}",
      hiddenOn: '!config.dataZoom[0].show',
    },
    {
      label: '结束范围',
      type: "input-number",
      name: "config.dataZoom[0].cusEnd",
      value: "${config.dataZoom[0].cusEnd}",
      hiddenOn: '!config.dataZoom[0].show',
    },
  ]
}

const tooltipConfig = {
  title: '提示信息',
  body: [
    {
      type: 'switch',
      label: '显示提示信息',
      name: 'config.tooltip.show',
      value: "${config.tooltip.show}",
    }
  ]
}

const DEFAULT_EVENT_PARAMS = [
  {
    type: 'object',
    properties: {
      data: {
        type: 'object',
        title: '数据',
        properties: {
          componentType: {
            type: 'string',
            title: 'componentType'
          },
          seriesType: {
            type: 'string',
            title: 'seriesType'
          },
          seriesIndex: {
            type: 'number',
            title: 'seriesIndex'
          },
          seriesName: {
            type: 'string',
            title: 'seriesName'
          },
          name: {
            type: 'string',
            title: 'name'
          },
          dataIndex: {
            type: 'number',
            title: 'dataIndex'
          },
          data: {
            type: 'object',
            title: 'data'
          },
          dataType: {
            type: 'string',
            title: 'dataType'
          },
          value: {
            type: 'number',
            title: 'value'
          },
          color: {
            type: 'string',
            title: 'color'
          }
        }
      }
    }
  }
];



export class BarChartPlugin extends BasePlugin {
  static id = 'BarChartPlugin';
  // 关联渲染器名字
  rendererName = 'bar-chart';
  $schema = '/schemas/ChartSchema.json';

  // 组件名称
  name = '柱状图';
  isBaseComponent = true;
  description = 'echarts 柱状图';
  docLink = '/amis/zh-CN/components/chart';
  tags = ['报表'];
  icon = 'fa fa-pie-chart';
  pluginIcon = 'chart-plugin';
  scaffold = {
    type: 'bar-chart',
    isEdit: true,
    config: chartDefaultConfig,
    replaceChartOption: true,
    datasetInfo: {},
    xAxisField: [],
    yAxisField: []
  };
  previewSchema = {
    ...this.scaffold,
    api: ''
  };

  // 事件定义
  events: RendererPluginEvent[] = [
    {
      eventName: 'init',
      eventLabel: '初始化',
      description: '组件实例被创建并插入 DOM 中时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              description: '当前数据域，可以通过.字段名读取对应的值'
            }
          }
        }
      ]
    },
    {
      eventName: 'click',
      eventLabel: '鼠标点击',
      description: '鼠标点击时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'mouseover',
      eventLabel: '鼠标悬停',
      description: '鼠标悬停时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'legendselectchanged',
      eventLabel: '切换图例选中状态',
      description: '切换图例选中状态时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              properties: {
                name: {
                  type: 'string',
                  title: 'name'
                },
                selected: {
                  type: 'object',
                  title: 'selected'
                }
              }
            }
          }
        }
      ]
    }
  ];

  // 动作定义
  actions: RendererPluginAction[] = [
    {
      actionType: 'reload',
      actionLabel: '重新加载',
      description: '触发组件数据刷新并重新渲染'
    },
    {
      actionType: 'setValue',
      actionLabel: '变量赋值',
      description: '触发组件数据更新'
    }
    // 特性动作太多了，这里先不加了，可以通过写代码配置
  ];

  panelTitle = '柱状图';
  panelJustify = true;
  panelBodyCreator = (context: BaseEventContext) => {
    const handleConfig = (data:any) => {
      // 创建配置的深拷贝
      const configCopy = JSON.parse(JSON.stringify(context.schema.config));
      const previewConfigCopy = JSON.parse(JSON.stringify(this.previewSchema.config));
      
      if (data.xAxis.length == 1) {
        // "data": "${list|pick:entries}",
        let name = data.xAxis[0].name
        configCopy.xAxis.data = '${list|pick:' + name + '}'
        previewConfigCopy.xAxis.data = '${list|pick:' + name + '}'
      }
      if (data.yAxis.length == 1) {
        let name = data.yAxis[0].name
        configCopy.series[0].data = '${list|pick:' + name + '}'
        previewConfigCopy.series[0].data = '${list|pick:' + name + '}'
      }

      // 保存轴字段选择到schema中
      const newSchema = {
        ...context.schema,
        config: configCopy,
        xAxisField: data.xAxis,
        yAxisField: data.yAxis
      };

      // 使用完整对象替换更新配置
      context.schema.config = configCopy;
      context.schema.xAxisField = data.xAxis;
      context.schema.yAxisField = data.yAxis;
      this.previewSchema.config = previewConfigCopy;
      
      // 通知编辑器进行更新
      context.node && this.manager.store.changeValueById(context.node.id, newSchema);
      
      this.panelBodyCreator(context);
    }

    const handleDataSet = (data:any) => {
      const apiUrl = `/admin-api/system/form-field-value/page/${data.associatedDataTable}?pageNo=1&pageSize=100`;
      
      // 创建完整的新schema以触发更新
      const newSchema = {
        ...context.schema,
        api: apiUrl,
        datasetInfo: data
      };
      
      this.previewSchema.api = apiUrl;
      context.schema.api = apiUrl;
      context.schema.datasetInfo = data;
      
      // 通知编辑器进行更新
      context.node && this.manager.store.changeValueById(context.node.id, newSchema);
      
      this.panelBodyCreator(context);
    }

    return !context.schema.isEdit?[]:[
      getSchemaTpl('tabs', [
        {
          title: '交互',
          data: {
            config: chartDefaultConfig
          },
          body: [<ChartDataInteraction 
            context={context} 
            config={chartDefaultConfig} 
            handleConfig={handleConfig} 
            selectDataSet={handleDataSet} 
            datasetInfo={context.schema.datasetInfo}
            xAxisField={context.schema.xAxisField}
            yAxisField={context.schema.yAxisField}
          />],
          className: 'p-none editorpanel'
        },
        {
          title: '样式',
          data: {
            config: chartDefaultConfig
          },
          body: getSchemaTpl('collapseGroup', [
            
            normalConfig,
            xAxisConfig,
            yAxisConfig,
            legendConfig,
            labelConfig,
            dataZoomConfig,
            tooltipConfig,
            {
              title: '宽高设置',
              body: [
                getSchemaTpl('style:widthHeight', {
                  widthSchema: {
                    label: tipedLabel(
                      '宽度',
                      '默认宽度为父容器宽度，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('100%')
                  },
                  heightSchema: {
                    label: tipedLabel(
                      '高度',
                      '默认高度为300px，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('300px')
                  }
                })
              ]
            },
            ...getSchemaTpl('theme:common', {exclude: ['layout']}),
          ])
        },
        otherConfig,
        {
          title: '事件',
          className: 'p-none',
          body: [
            getSchemaTpl('eventControl', {
              name: 'onEvent',
              ...getEventControlConfig(this.manager, context)
            })
          ]
        }
      ])
    ];
  };

  editDrillDown(id: string) {
    const manager = this.manager;
    const store = manager.store;
    const node = store.getNodeById(id);
    const value = store.getValueOf(id);

    const dialog = (value.clickAction && value.clickAction.dialog) || {
      title: '标题',
      body: ['<p>内容 <code>${value|json}</code></p>']
    };

    node &&
      value &&
      this.manager.openSubEditor({
        title: '配置 DrillDown 详情',
        value: {
          type: 'container',
          ...dialog
        },
        slot: {
          type: 'container',
          body: '$$'
        },
        typeMutable: false,
        onChange: newValue => {
          newValue = {
            ...value,
            clickAction: {
              actionType: 'dialog',
              dialog: newValue
            }
          };
          manager.panelChangeValue(newValue, diff(value, newValue));
        }
      });
  }
}

registerEditorPlugin(BarChartPlugin);
