import React, {FC, useEffect, useState} from 'react';
import './index.scss';
import {Avatar, Button, Modal} from 'amis';
import ThemeSwitch from '@/component/ThemeSwitch';
import {LogoutConfirmDialog} from '@/utils/schemaDataSet/LogoutConfirmDialog';
import AMISRenderer from '@/component/AMISRenderer';
import {isFromAppbuild, goToWorkbench, clearSourceInfo} from '@/utils/routeUtils';
import gongzuo from '@/image/common_icons/gongzuo.png';
import dajian from '@/image/common_icons/dajian.png';
import zujian from '@/image/common_icons/zujian.png';
import yingyong from '@/image/common_icons/yingyong.png';
import biaodan from '@/image/common_icons/biaodan.png';
import yemian from '@/image/common_icons/re_page.png';

// 定义导航项类型
interface NavItem {
  key: string;
  label: string;
  icon: React.ReactNode;
}

const renderHeader: FC<any> = (props: any) => {
  const [showUserModal, setShowUserModal] = React.useState(false);
  // 添加状态控制
  const [showLogoutDialog, setShowLogoutDialog] = React.useState(false);
  // 添加状态记录是否来自appbuild
  const [fromAppbuild, setFromAppbuild] = React.useState(false);
  // 添加当前选中的导航项状态
  const [activeNavKey, setActiveNavKey] = useState<string>('form');
  // 添加模板类型状态
  const [templateType, setTemplateType] = useState<string>('3');

  // 组件挂载时检查是否来自appbuild
  useEffect(() => {
    const isFromAppbuildValue = isFromAppbuild();
    setFromAppbuild(isFromAppbuildValue);
  }, []);

  // 跳转到个人设置
  const goToAccount = () => {
    setShowUserModal(false);
    props.history.push('/platform/account');
    // 触发切换到账户页面的事件
    const event = new CustomEvent('switchToAccount', {
      detail: {target: 'account'}
    });
    window.dispatchEvent(event);
  };

  // 退出登录
  const LogOut = () => {
    // 清除来源信息
    clearSourceInfo();
    props.history.push('/login'); // 先跳转
    setTimeout(() => {
      // 延迟清除状态
      props.store.setAccessToken('');
      props.store.setTenantId('');
      props.store.setUserInfo(false);
    }, 100);
  };

  // 退出登录确认
  const handleLogout = () => {
    setShowUserModal(false);
    setShowLogoutDialog(true);
    // props.store.setShowLogoutDialog(true);
  };

  // 生成企业 Logo
  const generateBusinessLogo = (name: string) => {
    if (!name) return '';
    return name.charAt(0).toUpperCase();
  };

  // 处理导航项点击事件
  const handleNavClick = (key: string) => {
    setActiveNavKey(key);
    // 根据不同的导航项设置不同的模板类型
    switch (key) {
      case 'component':
        setTemplateType('1'); // 组件模板
        // 触发类型变更事件
        const componentEvent = new CustomEvent('templateTypeChange', {
          detail: { type: '1' }
        });
        window.dispatchEvent(componentEvent);
        break;
      case 'application':
        setTemplateType('2'); // 应用模板
        // 触发类型变更事件
        const appEvent = new CustomEvent('templateTypeChange', {
          detail: { type: '2' }
        });
        window.dispatchEvent(appEvent);
        break;
      case 'form':
        setTemplateType('3'); // 表单模板
        // 触发类型变更事件
        const formEvent = new CustomEvent('templateTypeChange', {
          detail: { type: '3' }
        });
        window.dispatchEvent(formEvent);
        break;
      case 'page':
        setTemplateType('4'); // 页面模板
        // 触发类型变更事件
        const pageEvent = new CustomEvent('templateTypeChange', {
          detail: { type: '4' }
        });
        window.dispatchEvent(pageEvent);
        break;
      default:
        break;
    }
  };

  // 组件图标
  const ComponentIcon = () => (
    <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', width: 24, height: 24}}>
      <img src={zujian} alt="" />
    </div>
  );

  // 应用图标
  const ApplicationIcon = () => (
    <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', width: 24, height: 24}}>
             <img src={yingyong} alt="" />
    </div>
  );

  // 表单图标
  const FormIcon = () => (
    <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', width: 24, height: 24}}>
      <img src={biaodan} alt="" />

    </div>
  );

  // 页面图标
  const PageIcon = () => (
    <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center', width: 24, height: 24}}>
      <img src={yemian} alt="" />
    </div>
  );

  return (
    <div className="manageHeader">
      <div className="manageHeader-left">
        <div className="btn-group">
          <Button level="link" onClick={() => goToWorkbench(props)}>
            {fromAppbuild ? <img src={dajian} alt="dajian" /> : <img src={gongzuo} alt="gongzuo" />}
            {fromAppbuild ? '返回搭建平台' : '返回工作台'}
          </Button>
        </div>
      </div>

      <div className="manageHeader-center">
        <Button 
          level="link" 
          onClick={() => handleNavClick('component')}
          className={`nav-item ${activeNavKey === 'component' ? 'active' : ''}`}
        >
          <ComponentIcon />
          <span className="nav-text">组件</span>
        </Button>
        <Button 
          level="link" 
          onClick={() => handleNavClick('application')}
          className={`nav-item ${activeNavKey === 'application' ? 'active' : ''}`}
        >
          <ApplicationIcon />
          <span className="nav-text">应用</span>
        </Button>
        <Button 
          level="link" 
          onClick={() => handleNavClick('form')}
          className={`nav-item ${activeNavKey === 'form' ? 'active' : ''}`}
        >
          <FormIcon />
          <span className="nav-text">表单</span>
        </Button>
        <Button 
          level="link" 
          onClick={() => handleNavClick('page')}
          className={`nav-item ${activeNavKey === 'page' ? 'active' : ''}`}
        >
          <PageIcon />
          <span className="nav-text">页面</span>
        </Button>
      </div>

      <div className="manageHeader-right">
        <ThemeSwitch store={props.store} className="theme-switch" />

        <div className="user-info" onClick={() => setShowUserModal(true)}>
          {props.store.userInfo?.avatar ? (
            <Avatar src={props.store.userInfo?.avatar} size={32} />
          ) : (
            <div className="business-logo">
              {generateBusinessLogo(
                props.store.userInfo?.business_name ||
                  props.store.userInfo?.nickname
              )}
            </div>
          )}
          <span>{props.store.userInfo?.nickname}</span>
        </div>

        <Modal
          show={showUserModal}
          onClose={() => setShowUserModal(false)}
          onHide={() => setShowUserModal(false)}
          closeOnOutside={true}
          closeOnEsc={true}
          className="user-profile-modal"
        >
          <div className="pr-4 py-4" style={{width: '240px'}}>
            <div className="flex items-center">
              <Avatar
                src={props.store.userInfo?.avatar}
                size={40}
                className="mr-4 ml-5"
              />
              <div>
                <div className="font-bold text-sm mb-1">
                  {props.store.userInfo?.nickname}
                </div>
                <div className="text-xs text-muted mr-4">
                  {props.store.userInfo?.email}
                </div>
              </div>
            </div>
            <div className="divider"></div>
            <Button block level="link" onClick={goToAccount}>
              <i className="fa fa-cog" />
              个人设置
            </Button>
            <Button level="link" block onClick={handleLogout}>
              <i className="fa fa-sign-out-alt" />
              退出登录
            </Button>
          </div>
        </Modal>

        {/* 退出确认弹窗 */}
        <AMISRenderer
          show={showLogoutDialog}
          onClose={() => setShowLogoutDialog(false)}
          onConfirm={() => {
            setShowLogoutDialog(false);
            LogOut();
          }}
          schema={LogoutConfirmDialog(showLogoutDialog)}
        />
      </div>
    </div>
  );
};

export default renderHeader;
