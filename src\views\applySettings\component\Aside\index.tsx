import React, {FC} from 'react';
import './index.scss';
import {toast} from 'amis';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

// 图片
import arrow_right_icon from '@/image/common_icons/arrow_right_icon.png';



const renderAside: FC<any> = (props: any) => {
  /* data 数据 */
  // 子项
  const [isOpenSub, setIsOpenSub] = React.useState<any>([]);

  // 使用父组件传递的checkNavItem状态
  const { checkNavItem, setCheckNavItem } = props;
  /* data 数据 end */

  /* methods 方法 */
  // 子项是否打开事件,isOpenSub中是否有父id,如果有父id,就去除父id,如果没有父id,就添加父id
  const getIsOpenSubId = (id: any) => {
    let temp = [...isOpenSub];
    if (temp.includes(id)) {
      temp = temp.filter((item: any) => item !== id);
    } else {
      temp.push(id);
    }
    setIsOpenSub(temp);
  };
  // 选中的导航菜单栏
  const getCheckNavItem = (item: any) => {
    if (!item.children) {
      setCheckNavItem(item);
    }else{
      getIsOpenSubId(item.id)
    }
  };
  /* methods 方法 end */

  /* created 初始化 */
  React.useEffect(() => {
    // 当用户点击侧边栏菜单时，更新路由
    if(checkNavItem && checkNavItem.path){
      const currentPath = `/app${props.match.params.appId}/admin/appSetting/${checkNavItem.path}`;
      if(props.history.location.pathname !== currentPath) {
        props.history.push(currentPath);
      }
    }
  }, [checkNavItem]);

  /* created 初始化 end */

  return (
    <div className="navAside">
      <div className="navAside-menu">
        {props.navMenuList.map((item:any) => {
          return (
            <div key={item.id}>
              <div
                className={item.level == checkNavItem.level ? "navAside-menu-item itemClick" : "navAside-menu-item"}
                key={item.id}
                onClick={() => {getCheckNavItem(item)}}
              >
                <div className="navAside-menu-item-icon">
                  {/* <img
                    className="navAside-menu-item-icon-img"
                    src={item.icon}
                  /> */}

                  <i className={`fa-solid fa-${item.icon}`}></i>
                </div>
                <div className="navAside-menu-item-name">{item.name}</div>
                <div className="navAside-menu-item-arrow">
                  {item.children && (
                    // <img
                    //   className={
                    //     isOpenSub.indexOf(item.id) != -1
                    //       ? 'navAside-menu-item-arrow-img arrowDown'
                    //       : 'navAside-menu-item-arrow-img'
                    //   }
                    //   src={arrow_right_icon}
                    // />

                    <i className={
                        isOpenSub.indexOf(item.id) != -1
                          ? 'fa-solid fa-caret-right navAside-menu-item-arrow-img arrowDown'
                          : 'fa-solid fa-caret-right navAside-menu-item-arrow-img'
                      }></i>
                  )}
                </div>
              </div>
              {isOpenSub.indexOf(item.id) != -1 &&
                item.children?.map((children_item:any) => {
                  return (
                    <div
                      className={ children_item.level == checkNavItem.level ? "navAside-menu-subitem itemClick" : "navAside-menu-subitem"}
                      key={children_item.id}
                      onClick={() => {getCheckNavItem(children_item)}}
                    >
                      <div className="navAside-menu-subitem-name">
                        {children_item.name}
                      </div>
                    </div>
                  );
                })}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default renderAside;
