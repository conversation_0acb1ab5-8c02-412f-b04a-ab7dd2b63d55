const fs = require('fs');
const path = require('path');

// 配置
const config = {
  // 源文件路径
  sourceFiles: [
    // path.resolve(__dirname, '../src/scss/_properties.scss'),
    path.resolve(__dirname, '../src/scss/_variables.scss'),
    path.resolve(__dirname, '../src/scss/_dark-variables.scss'),
    path.resolve(__dirname, '../src/scss/_cxd-variables.scss')
  ],
  // 输出文件路径
  outputFile: path.resolve(__dirname, '../src/component/ThemeSwitch/theme-vars.ts'),
  // 是否包含注释
  includeComments: true,
  // 是否解析嵌套变量
  resolveNestedVars: true
};

// 存储提取的变量
const extractedVars = {
  common: {},
  dark: {},
  cxd: {}
};

// 当前处理的主题
let currentTheme = 'common';

// 提取 CSS 变量的函数
function extractCSSVars(content, theme) {
  // 存储当前注释
  let currentComment = '';
  
  // 按行分割内容
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 检查是否是主题选择器
    if (line.includes('.dark') || line.includes(':root.dark')) {
      currentTheme = 'dark';
      continue;
    } else if (line.includes('.cxd') || line.includes(':root.cxd')) {
      currentTheme = 'cxd';
      continue;
    } else if (line === '}') {
      currentTheme = theme; // 恢复到默认主题
      continue;
    }
    
    // 收集注释
    if (line.startsWith('//') && config.includeComments) {
      currentComment = line.substring(2).trim();
      continue;
    }
    
    // 提取 CSS 变量
    const cssVarMatch = line.match(/--[\w-]+\s*:\s*([^;]+);/);
    if (cssVarMatch) {
      const varName = cssVarMatch[0].split(':')[0].trim();
      let varValue = cssVarMatch[1].trim();
      
      // 处理值中的引号
      if (varValue.startsWith("'") && varValue.endsWith("'")) {
        varValue = varValue.substring(1, varValue.length - 1);
      }
      
      // 存储变量
      extractedVars[currentTheme][varName] = {
        value: varValue,
        comment: currentComment,
        hasNestedVars: varValue.includes('var(--')
      };
      
      // 重置注释
      currentComment = '';
    }
  }
}

// 处理每个源文件
config.sourceFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 确定默认主题
    let defaultTheme = 'common';
    if (filePath.includes('dark-variables')) {
      defaultTheme = 'dark';
    } else if (filePath.includes('cxd-variables')) {
      defaultTheme = 'cxd';
    }
    
    // 提取变量
    extractCSSVars(content, defaultTheme);
    console.log(`Processed: ${path.basename(filePath)}`);
  } else {
    console.warn(`File not found: ${filePath}`);
  }
});

// 解析嵌套变量
if (config.resolveNestedVars) {
  const resolveNestedVars = (theme) => {
    let hasChanges = false;
    
    Object.entries(extractedVars[theme]).forEach(([varName, varData]) => {
      if (varData.hasNestedVars) {
        const nestedVarRegex = /var\(([^)]+)\)/g;
        let match;
        let resolvedValue = varData.value;
        
        while ((match = nestedVarRegex.exec(varData.value)) !== null) {
          const nestedVarName = match[1].trim();
          const fallbackValue = match[1].includes(',') 
            ? match[1].split(',')[1].trim() 
            : null;
          
          // 查找嵌套变量的值
          const nestedVarValue = extractedVars[theme][nestedVarName]?.value || 
                                extractedVars.common[nestedVarName]?.value || 
                                fallbackValue || 
                                'inherit';
          
          // 替换嵌套变量
          resolvedValue = resolvedValue.replace(match[0], nestedVarValue);
        }
        
        if (resolvedValue !== varData.value) {
          extractedVars[theme][varName].resolvedValue = resolvedValue;
          hasChanges = true;
        }
      }
    });
    
    return hasChanges;
  };
  
  // 多次解析，直到所有嵌套变量都被解析
  let maxIterations = 10; // 防止无限循环
  let hasChanges = true;
  
  while (hasChanges && maxIterations > 0) {
    hasChanges = resolveNestedVars('common') || 
                resolveNestedVars('dark') || 
                resolveNestedVars('cxd');
    maxIterations--;
  }
}

// 生成 TypeScript 代码
function generateTypeScriptCode() {
  let code = `// 自动生成的主题变量文件，请勿手动修改

/**
 * 暗色主题变量
 */
export const darkThemeVars = {
`;

  // 添加亮色主题变量到暗色主题变量（交换内容）
  Object.entries(extractedVars.cxd).forEach(([varName, varData]) => {
    if (varData.comment && config.includeComments) {
      code += `  // ${varData.comment}\n`;
    }
    
    const valueToUse = config.resolveNestedVars && varData.resolvedValue 
      ? varData.resolvedValue 
      : varData.value;
      
    code += `  '${varName}': '${valueToUse}',\n`;
  });

  // 添加通用变量到暗色主题
  Object.entries(extractedVars.common).forEach(([varName, varData]) => {
    if (!extractedVars.cxd[varName]) {
      if (varData.comment && config.includeComments) {
        code += `  // ${varData.comment}\n`;
      }
      
      const valueToUse = config.resolveNestedVars && varData.resolvedValue 
        ? varData.resolvedValue 
        : varData.value;
        
      code += `  '${varName}': '${valueToUse}',\n`;
    }
  });

  code += `};

/**
 * 亮色主题变量
 */
export const cxdThemeVars = {
`;

  // 添加暗色主题变量到亮色主题变量（交换内容）
  Object.entries(extractedVars.dark).forEach(([varName, varData]) => {
    if (varData.comment && config.includeComments) {
      code += `  // ${varData.comment}\n`;
    }
    
    const valueToUse = config.resolveNestedVars && varData.resolvedValue 
      ? varData.resolvedValue 
      : varData.value;
      
    code += `  '${varName}': '${valueToUse}',\n`;
  });

  // 添加通用变量到亮色主题
  Object.entries(extractedVars.common).forEach(([varName, varData]) => {
    if (!extractedVars.dark[varName]) {
      if (varData.comment && config.includeComments) {
        code += `  // ${varData.comment}\n`;
      }
      
      const valueToUse = config.resolveNestedVars && varData.resolvedValue 
        ? varData.resolvedValue 
        : varData.value;
        
      code += `  '${varName}': '${valueToUse}',\n`;
    }
  });

  code += `};
`;

  return code;
}

// 写入输出文件
const outputCode = generateTypeScriptCode();
fs.writeFileSync(config.outputFile, outputCode);

console.log(`Generated: ${path.basename(config.outputFile)}`);
console.log(`Total variables: Common: ${Object.keys(extractedVars.common).length}, Dark: ${Object.keys(extractedVars.dark).length}, CXD: ${Object.keys(extractedVars.cxd).length}`); 