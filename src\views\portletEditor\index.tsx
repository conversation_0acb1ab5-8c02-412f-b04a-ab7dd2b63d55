import React, {useEffect, useState} from 'react';
import {Editor, ShortcutKey, setSchemaTpl, PagePlugin, AppRendererProcessor} from 'amis-editor';
import {inject, observer} from 'mobx-react';
import {RouteComponentProps} from 'react-router-dom';
import {toast, Select} from 'amis';
import {currentLocale} from 'i18n-runtime';
import {IMainStore} from '@/store';
import {configureManagerEditorPlugin} from '@/editor/DisabledEditorPlugin'; // 用于隐藏一些不需要的Editor预置组件
import {showRenderers} from '@/editor/disabledRenderers';
import '@/renderer/MyRenderer';
import {cxdThemeVars} from '@/component/ThemeSwitch/theme-vars';
import './index.scss';
import {getAmisJsonFormValue} from '@/utils/amisJsonForm/index';

import {
  findCreateBody,
  replacedCreateBody,
  replacedUpdateBody,
  replacedcolumns,
  replacedViewBody
} from '@/utils/schemaDataSet/commonEditor';
import EditorDialog from '@/component/EditorDialog';
import {
  getFormDataPage, //获取表单数据
  getApplicationPageAndClass, //获取页面信息
  updateFormData //更新表单数据
} from '@/utils/api/api';

const appRendererProcessor: AppRendererProcessor = (renderer, context) => {
  // 只处理 select 类型的组件
  if (!Array.isArray(renderer) && renderer.rendererName === 'select') {
    const baseRenderer = renderer;

    return [
      // 原始 select 组件
      {
        ...baseRenderer,
        name: '选择器',
        order: 0
      },
      // 变体1：带预设选项的 select
      {
        ...baseRenderer,
        name: '选择器(预设选项)',
        order: 1,
        scaffold: {
          ...baseRenderer.scaffold,
          options: [
            {label: '选项A', value: 'A'},
            {label: '选项B', value: 'B'},
            {label: '选项C', value: 'C'}
          ]
        }
      },
      // 变体2：多选 select
      {
        ...baseRenderer,
        name: '多选选择器',
        order: 2,
        scaffold: {
          ...baseRenderer.scaffold,
          multiple: true,
          options: [
            {label: '选项1', value: '1'},
            {label: '选项2', value: '2'},
            {label: '选项3', value: '3'}
          ]
        }
      },
      // 变体3：搜索选择器
      {
        ...baseRenderer,
        name: '搜索选择器',
        order: 3,
        scaffold: {
          ...baseRenderer.scaffold,
          searchable: true,
          options: [
            {label: '苹果', value: 'apple'},
            {label: '香蕉', value: 'banana'},
            {label: '橙子', value: 'orange'}
          ]
        }
      }
    ];
  }

  return renderer;
};

const editorLanguages = [
  {
    label: '简体中文',
    value: 'zh-CN'
  },
  {
    label: 'English',
    value: 'en-US'
  }
];

export interface DSField {
  value: string;
  label: string;
  [propKey: string]: any;
}

setSchemaTpl('formItemName', {
  label: '字段名',
  name: 'name',
  disabled: true,
  type: 'ae-DataBindingControl',
  onBindingChange(field: DSField, onBulkChange: (value: any) => void) {
    onBulkChange(field.resolveEditSchema?.() || {label: field.label});
  }
});

setSchemaTpl('inputType', {
  require: false,
  label: '输入类型',
  name: 'type',
  type: 'select',
  disabled: true,
  creatable: false,
  options: [
    {
      label: '文本',
      value: 'input-text'
    },
    {
      label: '密码',
      value: 'input-password'
    },
    {
      label: '邮箱',
      value: 'input-email'
    },
    {
      label: 'URL',
      value: 'input-url'
    }
  ]
});

// validation

export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<{
    appId: string;
    form?: string;
  }>) {
    const curLanguage = currentLocale(); // 获取当前语料类型
    // 是否schema发生改变
    const isChange = React.useRef<boolean>(false);
    const [isPreview, setIsPreview] = React.useState<boolean>(false);
    const [exitIsOpen, setExitIsOpen] = React.useState<boolean>(false);
    const [pageId, setPageId] = React.useState<string>('');
    const [appId, setAppId] = React.useState<string>('');

    React.useEffect(() => {
      // 更新所有主题相关的类名
      updateElementsWithPrefix('dark', 'cxd');

      // 更新 CSS 变量
      updateCSSVariables();
      let page_id = match.params.form?.split('page')[1];
      let app_id = match.params.appId;
      if (page_id) {
        setPageId(page_id);
      }
      if (app_id) {
        setAppId(app_id);
      }
    }, []);

    // 在组件加载时添加样式
    React.useEffect(() => {
      // 创建唯一的 style id
      const styleId = 'editor-custom-styles';

      // 如果已存在则移除
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        existingStyle.remove();
      }

      // 创建新的 style 标签
      const styleElement = document.createElement('style');
      styleElement.id = styleId;
      styleElement.textContent = `
      .ae-RendererList-group.collapse-content .ae-RendererList-item{
-webkit-flex: 0 0 40px;
width:auto;
margin:0 10px 12px 10px;
}

.ae-RendererList-group.collapse-content .ae-RendererList-item .ae-RendererInfo{
max-width: 180px;
}

.ae-RendererList-group.collapse-content .ae-RendererList-item:hover .ae-RendererInfo{
max-width: 180px;
}

.ae-RendererList-group.collapse-content:last-child{
flex-direction: column;
display: flex;
}
 
      .cxd-Page-body{
      margin:auto!important;
      width:1180px!important;
      }
          .cxd-TextControl-input,
          .cxd-DatePicker,
          .cxd-DateRangePicker,
          .cxd-TextareaControl-input,
          .cxd-Number,
          .cxd-Select,
          .cxd-ResultBox,
          .cxd-LocationPicker,
          // .cxd-ImageControl-addBtn,
          .cxd-FileControl-selectBtn ,
          .cxd-Checkbox--checkbox--default > i,
          .cxd-Checkbox--radio--default > i,
          .cxd-ExcelControl-dropzone ,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugs,
          .cxd-DateRangePicker-rangers{
            background: #fff!important;
          }

          .cxd-CalendarMobile-footer .cxd-DateRangePicker-rangers,
          .cxd-CalendarMobile-footer .date-range-confirm,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugsItem:not(.is-mobile):hover{
            background: #f7f8fa!important;
          }

          .cxd-Checkbox--radio--default,
          .cxd-Checkbox--radio--default:hover:not(.disabled),
          .cxd-Checkbox--checkbox--default--checked:hover:not(.disabled),
          .cxd-Checkbox--checkbox--default:hover:not(.disabled):not(.checked),
          .cxd-Checkbox--checkbox--default,
          .cxd-Cascader-option--text,
          .cxd-PickerColumns-columnItem,
          .cxd-DatePicker-input,
          .cxd-ResultBox-singleValue,
          .cxd-Select-valueWrap ,
          .cxd-DateRangePicker .cxd-DateRangePicker-input,
          .cxd-CalendarMobile-title, .cxd-CalendarMobile-subtitle,
          .cxd-PickerColumns-header,
          .rdt thead tr:first-child th,
          .cxd-CalendarMobile-range-text,
          .cxd-CalendarMobile-body .calendar-wrap,
          .cxd-CalendarMobile-footer .date-range-confirm,
          .rdtSwitch,
          .cxd-DateRangePicker-ranger a,
          .cxd-CalendarMobile-weekdays,
          .cxd-TimeRangeHeaderWrapper,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugsItem,
          td.rdtMonth > span, td.rdtYear > span, td.rdtQuarter > span,
          .cxd-CalendarMobile-time-title{
            color: #000!important;
          }

          .cxd-Button--primary,
          .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeStart .calendar-wrap, .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeStart:hover .calendar-wrap, .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeEnd .calendar-wrap, .cxd-CalendarMobile .cxd-CalendarMobile-calendar-wrap .cxd-CalendarMobile-calendar .rdtPicker .rdtRangeEnd:hover .calendar-wrap,
          {
          color: #fff!important;
          }

          .rdt .rdtPicker td.is-disabled,
          .cxd-CalendarInputWrapper .cxd-CalendarInput-sugsItem.is-highlight{
            background: #e6f0ff!important;
          }

          /* 强制使用亮色主题 */
          .Editor-Demo {
            --colors-neutral-fill-11: #fff !important;
          }

          /* 确保子元素使用亮色主题 */
          .Editor-Demo [class*="dark-"] {
            background-color: var(--colors-neutral-fill-11) !important;
          }
      `;

      // 添加到 head
      document.head.appendChild(styleElement);

      // 清理函数
      return () => {
        const style = document.getElementById(styleId);
        if (style) {
          style.remove();
        }
      };
    }, []);

    // 更新所有带有特定前缀的元素的类名
    const updateElementsWithPrefix = (oldTheme: string, newTheme: string) => {
      // 获取所有元素
      const allElements = document.getElementsByTagName('*');

      // 遍历所有元素
      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];
        const classList = element.classList;

        // 遍历元素的所有类名
        for (let j = 0; j < classList.length; j++) {
          const className = classList[j];
          // 如果类名以旧主题为前缀
          if (className.startsWith(`${oldTheme}-`)) {
            // 移除旧主题类名
            classList.remove(className);
            // 添加新主题类名
            classList.add(
              `${newTheme}-${className.slice(oldTheme.length + 1)}`
            );
            // 因为classList是实时的，所以我们需要调整索引
            j--;
          }
        }
      }
    };

    // 更新 CSS 变量
    const updateCSSVariables = () => {
      const root = document.documentElement;
      Object.entries(cxdThemeVars).forEach(([key, value]) => {
        root.style.setProperty(key, value as string);
      });
    };

    // 页面的 schema
    const [schema, setSchema] = useState<any>('');
    // 页面信息
    const [pageData, setPageData] = useState<any>(false);
    // 获取页面的信息
    const handleGetApplicationPageAndClass = () => {
      let data = {
        id: pageId
      };
      getApplicationPageAndClass(data).then((res: any) => {
        if (res.code == 0) {
          setPageData(res.data);
          handleGetFormDataPage(res.data.pageType);
        } else {
          toast.error(res.msg);
        }
      });
    };
    const [formData, setFormData] = useState<any>(false);
    // 获取表单信息
    const handleGetFormDataPage = (pageType: number | string) => {
      let data = {
        applicationPageId: Number(pageId),
        pageNo: 1,
        pageSize: 10
      };
      getFormDataPage(data).then((res: any) => {
        if (res.code == 0) {
          if (res.data.list.length > 0) {
            if (res.data.list[0]) {
              res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
              let form_data = res.data.list[0];
              setFormData(form_data);
              let renderers: any = showRenderers('portlet');
              configureManagerEditorPlugin(renderers);
              setSchema(form_data.data);
            } else {
              toast.success('暂无表单数据');
            }
          } else {
            toast.success('暂无表单数据');
          }
        } else {
          toast.error(res.msg);
        }
      });
    };
    React.useEffect(() => {
      if (pageId) {
        handleGetApplicationPageAndClass();
      }
    }, [pageId]);

    //切换中英文
    const changeLocale = (value: string) => {
      localStorage.setItem('suda-i18n-locale', value);
      window.location.reload();
    };
    // 编辑变动
    const onChangeEditor = (value: any) => {
      setSchema(value);
      isChange.current = true;
    };
    // 插入节点前事件
    const beforeInsert = (event: any) => {
      if (event.context.data.name) {
        let nameArr = event.context.data.name.split('-');
        event.context.data.name =
          nameArr[nameArr.length - 1] + `_${Date.now()}`;
      }
    };
    // 是否为隐藏属性，隐藏属性是在配置中有，但是在代码编辑器中不可见。
    const isHiddenProps = (props: any) => {
      // console.log('isHiddenProps=======>', props)
      // if (props.name === 'className') {
      //   return true;
      // }
      return false;
    };
    // 面板里面编辑修改前的事件。可通过 event.preventDefault() 阻止。
    const beforeUpdate = (event: any) => {
      // console.log('beforeUpdate=====>', event)
    };

    // 同用保存
    const onUpdateFormDataSave = () => {
      let data: any = {};
      data.id = formData.id;
      data.applicationPageId = formData.applicationPageId;
      data.data = window.JSON.stringify(schema);
      data.field = '';
      updateFormData(data).then((res: any) => {
        if (res.code == 0) {
          handleGetApplicationPageAndClass();
          isChange.current = false;
          toast.success('保存成功');
        } else {
          toast.error(res.msg);
        }
      });
    };

    // 保存
    const onSave = () => {
      if (isChange.current) {
        isChange.current = false;
        // if (pageData.pageType == 1 || pageData.pageType == 2) {
        //   onFieldSaveTable();
        // } else {
        onUpdateFormDataSave();
        // }
      }
    };
    // 退出
    const exit = () => {
      history.push(`/app${match.params.appId}/admin/${match.params.form}`);
    };

    return (
      <div className="Editor-Demo">
        <div className="Editor-header">
          <div className="Editor-title">{pageData.name}</div>

          <div className="Editor-header-actions">
            <ShortcutKey />
            <Select
              className="margin-left-space"
              options={editorLanguages}
              value={curLanguage}
              clearable={false}
              onChange={(e: any) => changeLocale(e.value)}
            />
            {isChange && (
              <div
                className={`header-action-btn m-1 save-btn`}
                onClick={() => onSave()}
              >
                保存
              </div>
            )}
            <div
              className={`header-action-btn m-1 ${isPreview ? 'primary' : ''}`}
              onClick={() => {
                setIsPreview(!isPreview);
              }}
            >
              {isPreview ? '编辑' : '预览'}
            </div>
            {!isPreview && (
              <div
                className={`header-action-btn exit-btn`}
                onClick={() =>
                  isChange.current ? setExitIsOpen(true) : exit()
                }
              >
                退出
              </div>
            )}
          </div>
        </div>
        <div className="Editor-inner">
          <Editor
            theme={'cxd'}
            i18nEnabled={false}
            appRendererProcessor={appRendererProcessor}
            preview={isPreview}
            isMobile={store.isMobile}
            value={schema}
            onChange={e => onChangeEditor(e)}
            onPreview={() => {
              setIsPreview(true);
            }}
            onSave={() => onSave()}
            className="is-fixed"
            $schemaUrl={''}
            showCustomRenderersPanel={true}
            amisEnv={{
              fetcher: store.fetcher,
              notify: store.notify,
              alert: store.alert,
              copy: store.copy
            }}
            beforeInsert={event => beforeInsert(event)}
            isHiddenProps={props => isHiddenProps(props)}
            beforeUpdate={event => beforeUpdate(event)}
          />
          <EditorDialog
            show={exitIsOpen}
            onClose={() => setExitIsOpen(false)}
            onConfirm={exit}
          />
        </div>
      </div>
    );
  })
);
