export const EditBirthdayDialog = (show: boolean, birthday?: string) => {
  // 获取今天的日期字符串
  const today = new Date().toISOString().split('T')[0];  // 格式为 YYYY-MM-DD

  return {
    type: 'dialog',
    title: {
        type: 'html',
        html: '<b>选择出生日期</b>'
      },
    show: show,
    showCloseButton: false,
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请选择出生日期'
      },  
      body: [
        {
          required: true,
          type: 'input-date',
          name: 'birthday',
          label: '',
          value: birthday,
          format: 'YYYY-MM-DD',
          inputFormat: 'YYYY-MM-DD',
          placeholder: '选择日期',
          inputClassName: 'name-input',
          labelClassName: 'name-label',
          maxDate: today  // 使用具体的日期字符串
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'close'
      },
      {
        type: 'button',
        label: '确定',
        level: 'primary',
        actionType: 'confirm'
      }
    ]
  };
};
