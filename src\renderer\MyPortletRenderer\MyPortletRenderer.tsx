import React from 'react';
import {Renderer} from 'amis';
import {FormControlProps} from 'amis-core';
import {Tabs, Tab, TooltipWrapper} from 'amis-ui';
import './MyPortlet.scss';

export interface PortletProps extends FormControlProps {
  label?: string;
  name: string;
  // 标题栏相关属性
  header?: {
    icon?: string;
    title?: string;
    remark?: object; // 更多文字，替代之前的subTitle
  };
  tooltip?: string; // 帮助提示文本
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left'; // 帮助提示位置
  showHeader?: boolean; // 是否显示标题栏
  showMore?: boolean; // 是否显示更多
  headerClassName?: string; // 标题栏类名
  showTabs?: boolean; // 是否显示标签页导航
  showContent?: boolean; // 是否显示内容区域
  tabs?: Array<{
    id: string;
    title: string;
    icon?: string;
    iconPosition?: 'left' | 'right';
    body?: any;
    reload?: boolean;
    mountOnEnter?: boolean;
    unmountOnExit?: boolean;
    toolbar?: Array<any>;
  }>;
  entries?: Array<{
    title: string;
    icon?: string;
    iconBg?: string;
    url?: string;
    openType?: string;
    target?: string;
    group?: string;
  }>;
  tabsMode?: '' | 'line' | 'card' | 'radio' | 'vertical' | 'tiled';
  tabsClassName?: string;
  contentClassName?: string;
  linksClassName?: string;
  mountOnEnter?: boolean;
  unmountOnExit?: boolean;
  toolbar?: Array<any>;
  scrollable?: boolean;
  divider?: boolean;
  description?: string;
  hideHeader?: boolean;
  style?: {
    [propName: string]: any;
  };
  value?: any;
  static?: boolean;
  className?: string;
}

@Renderer({
  type: 'my-portlet',
  name: 'my-portlet'
})
export class MyPortletRenderer extends React.PureComponent<PortletProps> {
  static defaultProps = {
    className: '',
    tabsMode: 'line',
    divider: true,
    scrollable: false,
    mountOnEnter: true,
    unmountOnExit: false,
    showHeader: false,
    showTabs: true,
    showContent: true
  };

  state = {
    activeKey: 0
  };

  constructor(props: PortletProps) {
    super(props);
  }

  handleSelect = (key: number) => {
    this.setState({
      activeKey: key
    });
  };

  renderToolbarItem(toolbar: Array<any>) {
    const {render} = this.props;
    const actions: any = [];

    if (Array.isArray(toolbar)) {
      toolbar.forEach((action, index) => {
        actions.push(
          render(
            `toolbar/${index}`,
            {
              type: 'button',
              level: 'link',
              size: 'sm',
              ...action
            },
            {
              key: index
            }
          )
        );
      });
    }

    return actions;
  }

  renderToolbar() {
    const {toolbar, tabs} = this.props;
    const {activeKey} = this.state;

    let tabToolbar = null;
    let tabToolbarTpl = null;

    // 全局工具栏
    const toolbarTpl = toolbar ? (
      <div className="toolbar">
        {this.renderToolbarItem(toolbar)}
      </div>
    ) : null;

    // 当前标签页的工具栏
    if (typeof activeKey !== 'undefined' && tabs && tabs[activeKey]) {
      tabToolbar = tabs[activeKey].toolbar;
      tabToolbarTpl = tabToolbar ? (
        <div className="tab-toolbar">
          {this.renderToolbarItem(tabToolbar)}
        </div>
      ) : null;
    }

    return toolbarTpl || tabToolbarTpl ? (
      <div className="Portlet-toolbar">
        {toolbarTpl}
        {tabToolbarTpl}
      </div>
    ) : null;
  }

  renderDesc() {
    const {description} = this.props;

    return description ? (
      <span className="Portlet-header-desc">{description}</span>
    ) : null;
  }

  renderHeader() {
    const {
      title,
      remark,
      icon,
      showMore,
      headerClassName,
      linkType,
      href,
      internalPage,
      internalName
    } = this.props;

    if (!title && !remark && !icon) {
      return null;
    }

    // 处理"更多"点击事件
    const handleMoreClick = () => {
      // 从URL中提取应用ID
      let applicationId = ''; // 默认值
      try {
        // 获取URL路径
        const pathname = window.location.hash;
        // 方法1：正则表达式匹配portletEditor后面的数字
        const matches = pathname.match(/\/portletEditor(\d+)/);
        if (matches && matches[1]) {
          applicationId = matches[1];
        }
      } catch (e) {
        console.error('提取应用ID时出错:', e);
      }

      if (linkType === 'internal' && internalPage) {
        // 内部页面跳转
        // window.location.href = `#/app${applicationId}/admin/page${internalPage}`;
        window.open(
          `#/app${applicationId}/admin/page${internalPage}`,
          '_blank'
        );
      } else if (href) {
        // 外部链接跳转
        if (href.startsWith('http://') || href.startsWith('https://')) {
          window.open(href, '_blank');
        } else {
          window.open(`http://${href}`, '_blank');
        }
      }
    };

    const headerClass = `Portlet-header ${headerClassName || ''}`;

    return (
      <div className={headerClass.trim()}>
        <div className="Portlet-header-title-area">
          {icon && (
            <span
              className="Portlet-header-icon"
              dangerouslySetInnerHTML={{
                __html: icon.replace(/^['"]|['"]$/g, '')
              }}
            ></span>
          )}
          <div className="Portlet-header-titles">
            {title && (
              <div className="Portlet-header-main-title">
                {title}
              </div>
            )}
            {remark && (
              <div
                className={`Portlet-header-hint-titles ${remark.className || ''}`}
              >
                <i className={remark.icon}></i>
                <TooltipWrapper
                  placement={remark.placement || 'right'}
                  tooltip={remark.content}
                  trigger={
                    Array.isArray(remark.trigger)
                      ? remark.trigger
                      : [remark.trigger || 'hover']
                  }
                  rootClose={remark.rootClose}
                >
                  <span className="Portlet-header-hint-title">
                    {' '}
                    {remark.title}
                  </span>
                </TooltipWrapper>
                <i className="fa fa-angle-right"></i>
              </div>
            )}
          </div>
        </div>

        {showMore && (
          <div
            className="Portlet-header-more"
            onClick={handleMoreClick}
            style={{cursor: 'pointer'}}
          >
            <span>更多</span>
            <i className="fa fa-angle-right"></i>
          </div>
        )}
      </div>
    );
  }

  render() {
    const {
      static: isStatic,
      name,
      label,
      header,
      showHeader,
      tabs,
      entries,
      tabsMode,
      showTabs,
      showContent,
      tabsClassName,
      contentClassName,
      linksClassName,
      mountOnEnter,
      unmountOnExit,
      scrollable,
      divider,
      hideHeader,
      style,
      className,
      render,
      value,
      data,
      tooltip,
      tooltipPlacement,
      translate: __
    } = this.props as any;

    // 处理静态展示模式
    if (isStatic) {
      if (render) {
        return render('static', {
          type: 'static',
          label,
          name,
          value: value || (data && data[name]) || '--'
        });
      }

      // 备用渲染方法：直接使用div
      return (
        <div className="static-value">
          <div className="static-label">{label || name}</div>
          <div className="static-content">
            {value || (data && data[name]) || '--'}
          </div>
        </div>
      );
    }

    // 确保tabs是数组
    const safeTabs = Array.isArray(tabs) ? tabs : [];

    // 渲染快捷入口项
    const renderEntryItems = (groupId?: string) => {
      if (!Array.isArray(entries) || entries.length === 0) {
        return null;
      }

      // 如果指定了分组ID，则只渲染该分组的入口项
      const filteredEntries = groupId
        ? entries.filter(entry => entry.group === groupId)
        : entries;
      console.log('groupId', groupId);
      console.log('entries', entries);
      console.log('filteredEntries', filteredEntries);

      if (filteredEntries.length === 0) {
        return null;
      }

      return (
        <div className="portal-entrance">
          {filteredEntries.map((entry, index) => (
            <div
              className="entrance-item"
              key={`entry-${index}`}
              onClick={() => handleEntryClick(entry)}
            >
              <div
                className="entrance-icon"
                style={{
                  background: entry.iconBg || '',
                  color: entry.iconColor || ''
                }}
              >
                {typeof entry.icon === 'string' &&
                entry.icon.includes('<svg') ? (
                  <span
                    dangerouslySetInnerHTML={{
                      __html: entry.icon || ''
                    }}
                  ></span>
                ) : (
                  <i className={entry.icon || ''}></i>
                )}
              </div>
              <div className="entrance-title">{entry.title}</div>
            </div>
          ))}
        </div>
      );
    };

    // 处理入口项点击事件
    const handleEntryClick = (entry: any) => {
      if (!entry) return;

      // 从URL中提取应用ID
      let applicationId = '';
      try {
        const pathname = window.location.hash;
        const matches = pathname.match(/\/portletEditor(\d+)/);
        if (matches && matches[1]) {
          applicationId = matches[1];
        }
      } catch (e) {
        console.error('提取应用ID时出错:', e);
      }

      if (entry.openType === 'internal' && entry.internalPage) {
        // 内部页面跳转
        window.open(
          `#/app${applicationId}/admin/page${entry.internalPage}`,
          '_blank'
        );
      } else if (entry.href) {
        // 外部链接跳转
        if (
          entry.href.startsWith('http://') ||
          entry.href.startsWith('https://')
        ) {
          window.open(entry.href, '_blank');
        } else {
          window.open(`http://${entry.href}`, '_blank');
        }
      }
    };

    // 如果没有标签页，显示默认内容
    if (safeTabs.length === 0) {
      const portletClassName = `Portlet ${className || ''} ${hideHeader ? 'no-header' : ''}`;
      
      return (
        <div
          className={portletClassName.trim()}
          data-tooltip={tooltip}
          data-position={tooltipPlacement || 'top'}
        >
          {showHeader && this.renderHeader()}
          <div className="portlet-content p-2">{renderEntryItems()}</div>
        </div>
      );
    }

    // 构建标签页组件
    const tabItems = safeTabs.map((tab, index) => {
      console.log('tab', tab);
      // 使用标签页的ID或索引作为分组ID
      const groupId = tab.id?.toString();

      // 如果是第一个标签且有entries数据，则渲染入口项
      const content =
        index === 0 && Array.isArray(entries) && entries.length > 0
          ? renderEntryItems(groupId) ||
            render(
              `tab/${index}`,
              tab.body || {
                type: 'tpl',
                tpl: '标签页内容'
              }
            )
          : render(
              `tab/${index}`,
              tab.body || {
                type: 'tpl',
                tpl: '标签页内容'
              }
            );

      const tabIcon =
        typeof tab.icon === 'string' && tab.icon.trim().startsWith('<svg')
          ? render(`tab-${index}-icon-svg`, {
              type: 'html',
              html: tab.icon.replace('<svg', '<svg width="16" height="16"')
            })
          : tab.icon;

      return (
        <Tab
          key={index}
          eventKey={index}
          title={tab.title}
          icon={tabIcon}
          iconPosition={tab.iconPosition || 'left'}
          disabled={tab.disabled}
          mountOnEnter={
            typeof tab.mountOnEnter !== 'undefined'
              ? tab.mountOnEnter
              : mountOnEnter
          }
          unmountOnExit={
            typeof tab.unmountOnExit !== 'undefined'
              ? tab.unmountOnExit
              : typeof tab.reload !== 'undefined'
              ? tab.reload
              : unmountOnExit
          }
        >
          {content}
        </Tab>
      );
    });

    // 构建Tabs组件的className
    let tabsClasses = ['Portlet-tab'];
    if (tabsClassName) tabsClasses.push(tabsClassName);
    if (safeTabs.length <= 1) tabsClasses.push('unactive-select');
    if (!divider) tabsClasses.push('no-divider');
    if (showTabs === false) tabsClasses.push('hide-tabs');
    
    const tabsClassname = tabsClasses.join(' ');

    // 构建Portlet容器的className
    let portletClasses = ['Portlet'];
    if (className) portletClasses.push(className);
    if (hideHeader) portletClasses.push('no-header');
    if (showHeader) portletClasses.push('with-custom-header');
    
    const portletClassname = portletClasses.join(' ');

    return (
      <div
        className={portletClassname}
        style={style}
        data-tooltip={tooltip}
        data-position={tooltipPlacement || 'top'}
      >
        {showHeader && this.renderHeader()}
        <Tabs
          addBtnText={__('add')}
          classPrefix=""
          mode={tabsMode || 'line'}
          className={tabsClassname}
          contentClassName={contentClassName}
          linksClassName={linksClassName}
          activeKey={this.state.activeKey}
          onSelect={this.handleSelect}
          toolbar={this.renderToolbar()}
          additionBtns={this.renderDesc()}
          scrollable={scrollable}
        >
          {showContent !== false
            ? tabItems
            : tabItems.map((tab, index) => (
                <Tab
                  key={index}
                  eventKey={index}
                  title={tab.props.title}
                  icon={tab.props.icon}
                  iconPosition={tab.props.iconPosition}
                  disabled={tab.props.disabled}
                  mountOnEnter={tab.props.mountOnEnter}
                  unmountOnExit={tab.props.unmountOnExit}
                >
                  <div className="text-center text-muted p-2">内容已隐藏</div>
                </Tab>
              ))}
        </Tabs>
      </div>
    );
  }
}
