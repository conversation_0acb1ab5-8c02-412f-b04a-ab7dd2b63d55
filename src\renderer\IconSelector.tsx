import {Renderer} from 'amis';
import React from 'react';
import {FormControlProps} from 'amis-core';
import debounce from 'lodash/debounce';

export interface IconSelectorProps extends FormControlProps {
  label?: string;
  name: string;
  clearable?: boolean;
  placeholder?: string;
  returnSvg?: boolean;
  value?: string;
  static?: boolean;
}

@Renderer({
  name: 'icon-selector',
  type: 'icon-selector'
})
export default class IconSelector extends React.PureComponent<IconSelectorProps> {
  constructor(props: IconSelectorProps) {
    super(props);

    this.handleSearchValueChange = debounce(
      this.handleSearchValueChange.bind(this),
      300
    );
  }
  handleSearchValueChange(e: string) {
    this.setState({
      searchValue: e
    });
  }

  render() {
    const {
      static: isStatic,
      name,
      label,
      placeholder,
      formMode,
      defaultStatic,
      render
    } = this.props as any;

    // 安全地获取值，不修改原始数据
    const {data = {}} = this.props;
    const realValue = data[name]; // 从表格行数据中取值

    // 处理静态展示
    if (isStatic) {
      // 如果有render函数，使用它渲染
      if (render) {
        return render('static', {
          type: 'static',
          label,
          name,
          tpl: realValue.replace(/'/g, '')
        });
      }
      // 备用渲染方法：直接使用div
      return (
        <div className="static-value">
          <div className="static-label">{label || name}</div>
          <div className="static-content">{realValue}</div>
        </div>
      );
    }
    // 判断是否处于查看模式
    const isDetailMode =
      isStatic ||
      formMode === 'static' ||
      formMode === 'view' ||
      defaultStatic === true;
    // console.log('defaultStatic', defaultStatic);
    // console.log('formMode', formMode);
    // console.log('isDetailMode', isDetailMode);
    // console.log('所有属性：', Object.keys(this.props));

    const treeProps = {
      ...this.props,
      type: isDetailMode ? 'icon-selector' : 'icon-select',
      label: label,
      name: name,
      value: realValue,
      placeholder: placeholder,
      returnSvg: this.props.returnSvg !== undefined ? this.props.returnSvg : true,
      static: isDetailMode
    };

    if (render) {
      return render('icon-selector', treeProps);
    }

    return <></>;
  }
}
