import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class MyInputExcelPlugin extends BasePlugin {
  static id = 'MyInputExcelPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'my-input-excel';
  $schema = '/schemas/MyInputExcelSchema.json';

  // 组件基本信息
  name = 'Excel解析';
  panelTitle = 'Excel 解析';
  icon = 'fa fa-file-excel-o';
  panelIcon = 'fa fa-file-excel-o';
  pluginIcon = 'fa fa-file-excel-o';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = false;

  // 组件描述信息
  description = 'Excel文件解析组件，支持解析Excel文件为数组或对象';
  docLink = '/amis/zh-CN/components/form/input-excel';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'my-input-excel',
    label: 'Excel解析',
    name: 'excel',
    placeholder: '请选择Excel文件',
    allSheets: false,
    parseMode: 'object',
    includeEmpty: false,
    plainText: false,
    parseImage: false,
    imageDataURI: false
  };

  // 预览界面
  previewSchema = {
    type: 'my-input-excel',
    label: 'Excel解析',
    name: 'excel',
    placeholder: '请选择Excel文件'
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                getSchemaTpl('formItemName', {
                  required: true
                }),
                getSchemaTpl('label'),
                {
                  type: 'input-text',
                  name: 'placeholder',
                  label: '占位提示',
                  description: '文件选择提示文字'
                }
              ]
            },
            {
              title: '解析配置',
              body: [
                {
                  type: 'switch',
                  name: 'allSheets',
                  label: '解析所有工作表',
                  value: false,
                  description: '是否解析所有sheet，默认只解析第一个'
                },
                {
                  type: 'select',
                  name: 'parseMode',
                  label: '解析模式',
                  value: 'object',
                  options: [
                    {label: '对象数组', value: 'object'},
                    {label: '二维数组', value: 'array'}
                  ],
                  description: 'array是解析成二维数组，object是将第一列作为字段名解析为对象数组'
                },
                {
                  type: 'switch',
                  name: 'includeEmpty',
                  label: '包含空内容',
                  value: false,
                  description: '是否包含空内容，主要用于二维数组模式'
                },
                {
                  type: 'switch',
                  name: 'plainText',
                  label: '纯文本模式',
                  value: false,
                  description: '是否启用纯文本模式'
                },
                {
                  type: 'switch',
                  name: 'parseImage',
                  label: '解析图片',
                  value: false,
                  description: '是否解析Excel中的图片'
                },
                {
                  type: 'switch',
                  name: 'imageDataURI',
                  label: '图片Data URI',
                  value: false,
                  visibleOn: 'this.parseImage',
                  description: '图片解析结果使用data URI格式'
                }
              ]
            },
            {
              title: '自动填充',
              body: [
                {
                  type: 'combo',
                  name: 'autoFill',
                  label: '自动填充配置',
                  description: '文件解析完成后将字段同步到表单内部',
                  multiple: true,
                  items: [
                    {
                      type: 'input-text',
                      name: 'key',
                      label: '字段名',
                      placeholder: '表单字段名'
                    },
                    {
                      type: 'input-text',
                      name: 'value',
                      label: '填充值',
                      placeholder: '支持表达式，如：${filename}'
                    }
                  ]
                }
              ]
            },
            getSchemaTpl('status', {
              isFormItem: true
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            getSchemaTpl('theme:formItem'),
            getSchemaTpl('theme:form-label'),
            getSchemaTpl('theme:form-description')
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(MyInputExcelPlugin);

