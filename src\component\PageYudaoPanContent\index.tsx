import React, {<PERSON>} from 'react';
import './index.scss';
import {<PERSON><PERSON>, <PERSON><PERSON>, Tab, toast, Modal} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj} from '@/utils/schemaPageTemplate/createPageObjs';
import fileAudio from '@/image/common_icons/file_audio.png';
import filePdf from '@/image/common_icons/file_pdf.png';
import fileWord from '@/image/common_icons/file_word.png';
import fileZip from '@/image/common_icons/file_zip.png';
import PagePanDataSettings from '@/component/PagePanDataSettings/index';

import {
  getFilePage, //获取表单数据
  getFormDataPage
} from '@/utils/api/api';
import PagePanSettings from '@/component/PagePanSettings';

// 声明全局方法
declare global {
  interface Window {
    openUploadDialog: () => void;
    setShowFolderModal: (show: boolean) => void;
    handleDeleteFile: (id: number) => void;
  }
}

const PageContent: FC<any> = (props: any) => {
  const [pageData, setPageData] = React.useState<any>({});
  
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');
  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }
    
    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  const [fileList, setFileList] = React.useState<any>([]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [showUploadModal, setShowUploadModal] = React.useState(false);
  const [showFolderModal, setShowFolderModal] = React.useState(false);
  const [newFolderName, setNewFolderName] = React.useState('');
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  //card ,list
  const [mode, setMode] = React.useState('card');

  const getToBlak = () => {
    let schema = JSON.parse(props.pageData.schema)
    let a = document.createElement('a');
    a.href = schema.body[0].src;
    window.open(a.href, '_blank');
  };

  // 获取表单数据 pageData
  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: props.pageData.id,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            // toast.success('获取表单数据成功');
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            res.data.list[0].pageType = 13;
            res.data.list[0].appId = props.computedMatch.params.appId;
            setPageData(res.data.list[0]);
          }else{
            toast.success('暂无表单数据');
          }
        }else{
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };
  React.useEffect(() => {
    if (props.pageData?.id) {
      handleGetFormDataPage();
    }
    // 只有在admin模式下才处理tab切换逻辑
    if (props.match.params.playType == 'admin') {
      const urlParams = new URLSearchParams(props.history.location.search);
      let tabKey = urlParams.get('activeTabKey') || 'preview';
      setActiveKey(tabKey);
    }
  }, [props.pageData?.id, props.history.location, props.match.params.playType]);

  return (
    <div className="pageBox">
     {props.match.params.playType == 'admin' &&  <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-6">
            {/* <Button size="lg" level="default" onClick={() => getToBlak()}>
              访问
            </Button> */}
          </div>
        </div>
      </div>}
      <div className="pageTabsLink">
        {props.match.params.playType == 'admin' ? (
          // admin模式：显示完整的tab标签栏
          <Tabs
            mode="line"
            theme={localStorage.getItem('amis-theme') || 'cxd'} // 显式传入主题
            onSelect={(key: any) => handleTabChange(key)}
            toolbar={TabsToolbar()}
            linksClassName="pageTabsLink-tabsTitle"
            activeKey={activeKey}
          >
            <Tab title="网盘预览" eventKey="preview">
            <div className="pageTabsLink-tabsContent">
              <div>全部文件</div>
              <div style={{display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>
                <div>
                  <Button level="default" onClick={() => setMode('card')}>
                    card
                  </Button>
                </div>
                <div>
                  <Button level="default" onClick={() => setMode('list')}>
                    list
                  </Button>
                </div>
              </div>
              {mode === 'card' && (
                <AMISRenderer
                  schema={{
                    type: 'page',
                    title: '',
                    data: {
                      applicationPageId: props.pageData.id
                    },
                    body: [
                      {
                        type: 'crud',
                        syncLocation: false,
                        id: 'crudId',
                        api: '/admin-api/infra/file/page?pageNo=${page}&pageSize=${perPage}&applicationPageId=${applicationPageId}',
                        mode: 'cards',
                        columnsCount: 5,
                        card: {
                          className: "border-none",
                          itemAction: {
                            type: "button",
                            actionType: "dialog",
                            dialog: {
                              title: '文件预览',
                              size: 'lg',
                              body: [{
                                type: 'image',
                                src: '${url}',
                                // imageMode: "original",
                                showToolbar: true,
                                enlargeAble: true,
                                visibleOn: '${type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp"}'
                              },{
                                type: 'audio',
                                src: '${url}',
                                visibleOn: '${type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav"}'
                              },{
                                type: 'pdf-viewer',
                                src: '${url}',
                                visibleOn: '${type === "application/pdf"}'
                              },{
                                type: 'office-viewer',
                                src: '${url}',
                                visibleOn: '${type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}'
                              },{
                                type: 'video',
                                src: '${url}',
                                visibleOn: '${type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska"}'
                              },
                              {
                                type: 'markdown',
                                src: '${url}',
                                visibleOn: '${type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                              },
                              {
                                type: 'iframe',
                                src: '${url}',
                                visibleOn: '${type === "text/plain"}',
                              }
                            ],
                            }
                          },
                          body: [
                            {
                              type: 'flex',
                              alignItems: 'center',
                              direction: 'column',
                              items: [
                                {
                                  type: 'image',
                                  src: '${url}',
                                  width: '40px',
                                  height: '40px',
                                  visibleOn: '${type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp"}'
                                },
                                {
                                  type: 'icon',
                                  icon: 'fas fa-file-audio fa-2x',
                                  className: "m-b-sm",
                                  visibleOn: '${type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav"}'
                                },
                                {
                                  type: 'icon',
                                  icon: 'fas fa-file-pdf fa-2x',
                                  className: "m-b-sm",
                                  visibleOn: '${type === "application/pdf"}'
                                },
                                {
                                  type: 'icon',
                                  icon: 'fas fa-file-word fa-2x',
                                  className: "m-b-sm",
                                  visibleOn: '${type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}'
                                },
                                {
                                  type: 'icon',
                                  icon: 'fas fa-file-video fa-2x',
                                  className: "m-b-sm",
                                  visibleOn: '${type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska"}'
                                },
                                {
                                  type: 'icon',
                                  icon: 'fa fa-file-zip-o fa-2x',
                                  className: "m-b-sm",
                                  visibleOn: '${type === "application/zip"}'
                                },
                                {
                                  type: 'icon',
                                  icon: 'fas fa-file-text fa-2x',
                                  className: "m-b-sm",
                                  visibleOn: '${type === "text/plain" || type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                                },
                                {
                                  type: 'icon',
                                  icon: 'fas fa-question fa-2x',
                                  className: "m-b-sm",
                                  hiddenOn: '${type === "application/zip" || type === "application/zip" ||type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp" || type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav" || type === "application/pdf" || type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska" || type === "text/plain" || type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                                },
                            {
                              type: 'tpl',
                              tpl: '${name}',
                              // width: '100%'
                            },
                            {
                              type: 'tpl',
                              tpl: "${createTime / 1000 | date:MM-DD HH\\:mm}"  
                            }
                          ]
                          }
                            
                          ]
                        },
                        headerToolbar: [
                          {
                            type: 'button',
                            label: '上传文件',
                            level: 'primary',
                            icon: 'fa fa-upload',
                            className: 'mr-2',
                            actionType: 'dialog',
                            feat: 'Insert',
                            reload: true,
                            dialog: {
                              title: '上传文件',
                              body: [
                                {
                                  type: 'input-file',
                                  receiver: {
                                    method: 'post',
                                    url: '/admin-api/infra/file/uploadByApplication',
                                    data: {
                                      applicationPageId: props.pageData.id
                                    } 
                                  },
                                  name: 'file',
                                  drag: true,
                                  feat: 'Insert',

                                }
                              ],
                                onEvent:{
                                  confirm: {
                                    actions: [
                                      {
                                        actionType: 'reload',
                                        componentId: 'crudId',
                                      }
                                    ]
                                  }
                                }
                            }
                          },
                          {
                            type: 'reload',
                          }
                        ],
                        footerToolbar: [
                          'statistics',
                          'switch-per-page',
                          'pagination'
                        ],
                        columns: [
                          {
                            name: 'name',
                            label: '文件名',
                            type: 'tpl',
                            tpl: '<div class="flex items-center"><i class="${type === \'video/mp4\' ? \'fa fa-file-video\' : (type.includes(\'image\') ? \'fa fa-file-image\' : \'fa fa-file\')}" style="margin-right: 8px;"></i>${name}</div>'
                          },
                          {
                            name: 'size',
                            label: '大小',
                            type: 'tpl',
                            tpl: '${(size / 1024).toFixed(2)} KB'
                          },
                          {
                            name: 'type',
                            label: '类型',
                            type: 'mapping',
                            map: {
                              // 视频文件
                              'video/mp4': '视频',
                              'video/ogg': '视频',
                              'video/x-matroska': '视频',
                              
                              // 图片文件
                              'image/jpeg': '图片',
                              'image/png': '图片',
                              'image/gif': '图片',
                              'image/svg+xml': 'SVG图片',
                              'image/webp': '图片',
                              
                              // 音频文件
                              'audio/mp4': '音频',
                              'audio/mpeg': '音频',
                              'audio/aac': '音频',
                              'audio/x-caf': '音频',
                              'audio/flac': '音频',
                              'audio/ogg': '音频',
                              'audio/wav': '音频',

                              'text/plain': '文本',
                              'text/x-web-markdown': 'Markdown',
                              'text/x-markdown': 'Markdown',
                              'text/markdown': 'Markdown',

                              
                              // 文档文件
                              'application/pdf': 'PDF文档',
                              'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
                              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel文档',
                              'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PPT文档',
                              
                              // 压缩文件
                              'application/zip': '压缩文件',
                              'application/x-rar-compressed': '压缩文件',
                              'application/x-7z-compressed': '压缩文件',
                              
                              '*': '未知'
                            }
                          },
                          {
                            name: 'createTime',
                            label: '上传时间',
                            type: 'tpl',
                            tpl: "${createTime / 1000 | date}"
                          },
                          {
                            type: 'operation',
                            label: '操作',
                            buttons: [
                              {
                                type: 'button',
                                label: '预览',
                                icon: 'fa fa-eye',
                                actionType: 'dialog',
                                dialog: {
                                  title: '文件预览',
                                  size: 'lg',
                                  body: [{
                                    type: 'image',
                                    src: '${url}',
                                    // imageMode: "original",
                                    showToolbar: true,
                                    enlargeAble: true,
                                    visibleOn: '${type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp"}'
                                  },{
                                    type: 'audio',
                                    src: '${url}',
                                    visibleOn: '${type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav"}'
                                  },{
                                    type: 'pdf-viewer',
                                    src: '${url}',
                                    visibleOn: '${type === "application/pdf"}'
                                  },{
                                    type: 'office-viewer',
                                    src: '${url}',
                                    visibleOn: '${type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}'
                                  },{
                                    type: 'video',
                                    src: '${url}',
                                    visibleOn: '${type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska"}'
                                  },
                                  {
                                    type: 'markdown',
                                    src: '${url}',
                                    visibleOn: '${type === "text/plain" || type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                                  }
                                ],
                                }
                              },
                              {
                                type: 'button',
                                label: '下载',
                                icon: 'fa fa-download',
                                actionType: 'url',
                                url: '${url}',
                                blank: true
                              },
                              {
                                type: 'button',
                                label: '删除',
                                icon: 'fa fa-trash',
                                className: 'text-danger',
                                actionType: 'dialog',
                                dialog: {
                                  type: 'dialog',
                                  title: '',
                                  className: 'py-2',
                                  actions: [
                                    {
                                      type: 'action',
                                      actionType: 'cancel',
                                      label: '取消',
                                      id: 'u:bf2ded085c52'
                                    },
                                    {
                                      type: 'action',
                                      actionType: 'submit',
                                      label: '删除',
                                      level: 'danger',
                                      id: 'u:6f5c3305ac5b'
                                    }
                                  ],
                                  body: [
                                    {
                                      type: 'form',
                                      wrapWithPanel: false,
                                      api: 'delete:/admin-api/infra/file/delete?id=${id}',
                                      body: [
                                        {
                                          type: 'tpl',
                                          className: 'py-2',
                                          tpl: '确认删除选中项？',
                                          id: 'u:b5980d190942'
                                        }
                                      ],
                                      id: 'u:130a702e34da',
                                      feat: 'Insert',
                                      dsType: 'api',
                                      labelAlign: 'left'
                                    }
                                  ],
                                  actionType: 'dialog',
                                  showCloseButton: true,
                                  showLoading: true,
                                  draggable: false,
                                  editorSetting: {
                                    displayName: '删除文件'
                                  }
                                }
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }}
                  embedMode={true}
                />
              )}
              {mode === 'list' && (
               <AMISRenderer
               schema={pageData.data}
               embedMode={true}
             />
              )}
                    
            </div>
            </Tab>
            <Tab title="数据页设置" eventKey="dataset">
            <PagePanDataSettings
                pageData={pageData}
                history={props.history}
                store={props.store}
                update={() => {
                  props.updatePage();
                  handleGetFormDataPage();
                }}
              />
            </Tab>
            <Tab title="页面设置" eventKey="setting">
              <PagePanSettings
                pageData={props.pageData}
                history={props.history}
                store={props.store}
                update={() => {
                  props.updatePage();
                }}
              />
            </Tab>
            <Tab title="页面发布" eventKey="publish"></Tab>
          </Tabs>
        ) : (
          // 非admin模式：只显示网盘预览内容，不显示tab标签栏
          <div className="pageTabsLink-tabsContent">
            <div>全部文件</div>
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>
              <div>
                <Button level="default" onClick={() => setMode('card')}>
                  card
                </Button>
              </div>
              <div>
                <Button level="default" onClick={() => setMode('list')}>
                  list
                </Button>
              </div>
            </div>
            {mode === 'card' && (
              <AMISRenderer
                schema={{
                  type: 'page',
                  title: '',
                  data: {
                    applicationPageId: props.pageData.id
                  },
                  body: [
                    {
                      type: 'crud',
                      syncLocation: false,
                      id: 'crudId',
                      api: '/admin-api/infra/file/page?pageNo=${page}&pageSize=${perPage}&applicationPageId=${applicationPageId}',
                      mode: 'cards',
                      columnsCount: 5,
                      card: {
                        className: "border-none",
                        itemAction: {
                          type: "button",
                          actionType: "dialog",
                          dialog: {
                            title: '文件预览',
                            size: 'lg',
                            body: [{
                              type: 'image',
                              src: '${url}',
                              // imageMode: "original",
                              showToolbar: true,
                              enlargeAble: true,
                              visibleOn: '${type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp"}'
                            },{
                              type: 'audio',
                              src: '${url}',
                              visibleOn: '${type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav"}'
                            },{
                              type: 'pdf-viewer',
                              src: '${url}',
                              visibleOn: '${type === "application/pdf"}'
                            },{
                              type: 'office-viewer',
                              src: '${url}',
                              visibleOn: '${type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}'
                            },{
                              type: 'video',
                              src: '${url}',
                              visibleOn: '${type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska"}'
                            },
                            {
                              type: 'markdown',
                              src: '${url}',
                              visibleOn: '${type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                            },
                            {
                              type: 'iframe',
                              src: '${url}',
                              visibleOn: '${type === "text/plain"}',
                            }
                          ],
                          }
                        },
                        body: [
                          {
                            type: 'flex',
                            alignItems: 'center',
                            direction: 'column',
                            items: [
                              {
                                type: 'image',
                                src: '${url}',
                                width: '40px',
                                height: '40px',
                                visibleOn: '${type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp"}'
                              },
                              {
                                type: 'icon',
                                icon: 'fas fa-file-audio fa-2x',
                                className: "m-b-sm",
                                visibleOn: '${type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav"}'
                              },
                              {
                                type: 'icon',
                                icon: 'fas fa-file-pdf fa-2x',
                                className: "m-b-sm",
                                visibleOn: '${type === "application/pdf"}'
                              },
                              {
                                type: 'icon',
                                icon: 'fas fa-file-word fa-2x',
                                className: "m-b-sm",
                                visibleOn: '${type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}'
                              },
                              {
                                type: 'icon',
                                icon: 'fas fa-file-video fa-2x',
                                className: "m-b-sm",
                                visibleOn: '${type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska"}'
                              },
                              {
                                type: 'icon',
                                icon: 'fa fa-file-zip-o fa-2x',
                                className: "m-b-sm",
                                visibleOn: '${type === "application/zip"}'
                              },
                              {
                                type: 'icon',
                                icon: 'fas fa-file-text fa-2x',
                                className: "m-b-sm",
                                visibleOn: '${type === "text/plain" || type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                              },
                              {
                                type: 'icon',
                                icon: 'fas fa-question fa-2x',
                                className: "m-b-sm",
                                hiddenOn: '${type === "application/zip" || type === "application/zip" ||type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp" || type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav" || type === "application/pdf" || type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska" || type === "text/plain" || type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                              },
                          {
                            type: 'tpl',
                            tpl: '${name}',
                            // width: '100%'
                          },
                          {
                            type: 'tpl',
                            tpl: "${createTime / 1000 | date:MM-DD HH\\:mm}"
                          }
                        ]
                        }

                        ]
                      },
                      headerToolbar: [
                        {
                          type: 'button',
                          label: '上传文件',
                          level: 'primary',
                          icon: 'fa fa-upload',
                          className: 'mr-2',
                          actionType: 'dialog',
                          feat: 'Insert',
                          reload: true,
                          dialog: {
                            title: '上传文件',
                            body: [
                              {
                                type: 'input-file',
                                receiver: {
                                  method: 'post',
                                  url: '/admin-api/infra/file/uploadByApplication',
                                  data: {
                                    applicationPageId: props.pageData.id
                                  }
                                },
                                name: 'file',
                                drag: true,
                                feat: 'Insert',

                              }
                            ],
                              onEvent:{
                                confirm: {
                                  actions: [
                                    {
                                      actionType: 'reload',
                                      componentId: 'crudId',
                                    }
                                  ]
                                }
                              }
                          }
                        },
                        {
                          type: 'reload',
                        }
                      ],
                      footerToolbar: [
                        'statistics',
                        'switch-per-page',
                        'pagination'
                      ],
                      columns: [
                        {
                          name: 'name',
                          label: '文件名',
                          type: 'tpl',
                          tpl: '<div class="flex items-center"><i class="${type === \'video/mp4\' ? \'fa fa-file-video\' : (type.includes(\'image\') ? \'fa fa-file-image\' : \'fa fa-file\')}" style="margin-right: 8px;"></i>${name}</div>'
                        },
                        {
                          name: 'size',
                          label: '大小',
                          type: 'tpl',
                          tpl: '${(size / 1024).toFixed(2)} KB'
                        },
                        {
                          name: 'type',
                          label: '类型',
                          type: 'mapping',
                          map: {
                            // 视频文件
                            'video/mp4': '视频',
                            'video/ogg': '视频',
                            'video/x-matroska': '视频',

                            // 图片文件
                            'image/jpeg': '图片',
                            'image/png': '图片',
                            'image/gif': '图片',
                            'image/svg+xml': 'SVG图片',
                            'image/webp': '图片',

                            // 音频文件
                            'audio/mp4': '音频',
                            'audio/mpeg': '音频',
                            'audio/aac': '音频',
                            'audio/x-caf': '音频',
                            'audio/flac': '音频',
                            'audio/ogg': '音频',
                            'audio/wav': '音频',

                            'text/plain': '文本',
                            'text/x-web-markdown': 'Markdown',
                            'text/x-markdown': 'Markdown',
                            'text/markdown': 'Markdown',


                            // 文档文件
                            'application/pdf': 'PDF文档',
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel文档',
                            'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PPT文档',

                            // 压缩文件
                            'application/zip': '压缩文件',
                            'application/x-rar-compressed': '压缩文件',
                            'application/x-7z-compressed': '压缩文件',

                            '*': '未知'
                          }
                        },
                        {
                          name: 'createTime',
                          label: '上传时间',
                          type: 'tpl',
                          tpl: "${createTime / 1000 | date}"
                        },
                        {
                          type: 'operation',
                          label: '操作',
                          buttons: [
                            {
                              type: 'button',
                              label: '预览',
                              icon: 'fa fa-eye',
                              actionType: 'dialog',
                              dialog: {
                                title: '文件预览',
                                size: 'lg',
                                body: [{
                                  type: 'image',
                                  src: '${url}',
                                  // imageMode: "original",
                                  showToolbar: true,
                                  enlargeAble: true,
                                  visibleOn: '${type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp"}'
                                },{
                                  type: 'audio',
                                  src: '${url}',
                                  visibleOn: '${type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav"}'
                                },{
                                  type: 'pdf-viewer',
                                  src: '${url}',
                                  visibleOn: '${type === "application/pdf"}'
                                },{
                                  type: 'office-viewer',
                                  src: '${url}',
                                  visibleOn: '${type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}'
                                },{
                                  type: 'video',
                                  src: '${url}',
                                  visibleOn: '${type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska"}'
                                },
                                {
                                  type: 'markdown',
                                  src: '${url}',
                                  visibleOn: '${type === "text/plain" || type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                                }
                              ],
                              }
                            },
                            {
                              type: 'button',
                              label: '下载',
                              icon: 'fa fa-download',
                              actionType: 'url',
                              url: '${url}',
                              blank: true
                            },
                            {
                              type: 'button',
                              label: '删除',
                              icon: 'fa fa-trash',
                              className: 'text-danger',
                              actionType: 'dialog',
                              dialog: {
                                type: 'dialog',
                                title: '',
                                className: 'py-2',
                                actions: [
                                  {
                                    type: 'action',
                                    actionType: 'cancel',
                                    label: '取消',
                                    id: 'u:bf2ded085c52'
                                  },
                                  {
                                    type: 'action',
                                    actionType: 'submit',
                                    label: '删除',
                                    level: 'danger',
                                    id: 'u:6f5c3305ac5b'
                                  }
                                ],
                                body: [
                                  {
                                    type: 'form',
                                    wrapWithPanel: false,
                                    api: 'delete:/admin-api/infra/file/delete?id=${id}',
                                    body: [
                                      {
                                        type: 'tpl',
                                        className: 'py-2',
                                        tpl: '确认删除选中项？',
                                        id: 'u:b5980d190942'
                                      }
                                    ],
                                    id: 'u:130a702e34da',
                                    feat: 'Insert',
                                    dsType: 'api',
                                    labelAlign: 'left'
                                  }
                                ],
                                actionType: 'dialog',
                                showCloseButton: true,
                                showLoading: true,
                                draggable: false,
                                editorSetting: {
                                  displayName: '删除文件'
                                }
                              }
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }}
                embedMode={true}
              />
            )}
            {mode === 'list' && (
             <AMISRenderer
             schema={pageData.data}
             embedMode={true}
           />
            )}
          </div>
        )}
      </div>


      {/* 创建文件夹模态框 */}
      <Modal
        show={showFolderModal}
        onHide={() => setShowFolderModal(false)}
        title="新建文件夹"
      >
        <div className="p-4">
          <div className="form-group mb-4">
            <label className="mb-2">文件夹名称</label>
            <input 
              type="text" 
              className="form-control"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="请输入文件夹名称"
            />
          </div>
          <div className="d-flex justify-content-end">
            <Button level="default" className="mr-2" onClick={() => setShowFolderModal(false)}>
              取消
            </Button>
            <Button level="primary" onClick={() => {}}>
              确定
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default PageContent;
