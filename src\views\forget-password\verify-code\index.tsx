import React, {useState, useEffect} from 'react';
import {RouteComponentProps} from 'react-router-dom';
import {Button, VerificationCode, toast} from 'amis-ui';
import {sendSms} from '@/utils/api/api';

import './index.scss';

export default function VerifyCode({
  history,
  location
}: RouteComponentProps) {
  const [code, setCode] = useState('');
  const [countdown, setCountdown] = useState(60);
  const urlParams = new URLSearchParams(location.search);
  const username = urlParams.get('username');

  useEffect(() => {
    if (!username) {
      history.replace('/forget-password');
      return;
    }


    sendSms({
      mobile: username,
      scene: '23'
    })
      .then(res => {
        if (res.code === 0) {
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown(time => {
              if (time <= 1) {
                clearInterval(timer);
                return 0;
              }
              return time - 1;
            });
          }, 1000);
        } else {
          toast.error(res.msg || '获取验证码失败');
        }
      })
      .catch(err => toast.error(err.msg || '获取验证码失败'));

    // const timer = setInterval(() => {
    //   setCountdown(time => {
    //     if (time <= 1) {
    //       clearInterval(timer);
    //       return 0;
    //     }
    //     return time - 1;
    //   });
    // }, 1000);

    // return () => clearInterval(timer);
  }, []);

  // 监听 code 的变化
  useEffect(() => {
    if (code.length === 6) {
      console.log('验证码输入完成，username:', username);
      // 当验证码输入完成时
      // TODO: 这里应该调用验证码验证接口
      // 模拟验证成功
      history.push(`/forget-password/reset-password?username=${username}`
      );
    }
  }, [code]);

  return (
    <div className="verify-code-page">
      <div className="form-content">
        <div className="page-title">输入验证码</div>
        <div className="verify-tip">验证码已发送至 {username}</div>
        <div className="code-input">
          <VerificationCode
            value={code}
            onChange={(value: string) => setCode(value)}
            length={6}
            placeholder=""
            className="verification-code"
            inputClassName="cxd-VerificationCode-input"
            inputType="tel"
            autoFocus
            separate
            inputMode="numeric"  // 添加这个属性以显示数字键盘
          />
        </div>
        <div className="resend-code">
          {countdown > 0 ? (
            <span>
              <span className="countdown">{countdown}</span>秒后重新发送验证码
            </span>
          ) : (
            <Button
              level="link"
              onClick={() => {
                // TODO: 重新发送验证码
                setCountdown(60);
              }}
            >
              重新发送验证码
            </Button>
          )}
        </div>
      </div>
    </div>
  );
} 