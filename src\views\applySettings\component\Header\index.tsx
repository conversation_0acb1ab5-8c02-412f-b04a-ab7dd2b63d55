import React, {<PERSON>} from 'react';
import './index.scss';
import {toast} from 'amis';

// 图片
import app_default_icon from '@/image/common_icons//app_default_icon.png';

const renderHeader: FC<any> = (props: any) => {
  /* data 数据 */
  
  /* data 数据 end */

  /* methods 方法 */

  /* methods 方法 end */

  /* created 初始化 */

  /* created 初始化 end */
  return (
    <div className="applySettingsHeader">
      <div className="applySettingsHeader-left">
        {/* 工作台 */}
        <div
          className="applySettingsHeader-left-gzt"
          onClick={() => {
            props.history.push('/');
          }}
        >
          <i className="fa fa-home applySettingsHeader-left-gzt-icon"></i>
        </div>
        {/* 应用图标、名称 */}
        <div className="applySettingsHeader-left-appInfo">
          <div className="applySettingsHeader-left-appInfo-icon">
            {props.applyData.icon ? <img
              src={props.applyData.icon}
              alt={props.applyData?.name}
              className="applySettingsHeader-left-appInfo-icon-img"
            /> : <img
              src={app_default_icon}
              alt={props.applyData?.name}
              className="applySettingsHeader-left-appInfo-icon-img"
            />}
          </div>
          <div className="applySettingsHeader-left-appInfo-name" onClick={()=>{props.history.push(`/app${props.applyData.id}/admin`)}}>
            {props.applyData?.name}
          </div>
          <span className='applySettingsHeader-left-appInfo-gan'>/</span>
        </div>
        {/* 应用设置 */}
        <div className="applySettingsHeader-left-appSet">
          <span>应用设置</span>
        </div>
      </div>
    </div>
  );
};

export default renderHeader;
