export const EditTeamDescribeDialog = (show: boolean, currentTeamName?: string) => {
    return {
      type: 'dialog',
      title: {
        type: 'html',
        html: '<b>编辑团队描述</b>'
      },
      showCloseButton: false,
      data: {
        description: currentTeamName
      },
      body: {
        type: 'form',
        mode: 'normal',
        wrapWithPanel: false,
        messages: {
          validateFailed: '请输入团队描述'
        },
        body: [
          {
            type: 'input-text',
            name: 'description',
            placeholder: '团队描述',
            required: true,
            maxLength: 50,
            validations: {
              maxLength: 50
            },
            inputClassName: 'description-input',
            labelClassName: 'description-label'
          }
        ]
      },
      actions: [
        {
          type: 'button',
          label: '取消',
          actionType: 'cancel',
          className: 'cancel-btn'
        },
        {
          type: 'button',
          label: '确认',
          level: 'primary',
          actionType: 'confirm',
          className: 'confirm-btn'
        }
      ],
      show: show
    };
  };
  