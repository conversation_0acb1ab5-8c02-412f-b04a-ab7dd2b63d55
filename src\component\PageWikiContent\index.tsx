import React, {FC} from 'react';
import './index.scss';
import {Button, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {
  createFromObj,
  createCustompageObj,
  createIframeObj,
  createReportObj,
  
} from '@/utils/schemaPageTemplate/createPageObjs';

const PageWikiContent: FC<any> = (props: any) => {
  React.useEffect(() => {
    console.log('props PagelinkContent',props)
  }, [props]);
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('manage');

  const getToBlak = () => {
    let a = document.createElement('a');
    a.href = props.pageData.url;
    window.open(a.href, '_blank');
  };


  return (
    <div className="pageBox">
      <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-6">
            <Button size="lg" level="default" onClick={() => getToBlak()}>
              访问
            </Button>
          </div>
        </div>
      </div>
      <div className="pageTabsLink">
        {/* activeKey */}
        <Tabs
          mode="line"
          onSelect={(key: any) => setActiveKey(key)}
          toolbar={TabsToolbar()}
          linksClassName="pageTabsLink-tabsTitle"
          setActiveKey="manage"
        >
          <Tab title="链接预览" eventKey="preview">
            <div className="pageTabsLink-tabsContent">
              <AMISRenderer
                schema={createIframeObj(props.pageData.url)}
                embedMode={true}
              />
            </div>
          </Tab>
          <Tab title="页面设置" eventKey="3"></Tab>
          <Tab title="页面发布" eventKey="4"></Tab>
        </Tabs>
      </div>
    </div>
  );
};

export default PageWikiContent;
