.submission-layout {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: auto;

  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  padding-bottom: 60px;
}

.submission-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  height: 50px;
  color: var(--text-color);
  background-color: var(--body-bg);
  border-bottom: 1px solid var(--borderColor);

  &-left {
    display: flex;
    align-items: center;

    &-appname {
      display: flex;
      align-items: center;

      &-icon {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }

      &-name {
        font-size: 14px;
        margin-right: 6px;
      }
    }
  }

  &-right {
    display: flex;
    align-items: center;

    &-user {
      display: flex;
      align-items: center;
      cursor: pointer;

      &-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        overflow: hidden;
      }
    }
  }
}

.submission-page-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  margin: 0 auto;
  width: 100%;

  .submission-form-title {
    padding: 20px 10px;
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
  }
}
