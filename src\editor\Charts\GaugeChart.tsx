import {Button} from 'amis';
import React from 'react';
import {
  registerEditorPlugin,
  BaseEventContext,
  BasePlugin,
  RendererPluginAction,
  diff,
  defaultValue,
  getSchemaTpl,
  CodeEditor as AmisCodeEditor,
  RendererPluginEvent,
  tipedLabel
} from 'amis-editor-core';
// import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import ChartDataInteraction from '@/editor/component/ChartDataInteraction/GaugeChartDataInteraction';

const ChartConfigEditor = ({value, onChange}: any) => {
  return (
    <div className="ae-JsonEditor">
      <AmisCodeEditor value={value} onChange={onChange} />
    </div>
  );
};

const DEFAULT_EVENT_PARAMS = [
  {
    type: 'object',
    properties: {
      data: {
        type: 'object',
        title: '数据',
        properties: {
          componentType: {
            type: 'string',
            title: 'componentType'
          },
          seriesType: {
            type: 'string',
            title: 'seriesType'
          },
          seriesIndex: {
            type: 'number',
            title: 'seriesIndex'
          },
          seriesName: {
            type: 'string',
            title: 'seriesName'
          },
          name: {
            type: 'string',
            title: 'name'
          },
          dataIndex: {
            type: 'number',
            title: 'dataIndex'
          },
          data: {
            type: 'object',
            title: 'data'
          },
          dataType: {
            type: 'string',
            title: 'dataType'
          },
          value: {
            type: 'number',
            title: 'value'
          },
          color: {
            type: 'string',
            title: 'color'
          }
        }
      }
    }
  }
];

const chartDefaultConfig = {
  series: [
    {
      name: 'Pressure',
      type: 'gauge',
      detail: {
        formatter: '{value}'
      },
      data: [
        {
          value: 50,
          name: 'SCORE'
        }
      ],
      radius: "75%",
      showScale: true,
      roundCap: true,
      min: 0,
      max: 100,
      splitNumber: 5,
      lineWidth: 10,
      progress: {
        show: true,
        roundCap: true,
        width: 10
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        show: true
      },
      pointer: {
        show: true
      },
      axisLine: {
        show: true,
        roundCap: true,
        lineStyle: {
          width: 10
        }
      }
    }
  ]
};


const otherConfig = {
  title: '其他',
  className: 'p-none',
  data: {
    config: chartDefaultConfig
  },
  body: getSchemaTpl('collapseGroup', [
    {
      title: '标题配置',
      body: [
        {
          type: 'switch',
          label: '标题显示',
          name: 'config.title.show',
          value: "${config.title.show}",
        },
        getSchemaTpl('label', {
          label: '标题文本',
          hiddenOn: '!config.title.show',
          name: 'config.title.text',
          value: "${config.title.text}",
        }),
        {
          type: 'switch',
          label: '标题提示',
          hiddenOn: '!config.title.show',
          name: 'config.title.showSubtext',
          value: "${config.title.showSubtext}",
        },
        {
          type: "formula",
          name: "config.title.subtextStyle.fontSize",
          formula: "${config.title.showSubtext ? 14 : 0}"
        },
        getSchemaTpl('label', {
          label: '提示内容',
          hiddenOn: '!config.title.show || !config.title.showSubtext',
          name: 'config.title.subtext',
          value: "${config.title.subtext}",
        }),
      ]
    },
    {
      title: '工具栏配置',
      body: [
        {
          type: 'switch',
          label: '复制为图片',
          hiddenOn: '!config.toolbox.show',
          name: 'config.toolbox.feature.saveAsImage.show',
          value: "${config.toolbox.feature.saveAsImage.show}",
        }
      ]
    }
  ])
};

const singleColorConfig = {
  title: '单值颜色',
  body: [
    {
      label: '主题',
      type: 'select',
      name: 'chartThemeName',
      value: "${chartThemeName}",
      options: [
        {label: '默认', value: ''},
        {label: 'vintage', value: 'vintage'},
        {label: 'westeros', value: 'westeros'},
        {label: 'wonderland', value: 'wonderland'},
        {label: 'chalk', value: 'chalk'},
        {label: 'macarons', value: 'macarons'},
        {label: 'shine', value: 'shine'},
        {label: 'dark', value: 'dark'},
        {label: 'essos', value: 'essos'},
        {label: 'walden', value: 'walden'},
        {label: 'infographic', value: 'infographic'},
        {label: 'roma', value: 'roma'},
        {label: 'purple-passion', value: 'purple-passion'},        
      ]
    },
    {
      type: 'switch',
      label: '单值颜色',
      name: 'config.isSingleColor',
      value: "${config.isSingleColor}",
    },
    {
      type: 'input-color',
      label: '单值颜色',
      name: 'config.singleColor',
      value: "${config.singleColor}",
      hiddenOn: '!config.isSingleColor',
    },
  ]
};




const scaleConfig = {
  title: '刻度',
  body: [
    {
      type: 'switch',
      label: '显示刻度',
      name: 'config.series[0].showScale',
      value: "${config.series[0].showScale}",
    },
    {
      type: "formula",
      name: "config.series[0].axisLabel.show",
      formula: "${config.series[0].showScale}"
    },
    {
      type: "formula",
      name: "config.series[0].splitLine.show",
      formula: "${config.series[0].showScale}"
    },
    {
      label: '最小值',
      type: "input-number",
      name: "config.series[0].min",
      value: "${config.series[0].min}",
      hiddenOn: '!config.series[0].showScale',
    },
    {
      label: '最大值',
      type: "input-number",
      name: "config.series[0].max",
      value: "${config.series[0].max}",
      hiddenOn: '!config.series[0].showScale',
    },
    {
      label: '分割数',
      type: "input-number",
      name: "config.series[0].splitNumber",
      value: "${config.series[0].splitNumber}",
      hiddenOn: '!config.series[0].showScale',
    },
  ]
}

const pointerConfig = {
  title: '样式配置',
  body: [
    {
      type: 'switch',
      label: '圆角形态',
      name: 'config.series[0].roundCap',
      value: "${config.series[0].roundCap}",
    },
    {
      type: "formula",
      name: "config.series[0].progress.roundCap",
      formula: "${config.series[0].roundCap}"
    },
    {
      type: "formula",
      name: "config.series[0].axisLine.roundCap",
      formula: "${config.series[0].roundCap}"
    },
    {
      type: 'switch',
      label: '显示指针',
      name: 'config.series[0].pointer.show',
      value: "${config.series[0].pointer.show}",
    },
    {
      label: '色条粗细',
      type: "input-number",
      name: "config.series[0].lineWidth",
      value: "${config.series[0].lineWidth}",
    },
    {
      type: "formula",
      name: "config.series[0].progress.width",
      formula: "${config.series[0].lineWidth}"
    },
    {
      type: "formula",
      name: "config.series[0].axisLine.lineStyle.width",
      formula: "${config.series[0].lineWidth}"
    },
    {
      label: '相对半径',
      type: "input-number",
      unitOptions: [
        "%",
      ],
      name: "config.series[0].radius",
      value: "${config.series[0].radius}",
    },
    // {
    //   label: '相对内半径',
    //   type: "input-number",
    //   name: "config.series[0].innerRadius",
    //   value: "${config.series[0].innerRadius}",
    // },

  ]
}


export class GaugeChartPlugin extends BasePlugin {
  static id = 'GaugeChartPlugin';
  // 关联渲染器名字
  rendererName = 'gauge-chart';
  $schema = '/schemas/ChartSchema.json';

  // 组件名称
  name = '仪表盘';
  isBaseComponent = true;
  description = 'echarts 仪表盘';
  docLink = '/amis/zh-CN/components/chart';
  tags = ['报表'];
  icon = 'fa fa-pie-chart';
  pluginIcon = 'chart-plugin';
  scaffold = {
    type: 'gauge-chart',
    isEdit: true,
    config: chartDefaultConfig,
    replaceChartOption: true,
    datasetInfo: {},
    yAxisField: []
  };
  previewSchema = {
    ...this.scaffold,
    api: ''
  };

  // 事件定义
  events: RendererPluginEvent[] = [
    {
      eventName: 'init',
      eventLabel: '初始化',
      description: '组件实例被创建并插入 DOM 中时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              description: '当前数据域，可以通过.字段名读取对应的值'
            }
          }
        }
      ]
    },
    {
      eventName: 'click',
      eventLabel: '鼠标点击',
      description: '鼠标点击时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'mouseover',
      eventLabel: '鼠标悬停',
      description: '鼠标悬停时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'legendselectchanged',
      eventLabel: '切换图例选中状态',
      description: '切换图例选中状态时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              properties: {
                name: {
                  type: 'string',
                  title: 'name'
                },
                selected: {
                  type: 'object',
                  title: 'selected'
                }
              }
            }
          }
        }
      ]
    }
  ];

  // 动作定义
  actions: RendererPluginAction[] = [
    {
      actionType: 'reload',
      actionLabel: '重新加载',
      description: '触发组件数据刷新并重新渲染'
    },
    {
      actionType: 'setValue',
      actionLabel: '变量赋值',
      description: '触发组件数据更新'
    }
    // 特性动作太多了，这里先不加了，可以通过写代码配置
  ];

  panelTitle = '仪表盘';
  panelJustify = true;
  panelBodyCreator = (context: BaseEventContext) => {

    const handleConfig = (data:any) => {
      // 创建配置的深拷贝
      const configCopy = JSON.parse(JSON.stringify(context.schema.config));
      const previewConfigCopy = JSON.parse(JSON.stringify(this.previewSchema.config));

      if (data.yAxis.length >= 1) {
        let yname = data.yAxis[0].name
        configCopy.series[0].data = '${list|pick:value~' + yname + '}'
        previewConfigCopy.series[0].data = '${list|pick:value~' + yname + '}'

        const newSchema = {
          ...context.schema,
          config: configCopy,
          yAxisField: data.yAxis
        };
        context.schema.config = configCopy;
        context.schema.yAxisField = data.yAxis;
        this.previewSchema.config = previewConfigCopy;
        
        // 通知编辑器进行更新
        context.node && this.manager.store.changeValueById(context.node.id, newSchema);
        
        this.panelBodyCreator(context);
      }

    }

    const handleDataSet = (data:any) => {
      const apiUrl = `/admin-api/system/form-field-value/page/${data.associatedDataTable}?pageNo=1&pageSize=100`;
      
      // 创建完整的新schema以触发更新
      const newSchema = {
        ...context.schema,
        api: apiUrl,
        datasetInfo: data
      };
      
      this.previewSchema.api = apiUrl;
      context.schema.api = apiUrl;
      context.schema.datasetInfo = data;

      // 通知编辑器进行更新
      context.node && this.manager.store.changeValueById(context.node.id, newSchema);
      
      this.panelBodyCreator(context);
    }
    return !context.schema.isEdit?[]:[
      getSchemaTpl('tabs', [
        {
          title: '交互',
          data: {
            config: chartDefaultConfig
          },
          body: [<ChartDataInteraction 
            context={context} 
            config={chartDefaultConfig} 
            handleConfig={handleConfig} 
            selectDataSet={handleDataSet} 
            datasetInfo={context.schema.datasetInfo}
            yAxisField={context.schema.yAxisField}
          />],
          className: 'p-none editorpanel'
        },
        {
          title: '样式',
          body: getSchemaTpl('collapseGroup', [
            singleColorConfig,
            scaleConfig,
            pointerConfig,
            {
              title: '宽高设置',
              body: [
                getSchemaTpl('style:widthHeight', {
                  widthSchema: {
                    label: tipedLabel(
                      '宽度',
                      '默认宽度为父容器宽度，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('100%')
                  },
                  heightSchema: {
                    label: tipedLabel(
                      '高度',
                      '默认高度为300px，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('300px')
                  }
                })
              ]
            },
            ...getSchemaTpl('theme:common', {exclude: ['layout']})
          ])
        },
        otherConfig,
        {
          title: '事件',
          className: 'p-none',
          body: [
            getSchemaTpl('eventControl', {
              name: 'onEvent',
              ...getEventControlConfig(this.manager, context)
            })
          ]
        }
      ])
    ];
  };

  editDrillDown(id: string) {
    const manager = this.manager;
    const store = manager.store;
    const node = store.getNodeById(id);
    const value = store.getValueOf(id);

    const dialog = (value.clickAction && value.clickAction.dialog) || {
      title: '标题',
      body: ['<p>内容 <code>${value|json}</code></p>']
    };

    node &&
      value &&
      this.manager.openSubEditor({
        title: '配置 DrillDown 详情',
        value: {
          type: 'container',
          ...dialog
        },
        slot: {
          type: 'container',
          body: '$$'
        },
        typeMutable: false,
        onChange: newValue => {
          newValue = {
            ...value,
            clickAction: {
              actionType: 'dialog',
              dialog: newValue
            }
          };
          manager.panelChangeValue(newValue, diff(value, newValue));
        }
      });
  }
}

registerEditorPlugin(GaugeChartPlugin);
