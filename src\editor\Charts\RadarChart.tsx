import {Button} from 'amis';
import React from 'react';
import {
  registerEditorPlugin,
  BaseEventContext,
  BasePlugin,
  RendererPluginAction,
  diff,
  defaultValue,
  getSchemaTpl,
  CodeEditor as AmisCodeEditor,
  RendererPluginEvent,
  tipedLabel
} from 'amis-editor-core';
// import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import ChartDataInteraction from '@/editor/component/ChartDataInteraction/RadarChartDataInteraction';



const ChartConfigEditor = ({value, onChange}: any) => {
  return (
    <div className="ae-JsonEditor">
      <AmisCodeEditor value={value} onChange={onChange} />
    </div>
  );
};

const DEFAULT_EVENT_PARAMS = [
  {
    type: 'object',
    properties: {
      data: {
        type: 'object',
        title: '数据',
        properties: {
          componentType: {
            type: 'string',
            title: 'componentType'
          },
          seriesType: {
            type: 'string',
            title: 'seriesType'
          },
          seriesIndex: {
            type: 'number',
            title: 'seriesIndex'
          },
          seriesName: {
            type: 'string',
            title: 'seriesName'
          },
          name: {
            type: 'string',
            title: 'name'
          },
          dataIndex: {
            type: 'number',
            title: 'dataIndex'
          },
          data: {
            type: 'object',
            title: 'data'
          },
          dataType: {
            type: 'string',
            title: 'dataType'
          },
          value: {
            type: 'number',
            title: 'value'
          },
          color: {
            type: 'string',
            title: 'color'
          }
        }
      }
    }
  }
];

const chartDefaultConfig = {

  radar: {
    shape: 'polygon',
    splitNumber: 5,
    axisLine: {
      show: true
    },
    splitLine: {
      show: true
    },
    axisName: {
      show: true,
      fontSize: 12,
      color: '#333'
    },
    axisLabel: {
      show: false,
      fontSize: 12,
      color: '#333'
    },
    max: 0,
    min: 0,
    indicator: [
      {
        name: 'Sales',
      },
      {
        name: 'Administration',
      },
      {
        name: 'Information Technology',
      },
      {
        name: 'Customer Support',
      },
      {
        name: 'Development',
      },
      {
        name: 'Marketing',
      }
    ]
  },
  series: [
    {
      name: 'Budget',
      type: 'radar',
      cusSymbolSize: 4,
      symbol: 'circle',
      data: [
        {
          value: [4200, 3000, 2000, 3000, 5000, 1800],
          name: 'Allocated Budget',
        }
      ],
      lineStyle: {
        width: 1,
      }
    }
  ],
  showSymbol: true
};

const otherConfig = {
  title: '其他',
  className: 'p-none',
  data: {
    config: chartDefaultConfig
  },
  body: getSchemaTpl('collapseGroup', [
    {
      title: '标题配置',
      body: [
        {
          type: 'switch',
          label: '标题显示',
          name: 'config.title.show',
          value: "${config.title.show}",
        },
        getSchemaTpl('label', {
          label: '标题文本',
          hiddenOn: '!config.title.show',
          name: 'config.title.text',
          value: "${config.title.text}",
        }),
        {
          type: 'switch',
          label: '标题提示',
          hiddenOn: '!config.title.show',
          name: 'config.title.showSubtext',
          value: "${config.title.showSubtext}",
        },
        {
          type: "formula",
          name: "config.title.subtextStyle.fontSize",
          formula: "${config.title.showSubtext ? 14 : 0}"
        },
        getSchemaTpl('label', {
          label: '提示内容',
          hiddenOn: '!config.title.show || !config.title.showSubtext',
          name: 'config.title.subtext',
          value: "${config.title.subtext}",
        }),
      ]
    },
    {
      title: '工具栏配置',
      body: [
        {
          type: 'switch',
          label: '复制为图片',
          name: 'config.toolbox.feature.saveAsImage.show',
          value: "${config.toolbox.feature.saveAsImage.show}",
        }
      ]
    }
  ])
};

const normalConfig = {
  title: '常用配置',
  body: [
    {
      label: '主题',
      type: 'select',
      name: 'chartThemeName',
      value: "${chartThemeName}",
      options: [
        {label: '默认', value: ''},
        {label: 'vintage', value: 'vintage'},
        {label: 'westeros', value: 'westeros'},
        {label: 'wonderland', value: 'wonderland'},
        {label: 'chalk', value: 'chalk'},
        {label: 'macarons', value: 'macarons'},
        {label: 'shine', value: 'shine'},
        {label: 'dark', value: 'dark'},
        {label: 'essos', value: 'essos'},
        {label: 'walden', value: 'walden'},
        {label: 'infographic', value: 'infographic'},
        {label: 'roma', value: 'roma'},
        {label: 'purple-passion', value: 'purple-passion'},        
      ]
    },
    getSchemaTpl('label', {
      label: '图表颜色',
      name: 'config.color',
      value: "${config.color}",
    }),
    {
      type: 'switch',
      label: '展示点',
      name: 'config.showSymbol',
      value: "${config.showSymbol}",
    },
    {
      label: '点大小',
      type: "input-number",
      name: "config.series[0].cusSymbolSize",
      value: "${config.series[0].cusSymbolSize}",
    },
    {
      type: "formula",
      name: "config.series[0].symbolSize",
      formula: "${config.showSymbol ? config.series[0].cusSymbolSize : 0}"
    },
    {
      label: '点形状',
      type: "select",
      name: "config.series[0].symbol",
      value: "${config.series[0].symbol}",
      options: [
        {label: '空心圆', value: 'emptyCircle'},
        {label: '圆形', value: 'circle'},
        {label: '方形', value: 'rect'},
        {label: '三角形', value: 'triangle'},
        {label: '菱形', value: 'diamond'},
        {label: '大头针', value: 'pin'},
        {label: '圆角矩形', value: 'roundRect'},
        {label: '箭头', value: 'arrow'},
      ]
    },
    {
      label: '折线粗细',
      type: "input-number",
      name: "config.series[0].lineStyle.width",
      value: "${config.series[0].lineStyle.width}",
    },
    {
      type: 'switch',
      label: '填充',
      name: 'config.areaShow',
      value: "${config.areaShow}",
    },
    {
      type: "formula",
      name: "config.series[0].areaStyle.opacity",
      formula: "${config.areaShow ? 0.7 : 0}"
    },
    {
      label: '类型',
      type: "select",
      name: "config.radar.shape",
      value: "${config.radar.shape}",
      options: [
        {label: '圆形', value: 'circle'},
        {label: '多边形', value: 'polygon'},
      ]
    },
  ]
};

const diffConfig = {
  title: '分类',
  body: [
    {
      type: 'switch',
      label: '轴显示',
      name: 'config.radar.axisLine.show',
      value: "${config.radar.axisLine.show}",
    },
    {
      type: 'switch',
      label: '网格线',
      name: 'config.radar.splitLine.show',
      value: "${config.radar.splitLine.show}",
    },
    {
      type: 'switch',
      label: '标签',
      name: 'config.radar.axisName.show',
      value: "${config.radar.axisName.show}",
    },
  ]
}

const scaleConfig = {
  title: '刻度',
  body: [
    {
      type: 'switch',
      label: '轴显示',
      name: 'config.radar.axisLine.show',
      value: "${config.radar.axisLine.show}",
    },
    {
      type: 'switch',
      label: '轴线',
      name: 'config.radar.axisLine.show',
      value: "${config.radar.axisLine.show}",
    },
    {
      type: 'switch',
      label: '网格线',
      name: 'config.radar.splitLine.show',
      value: "${config.radar.splitLine.show}",
    },
    {
      type: 'switch',
      label: '标签',
      name: 'config.radar.axisLabel.show',
      value: "${config.radar.axisLabel.show}",
    },
    {
      type: 'input-number',
      label: '最大值',
      name: 'config.radar.max',
      value: "${config.radar.max}",
    },
    {
      type: 'input-number',
      label: '最小值',
      name: 'config.radar.min',
      value: "${config.radar.min}",
    },
    {
      type: 'input-number',
      label: '刻度数',
      name: 'config.radar.splitNumber',
      value: "${config.radar.splitNumber}",
    },
  ]
}

const legendConfig = {
  title: '图例',
  body: [
    {
      type: 'switch',
      label: '显示图例',
      name: 'config.legend.show',
      value: "${config.legend.show}",
    },
    {
      type: 'switch',
      label: '是否分页',
      hiddenOn: '!config.legend.show',
      name: 'config.legend.pageShow',
      value: "${config.legend.pageShow}",
    },
    {
      type: "formula",
      name: "config.legend.type",
      formula: "${config.legend.pageShow ? 'scroll' : 'plain'}"
    },
  ]
}

const labelConfig = {
  title: '标签配置',
  body: [
    {
      label: '标签字号',
      type: "input-number",
      name: "config.radar.axisName.fontSize",
      value: "${config.radar.axisName.fontSize}",
    },
    {
      label: '标签颜色',
      type: "input-color",
      name: 'config.radar.axisName.color',
      value: "${config.radar.axisName.color}",
    }
  ]
}

const tooltipConfig = {
  title: '提示信息',
  body: [
    {
      type: 'switch',
      label: '显示提示信息',
      name: 'config.tooltip.show',
      value: "${config.tooltip.show}",
    }
  ]
}

export class RadarChartPlugin extends BasePlugin {
  static id = 'RadarChartPlugin';
  // 关联渲染器名字
  rendererName = 'radar-chart';
  $schema = '/schemas/ChartSchema.json';

  // 组件名称
  name = '雷达图';
  isBaseComponent = true;
  description = 'echarts 雷达图';
  docLink = '/amis/zh-CN/components/chart';
  tags = ['报表'];
  icon = 'fa fa-pie-chart';
  pluginIcon = 'chart-plugin';
  scaffold = {
    type: 'radar-chart',
    isEdit: true,
    config: chartDefaultConfig,
    replaceChartOption: true,
    datasetInfo: {},
    xAxisField: [],
    yAxisField: []
  };
  previewSchema = {
    ...this.scaffold,
    api: ''
  };

  // 事件定义
  events: RendererPluginEvent[] = [
    {
      eventName: 'init',
      eventLabel: '初始化',
      description: '组件实例被创建并插入 DOM 中时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              description: '当前数据域，可以通过.字段名读取对应的值'
            }
          }
        }
      ]
    },
    {
      eventName: 'click',
      eventLabel: '鼠标点击',
      description: '鼠标点击时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'mouseover',
      eventLabel: '鼠标悬停',
      description: '鼠标悬停时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'legendselectchanged',
      eventLabel: '切换图例选中状态',
      description: '切换图例选中状态时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              properties: {
                name: {
                  type: 'string',
                  title: 'name'
                },
                selected: {
                  type: 'object',
                  title: 'selected'
                }
              }
            }
          }
        }
      ]
    }
  ];

  // 动作定义
  actions: RendererPluginAction[] = [
    {
      actionType: 'reload',
      actionLabel: '重新加载',
      description: '触发组件数据刷新并重新渲染'
    },
    {
      actionType: 'setValue',
      actionLabel: '变量赋值',
      description: '触发组件数据更新'
    }
    // 特性动作太多了，这里先不加了，可以通过写代码配置
  ];

  panelTitle = '雷达图';
  panelJustify = true;
  panelBodyCreator = (context: BaseEventContext) => {
        const handleConfig = (data:any) => {
      // 创建配置的深拷贝
      const configCopy = JSON.parse(JSON.stringify(context.schema.config));
      const previewConfigCopy = JSON.parse(JSON.stringify(this.previewSchema.config));

      if (data.xAxis.length == 1) {
        let xname = data.xAxis[0].name
        configCopy.radar.indicator =  '${list|pick:name~' + xname + '}'
        previewConfigCopy.radar.indicator = '${list|pick:name~' + xname + '}'
      }
      if (data.yAxis.length > 0) {
        let json = data.yAxis.map((item:any) => {
          return {
            label: item.label,
            name: item.name
          }
        })
        let jsonString = JSON.stringify(json)
        configCopy.series[0].data = '${list|radarParse:' + jsonString + '}'
        previewConfigCopy.series[0].data = '${list|radarParse:' + jsonString + '}'
      }

        const newSchema = {
          ...context.schema,
          config: configCopy,
          xAxisField: data.xAxis,
          yAxisField: data.yAxis
        };
        context.schema.config = configCopy;
        context.schema.xAxisField = data.xAxis;
        context.schema.yAxisField = data.yAxis;
        this.previewSchema.config = previewConfigCopy;
        
        // 通知编辑器进行更新
        context.node && this.manager.store.changeValueById(context.node.id, newSchema);
        
        this.panelBodyCreator(context);
    }

    const handleDataSet = (data:any) => {
      const apiUrl = `/admin-api/system/form-field-value/page/${data.associatedDataTable}?pageNo=1&pageSize=100`;
      
      // 创建完整的新schema以触发更新
      const newSchema = {
        ...context.schema,
        api: apiUrl,
        datasetInfo: data
      };
      
      this.previewSchema.api = apiUrl;
      context.schema.api = apiUrl;
      context.schema.datasetInfo = data;

      // 通知编辑器进行更新
      context.node && this.manager.store.changeValueById(context.node.id, newSchema);
      
      this.panelBodyCreator(context);
    }

    return !context.schema.isEdit?[]:[
      getSchemaTpl('tabs', [
        {
          title: '交互',
          data: {
            config: chartDefaultConfig
          },
          body: [<ChartDataInteraction 
            context={context} 
            config={chartDefaultConfig} 
            handleConfig={handleConfig} 
            selectDataSet={handleDataSet} 
            datasetInfo={context.schema.datasetInfo}
            xAxisField={context.schema.xAxisField}
            yAxisField={context.schema.yAxisField}
          />],
          className: 'p-none editorpanel'
        },
        {
          title: '样式',
          body: getSchemaTpl('collapseGroup', [
            normalConfig,
            diffConfig,
            scaleConfig,
            labelConfig,
            legendConfig,
            tooltipConfig,
            {
              title: '宽高设置',
              body: [
                getSchemaTpl('style:widthHeight', {
                  widthSchema: {
                    label: tipedLabel(
                      '宽度',
                      '默认宽度为父容器宽度，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('100%')
                  },
                  heightSchema: {
                    label: tipedLabel(
                      '高度',
                      '默认高度为300px，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('300px')
                  }
                })
              ]
            },
            ...getSchemaTpl('theme:common', {exclude: ['layout']})
          ])
        },
        otherConfig,
        {
          title: '事件',
          className: 'p-none',
          body: [
            getSchemaTpl('eventControl', {
              name: 'onEvent',
              ...getEventControlConfig(this.manager, context)
            })
          ]
        }
      ])
    ];
  };

  editDrillDown(id: string) {
    const manager = this.manager;
    const store = manager.store;
    const node = store.getNodeById(id);
    const value = store.getValueOf(id);

    const dialog = (value.clickAction && value.clickAction.dialog) || {
      title: '标题',
      body: ['<p>内容 <code>${value|json}</code></p>']
    };

    node &&
      value &&
      this.manager.openSubEditor({
        title: '配置 DrillDown 详情',
        value: {
          type: 'container',
          ...dialog
        },
        slot: {
          type: 'container',
          body: '$$'
        },
        typeMutable: false,
        onChange: newValue => {
          newValue = {
            ...value,
            clickAction: {
              actionType: 'dialog',
              dialog: newValue
            }
          };
          manager.panelChangeValue(newValue, diff(value, newValue));
        }
      });
  }
}

registerEditorPlugin(RadarChartPlugin);
