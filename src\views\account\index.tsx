import React, {useState, useRef, useEffect} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {Button, Avatar, Tabs, Tab, toast} from 'amis-ui';
import {RouteComponentProps} from 'react-router-dom';
import AMISRenderer from '@/component/AMISRenderer';
import {UnbindWxDialog} from '@/utils/schemaDataSet/UnbindWxDialog';
import {PasswordDialog} from '@/utils/schemaDataSet/PasswordDialog';
import {VerifyPasswordDialog} from '@/utils/schemaDataSet/VerifyPasswordDialog';
import {BindMobileDialog} from '@/component/BindMobileDialog/index';
import {BindEmailDialog} from '@/component/BindEmailDialog';
import {RebindEmailDialog} from '@/component/RebindEmailDialog';
import {EditNameDialog} from '@/utils/schemaDataSet/EditNameDialog';
import {EditNicknameDialog} from '@/utils/schemaDataSet/EditNicknameDialog';
import {EditAddressDialog} from '@/utils/schemaDataSet/EditAddressDialog';
import {EditBioDialog} from '@/utils/schemaDataSet/EditBioDialog';
import {EditGenderDialog} from '@/utils/schemaDataSet/EditGenderDialog';
import {EditBirthdayDialog} from '@/utils/schemaDataSet/EditBirthdayDialog';
import {RebindMobileDialog} from '@/component/RebindMobileDialog';
import {formatTimestamp} from '@/utils/common/timeUtil';
import cityCodeUtil from '@/utils/cityCodeUtil/cityCodeUtil';
import {
  updateUserProfile,
  updateUserAvatar,
  socialUnBind
} from '@/utils/api/api';
import './index.scss';

interface AccountProps extends RouteComponentProps {
  store: IMainStore;
}

export default inject('store')(
  observer(function ({store, history, location, match}: AccountProps) {
    const [activeTab, setActiveTab] = useState('basic');
    const [showUnbindWxDialog, setShowUnbindWxDialog] = useState(false);
    const [showPasswordDialog, setShowPasswordDialog] = useState(false);
    const [showVerifyDialog, setShowVerifyDialog] = useState(false);
    const [showBindMobileDialog, setShowBindMobileDialog] = useState(false);
    const [showBindEmailDialog, setShowBindEmailDialog] = useState(false);
    const [showRebindEmailDialog, setShowRebindEmailDialog] = useState(false);
    const [showRebindMobileDialog, setShowRebindMobileDialog] = useState(false);
    const [countdown, setCountdown] = useState(0);
    const timerRef = useRef<ReturnType<typeof setInterval>>();
    const [mobile, setMobile] = useState('');
    const [code, setCode] = useState('');
    const [emailCountdown, setEmailCountdown] = useState(0);
    const emailTimerRef = useRef<ReturnType<typeof setInterval>>();
    const [showEditNameDialog, setShowEditNameDialog] = useState(false);
    const [showEditNicknameDialog, setShowEditNicknameDialog] = useState(false);
    const [showEditAddressDialog, setShowEditAddressDialog] = useState(false);
    const [showEditBioDialog, setShowEditBioDialog] = useState(false);
    const [showEditGenderDialog, setShowEditGenderDialog] = useState(false);
    const [showEditBirthdayDialog, setShowEditBirthdayDialog] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // 通用的更新用户资料方法
    const updateProfile = (
      data: any,
      fieldName: string,
      value: any,
      closeDialog: () => void
    ) => {
      console.log(`edit ${fieldName}:`, value);

      updateUserProfile(data)
        .then(res => {
          console.log('update user profile:', res);
          if (res.code === 0) {
            store.setUserInfo({
              ...store.userInfo,
              [fieldName]: value
            });
            closeDialog();
          } else {
            toast.error(res.message);
          }
        })
        .catch(err => {
          toast.error(err.message);
        });
    };

    // 通用的文件上传方法
    const uploadFile = (
      file: File,
      fieldName: string,
      uploadApi: (formData: FormData) => Promise<any>,
      options: {
        maxSize?: number;
        allowedTypes?: RegExp;
        errorMessages?: {
          typeError?: string;
          sizeError?: string;
        };
      } = {}
    ) => {
      // 默认配置
      const defaultOptions = {
        maxSize: 2 * 1024 * 1024, // 默认2MB
        allowedTypes: /^image\/(jpeg|png)$/, // 默认jpg和png
        errorMessages: {
          typeError: '只支持 jpg、png 格式的图片',
          sizeError: '图片大小不能超过 2M'
        }
      };

      const config = {...defaultOptions, ...options};

      // 验证文件类型
      if (!file.type.match(config.allowedTypes)) {
        store.alert(config.errorMessages.typeError || '文件类型不支持');
        return;
      }

      // 验证文件大小
      if (file.size > config.maxSize) {
        store.alert(config.errorMessages.sizeError || '文件大小超出限制');
        return;
      }

      // 创建 FormData
      const formData = new FormData();
      formData.append(fieldName, file);
      console.log('file:', file);

      // 调用上传API
      return uploadApi(formData)
        .then(res => {
          if (res.code === 0) {
            store.setUserInfo({
              ...store.userInfo,
              avatar: res.data
            });
          }
        })
        .catch(err => {
          toast.error(err.message);
          // throw err;
        });
    };

    const unbindWx = () => {
      console.log('unbindWx');
      socialUnBind({
        "type": '32',
        "openid": store.userInfo?.socialUsers![0].openid
      }).then(res => {
        if (res.code === 0) {
          toast.success('解绑成功');
          store.setUserInfo({
            ...store.userInfo,
            socialUsers: []
          });
        } else {
          toast.error('解绑失败：' + (res.msg || '未知错误'));
        }
      });
    };

    const handleUnbindWx = () => {
      if (store.userInfo?.socialUsers?.length !== 0) {
        setShowUnbindWxDialog(true);
      } else {
        console.log('bind wx');
        history.push('/bindWx');
      }
    };

    const handlePasswordClick = () => {
      if (store.userInfo?.password) {
        // 如果已有密码，先显示验证密码对话框
        setShowVerifyDialog(true);
      } else {
        // 如果没有密码，直接显示设置密码对话框
        setShowPasswordDialog(true);
      }
    };

    const handleVerifyPassword = (values: {password: string}[]) => {
      const password = values[0]?.password;
      if (password) {
        // TODO: 验证密码
        console.log('verify password:', password);
        setShowVerifyDialog(false);
        // 验证成功后显示重置密码对话框
        setShowPasswordDialog(true);
      }
    };

    const handleSetPassword = (values: {password: string}[]) => {
      const password = values[0]?.password;
      if (password) {
        console.log('set password:', password);
        // 调用设置密码的 API
      }
    };

    const handleSendCode = (mobile: string) => {
      if (!mobile || mobile.length !== 11) {
        return;
      }

      // 开始倒计时
      setCountdown(60);

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      timerRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      // TODO: 调用发送验证码的 API
      console.log('send code to:', mobile);
    };

    const handleBindMobile = (mobile: string, code: string) => {
      console.log('bind mobile:', mobile, code);
      // 调用绑定手机号的 API
      setShowBindMobileDialog(false);
    };

    const handleSendEmailCode = (email: string) => {
      if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        return;
      }

      // 开始倒计时
      setEmailCountdown(60);

      if (emailTimerRef.current) {
        clearInterval(emailTimerRef.current);
      }

      emailTimerRef.current = setInterval(() => {
        setEmailCountdown(prev => {
          if (prev <= 1) {
            if (emailTimerRef.current) {
              clearInterval(emailTimerRef.current);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      // TODO: 调用发送验证码的 API
      console.log('send email code to:', email);
    };

    const handleBindEmail = (email: string, code: string) => {
      console.log('bind email:', email, code);
      // 调用绑定邮箱的 API
      setShowBindEmailDialog(false);
    };

    const handleRebindEmail = (email: string, code: string) => {
      console.log('rebind email:', email, code);
      // 调用重新绑定邮箱的 API
      setShowRebindEmailDialog(false);
    };

    const handleRebindMobile = (mobile: string, code: string) => {
      console.log('rebind mobile:', mobile, code);
      // TODO: 调用重新绑定手机号的 API
      setShowRebindMobileDialog(false);
    };

    const handleEditName = (values: {realname: string}[]) => {
      const realname = values[0]?.realname;
      if (realname) {
        updateProfile({realname: realname}, 'realname', realname, () =>
          setShowEditNameDialog(false)
        );
      }
    };

    const handleEditNickname = (values: {nickname: string}[]) => {
      const nickname = values[0]?.nickname;
      if (nickname) {
        updateProfile({nickname: nickname}, 'nickname', nickname, () =>
          setShowEditNicknameDialog(false)
        );
      }
    };

    const handleEditAddress = (values: {detailAddress_city: string,detailAddress_detail:string}[]) => {
      const detailedCity = values[0]?.detailAddress_city;
      const detailedAddress = values[0]?.detailAddress_detail;
      const cityName = cityCodeUtil.getFullAddressByCode(detailedCity);
      console.log('edit address:', detailedCity, detailedAddress,cityName);
      if (detailedCity) {
        updateProfile(
          {detailedAddress: cityName+" "+detailedAddress},
          'detailedAddress',
          cityName+" "+detailedAddress,
          () => setShowEditAddressDialog(false)
        );
      }
    };

    const handleEditBio = (values: {bio: string}[]) => {
      const bio = values[0]?.bio;
      if (bio) {
        updateProfile({bio: bio}, 'bio', bio, () =>
          setShowEditBioDialog(false)
        );
      }
    };

    const handleEditGender = (values: {sex: string}[]) => {
      console.log('edit sex:', values);
      const sex = values[0]?.sex;
      if (sex) {
        updateProfile({sex: sex}, 'sex', sex, () =>
          setShowEditGenderDialog(false)
        );
      }
    };

    const handleEditBirthday = (values: {birthday: string}[]) => {
      const birthday = values[0]?.birthday;

      if (birthday) {
        // 将日期字符串转换为时间戳（毫秒）
        const timestamp = new Date(birthday).getTime();
        updateProfile({birthday: timestamp}, 'birthday', timestamp, () =>
          setShowEditBirthdayDialog(false)
        );
      }
    };

    // 处理头像上传
    const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        uploadFile(file, 'avatarFile', updateUserAvatar, {
          allowedTypes: /^image\/(jpeg|png)$/,
          maxSize: 2 * 1024 * 1024,
          errorMessages: {
            typeError: '只支持 jpg、png 格式的图片',
            sizeError: '图片大小不能超过 2M'
          }
        });
      }
      // 清空 input 的值，这样同一个文件可以重复选择
      event.target.value = '';
    };

    // 组件卸载时清理定时器
    useEffect(() => {
      console.log(EditAddressDialog(
        showEditAddressDialog,
        store.userInfo?.detailedAddress
      ));
      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        if (emailTimerRef.current) {
          clearInterval(emailTimerRef.current);
        }
      };
    }, []);

    return (
      <div className="account-settings">
        <div className="account-settings-header">
          <div className="account-settings-header-title">个人设置</div>
          <div className="account-settings-header">
            <Tabs
              mode="line"
              theme={store.theme} // 显式传入主题
              activeKey={activeTab}
              onSelect={(key: string) => setActiveTab(key)}
              toolbar={
                <div className="account-settings-header-tabsToolbar"></div>
              }
              linksClassName="account-settings-header-tabsTitle"
            >
              <Tab title="账户信息" eventKey="basic">
                <div className="account-settings-tabsContent">
                  {/* 头像设置 */}
                  <div className="settings-item">
                    <div className="avatar-wrapper">
                      {store.userInfo?.avatar ? (
                        <Avatar src={store.userInfo?.avatar} size={48} />
                      ) : (
                        <div className="default-avatar">
                          {store.userInfo?.nickname?.charAt(0)}
                        </div>
                      )}
                    </div>
                    <div className="settings-main">
                      <div className="settings-label">头像</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            支持 2M 以内的 JPG 或 PNG 图片
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        style={{display: 'none'}}
                        onChange={handleAvatarUpload}
                      />
                      <Button
                        level="link"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        更换头像
                      </Button>
                    </div>
                  </div>

                  {/* 真实姓名 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">真实姓名</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.realname || '填写真实姓名'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button
                        level="link"
                        onClick={() => setShowEditNameDialog(true)}
                      >
                        编辑姓名
                      </Button>
                    </div>
                  </div>

                  {/* 昵称 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">昵称</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.nickname || '为自己起一个称呼'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button
                        level="link"
                        onClick={() => setShowEditNicknameDialog(true)}
                      >
                        编辑昵称
                      </Button>
                    </div>
                  </div>

                  {/* 手机号 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">手机号</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.mobile ||
                              '绑定手机号后，可以通过短信验证码登录'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      {store.userInfo?.mobile && (
                        <Button
                          level="link"
                          onClick={() => setShowRebindMobileDialog(true)}
                        >
                          更换绑定
                        </Button>
                      )}
                      <Button
                        level="link"
                        onClick={() => {
                          if (!store.userInfo?.mobile) {
                            setShowBindMobileDialog(true);
                          } else {
                            // 处理解除绑定的逻辑
                            console.log('unbind mobile');
                          }
                        }}
                      >
                        {store.userInfo?.mobile ? '解除绑定' : '绑定手机号'}
                      </Button>
                    </div>
                  </div>

                  {/* 邮箱 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">邮箱</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.email ||
                              '绑定邮箱后，可以通过邮箱登录'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      {store.userInfo?.email && (
                        <Button
                          level="link"
                          onClick={() => setShowRebindEmailDialog(true)}
                        >
                          更换绑定
                        </Button>
                      )}
                      <Button
                        level="link"
                        onClick={() => {
                          if (!store.userInfo?.email) {
                            setShowBindEmailDialog(true);
                          } else {
                            // 处理解除绑定的逻辑
                            console.log('unbind email');
                          }
                        }}
                      >
                        {store.userInfo?.email ? '解除绑定' : '绑定邮箱'}
                      </Button>
                    </div>
                  </div>

                  {/* 密码 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">密码</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.password
                              ? '••••••'
                              : '设置密码后，可以通过账号密码登录'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button level="link" onClick={handlePasswordClick}>
                        {store.userInfo?.password ? '重置密码' : '设置密码'}
                      </Button>
                    </div>
                  </div>

                  {/* 微信 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">微信</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.socialUsers?.length !== 0
                              ? '已绑定'
                              : '绑定微信后，可以通过微信扫码登录'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button level="link" onClick={handleUnbindWx}>
                        {store.userInfo?.socialUsers?.length !== 0 ? '解除绑定' : '绑定微信'}
                      </Button>
                    </div>
                  </div>

                  {/* 性别 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">性别</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.sex == 1
                              ? '男'
                              : store.userInfo?.sex == 2
                              ? '女'
                              : '请选择'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button
                        level="link"
                        onClick={() => setShowEditGenderDialog(true)}
                      >
                        选择性别
                      </Button>
                    </div>
                  </div>

                  {/* 出生日期 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">出生日期</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.birthday
                              ? formatTimestamp(store.userInfo?.birthday)
                              : '选择你的出生日期'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button
                        level="link"
                        onClick={() => setShowEditBirthdayDialog(true)}
                      >
                        选择日期
                      </Button>
                    </div>
                  </div>

                  {/* 详细地址 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">详细地址</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.detailedAddress ||
                              '填写你的居住地'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button
                        level="link"
                        onClick={() => setShowEditAddressDialog(true)}
                      >
                        编辑地址
                      </Button>
                    </div>
                  </div>

                  {/* 个人简介 */}
                  <div className="settings-item">
                    <div className="settings-main">
                      <div className="settings-label">个人简介</div>
                      <div className="settings-content">
                        <div className="settings-value">
                          <div className="settings-text">
                            {store.userInfo?.intro ||
                              '填写个人简介，让大家更好的认识你'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="settings-action">
                      <Button
                        level="link"
                        onClick={() => setShowEditBioDialog(true)}
                      >
                        编辑简介
                      </Button>
                    </div>
                  </div>
                </div>
              </Tab>
              <Tab title="团队" eventKey="team">
                <div className="account-settings-tabsContent">
                  {/* 团队内容 */}
                </div>
              </Tab>
              <Tab title="通知设置" eventKey="notify">
                <div className="account-settings-tabsContent">
                  {/* 通知设置内容 */}
                </div>
              </Tab>
            </Tabs>
          </div>
        </div>

        <AMISRenderer
          show={showUnbindWxDialog}
          onClose={() => setShowUnbindWxDialog(false)}
          onConfirm={() => {
            // 处理解除绑定的逻辑
            setShowUnbindWxDialog(false);
            unbindWx();
          }}
          schema={UnbindWxDialog(showUnbindWxDialog)}
        />

        {/* 验证密码对话框 */}
        <AMISRenderer
          show={showVerifyDialog}
          onClose={() => setShowVerifyDialog(false)}
          onConfirm={handleVerifyPassword}
          schema={VerifyPasswordDialog(showVerifyDialog)}
        />

        {/* 设置/重置密码对话框 */}
        <AMISRenderer
          show={showPasswordDialog}
          onClose={() => setShowPasswordDialog(false)}
          onConfirm={handleSetPassword}
          schema={PasswordDialog(
            showPasswordDialog,
            !!store.userInfo?.password
          )}
        />

        {/* 手机号绑定对话框 */}
        <BindMobileDialog
          show={showBindMobileDialog}
          isRebind={!!store.userInfo?.mobile}
          countdown={countdown}
          currentMobile={store.userInfo?.mobile}
          onClose={() => setShowBindMobileDialog(false)}
          onConfirm={handleBindMobile}
          onSendCode={handleSendCode}
        />

        {/* 邮箱绑定对话框 */}
        <BindEmailDialog
          show={showBindEmailDialog}
          isRebind={!!store.userInfo?.email}
          countdown={emailCountdown}
          currentEmail={store.userInfo?.email}
          onClose={() => setShowBindEmailDialog(false)}
          onConfirm={handleBindEmail}
          onSendCode={handleSendEmailCode}
        />

        {/* 编辑姓名对话框 */}
        <AMISRenderer
          show={showEditNameDialog}
          onClose={() => setShowEditNameDialog(false)}
          onConfirm={handleEditName}
          schema={EditNameDialog(showEditNameDialog, store.userInfo?.realname)}
        />

        {/* 编辑昵称对话框 */}
        <AMISRenderer
          show={showEditNicknameDialog}
          onClose={() => setShowEditNicknameDialog(false)}
          onConfirm={handleEditNickname}
          schema={EditNicknameDialog(
            showEditNicknameDialog,
            store.userInfo?.nickname
          )}
        />

        {/* 编辑地址对话框 */}
        <AMISRenderer
          show={showEditAddressDialog}
          onClose={() => setShowEditAddressDialog(false)}
          onConfirm={handleEditAddress}
          schema={EditAddressDialog(
            showEditAddressDialog,
            store.userInfo?.detailedAddress
          )}
        />

        {/* 编辑个人简介对话框 */}
        <AMISRenderer
          show={showEditBioDialog}
          onClose={() => setShowEditBioDialog(false)}
          onConfirm={handleEditBio}
          schema={EditBioDialog(showEditBioDialog, store.userInfo?.intro)}
        />

        {/* 编辑性别对话框 */}
        <AMISRenderer
          show={showEditGenderDialog}
          onClose={() => setShowEditGenderDialog(false)}
          onConfirm={handleEditGender}
          schema={EditGenderDialog(showEditGenderDialog, store.userInfo?.sex)}
        />

        {/* 编辑出生日期对话框 */}
        <AMISRenderer
          show={showEditBirthdayDialog}
          onClose={() => setShowEditBirthdayDialog(false)}
          onConfirm={handleEditBirthday}
          schema={EditBirthdayDialog(
            showEditBirthdayDialog,
            store.userInfo?.birthday
          )}
        />

        {/* 重新绑定邮箱对话框 */}
        <RebindEmailDialog
          show={showRebindEmailDialog}
          currentEmail={store.userInfo?.email}
          emailCountdown={emailCountdown}
          mobileCountdown={countdown}
          onClose={() => setShowRebindEmailDialog(false)}
          onConfirm={handleRebindEmail}
          onSendEmailCode={handleSendEmailCode}
          onSendMobileCode={handleSendCode}
        />

        {/* 重新绑定手机号对话框 */}
        <RebindMobileDialog
          show={showRebindMobileDialog}
          currentMobile={store.userInfo?.mobile}
          emailCountdown={emailCountdown}
          mobileCountdown={countdown}
          onClose={() => setShowRebindMobileDialog(false)}
          onConfirm={handleRebindMobile}
          onSendEmailCode={handleSendEmailCode}
          onSendMobileCode={handleSendCode}
        />
      </div>
    );
  })
);
