import React, {FC} from 'react';
import './index.scss';
import {Button, Tabs, Tab, toast, Modal, responseAdaptor} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj, createCustompageObj} from '@/utils/schemaPageTemplate/createPageObjs';
import fileAudio from '@/image/common_icons/file_audio.png';
import filePdf from '@/image/common_icons/file_pdf.png';
import fileWord from '@/image/common_icons/file_word.png';
import fileZip from '@/image/common_icons/file_zip.png';
import PagePanDataSettings from '@/component/PagePanDataSettings/index';

import {
  getFilePage, //获取表单数据
  getFormDataPage,
  createFormData
} from '@/utils/api/api';
import PagePanSettings from '@/component/PagePanSettings';
import { height } from '@/editor/EChartsEditor/Common';

// 声明全局方法
declare global {
  interface Window {
    openUploadDialog: () => void;
    setShowFolderModal: (show: boolean) => void;
    handleDeleteFile: (id: number) => void;
  }
}

const PageContent: FC<any> = (props: any) => {
  const [pageData, setPageData] = React.useState<any>({});
  
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');
  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }
    
    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  const [fileList, setFileList] = React.useState<any>([]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [showUploadModal, setShowUploadModal] = React.useState(false);
  const [showFolderModal, setShowFolderModal] = React.useState(false);
  const [newFolderName, setNewFolderName] = React.useState('');
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const appId = props.history.location.pathname.split('/')[1].slice(3);

  //card ,list
  const [mode, setMode] = React.useState('card');

  const getToBlak = () => {
    let schema = JSON.parse(props.pageData.schema)
    let a = document.createElement('a');
    a.href = schema.body[0].src;
    window.open(a.href, '_blank');
  };

  // 获取表单数据 pageData
  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: 1,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            // toast.success('获取表单数据成功');
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            res.data.list[0].pageType = 13;
            res.data.list[0].appId = 1;
            setPageData(res.data.list[0]);
          }else{
            toast.success('暂无表单数据');
          }
        }else{
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  const initCustompageData = (pageId: any, info: any) => {
    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(createCustompageObj(info.name)),
      field: ''
    };
    createFormData(data).then((res: any) => {
      if (res.code == 0) {
        toast.success('初始化数据完成~');
        // setOpenCustomPagePopup(false);
        props.history.push(
          `/app${appId}/design/page${pageId}?editCode=${pageId}`
        );
        handleGetFormDataPage();
        // props.onSaveSuccess(pageId);
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };

  React.useEffect(() => {
    // if (props.pageData?.id) {
      handleGetFormDataPage();
    // }
    const urlParams = new URLSearchParams(props.history.location.search);
    let tabkey =  urlParams.get('activeTabKey') || 'preview';
    console.log(appId);

    setActiveKey(tabkey);
  }, [props.location]);

  return (
    <div className="pageBox">
      <div className="pageTop">
        <div className="pageTop-title">数据卡片管理</div>
      </div>
      <div className="pageTabsLink">
        {/* activeKey */}
        <Tabs
          mode="line"
          theme={localStorage.getItem('amis-theme') || 'cxd'} // 显式传入主题
          onSelect={(key: any) => handleTabChange(key)}
          toolbar={TabsToolbar()}
          linksClassName="pageTabsLink-tabsTitle"
          activeKey={activeKey}
        >
          <Tab title="数据卡片预览" eventKey="preview">
            <div className="pageTabsLink-tabsContent">
              <div>全部数据卡片</div>
              <div style={{display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>
                <div>
                  <Button level="default" onClick={() => setMode('card')}>
                    卡片
                  </Button>
                </div>
                <div>
                  <Button level="default" onClick={() => setMode('list')}>
                    表格
                  </Button>
                </div>
              </div>
              {mode === 'card' && (
                <AMISRenderer
                  schema={{
                    type: 'page',
                    title: '',
                    body: [
                      {
                        type: 'crud',
                        syncLocation: false,
                        id: 'crudId',
                        api: '/admin-api/system/application-page-and-class/get-data-card-page?pageNo=${page}&pageSize=${perPage}&applicationId=' + appId,
                        mode: 'cards',
                        columnsCount: 4,
                        card: {
                          className: "border-none",
                          itemAction: {
                            type: "button",
                            actionType: "dialog",
                            dialog: {
                              title: '数据卡片预览',
                              size: 'lg',
                              body: [
                                {
                                  type: 'editor',
                                  value: '${pageData|bodyParse}',
                                  name: 'cardBody',
                                  hidden: true,
                                },
                                {
                                  type: 'amis',
                                  name: 'cardBody',
                                }
                              ]
                            }
                          },
                          body: [
                            {
                              type: 'service',
                              // name: 'form',
                              body: [
                                {
                                  type: 'wrapper',
                                  style: {
                                    border: '1px solid #e8e9eb',
                                    borderRadius: '4px',
                                  },
                                  body: [
                                    {
                                      type: 'wrapper',
                                      style: {
                                        height: 200,
                                        backgroundColor: '#F7F8FA',
                                        overflow: 'auto' // 如果内容超出高度，显示滚动条
                                      },
                                      body: [
                                        {
                                          type: 'editor',
                                          name: 'cardBody',
                                          value: '${pageData|bodyParse}',
                                          hidden: true,
                                        },
                                        {
                                          type: 'amis',
                                          name: 'cardBody',
                                          className: 'h-8',
                                          style: {
                                            height: 200,
                                            overflow: 'auto' // 如果内容超出高度，显示滚动条
                                          },
                                        },
                                      ]
                                    },
                                    
                                    {
                                      type: 'tpl',
                                      tpl: '${name}',
                                    }
                                  ]
                                },
                                
                              ]
                            }
                          ]
                        },
                        headerToolbar: [
                          {
                            type: 'button',
                            label: '新建数据卡片',
                            level: 'primary',
                            icon: 'fa fa-add',
                            className: 'mr-2',
                            actionType: 'dialog',
                            feat: 'Insert',
                            reload: true,
                            dialog: {
                              title: '创建数据卡片',
                              body: [
                                {
                                  type: 'form',
                                  api: {
                                    method: 'post',
                                    url: '/admin-api/system/application-page-and-class/create',
                                    data: {
                                      applicationId: appId,
                                      name: '${name}',
                                      applicantOrBackend: 1,
                                      pageType: 16,
                                      type: 1,                
                                    },
                                    adaptor: function (payload: any, response: any, api: any, context: any) {
                                      initCustompageData(response.data.data.id, context);
                                      return payload;
                                    } 
                                  },
                                  body: [
                                    {
                                      type: 'input-text',
                                      name: 'name',
                                      label: '数据卡片名称',
                                    }
                                  ]
                                }
                              ]
                            }
                          },
                          {
                            type: 'reload',
                          }
                        ],
                        footerToolbar: [
                          'statistics',
                          'switch-per-page',
                          'pagination'
                        ]
                      
                      }
                    ]
                  }}
                  embedMode={true}
                />
              )}
              {mode === 'list' && (
               <AMISRenderer
               schema={{
                type: 'page',
                title: '',
                body: [
                  {
                    type: 'crud',
                    syncLocation: false,
                    id: 'crudId',
                    api: '/admin-api/system/application-page-and-class/get-data-card-page?pageNo=${page}&pageSize=${perPage}&applicationId=' + appId,
                    columns: [
                      {
                        type: 'tpl',
                        tpl: '${name}',
                        label: '卡片名称',
                      },
                      {
                        type: 'tpl',
                        tpl: '已发布',
                        label: '状态',

                      },
                      {
                        type: 'tpl',
                        tpl: '${creator}',
                        label: '创建人',
                      },
                      {
                        type: 'input-datetime',
                        name: 'createTime',
                        label: '创建时间',
                        valueFormat: 'x',
                        static: true,
                      },{
                        type: 'tpl',
                        tpl: '${updater}',
                        label: '最近编辑人',
                      },
                      {
                        type: 'input-datetime',
                        name: 'updateTime',
                        label: '最近编辑时间',
                        valueFormat: 'x',
                        static: true,
                      },
                      {
                        type: 'operation',
                        label: '操作',
                        buttons: [
                          {
                            label: '查看',
                            type: 'button',
                            actionType: 'dialog',
                            level: 'link',
                            // link: `/app${appId}/admin/` + 'page${id}',
                            dialog: {
                              title: '数据卡片预览',
                              size: 'lg',
                              body: [
                                {
                                  type: 'editor',
                                  value: '${pageData|bodyParse}',
                                  name: 'cardBody',
                                  hidden: true,
                                },
                                {
                                  type: 'amis',
                                  name: 'cardBody'
                                }
                              ]
                            }
                          },
                          {
                            label: '编辑',
                            type: 'button',
                            // actionType: 'drawer',
                            level: 'link',
                            editorSetting: {
                              behavior: 'update'
                            },
                            actionType: 'link',
                            link: `/app${appId}/design/` + 'page${id}?editCode=${id}',
                            // onClick: (context: any) => {
                            //   console.log(context);
                            //   // console.log(props.pageData);
                            //   // props.history.push(
                            //   //   `/app${props.computedMatch.params.appId}/design/page${pageId}?editCode=${pageId}`
                            //   // );
                            // },
                            id: 'u:4d423864d6c0',
                            // drawer: {
                            //   type: 'drawer',
                            //   title: '编辑',
                            //   position: "right",
                            //   body: [
                                
                            //   ],
                            //   actionType: 'drawer',
                            //   actions: [
                            //     {
                            //       type: 'button',
                            //       actionType: 'cancel',
                            //       label: '取消',
                            //       id: 'u:2e86c554e48e'
                            //     },
                            //     {
                            //       type: 'button',
                            //       actionType: 'confirm',
                            //       label: '确定',
                            //       primary: true,
                            //       id: 'u:392213d59e8d'
                            //     }
                            //   ],
                            //   showCloseButton: true,
                            //   closeOnOutside: false,
                            //   closeOnEsc: false,
                            //   showErrorMsg: true,
                            //   showLoading: true,
                            //   draggable: false,
                            //   size: 'lg',
                            //   resizable: false,
                            //   editorSetting: {
                            //     displayName: '编辑'
                            //   }
                            // }
                          },
                         
                          {
                            type: 'button',
                            label: '删除',
                            actionType: 'dialog',
                            level: 'link',
                            className: 'text-danger',
                            dialog: {
                              type: 'dialog',
                              title: '',
                              className: 'py-2',
                              actions: [
                                {
                                  type: 'action',
                                  actionType: 'cancel',
                                  label: '取消'
                                },
                                {
                                  type: 'action',
                                  actionType: 'submit',
                                  label: '删除',
                                  level: 'danger'
                                }
                              ],
                              body: [
                                {
                                  type: 'form',
                                  wrapWithPanel: false,
                                  api: {
                                    method: 'delete',
                                    url: '/admin-api/system/application-page-and-class/delete?id=${id}&type=1&operateType=2',
                                  },
                                  body: [
                                    {
                                      type: 'tpl',
                                      className: 'py-2',
                                      tpl: '确认删除选中项？'
                                    }
                                  ],
                                  feat: 'Insert',
                                  dsType: 'api',
                                  labelAlign: 'left'
                                }
                              ],
                              actionType: 'dialog',
                              showCloseButton: true,
                              closeOnOutside: false,
                              closeOnEsc: false,
                              showErrorMsg: true,
                              showLoading: true,
                              draggable: false,
                              editorSetting: {
                                displayName: '删除'
                              }
                            }
                          }
                        ]
                      }
                    ],
                    headerToolbar: [
                      {
                        type: 'button',
                        label: '新建数据卡片',
                        level: 'primary',
                        icon: 'fa fa-add',
                        className: 'mr-2',
                        actionType: 'dialog',
                        feat: 'Insert',
                        reload: true,
                        dialog: {
                          title: '创建数据卡片',
                          body: [
                            {
                              type: 'form',
                              api: {
                                method: 'post',
                                url: '/admin-api/system/application-page-and-class/create',
                                data: {
                                  applicationId: appId,
                                  name: '${name}',
                                  applicantOrBackend: 1,
                                  pageType: 16,
                                  type: 1,                
                                },
                                adaptor: function (payload: any, response: any, api: any, context: any) {
                                  initCustompageData(response.data.data.id, context);
                                  return payload;
                                } 
                              },
                              body: [
                                {
                                  type: 'input-text',
                                  name: 'name',
                                  label: '数据卡片名称',
                                }
                              ]
                            }
                          ]
                        }
                      },
                      {
                        type: 'reload',
                      }
                    ],
                    footerToolbar: [
                      'statistics',
                      'switch-per-page',
                      'pagination'
                    ]
                  
                  }
                ]
              }}
               embedMode={true}
             />
              )}
                    
            </div>
          </Tab>
          <Tab title="数据页设置" eventKey="dataset">
          {/* <PagePanDataSettings
              pageData={pageData}
              history={props.history}
              store={props.store}
              update={() => {
                props.updatePage();
                handleGetFormDataPage();
              }}
            /> */}
          </Tab>
          <Tab title="页面设置" eventKey="setting">
            {/* <PagePanSettings
              pageData={props.pageData}
              history={props.history}
              store={props.store}
              update={() => {
                props.updatePage();
              }}
            /> */}
          </Tab>
          <Tab title="页面发布" eventKey="publish"></Tab>
        </Tabs>
      </div>


      {/* 创建文件夹模态框 */}
      <Modal
        show={showFolderModal}
        onHide={() => setShowFolderModal(false)}
        title="新建文件夹"
      >
        <div className="p-4">
          <div className="form-group mb-4">
            <label className="mb-2">文件夹名称</label>
            <input 
              type="text" 
              className="form-control"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="请输入文件夹名称"
            />
          </div>
          <div className="d-flex justify-content-end">
            <Button level="default" className="mr-2" onClick={() => setShowFolderModal(false)}>
              取消
            </Button>
            <Button level="primary" onClick={() => {}}>
              确定
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default PageContent;
