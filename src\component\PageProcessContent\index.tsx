import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, <PERSON><PERSON>, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {findCreateBody} from '@/utils/schemaDataSet/commonEditor';
import {createReportObj} from '@/utils/schemaPageTemplate/createPageObjs';
import {ApprovalDialog} from '@/component/ApprovalDialog';

import {
  amisCodeUpdate,
  amisCodeGet,
  wflowApproveCodeGet,
  wflowApproveCodeUpdate,
  getFormDataPage,
  startProcess,
  getModelById,
  saveProcess, //更新流程
  deployProcess
} from '@/utils/api/api';

// 组件：页面设置
import PageSettings from '@/component/PageSettings/index';
// 组件：数据页设置
import PageDataSettings from '@/component/PageDataSettings/index';
import {update} from 'lodash';
import {width} from '@/editor/EChartsEditor/Common';
import cityCodeUtil from '@/utils/cityCodeUtil/cityCodeUtil';
const PageContent: FC<any> = (props: any) => {
  /* data 数据 */
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');
  // wflow_code
  const wflowId = React.useRef<any>('');
  const [pageData, setPageData] = React.useState<any>({});
  const forms = React.useRef<any>([]);
  const fields = React.useRef<any>([]);
  const form = React.useRef<any>(null);
  const [showApprovalDialog, setShowApprovalDialog] = React.useState(false);
  const [formDataToSubmit, setFormDataToSubmit] = React.useState<any>(null);
  const [refreshListKey, setRefreshListKey] = React.useState(0);
  React.useEffect(() => {
    window.addEventListener('cityReplaceNeeded', () => {
      setTimeout(() => {
        // 页面渲染后替换城市码为名称
        document.querySelectorAll('.amis-city-code').forEach(el => {
          const code = el.getAttribute('data-code');
          if (code) {
            try {
              const cityName = cityCodeUtil.getFullAddressByCode(code);
              el.textContent = cityName;
            } catch (e) {
              el.textContent = code;
            }
          }
        });
      }, 3000);
    });

    if (props.pageData?.id) {
      handleGetFormDataPage();
    }
    // 只有在admin模式下才处理tab切换逻辑
    if (props.match.params.playType == 'admin') {
      const urlParams = new URLSearchParams(props.history.location.search);
      let tabKey = urlParams.get('activeTabKey') || 'preview';
      setActiveKey(tabKey);
    }
  }, [props.pageData?.id, props.history.location, props.match.params.playType]);

  // 获取表单数据 pageData
  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: props.pageData.id,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            // toast.success('获取表单数据成功');
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            res.data.list[0].appId = props.computedMatch.params.appId;
            forms.current = findCreateBody(res.data.list[0].data);
            if (res.data.list[0].field) {
              fields.current = JSON.parse(res.data.list[0].field).map(
                (item: any) => ({
                  ...item,
                  name: item.label
                })
              );
            }
            setPageData(res.data.list[0]);
            getProcessData();
          } else {
            toast.success('暂无表单数据');
          }
        } else {
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  // 加载表单信息
  const loadFormInfo = async (wflowCode: string) => {
    getModelById(wflowCode)
      .then((rsp: any) => {
        console.log('获取流程模型成功', rsp);
        form.current = rsp;
      })
      .catch((err: any) => {
        console.error('获取流程模型失败:', err);
      });
  };

  const createCustompageObj = (bodyContent: any) => {
    // 确保 bodyContent 是数组
    const bodyArray = Array.isArray(bodyContent) ? bodyContent : [bodyContent];

    return {
      type: 'form',
      title: '',
      regions: ['body'],
      // body: [
      //   {
      // type: 'wrapper',
      body: bodyArray,
      //   }
      // ],
      actions: [
        {
          type: 'button',
          label: '取消',
          actionType: 'close'
        },
        {
          type: 'button',
          label: '确定',
          level: 'primary',
          actionType: 'submit',
          close: true
        }
      ],
      onEvent: {
        submitSucc: {
          actions: [
            {
              actionType: 'closeDialog'
            }
            // {
            //   "actionType": "wait",
            //   "args": {
            //     "time": 5000
            //   }
            // },
            // {
            //   actionType: 'reload',
            //   componentId: 'u:f1f24e58104f'
            // }
          ]
        }
      },
      onSubmit: async (values: any, event: any) => {
        console.log(event);
        // 在提交时捕获表单值
        // 将 name 转换为 id 作为 key
        const convertedData: Record<string, any> = {};

        // 扫描所有表单控件，获取 id 和 name 的映射关系
        const nameToIdMap: Record<string, string> = {};
        scanFormItems(forms.current, nameToIdMap);

        // 使用映射转换数据结构
        Object.keys(values).forEach(key => {
          if (nameToIdMap[key]) {
            // 使用 id 作为新的键
            convertedData[nameToIdMap[key]] = values[key];
          } else {
            // 如果没有找到映射，保留原键
            convertedData[key] = values[key];
          }
        });

        try {
          console.log('convertedData', convertedData);
          setFormDataToSubmit(convertedData);
          setShowApprovalDialog(true);
          return true; // 返回 true 表示提交成功
        } catch (err) {
          console.error(err);
          return false; // 返回 false 表示提交失败
        }
      }
    };
  };

  // 添加新方法，扫描表单项目，建立 name 到 id 的映射
  const scanFormItems = (items: any, nameToIdMap: any) => {
    if (!items || !Array.isArray(items)) return;

    items.forEach(item => {
      // 如果有 name 和 id 属性，建立映射
      if (item.name && item.id) {
        nameToIdMap[item.name] = item.id;
      }

      // 递归处理嵌套项目
      if (item.body) {
        scanFormItems(item.body, nameToIdMap);
      }
      if (item.columns) {
        scanFormItems(item.columns, nameToIdMap);
      }
      if (item.tabs) {
        item.tabs.forEach((tab: any) => {
          if (tab.body) {
            scanFormItems(tab.body, nameToIdMap);
          }
        });
      }
    });
  };

  const handleSubmit = (formDataS: any, processUsers: any) => {
    console.log('formDataS', formDataS);
    // 遍历所有键，查找地址字段
    Object.keys(formDataS).forEach(key => {
      if (key.endsWith('_city')) {
        const baseKey = key.replace('_city', '');
        const detailKey = baseKey + '_detail';

        // 如果存在对应的详细地址
        if (formDataS[detailKey]) {
          // 创建合并的地址字段
          formDataS[baseKey] = formDataS[key] + '|' + formDataS[detailKey];

          // 删除原始字段
          delete formDataS[key];
          delete formDataS[detailKey];
        }
      }
    });
    console.log('formDataS', formDataS);
    let startParams = {
      applicationPageId: props.pageData.id,
      tenantId: props.store.tenant_id,
      deptId: props.store.userInfo?.dept.id,
      formData: formDataS,
      processUsers: processUsers
    };

    if (form.current.processDefId) {
      startProcess(form.current.processDefId, startParams)
        .then((rsp: any) => {
          // setShowApprovalDialog(true);
          console.log(rsp);
          setShowApprovalDialog(false);
          setFormDataToSubmit(null);
          setRefreshListKey(prev => prev + 1);
        })
        .catch((err: any) => {
          console.error(err);
        });
    } else {
      console.error('processDefId is not defined');
    }
  };

  const formResult = fields.current;

  const formColumns = formResult
    .filter((item: any) => item.name != null && item.name.trim() !== '')
    .map((item: any) => {
      if (item.name === '开关') {
        return {
          name: item.name,
          label: item.name,
          falseValue: 'false',
          trueValue: 'true',
          type: 'switch',
          static: true
        };
      }

      if (item.name === '图片上传') {
        return {
          name: item.name,
          label: item.name,
          type: 'image',
          enlargeAble: true
        };
      }

      if (item.name === '文件上传') {
        return {
          name: item.name,
          label: item.name,
          type: 'tpl',
          tpl: `
            <% if (data['文件上传']) { 
                 const fileUrl = data['文件上传'];
                 const fileName = fileUrl.split('/').pop();
                 const fileExt = fileName.split('.').pop().toLowerCase();
            %>
           
                <a href="<%= fileUrl %>" target="_blank" style="color: blue; text-decoration: underline;">
                   <%= fileName %>
                </a>
            <% } %>
          `
        };
      }

      if (item.name === 'Excel') {
        return {
          name: item.name,
          label: item.name,
          type: 'tpl',
          tpl: `
            <% if (Array.isArray(data.Excel) && data.Excel.length > 0) { %>
              <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                  <tr style="background: #f5f5f5;">
                    <% for (let key in data.Excel[0]) { %>
                      <th style="border: 1px solid #ddd; padding: 8px;"><%= key %></th>
                    <% } %>
                  </tr>
                </thead>
                <tbody>
                  <% for (let i = 0; i < data.Excel.length; i++) { %>
                    <tr>
                      <% for (let key in data.Excel[i]) { %>
                        <td style="border: 1px solid #ddd; padding: 8px;"><%= data.Excel[i][key] %></td>
                      <% } %>
                    </tr>
                  <% } %>
                </tbody>
              </table>
            <% } else { %>
            <% } %>
          `
        };
      }

      if (item.name === '城市选择') {
        return {
          name: item.name,
          label: item.name,
          type: 'input-city',
          static: true
        };
      }

      if (item.name === '地址') {
        return {
          name: item.name,
          label: item.name,
          type: 'tpl',
          tpl: `
            <% if (data['${item.name}']) { 
            const addressParts = data['${item.name}'].split('|');
                 let cityCode='';
                 let detailAddress='';
                 if(addressParts.length>=1){
                 cityCode = addressParts[0];
                 }
                 if(addressParts.length==2){
                detailAddress  = addressParts[1] || '';
                 }
                if(addressParts.length>2){
                detailAddress = addressParts.slice(1).join('');
                }
            %>
              <div>
                <span class="amis-city-code" data-code="<%= cityCode %>"><%= cityCode %></span>
                <% if (detailAddress) { %>
                  <span class="address-separator"> </span>
                  <span class="detail-address"><%= detailAddress %></span>
                <% } %>
              </div>
            <% } else { %>
              --
            <% } %>
          `
        };
      }

      if (item.name === '富文本') {
        return {
          name: item.name,
          label: item.name,
          type: 'tpl'
        };
      }

      if (item.name === '选择图标') {
        return {
          type: 'tpl',
          label: item.name,
          name: item.name
        };
      }

      if (item.name === '人员单选') {
        return {
          type: 'user-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }

      if (item.name === '人员多选') {
        return {
          type: 'user-multi-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }

      if (item.name === '角色单选') {
        return {
          type: 'role-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }

      if (item.name === '角色多选') {
        return {
          type: 'role-multi-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }

      if (item.name === '部门单选') {
        return {
          type: 'department-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }

      if (item.name === '部门多选') {
        return {
          type: 'department-multi-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }


      if (item.name === '应用图标') {
        return {
          type: 'input-appicon',
          label: item.name,
          name: item.name,
          static: true,
          width: item.width,
          height: item.height,
          borderRadius: item.borderRadius
        };
      }
      
      if (item.name === '岗位单选') {
        return {
          type: 'pos-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }

      if (item.name === '岗位多选') {
        return {
          type: 'pos-multi-select',
          label: item.name,
          name: item.name,
          static: true
        };
      }

      if (item.name === '上传头像') {
        return {
          type: 'input-avatar',
          label: item.name,
          name: item.name,
          width: item.width,
          height: item.height,
          borderRadius: item.borderRadius,
          static: true
        };
      }

      if (item.name === '位置选择') {
        return {
          name: item.name,
          label: item.name,
          type: 'text'
        };
      }

      if (item.name === '签名') {
        return {
          name: item.name,
          label: item.name,
          type: 'image',
          enlargeAble: true
        };
      }

      return {
        name: item.name,
        label: item.name,
        type: 'text'
      };
    });

  const columns = [
    {
      name: 'instanceName',
      label: '审批类型',
      type: 'text'
    },
    {
      name: 'staterUser.name',
      label: '发起人',
      type: 'text' // 用于导出
    },

    {
      name: 'startTime',
      label: '提交时间',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      name: 'finishTime',
      label: '结束时间',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      name: 'taskName',
      label: '当前节点',
      type: 'text'
    },
    {
      name: 'status',
      label: '审批状态',
      type: 'text'
    },

    {
      name: '',
      label: '已耗时',
      type: 'tpl',
      tpl: `
        <% 
          var duration;
          if (data.taskEndTime) {
          // 我已处理的情况
          duration = data.taskEndTime - data.createTime;
          } else {
          // 其他情况
          duration = data.finishTime ? (data.finishTime - data.startTime) : (Math.floor(new Date().getTime()/1000) - data.startTime);
          }
          var days = Math.floor(duration / (24 * 60 * 60));
          var hours = Math.floor((duration % (24 * 60 * 60)) / (60 * 60));
          var minutes = Math.floor((duration % (60 * 60)) / 60);
          print(days + '天 ' + hours + '小时 ' + minutes + '分钟');
          %>
      `
    }
  ];

  const getDataSetCurd = () => {
    const dataSetCurd = {
      id: 'u:6be5133b77f5',
      type: 'page',
      title: '数据管理',
      regions: ['body'],
      pullRefresh: {disabled: true},
      body: [
        {
          type: 'crud',
          syncLocation: false,
          loadDataOnce: false,
          translate: true,
          enableRegionIntercept: true, // 启用区域拦截
          api: {
            method: 'get',
            url: `/admin-api/wflow/process/mySubmitted-zerocode?tenantId=${props.store.tenant_id}&pageNo=1&pageSize=10&applicationPageId=${props.pageData.id}`,
            messages: {},
            requestAdaptor: '',
            adaptor: `
  const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000); // 将秒级时间戳转换为毫秒级
    return date.getFullYear() + '-' + 
           String(date.getMonth() + 1).padStart(2, '0') + '-' +
           String(date.getDate()).padStart(2, '0') + ' ' +
           String(date.getHours()).padStart(2, '0') + ':' +
           String(date.getMinutes()).padStart(2, '0') + ':' +
           String(date.getSeconds()).padStart(2, '0');
  };

  if (payload.records) {
    payload.records = payload.records.map(record => {
      const newRecord = {
        ...record,
        startTime: record.startTime / 1000,
        finishTime: record.finishTime / 1000
      };

      if (record.formAbstracts && Array.isArray(record.formAbstracts)) {
        newRecord.formAbstracts = record.formAbstracts.map(abstract => {
          let newAbstract = {...abstract};
          if (abstract.type && abstract.type.startsWith('icon_') && typeof abstract.value === 'string') {
            let cleanedValue = abstract.value;
            if (cleanedValue.startsWith("'") && cleanedValue.endsWith("'")) {
              cleanedValue = cleanedValue.slice(1, -1);
            }
            newAbstract.value = cleanedValue;
          } else {
              newAbstract.value = abstract.value;
          }
          // 处理日期类型
          if (abstract.type && abstract.type.startsWith('date_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }

          if (abstract.type && abstract.type.startsWith('year_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          if (abstract.type && abstract.type.startsWith('time_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          if (abstract.type && abstract.type.startsWith('month_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          if (abstract.type && abstract.type.startsWith('datetime_')&&abstract.value) {
            newAbstract.value = formatDate(parseInt(abstract.value));
          }
          // 处理日期范围类型
          if (abstract.type && abstract.type.startsWith('range_')&&abstract.value) {
            const [start, end] = abstract.value.split(',');
            if (start && end) {
              newAbstract.value = \`\${formatDate(parseInt(start))},\${formatDate(parseInt(end))}\`;
            }
          }

          // 使用字段 NAME 作为 key
          newRecord[\`\${abstract.name}\`] = newAbstract.value;
          return newAbstract;
        });
      }
      return newRecord;
    });
  }

  const event = new CustomEvent('cityReplaceNeeded',{});
window.dispatchEvent(event);
  return payload;
`
          },
          // 配置行点击事件
          itemAction: {
            actionType: 'drawer', // 打开抽屉
            data: {
              instanceId: '${instanceId}',
              nodeId: '${nodeId}',
              formAbstracts: '${formAbstracts | json}'
            },
            drawer: {
              resizable: true,
              showCloseButton: true,
              closeOnOutside: true,
              position: 'right', // 从右侧打开
              title: '审批详情',
              style: {
                width: 'auto', // 或者你可以设置为 'fit-content'
                maxWidth: '90vw', // 防止内容太大撑满屏幕
                zIndex: 9999 // 增加 z-index，确保抽屉内容位于顶部
              },
              body: {
                type: 'panel',
                body: [
                  {
                    type: 'iframe',
                    src: `${
                      process.env.NODE_ENV === 'development'
                        ? 'http://localhost:88/#/workspace/ProcessInstancePreview'
                        : 'https://javaddm.fjpipixia.com/wflow/#/workspace/ProcessInstancePreview'
                    }?instanceId=\${instanceId}&nodeId=\${nodeId}&userID=${
                      props.store.userInfo?.id
                    }&tenantId=${props.store.tenant_id}&authorization=${
                      props.store.access_token
                    }`, // 可以传递参数到外部链接
                    height: '100vh', // 计算视口高度减去头部和其他元素的高度
                    frameBorder: 0,
                    style: {
                      width: '100%',
                      border: 'none',
                      overflow: 'hidden',
                      display: 'block'
                    },
                    allowFullscreen: true
                  }
                ]
              },
              actions: []
            }
          },
          autoGenerateFilter: {
            columnsNum: 2,
            showBtnToolbar: false
          },
          filter: {
            title: '',
            body: [
              // {
              //   "type": "select",
              //   "name": "code",
              //   "placeholder": "筛选流程",
              //   "options": [
              //     {label: '状态-全部', value: ''},
              //     {label: '状态-进行中', value: false},
              //     {label: '状态-已结束', value: true}
              //   ]
              // },
              {
                type: 'input-datetime-range',
                name: 'startTimes',
                format: 'YYYY-MM-DD HH:mm:ss',
                placeholder: '发起开始时间'
              },
              {
                type: 'select',
                name: 'finished',
                placeholder: '审批状态',
                value: '',
                options: [
                  {label: '状态-全部', value: ''},
                  {label: '状态-进行中', value: false},
                  {label: '状态-已结束', value: true}
                ]
              },
              {
                type: 'input-text',
                name: 'keyword',
                size: 'md',
                clearable: true,
                placeholder: '搜索关键字(发起人、流程名称)'
              }
            ],
            actions: [
              // 添加操作按钮区域
              {
                type: 'submit', // 查询按钮
                label: '搜索',
                level: 'primary', // 按钮样式
                size: 'md' // 按钮大小
              },
              {
                type: 'reset', // 重置按钮
                label: '重置',
                level: 'default', // 按钮样式
                size: 'md' // 按钮大小
              }
            ]
          },
          id: 'u:f1f24e58104f',
          perPageAvailable: [5, 10, 20, 50, 100],
          bulkActions: [],
          // "itemActions": [
          //   {
          //     "label": "按钮",
          //     "type": "button",
          //     "id": "u:932bece73200"
          //   },
          //   {
          //     "type": "button",
          //     "label": "删除",
          //     "actionType": "ajax",
          //     "api": {
          //       "method": "post",
          //       "url": "/amis/api/xxxx/$id",
          //       "data": {
          //         "&": "$$",
          //         "op": "delete"
          //       }
          //     },
          //     "confirmText": "确定要删除？"
          //   }
          // ],
          filterTogglable: true,
          headerToolbar: [
            {
              type: 'columns-toggler',
              draggable: true
            },
            'reload',
            [
              {
                label: '新增',
                type: 'button',
                actionType: 'drawer',
                level: 'primary',
                editorSetting: {
                  behavior: 'create'
                },
                id: 'u:215cf1bb50d5',
                drawer: {
                  type: 'drawer',
                  position: 'right',
                  title: '新增',
                  body: [
                    {
                      type: 'form',
                      title: '新增',
                      api: {
                        method: 'post',
                        url: `/admin-api/system/form-field-value/create/${props.pageData.id}`
                      },
                      body:
                        createCustompageObj(findCreateBody(pageData.data)) ||
                        [],
                      id: 'u:6dc50e8d3d85',
                      actions: [],
                      feat: 'Insert',
                      close: true
                    }
                  ],
                  actionType: 'drawer',
                  id: 'u:360e7093b381',
                  actions: [
                    // {
                    //   type: 'button',
                    //   actionType: 'cancel',
                    //   label: '取消',
                    //   id: 'u:b43c0a83e65a'
                    // },
                    // {
                    //   type: 'button',
                    //   label: '确定',
                    //   level: 'primary',
                    //   actionType: 'submit'
                    // }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '新增'
                  }
                }
              }
            ],
            // 'load-more',
            {
              type: 'export-csv',
              tpl: '内容',
              wrapperComponent: '',
              id: 'u:30129f3c0998'
            },
            {
              type: 'export-excel',
              tpl: '内容',
              wrapperComponent: '',
              id: 'u:6a63d52666a1',
              disabledOnAction: false
            },
            'export-excel-template',
            'bulkActions',
            'statistics'
          ],
          syncResponse2Query: false,
          pageField: 'pageNo',
          orderField: 'id',
          perPageField: 'pageSize',
          columns: [...columns, ...formColumns],
          // columns: columns,
          alwaysShowPagination: true,
          footerToolbar: [
            {
              type: 'statistics'
            },
            {
              type: 'switch-per-page',
              tpl: '切换页码',
              wrapperComponent: '',
              id: 'u:bda5e402486b'
            },
            {
              type: 'pagination',
              tpl: '分页',
              wrapperComponent: '',
              id: 'u:b2ec821b8884',
              __editorStatebaseControlClassName: 'default'
            }
          ]
        }
      ],
      asideResizor: false
    };
    return dataSetCurd;
  };

  /* data 数据 end */

  /* methods 方法 */
  // 表单预览的schema
  const formPreviewSchema = (schema: any) => {
    let data = {
      type: 'page',
      regions: ['body'],
      pullRefresh: {
        disabled: true
      },
      body: findCreateBody(schema)
    };
    return data;
  };
  //  表单编辑的schema
  const editForm = () => {
    props.history.push(
      `/app${props.computedMatch.params.appId}/design/${props.computedMatch.params.form}?editCode=${props.pageData.id}`
    );
  };

  //   编辑流程
  const editProess = async () => {
    let json = await amisCodeGet({applicationPageId: props.pageData.id});
    console.log('json', json);

    let jsonStr = formPreviewSchema(pageData.data);
    let data = {
      id: json.data.id,
      applicationPageId: props.pageData.id,
      jsonStr: JSON.stringify(jsonStr)
    };
    amisCodeUpdate(data).then((res: any) => {
      if (res.code == 0) {
        let a = document.createElement('a');
        a.href = `${
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:88/#/admin/design'
            : 'https://javaddm.fjpipixia.com/wflow/#/admin/design'
        }?pageID=${props.pageData.id}&pageName=${props.pageData.name}&code=${
          wflowId.current
        }&applyID=${props.pageData.applicationId}&userID=${
          props.store.userInfo?.id
        }&tenantId=${props.store.tenant_id}&authorization=${
          props.store.access_token
        }`;
        window.open(a.href, '_blank');
      }
    });
  };
  // 获取审批流程信息
  const getProcessData = () => {
    console.log('getProcessData', pageData);
    let data = {
      applicationPageId: props.pageData.id
    };
    wflowApproveCodeGet(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data) {
          // setWflow(res.data);
          if (res.data.wflowProcessId) {
            wflowId.current = res.data.wflowProcessId;
          }
          // if (res.data.wflowProcessId) {
          doSave(res.data, res.data.wflowProcessId, res.data.id);

          // }
        }
      }
    });
  };

  const doSave = (data: any, wflowProcessId: string, id: string) => {
    let processJson = {
      applicationId: props.pageData.applicationId,
      formId: null,
      formName: props.pageData.name,
      applicationPageId: props.pageData.id,
      tenantId: props.store.tenant_id,
      logo: JSON.stringify({
        icon: 'bi:people-fill',
        background: '#1e90ff'
      }),
      settings: JSON.stringify({
        commiter: [],
        admin: [],
        sign: false,
        enableCancel: true,
        reExecute: false,
        sameSkip: 'NONE',
        recallSkip: true,
        notify: {
          types: ['APP'],
          title: '消息通知标题'
        }
      }),
      groupId: 104,
      formItems: [],
      formConfig: JSON.stringify({
        labelPos: 'top',
        ruleType: 'SIMPLE',
        labelWidth: '',
        rules: [],
        ruleJs:
          '//formData: 表单数据  formMap: 表单字段id -> 字段json配置\r\nfunction doChange(formData, formMap){\r\n\t\r\n}'
      }),
      processConfig: JSON.stringify({
        listener: {
          start: [],
          pass: [],
          refuse: [],
          cancel: []
        }
      }),
      process: JSON.stringify({
        id: 'root',
        parentId: null,
        type: 'ROOT',
        name: '发起人',
        desc: '任何人',
        props: {
          assignedUser: [],
          formPerms: [],
          listeners: {},
          operationPerm: {
            complete: {alisa: '提交', show: true}
          }
        },
        children: {}
      }),
      remark: '备注说明'
    };

    if (!data.settings) {
      data.settings = JSON.stringify(processJson);
    }
    const settings = JSON.parse(data.settings);
    settings.formItems = JSON.stringify(forms.current);
    settings.formId = wflowProcessId;
    saveProcess(settings, false).then((res: any) => {
      if (!wflowProcessId) {
        wflowId.current = res;
        wflowProcessId = res;
      }
      wflowApproveCodeUpdate({
        applicationPageId: props.pageData.id,
        id: id,
        wflowProcessId: wflowProcessId,
        settings: data.settings,
        jsonStr: processJson.process
      });
      deployProcess(wflowProcessId, false).then((res: any) => {
        loadFormInfo(wflowProcessId);
      });
    });
  };

  const onVisit = () => {
    const url = `#/app${props.computedMatch.params.appId}/processSubmission/${props.computedMatch.params.form}?editCode=${props.pageData.id}&wflowCode=${wflowId.current}&pageName=${props.pageData.name}`;
    const baseUrl = window.location.origin + window.location.pathname;
    window.open(baseUrl + url, '_blank');
  };

  /* methods 方法 end */

  // 在组件加载时添加样式
  React.useEffect(() => {
    // 创建唯一的 style id
    const styleId = 'pageProcess-custom-styles';

    // 如果已存在则移除
    const existingStyle = document.getElementById(styleId);
    if (existingStyle) {
      existingStyle.remove();
    }

    // 创建新的 style 标签
    const styleElement = document.createElement('style');
    styleElement.id = styleId;
    styleElement.textContent = `
  // .cxd-Wrapper, .cxd-Container,
  // .dark-Wrapper,.dark-Container{
  //   height: calc(100vh - 10rem);
  // }
    `;

    // 添加到 head
    document.head.appendChild(styleElement);

    // 清理函数
    return () => {
      const style = document.getElementById(styleId);
      if (style) {
        style.remove();
      }
    };
  }, []);
  /* created 初始化 */

  // React.useEffect(() => {
  //   if (wflowData) {
  //     let list: any = formatProcessNodes(wflowData);
  //     setProcessNodesList(list);
  //   }
  // }, [wflowData]);
  /* created 初始化 end */

  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageProcessTabs-tabsToolbar"></div>;
  };

  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);

    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);

    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }

    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }

    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  return (
    <div className="pageBox">
      {props.match.params.playType == 'admin' && (
        <div className="pageTop">
          <div className="pageTop-title">{props.pageData.name}</div>
          <div className="pageTop-right">
            <div className="pl-6">
              {/* {wflow && wflow.wflowProcessId && ( */}
              <Button
                size="lg"
                level="default"
                onClick={() => {
                  onVisit();
                }}
              >
                访问
              </Button>
              {/* )} */}
            </div>
            <div className="pl-1">
              <Button size="lg" level="default">
                生成数据页
              </Button>
            </div>
            <div className="pl-1">
              <Button size="lg" level="default" onClick={() => editProess()}>
                编辑流程
              </Button>
            </div>
            <div className="pl-1">
              <Button size="lg" level="primary" onClick={() => editForm()}>
                编辑表单
              </Button>
            </div>
          </div>
        </div>
      )}
      <div className="pageProcessTabs">
        {props.match.params.playType == 'admin' ? (
          // admin模式：显示完整的tab标签栏
          <Tabs
            theme={props.store.theme}
            mode="line"
            onSelect={(key: any) => handleTabChange(key)}
            toolbar={TabsToolbar()}
            linksClassName="pageProcessTabs-tabsTitle"
            activeKey={activeKey}
          >
            <Tab title="流程表单预览" eventKey="preview">
              <div className="pageProcessTabs-tabsContent">
                <div className="pageProcessTabs-tabsContent-form">
                  <AMISRenderer
                    schema={formPreviewSchema(pageData.data)}
                    data={{
                      onVisit
                    }}
                    embedMode={true}
                  />
                </div>
                {/* )} */}
              </div>
            </Tab>
            <Tab title="数据管理" eventKey="manage">
              <div className="pageProcessTabs-tabsContent">
                <AMISRenderer
                  key={refreshListKey}
                  schema={getDataSetCurd()}
                  data={{
                    onVisit
                  }}
                />
              </div>
            </Tab>
            <Tab title="数据页设置" eventKey="dataset">
              <PageDataSettings
                pageData={pageData}
                history={props.history}
                store={props.store}
                update={() => {
                  props.updatePage();
                  handleGetFormDataPage();
                }}
              />
            </Tab>
            <Tab title="页面设置" eventKey="setting">
              <PageSettings
                pageData={props.pageData}
                store={props.store}
                history={props.history}
                update={() => {
                  props.updatePage();
                }}
              />
            </Tab>
            <Tab title="页面发布" eventKey="publish"></Tab>
          </Tabs>
        ) : (
          // 非admin模式：只显示流程表单预览内容，不显示tab标签栏
          <div className="pageProcessTabs-tabsContent">
            <div className="pageProcessTabs-tabsContent-form">
              <AMISRenderer
                schema={formPreviewSchema(pageData.data)}
                data={{
                  onVisit
                }}
                embedMode={true}
              />
            </div>
          </div>
        )}
      </div>
      <ApprovalDialog
        show={showApprovalDialog}
        processData={form.current?.process}
        formData={formDataToSubmit}
        onClose={() => {
          setShowApprovalDialog(false);
          setFormDataToSubmit(null);
        }}
        onConfirm={values => {
          // 合并表单数据和审批人数据
          handleSubmit(formDataToSubmit, values.processUsers);
        }}
      />
    </div>
  );
};

export default PageContent;
