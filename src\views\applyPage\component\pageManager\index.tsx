import React, {useState, useEffect} from 'react';
import {observer} from 'mobx-react';
import {DndProvider} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';
import {toast, Modal, confirm} from 'amis';
import CommonHeader from '@/views/components/CommonHeader';
import './index.scss';

interface PageManagerProps {
  store: any;
  history: any;
  match: any;
  location: any;
  // 从applyPage传递的数据和方法
  appMenuList: any[];
  isCollapsePage: boolean;
  setIsCollapsePage: (value: boolean) => void;
  handleNewPagesType: (type: number) => void;
  setIsInitPageType: (value: boolean) => void;
  setCheckedPageData: (value: any) => void;
  applyData: any;
  // 其他必要的方法
  openEditApplyName: (e: any, name: any) => void;
  saveEditApplyName: () => void;
  openApplyNameInput: boolean;
  editApplyName: string;
}

const PageManager: React.FC<PageManagerProps> = observer((props) => {
  const {
    store,
    history,
    match,
    location,
    appMenuList,
    isCollapsePage,
    setIsCollapsePage,
    handleNewPagesType,
    setIsInitPageType,
    setCheckedPageData,
    applyData,
    openEditApplyName,
    saveEditApplyName,
    openApplyNameInput,
    editApplyName
  } = props;

  // 中间导航配置
  const middleNavItems = [
    {
      key: 'pageManager',
      label: '页面管理',
      active: true,
      onClick: () => {
        history.push(`/app${match.params.appId}/${match.params.playType}/pageManager`);
      }
    },
    {
      key: 'appSetting',
      label: '应用设置',
      active: false,
      onClick: () => {
        history.push(`/app${match.params.appId}/${match.params.playType}/appSetting/basicSetting`);
      }
    },
    {
      key: 'appPublish',
      label: '应用发布',
      active: false,
      onClick: () => {
        history.push(`/app${match.params.appId}/${match.params.playType}/appPublish`);
      }
    },
    {
      key: 'approval',
      label: '审批',
      active: match.params.form === 'approval',
      onClick: () => {
        setCheckedPageData(false);
        setIsInitPageType(false);
        history.push(`/app${match.params.appId}/${match.params.playType}/approval`);
      }
    }
  ];

  // 页面工具栏事件处理
  const handleSearchClick = () => {
    // TODO: 实现搜索功能
    toast.info('搜索功能开发中...');
  };

  const handleAddCategoryClick = () => {
    handleNewPagesType(1);
  };

  const handleAddPageClick = () => {
    setIsInitPageType(true);
    setCheckedPageData(false);
    history.push(`/app${match.params.appId}/${match.params.playType}`);
  };

  const handleToggleCollapse = () => {
    setIsCollapsePage(!isCollapsePage);
  };

  return (
    <div className="page-manager">
      <CommonHeader
        store={store}
        history={history}
        type="app"
        className="page-manager-header"
        buttonText="应用搭建"
        buttonIcon={require('@/image/common_icons/dajian.png')}
        applyData={applyData}
        openApplyNameInput={openApplyNameInput}
        editApplyName={editApplyName}
        match={match}
        onEditApplyName={openEditApplyName}
        onSaveApplyName={saveEditApplyName}
        showMiddleNav={true}
        middleNavItems={middleNavItems}
        showPageTools={true}
        onSearchClick={handleSearchClick}
        onAddCategoryClick={handleAddCategoryClick}
        onAddPageClick={handleAddPageClick}
        onToggleCollapse={handleToggleCollapse}
        isCollapsed={isCollapsePage}
      />
      
      <div className="page-manager-content">
        <div className="page-manager-main">
          {/* 页面列表内容 */}
          <div className={isCollapsePage ? 'displayNone' : 'pagesNav'}>
            <DndProvider backend={HTML5Backend}>
              {appMenuList.length > 0 ? (
                <div className="page-list">
                  {appMenuList.map((item: any, index: any) => (
                    <div key={item.id || index} className="page-item">
                      <div className="page-item-icon">
                        <i className={item.type === 1 ? 'fa fa-file-o' : 'fa fa-folder-o'}></i>
                      </div>
                      <div className="page-item-content">
                        <div className="page-item-name">{item.name}</div>
                        {item.children && item.children.length > 0 && (
                          <div className="page-item-children">
                            {item.children.map((child: any, childIndex: any) => (
                              <div key={child.id || childIndex} className="page-child-item">
                                <i className="fa fa-file-o"></i>
                                <span>{child.name}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                      <div className="page-item-actions">
                        <i className="fa fa-ellipsis-v"></i>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <div className="empty-state-icon">
                    <i className="fa fa-file-o"></i>
                  </div>
                  <div className="empty-state-text">暂无页面</div>
                  <div className="empty-state-action">
                    <button 
                      className="btn btn-primary"
                      onClick={handleAddPageClick}
                    >
                      创建第一个页面
                    </button>
                  </div>
                </div>
              )}
            </DndProvider>
          </div>
        </div>
      </div>
    </div>
  );
});

export default PageManager;
