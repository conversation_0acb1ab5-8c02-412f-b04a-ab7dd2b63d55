import React from 'react';
import {Renderer} from 'amis';
import {FormControlProps} from 'amis-core';
export interface InputAvatarProps extends FormControlProps {
  label?: string;
  name: string;
  clearable?: boolean;
  placeholder?: string;
  value?: string;
  static?: boolean;
  width?: number;
  height?: number;
  accept?: string;
  crop?: boolean;
  uploadBtnText?: string;
  borderRadius?: number | string; // 添加圆角属性
  limit: undefined;
}

@Renderer({
  type: 'input-avatar',
  name: 'input-avatar'
})
export class InputAvatarRenderer extends React.PureComponent<InputAvatarProps> {
  constructor(props: InputAvatarProps) {
    super(props);
  }

  render() {
    const {
      static: isStatic,
      name,
      label,
      placeholder,
      width,
      height,
      accept,
      crop,
      uploadBtnText,
      borderRadius, // 获取圆角属性
      render,
      frameImage
      // ...rest
    } = this.props as any;

    // 构建基本配置
    const config: any = {
      // ...rest,
      type: 'input-image',
      label,
      name,
      frameImage: frameImage || false, // 修改这里，使用传入的frameImage值
      accept: accept || 'image/jpeg,image/jpg,image/png',
      crop: crop !== false,
      placeholder: placeholder || '请选择头像',
      imageClassName: 'r w-full avatar',
      imageWidth: width || 100,
      imageHeight: height || 100,
      thumbMode: 'cover',
      receiver: {
        url: '/app-api/infra/file/upload-zerocode',
        method: 'post'
      },
      // 使用内联样式设置变量，包括圆角
      style: {
        '--avatar-width': `${width || 100}`,
        '--avatar-height': `${height || 100}`,
        '--avatar-border-radius':`${borderRadius || '50%'}`,
          // typeof borderRadius === 'number' ||
          // (!isNaN(Number(borderRadius)) && !String(borderRadius).includes('%'))
          //   ? `${borderRadius}px`
          //   : borderRadius || '50%' // 默认为圆形
      },
      autoUpload: true,
      imageMode: 'cover',
      className: 'avatar-uploader'
    };

    // // 只有当 uploadBtnText 不是空字符串时才添加
    // if (uploadBtnText !== '') {
    //   config.uploadBtnText = uploadBtnText || '';
    // } else {
    //   // 当为空字符串时，设置为一个空格，这样按钮会显示但没有文字
    //   config.uploadBtnText = ' ';
    // }
    if (isStatic) {
      if (render) {
        return render('input-image', config);
      }
    } else {
      const allConfig = {
        ...this.props,
        ...config
      };

      if (render) {
        return render('input-avatar', allConfig);
      }
      //兜底
      return <></>;
    }
  }
}
