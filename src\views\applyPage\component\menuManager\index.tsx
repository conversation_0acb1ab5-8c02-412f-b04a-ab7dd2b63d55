import React, {FC, useState, useEffect} from 'react';
import './index.scss';
import AMISRenderer from '@/component/AMISRenderer';
import {
  getDataSetPage,
  deleteApplicationPageAndClass,
  getApplicationPageAndClassList,
  createFormData,
  getFormDataPage,
  updateFormData
} from '@/utils/api/api';
import {toast} from 'amis-ui';
import {IMainStore} from '@/store';
import {
  createFromObj,
  createCustompageObj,
  createIframeObj,
  createReportObj,
  createDatasetImportObj,
  createPanObj
} from '@/utils/schemaPageTemplate/createPageObjs';

const MenuManager: FC<{
  history: any;
  store: IMainStore;
  applyData: any;
  onMenuRefresh: () => void;
  onCreatePage?: () => void;
}> = (props: any) => {
  const [refreshListKey, setRefreshListKey] = React.useState(0);
  const { applyData } = props;
  
  // 目录列表
  const [categoryList, setCategoryList] = useState<any[]>([]);

  // 添加数据集列表状态
  const [dataSetOptions, setDataSetOptions] = useState([]);

  // 监听自定义事件，用于刷新数据
  useEffect(() => {
    const handleRefresh = () => {
      props.onMenuRefresh();
      console.log('refreshMenuManager 事件已接收!', new Date().toISOString());
      setRefreshListKey(prev => prev + 1); // 更新刷新键，触发数据重新加载
    };

    window.addEventListener('refreshMenuManager', handleRefresh);

    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('refreshMenuManager', handleRefresh);
    };
  }, []);

  // 获取数据集列表和目录列表的函数
  const fetchData = () => {
    // 获取数据集列表
    getDataSetPage({
      applicationId: Number(props.applyData.id),
      applicantOrBackend: 1, // 应用页面使用1
      pageNo: 1,
      pageSize: 100
    }).then((res: any) => {
      if (res.code === 0 && res.data.list) {
        setDataSetOptions(res.data.list);
      } else {
        toast.error('获取数据集列表失败');
      }
    });

    // 修改为保留树状结构的函数
    const filterTypeTwo = (list: any[]): any[] => {
      if (!list || !Array.isArray(list)) return [];

      return list
        .map(item => {
          // 如果不是目录类型，则跳过
          if (item.type !== 2) return null;

          // 复制当前项，保留其所有属性
          const newItem = {...item};

          // 如果有子项，递归处理子项
          if (newItem.children && Array.isArray(newItem.children)) {
            newItem.children = filterTypeTwo(newItem.children).filter(Boolean);
          }

          return newItem;
        })
        .filter(Boolean); // 过滤掉空值
    };

    // 请求目录列表
    getApplicationPageAndClassList({
      applicationId: Number(props.applyData.id),
      applicantOrBackend: 1, // 应用页面使用1
    }).then((res: any) => {
      if (res.code === 0 && res.data) {
        setCategoryList(res.data);
      } else {
        toast.error('获取目录列表失败');
      }
    });
  };

  // 初始化时获取数据
  useEffect(() => {
    fetchData();
  }, [refreshListKey, props.applyData]); // 依赖于refreshListKey和applyData，当它们变化时重新获取数据

  // 删除页面/分类
  const delClassOrPage = (item: any) => {
    let data = {
      id: item.id,
      type: item.type,
      operateType: 2
    };
    deleteApplicationPageAndClass(data).then((res: any) => {
      if (res.code === 0) {
        props.onMenuRefresh();
        setRefreshListKey(prev => prev + 1);
        toast.success('删除成功');
      } else {
        toast.error(res.msg);
      }
    });
  };
  
  // 新建页面处理函数
  const handleCreateNewPage = () => {
    // 调用父组件设置状态并跳转到新建页面流程
    if (props.onCreatePage) {
      props.onCreatePage();
    }
  };

  // 初始化表单数据-创建表单数据
  const initFormData = (pageId: number, pageName: string) => {
    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(createFromObj(pageName, pageId.toString())),
      field: ''
    };
    createFormData(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('初始化数据完成~');
        props.history.push(
          `/app${props.applyData.id}/design/page${pageId}?editCode=${pageId}`
        );
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };

  // 初始化表单数据-创建自定义页面、amis报表和门户
  const initCustompageData = (pageId: number, pageName: string) => {
    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(createCustompageObj(pageName)),
      field: ''
    };
    createFormData(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('初始化数据完成~');
        props.history.push(
          `/app${props.applyData.id}/design/page${pageId}?editCode=${pageId}`
        );
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };

  // 初始化芋道网盘页面
  const initPanpageData = (pageId: number, pageName: string) => {
    let data = {
      applicationPageId: pageId,
      data: window.JSON.stringify(createPanObj(pageName, pageId.toString())),
      field: ''
    };
    createFormData(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('初始化数据完成~');
        props.onMenuRefresh();
      } else {
        toast.error('初始化数据失败 ' + res.msg);
      }
    });
  };

  // 初始化数据源导入页面
  const initDataSetImportData = (pageId: number, info: any) => {
    handleGetFormDataPage(pageId, info);
  };

  // 获取表单数据 pageData
  const handleGetFormDataPage = (pageId: number, info: any) => {
    let data = {
      applicationPageId: pageId,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code === 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            updateFromSchema(pageId, info, res.data.list[0]);
          } else {
            props.onMenuRefresh();
          }
        } else {
          props.onMenuRefresh();
        }
      } else {
        toast.error(res.msg);
        props.onMenuRefresh();
      }
    });
  };

  const updateFromSchema = (pageId: number, info: any, pageData: any) => {
    let field = JSON.parse(pageData.field);
    let schema = window.JSON.stringify(
      createDatasetImportObj(info.name, pageId.toString(), field, info.dataSetId)
    );

    let data = {
      id: pageData.id,
      data: schema,
      field: pageData.field,
      applicationPageId: pageId
    };
    updateFormData(data).then((res: any) => {
      if (res.code === 0) {
        toast.success('初始化数据完成~');
        props.onMenuRefresh();
      } else {
        toast.error(res.msg);
        props.onMenuRefresh();
      }
    });
  };

  const getDataSetCurd = () => {
    const dataSetCurd = {
      id: 'u:6be5133b77f5',
      type: 'page',
      title: '菜单管理',
      regions: ['body'],
      pullRefresh: {disabled: true},
      body: [
        {
          type: 'crud',
          syncLocation: false,
          api: {
            method: 'get',
            url: `/admin-api/system/application-page-and-class/list?applicationId=${props.applyData.id}&applicantOrBackend=1`,
            messages: {},
            requestAdaptor: '',
            adaptor: "console.log('payload', payload);\r\nreturn payload;"
          },
          bulkActions: [],
          itemActions: [],
          id: 'u:f1f24e58104f',
          perPageAvailable: [5, 10, 20, 50, 100],
          messages: {},
          filterSettingSource: [
            'id',
            'name',
            'icon',
            'dataSetName',
            'url',
            'route',
            'sort',
            'status'
          ],
          filterEnabledList: [
            {label: 'name', value: 'name'},
            {label: 'dataSetName', value: 'dataSetName'},
            {label: 'url', value: 'url'},
            {label: 'route', value: 'route'},
            {label: 'sort', value: 'sort'}
          ],
          headerToolbar: [
            {
              label: '新增',
              type: 'button',
              actionType: 'dialog',
              level: 'primary',
              editorSetting: {
                behavior: 'create'
              },
              dialog: {
                type: 'dialog',
                title: '新增',
                body: [
                  {
                    type: 'form',
                    id: 'menu-form',
                    mode: 'horizontal',
                    labelAlign: 'right',
                    api: {
                      method: 'post',
                      url: '/admin-api/system/application-page-and-class/create',
                      // ... existing code ...
                      // ... existing code ...
                      requestAdaptor: `
// 根据 pageType 处理不同字段
if (api.data.pageType === '13') {
  // 数据集类型：保留 dataSetId，删除 url
  api.data.pageType = api.data.dataSetId ? '13' : '';
  delete api.data.url;
} else if (api.data.pageType === '4') {
  // 链接类型：保留 url，删除 dataSetId
  delete api.data.dataSetId;
} else {
  // 空类型：删除 dataSetId 和 url
  delete api.data.dataSetId;
  delete api.data.url;
}

api.data.parentId = api.data.parentId ? api.data.parentId : '';
api.data.icon = api.data.icon?.svg ? api.data.icon.svg.replace(/^'|'$/g, '') : '';
return api;
`,

                      // adaptor: 'return api.data;',
                      adaptor: function (payload: any, response: any, api: any, context: any) {
                        // saveAction(response.data.data);
                        if (payload.code === 0) {
                          //有些页面需要初始化数据
                          const pageId = payload.data.id;
                          const pageName = api.data.name;
                          
                          // 根据不同页面类型初始化数据
                          switch(api.data.pageType) {
                            case '1': // 普通表单
                            case 1:
                            case '2': // 流程表单
                            case 2:
                              setTimeout(() => {
                                initFormData(pageId, pageName);
                              }, 100);
                              break;
                            case '5': // 自定义页面
                            case 5:
                            case '7': // amis报表
                            case 7:
                            case '17': // 门户
                            case 17:
                              setTimeout(() => {
                                initCustompageData(pageId, pageName);
                              }, 100);
                              break;
                            case '13': // 数据源导入
                            case 13:
                              setTimeout(() => {
                                initDataSetImportData(pageId, {
                                  name: pageName,
                                  dataSetId: api.data.dataSetId
                                });
                              }, 100);
                              break;
                            case '14': // 芋道网盘
                            case 14:
                              setTimeout(() => {
                                initPanpageData(pageId, pageName);
                              }, 100);
                              break;
                            default:
                              // 其他类型页面刷新菜单
                              setTimeout(() => {
                                window.dispatchEvent(new CustomEvent('refreshMenuManager'));
                              }, 100);
                          }
                          
                          return {
                            status: 0,
                            msg: '保存成功'
                          };
                        }
                        return {
                          status: 1,
                          msg: payload.msg || '保存失败'
                        };
                      },
                      data: {
                        'applicantOrBackend': 1,
                        'type': 1,
                        'applicationId': props.applyData.id,
                        '&': '$$'
                      }
                    },
                    body: [
                      {
                        type: 'select',
                        name: 'pageType',
                        label: '菜单内容',
                        options: [
                          {
                            label: '新建普通表单',
                            value: '1'
                          },
                          {
                            label: '新建流程表单',
                            value: '2'
                          },
                          {
                            label: '添加外部链接',
                            value: '4'
                          },
                          {
                            label: '新建自定义页面',
                            value: '5'
                          },
                          {
                            label: 'amis报表',
                            value: '7'
                          },
                          {
                            label: '数据源导入',
                            value: '13'
                          },
                          {
                            label: '芋道网盘',
                            value: '14'
                          },
                          {
                            label: '门户',
                            value: '17'
                          },
                          {
                            label: '无',
                            value: ''
                          }
                        ],
                        value: '1',
                        mode: 'horizontal'
                      },
                      // 数据集（仅菜单类型且内容为数据集时显示）
                      {
                        type: 'select',
                        name: 'dataSetId',
                        label: '数据集　',
                        required: true,
                        clearable: true,
                        searchable: true,
                        options: dataSetOptions.map((item: any) => ({
                          label: item.name,
                          value: item.id,
                          name: item.name, // 保留原始字段
                          associatedDataTable: item.associatedDataTable // 加上这个字段
                        })),
                        placeholder: '请选择数据集',
                        visibleOn: "data.pageType === '13'",
                        onEvent: {
                          change: {
                            actions: [
                              {
                                actionType: 'custom',
                                script: `
                                  if (event.data.selectedItems) {
                                    // 直接修改表单数据
                                    doAction({
                                      actionType: 'setValue',
                                      componentId: "menu-form",
                                      args: {
                                        value: {
                                          name: event.data.selectedItems.name || '',
                                          route: event.data.selectedItems.associatedDataTable || ''
                                        }
                                      }
                                    });
                                  }
                                `
                              }
                            ]
                          }
                        }
                      },
                      // 链接（仅菜单类型且内容为链接时显示）
                      {
                        type: 'input-url',
                        name: 'url',
                        required: true,
                        label: '链接　　',
                        placeholder: '请输入链接',
                        visibleOn: "data.pageType === '4'"
                      },
                      // 打开方式（仅菜单类型且内容为链接时显示）
                      // {
                      //   type: 'radios',
                      //   name: 'linkOpenType',
                      //   label: '打开方式',
                      //   options: [
                      //     {
                      //       label: '新窗口打开',
                      //       value: '1'
                      //     },
                      //     {
                      //       label: '本页面打开',
                      //       value: '2'
                      //     }
                      //   ],
                      //   value: '1',
                      //   mode: 'horizontal',
                      //   visibleOn: "data.type === 1 && data.pageType === '4'"
                      // },
                      // 上级菜单（两种类型都需要）
                      {
                        type: 'tree-select',
                        name: 'parentId',
                        label: '上级菜单',
                        searchable: true,
                        options: categoryList,
                        labelField: 'name',
                        valueField: 'id',
                        clearable: true,
                        placeholder: '请选择上级菜单'
                      },
                      // 菜单名称
                      {
                        type: 'input-text',
                        name: 'name',
                        label: '菜单名称',
                        required: true,
                        placeholder: '请输入菜单名称',
                        maxLength: 30,
                        showCounter: true
                      },
                      // 图标（两种类型都需要）
                      {
                        type: 'icon-select',
                        name: 'icon',
                        label: '菜单图标',
                        placeholder: '请选择图标'
                      },
                      // 路由地址（两种类型都需要）
                      {
                        type: 'input-text',
                        name: 'route',
                        label: '路由地址',
                        placeholder: '请输入路由地址',
                        maxLength: 255,
                        showCounter: true,
                        validations: {
                          matchRegexp: '^[^\u4e00-\u9fa5]*$'
                        },
                        validationErrors: {
                          matchRegexp: '不能输入中文字符'
                        }
                      },
                      // 排序（两种类型都需要）
                      {
                        type: 'input-number',
                        name: 'sort',
                        label: '排序　　',
                        placeholder: '请输入排序数值',
                        min: 0,
                        showCounter: true
                      },
                      // 菜单状态（两种类型都需要）
                      {
                        type: 'radios',
                        name: 'status',
                        label: '菜单状态',
                        options: [
                          {
                            label: '显示',
                            value: 1
                          },
                          {
                            label: '隐藏',
                            value: 0
                          }
                        ],
                        value: 1,
                        mode: 'horizontal'
                      }
                    ],
                    actions: [
                      {
                        type: 'button',
                        label: '取消',
                        actionType: 'cancel'
                      },
                      {
                        type: 'submit',
                        label: '确认',
                        level: 'primary'
                      }
                    ]
                  }
                ],
                showCloseButton: true,
                closeOnOutside: false,
                closeOnEsc: false,
                showErrorMsg: true,
                showLoading: true,
                draggable: false,
                resizable: false,
                size: 'md'
              },
              id: 'u:08262cfb8de7',
              disabledOnAction: false
            },
            {
              type: 'export-csv',
              tpl: '内容',
              wrapperComponent: '',
              id: 'u:30129f3c0998'
            },
            {
              type: 'export-excel',
              tpl: '内容',
              wrapperComponent: '',
              id: 'u:6a63d52666a1',
              disabledOnAction: false
            },
            'bulkActions'
          ],
          syncResponse2Query: false,
          filter: {
            title: '查询条件',
            columnCount: 4,
            mode: 'horizontal',
            body: [
              {
                type: 'input-text',
                label: '菜单名称',
                name: 'name',
                id: 'u:abc469cccf0f'
              },
              {
                type: 'input-text',
                label: '数据集',
                name: 'dataSetName',
                id: 'u:12bbf528040a'
              },
              {
                label: '链接',
                type: 'input-text',
                name: 'url',
                id: 'u:bc6bd32e2498'
              },
              {
                type: 'input-text',
                label: '路由链接',
                name: 'route',
                id: 'u:8aaeed28c1af'
              }
            ],
            id: 'u:5055940e851d',
            actions: [
              {
                type: 'reset',
                label: '重置',
                id: 'u:da879e1b84f7'
              },
              {
                type: 'submit',
                label: '搜索',
                primary: true,
                id: 'u:6296d9c78a58'
              }
            ],
            feat: 'Insert',
            labelAlign: 'left'
          },
          pageField: 'pageNo',
          orderField: 'id',
          perPageField: 'pageSize',
          columns: [
            {
              label: 'ID',
              type: 'text',
              className: 'text-md',
              name: 'id',
              id: 'u:70ad4de0d3e9',
              placeholder: '-',
              remark: ''
            },
            {
              label: '菜单名称',
              type: 'text',
              className: 'text-md',
              name: 'name',
              id: 'u:bc6bd32e2494'
            },
            {
              label: '图标',
              type: 'tpl',
              name: 'icon',
              id: 'u:b59187724448',
              className: 'icon-cell'
            },
            {
              label: '数据集',
              type: 'text',
              className: 'text-md',
              name: 'dataSetName',
              id: 'u:bc6bd32e2498'
            },
            {
              label: '链接',
              type: 'text',
              className: 'text-md',
              name: 'url',
              id: 'u:bc6bd32e2499'
            },
            {
              label: '路由链接',
              type: 'text',
              className: 'text-md',
              name: 'route',
              id: 'u:bc6bd32e2495'
            },
            {
              label: '排序',
              type: 'text',
              className: 'text-md',
              name: 'sort',
              id: 'u:7271f7484f90'
            },
            {
              label: '菜单状态',
              name: 'status',
              className: 'text-md',
              type: 'tpl',
              id: 'u:7271f7484f91',
              tpl: "${status === 1 ? '显示' : '隐藏'}",
              static: true
            },
            {
              type: 'operation',
              label: '操作',
              buttons: [
                {
                  label: '查看',
                  type: 'button',
                  actionType: 'drawer',
                  level: 'link',
                  editorSetting: {
                    behavior: 'view'
                  },
                  id: 'u:88e384769ff8',
                  drawer: {
                    type: 'drawer',
                    title: '查看详情',
                    body: [
                      {
                        type: 'form',
                        body: [
                          {
                            name: 'id',
                            label: 'id',
                            type: 'static'
                          },
                          {
                            name: 'name',
                            label: '菜单名称',
                            type: 'static'
                          },
                          {
                            name: 'icon',
                            label: '图标',
                            type: 'static-tpl',
                            tpl: '${icon | raw}'
                          },
                          {
                            name: 'dataSetName',
                            label: '数据集',
                            type: 'static'
                          },
                          {
                            name: 'url',
                            label: '链接',
                            type: 'static'
                          },
                          {
                            name: 'route',
                            label: '路由链接',
                            type: 'static'
                          },
                          {
                            name: 'status',
                            label: '菜单状态',
                            type: 'static',
                            tpl: "${status ? '显示' : '隐藏'}"
                          },
                          {
                            name: 'createUser',
                            label: '创建者',
                            type: 'static'
                          },
                          {
                            name: 'createTime',
                            label: '创建时间',
                            type: 'static',
                            tpl: '${createTime / 1000 | date}'
                          }
                        ],
                        id: 'u:2af2f2b5c2fd',
                        feat: 'View',
                        dsType: 'api',
                        labelAlign: 'top',
                        title: '表单',
                        mode: 'flex',
                        static: true,
                        actions: [
                          {
                            type: 'button',
                            label: '关闭',
                            level: 'default',
                            actionType: 'close'
                          }
                        ]
                      }
                    ],
                    actionType: 'drawer',
                    id: 'u:f202e0cd0692',
                    actions: [],
                    showCloseButton: true,
                    closeOnOutside: false,
                    closeOnEsc: false,
                    showErrorMsg: true,
                    showLoading: true,
                    draggable: false,
                    size: 'lg',
                    resizable: false,
                    editorSetting: {
                      displayName: '查看详情'
                    }
                  }
                },
                {
                  label: '编辑',
                  type: 'button',
                  level: 'link',
                  onEvent: {
                    click: {
                      actions: [
                        {
                          actionType: 'custom',
                          script: `
                            // 编辑现有页面
                            console.log('编辑页面:', event.data);
                            props.history.push(
                              \`/app\${props.applyData.id}/\${props.history.location.pathname.split('/')[2]}/page\${event.data.id}\`
                            );
                          `
                        }
                      ]
                    }
                  }
                },
                {
                  label: '删除',
                  type: 'button',
                  level: 'link',
                  className: 'text-danger',
                  confirmText: '确定要删除？',
                  id: 'u:6429a3a67359',
                  onEvent: {
                    click: {
                      actions: [
                        {
                          actionType: 'custom',
                          expression: 'delClassOrPage(event.data)'
                        }
                      ]
                    }
                  }
                }
              ],
              id: 'u:ffb3ca577178'
            }
          ],

          alwaysShowPagination: true,
          footerToolbar: [
            {
              type: 'statistics'
            },
            {
              type: 'switch-per-page',
              tpl: '切换页码',
              wrapperComponent: '',
              id: 'u:bda5e402486b'
            },
            {
              type: 'pagination',
              tpl: '分页',
              wrapperComponent: '',
              id: 'u:b2ec821b8884',
              __editorStatebaseControlClassName: 'default'
            }
          ]
        }
      ],
      asideResizor: false
    };
    return dataSetCurd;
  };

  return (
    <AMISRenderer
      key={refreshListKey}
      schema={getDataSetCurd()}
      embedMode={true}
      data={{
        handleCreateNewPage,
        delClassOrPage
      }}
    />
  );
};

export default MenuManager; 