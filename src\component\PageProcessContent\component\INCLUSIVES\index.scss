.process_root {
  position: relative;
  width: 100%;
  margin-top: 0.375rem;
  
  &-info{
    width: 100%;
    min-height: 2.5rem;
    display: flex;
    align-items: center;

    &-box{
      width: 2.5rem;
      height: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.5rem;
      background-color: #3496FB;

      &-icon{
        width: 1.25rem;
        height: 1.25rem;
        display: block;
      }
    }

    &-text{
      width: 15rem;
      margin-left: 1.25rem;

      &-name{
        color: #333333;
        font-size: 0.875rem;
      }

      &-desc{
        color: #999999;
        font-size: 0.875rem;
      }

    }

    &-member{
      flex: 1;
      display: flex;
      

      &-item{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-left: 1.25rem;

        &-header{
          width: 2.375rem;
          height: 2.375rem;
          border-radius: 50%;
          background-color: #3496FB;

          &-icon{
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        &-name{
          color: #333333;
          font-size: 0.875rem;
        }
      }
    }
  }

  &-line{
    margin-top: 0.375rem;
    width: 100%;
    height: 3.125rem;
    margin-left: 1.1875rem;
    border-left: 2px solid rgba($color: #000000, $alpha: 0.1);
  }
}