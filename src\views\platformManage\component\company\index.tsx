import React, {FC, useState, useRef, useContext } from 'react';
import { observer } from 'mobx-react';
import { Card, Button, toast } from 'amis';
import { EditTenantNameDialog } from '@/utils/schemaDataSet/EditTenantNameDialog';
import { EditTenantAbbreviationDialog } from '@/utils/schemaDataSet/EditTenantAbbreviationDialog';
import { EditTenantRemarkDialog } from '@/utils/schemaDataSet/EditTenantRemarkDialog';
import {getTenant, updateTenant, updateIcon} from '@/utils/api/api';
import AMISRenderer from '@/component/AMISRenderer';
import {IMainStore} from '@/store';
import { MobXProviderContext } from 'mobx-react';
import { RendererEvent, IScopedContext } from 'amis';
import './index.scss';


const Company: FC<any> = (props: any) => { 

  function useStore(): IMainStore {
    const {store} = useContext(MobXProviderContext);
    return store as IMainStore;
  }
  const store = useStore();
  console.log(store.userInfo,'store--');

  const [companyInfo, setcompanyInfo] = React.useState<any>(false);
  // 编辑企业全称
  const [showEditTenantNameDialog, setshowEditTenantNameDialog] = useState(false);
  // 编辑企业简称
  const [showEditTenantAbbreviationDialog, setshowEditTenantAbbreviationDialog] = useState(false);
  // 编辑企业描述
  const [showEditTenantRemarkDialog, setshowEditTenantRemarkDialog] = useState(false);
  // 新增：控制图标弹框显示
  const [showEditIconDialog, setShowEditIconDialog] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = () => {
    toast.success('保存成功');
  };
  
  React.useEffect(() => {
    const storeData = localStorage.getItem('store');
      if (storeData) {
        const store = JSON.parse(storeData);
        const tenant_id = store.tenant_id;
        console.log(tenant_id)
        getCompany(tenant_id)
      }

  }, []);

    // 获取企业详情
  const getCompany = async (tenant_id: any) => {
    let data = {
      id: tenant_id,
    };
    let res = await getTenant(data);
    if (res.code == 0) {
      if (res.data) {   //res.data.list
        setcompanyInfo(res.data);
      }

    }
  };

  const handleEditTenantName = (values: any) => {
    // 处理企业全称的逻辑
    const name = values[0]?.name;
    if (name) {
      updateProfile(
        { name: name },
         'name',
         name,
        () => {
            setshowEditTenantNameDialog(false);
          }
      );
    }
  };

  const handleEditTenantAbbreviation = (values: any) => {
    // 处理企业简称的逻辑
    const abbreviation = values[0]?.abbreviation;
    if (abbreviation) {
      updateProfile(
        { abbreviation: abbreviation },
          'abbreviation',
          abbreviation,
        () => {
          setshowEditTenantAbbreviationDialog(false);
          }
      );
    }
  };

  const handleEditTenantRemark = (values: any) => {
    // 处理企业描述的逻辑
    const remark = values[0]?.remark;
    if (remark) {
      updateProfile(
        { remark: remark },
        'remark',
        remark,
        () => {
          setshowEditTenantRemarkDialog(false);
          }
      );
    }
  };  

  // 通用的更新方法
  const updateProfile = (data: any, fieldName: string, value: any, closeDialog: () => void) => {
    companyInfo[fieldName]=value;
    updateTenant(companyInfo)
      .then(res => {
        console.log('update user profile:', res);
        if (res.code === 0) {
          setcompanyInfo({
            ...companyInfo,
            [fieldName]: value
          });
          if(fieldName=='abbreviation'){
            store.setUserInfo({
              ...store.userInfo,
              companyName: value
            });
          }
          closeDialog();
        } else {
          toast.error(res.message);
        }
      })
      .catch(err => {
        toast.error(err.message);
      });
  };

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadFile(
        file,
        'file',
        updateIcon,
        {
          allowedTypes: /^image\/(jpeg|png)$/,
          maxSize: 2 * 1024 * 1024,
          errorMessages: {
            typeError: '只支持 jpg、png 格式的图片',
            sizeError: '图片大小不能超过 2M'
          },

        }
      );
    }
    // 清空 input 的值，这样同一个文件可以重复选择
    event.target.value = '';
  };

  // 通用的文件上传方法
  const uploadFile = (
    file: File, 
    fieldName: string, 
    uploadApi: (formData: FormData) => Promise<any>,
    options: {
      maxSize?: number;
      allowedTypes?: RegExp;
      errorMessages?: {
        typeError?: string;
        sizeError?: string;
      };
    
    } = {}
  ) => {
    // 默认配置
  const defaultOptions = {
    maxSize: 2 * 1024 * 1024, // 默认2MB
    allowedTypes: /^image\/(jpeg|png)$/, // 默认jpg和png
    errorMessages: {
      typeError: '只支持 jpg、png 格式的图片',
      sizeError: '图片大小不能超过 2M'
    }
  };
  
  const config = { ...defaultOptions, ...options };
  
  // 验证文件类型
  if (!file.type.match(config.allowedTypes)) {
    toast.error(config.errorMessages.typeError || '文件类型不支持');
    return;
  }
  
  // 验证文件大小
  if (file.size > config.maxSize) {
    toast.error(config.errorMessages.sizeError || '文件大小超出限制');
    return;
  }
  
  // 创建 FormData
  const formData = new FormData();
  formData.append(fieldName, file);
  console.log('file:', file);

  // 打印FormData内容的正确方法
  console.log('FormData内容:');
  // 使用更兼容的方式打印FormData
  console.log(`${fieldName}: File(${file.name}, ${file.type}, ${file.size} bytes)`);
  
  // 临时预览（如果是图片）
  if (file.type.startsWith('image/')) {
    const reader = new FileReader();
    reader.onload = e => {
      setcompanyInfo({
        ...companyInfo,
        [fieldName]: e.target?.result as string
      });
    };
    reader.readAsDataURL(file);
  }
  
  // 调用上传API
  return uploadApi(formData)
    .then(res => {
      console.log('update user profile:', res);
      if (res.code === 0) {
        companyInfo.logo=res.data   //res.data
        updateTenant(companyInfo)
        setcompanyInfo({
          ...companyInfo,
          //icon: res.data.url
        });
        store.setUserInfo({
          ...store.userInfo,
          companyLogo: res.data
        });
      } else {
        console.log('上传失败')
        toast.error(res.message);
      }   
      // console.log(`upload ${fieldName}:`, res);
      // return res;
    })
    .catch(err => {
      toast.error(err.message);
      throw err;
    });
};

  // 新增：AMIS弹框schema
  const editIconDialogSchema = {
    type: 'dialog',
    title: '修改应用图标',
    body: {
      type: 'form',
      body: [
        {
          type: 'tpl',
          tpl: '<span class="icon-preview" onclick="this.innerHTML = ${icon}"></span>',
          className: 'text-left',
          style: {
            width: '48px',
            height: '48px',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: "${iconBg || '#F5F7FA'}",
            borderRadius: '8px',
            fontSize: '32px',
            marginBottom: '16px',
            marginRight: '16px',
            verticalAlign: 'middle'
          }
        },
        {
          type: 'button',
          label: '重置图标',
          level: 'default',
          className: 'reset-btn text-right',
          wrapperComponent: 'div',
          style: {
            height: '36px',
            background: '#fff',
            border: '1px solid #E5E6EB',
            float: 'right',
            color: '#1D2129',
            borderRadius: '6px',
            fontWeight: 500,
            fontSize: '14px',
            marginBottom: '16px',
            verticalAlign: 'middle'
          },
          onClick: "(e, props, data, throwErrors, renderer) => { renderer.props.onChange && renderer.props.onChange({logo: '', iconColor: '', iconBg: ''}); }"
        },

        {
          type: 'icon-selector',
          name: 'icon',
          label: '图标',
          placeholder: '请选择图标',
          labelClassName: 'font-normal text-left',
          className: 'text-left',
          returnSvg: true,
          onEvent: {
            change: {
              actions: [
                {
                  actionType: 'custom',
                  script: (
                    context: IScopedContext, 
                    doAction: (action: any, data?: any) => void, 
                    event: RendererEvent<{ icon: string }> // 明确event.data的结构
                  ) => {
                    console.log('图标值:', event.data.icon); // 类型安全
                  }
                },
                {
                  actionType: 'setValue',
                  script: (
                      context: IScopedContext, 
                      doAction: (action: any, data?: any) => void, 
                      event: RendererEvent<{ icon: string }> // 明确event.data的结构
                    ) => {
                      console.log('图标值:', event.data.icon); // 类型安全
                    },
                  componentId: 'iconPreview',
                  args: {
                    tpl: '<span class="icon-preview">${icon}</span>' // 移除alert，改为动态绑定
                  }
                }
              ]
            }
          }

        },
        {
          type: 'input-color',
          name: 'iconColor',
          label: '图标颜色',
          placeholder: '请选择颜色',
          labelClassName: 'font-normal text-left',
          className: 'text-left'
        },
        {
          type: 'input-color',
          name: 'iconBg',
          label: '图标背景',
          placeholder: '请选择背景颜色',
          labelClassName: 'font-normal text-left',
          className: 'text-left'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel'
      },
      {
        type: 'button',
        label: '保存',
        level: 'primary',
        actionType: 'submit'
      }
    ]
  };

  // 新增：弹框保存处理
  const handleEditIconConfirm = (values: any) => {
    // 这里可以处理保存逻辑，比如调用 updateTenant 或 updateIcon
    // values.logo, values.iconColor, values.iconBg
    setcompanyInfo({
      ...companyInfo,
      logo: values.logo,
      iconColor: values.iconColor,
      iconBg: values.iconBg
    });
    setShowEditIconDialog(false);
    toast.success('保存成功');
  };

  return (
    <div className="teamMain">
       <div className="page-header">
        <div className="title">企业设置</div>
        <div className="subtitle">这里是描述企业基础设置重要指标</div>
      </div>
      {/* <div className="teamMain-top">
        <div className="teamMain-top-left">
          <div className="teamMain-top-left-name">a</div>
          <div className="teamMain-top-left-describe">b</div>
        </div>
      </div> */}
      <div className="teamMain-main">
        {/* Tabs:配置选项卡+工具栏  toolbar*/}

            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-pjImage">
                  <div className="teamMain-main-setCenter-left-pjImage-image">
                      {companyInfo.logo && (
                        <img
                          className="teamMain-main-setCenter-left-pjImage-image-img"
                          src={companyInfo.logo}
                        />
                      )}
                      {/* <Avatar src={projectInfo.icon} size={48} /> */}

                  </div>
                  <div className="teamMain-main-setCenter-left-pjImage-name">
                    企业图标
                  </div>
                </div>

                <div className="teamMain-main-setCenter-left-content">
                  支持 2M 以内 JPG、PNG 图片
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                      <input
                             ref={fileInputRef}
                             type="file"
                             accept="image/*"
                             style={{display: 'none'}}
                             onChange={handleAvatarUpload}
                        />
                <div className="teamMain-main-setCenter-right-btn"   onClick={() => setShowEditIconDialog(true)}
                >
                    上传图标
                  </div>
              </div>
            </div>

            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-name">
                  企业全称
                </div>
                <div className="teamMain-main-setCenter-left-content">
                    {companyInfo.name}
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <div className="teamMain-main-setCenter-right-btn" onClick={()=>setshowEditTenantNameDialog(true)}>
                  编辑全称
                </div>
              </div>
            </div>

            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-name">
                  企业简称
                </div>
                <div className="teamMain-main-setCenter-left-content">
                {companyInfo.abbreviation}
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <div className="teamMain-main-setCenter-right-btn" onClick={()=>setshowEditTenantAbbreviationDialog(true)}>
                  编辑简称
                </div>
              </div>
            </div>

            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-name">
                  企业描述
                </div>
                <div className="teamMain-main-setCenter-left-content">
                {companyInfo.remark}
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <div className="teamMain-main-setCenter-right-btn" onClick={()=>setshowEditTenantRemarkDialog(true)}>
                  编辑描述
                </div>
              </div>
            </div>



            {/* <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-name">
                  删除团队
                </div>
                <div className="teamMain-main-setCenter-left-content">
                  永久删除团队所有应用，且不可恢复
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <div className="teamMain-main-setCenter-right-delbtn">
                  删除团队
                </div>
              </div>
            </div> */}
      </div>
      {showEditTenantNameDialog && (
          <AMISRenderer
            show={showEditTenantNameDialog}
            onClose={() => setshowEditTenantNameDialog(false)}
            onConfirm={handleEditTenantName}
            schema={EditTenantNameDialog(showEditTenantNameDialog, companyInfo.name)}
          />
        )}

        {showEditTenantAbbreviationDialog && (
          <AMISRenderer
            show={showEditTenantAbbreviationDialog}
            onClose={() => setshowEditTenantAbbreviationDialog(false)}
            onConfirm={handleEditTenantAbbreviation}
            schema={EditTenantAbbreviationDialog(showEditTenantAbbreviationDialog, companyInfo.abbreviation)}
          />
        )}

        {showEditTenantRemarkDialog && (
          <AMISRenderer
            show={showEditTenantRemarkDialog}
            onClose={() => setshowEditTenantRemarkDialog(false)}
            onConfirm={handleEditTenantRemark}
            schema={EditTenantRemarkDialog(showEditTenantRemarkDialog, companyInfo.remark)}
          />
        )}

        {showEditIconDialog && (
          <AMISRenderer
            show={showEditIconDialog}
            onClose={() => setShowEditIconDialog(false)}
            onConfirm={handleEditIconConfirm}
            schema={editIconDialogSchema}
          />
        )}
      
    </div>
  );
};

export default Company;
