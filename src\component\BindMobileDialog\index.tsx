import React from 'react';
import {Modal, Input, Button, toast} from 'amis';
import './index.scss';

interface BindMobileDialogProps {
  show: boolean;
  isRebind: boolean;
  countdown: number;
  currentMobile?: string;
  onClose: () => void;
  onConfirm: (mobile: string, code: string) => void;
  onSendCode: (mobile: string) => void;
}

export const BindMobileDialog: React.FC<BindMobileDialogProps> = ({
  show,
  isRebind,
  countdown,
  currentMobile,
  onClose,
  onConfirm,
  onSendCode
}) => {
  const [mobile, setMobile] = React.useState(currentMobile || '');
  const [code, setCode] = React.useState('');

  React.useEffect(() => {
    setMobile(currentMobile || '');
  }, [currentMobile]);

  const handleSendCode = () => {
    if (!mobile || !/^1\d{10}$/.test(mobile)) {
      toast.error('请输入正确的手机号');
      return;
    }
    onSendCode(mobile);
  };

  const handleConfirm = () => {
    if (!mobile || !/^1\d{10}$/.test(mobile)) {
      toast.error('请输入正确的手机号');
      return;
    }

    if (!code) {
      toast.error('请输入验证码');
      return;
    }
    onConfirm(mobile, code);
  };

  return (
    <Modal
      show={show}
      onClose={onClose}
      closeOnEsc
      closeOnOutside={false}
    >
      <div className="bind-mobile-dialog">
        <div className="dialog-header">
          <b>{isRebind ? '绑定新手机号' : '绑定手机号'}</b>
        </div>
        <div className="dialog-body">
          <div className="form-item">
            <Input
              value={mobile}
              onChange={e => setMobile(e.target.value)}
              placeholder={isRebind ? '新手机号' : '手机号'}
              maxLength={11}
              className="form-control"
            />
          </div>
          <div className="form-item code-item">
            <Input
              value={code}
              onChange={e => setCode(e.target.value)}
              placeholder="验证码"
              maxLength={6}
              className="form-control"
            />
            <Button
              className="verify-code-btn"
              disabled={countdown > 0}
              onClick={handleSendCode}
            >
              {countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码'}
            </Button>
          </div>
        </div>
        <div className="dialog-footer">
          <Button
            onClick={onClose}
            className="cancel-btn"
          >
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            level="primary"
            className="confirm-btn"
          >
            确定
          </Button>
        </div>
      </div>
    </Modal>
  );
}; 