import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class MyNavPlugin extends BasePlugin {
  static id = 'MyJsonSchemaEditorPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'json-schema-editor';
  $schema = '/schemas/JSONSchemaEditorSchema.json';

  // 组件基本信息
  name = 'JSON Schema 编辑器';
  panelTitle = 'JSON Schema 编辑器';
  icon = 'fa fa-code';
  panelIcon = 'fa fa-code';
  pluginIcon = 'fa fa-code';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于编辑 JSON Schema，支持可视化编辑复杂的 JSON Schema 结构';
  docLink = '/amis/zh-CN/components/form/json-schema-editor';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'json-schema-editor',
    name: 'jsonSchema',
    label: 'JSON Schema 编辑器'
  };

  // 预览界面
  previewSchema = {
    type: 'json-schema-editor',
    name: 'jsonSchema',
    label: 'JSON Schema 编辑器'
  };

  // 添加事件定义
  events = [
    {
      eventName: 'change',
      eventLabel: '值变化',
      description: '当值变化时触发',
      // ...
    }
  ];

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                getSchemaTpl('formItemName', {
                  required: true
                }),
                getSchemaTpl('label'),
                getSchemaTpl('switch', {
                  name: 'rootTypeMutable',
                  label: '允许修改顶层类型',
                  value: true
                }),
                getSchemaTpl('switch', {
                  name: 'showRootInfo',
                  label: '显示顶层类型信息',
                  value: true
                }),
                getSchemaTpl('switch', {
                  name: 'enableAdvancedSetting',
                  label: '开启详情配置',
                  value: false
                }),
                getSchemaTpl('switch', {
                  name: 'mini',
                  label: '迷你模式',
                  value: false,
                  description: '迷你模式下会隐藏一些不必要的元素'
                })
              ]
            },
            {
              title: '高级',
              body: [
                {
                  type: 'select',
                  name: 'disabledTypes',
                  label: '禁用类型',
                  multiple: true,
                  options: [
                    {
                      label: 'string',
                      value: 'string'
                    },
                    {
                      label: 'number',
                      value: 'number'
                    },
                    {
                      label: 'integer',
                      value: 'integer'
                    },
                    {
                      label: 'object',
                      value: 'object'
                    },
                    {
                      label: 'array',
                      value: 'array'
                    },
                    {
                      label: 'boolean',
                      value: 'boolean'
                    },
                    {
                      label: 'null',
                      value: 'null'
                    }
                  ],
                  description: '默认禁用了 null 类型'
                }
              ]
            },
            {
              title: '状态',
              body: [
                ...getSchemaTpl('status').body
              ]
            }
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          getSchemaTpl('style:common', {
            exclude: ['layout']
          }),
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(MyNavPlugin);
