.reset-success-page {
  width: 460px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: var(--boxShadow);

  .form-content {
    padding: 30px;
    width: 420px;
    border: 1px dashed var(--borderColor);
    margin: 20px;

    .page-title {
      font-size: 30px;
      color: var(--text-color);
      margin-bottom: 20px;
      font-weight: bold;
      text-align: center;
    }

    .success-tip {
      text-align: center;
      color: var(--text-color);
      font-size: 14px;
      margin-bottom: 8px;
    }

    .countdown-tip {
      text-align: center;
      color: var(--text--muted-color);
      font-size: 14px;
      margin-bottom: 30px;

      .countdown {
        color: var(--primary);
      }
    }

    .login-btn {
      height: 40px;
      font-size: 14px;
      background: var(--primary);
      border: none;
      border-radius: 4px;
      color: var(--button-primary-default-font-color);
    }
  }
} 