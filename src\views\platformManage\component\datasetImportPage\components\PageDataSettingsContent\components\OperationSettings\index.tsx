import React from 'react';
import AMISRenderer from '@/component/AMISRenderer';
import './index.scss';

interface OperationSettingsProps {
  dataSetInfo: any;
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;  // 为其他可能的属性添加索引签名
  };
}

const OperationSettings: React.FC<OperationSettingsProps> = ({ dataSetInfo,initPathItem }) => {
  return (
    <div className="operation-settings">
      <AMISRenderer 
        schema={{
          type: 'form',
          initApi: `/admin-api/system/data-set/get-operation-config/${dataSetInfo?.id}`,
          api: `/admin-api/system/data-set/update-operation-config/${dataSetInfo?.id}`,
          body: [
            {
              type: 'switch',
              name: 'enableOperation',
              label: '显示操作栏',
              onText: '是',
              offText: '否'
            },
            {
              type: 'input-text',
              name: 'operationWidth',
              label: '操作栏宽度',
              addOn: {
                label: 'px',
                type: 'text'
              }
            },
            {
              type: 'switch',
              name: 'fixedOperation',
              label: '固定操作栏',
              onText: '是',
              offText: '否'
            }
          ]
        }}
      />
    </div>
  );
};

export default OperationSettings; 