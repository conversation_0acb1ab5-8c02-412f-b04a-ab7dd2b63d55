// 导入全局颜色变量
$editor-default-color: #151b26; // 默认的字体色
$editor-active-color: #2468f2;
$editor-hover-color: #5086f5;
$editor-border-color: #e8e9eb; // 默认的边框色
$default-icon-color: #84868c; // 默认的icon颜色

$active-bg-color: #e6f0ff; // 激活态的背景色
$hover-bg-color: #f7f7f9; // 激活态的背景色

$disabled-color: #b8babf; // 禁用文字颜色
$disabled-bg-color: #f7f7f9; // 禁用背景颜色

html {
  font-size: 16px;
}
body {
  background-color: var(--body-bg);
}

.routes-wrapper {
  background-color: var(--colors-neutral-text-10);
}

.Editor-Demo {
  position: relative;
  height: 100vh;
  min-height: 510px;
  display: flex;
  flex-direction: column;

  .Editor-header {
    flex: 0 0 48px;
    position: relative;
    background: #fff;
    // box-shadow: 0 2px 7px 0 rgba(232,232,232,0.50);
    display: flex;
    border-bottom: 1px solid $editor-border-color;
    box-sizing: border-box;
    z-index: 1000;

    .editor-header-icon svg,
    .shortcut-icon-btn svg {
      display: inline-block;
      width: 16px;
      height: 16px;
    }

    .Editor-title {
      flex: 1 1 565px;
      padding: 0 15px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 0;
      user-select: none;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .Editor-view-mode-group-container {
      flex: 0 1 150px;
      display: flex;
      justify-content: center;
      align-items: center;

      .Editor-view-mode-group {
        font-size: 14px;
        font-family: PingFangSC-Regular;
        // color: #fff;
        letter-spacing: 0;
        text-align: center;
        width: 100px;
        height: 32px;
        border-radius: 4px;
        font-weight: 400;
        background-color: #f2f2f4;

        display: flex;
        justify-content: center;
        align-items: center;

        .Editor-view-mode-btn {
          user-select: none;
          padding: 0;
          border-radius: 4px;
          width: 40px;
          height: 24px;
          cursor: pointer;
          transition: transform ease-out 0.2s;
          display: inline-flex;
          justify-content: center;
          align-items: center;

          svg {
            color: $editor-default-color;
          }

          &:first-child {
            margin-right: 12px;
          }

          &:hover > svg {
            color: $editor-active-color;
          }

          &.is-active {
            background: $editor-active-color;

            svg {
              color: #fff;
            }

            &:hover {
              background: #5086f5;
            }
          }
        }
      }
    }

    .Editor-header-actions {
      flex: 1 1 565px;
      padding: 0 24px;
      font-size: 12px;
      white-space: nowrap;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .header-action-item {
        margin-left: 18px;
        user-select: none;
        cursor: pointer;
        transition: transform ease-out 0.2s;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        > svg {
          width: 16px;
          fill: $editor-default-color;
        }

        &:hover > svg {
          fill: $editor-active-color;
        }

        &.disabled {
          cursor: not-allowed;

          > svg {
            fill: $disabled-color;
          }
        }

        // 历史记录icon单独处理
        > svg.icon-editor-history {
          color: $editor-default-color;

          &:hover {
            color: $editor-active-color;
          }

          &.disabled,
          &.disabled:hover {
            cursor: not-allowed;
            color: $disabled-color;
          }
        }
      }

      .header-action-btn {
        margin-left: 8px;
        user-select: none;
        cursor: pointer;
        padding: 0 16px;
        min-width: 72px;
        height: 32px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        line-height: 20px;
        color: #fff;
        font-weight: 400;
        background: $editor-active-color;
        border-color: 1px solid $editor-active-color;
        border-radius: 4px;
        transition: transform ease-out 0.2s;
        display: inline-flex;
        justify-content: center;
        align-items: center;

        &.preview-btn {
          color: #151a26;
          border: 1px solid #dadbdd;
          background: #fff;
          border-radius: 4px;

          &:hover {
            color: $editor-active-color;
            border-color: $editor-active-color;
            background: #fff;
          }
        }

        &:hover {
          color: #fff;
          background: $editor-hover-color;
          border-color: $editor-hover-color;
        }

        &.disabled {
          cursor: not-allowed;
          color: $disabled-color;
          background-color: $disabled-bg-color;
          border-color: $disabled-bg-color;
        }

        &.exit-btn {
          background-color: #fff;
          border: 1px solid #ccc;
          color: #332e2e;

          &:hover {
            color: #fff;
            background: #fa541c;
            border-color: #fa541c;
          }
        }

        &.save-btn {
          background-color: #fff;
          border: 1px solid #2468f2;
          color: #2468f2;

          &:hover {
            color: #fff;
            background: #23b705;
            border-color: #23b705;
          }
        }
      }

      .margin-left-space {
        margin-left: 30px;
      }
    }
  }
  .Editor-inner {
    position: relative;
    flex: 1 1 auto;
    overflow-x: hidden;
    overflow-y: auto;
  }
}

//暗黑模式样式
// 全局过渡效果
* {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.cxd {
  // 从cxd-variables.scss中获取颜色值
  @import './cxd-variables';
}

// 暗黑主题

.dark {
  // 从dark-variables.scss中获取颜色值
  @import './dark-variables';
}

.cxd-Layout-main,
.dark-Layout-main {
  .cxd-Page-body,
  .dark-Page-body {
    // background-color: var(--background) !important;
    padding: 0;
  }
  // .cxd-Page,
  // .dark-Page {
  //   background-color: var(--background);
  // }
}

// .dark-Page,
// .cxd-Page {
//   height: auto;
// }

// .dark-Page,
// .cxd-Page {
//   height: 100vh;
// }

@media (min-width: 768px) {
  .cxd-Layout-brand,
  .cxd-Layout-brandBar,
  .cxd-Layout-aside,
  .dark-Layout-brand,
  .dark-Layout-brandBar,
  .dark-Layout-aside {
    width: 15rem;
  }
}

@media (min-width: 768px) {
  .cxd-Layout--withAside .cxd-Layout-headerBar,
  .cxd-Layout--withAside .cxd-Layout-footer,
  .dark-Layout--withAside .dark-Layout-headerBar,
  .dark-Layout--withAside .dark-Layout-footer {
    margin-left: 15rem;
  }
}

.cxd-Layout-content,
.dark-Layout-content {
  // padding: 0;
  // background-color: transparent;
  // min-height: calc(100vh - 3.125rem);
  background-color: var(--body-bg);
  padding: 12px;
  min-height: calc(100vh - 3.125rem);
}


.cxd-Tabs-content,
.dark-Tabs-content {
  background-color: transparent;
}

.dark-Table,
.cxd-Table {
  margin-bottom: 0;
}

.cxd-Form-item--horizontal .cxd-Form-itemColumn--normal {
  width: 0 !important;
}

.cxd-Form-item--horizontal > .cxd-Form-label {
  text-align: left;
  width: 0;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: fit-content !important;
}

.dark-Form-item--horizontal [class*='dark-Form-itemColumn--'] {
  flex: 0;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: fit-content !important;
}

// 头像上传组件
.avatar-uploader .cxd-Image img,
.avatar-uploader .dark-Image img,
.cxd-Image--thumb,
.dark-Image--thumb,
.cxd-ImageControl-addBtn,
.dark-ImageControl-addBtn,
.cxd-Image .cxd-Img-container .mask,
.dark-Image .dark-Img-container .mask,
.cxd-Image-thumb,
.dark-Image-thumb,
.cxd-ImageControl-item,
.dark-ImageControl-item,
.cxd-ImageControl-addBtn-bg
.dark-ImageControl-addBtn-bg{
  padding: 0;
  margin: 0;
  width: var(--avatar-width, 100px) !important;
  height: var(--avatar-height, 100px) !important;
  border-radius: var(--avatar-border-radius, 0) !important;
  overflow: hidden;
}

.cxd-ImageControl-addBtn svg,
.dark-ImageControl-addBtn svg{
  margin-bottom: 0;
}

.cxd-ImageControl .ImageControl-addBtn-icon,
.dark-ImageControl .ImageControl-addBtn-icon{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}


.divider {
  width: 100%;
  height: 1px;
  background-color: var(--borderColor);
  margin: 12px 6px;
}

.business-logo {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary);
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
}


/* 修复抽屉中的下拉框问题 */
.cxd-Drawer {
  &-content {
    /* 确保抽屉内容有足够高的z-index */
    position: absolute !important;
    z-index: 1400 !important;
  }

  /* 修复抽屉中下拉菜单位置 */
  .cxd-DropDown-menu-root {
    z-index: 1410 !important;
    position: fixed;
  }

  /* 修复Select下拉菜单 */
  .cxd-Select-popover {
    z-index: 1410 !important;
    position: fixed !important;
  }

  /* 修复嵌套选择器下拉菜单 */
  .cxd-NestedSelect-popover {
    z-index: 1410 !important;
    position: fixed !important;
  }
}

  /* 修复数据管理页面 列设置弹窗 菜单项样式 */
.dark-ColumnToggler-modal-content .dark-ColumnToggler-menuItem,
.cxd-ColumnToggler-modal-content .cxd-ColumnToggler-menuItem{
  background-color: var(--background);
  color: var(--text-color);
}

.cxd-Page-main > .cxd-Page-header,
.dark-Page-main > .dark-Page-header{
  border-bottom: none!important;
}
