import React, {FC} from 'react';
import './index.scss';
import {Button, toast, Modal, Input, Select, responseAdaptor} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj, createCustompageObj} from '@/utils/schemaPageTemplate/createPageObjs';
import fileAudio from '@/image/common_icons/file_audio.png';
import filePdf from '@/image/common_icons/file_pdf.png';
import fileWord from '@/image/common_icons/file_word.png';
import fileZip from '@/image/common_icons/file_zip.png';
import type_folder from '@/image/page_icons/type_folder.png';
import PagePanDataSettings from '@/component/PagePanDataSettings/index';

import {
  getFilePage, //获取表单数据
  getFormDataPage,
  createFormData,
  getImageClassificationPage,
  createComponentClassification,
  updateComponentClassification,
  deleteImageClassification
} from '@/utils/api/api';
import PagePanSettings from '@/component/PagePanSettings';
import { height } from '@/editor/EChartsEditor/Common';

// 声明全局方法
declare global {
  interface Window {
    openUploadDialog: () => void;
    setShowFolderModal: (show: boolean) => void;
    handleDeleteFile: (id: number) => void;
  }
}

const ComponentTemplatePageContent: FC<any> = (props: any) => {
  const [pageData, setPageData] = React.useState<any>({});
  const type = props.type
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');
  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }
    
    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  const [fileList, setFileList] = React.useState<any>([]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [showUploadModal, setShowUploadModal] = React.useState(false);
  const [showFolderModal, setShowFolderModal] = React.useState(false);
  const [showCreateCategoryModal, setShowCreateCategoryModal] = React.useState(false);
  const [newFolderName, setNewFolderName] = React.useState('');
  const [newCategoryName, setNewCategoryName] = React.useState('');
  const [categoryEditMode, setCategoryEditMode] = React.useState('create'); // 'create' 或 'rename'
  const [categoryToEdit, setCategoryToEdit] = React.useState<any>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const appId = props.history.location.pathname.split('/')[1].slice(3);

  //card ,list
  const [mode, setMode] = React.useState('card');
  const [categories, setCategories] = React.useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = React.useState<any>(null);
  const [rightMenuVisible, setRightMenuVisible] = React.useState(false);
  const [rightMenuPosition, setRightMenuPosition] = React.useState({ x: 0, y: 0 });
  const [rightMenuItem, setRightMenuItem] = React.useState<any>(null);

  const [showDeleteCategoryModal, setShowDeleteCategoryModal] = React.useState(false);
  const [categoryToDelete, setCategoryToDelete] = React.useState<any>(null);

  const [searchKeyword, setSearchKeyword] = React.useState('');
  const [searchStatus, setSearchStatus] = React.useState('');
  const [searchCategory, setSearchCategory] = React.useState('');

  const [showCreateTemplateModal, setShowCreateTemplateModal] = React.useState(false);

  const [amisKey, setAmisKey] = React.useState(0);

  const getToBlak = () => {
    let schema = JSON.parse(props.pageData.schema)
    let a = document.createElement('a');
    a.href = schema.body[0].src;
    window.open(a.href, '_blank');
  };

  // 获取表单数据 pageData
  // const handleGetFormDataPage = () => {
  //   let data = {
  //     applicationPageId: 1,
  //     pageNo: 1,
  //     pageSize: 10
  //   };
  //   getFormDataPage(data).then((res: any) => {
  //     if (res.code == 0) {
  //       if (res.data.list.length > 0) {
  //         if (res.data.list[0]) {
  //           // toast.success('获取表单数据成功');
  //           res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
  //           res.data.list[0].pageType = 13;
  //           res.data.list[0].appId = 1;
  //           setPageData(res.data.list[0]);
  //         }else{
  //           toast.success('暂无表单数据');
  //         }
  //       }else{
  //         toast.success('暂无表单数据');
  //       }
  //     } else {
  //       toast.error(res.msg);
  //     }
  //   });
  // };

  const initCustompageData = (pageId: any, info: any) => {
    // let data = {
    //   applicationPageId: pageId,
    //   data: window.JSON.stringify(createCustompageObj(info.name)),
    //   field: ''
    // };
    // createFormData(data).then((res: any) => {
    //   if (res.code == 0) {
    //     toast.success('初始化数据完成~');
        // setOpenCustomPagePopup(false);
        // if(type == '3'){
        //   props.history.push(
        //     `/formTempEditor/${pageId}`
        //   );
        // }else{
        //   props.history.push(
        //     `/componentTempEditor/${pageId}`
        //   );
        // }
        // handleGetFormDataPage();
        // props.onSaveSuccess(pageId);
    //   } else {
    //     toast.error('初始化数据失败 ' + res.msg);
    //   }
    // });
  };

  React.useEffect(() => {
    // if (props.pageData?.id) {
      // handleGetFormDataPage();
    // }
    const urlParams = new URLSearchParams(props.history.location.search);
    let tabkey =  urlParams.get('activeTabKey') || 'preview';
    console.log(appId);

    setActiveKey(tabkey);
  }, [props.location]);

  // 创建新分类
  const handleCreateCategory = () => {
    setShowCreateCategoryModal(true);
    setNewCategoryName('');
    setCategoryEditMode('create');
    setCategoryToEdit(null);
  };

  // 重命名分类
  const handleRenameCategory = (category: any) => {
    if (!category || category.id === 'all') return; // 不允许修改"全部分类"
    setShowCreateCategoryModal(true);
    setCategoryEditMode('rename');
    setCategoryToEdit(category);
  };

  // 删除分类
  const handleDeleteCategory = (category: any) => {
    console.log(category);
    if (!category || category.id === 'all') return; // 不允许删除"全部分类"
    
    // 显示删除确认弹窗
    setCategoryToDelete(category);
    setShowDeleteCategoryModal(true);
    setRightMenuVisible(false);
  };
  
  // 执行删除分类操作
  const confirmDeleteCategory = () => {
    if (!categoryToDelete) return;
    
    // 调用删除分类API
    deleteImageClassification({
      id: categoryToDelete.id
    }).then(result => {
        if (result.code === 0) {
          toast.success('分类删除成功');
          // 更新本地状态
          const updatedCategories = categories.filter(cat => cat.id !== categoryToDelete.id);
          setCategories(updatedCategories);
          if (selectedCategory?.id === categoryToDelete.id) {
            setSelectedCategory({
              id: 'all',
              name: '全部分类',
              type: 2
            });
          }
        } else {
          toast.error(result.msg || '删除失败');
        }
        setShowDeleteCategoryModal(false);
      })
      .catch(error => {
        console.error('删除分类失败', error);
        toast.error('删除分类失败，请稍后重试');
        setShowDeleteCategoryModal(false);
      });
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, category: any) => {
    e.preventDefault();
    const rect = document.documentElement.getBoundingClientRect();
    const windowHeight = rect.bottom - rect.top;
    
    // 计算菜单位置
    const isAboveHalf = e.clientY < windowHeight / 2;
    setRightMenuPosition({
      x: e.clientX,
      y: isAboveHalf ? e.clientY : e.clientY - 100
    });
    
    setRightMenuItem(category);
    setRightMenuVisible(true);
  };

  // 点击其他地方关闭右键菜单
  React.useEffect(() => {
    const handleClickOutside = () => {
      setRightMenuVisible(false);
    };

    if (rightMenuVisible) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [rightMenuVisible]);

  // 添加默认分类
  React.useEffect(() => {
    // 从接口获取分类列表
    fetchCategoryList();
  }, []);

  // 获取分类列表函数
  const fetchCategoryList = () => {
    getImageClassificationPage({
      pageNo: 1,
      pageSize: 10,
      type: type
    }).then(result => {
      if (result.code === 0) {
        setCategories([
            {
              id: 'all',
              name: '全部分类',
              type: 2
            },
            ...result.data.list
          ]);
        }
      })
      .catch(error => {
        console.error('获取分类列表失败：', error);
        // 加载失败时使用默认分类
        setCategories([
          {
            id: 'all',
            name: '全部分类',
            type: 2
          },
          {
            id: 1,
            name: '报表组件',
            type: 2
          },
          {
            id: 2,
            name: '表单组件',
            type: 2
          },
          {
            id: 3,
            name: '弹窗基础组件',
            type: 2
          }
        ]);
      });
    setSelectedCategory({
      id: 'all',
      name: '全部分类',
      type: 2
    });
  };

  return (
    <div className="pageBox">
      <div className="pageTop">
        <div className="pageTop-title">{type == '1' ? '图片管理' : '图标管理'}</div>
      </div>
      

      {/* 顶部操作区域 */}
      <div className="component-header" style={{display: 'flex', justifyContent: 'space-between'}}>
        <div className="action-area">
          <Button level="primary" onClick={() => setShowCreateTemplateModal(true)}>
            {type == '1' ? '上传图片' : '上传图标'}
          </Button>
        </div>
        <div className="mode-switch">
          <Button level="default" onClick={() => setMode('card')} className={mode === 'card' ? 'active' : ''}>
            卡片
          </Button>
          <Button level="default" onClick={() => setMode('list')} className={mode === 'list' ? 'active' : ''}>
            表格
          </Button>
        </div>
      </div>
      
      <div className="component-container">
        {/* 左侧分类栏 */}
        <div className="category-sidebar">
          <div className="category-header">
            <span>分类</span>
            <Button level="link" size="sm" onClick={handleCreateCategory} className="add-category-btn">
              <i className="fa fa-plus"></i>
            </Button>
          </div>
          <div className="category-list">
            {categories.map((category) => (
              <div
                key={category.id}
                className={`category-item ${selectedCategory?.id === category.id ? 'active' : ''}`}
                onClick={() => {
                  setSelectedCategory(category);
                  // 通过设置新的key来强制刷新AMIS渲染器
                  setAmisKey(Date.now());
                }}
                onContextMenu={(e) => handleContextMenu(e, category)}
              >
                <div className="category-item-content">
                  <img src={type_folder} className="category-icon" />
                  <span className="category-name">{category.name}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧内容区 */}
        <div className="content-area">
          {mode === 'card' && (
            <AMISRenderer
              key={amisKey}
              schema={{
                type: 'page',
                body: [
                  {
                    type: 'crud',
                    syncLocation: false,
                    id: 'crudId',
                    api: {
                      method: 'get',
                      url: '/admin-api/infra/file/page',
                      data: {
                        pageNo: '${page}',
                        pageSize: '${perPage}',
                        imageClassificationId: selectedCategory && selectedCategory.id !== 'all' 
                          ? selectedCategory.id 
                          : '${classificationId}',
                        // type: type,
                        isImageManagement: '1',
                        imageManagementType: type
                      },
                      // 过滤掉没有值的参数
                      requestAdaptor: function(api: any) {
                        const data = {...api.data};
                        Object.keys(data).forEach(key => {
                          if (data[key] === undefined || data[key] === '' || data[key] === null) {
                            delete data[key];
                          }
                        });
                        api.data = data;
                        return api;
                      }
                    },
                    mode: 'cards',
                    columnsCount: 4,
                    card: {
                      className: "border-none",
                      itemAction: {
                        type: "button",
                        actionType: "dialog",
                        dialog: {
                          title: type == '1' ? '图片预览' : '图标预览',
                          size: 'lg',
                          body: [
                            {
                              type: 'image',
                              value: '${url}',
                              name: 'url',
                              showToolbar: true,
                              enlargeAble: true
                            }
                          ]
                        }
                      },
                      body: [
                        {
                          type: 'service',
                          body: [
                            {
                              type: 'wrapper',
                              style: {
                                border: '1px solid #e8e9eb',
                                borderRadius: '4px',
                              },
                              body: [
                                {
                                  type: 'wrapper',
                                  style: {
                                    height: 200,
                                    backgroundColor: '#F7F8FA',
                                    overflow: 'auto'
                                  },
                                  body: [
                                    {
                                      type: 'flex',
                                      "justify": "center",
                                      "alignItems": "center",
                                      style: {
                                        height: 200,
                                        overflow: 'auto'
                                      },
                                      items: [
                                        {
                                          type: 'image',
                                          name: 'url',
                                        },
                                      ]
                                    }
                                  ]
                                },
                                {
                                  type: 'flex',
                                  items: [
                                    {
                                      type: 'container',
                                      body: [
                                        {
                                          type: 'tpl',
                                          tpl: '${name}',
                                          style: {
                                            flex: 1
                                          }
                                        }
                                      ],
                                      style: {
                                        display: 'flex',
                                        alignItems: 'center',
                                        flex: 1
                                      }
                                    },
                                  
                                    {
                                      type: 'dropdown-button',
                                      label: '',
                                      hideCaret: true,
                                      icon: 'fa fa-ellipsis-h',
                                      align: 'right',
                                      trigger: 'hover',
                                      size: 'sm',
                                      btnClassName: 'border-0',
                                      buttons: [
                                        {
                                          type: 'button',
                                          label: '复制链接',
                                          actionType: 'copy',
                                          content: '${url}'
                                        },
                                        {
                                          type: 'button',
                                          label: '复制SVG代码',
                                          actionType: 'copy',
                                          content: '${svgCode}',
                                          hidden: type == '1'
                                        },  
                                        {
                                          type: 'button',
                                          label: '删除',
                                          actionType: 'ajax',
                                          confirmText: '确认要删除该' + (type == '1' ? '图片' : '图标') + '吗？',
                                          api: {
                                            method: 'delete',
                                            url: '/admin-api/infra/file/delete?id=${id}'
                                          }
                                        }
                                      ]
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    },
                    footerToolbar: [
                      'statistics',
                      'switch-per-page',
                      'pagination'
                    ]
                  }
                ]
              }}
              embedMode={true}
            />
          )}

          {mode === 'list' && (
            <AMISRenderer
              key={amisKey}
              schema={{
                type: 'page',
                body: [
                  {
                    type: 'crud',
                    syncLocation: false,
                    id: 'crudId',
                    api: {
                      method: 'get',
                      url: '/admin-api/infra/file/page',
                      data: {
                        pageNo: '${page}',
                        pageSize: '${perPage}',
                        imageClassificationId: selectedCategory && selectedCategory.id !== 'all' 
                          ? selectedCategory.id 
                          : '${classificationId}',
                        isImageManagement: '1',
                        imageManagementType: type
                      },
                      // 过滤掉没有值的参数
                      requestAdaptor: function(api: any) {
                        const data = {...api.data};
                        Object.keys(data).forEach(key => {
                          if (data[key] === undefined || data[key] === '' || data[key] === null) {
                            delete data[key];
                          }
                        });
                        api.data = data;
                        return api;
                      }
                    },
                    columns: [
                      {
                        type: 'image',
                        label: '',
                        name: 'url',
                      },
                      {
                        type: 'tpl',
                        tpl: '${name}',
                        label: '名称',
                      },
                      {
                        type: 'operation',
                        label: '操作',
                        buttons: [
                          {
                            label: '查看',
                            type: 'button',
                            actionType: 'dialog',
                            level: 'link',
                            dialog: {
                              title: type == '1' ? '图片预览' : '图标预览',
                              size: 'lg',
                              body: [
                                {
                                  type: 'image',
                                  value: '${url}',
                                  name: 'url',
                                  showToolbar: true,
                                  enlargeAble: true
                                }
                              ]
                            }
                          },
                          {
                            label: '复制链接',
                            type: 'button',
                            level: 'link',
                            actionType: 'copy',
                            content: '${url}'
                          },
                          {
                            type: 'button',
                            label: '删除',
                            actionType: 'dialog',
                            level: 'link',
                            className: 'text-danger',
                            dialog: {
                              type: 'dialog',
                              title: '',
                              className: 'py-2',
                              actions: [
                                {
                                  type: 'action',
                                  actionType: 'cancel',
                                  label: '取消'
                                },
                                {
                                  type: 'action',
                                  actionType: 'submit',
                                  label: '删除',
                                  level: 'danger'
                                }
                              ],
                              body: [
                                {
                                  type: 'form',
                                  wrapWithPanel: false,
                                  api: {
                                    method: 'delete',
                                    url: '/admin-api/infra/file/delete?id=${id}',
                                  },
                                  body: [
                                    {
                                      type: 'tpl',
                                      className: 'py-2',
                                      tpl: '确认删除选中项？'
                                    }
                                  ],
                                  feat: 'Insert',
                                  dsType: 'api',
                                  labelAlign: 'left'
                                }
                              ]
                            }
                          }
                        ]
                      }
                    ],

                    footerToolbar: [
                      'statistics',
                      'switch-per-page',
                      'pagination'
                    ]
                  }
                ]
              }}
              embedMode={true}
            />
          )}
        </div>
      </div>


      {/* 分类编辑模态框 */}
      {showCreateCategoryModal && (
        <AMISRenderer
          show={showCreateCategoryModal}
          onClose={() => setShowCreateCategoryModal(false)}
          schema={{
            type: 'dialog',
            title: categoryEditMode === 'create' ? '新建分类' : '编辑分类',
            body: {
              type: 'form',
              api: {
                method: categoryEditMode === 'create' ? 'post' : 'put',
                url: categoryEditMode === 'create' ? '/admin-api/system/image-classification/create' : '/admin-api/system/image-classification/update',
                data: {
                  name: '${name}',
                  id: categoryEditMode === 'rename' && categoryToEdit ? categoryToEdit.id : '',
                  type: type
                },
                adaptor: function (payload: any, response: any, api: any, context: any) {
                  setShowCreateCategoryModal(false);
                  // 刷新分类列表
                  fetchCategoryList();
                  return payload;
                }
              },
              body: [
                {
                  type: 'input-text',
                  name: 'name',
                  label: '分类名称',
                  placeholder: '请输入分类名称',
                  value: categoryEditMode === 'rename' && categoryToEdit ? categoryToEdit.name : '',
                  required: true
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确定',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
        />
      )}

      {/* 删除分类确认对话框 */}
      {showDeleteCategoryModal && (
        <AMISRenderer
          show={showDeleteCategoryModal}
          schema={{
            type: 'dialog',
            title: '删除确认',
            body: {
              type: 'form',
              body: [
                {
                  type: 'tpl',
                  tpl: `确定要删除"${categoryToDelete.name}"分类吗？`,
                  className: 'py-2 text-center'
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel',
                onClick: () => setShowDeleteCategoryModal(false)
              },
              {
                type: 'button',
                label: '确定',
                level: 'danger',
                actionType: 'confirm',
                onClick: confirmDeleteCategory
              }
            ]
          }}
          onClose={() => setShowDeleteCategoryModal(false)}
        />
      )}

      {/* 右键菜单 */}
      {rightMenuVisible && rightMenuItem && (
        <div
          className="customContextMenu"
          style={{
            left: rightMenuPosition.x + 'px',
            top: rightMenuPosition.y + 'px'
          }}
        >
          <div
            className="customContextMenu-item"
            onClick={() => {
              handleRenameCategory(rightMenuItem);
              setRightMenuVisible(false);
            }}
          >
            <div className="customContextMenu-item-name">重命名</div>
          </div>
          <div
            className="customContextMenu-item"
            onClick={() => handleDeleteCategory(rightMenuItem)}
          >
            <div className="customContextMenu-item-name">删除分类</div>
          </div>
        </div>
      )}

      {/* 创建组件模板对话框 */}
      {showCreateTemplateModal && (
        <AMISRenderer
          show={showCreateTemplateModal}
          schema={{
            type: 'dialog',
            title: '上传' + (type == '1' ? '图片' : '图标'),
            body: {
              type: 'form',
              api: {
                method: 'post',
                url: '/admin-api/infra/file/uploadByImage',
                data: {
                  file: '${file}',
                  imageClassificationId: '${classificationId}',
                  isImageManagement: '1',
                  imageManagementType: type
                },
                requestAdaptor: async function (api: any, context: any) {
                  if (type == '2') {
                    const readFileAsText = (file: File | Blob) => {
                      return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                          resolve(e.target?.result);
                        };
                        reader.onerror = (e) => {
                          reject(e);
                        };
                        reader.readAsText(file);
                      });
                    };
                    
                    let extractedContent = '';
                    try {
                      // 等待文件读取完成
                      const content = await readFileAsText(context.file) as string;
                      //正则匹配svg标签内的内容
                      const svgContentRegex = /<svg[\s\S]*?<\/svg>/i;
                      const match = content.match(svgContentRegex);
                      extractedContent = match ? match[0] : content;
                    } catch (error) {
                      console.error('读取文件失败:', error);
                    }
                    
                    return {
                      ...api,
                      data: {
                        ...api.data,
                        svgCode: extractedContent
                      }
                    };
                  } else {
                    return api;
                  }
                },
                adaptor: function (payload: any, response: any, api: any, context: any) {

                  setShowCreateTemplateModal(false);
                  initCustompageData(response.data.data, context);
                  // 上传成功后刷新AMIS渲染器
                  setAmisKey(Date.now());
                  return payload;
                }
              },
              body: [
                {
                  type: 'input-file',
                  name: 'file',
                  label: type == '1' ? '图片' : '图标',
                  required: true,
                  autoUpload: false,
                  hideUploadButton: true,
                  asBlob: true,
                  accept: '.jpg,.jpeg,.png,.gif,.svg'
                },
                {
                  type: 'select',
                  name: 'classificationId',
                  label: '分类',
                  options: categories.filter((category) => category.id !== 'all').map((category) => ({
                    label: category.name,
                    value: category.id
                  })),
                },
               
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确定',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
          onClose={() => setShowCreateTemplateModal(false)}
        />
      )}

    </div>
  );
};

export default ComponentTemplatePageContent;
