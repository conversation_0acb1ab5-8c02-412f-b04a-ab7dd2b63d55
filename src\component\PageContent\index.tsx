import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {findCreateBody,replacedAddressBody} from '@/utils/schemaDataSet/commonEditor';
// 接口引入
import {
  createApplicationPageAndClass,
  getApplicationPageAndClassList,
  getApplicationPagePermissionList,
  updateFormData,
  getFormDataPage // 获取表单数据
} from '@/utils/api/api';

// 组件：页面设置
import PageSettings from '@/component/PageSettings/index';
import PageDataSettings from '@/component/PageDataSettings/index';
import PageRelease from '@/component/PageRelease';
import {CreateDataPagePopup} from '@/utils/schemaDataSet/CreateDataPagePopup';

// 定义Props接口
interface PageContentProps {
  pageData: any;
  history: any;
  match?: {
    params: {
      playType?: string;
    }
  };
  computedMatch?: {
    params: {
      appId: string;
      form?: string;
    }
  };
  store: any;
  updatePage: () => void;
}

interface PageReleaseProps {
  pageDataId: string;
  pageDataName?: string;
  appId?: string;
  form?: string;
}

const PageContent: FC<PageContentProps> = (props: PageContentProps) => {
  // 引用菜单列表数据（分类+页面）
  const [appCategoryList, setAppCategoryList] = React.useState<any>([]);
  // 新建数据页弹窗
  const [openCreateDataPagePopup, setOpenCreateDataPagePopup] =
    React.useState<boolean>(false);
  // 选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState<any>('manage');
  // 表单数据
  const [formData, setFormData] = React.useState<any>({});
  // 权限列表
  const [permissionList, setPermissionList] = React.useState<any>([]);
  // 是否有提交权限
  const [hasSubmitPermission, setHasSubmitPermission] = React.useState<boolean>(false);

  // 获取表单数据 formData
  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: props.pageData.id,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then(async (res: any) =>{
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            res.data.list[0].appId = props.computedMatch?.params?.appId;
            res.data.list[0].data  = await replacedAddressBody(res.data.list[0].data);
            console.log("res.data.list[0]", res.data.list[0])
            setFormData(res.data.list[0]);
            handleGetApplicationPagePermissionList(res.data.list[0])
          } else {
            toast.success('暂无表单数据');
          }
        } else {
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  const handleGetApplicationPagePermissionList = (formData: any) => {
    let data = {
      applicationPageId: props.pageData.id,
      pageNo: 1,
      pageSize: 100
    };
    
    getApplicationPagePermissionList(data).then((res: any) => {
      if (res.code == 0) {
        setPermissionList(res.data.list);
        
        // 检查当前用户是否有提交权限
        const hasEditPermission = checkPermission(res.data.list.filter((item: any) => item.type == 1));
        const hasViewPermission = checkPermission(res.data.list.filter((item: any) => item.type == 2));

        // 如果有表单数据，更新表单数据中的新增按钮显示状态
        updateFormDataWithPermission(formData, hasEditPermission, hasViewPermission);
        
      } else {
        toast.error(res.msg);
      }
    });
  };
  
  // 检查用户是否有权限
  const checkPermission = (permissionList: any[]) => {
    // 这里需要根据实际情况判断当前用户是否有权限
    // 例如，可以从store中获取当前用户ID，然后检查是否在权限列表中
    // 或者检查是否有"all"权限
    // 示例逻辑：如果有任何权限项的userIds为"all"，或者包含当前用户ID，则有权限
    const currentUserId = props.store.userInfo.id;
    
    return permissionList.some(permission => 
      permission.userIds === 'all' || 
      permission.userIds.split(',').includes(currentUserId.toString())
    );
  };
  
  // 更新表单数据中的新增按钮显示状态
  const updateFormDataWithPermission = (data: any, hasEditPermission: boolean, hasViewPermission: boolean) => {
    // 深拷贝数据以避免直接修改状态
    const updatedData = JSON.parse(JSON.stringify(data.data.body[0]));
    
    // 查找并更新headerToolbar中的新增按钮
    if (updatedData.headerToolbar && Array.isArray(updatedData.headerToolbar)) {
      updatedData.headerToolbar = updatedData.headerToolbar.map((item: any) => {
        if (item.label === '新增' && item.type === 'button' && item.actionType === 'drawer') {
          return {
            ...item,
            hidden: !hasEditPermission
          };
        }
        return item;
      });
    }
    //隐藏编辑和删除按钮
    if (updatedData.columns && Array.isArray(updatedData.columns)) {
      updatedData.columns = updatedData.columns.map((column: any) => {
        if (column.type === 'operation') {
          // 深拷贝操作列的按钮
          const updatedButtons = Array.isArray(column.buttons) 
            ? column.buttons.map((button: any) => {
                // 隐藏编辑和删除按钮
                if ((button.label === '编辑' && button.actionType === 'drawer') || 
                    (button.label === '删除')) {
                  return {
                    ...button,
                    hidden: !hasEditPermission
                  };
                }
                return button;
              })
            : column.buttons;
          
          return {
            ...column,
            buttons: updatedButtons
          };
        }
        return column;
      });
    }

    // 更新表单数据
    setFormData((prev: any) => ({
      ...prev,
      data: {
        ...prev.data,
        body: hasViewPermission ? [updatedData] : []
      }
    }));
  };

  // 表单预览的schema
  const formPreviewSchema = (schema: any) => {
    // 获取表单字段
    let formBody = findCreateBody(schema);

    // 尝试获取form配置（如columnCount等）
    let formConfig: any = {};
    if (schema && schema.body && schema.body.length > 0) {
      const firstBodyItem = schema.body[0];
      if (firstBodyItem.headerToolbar && firstBodyItem.headerToolbar.length > 0) {
        for (const toolbarItem of firstBodyItem.headerToolbar) {
          if (
            toolbarItem.actionType &&
            toolbarItem.editorSetting &&
            toolbarItem.editorSetting.behavior == 'create'
          ) {
            for (const formitem of toolbarItem[toolbarItem.actionType].body) {
              if (formitem.type === 'form') {
                // 提取form配置，排除body和一些不需要的属性
                const {body, api, actions, id, feat, ...config} = formitem;
                formConfig = config;
                break;
              }
            }
          }
        }
      }
    }

    let data = {
      type: 'page',
      regions: ['body'],
      pullRefresh: {
        disabled: true
      },
      body: {
        type: 'form',
        body: formBody,
        ...formConfig  // 应用form配置（包括columnCount）
      }
    };
    return data;
  };

  // 创建数据页弹窗确认事件 onCreateDataPagePopupConfirm
  const onCreateDataPagePopupConfirm = (val: any) => {
    val = val[0];
    console.log('val', val);

    // 创建页面
    let data: any = {
      url: '',
      dataSetId: props.pageData.dataSetId,
      applicationId: props.pageData.applicationId,
      applicantOrBackend: 1,
      pageType: 15,
      parentId: val.select ? val.select : 0,
      name: val.page_name,
      type: 1
    };

    createApplicationPageAndClass(data).then((res: any) => {
      if (res.code == 0) {
        // 普通表单
        // handleCreatePageSuccess(item, data, res);
        setOpenCreateDataPagePopup(false);
        toast.success('创建成功');
        props.updatePage();
      } else {
        toast.error('创建失败 ', res.msg);
      }
    });
  };

  const editForm = () => {
    const appId = props.computedMatch?.params?.appId;
    const form = props.computedMatch?.params?.form;
    const pageId = props?.pageData?.id;
    
    if (!appId || !form || !pageId) {
      toast.error('缺少必要的参数');
      return;
    }
    
    props.history.push(
      `/app${appId}/design/${form}?editCode=${pageId}`
    );
  };

  const editCrud = () => {
    const appId = props.computedMatch?.params?.appId;
    const form = props.computedMatch?.params?.form;
    const pageId = props?.pageData?.id;    
    
    props.history.push(
      `/app${appId}/editcrud/${form}?editCode=${pageId}`
    );
  };

  // 访问页面内容 - 在新窗口打开
  const visitPage = () => {
    const appId = props.computedMatch?.params?.appId;
    const form = props.computedMatch?.params?.form;
    const pageId = props?.pageData?.id;
    const pageName = props?.pageData?.name;
    
    if (!appId || !form || !pageId) {
      toast.error('缺少必要的参数');
      return;
    }
    
    const url = `#/app${appId}/submission/${form}?editCode=${pageId}&pageName=${pageName}`;
    const baseUrl = window.location.origin + window.location.pathname;
    window.open(baseUrl + url, '_blank');
  };

  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);

    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);

    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }

    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }

    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  // 在组件加载时添加样式
  React.useEffect(() => {
    // 创建唯一的 style id
    const styleId = 'pageNormal-custom-styles';
    console.log(props.match?.params?.playType);
    // 如果已存在则移除
    const existingStyle = document.getElementById(styleId);
    if (existingStyle) {
      existingStyle.remove();
    }

    // 创建新的 style 标签
    const styleElement = document.createElement('style');
    styleElement.id = styleId;
    styleElement.textContent = `
  .cxd-Layout-main .cxd-Page-body, .cxd-Layout-main .dark-Page-body, .dark-Layout-main .cxd-Page-body, .dark-Layout-main .dark-Page-body{
   background-color: var(--body-bg);
}
  `;

    // 添加到 head
    document.head.appendChild(styleElement);

    // 清理函数
    return () => {
      const style = document.getElementById(styleId);
      if (style) {
        style.remove();
      }
    };
  }, []);

  React.useEffect(() => {
    console.log('PageContent props', props);
    if (props.pageData?.id) {
      handleGetFormDataPage();
    }
    // 只有在admin模式下才处理tab切换逻辑
    if (props.match?.params?.playType === 'admin') {
      // 从 URL 查询参数中获取 activeTabKey，如果没有则默认为 'manage'
      const urlParams = new URLSearchParams(props.history.location.search);
      let tabKey = urlParams.get('activeTabKey') || 'manage';
      setActiveKey(tabKey);
    }

  }, [props.pageData?.id, props.history.location, props.match?.params?.playType]);

  // 检查必要的props是否存在
  if (!props.match || !props.computedMatch) {
    console.warn('缺少必要的路由参数');
    return null;
  }

  return (
    <div className="pageBox">
      {props.match.params.playType === 'admin' && (
        <div className="pageTop">
          <div className="pageTop-title">{props?.pageData?.name}</div>
          <div className="pageTop-right">
            <div className="pl-6">
              <Button size="lg" level="default" onClick={() => visitPage()}>
                访问
              </Button>
            </div>
            <div className="pl-1">
              <Button
                size="lg"
                level="default"
                onClick={() => setOpenCreateDataPagePopup(true)}
              >
                生成数据页
              </Button>
            </div>
            <div className="pl-1">
              <Button size="lg" level="primary" onClick={() => editForm()}>
                编辑表单
              </Button>
            </div>
            <div className="pl-1">
              <Button size="lg" level="primary" onClick={() => editCrud()}>
              编辑增删改查
              </Button>
            </div>
          </div>
        </div>
      )}
      <div className="cxd-pageTabsForm">
        {props.match?.params?.playType === 'admin' ? (
          // admin模式：显示完整的tab标签栏
          <Tabs
            mode="line"
            onSelect={(key: any) => handleTabChange(key)}
            linksClassName="pageTabsForm-tabsTitle"
            activeKey={activeKey}
          >
            <Tab title="普通表单预览" eventKey="preview">
              <div className="pageTabsForm-tabsContent">
                <div className="pageTabsForm-tabsContent-form">
                  <AMISRenderer
                    schema={formPreviewSchema(formData.data)}
                    embedMode={true}
                  />
                </div>
              </div>
            </Tab>
            <Tab title="数据管理" eventKey="manage">
              <div className="pageTabsForm-tabsContent">
                {formData.data?.body && (
                  <AMISRenderer schema={formData.data} embedMode={true} />
                )}
              </div>
            </Tab>
            <Tab title="数据页设置" eventKey="dataset">
              <PageDataSettings
                pageData={formData}
                history={props.history}
                store={props.store}
                update={() => {
                  console.log('update');
                  // props.updatePage();
                  handleGetFormDataPage();
                }}
              />
            </Tab>
            <Tab title="页面设置" eventKey="setting">
              <PageSettings
                formData={formData}
                history={props.history}
                pageData={props.pageData}
                store={props.store}
                update={() => {
                  props.updatePage();
                }}
              />
            </Tab>
            <Tab title="页面发布" eventKey="publish">
              <PageRelease
                pageDataId={props?.pageData?.id || ''}
                pageDataName={formData?.dataSetName}
                appId={props.computedMatch?.params?.appId || ''}
                form={props.computedMatch?.params?.form || ''}
              />
            </Tab>
          </Tabs>
        ) : (
          // 非admin模式：只显示普通表单预览内容，不显示tab标签栏
          <div className="pageTabsForm-tabsContent">
            <div className="pageTabsForm-tabsContent-form">
              <AMISRenderer
                schema={formPreviewSchema(formData.data)}
                embedMode={true}
              />
            </div>
          </div>
        )}
      </div>

      {/* 新建数据页弹窗 */}
      {openCreateDataPagePopup && (
        <AMISRenderer
          show={openCreateDataPagePopup}
          onClose={() => setOpenCreateDataPagePopup(false)}
          onConfirm={(val: any) => onCreateDataPagePopupConfirm(val)}
          schema={CreateDataPagePopup({
            pageName: `${props?.pageData?.name?.trim()}-数据管理`,
            group: appCategoryList
          })}
        />
      )}
    </div>
  );
};

export default PageContent;
