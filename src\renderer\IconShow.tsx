import {Renderer, render as renderAmis} from 'amis';
import React from 'react';
import {RendererProps, autobind} from 'amis-core';

export interface IconShowProps extends RendererProps {
  label?: string;
  name: string;
  tpl?: string;
}

@Renderer({
  test: /\bmy-renderer$/,
  name: 'icon-show',
  type: 'icon-show'
})
export default class IconShow extends React.PureComponent<IconShowProps> {
  @autobind
  render() {
    const {tpl, name, classnames: cx, className, themeCss, render} = this.props;
    const tplValue = tpl ? tpl : '';
    return (
      <>
        {render('static', {
          type: 'static',
          name: name,
          tpl: tplValue.replace(/'/g, ''),
          classnames: cx,
          className,
          themeCss
        })}
      </>
    );
  }
}
