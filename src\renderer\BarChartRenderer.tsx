import {
  Renderer,
} from 'amis-core';
import {ServiceStore} from 'amis-core';

import {ScopedContext, IScopedContext} from 'amis-core';

import {ChartProps, Chart } from 'amis/lib/renderers/Chart';


@Renderer({
  type: 'bar-chart',
  storeType: ServiceStore.name
})
export class Bar<PERSON><PERSON><PERSON><PERSON><PERSON> extends Chart {
  static contextType = ScopedContext;

  constructor(props: ChartProps, context: IScopedContext) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    const scoped = this.context as IScopedContext;
    scoped.unRegisterComponent(this);
  }

  setData(values: object, replace?: boolean) {
    const {store} = this.props;
    store.updateData(values, undefined, replace);
    // 重新渲染
    this.renderChart(this.props.config, store.data);
  }

  getData() {
    const {store} = this.props;
    return store.data;
  }
}
