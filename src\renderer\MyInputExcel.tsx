import React from 'react';
import {Renderer} from 'amis';
import {FormControlProps, TestIdBuilder} from 'amis-core';

/**
 * Excel 解析组件属性接口
 */
export interface MyInputExcelProps extends FormControlProps {
  /**
   * 组件类型
   */
  type?: 'my-input-excel';
  /**
   * 是否解析所有 sheet，默认情况下只解析第一个
   */
  allSheets?: boolean;
  /**
   * 解析模式，array 是解析成二维数组，object 是将第一列作为字段名，解析为对象数组
   */
  parseMode?: 'array' | 'object';
  /**
   * 是否包含空内容，主要用于二维数组模式
   */
  includeEmpty?: boolean;
  /**
   * 纯文本模式
   */
  plainText?: boolean;
  /**
   * 解析图片
   */
  parseImage?: boolean;
  /** 图片解析结果使用 data URI 格式 */
  imageDataURI?: boolean;
  /**
   * 占位文本提示
   */
  placeholder?: string;
  /**
   * 文件解析完成后将字段同步到表单内部
   */
  autoFill?: {
    [propName: string]: any;
  };
  testIdBuilder?: TestIdBuilder;
}

@Renderer({
  type: 'my-input-excel',
  name: 'my-input-excel'
})
export class MyInputExcelRenderer extends React.PureComponent<MyInputExcelProps> {
  static defaultProps = {
    allSheets: false,
    parseMode: 'object',
    includeEmpty: false,
    plainText: false,
    parseImage: false,
    imageDataURI: false,
    placeholder: '请选择Excel文件'
  };

  constructor(props: MyInputExcelProps) {
    super(props);
  }

  render() {
    const {
      static: isStatic,
      name,
      label,
      placeholder,
      allSheets,
      parseMode,
      includeEmpty,
      plainText,
      parseImage,
      imageDataURI,
      autoFill,
      render
    } = this.props;

    // 构建 input-excel 配置
    const config: any = {
      type: 'input-excel',
      label,
      name,
      placeholder: placeholder || '请选择Excel文件',
      allSheets: allSheets || false,
      parseMode: parseMode || 'object',
      includeEmpty: includeEmpty || false,
      plainText: plainText || false,
      parseImage: parseImage || false,
      imageDataURI: imageDataURI || false,
      autoFill: autoFill || {}
    };

    if (isStatic) {
      if (render) {
        return render('input-excel', config);
      }
      return <></>;
    } else {
      const allConfig = {
        ...this.props,
        ...config
      };

      if (render) {
        return render('my-input-excel', allConfig);
      }
      return <></>;
    }
  }
}
