import React from 'react';
import {Renderer} from 'amis';
import {FormControlProps} from 'amis-core';
import {SpinnerExtraProps} from 'amis-ui';

// 流水号字段配置接口
export interface SerialField {
  /**
   * 字段名称
   */
  name: string;
  /**
   * 字段类型：文本字符、引用时间、自动编号
   */
  type: 'text' | 'date' | 'auto';
  /**
   * 字段值或格式
   */
  value?: string;
  /**
   * 自动编号起始值
   */
  startValue?: number;
  /**
   * 自动编号间隔位数
   */
  increment?: number;
  /**
   * 重复类型
   */
  resetType?: 'never' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  /**
   * 是否可删除
   */
  removable?: boolean;
}

export interface SerialProps extends FormControlProps, SpinnerExtraProps {
  /**
   * 组件类型
   */
  type?: 'serial';
  /**
   * 标签
   */
  label?: string;
  /**
   * 字段名
   */
  name: string;
  /**
   * 占位符
   */
  placeholder?: string;
  /**
   * 当前值
   */
  value?: string;
  /**
   * 是否静态显示
   */
  static?: boolean;
  /**
   * 流水号规则
   */
  serialRule?: string;
  /**
   * 内容来源：随意内容、表单、数据
   */
  contentSource?: 'custom' | 'form' | 'data';
  /**
   * 基本
   */
  basic?: string;
  /**
   * 字段名
   */
  fieldName?: string;
  /**
   * 图标
   */
  icon?: string;
  /**
   * 标题显示
   */
  showTitle?: boolean;
  /**
   * 内容显示
   */
  showContent?: boolean;
  /**
   * 描述
   */
  description?: string;
  /**
   * 流水号字段列表
   */
  serialFields?: SerialField[];
  /**
   * 可见
   */
  visible?: boolean;
  /**
   * 条件
   */
  condition?: string;
  /**
   * 动态显示
   */
  dynamicDisplay?: boolean;
  /**
   * 只读
   */
  readonly?: boolean;
  /**
   * 禁用
   */
  disabled?: boolean;
  /**
   * 隐藏
   */
  hidden?: boolean;
  /**
   * 布局模式：水平、垂直
   */
  mode?: 'horizontal' | 'vertical' | 'inline';
  /**
   * 控件大小
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'full';
  /**
   * 标题提示
   */
  labelRemark?: {
    icon?: string;
    trigger?: string[];
    className?: string;
    placement?: string;
    title?: string;
    content?: string;
  };
  /**
   * 控件提示
   */
  remark?: {
    icon?: string;
    trigger?: string[];
    className?: string;
    placement?: string;
    title?: string;
    content?: string;
  };
}

@Renderer({
  type: 'serial',
  name: 'serial'
})
export class SerialRenderer extends React.PureComponent<SerialProps> {
  static defaultProps = {
    className: '',
    serialFields: [],
    showTitle: true,
    showContent: true,
    contentSource: 'custom',
    visible: true,
    dynamicDisplay: false,
    readonly: false,
    disabled: false,
    hidden: false
  };

  constructor(props: SerialProps) {
    super(props);
  }

  // 获取下一个自动编号（预览用，不递增计数器）
  getNextAutoNumber = (fieldIndex: number, startValue: number, digits: number, resetType: string): string => {
    const now = new Date();
    const counterKey = `serial_counter_${fieldIndex}`;
    const lastResetKey = `serial_last_reset_${fieldIndex}`;

    // 获取上次重置时间
    let lastReset = localStorage.getItem(lastResetKey);
    let shouldReset = false;

    // 检查是否需要重置计数器
    if (resetType !== 'never' && lastReset) {
      const lastResetDate = new Date(lastReset);

      switch (resetType) {
        case 'daily':
          shouldReset = now.toDateString() !== lastResetDate.toDateString();
          break;
        case 'weekly':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          weekStart.setHours(0, 0, 0, 0);
          const lastWeekStart = new Date(lastResetDate);
          lastWeekStart.setDate(lastResetDate.getDate() - lastResetDate.getDay());
          lastWeekStart.setHours(0, 0, 0, 0);
          shouldReset = weekStart.getTime() !== lastWeekStart.getTime();
          break;
        case 'monthly':
          shouldReset = now.getMonth() !== lastResetDate.getMonth() || now.getFullYear() !== lastResetDate.getFullYear();
          break;
        case 'quarterly':
          const currentQuarter = Math.floor(now.getMonth() / 3);
          const lastQuarter = Math.floor(lastResetDate.getMonth() / 3);
          shouldReset = currentQuarter !== lastQuarter || now.getFullYear() !== lastResetDate.getFullYear();
          break;
        case 'yearly':
          shouldReset = now.getFullYear() !== lastResetDate.getFullYear();
          break;
      }
    }

    // 获取当前计数器值（不递增）
    let currentCounter = 0;
    if (!shouldReset) {
      const stored = localStorage.getItem(counterKey);
      currentCounter = stored ? parseInt(stored) : 0;
    }

    // 计算下一个编号（预览用）
    // 当计数器为0时，下一个编号就是起始值
    // 当计数器为1时，下一个编号是起始值+1
    const nextNumber = startValue + currentCounter;

    // 根据固定位数格式化编号
    const formattedNumber = String(nextNumber).padStart(digits, '0');

    return formattedNumber;
  };

  // 生成并递增自动编号（实际保存数据时调用）
  generateAutoNumber = (fieldIndex: number, startValue: number, digits: number, resetType: string): string => {
    const now = new Date();
    const counterKey = `serial_counter_${fieldIndex}`;
    const lastResetKey = `serial_last_reset_${fieldIndex}`;

    // 获取上次重置时间
    let lastReset = localStorage.getItem(lastResetKey);
    let shouldReset = false;

    // 检查是否需要重置计数器
    if (resetType !== 'never' && lastReset) {
      const lastResetDate = new Date(lastReset);

      switch (resetType) {
        case 'daily':
          shouldReset = now.toDateString() !== lastResetDate.toDateString();
          break;
        case 'weekly':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          weekStart.setHours(0, 0, 0, 0);
          const lastWeekStart = new Date(lastResetDate);
          lastWeekStart.setDate(lastResetDate.getDate() - lastResetDate.getDay());
          lastWeekStart.setHours(0, 0, 0, 0);
          shouldReset = weekStart.getTime() !== lastWeekStart.getTime();
          break;
        case 'monthly':
          shouldReset = now.getMonth() !== lastResetDate.getMonth() || now.getFullYear() !== lastResetDate.getFullYear();
          break;
        case 'quarterly':
          const currentQuarter = Math.floor(now.getMonth() / 3);
          const lastQuarter = Math.floor(lastResetDate.getMonth() / 3);
          shouldReset = currentQuarter !== lastQuarter || now.getFullYear() !== lastResetDate.getFullYear();
          break;
        case 'yearly':
          shouldReset = now.getFullYear() !== lastResetDate.getFullYear();
          break;
      }
    }

    // 获取当前计数器值
    let currentCounter = 0;
    if (!shouldReset) {
      const stored = localStorage.getItem(counterKey);
      currentCounter = stored ? parseInt(stored) : 0;
    } else {
      console.log(`重置计数器[${fieldIndex}]: ${resetType}规则触发`);
    }

    // 递增计数器（实际生成编号时才递增）
    currentCounter += 1;

    // 计算最终编号
    const finalNumber = startValue + currentCounter - 1;

    // 根据固定位数格式化编号
    const formattedNumber = String(finalNumber).padStart(digits, '0');

    // 保存计数器和重置时间
    try {
      localStorage.setItem(counterKey, String(currentCounter));
      localStorage.setItem(lastResetKey, now.toISOString());

      console.log(`生成自动编号[${fieldIndex}]: 起始值=${startValue}, 计数器=${currentCounter}, 最终编号=${finalNumber}, 格式化=${formattedNumber}, 重置=${shouldReset}`);
    } catch (e) {
      console.warn('保存计数器失败:', e);
    }

    return formattedNumber;
  };

  // 为表单提供的生成完整流水号方法（实际保存数据时调用）
  generateSerialForSave = (fields?: SerialField[]): string => {
    const serialFields = fields || this.props.serialFields || [];
    let result = '';

    serialFields.forEach((field, index) => {
      switch (field.type) {
        case 'text':
          result += field.value || '';
          break;
        case 'date':
          const now = new Date();
          const format = field.value || 'YYYYMMDD';
          if (format === 'YYYY') {
            result += now.getFullYear();
          } else if (format === 'YYYYMM') {
            result += now.getFullYear() + String(now.getMonth() + 1).padStart(2, '0');
          } else if (format === 'YYYYMMDD') {
            result += now.getFullYear() +
                     String(now.getMonth() + 1).padStart(2, '0') +
                     String(now.getDate()).padStart(2, '0');
          } else if (format === 'MMDD') {
            result += String(now.getMonth() + 1).padStart(2, '0') +
                     String(now.getDate()).padStart(2, '0');
          } else if (format === 'DD') {
            result += String(now.getDate()).padStart(2, '0');
          }
          break;
        case 'auto':
          // 实际生成并递增自动编号
          let startValue = field.startValue || 1;
          let digits = field.increment || 1;
          let resetType = field.resetType || 'never';

          try {
            const storageKey = 'serial_auto_settings_' + index;
            const stored = localStorage.getItem(storageKey);
            if (stored) {
              const savedSettings = JSON.parse(stored);
              startValue = savedSettings.startValue || startValue;
              digits = savedSettings.increment || digits;
              resetType = savedSettings.resetType || resetType;
            }
          } catch (e) {
            console.warn(`读取localStorage设置失败[${index}]:`, e);
          }

          const autoNumber = this.generateAutoNumber(index, startValue, digits, resetType);
          result += autoNumber;
          break;
      }
    });

    return result;
  };



  // 生成流水号（预览用，不递增计数器）
  generateSerial = (fields?: SerialField[]) => {
    const serialFields = fields || this.props.serialFields || [];
    let result = '';

    serialFields.forEach((field, index) => {
      switch (field.type) {
        case 'text':
          result += field.value || '';
          break;
        case 'date':
          const now = new Date();
          const format = field.value || 'YYYYMMDD';
          if (format === 'YYYY') {
            result += now.getFullYear();
          } else if (format === 'YY') {
            result += String(now.getFullYear()).slice(-2);
          } else if (format === 'MM') {
            result += String(now.getMonth() + 1).padStart(2, '0');
          } else if (format === 'DD') {
            result += String(now.getDate()).padStart(2, '0');
          } else if (format === 'YYYYMM') {
            result += now.getFullYear() +
                     String(now.getMonth() + 1).padStart(2, '0');
          } else if (format === 'YYYYMMDD') {
            result += now.getFullYear() +
                     String(now.getMonth() + 1).padStart(2, '0') +
                     String(now.getDate()).padStart(2, '0');
          }
          break;
        case 'auto':
          // 从localStorage读取保存的设置，使用字段索引生成唯一存储键
          let startValue = field.startValue || 1;
          let digits = field.increment || 1; // increment现在表示固定位数
          let resetType = field.resetType || 'never';

          try {
            const storageKey = 'serial_auto_settings_' + index;
            const stored = localStorage.getItem(storageKey);
            if (stored) {
              const savedSettings = JSON.parse(stored);
              startValue = savedSettings.startValue || startValue;
              digits = savedSettings.increment || digits; // 这里increment实际存储的是位数
              resetType = savedSettings.resetType || resetType;
              console.log(`从localStorage读取自动编号设置[${index}]:`, savedSettings);
            } else {
              console.log(`没有找到自动编号设置[${index}]，使用默认值`);
            }
          } catch (e) {
            console.warn(`读取localStorage设置失败[${index}]:`, e);
          }

          // 预览自动编号（不递增计数器）
          const nextNumber = this.getNextAutoNumber(index, startValue, digits, resetType);
          result += nextNumber;
          break;
        default:
          break;
      }
    });

    return result;
  };

  render() {
    const {
      static: isStatic,
      name,
      label,
      placeholder,
      value,
      render,
      description,
      serialFields,
      visible,
      readonly,
      disabled,
      hidden,
      mode,
      size,
      labelRemark,
      remark
    } = this.props;

    // 如果组件被隐藏，不渲染
    if (hidden || !visible) {
      return null;
    }

    // 生成流水号值
    const serialValue = value || this.generateSerial(serialFields);

    // 构建流水号显示配置 - 使用static类型，不可编辑
    const config: any = {
      type: 'static',
      label,
      name,
      value: serialValue || '待生成',
      description,
      className: 'serial-display',
      mode: mode || 'horizontal',
      size: size || 'md',
      labelRemark,
      remark,
      placeholder: serialValue ? undefined : '自动生成的流水号'
    };

    // 如果是静态显示模式
    if (isStatic) {
      const staticConfig = {
        type: 'static',
        label,
        name,
        value: serialValue,
        className: 'serial-static',
        mode: mode || 'horizontal',
        size: size || 'md',
        labelRemark,
        remark
      };

      if (render) {
        return render('static', staticConfig);
      }
      return <div className="serial-static">{serialValue}</div>;
    }

    // 动态模式 - 使用static类型渲染
    if (render) {
      return render('static', config);
    }

    return (
      <div className="serial-component">
        {label && <label className="serial-label">{label}</label>}
        <div className="serial-display-value">
          {serialValue || '待生成'}
        </div>
        {description && <div className="serial-description">{description}</div>}
      </div>
    );
  }
}
