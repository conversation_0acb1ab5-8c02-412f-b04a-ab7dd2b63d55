.editor-right-panel {
  width: 27rem;
  .config-form-content {
    padding-bottom: 0;
  }

  .editor-prop-config-tabs {
    margin-top: 0;
    margin-bottom: 0;
  }
}

.chartDataBox {
  min-width: 26.25rem;
  border-top: 1px solid #e6e6e6;
  height: 100%;
  display: flex;
  overflow: auto;
  /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
  -webkit-scrollbar {
    display: none;
  }
  /* 隐藏Firefox浏览器的滚动条 */
  scrollbar-width: none;

  .chartConfiguration {
    flex: 1;
    border-right: 1px solid #e6e6e6;
    box-sizing: border-box;
    padding: 0.625rem;

    &-xAxis {
      width: 100%;
      margin-bottom: 0.625rem;

      &-title {
        font-size: 0.875rem;
        color: #000000;
        margin-bottom: 0.375rem;
        height: 2rem;
        line-height: 2rem;
        display: flex;
        justify-content: space-between;
        &-fx{
          font-weight: bold;
          color: rgba($color: #000000, $alpha: 0.38);
        }
      }

      &-box {
        box-sizing: border-box;
        width: 100%;
        min-height: 3.75rem;
        background-color: #f0f0f0;
        border: 1px dashed rgba($color: #000000, $alpha: 0.15);
        border-radius: 0.25rem;
        padding: 0.375rem;
        padding-bottom: 0;

        &:hover {
          background-color: rgba($color: #000000, $alpha: 0.1);
        }

        &-tips {
          width: 100%;
          height: 3.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.75rem;
          color: rgba($color: #000000, $alpha: 0.25);
        }

        &-tipsItem {
          width: 100%;
          height: 1.375rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.75rem;
          color: rgba($color: #000000, $alpha: 0.25);
        }

        &-item {
          width: 100%;
          height: 2rem;
          border-radius: 0.25rem;
          background-color: #267bfc;
          display: flex;
          align-items: center;
          font-size: 0.875rem;
          color: #ffffff;
          cursor: pointer;
          box-sizing: border-box;
          padding: 0 0.375rem;

          &-name {
            font-size: 0.875rem;
            color: #ffffff;
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
          }

          &-type {
            min-width: 1.5rem;
            font-size: 0.75rem;
            color: rgba($color: #000000, $alpha: 0.88);
            margin-right: 0.625rem;
            font-weight: bold;
          }

          &-fillin {
            width: 1rem;
            height: 1rem;
            margin-left: 0.5rem;

            &-icon {
              width: 100%;
              height: 100%;
              display: block;

              &:active {
                opacity: 0.6;
              }
              &:hover {
                opacity: 0.8;
              }
            }
          }

          &-delete {
            width: 1rem;
            height: 1rem;
            margin-left: 0.5rem;

            &-icon {
              width: 100%;
              height: 100%;
              display: block;

              &:active {
                opacity: 0.6;
              }
              &:hover {
                opacity: 0.8;
              }
            }
          }
        }
      }
    }

    &-yAxis {
      width: 100%;
      margin-bottom: 0.625rem;

      &-title {
        font-size: 0.875rem;
        color: #000000;
        margin-bottom: 0.375rem;
        height: 2rem;
        line-height: 2rem;
        display: flex;
        justify-content: space-between;

        &-fx{
          font-weight: bold;
          color: rgba($color: #000000, $alpha: 0.38);
        }
      }

      &-box {
        box-sizing: border-box;
        width: 100%;
        min-height: 3.75rem;
        background-color: #f0f0f0;
        border: 1px dashed rgba($color: #000000, $alpha: 0.15);
        border-radius: 0.25rem;

        &:hover {
          background-color: rgba($color: #000000, $alpha: 0.1);
        }

        &-tips {
          width: 100%;
          height: 3.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.75rem;
          color: rgba($color: #000000, $alpha: 0.25);
        }
      }
    }
  }

  .dataSetData {
    flex: 1;
    box-sizing: border-box;
    padding: 0.625rem;

    &-title {
      width: 100%;
      height: 2rem;
      line-height: 2rem;
      font-size: 0.875rem;
      color: #000000;
      margin-bottom: 0.375rem;
    }

    &-choseBtn {
      width: 100%;
      height: 2rem;
      border-radius: 0.25rem;
      background-color: #267bfc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.875rem;
      color: #ffffff;
      cursor: pointer;

      &:active {
        opacity: 0.6;
      }
      &:hover {
        opacity: 0.8;
      }
    }

    &-changeBtn {
      width: 100%;

      &-name {
        width: 100%;
        height: 2rem;
        display: flex;
        align-items: center;
        font-size: 0.875rem;
        color: #666666;
      }

      &-btn {
        width: 100%;
        height: 2rem;
        border-radius: 0.25rem;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        color: rgba($color: #000000, $alpha: 0.88);
        cursor: pointer;
        border: 1px solid rgba($color: #000000, $alpha: 0.15);

        &:active {
          opacity: 0.6;
        }
        &:hover {
          opacity: 0.8;
        }
      }
    }

    &-fieldtitle {
      width: 100%;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 0.5rem;
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }

    &-field {
      width: 100%;
      height: 2rem;
      border-radius: 0.25rem;
      background-color: #f1f2f3;
      display: flex;
      align-items: center;
      font-size: 0.875rem;
      color: #ffffff;
      cursor: pointer;
      margin-bottom: 0.25rem;
      padding: 0 0.375rem;
      box-sizing: border-box;

      &:active {
        opacity: 0.6;
      }
      &:hover {
        opacity: 0.8;
      }

      &-name {
        font-size: 0.875rem;
        color: #333333;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
      }

      &-type {
        min-width: 1.5rem;
        font-size: 0.75rem;
        color: rgba($color: #000000, $alpha: 0.25);
        margin-right: 0.625rem;
        font-weight: bold;
      }
    }
  }
}
