import React from 'react';
import {Renderer} from 'amis';
import {RendererProps} from 'amis-core';
import {getPropValue} from 'amis-core';
import cx from 'classnames';
import {autobind} from 'amis-core';

export interface RelatedFiledProps extends RendererProps {
  /**
   * 关联字段
   */
  source?: string | object;
  /**
   * 占位符
   */
  placeholder?: string;
  /**
   * 是否可清除
   */
  clearable?: boolean;
  /**
   * 是否可搜索
   */
  searchable?: boolean;
  /**
   * 是否多选
   */
  multiple?: boolean;
  /**
   * 选项值检查
   */
  showInvalidMatch?: boolean;
  /**
   * 虚拟列表阈值
   */
  virtualThreshold?: number;
  /**
   * 选项高度
   */
  itemHeight?: number;
  /**
   * 边框模式
   */
  borderMode?: 'full' | 'half' | 'none';
  /**
   * 是否内联
   */
  inline?: boolean;
  /**
   * 组件尺寸
   */
  size?: 'xs' | 'sm' | 'md' | 'lg';
  /**
   * 选项
   */
  options?: Array<any>;
  /**
   * 标签
   */
  label?: string;
  /**
   * 标签提示
   */
  labelRemark?: string;
  /**
   * 控件提示
   */
  remark?: string;
  /**
   * 描述
   */
  description?: string;
  /**
   * 是否必填
   */
  required?: boolean;
  /**
   * 校验规则
   */
  validations?: any;
  /**
   * 校验触发方式
   */
  validateOnChange?: boolean | 'default';
  /**
   * 校验API
   */
  validationApi?: string;
  /**
   * 分隔符
   */
  delimiter?: string;
  /**
   * 提取值
   */
  extractValue?: boolean;
  /**
   * 拼接值
   */
  joinValues?: boolean;
  /**
   * 只读
   */
  readOnly?: boolean;
  /**
   * 关联表单ID
   */
  relatedFormId?: string | number;
  /**
   * 字段名
   */
  fieldName?: string;
}

@Renderer({
  type: 'relatedfiled',
  name: 'relatedfiled'
})
export class RelatedFiledRenderer extends React.Component<RelatedFiledProps> {
  constructor(props: RelatedFiledProps) {
    super(props);
  }


  render() {
    const {
      static: isStatic,
      className,
      style,
      placeholder,
      clearable,
      searchable,
      multiple,
      showInvalidMatch,
      virtualThreshold,
      itemHeight,
      disabled,
      name,
      source,
      borderMode,
      size,
      hidden,
      visible,
      render,
      options,
      value,
      data,
      label,
      labelRemark,
      remark,
      description,
      required,
      validations,
      validateOnChange,
      validationApi,
      delimiter,
      extractValue,
      joinValues,
      readOnly,
      inline
    } = this.props;

    if (hidden || visible === false) {
      return null;
    }
    
    // 从data中获取options，如果props中没有提供
    const finalOptions = options || (data && data.options) || [];

    // 详细的调试信息
    console.log('=== RelatedFiled渲染器调试信息 ===');
    console.log('接收到的所有props:', this.props);
    console.log('选项数据:', finalOptions);
    console.log('source配置:', source);
    console.log('关联配置:', {
      fieldName: this.props.fieldName,
      relatedFormId: this.props.relatedFormId,
      relatedForm: this.props.relatedForm,
      relatedField: this.props.relatedField,
      linkType: this.props.linkType
    });
    console.log('=== 调试信息结束 ===');

    // 如果有关联表单ID和字段名，构建动态source
    let dynamicSource = null;
    if (this.props.relatedFormId && this.props.fieldName) {
      const sourceUrl = `/admin-api/system/form-field-value/page/${this.props.relatedFormId}?pageNo=1&pageSize=100`;
      dynamicSource = {
        method: 'GET',
        url: sourceUrl,
        adaptor: `
          if (!payload.data || !payload.data.list) {
            return {
              data: {
                options: []
              },
              status: 0,
              msg: ''
            };
          }

          const fieldName = '${this.props.fieldName}';
          const options = payload.data.list
            .filter((item) => {
              const fieldValue = item[fieldName];
              // 过滤掉空值、null、undefined、空字符串和纯空格
              return fieldValue != null &&
                     fieldValue !== '' &&
                     String(fieldValue).trim() !== '';
            })
            .map((item, index) => ({
              label: String(item[fieldName]).trim(),
              value: String(item[fieldName]).trim()
            }))
            // 去重，避免重复选项
            .filter((option, index, self) =>
              index === self.findIndex(o => o.value === option.value)
            );

          return {
            ...payload,
            data: {
              options: options
            }
          };
        `
      };
    }

    // 构建select配置，包含所有可能的属性
    const config: any = {
      type: 'select',
      className,
      name,
      label,
      labelRemark,
      remark,
      description,
      placeholder: placeholder || '请选择关联项',
      clearable: clearable !== false,
      searchable: searchable !== false,
      multiple,
      disabled,
      readOnly,
      required,
      // 优先使用动态source，然后是传入的source，最后是静态options
      ...(dynamicSource ? { source: dynamicSource } :
          source ? { source } :
          { options: finalOptions }),
      showInvalidMatch,
      borderMode,
      size,
      itemHeight,
      virtualThreshold,
      validations,
      validateOnChange,
      validationApi,
      delimiter,
      extractValue,
      joinValues,
      inline
    };

    // 处理静态展示模式
    if (isStatic) {
      if (render) {
        return render('static', {
          type: 'static',
          label,
          name,
          value: value || (data && data[name]) || '--'
        });
      }

      // 备用渲染方式
      return (
        <div className="RelatedFieldSelect" style={style}>
          <select
            className="form-control"
            disabled={disabled || readOnly}
            placeholder={placeholder}
          >
            <option value="">{placeholder}</option>
            {options &&
              options.map((option, index) => (
                <option key={index} value={option.value}>
                  {option.label}
                </option>
              ))}
          </select>
        </div>
      );
    } else {
      const allConfig = {
        ...this.props,
        ...config
      };

      console.log('RelatedFiled最终配置:', allConfig);

      if (render) {
        // 确保使用select渲染器来显示下拉选项
        return render('relatedfiled', allConfig);
      }

      // 如果render函数不可用，提供一个fallback
      return (
        <div className="RelatedFieldSelect" style={style}>
          <div className="alert alert-warning">
            关联字段组件渲染失败，请检查配置
          </div>
        </div>
      );
    }
  }
}
