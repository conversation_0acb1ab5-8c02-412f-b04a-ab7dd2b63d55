let curd ={
  type: 'page',
  id: 'u:eeea3f9d9550',
  title: '数据源列表',
  regions: ['body'],
  pullRefresh: {
    disabled: true
  },
  body: [
    {
      type: 'crud',
      syncLocation: false,
      api: {
        method: 'get',
        url: '/admin-api/system/data-source/page'
      },
      bulkActions: [],
      itemActions: [],
      id: 'u:9ebdfd442bed',
      perPageAvailable: [5, 10, 20, 50, 100],
      messages: {},
      footerToolbar: [
        {
          type: 'statistics'
        },
        {
          type: 'pagination'
        },
        {
          type: 'switch-per-page',
          tpl: '内容',
          wrapperComponent: '',
          id: 'u:5d1490ad088a'
        }
      ],
      filterSettingSource: [
        'id',
        'applicationId',
        'type',
        'name',
        'connectionMethod',
        'url',
        'host',
        'port',
        'databaseName',
        'userName',
        'passWord',
        'remark',
        'createTime'
      ],
      headerToolbar: [
        {
          label: '新增',
          type: 'button',
          actionType: 'dialog',
          level: 'primary',
          editorSetting: {
            behavior: 'create'
          },
          dialog: {
            type: 'dialog',
            title: '新增',
            body: [
              {
                type: 'form',
                api: {
                  method: 'post',
                  url: '/admin-api/system/data-source/create',
                  requestAdaptor: '',
                  adaptor: '',
                  messages: {},
                  data: {
                    'applicationId': 1,
                    '&': '$$'
                  }
                },
                body: [
                  {
                    type: 'select',
                    name: 'type',
                    label: '类型',
                    id: 'u:aadd1745186e',
                    multiple: false,
                    options: [
                      {
                        label: 'MySQL',
                        value: '1'
                      }
                    ],
                    value: '1',
                    required: true
                  },
                  {
                    type: 'input-text',
                    name: 'name',
                    label: '数据源名称',
                    id: 'u:c5320a65b540',
                    required: true
                  },
                  {
                    type: 'radios',
                    name: 'connectionMethod',
                    label: '连接方式',
                    id: 'u:d054107a13ae',
                    multiple: false,
                    options: [
                      {
                        label: '主机名连接',
                        value: '1'
                      },
                      {
                        label: 'JDBC连接',
                        value: '2'
                      }
                    ],
                    optionType: 'default',
                    value: '1',
                    required: true
                  },
                  {
                    type: 'textarea',
                    name: 'url',
                    label: 'JDBC URL',
                    id: 'u:929482bce270',
                    required: true,
                    visibleOn: '${connectionMethod == 2}',
                    minRows: 3,
                    maxRows: 20
                  },
                  {
                    type: 'input-text',
                    name: 'host',
                    label: '主机地址',
                    id: 'u:67db6d4f0412',
                    required: true,
                    visibleOn: '${connectionMethod == 1}'
                  },
                  {
                    type: 'input-text',
                    name: 'port',
                    label: '端口',
                    id: 'u:23dd97827d1a',
                    required: true,
                    visibleOn: '${connectionMethod == 1}'
                  },
                  {
                    type: 'input-text',
                    name: 'databaseName',
                    label: '数据库名',
                    id: 'u:da81d5f52543',
                    required: true,
                    visibleOn: '${connectionMethod == 1}'
                  },
                  {
                    type: 'input-text',
                    name: 'userName',
                    label: '用户名',
                    id: 'u:c673ca46ab2b',
                    required: true
                  },
                  {
                    type: 'input-password',
                    name: 'passWord',
                    label: '密码',
                    id: 'u:a0c13c286bfd',
                    required: true
                  },
                  {
                    type: 'textarea',
                    name: 'remark',
                    label: '备注',
                    id: 'u:e9072a3af066',
                    minRows: 3,
                    maxRows: 20,
                    showCounter: true,
                    maxLength: 200
                  }
                ],
                id: 'u:2db98e6ee317',
                actions: [
                  {
                    type: 'submit',
                    label: '提交',
                    primary: true
                  }
                ],
                feat: 'Insert',
                dsType: 'api',
                labelAlign: 'left'
              }
            ],
            actionType: 'dialog',
            id: 'u:3222d1be60a0',
            actions: [
              {
                type: 'button',
                actionType: 'cancel',
                label: '取消',
                id: 'u:60612a5ca8ce'
              },
              {
                type: 'button',
                actionType: 'confirm',
                label: '确定',
                primary: true,
                id: 'u:09ef016f0a16'
              }
            ],
            showCloseButton: true,
            closeOnOutside: false,
            closeOnEsc: false,
            showErrorMsg: true,
            showLoading: true,
            draggable: false
          },
          id: 'u:08262cfb8de7',
          disabledOnAction: false
        },
        {
          type: 'export-csv',
          tpl: '内容',
          wrapperComponent: '',
          id: 'u:30129f3c0998'
        },
        {
          type: 'export-excel',
          tpl: '内容',
          wrapperComponent: '',
          id: 'u:6a63d52666a1',
          disabledOnAction: false
        },
        'bulkActions'
      ],
      filterEnabledList: [
        {
          label: 'databaseName',
          value: 'databaseName'
        },
        {
          label: 'userName',
          value: 'userName'
        },
        {
          label: 'type',
          value: 'type'
        },
        {
          label: 'name',
          value: 'name'
        },
        {
          label: 'id',
          value: 'id'
        },
        {
          label: 'connectionMethod',
          value: 'connectionMethod'
        },
        {
          label: 'remark',
          value: 'remark'
        }
      ],
      filter: {
        id: 'u:5377410310ea',
        title: '查询条件',
        mode: 'horizontal',
        labelAlign: 'left',
        feat: 'Insert',
        body: [
          {
            type: 'input-text',
            label: 'ID',
            name: 'id',
            id: 'u:f5c29dbd4db5'
          },
          {
            type: 'input-text',
            label: '数据库名',
            name: 'databaseName',
            id: 'u:c7677b2c6163'
          },
          {
            type: 'input-text',
            label: '数据源名称',
            name: 'name',
            id: 'u:53ca2b50093d'
          },
          {
            type: 'input-text',
            label: '用户名',
            name: 'userName',
            id: 'u:b53b00625761'
          },
          {
            type: 'select',
            label: '类型',
            name: 'type',
            id: 'u:d88b2319dde9',
            options: [
              {
                label: 'MySQL',
                value: '1'
              }
            ],
            multiple: false,
            clearable: true
          },
          {
            type: 'select',
            label: '连接方式',
            name: 'connectionMethod',
            id: 'u:28931d465f63',
            options: [
              {
                label: '主机名连接',
                value: '1'
              },
              {
                label: 'JDBC连接',
                value: '2'
              }
            ],
            multiple: false,
            clearable: true
          },
          {
            type: 'input-text',
            label: '备注',
            name: 'remark',
            id: 'u:3c4f1f2bbdcc'
          }
        ],
        actions: [
          {
            type: 'reset',
            label: '重置',
            id: 'u:da879e1b84f7'
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
            id: 'u:c7c47c74d0f3'
          }
        ],
        columnCount: 4
      },
      columns: [
        {
          label: 'id',
          type: 'text',
          name: 'id',
          placeholder: '-',
          id: 'u:2c5b9b5d1178'
        },
        {
          label: '应用编号',
          type: 'text',
          placeholder: '-',
          name: 'applicationId',
          id: 'u:51cdb77cb423'
        },
        {
          label: '类型(目前只有MySQL)',
          type: 'select',
          name: 'type',
          id: 'u:6cda632ee949',
          placeholder: '-',
          options: [
            {
              label: 'MySQL',
              value: '1'
            }
          ],
          static: true
        },
        {
          label: '数据源名称',
          type: 'text',
          name: 'name',
          placeholder: '-',
          id: 'u:7323aa2940be'
        },
        {
          label: '连接方式',
          type: 'select',
          name: 'connectionMethod',
          id: 'u:aa012af09586',
          placeholder: '-',
          options: [
            {
              label: '主机名连接',
              value: '1'
            },
            {
              label: 'JDBC连接',
              value: '2'
            }
          ],
          static: true
        },
        {
          label: 'JDBC URL',
          type: 'text',
          name: 'url',
          id: 'u:86c85011f306',
          placeholder: '-',
          className: 'word-break '
        },
        {
          label: '主机地址',
          type: 'text',
          name: 'host',
          placeholder: '-',
          id: 'u:36efbbb3c7c7'
        },
        {
          label: '端口',
          type: 'text',
          name: 'port',
          placeholder: '-',
          id: 'u:fe026ee30c7c'
        },
        {
          label: '数据库名',
          type: 'text',
          name: 'databaseName',
          placeholder: '-',
          id: 'u:032204c6dc1f'
        },
        {
          label: '用户名',
          type: 'text',
          name: 'userName',
          placeholder: '-',
          id: 'u:9ec169b6214a'
        },
        {
          label: '密码',
          type: 'text',
          name: 'passWord',
          placeholder: '-',
          id: 'u:de0acc9debdf'
        },
        {
          label: '备注',
          type: 'text',
          name: 'remark',
          placeholder: '-',
          id: 'u:f33f85e5bf21'
        },
        {
          type: 'input-datetime',
          label: '创建时间',
          name: 'createTime',
          id: 'u:ebfe2ec6bc12',
          valueFormat: 'x',
          displayFormat: 'YYYY-MM-DD HH:mm:ss'
        },
        {
          type: 'operation',
          label: '操作',
          buttons: [
            {
              type: 'button',
              label: '删除',
              actionType: 'ajax',
              level: 'link',
              className: 'text-danger',
              confirmText: '确定要删除？',
              api: {
                method: 'DELETE',
                url: '/admin-api/system/data-source/delete',
                data: {
                  'id': "${id}",
                }
              },
              editorSetting: {
                behavior: 'delete'
              },
              id: 'u:6429a3a67359'
            },
            {
              label: '查看',
              type: 'button',
              actionType: 'dialog',
              level: 'link',
              editorSetting: {
                behavior: 'view'
              },
              dialog: {
                title: '查看详情',
                body: {
                  type: 'form',
                  initApi: '',
                  body: [
                    {
                      label: 'id',
                      name: 'id',
                      id: 'u:2814b030a8be',
                      type: 'static'
                    },
                    {
                      label: '应用编号',
                      name: 'applicationId',
                      id: 'u:5dbc773d1a01',
                      type: 'static'
                    },
                    {
                      label: '类型(目前只有MySQL)',
                      name: 'type',
                      id: 'u:26e0e1d84a69',
                      placeholder: '-',
                      options: [
                        {
                          label: 'MySQL',
                          value: '1'
                        }
                      ],
                      static: true,
                      type: 'static'
                    },
                    {
                      label: '数据源名称',
                      name: 'name',
                      id: 'u:66a22719be63',
                      placeholder: '-',
                      type: 'static'
                    },
                    {
                      label: '连接方式',
                      name: 'connectionMethod',
                      id: 'u:49423efdceca',
                      placeholder: '-',
                      options: [
                        {
                          label: '主机名连接',
                          value: '1'
                        },
                        {
                          label: 'JDBC连接',
                          value: '2'
                        }
                      ],
                      static: true,
                      type: 'static'
                    },
                    {
                      label: 'JDBC URL',
                      name: 'url',
                      id: 'u:a3f4566a966b',
                      placeholder: '-',
                      textOverflow: 'default',
                      className: 'word-break ',
                      width: 0,
                      type: 'static'
                    },
                    {
                      label: '主机地址',
                      name: 'host',
                      id: 'u:4b6b75e1ecec',
                      type: 'static'
                    },
                    {
                      label: '端口',
                      name: 'port',
                      id: 'u:476bf179834d',
                      type: 'static'
                    },
                    {
                      label: '数据库名',
                      name: 'databaseName',
                      id: 'u:2432b356aa4b',
                      type: 'static'
                    },
                    {
                      label: '用户名',
                      name: 'userName',
                      id: 'u:19ba82a25d8c',
                      type: 'static'
                    },
                    {
                      label: '密码',
                      name: 'passWord',
                      id: 'u:510ec78fac22',
                      type: 'static'
                    },
                    {
                      label: '备注',
                      name: 'remark',
                      id: 'u:b1afd5c08ac8',
                      type: 'static'
                    },
                    {
                      label: '创建时间',
                      name: 'createTime',
                      id: 'u:78f53f45fd44',
                      type: 'static'
                    }
                  ]
                }
              },
              id: 'u:a3166842880a'
            }
          ],
          id: 'u:67e2cfd2c1c4'
        }
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      filterTogglable: false,
      pageField: 'pageNo',
      perPageField: 'pageSize',
      draggable: false
    }
  ],
  asideResizor: false
};