interface DeleteSubmissionConfirmProps {
  tableName: string;
  id: string | number;
}

export const DeleteSubmissionConfirm = {
  type: 'dialog',
  title: '',
  closeOnEsc: false,
  closeOnOutside: false,
  showCloseButton: false,
  actions: [
    {
      type: 'button',
      label: '取消',
      actionType: 'cancel'
    },
    {
      type: 'button',
      label: '删除',
      level: 'danger',
      actionType: 'submit'
    }
  ],
  body: {
    type: 'form',
    wrapWithPanel: false,
    api: {
      method: 'delete',
      // url: '/${tableName}/${id}'
      url: '/admin-api/system/form-field-value/delete/${tableName}/${id}'
    },
    body: [
      {
        type: 'tpl',
        tpl: '确认删除选中项？'
      }
    ]
  }
}; 