// 二次删除团队
const CreateApplyPopup = (porps: any) => {
  // console.log('porps DeleteTeamSecondaryConfirm',porps);
  let options: any = [];
  if (porps.type && porps.group) {
    porps.group.forEach((item: any) => {
      options.push({
        label: item.name,
        value: item.id
      });
    });
  }

  return {
    type: 'dialog',
    body: [
      {
        id: 'u:d717906b428d',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'group',
            label: '应用图标',
            body: [
              {
                type: 'html',
                html: `
                  <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                    <div id="app-icon-preview" style="color:#999;background:#f5f7fa;width:48px;height:48px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:24px;border:2px dashed #d9d9d9;">
                      <i class="fa fa-plus" style="font-size:16px;"></i>
                    </div>
                    <div>
                      <button type="button" class="cxd-Button cxd-Button--default cxd-Button--size-sm" onclick="window.dispatchEvent(new CustomEvent('openIconEditor'))">
                        <span>选择图标</span>
                      </button>
                    </div>
                  </div>
                `,
                row: 0
              }
            ]
          },
          {
            type: 'input-text',
            label: '应用名称',
            name: 'apply_name',
            row: 1,
            id: 'u:7f7cd9abac87',
            placeholder: '请输入应用名',
            required: true
          },
          {
            type: 'select',
            label: '应用类型',
            name: 'type',
            options: [
              {
                label: '普通应用',
                value: 1
              },
              {
                label: '菜单应用',
                value: 2
              }
            ],
            row: 2,
            id: 'u:app-type-select',
            multiple: false,
            required: true,
            placeholder: '请选择应用类型'
          },
          {
            type: 'select',
            label: '项目组',
            name: 'select',
            options,
            row: 3,
            id: 'u:82cf215496b9',
            multiple: false,
            visible: porps.type,
            required: porps.type
          },
          {
            type: 'textarea',
            label: '描述（可选）',
            name: 'description',
            row: 4,
            id: 'u:1779df294803',
            minRows: 3,
            maxRows: 20,
            placeholder: '请输入应用描述'
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'u:d717906b428d'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: true
      }
    ],
    title: '创建应用',
    id: 'u:2aac8a329856',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:d2f9d50b6428'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'u:50f63b7790e7',
        onEvent: {
          click: {
            weight: 0,
            actions: []
          }
        }
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '创建应用'
    },
    $$ref: 'modal-ref-2'
  };
};

export {CreateApplyPopup};
