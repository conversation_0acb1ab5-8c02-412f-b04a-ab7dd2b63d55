$ns: cxd-;

.#{$ns}InputI18n-container {
  .#{$ns}InputI18n-inner-container {
    position: relative;
  }

  .#{$ns}InputI18n-addon-container {
    position: absolute;
    top: 1px;
    height: calc(100% - 2px);
    right: 1px;
    background: #f7f7f9;
    border-radius: 0px 3px 3px 0px;
    width: 30px;
    cursor: pointer;

    &:hover {
      border-color: var(--Form-input-borderColor) !important;
      background: #e6f0ff !important;
    }

    .CorpusI18n-input-icon.icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .#{$ns}InputI18n-addon-active {
    background: #e6f0ff;
    color: var(--link-color);
  }

  .#{$ns}InputI18n-fx-container {
    position: absolute;
    top: 1px;
    height: calc(100% - 2px);
    right: 1px;
    border-radius: 0px 3px 3px 0px;
    width: 30px;
    cursor: pointer;

    .CorpusI18n-input-icon.icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-60%, -50%);
      font-size: 24px;
    }
  }

  .#{$ns}InputI18n-fx-active {
    color: var(--link-color);
  }

  .#{$ns}InputI18n-overlay {
    position: absolute;
    top: 1px;
    left: 1px;
    bottom: 1px;
    right: 31px;
    width: calc(100% - 32px);
    height: calc(100% - 2px);
    cursor: pointer;
  }
}

.#{$ns}InputI18n-multi-container {
  .#{$ns}InputBox {
    input {
      padding-right: 30px;
    }
  }
}

.#{$ns}CorpusI18n {
  &-textarea {
    .#{$ns}Form-label {
      padding: var(--Form-input-paddingY) 0;
      .#{$ns}TplField {
        font-size: px2rem(12px);
      }
    }
    .#{$ns}Form-value {
      padding-right: 0;
      .#{$ns}TextareaControl-input {
        font-size: px2rem(12px);
      }
    }
  }
  &-static {
    .#{$ns}Form-label {
      padding-left: 0;
      .#{$ns}TplField {
        font-size: px2rem(12px);
      }
    }
    .#{$ns}Form-value {
      .#{$ns}PlainField {
        font-size: px2rem(12px);
      }
    }
  }
  .#{$ns}Panel {
    margin-bottom: 0;
  }
}

.CorpusI18n-icon.icon {
  top: 0 !important;
}

.#{$ns}FormulaPickerI18n {
  .#{$ns}Modal-header {
    font-weight: bold;
  }
  &-tab {
    padding: var(--gap-base) 0;
    .#{$ns}FormulaEditor-content .#{$ns}FormulaEditor-header {
      display: none;
    }
  }
}

.RichTextI18n {
  position: relative;
  width: 100%;
  height: 100%;
  display: block;
  border: 1px solid var(--Form-input-borderColor);
  border-radius: var(--Form-input-borderRadius);
  background: var(--Form-input-bg);
  font-size: var(--Form-input-fontSize);
  &::placeholder {
    color: var(--Form-input-placeholderColor);
  }

  &:hover {
    border-color: var(--Form-input-onHover-borderColor);
  }

  .fr-toolbar.fr-top {
    border-radius: var(--Form-input-borderRadius);
  }

  &-editor {
    height: 100%;
    border-radius: var(--Form-input-borderRadius);
    overflow: auto;
    & > .CodeMirror {
      height: 100%;
      padding-bottom: 26px;
      .CodeMirror-lines {
        padding-bottom: 30px;
      }
      .CodeMirror-vscrollbar {
        margin-bottom: 26px;
      }

      // 解决上下 pre标签中表达式浮层遮挡问题
      .CodeMirror-measure + div {
        z-index: inherit !important;
      }
      pre.CodeMirror-line,
      .CodeMirror pre.CodeMirror-line-like {
        z-index: initial;
      }
    }

    .cm-expression {
      position: relative;
      display: inline-block;

      .expression-popover {
        position: absolute;
        display: none;
        left: 0;
        bottom: -30px;
        transform: translate(calc(24px - 50%));
        font-size: 12px;
        width: max-content;
        color: #fff;
        border-radius: 4px;
        padding: 2px 8px;
        background: var(--Tooltip-bg--dark);
        border: none;
        box-shadow: var(--Tooltip-boxShadow--dark);
        z-index: 10;

        .expression-popover-arrow {
          position: absolute;
          display: block;
          width: var(--Tooltip-arrow-width);
          height: var(--Tooltip-arrow-height);
          margin-left: calc(var(--Tooltip-arrow-width) * -1 / 2);
          left: 50%;
          top: calc(var(--Tooltip-arrow-height) * -1);

          &::before,
          &::after {
            position: absolute;
            display: block;
            content: '';
            border-color: transparent;
            border-style: solid;
            border-width: 0 calc(var(--Tooltip-arrow-width) / 2)
              var(--Tooltip-arrow-height) calc(var(--Tooltip-arrow-width) / 2);
          }
          &::before {
            border-width: 0;
          }
          &::after {
            border-bottom-color: var(--Tooltip-arrow-color--dark);
          }
        }
      }
    }

    .cm-expression-text {
      display: inline-block;
      position: relative;
      padding: 0 5px;
      border-radius: 3px;
      color: #fff;
      margin: 0 1px 1px;
      background: var(--button-primary-default-bg-color);
      cursor: pointer;
      width: 40px;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      vertical-align: middle;

      &:hover {
        background: var(--button-primary-hover-bg-color);
        & ~ .expression-popover {
          display: block;
        }
      }
    }
  }

  &-footer {
    position: absolute;
    width: 100%;
    background: #f7f7f9;
    bottom: 0px;
    height: 26px;
    margin: 0;
    border-radius: 0 0 4px 4px;
    list-style: none;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    > li {
      margin-right: 10px;
      cursor: pointer;
    }
    &-fxIcon {
      > a {
        font-size: 12px;
      }
    }
  }

  &-overlay {
    position: absolute;
    z-index: 5;
    top: 1px;
    left: 1px;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    cursor: pointer;
  }
}

.RichTextI18n-enabled-container {
  padding-bottom: 20px;

  .RichTextI18n-footer-fxIcon {
    .icon-corpus-i18n {
      color: var(--body-color);
    }
  }

  .RichTextI18n-footer-matched-fxIcon {
    .icon-corpus-i18n {
      color: var(--link-color);
    }
  }
}

.TextareaI18n {
  position: relative;
  width: 100%;
  height: 100%;
  display: block;
  border: 1px solid var(--Form-input-borderColor);
  border-radius: var(--Form-input-borderRadius);
  background: var(--Form-input-bg);
  font-size: var(--Form-input-fontSize);
  &::placeholder {
    color: var(--Form-input-placeholderColor);
  }

  &:hover {
    border-color: var(--Form-input-onHover-borderColor);
  }

  .#{$ns}TextareaControl > textarea {
    border: none;
  }

  &-footer {
    position: absolute;
    width: 100%;
    background: #f7f7f9;
    bottom: 0px;
    height: 26px;
    margin: 0;
    border-radius: 0 0 4px 4px;
    list-style: none;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    > li {
      margin-right: 10px;
      cursor: pointer;
    }
    &-corpusIcon {
      > a {
        font-size: 12px;
      }
    }
  }

  &-overlay {
    position: absolute;
    top: 1px;
    left: 1px;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    cursor: pointer;
  }
}

.TextareaI18n-multi-container {
  padding-bottom: 26px;

  .TextareaI18n-footer-corpusIcon {
    .icon-corpus-i18n {
      color: var(--body-color);
    }
  }

  .TextareaI18n-footer-highlight-corpusIcon {
    .icon-corpus-i18n {
      color: var(--link-color);
    }
  }
}

.#{$ns}CorpusConfigDialog-container {
  z-index: 9999;
}

.#{$ns}CorpusConfigDialog-confirm-dialog {
  z-index: 10000;
}

.#{$ns}CorpusConfigDialogBody {
  margin-bottom: 1rem;
  .corpus-keyword-searchbox {
    width: 20rem;
    font-size: 12px;
  }

  .operate-tip-container {
    position: absolute;
    right: 0;

    .create-tip,
    .update-tip {
      font-size: 12px;

      .dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #28a745;
        display: block;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: -10px;
      }
    }
  }

  .Corpus-FormItem-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;

    .Corpus-FormItem-label {
      position: relative;
      top: 6px;
      width: 70px;
      margin-right: 10px;
      text-align: right;
      word-wrap: break-word;
      word-break: break-all;
      font-weight: 500px;
    }

    .#{$ns}TextareaControl {
      width: 240px;
    }
  }

  .CorpusTablePicker-container {
    .CorpusTablePicker-outer-container {
      width: 100%;
      overflow-x: auto;
    }
    .CorpusTablePicker-inner-container {
      width: fit-content;
      min-width: 100%;
    }
    .CorpusTablePicker-Header-container {
      display: flex;
      align-items: center;
      background-color: #f7f8fa;
      padding: 10px 20px;
      padding-left: 40px;
      font-size: 12px;
      font-weight: 500;
    }

    .CorpusTablePicker-Body-container {
      .CorpusTablePicker-BodyRow-item {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f2f2f2;
        padding: 10px 20px;
        padding-left: 40px;
        cursor: pointer;
        font-size: 12px;
        position: relative;

        &:hover {
          background-color: #e6f0ff;
        }

        .CorpusTablePicker-radio {
          position: absolute;
          left: 10px;
          top: 50%;
          transform: translateY(-50%);
          border: 1px solid #f2f2f2;
        }
      }

      .CorpusTablePicker-BodyColumn-item {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        padding-right: 20px;
        box-sizing: border-box;
      }
    }
  }

  .#{$ns}Pagination-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin: 10px 0;
  }

  .CorpusDetailPanel-container {
    border: 1px solid #f7f8fa;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-size: 12px;
    margin-top: 16px;
    .CorpusDetailPanel-Header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #f7f8fa;
      padding: 5px 20px;
      .CorpusDetailPanel-Header-title {
        font-weight: 500;
      }

      .CorpusDetailPanel-Header-buttons-container {
        display: flex;
        align-items: center;

        .#{$ns}Button {
          font-size: 12px;
        }
      }
    }

    .CorpusDetailPanel-Body-container {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 20px;
      padding-bottom: 0;

      .CorpusDetailPanel-item {
        display: flex;
        align-items: flex-start;
        word-wrap: break-word;
        word-break: break-all;
        width: 50%;
        margin-bottom: 20px;

        .CorpusDetailPanel-label {
          width: 80px;
          margin-right: 10px;
          font-weight: 500;
          word-wrap: break-word;
          word-break: break-all;
          text-align: right;
          flex-shrink: 0;
        }

        .CorpusDetailPanel-textarea-label {
          position: relative;
          top: 8px;
        }

        .CorpusDetailPanel-content {
          word-wrap: break-word;
          word-break: break-all;
        }

        .#{$ns}CorpusDetailPanel-textarea {
          width: 100%;
        }
      }
    }
  }
}

.#{$ns}CorpusDetailPanel-button-tooltip {
  z-index: 20000;
}
