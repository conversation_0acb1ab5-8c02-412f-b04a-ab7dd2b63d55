{"id": {"desc": "<p>组件 ID。默认不指定。指定则可用于在 option 或者 API 中引用组件。</p>\n"}, "toolbox": {"desc": "<p>使用在 toolbox 中的按钮。</p>\n<p>brush 相关的 toolbox 按钮有：</p>\n<ul>\n<li><code class=\"codespan\">&#39;rect&#39;</code>：开启矩形选框选择功能。</li>\n<li><code class=\"codespan\">&#39;polygon&#39;</code>：开启任意形状选框选择功能。</li>\n<li><code class=\"codespan\">&#39;lineX&#39;</code>：开启横向选择功能。</li>\n<li><code class=\"codespan\">&#39;lineY&#39;</code>：开启纵向选择功能。</li>\n<li><code class=\"codespan\">&#39;keep&#39;</code>：切换『单选』和『多选』模式。后者可支持同时画多个选框。前者支持单击清除所有选框。</li>\n<li><code class=\"codespan\">&#39;clear&#39;</code>：清空所有选框。</li>\n</ul>\n"}, "brushLink": {"desc": "<p>不同系列间，选中的项可以联动。</p>\n<p>参见如下效果（刷选一个 <code class=\"codespan\">scatter</code>，其他 <code class=\"codespan\">scatter</code> 以及 <code class=\"codespan\">parallel</code> 图都会有选中效果）：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=scatter-matrix&edit=1&reset=1\" width=\"800\" height=\"550\"><iframe />\n\n\n<p><code class=\"codespan\">brushLink</code> 配置项是一个数组，内容是 seriesIndex，指定了哪些 series 可以被联动。例如可以是：</p>\n<ul>\n<li><code class=\"codespan\">[3, 4, 5]</code> 表示 seriesIndex 为 <code class=\"codespan\">3</code>, <code class=\"codespan\">4</code>, <code class=\"codespan\">5</code> 的 series 可以被联动。</li>\n<li><code class=\"codespan\">&#39;all&#39;</code> 表示所有 series 都进行 brushLink。</li>\n<li><code class=\"codespan\">&#39;none&#39;</code> 或 <code class=\"codespan\">null</code> 或 <code class=\"codespan\">undefined</code> 表示不启用 brushLink 功能。</li>\n</ul>\n<p><strong>注意</strong></p>\n<p>brushLink 是通过 dataIndex 进行映射，所以需要保证，<strong>联动的每个系列的 <code class=\"codespan\">data</code> 都是 <code class=\"codespan\">index</code> 对应的</strong>。*</p>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    brush: {\n        brushLink: [0, 1]\n    },\n    series: [\n        {\n            type: &#39;bar&#39;\n            data: [232,    4434,    545,      654]     // data 有四个项\n        },\n        {\n            type: &#39;parallel&#39;,\n            data: [[4, 5], [3, 5], [66, 33], [99, 66]] // data 同样有四个项，两个系列的 data 是对应的。\n        }\n    ]\n};\n</code></pre>\n"}, "seriesIndex": {"desc": "<p>指定哪些 series 可以被刷选，可取值为：</p>\n<ul>\n<li><code class=\"codespan\">&#39;all&#39;</code>: 所有 series</li>\n<li><code class=\"codespan\">&#39;Array&#39;</code>: series index 列表</li>\n<li><code class=\"codespan\">&#39;number&#39;</code>: 某个 series index</li>\n</ul>\n"}, "geoIndex": {"desc": "<p>指定哪些 geo 可以被刷选。</p>\n<p>可以设置 <code class=\"codespan\">brush</code> 是『全局的』还是『属于坐标系的』。</p>\n<p><strong>全局 brush</strong></p>\n<p>在 echarts 实例中任意地方刷选。这是默认情况。如果没有指定为『坐标系 brush』，就是『全局 brush』。</p>\n<p><strong>坐标系 brush</strong></p>\n<p>在 指定的坐标系中刷选。选框可以跟随坐标系的缩放和平移（roam 和 dataZoom）而移动。</p>\n<p>坐标系 brush 实际更为常用，尤其是在 geo 中。</p>\n<p>通过指定 <a href=\"#brush.geoIndex\">brush.geoIndex</a> 或 <a href=\"#brush.xAxisIndex\">brush.xAxisIndex</a> 或 <a href=\"#brush.yAxisIndex\">brush.yAxisIndex</a> 来规定可以在哪些坐标系中进行刷选。</p>\n<p>这几个配置项的取值可以是：</p>\n<ul>\n<li><code class=\"codespan\">&#39;all&#39;</code>，表示所有</li>\n<li><code class=\"codespan\">number</code>，如 <code class=\"codespan\">0</code>，表示这个 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">Array</code>，如 <code class=\"codespan\">[0, 4, 2]</code>，表示指定这些 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">&#39;none&#39;</code> 或 <code class=\"codespan\">null</code> 或 <code class=\"codespan\">undefined</code>，表示不指定。</li>\n</ul>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    geo: {\n        ...\n    },\n    brush: {\n        geoIndex: &#39;all&#39;, // 只可以在所有 geo 坐标系中刷选，也就是上面定义的 geo 组件中。\n        ...\n    }\n};\n</code></pre>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    grid: [\n        {...}, // grid 0\n        {...}  // grid 1\n    ],\n    xAxis: [\n        {gridIndex: 1, ...}, // xAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // xAxis 1，属于 grid 0。\n    ],\n    yAxis: [\n        {gridIndex: 1, ...}, // yAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // yAxis 1，属于 grid 0。\n    ],\n    brush: {\n        xAxisIndex: [0, 1], // 只可以在 xAxisIndex 为 `0` 和 `1` 的 xAxis 所在的直角坐标系中刷选。\n        ...\n    }\n};\n</code></pre>\n"}, "xAxisIndex": {"desc": "<p>指定哪些 xAxisIndex 可以被刷选。</p>\n<p>可以设置 <code class=\"codespan\">brush</code> 是『全局的』还是『属于坐标系的』。</p>\n<p><strong>全局 brush</strong></p>\n<p>在 echarts 实例中任意地方刷选。这是默认情况。如果没有指定为『坐标系 brush』，就是『全局 brush』。</p>\n<p><strong>坐标系 brush</strong></p>\n<p>在 指定的坐标系中刷选。选框可以跟随坐标系的缩放和平移（roam 和 dataZoom）而移动。</p>\n<p>坐标系 brush 实际更为常用，尤其是在 geo 中。</p>\n<p>通过指定 <a href=\"#brush.geoIndex\">brush.geoIndex</a> 或 <a href=\"#brush.xAxisIndex\">brush.xAxisIndex</a> 或 <a href=\"#brush.yAxisIndex\">brush.yAxisIndex</a> 来规定可以在哪些坐标系中进行刷选。</p>\n<p>这几个配置项的取值可以是：</p>\n<ul>\n<li><code class=\"codespan\">&#39;all&#39;</code>，表示所有</li>\n<li><code class=\"codespan\">number</code>，如 <code class=\"codespan\">0</code>，表示这个 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">Array</code>，如 <code class=\"codespan\">[0, 4, 2]</code>，表示指定这些 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">&#39;none&#39;</code> 或 <code class=\"codespan\">null</code> 或 <code class=\"codespan\">undefined</code>，表示不指定。</li>\n</ul>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    geo: {\n        ...\n    },\n    brush: {\n        geoIndex: &#39;all&#39;, // 只可以在所有 geo 坐标系中刷选，也就是上面定义的 geo 组件中。\n        ...\n    }\n};\n</code></pre>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    grid: [\n        {...}, // grid 0\n        {...}  // grid 1\n    ],\n    xAxis: [\n        {gridIndex: 1, ...}, // xAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // xAxis 1，属于 grid 0。\n    ],\n    yAxis: [\n        {gridIndex: 1, ...}, // yAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // yAxis 1，属于 grid 0。\n    ],\n    brush: {\n        xAxisIndex: [0, 1], // 只可以在 xAxisIndex 为 `0` 和 `1` 的 xAxis 所在的直角坐标系中刷选。\n        ...\n    }\n};\n</code></pre>\n"}, "yAxisIndex": {"desc": "<p>指定哪些 yAxisIndex 可以被刷选。</p>\n<p>可以设置 <code class=\"codespan\">brush</code> 是『全局的』还是『属于坐标系的』。</p>\n<p><strong>全局 brush</strong></p>\n<p>在 echarts 实例中任意地方刷选。这是默认情况。如果没有指定为『坐标系 brush』，就是『全局 brush』。</p>\n<p><strong>坐标系 brush</strong></p>\n<p>在 指定的坐标系中刷选。选框可以跟随坐标系的缩放和平移（roam 和 dataZoom）而移动。</p>\n<p>坐标系 brush 实际更为常用，尤其是在 geo 中。</p>\n<p>通过指定 <a href=\"#brush.geoIndex\">brush.geoIndex</a> 或 <a href=\"#brush.xAxisIndex\">brush.xAxisIndex</a> 或 <a href=\"#brush.yAxisIndex\">brush.yAxisIndex</a> 来规定可以在哪些坐标系中进行刷选。</p>\n<p>这几个配置项的取值可以是：</p>\n<ul>\n<li><code class=\"codespan\">&#39;all&#39;</code>，表示所有</li>\n<li><code class=\"codespan\">number</code>，如 <code class=\"codespan\">0</code>，表示这个 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">Array</code>，如 <code class=\"codespan\">[0, 4, 2]</code>，表示指定这些 index 所对应的坐标系。</li>\n<li><code class=\"codespan\">&#39;none&#39;</code> 或 <code class=\"codespan\">null</code> 或 <code class=\"codespan\">undefined</code>，表示不指定。</li>\n</ul>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    geo: {\n        ...\n    },\n    brush: {\n        geoIndex: &#39;all&#39;, // 只可以在所有 geo 坐标系中刷选，也就是上面定义的 geo 组件中。\n        ...\n    }\n};\n</code></pre>\n<p>例如：</p>\n<pre><code class=\"lang-javascript\">option = {\n    grid: [\n        {...}, // grid 0\n        {...}  // grid 1\n    ],\n    xAxis: [\n        {gridIndex: 1, ...}, // xAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // xAxis 1，属于 grid 0。\n    ],\n    yAxis: [\n        {gridIndex: 1, ...}, // yAxis 0，属于 grid 1。\n        {gridIndex: 0, ...}  // yAxis 1，属于 grid 0。\n    ],\n    brush: {\n        xAxisIndex: [0, 1], // 只可以在 xAxisIndex 为 `0` 和 `1` 的 xAxis 所在的直角坐标系中刷选。\n        ...\n    }\n};\n</code></pre>\n"}, "brushType": {"desc": "<p>默认的刷子类型。</p>\n<ul>\n<li><code class=\"codespan\">&#39;rect&#39;</code>：矩形选框。</li>\n<li><code class=\"codespan\">&#39;polygon&#39;</code>：任意形状选框。</li>\n<li><code class=\"codespan\">&#39;lineX&#39;</code>：横向选择。</li>\n<li><code class=\"codespan\">&#39;lineY&#39;</code>：纵向选择。</li>\n</ul>\n"}, "brushMode": {"desc": "<p>默认的刷子的模式。可取值为：</p>\n<ul>\n<li><code class=\"codespan\">&#39;single&#39;</code>：单选。</li>\n<li><code class=\"codespan\">&#39;multiple&#39;</code>：多选。</li>\n</ul>\n"}, "transformable": {"desc": "<p>已经选好的选框是否可以被调整形状或平移。</p>\n"}, "brushStyle": {"desc": "<p>选框的默认样式，值为：</p>\n<pre><code class=\"lang-javascript\">{\n    borderWidth: 1,\n    color: &#39;rgba(120,140,180,0.3)&#39;,\n    borderColor: &#39;rgba(120,140,180,0.8)&#39;\n},\n</code></pre>\n"}, "throttleType": {"desc": "<p>默认情况，刷选或者移动选区的时候，会不断得发 <code class=\"codespan\">brushSelected</code> 事件，从而告诉外界选中的内容。</p>\n<p>但是频繁的事件可能导致性能问题，或者动画效果很差。所以 brush 组件提供了 <a href=\"#brush.throttleType\">brush.throttleType</a>，<a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 来解决这个问题。</p>\n<p>throttleType 取值可以是：</p>\n<ul>\n<li><code class=\"codespan\">&#39;debounce&#39;</code>：表示只有停止动作了（即一段时间没有操作了），才会触发事件。时间阈值由 <a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 指定。</li>\n<li><code class=\"codespan\">&#39;fixRate&#39;</code>：表示按照一定的频率触发事件，时间间隔由 <a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 指定。</li>\n</ul>\n"}, "throttleDelay": {"desc": "<p>默认为 <code class=\"codespan\">0</code> 表示不开启 throttle。</p>\n<p>默认情况，刷选或者移动选区的时候，会不断得发 <code class=\"codespan\">brushSelected</code> 事件，从而告诉外界选中的内容。</p>\n<p>但是频繁的事件可能导致性能问题，或者动画效果很差。所以 brush 组件提供了 <a href=\"#brush.throttleType\">brush.throttleType</a>，<a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 来解决这个问题。</p>\n<p>throttleType 取值可以是：</p>\n<ul>\n<li><code class=\"codespan\">&#39;debounce&#39;</code>：表示只有停止动作了（即一段时间没有操作了），才会触发事件。时间阈值由 <a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 指定。</li>\n<li><code class=\"codespan\">&#39;fixRate&#39;</code>：表示按照一定的频率触发事件，时间间隔由 <a href=\"#brush.throttleDelay\">brush.throttleDelay</a> 指定。</li>\n</ul>\n"}, "removeOnClick": {"desc": "<p>在 <a href=\"#brush.brushMode\">brush.brushMode</a> 为 <code class=\"codespan\">&#39;single&#39;</code> 的情况下，是否支持『单击清除所有选框』。</p>\n"}, "inBrush": {"desc": "<p>定义 <strong>在选中范围中</strong> 的视觉元素。</p>\n<p>可选的视觉元素有：</p>\n<ul>\n<li><code class=\"codespan\">symbol</code>: 图元的图形类别。</li>\n<li><code class=\"codespan\">symbolSize</code>: 图元的大小。</li>\n<li><code class=\"codespan\">color</code>: 图元的颜色。</li>\n<li><code class=\"codespan\">colorAlpha</code>: 图元的颜色的透明度。</li>\n<li><code class=\"codespan\">opacity</code>: 图元以及其附属物（如文字标签）的透明度。</li>\n<li><code class=\"codespan\">colorLightness</code>: 颜色的明暗度，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n<li><code class=\"codespan\">colorSaturation</code>: 颜色的饱和度，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n<li><code class=\"codespan\">colorHue</code>: 颜色的色调，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n</ul>\n<p>大多数情况下，inBrush 可以不指定，维持本来的视觉配置。</p>\n"}, "outOfBrush": {"desc": "<p>定义 <strong>在选中范围外</strong> 的视觉元素。</p>\n<p>可选的视觉元素有：</p>\n<ul>\n<li><code class=\"codespan\">symbol</code>: 图元的图形类别。</li>\n<li><code class=\"codespan\">symbolSize</code>: 图元的大小。</li>\n<li><code class=\"codespan\">color</code>: 图元的颜色。</li>\n<li><code class=\"codespan\">colorAlpha</code>: 图元的颜色的透明度。</li>\n<li><code class=\"codespan\">opacity</code>: 图元以及其附属物（如文字标签）的透明度。</li>\n<li><code class=\"codespan\">colorLightness</code>: 颜色的明暗度，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n<li><code class=\"codespan\">colorSaturation</code>: 颜色的饱和度，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n<li><code class=\"codespan\">colorHue</code>: 颜色的色调，参见 <a href=\"https://en.wikipedia.org/wiki/HSL_and_HSV\" target=\"_blank\">HSL</a>。</li>\n</ul>\n<p><strong>注意</strong>，如果 outOfBrush 没有指定，默认会设置 color: <code class=\"codespan\">&#39;#ddd&#39;</code>，如果你不想要这个color，比如可以\n换成：</p>\n<pre><code class=\"lang-javascript\">brush: {\n    ...,\n    outOfBrush: {\n        colorAlpha: 0.1\n    }\n}\n</code></pre>\n"}, "z": {"desc": "<p>brush 选框的 z-index。当有和不想管组件有不正确的重叠时，可以进行调整。</p>\n"}}