import React from 'react';
import {Renderer} from 'amis';
import {FormControlProps} from 'amis-core';

export interface CarouselProps extends FormControlProps {
  label?: string;
  name: string;
  dataSource?: 'static' | 'dynamic';
  options?: Array<{
    image: string;
    title?: string;
    description?: string;
    href?: string;
    blank?: boolean;
  }>;
  source?: any;
  imageField?: string;
  titleField?: string;
  descriptionField?: string;
  hrefField?: string;
  auto?: boolean;
  interval?: number;
  duration?: number;
  width?: string | number;
  height?: string | number;
  controls?: Array<'dots' | 'arrows'>;
  controlsTheme?: 'light' | 'dark';
  animation?: 'fade' | 'slide';
  thumbMode?: 'contain' | 'cover';
  multiple?: {
    enable: boolean;
    count: number;
  };
  value?: any;
  static?: boolean;
  className?: string;
  itemClassName?: string;
}

@Renderer({
  type: 'my-carousel',
  name: 'my-carousel'
})
export class MyCarouselRenderer extends React.PureComponent<CarouselProps> {
  static defaultProps = {
    auto: true,
    interval: 3000,
    duration: 500,
    controls: ['dots', 'arrows'],
    controlsTheme: 'light',
    animation: 'fade',
    height: 300,
    thumbMode: 'contain',
    dataSource: 'static'
  };

  constructor(props: CarouselProps) {
    super(props);
  }

    // 添加删除轮播项的方法
    handleDeleteItem = (index: number) => {
      console.log('删除轮播项', index);
      const { options, onChange } = this.props as any;
      
      // 如果只有一项，不允许删除
      if (!options || options.length <= 1) {
        return false;
      }
      
      // 创建新的选项数组，排除要删除的项
      const newOptions = options.filter((_: any, i: number) => i !== index);
      
      // 调用onChange更新数据
      if (onChange) {
        onChange({
          ...this.props,
          options: newOptions
        });
        return true;
      }
      
      return false;
    }

  // 处理动态数据源的数据转换
  transformDynamicData(data: any[]) {
    const {
      imageField = 'image',
      titleField = 'title',
      descriptionField = 'description',
      hrefField = 'href'
    } = this.props;

    if (!data || !Array.isArray(data)) {
      return [];
    }

    return data.map(item => ({
      image: item[imageField] || '',
      title: titleField ? item[titleField] || '' : '',
      description: descriptionField ? item[descriptionField] || '' : '',
      href: hrefField ? item[hrefField] || '' : '',
      blank: false
    }));
  }

  render() {
    const {
      static: isStatic,
      name,
      label,
      options,
      dataSource,
      source,
      auto,
      interval,
      duration,
      width,
      height,
      controls,
      controlsTheme,
      animation,
      thumbMode,
      multiple,
      className,
      itemClassName,
      render,
      value,
      data
    } = this.props as any;

    console.log('controls', controls);
    // 构建基本配置
    const config: any = {
      type: 'carousel',
      name,
      label,
      options: options || [
        {
          image:
            '',
          title: '标题1'
        }
      ],
      auto: auto !== undefined ? auto : true,
      interval: interval || 3000,
      duration: duration || 500,
      width: width || '100%',
      height: height || 300,
      controls: controls,
      controlsTheme: controlsTheme || 'light',
      animation: animation || 'fade',
      thumbMode: thumbMode || 'contain',
      className: className,
      itemClassName: itemClassName
    };

    // 处理动态数据源
    if (dataSource === 'dynamic' && source) {
      config.source = source;
      
      // 添加数据映射
      config.adaptor = (payload: any) => {
        const sourceData = payload.data || [];
        return {
          ...payload,
          data: this.transformDynamicData(sourceData)
        };
      };
    }

    // 处理静态展示模式
    if (isStatic) {
      if (render) {
        return render('static', {
          type: 'static',
          label,
          name,
          value: value || (data && data[name]) || '--'
        });
      }

      // 备用渲染方法：直接使用div
      return (
        <div className="static-value">
          <div className="static-label">{label || name}</div>
          <div className="static-content">
            {value || (data && data[name]) || '--'}
          </div>
        </div>
      );
    } else {
      const allConfig = {
        ...this.props,
        ...config
      };

      if (render) {
        return render('my-carousel', allConfig);
      }

      return <></>;
    }
  }
}
