import React, {FC, useEffect, useState} from 'react';
import './index.scss';
import {Input, Button} from 'amis-ui';
import {Icon} from 'amis';
import {
  javaApplication,
  updateApplicationReleaseOrEnable,
  getPublishApplicationGroup,
  getPublishApplicationGroupList,
  updatePublishApplicationGroup,
  getComponentClassificationPage
} from '@/utils/api/api'; // 添加 API 引入
import AMISRenderer from '@/component/AMISRenderer'; // 导入确认弹框组件
import {toast} from 'amis'; // 导入 toast
import {EditEntryIcon} from '@/utils/schemaDataSet/EditEntryIcon';

import 'amis/lib/themes/cxd.css';
import 'amis/lib/themes/dark.css';
import 'amis/lib/helper.css';
import 'amis/sdk/iconfont.css';
import app from '@/App';
import {match} from 'path-to-regexp';
import {color} from 'echarts';
// 定义 Props 类型
interface AppPublishProps {
  appId: string;
  store: any; // 根据实际情况调整类型
  history: any; // 根据实际情况调整类型
  pageData: any;
}

const AppPublish: FC<AppPublishProps> = props => {
  const [appData, setAppData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [appName, setAppName] = useState<string>('');

  // 确认弹框相关状态
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const [confirmAction, setConfirmAction] = useState<{
    type:
      | 'enable'
      | 'disable'
      | 'publish'
      | 'unpublish'
      | 'publishbuild'
      | 'unpublishbuild';
    title: string;
    content: string;
    onConfirm: () => void;
  } | null>(null);

  // 分组选择弹框相关状态
  const [showGroupSelectDialog, setShowGroupSelectDialog] =
    useState<boolean>(false);
  const [groupList, setGroupList] = useState<any[]>([]);
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const [showCreateTemplateModal, setShowCreateTemplateModal] =
    React.useState(false);

  const [categories, setCategories] = React.useState<any[]>([]);
  const [showEditIconDialog, setShowEditIconDialog] = React.useState(false);

  // 应用模板发布相关状态
  const [templateFormData, setTemplateFormData] = React.useState<any>({
    name: '',
    coverImage: '',
    classificationId: '',
    remark: ''
  });

  // 解析应用图标内容，提取SVG、颜色和背景色
  const parseAppIcon = (iconHtml: string) => {
    if (!iconHtml) {
      return {
        icon: '',
        iconColor: '#000000',
        iconBg: '#F5F7FA'
      };
    }

    // 如果是完整的span元素，需要解析其中的内容
    if (iconHtml.includes('<span')) {
      // 提取style属性中的颜色和背景色
      const colorMatch = iconHtml.match(/color:\s*([^;]+)/);
      const bgMatch = iconHtml.match(/background:\s*([^;]+)/);

      // 提取SVG内容
      const svgMatch = iconHtml.match(/<svg[^>]*>.*?<\/svg>/);

      return {
        icon: svgMatch ? svgMatch[0] : '',
        iconColor: colorMatch ? colorMatch[1] : '#000000',
        iconBg: bgMatch ? bgMatch[1] : '#F5F7FA'
      };
    }

    // 如果是纯SVG或图标类名，直接返回
    return {
      icon: iconHtml,
      iconColor: '#000000',
      iconBg: '#F5F7FA'
    };
  };

  // 处理图标保存
  const handleIconSave = (values: any) => {
    console.log('处理图标保存:', values);

    if (!values || !Array.isArray(values) || values.length === 0) {
      console.log('无效的数据格式，取消更新');
      return;
    }

    const iconData = values[0];
    if (!iconData) {
      console.log('无图标数据，取消更新');
      return;
    }

    // 获取当前的图标数据作为默认值
    const currentIconData = parseAppIcon(appData?.logo || '');

    // 清理图标数据，移除可能的引号
    let cleanIcon = iconData.icon || '';
    if (typeof cleanIcon === 'string') {
      // 移除可能的外层引号
      cleanIcon = cleanIcon.replace(/^['"]|['"]$/g, '').trim();
    }

    // 使用新的图标数据，但保留原有的颜色和背景色（如果用户没有修改）
    const finalIconColor =
      iconData.iconColor || currentIconData.iconColor || '#000000';
    const finalIconBg = iconData.iconBg || currentIconData.iconBg || '#F5F7FA';

    // 构建新的图标HTML结构（类似EntryItemsManager的格式）
    let newCoverImage = '';
    if (cleanIcon) {
      if (cleanIcon.includes('<svg')) {
        // SVG图标：构建完整的span结构
        newCoverImage = `<span style="color:${finalIconColor};background:${finalIconBg};width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle">${cleanIcon}</span>`;
      } else {
        // 字体图标：构建完整的span结构
        newCoverImage = `<span style="color:${finalIconColor};background:${finalIconBg};width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:32px;vertical-align:middle"><i class="${cleanIcon}"></i></span>`;
      }
    }

    // 更新模板表单数据中的图标信息
    setTemplateFormData((prev: any) => ({
      ...prev,
      coverImage: newCoverImage,
      iconColor: finalIconColor,
      iconBg: finalIconBg
    }));

    // 关闭图标编辑对话框
    setShowEditIconDialog(false);

    toast.success('图标设置成功');
  };

  // 生成访问地址
  const generateVisitUrl = () => {
    if (!appData) return '';
    const url = `#/app${props.appId}/workbench`;
    const baseUrl = window.location.origin + window.location.pathname;
    return baseUrl + url;
  };

  // 显示确认弹框
  const showConfirm = (
    type:
      | 'enable'
      | 'disable'
      | 'publish'
      | 'unpublish'
      | 'publishbuild'
      | 'unpublishbuild',
    onConfirm: () => void
  ) => {
    const configs = {
      enable: {
        title: '启用应用',
        content:
          '启用应用前，请先对相应的应用进行充分的测试，测试通过后启用应用。'
      },
      disable: {
        title: '你确定要停用该应用吗',
        content: '停用后应用将无法提交数据，如需再次使用请重新启用应用。'
      },
      publish: {
        title: '启用应用',
        content:
          '启用应用前，请先对相应的应用进行充分的测试，测试通过后启用应用。'
      },
      unpublish: {
        title: '你确定要停用该应用吗',
        content: '停用后应用将无法提交数据，如需再次使用请重新启用应用。'
      },
      publishbuild: {
        title: '发布应用',
        content:
          '确定要发布该应用到搭建中心吗？发布后组织内成员可在搭建中心访问该应用。'
      },
      unpublishbuild: {
        title: '移除应用',
        content:
          '确定要从搭建中心移除该应用吗？移除后该应用将不再在搭建中心显示。'
      }
    };

    const config = configs[type];
    setConfirmAction({
      type,
      title: config.title,
      content: config.content,
      onConfirm
    });
    setShowConfirmDialog(true);
  };

  // 获取分组列表
  const fetchGroupList = async () => {
    try {
      const response = await getPublishApplicationGroupList({});
      if (response.code === 0) {
        setGroupList(response.data || []);
      } else {
        toast.error('获取分组列表失败');
      }
    } catch (err) {
      toast.error('获取分组列表失败');
    }
  };

  // 获取应用模板分组列表
  const fetchTemplateCategories = async () => {
    try {
      const response = await getComponentClassificationPage({
        pageNo: 1,
        pageSize: 10,
        type: 2 // type=2 表示应用模板分类
      });
      if (response.code === 0) {
        setCategories(response.data.list || []);
      } else {
        toast.error('获取分组列表失败');
      }
    } catch (err) {
      toast.error('获取分组列表失败');
    }
  };

  // 显示分组选择弹框
  const showGroupSelectModal = async () => {
    await fetchGroupList();
    setShowGroupSelectDialog(true);
  };

  // 处理分组选择确认
  const handleGroupSelectConfirm = async () => {
    if (!selectedGroupId) {
      toast.error('请选择一个分组');
      return;
    }

    try {
      const appId = Number(props.appId);
      if (isNaN(appId)) {
        toast.error('无效的应用 ID');
        return;
      }

      // 获取选中分组的详细信息
      const groupResponse = await getPublishApplicationGroup({
        id: selectedGroupId
      });
      if (groupResponse.code !== 0) {
        toast.error('获取分组信息失败');
        return;
      }

      const groupData = groupResponse.data;
      const currentApplicationIds = groupData.applicationIds
        ? groupData.applicationIds.split(',').filter((id: string) => id.trim())
        : [];

      // 如果应用不在分组中，则添加
      if (!currentApplicationIds.includes(appId.toString())) {
        currentApplicationIds.push(appId.toString());

        // 更新分组
        await updatePublishApplicationGroup({
          id: selectedGroupId,
          name: groupData.name,
          userIds: groupData.userIds,
          applicationIds: currentApplicationIds.join(','),
          sort: groupData.sort
        });

        // 更新应用发布状态
        await updateApplicationReleaseOrEnable({
          id: appId,
          isEnable: appData?.isEnabled ? 1 : 0,
          isRelease: 1,
          isPublishBuildCenter: appData?.isPublishBuildCenter ? 1 : 0
        });

        setAppData((prev: any) => ({
          ...prev,
          isRelease: true
        }));

        toast.success('应用已成功发布到工作台');
      }

      setShowGroupSelectDialog(false);
      setSelectedGroupId(null);
    } catch (err) {
      toast.error('发布到工作台失败');
    }
  };

  // 复制链接到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        console.log('文本已复制到剪贴板');
      },
      err => {
        console.error('无法复制文本: ', err);
      }
    );
  };

  // 获取应用数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        const appId = Number(props.appId); // 确保 appId 是数字类型
        if (isNaN(appId)) {
          toast.error('无效的应用 ID');
          return;
        }
        await fetchAppStatus(appId); // 传递转换后的 appId
        await fetchTemplateCategories(); // 获取应用模板分组数据
      } catch (err) {
        toast.error('获取应用数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [props.appId]);

  // 新增方法：更新应用启用状态
  const updateAppEnableStatus = async (
    isEnabled: boolean,
    isRelease: boolean,
    isPublishBuildCenter: boolean
  ) => {
    try {
      const appId = Number(props.appId); // 确保 appId 是数字类型
      if (isNaN(appId)) {
        toast.error('无效的应用 ID');
        return;
      }

      // 在API调用前保存当前状态，用于判断toast
      const currentIsEnabled = appData?.isEnabled || false;
      const currentIsRelease = appData?.isRelease || false;
      const currentIsPublishBuildCenter =
        appData?.isPublishBuildCenter || false;

      await updateApplicationReleaseOrEnable({
        id: appId, // 传递转换后的 appId
        isEnable: isEnabled ? 1 : 0,
        isRelease: isRelease ? 1 : 0,
        isPublishBuildCenter: isPublishBuildCenter ? 1 : 0
      });

      setAppData((prev: any) => ({
        ...prev,
        isEnabled: isEnabled,
        isRelease: isRelease, // 根据 isRelease 更新 isRelease 状态
        isPublishBuildCenter: isPublishBuildCenter
      }));

      // 显示成功提示 - 基于之前保存的状态判断
      if (isEnabled && !currentIsEnabled) {
        toast.success('应用启用成功');
      } else if (!isEnabled && currentIsEnabled) {
        toast.success('停用成功');
      } else if (!isRelease && currentIsRelease) {
        toast.success('应用已从工作台移除');
      } else if (!isPublishBuildCenter && currentIsPublishBuildCenter) {
        toast.success('应用已从搭建中心移除');
      } else if (isPublishBuildCenter && !currentIsPublishBuildCenter) {
        toast.success('应用已成功发布到搭建中心');
      }
    } catch (err) {
      toast.error('更新应用状态失败');
    }
  };

  // 新增方法：获取应用状态
  const fetchAppStatus = async (appId: number) => {
    try {
      const response = await javaApplication({id: appId}); // 传递转换后的 appId
      if (response.code === 0) {
        // 保存完整的应用数据，包括logo等字段
        setAppData({
          ...response.data,
          isEnabled: response.data.isEnable === 1,
          isRelease: response.data.isRelease === 1,
          isPublishBuildCenter: response.data.isPublishBuildCenter === 1 // 添加搭建中心状态初始化
        });
        setAppName(response.data.name || ''); // 设置应用名称

        // 初始化模板表单数据
        setTemplateFormData({
          name: response.data.name || '',
          coverImage: response.data.logo || '',
          classificationId: '',
          remark: response.data.description || '',
          iconColor: '#000000',
          iconBg: '#F5F7FA'
        });
      } else {
        toast.error(response.msg);
      }
    } catch (err) {
      toast.error('获取应用状态失败');
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div>
      <div className="appTitleContainer">
        {/* 标题框 */}
        <div className="appTitleBox">
          <div className="appTitle">应用发布</div>
          <div className="appDescription">
            发布应用到工作台，让应用访问更加快捷
          </div>
        </div>
      </div>

      <div className="appPublishBox">
        <div className="appPublishContainer">
          {/* 应用启用 */}
          <div className="appPublishSection">
            <div className="appPublishLeft">
              <h3>应用启用</h3>
              <p className="appPublishDescription">
                应用已启用，为可用状态，你可以通过下方渠道发布给成员使用。
              </p>
              <div className="appPublishRow">
                <Button
                  type="button"
                  style={{
                    backgroundColor: appData?.isEnabled ? '#30BF13' : '#F23D3D',
                    color: 'white',
                    border: 'none',
                    padding: '5px 10px',
                    borderRadius: '4px'
                  }}
                >
                  {appData?.isEnabled ? '已启用' : '未启用'}
                </Button>
              </div>
            </div>
            <div className="appPublishRight">
              <Button
                type="button"
                style={{
                  backgroundColor: appData?.isEnabled ? '#F23D3D' : '#30BF13',
                  color: 'white',
                  border: 'none',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}
                onClick={() => {
                  const actionType = appData?.isEnabled ? 'disable' : 'enable';
                  showConfirm(actionType, () => {
                    updateAppEnableStatus(
                      !appData?.isEnabled,
                      appData?.isRelease,
                      appData?.isPublishBuildCenter
                    );
                  });
                }}
              >
                {appData?.isEnabled ? '停用' : '启用'}
              </Button>
            </div>
          </div>
          <h2 style={{fontWeight: 'bold'}}>组织内发布</h2>
          {/* 组织内发布 */}
          <div className="appPublishSection">
            <div className="appPublishLeft">
              <h3>组织工作台</h3>
              <p className="appPublishDescription">
                应用发布后，出现在应用名称工作台，让应用访问更加快捷。
              </p>
              <div className="appPublishRow">
                <Button
                  type="button"
                  style={{
                    backgroundColor: appData?.isRelease ? '#30BF13' : '#F23D3D',
                    color: 'white',
                    border: 'none',
                    padding: '5px 10px',
                    borderRadius: '4px'
                  }}
                >
                  {appData?.isRelease ? '已发布' : '未发布'}
                </Button>
                <span className="appPublishSeparator"></span>
                {/* <span className="appPublishText">发布到：工作台-{appData?.groupName || '未分组'}</span> */}
              </div>
            </div>
            <div className="appPublishRight">
              <Button
                type="button"
                style={{
                  backgroundColor: appData?.isRelease ? '#F23D3D' : '#30BF13',
                  color: 'white',
                  border: 'none',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}
                onClick={() => {
                  if (appData?.isRelease) {
                    // 从工作台移除 - 显示确认弹框
                    showConfirm('unpublish', () => {
                      updateAppEnableStatus(
                        appData?.isEnabled,
                        !appData?.isRelease,
                        appData?.isPublishBuildCenter
                      );
                    });
                  } else {
                    // 添加至工作台 - 显示分组选择弹框
                    showGroupSelectModal();
                  }
                }}
              >
                {appData?.isRelease ? '从工作台移除' : '发布'}
              </Button>
            </div>
          </div>

          <div className="appPublishSection">
            <div className="appPublishLeft">
              <h3>搭建中心</h3>
              <p className="appPublishDescription">
                发布后应用会出现在搭建平台的"搭建中心"，默认组织内全员可见。
              </p>
              <div className="appPublishRow">
                <Button
                  type="button"
                  style={{
                    backgroundColor: appData?.isPublishBuildCenter
                      ? '#30BF13'
                      : '#F23D3D',
                    color: 'white',
                    border: 'none',
                    padding: '5px 10px',
                    borderRadius: '4px'
                  }}
                >
                  {appData?.isPublishBuildCenter ? '已发布' : '未发布'}
                </Button>
                <span className="appPublishSeparator"></span>
                <span className="appPublishText">
                  发布到：搭建平台-搭建中心
                </span>
              </div>
            </div>
            <div className="appPublishRight">
              <Button
                type="button"
                style={{
                  backgroundColor: appData?.isPublishBuildCenter
                    ? '#F23D3D'
                    : '#30BF13',
                  color: 'white',
                  border: 'none',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}
                onClick={() => {
                  // 使用正确的类型 - 搭建中心发布/取消发布
                  const actionType = appData?.isPublishBuildCenter
                    ? 'unpublishbuild'
                    : 'publishbuild';
                  showConfirm(actionType, () => {
                    // 更新应用状态，只改变isPublishBuildCenter的值
                    updateAppEnableStatus(
                      appData?.isEnabled,
                      appData?.isRelease,
                      !appData?.isPublishBuildCenter
                    );
                  });
                }}
              >
                {appData?.isPublishBuildCenter ? '从搭建中心移除' : '发布'}
              </Button>
            </div>
          </div>

          {props.store.userInfo?.hasAppTemplateAuth && (
            <div className="appPublishSection">
              <div className="appPublishLeft">
                <h3>应用模板</h3>
                <p className="appPublishDescription">
                  发布后应用会出现在应用搭建的"应用模板"。
                </p>
              </div>
              <div className="appPublishRight">
                <Button
                  type="button"
                  style={{
                    backgroundColor: appData?.isPublishBuildCenter
                      ? '#F23D3D'
                      : '#30BF13',
                    color: 'white',
                    border: 'none',
                    padding: '5px 10px',
                    borderRadius: '4px'
                  }}
                  onClick={async () => {
                    // 获取分组数据
                    await fetchTemplateCategories();
                    console.log('appData', appData);

                    // 重新初始化模板表单数据，确保显示当前应用的图标
                    setTemplateFormData({
                      name: appData?.name || '',
                      coverImage: appData?.logo || '',
                      classificationId: '',
                      remark: appData?.description || '',
                      iconColor: '#000000',
                      iconBg: '#F5F7FA'
                    });

                    // 更新DOM中的图标显示
                    setTimeout(() => {
                      const iconPreview =
                        document.getElementById('app-icon-preview');
                      if (iconPreview && appData?.logo) {
                        iconPreview.style.color = '#000000';
                        iconPreview.style.background = '#F5F7FA';

                        if (appData.logo.includes('<svg')) {
                          iconPreview.innerHTML = appData.logo;
                        } else {
                          iconPreview.innerHTML = `<i class="${appData.logo}"></i>`;
                        }
                      } else if (iconPreview) {
                        iconPreview.innerHTML = '';
                      }
                    }, 100);

                    setShowCreateTemplateModal(true);
                  }}
                >
                  发布
                </Button>
              </div>
            </div>
          )}

          <h2 style={{fontWeight: 'bold'}}>组织外发布</h2>
          {/* 组织外发布 */}

          {/*没实现功能*/}
          <div className="appPublishSection">
            <div className="appPublishLeft">
              <div className="appPublishSubSection">
                <h3>钉钉工作台</h3>
                <p className="appPublishDescription">
                  应用发布后，出现在钉钉工作台，默认组织内全员可见。
                </p>
                <div className="appPublishRow">
                  <Button
                    type="button"
                    style={{
                      backgroundColor: appData?.isDingTalkPublished
                        ? '#30BF13'
                        : '#F23D3D',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '4px'
                    }}
                  >
                    {appData?.isDingTalkPublished ? '已发布' : '未发布'}
                  </Button>
                  <span className="appPublishSeparator"></span>
                  <span className="appPublishText">
                    发布到：钉钉工作台-分组名称
                  </span>
                </div>
              </div>
            </div>
            <div className="appPublishRight">
              <Button
                type="button"
                style={{
                  backgroundColor: appData?.isDingTalkAddedToWorkbench
                    ? '#F23D3D'
                    : '#30BF13',
                  color: 'white',
                  border: 'none',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}
                onClick={() => {
                  // 模拟切换添加至工作台状态
                  setAppData((prev: any) => ({
                    ...prev,
                    isDingTalkAddedToWorkbench:
                      !prev?.isDingTalkAddedToWorkbench,
                    isDingTalkPublished: !prev?.isDingTalkAddedToWorkbench // 同步 isDingTalkPublished 状态
                  }));
                }}
              >
                {appData?.isDingTalkAddedToWorkbench
                  ? '从钉钉工作台移除'
                  : '添加至钉钉工作台'}
              </Button>
              <Button
                type="button"
                className="appPublishButton" // 添加类名以便统一样式
              >
                查看
              </Button>
            </div>
          </div>

          <div className="appPublishSection">
            <div className="appPublishLeft">
              <div className="appPublishSubSection">
                <h3>企业微信工作台</h3>
                <p className="appPublishDescription">
                  应用发布后，出现在企业微信工作台，可前往工作台访问。
                </p>
                <div className="appPublishRow">
                  <Button
                    type="button"
                    style={{
                      backgroundColor: appData?.isWeChatPublished
                        ? '#30BF13'
                        : '#F23D3D',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '4px'
                    }}
                  >
                    {appData?.isWeChatPublished ? '已发布' : '未发布'}
                  </Button>
                  <span className="appPublishSeparator"></span>
                  <span className="appPublishText">
                    发布到：企业微信工作台-分组名称
                  </span>
                </div>
              </div>
            </div>
            <div className="appPublishRight">
              <Button
                type="button"
                style={{
                  backgroundColor: appData?.isWeChatAddedToWorkbench
                    ? '#F23D3D'
                    : '#30BF13',
                  color: 'white',
                  border: 'none',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}
                onClick={() => {
                  // 模拟切换添加至工作台状态
                  setAppData((prev: any) => ({
                    ...prev,
                    isWeChatAddedToWorkbench: !prev?.isWeChatAddedToWorkbench,
                    isWeChatPublished: !prev?.isWeChatAddedToWorkbench // 同步 isWeChatPublished 状态
                  }));
                }}
              >
                {appData?.isWeChatAddedToWorkbench
                  ? '从企业微信工作台移除'
                  : '添加至企业微信工作台'}
              </Button>
              <Button
                type="button"
                className="appPublishButton" // 添加类名以便统一样式
              >
                查看
              </Button>
            </div>
          </div>

          {/* 应用访问链接 */}
          <div className="appPublishSection">
            <div className="appPublishLeft">
              <h3>应用访问链接</h3>
              <p className="appPublishDescription">
                如需更改，可到【应用设置-基础设置】中自定义访问地址{' '}
                <a
                  onClick={() => {
                    props.history.push(`/app${props.appId}/admin/appSetPage`);
                  }}
                >
                  去设置
                </a>
                。
              </p>
              <Input
                value={generateVisitUrl()}
                readOnly
                className="appPublishInput"
              />
            </div>
            <div className="appPublishRight">
              {/*没实现功能*/}
              <Button type="button" className="appPublishButton">
                二维码下载
              </Button>

              {/*没实现功能*/}
              <Button type="button" className="appPublishButton">
                访问链接
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 确认弹框 */}
      {showConfirmDialog && confirmAction && (
        <AMISRenderer
          show={showConfirmDialog}
          schema={{
            type: 'dialog',
            title: {
              type: 'html',
              html: `<b>${confirmAction.title}</b>`
            },
            body: {
              type: 'form',
              body: [
                {
                  type: 'tpl',
                  tpl: confirmAction.content,
                  className: 'py-2'
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel',
                onClick: () => setShowConfirmDialog(false)
              },
              {
                type: 'button',
                label: (() => {
                  switch (confirmAction.type) {
                    case 'enable':
                      return '启用';
                    case 'disable':
                      return '停用';
                    case 'publish':
                      return '启用';
                    case 'unpublish':
                      return '停用';
                    default:
                      return '确定';
                  }
                })(),
                level:
                  confirmAction.type === 'enable' ||
                  confirmAction.type === 'publish'
                    ? 'primary'
                    : 'danger',
                actionType: 'confirm',
                onClick: () => {
                  confirmAction.onConfirm();
                  setShowConfirmDialog(false);
                }
              }
            ]
          }}
          onClose={() => setShowConfirmDialog(false)}
        />
      )}

      {/* 分组选择弹框 */}
      {showGroupSelectDialog && (
        <AMISRenderer
          show={showGroupSelectDialog}
          schema={{
            type: 'dialog',
            title: {
              type: 'html',
              html: '<b>发布到工作台</b>'
            },
            body: {
              type: 'form',
              body: [
                {
                  type: 'tpl',
                  tpl: '发布到工作台后，成员可以从工作台中直接访问应用<br><br>',
                  className: 'py-2'
                },
                {
                  type: 'input-text',
                  label: '应用',
                  name: 'appName',
                  value: appName,
                  disabled: true,
                  required: true
                },
                {
                  type: 'select',
                  label: '工作台分组',
                  name: 'groupId',
                  placeholder: '请选择',
                  required: true,
                  options: groupList.map(group => ({
                    label: group.name,
                    value: group.id
                  })),
                  value: selectedGroupId,
                  onChange: (value: number) => setSelectedGroupId(value)
                },
                {
                  type: 'tpl',
                  tpl: '<b>如需调整工作台分组，请到 工作台</b>'
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel',
                onClick: () => {
                  setShowGroupSelectDialog(false);
                  setSelectedGroupId(null);
                }
              },
              {
                type: 'button',
                label: '发布',
                level: 'primary',
                actionType: 'confirm',
                onClick: handleGroupSelectConfirm
              }
            ]
          }}
          onClose={() => {
            setShowGroupSelectDialog(false);
            setSelectedGroupId(null);
          }}
        />
      )}

      {/* 创建模板对话框 */}
      {showCreateTemplateModal && (
        <AMISRenderer
          show={showCreateTemplateModal}
          schema={{
            type: 'dialog',
            title: {
              type: 'html',
              html: '<b>发布到应用模板</b>'
            },
            body: {
              type: 'form',
              api: {
                method: 'post',
                // url: '/admin-api/system/component-template/create',
                url: '/admin-api/system/application/create-by-template',
                data: {
                  applicationId: props.appId,
                  name: '${name}',
                  coverImage: templateFormData.coverImage,
                  classificationId: '${classificationId}',
                  remark: '${remark}',
                  type: 2 // 应用模板类型
                },
                adaptor: function (
                  payload: any,
                  response: any,
                  api: any,
                  context: any
                ) {
                  console.log('提交的数据:', payload);

                  // 重置模板表单数据，确保下次打开时显示当前应用的图标
                  setTemplateFormData({
                    name: '',
                    coverImage: '',
                    classificationId: '',
                    remark: ''
                  });

                  setShowCreateTemplateModal(false);
                  toast.success('发布到应用模板成功');
                  return payload;
                }
              },
              body: [
                {
                  type: 'input-text',
                  label: false,
                  value: '发布到应用模板后,成员可以从应用模板中直接使用应用',
                  static: true
                },
                {
                  type: 'group',
                  label: '应用图标',
                  body: [
                    {
                      type: 'html',
                      html: `
                        <div style="display: inline-flex; align-items: center;">
                          <div id="app-icon-preview" style="color:${
                            templateFormData.iconColor || '#000000'
                          };background:${
                        templateFormData.iconBg || '#F5F7FA'
                      };width:36px;height:36px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:24px;">
                            ${
                              templateFormData.coverImage
                                ? templateFormData.coverImage.includes('<svg')
                                  ? templateFormData.coverImage
                                  : `<i class="${templateFormData.coverImage}"></i>`
                                : ''
                            }
                          </div>
                        </div>
                      `
                    },
                    {
                      type: 'button',
                      label: '设置',
                      level: 'default',
                      size: 'sm',
                      className: 'ml-3 text-right',
                      wrapperComponent: 'div',
                      style: {
                        height: '36px',
                        background: '#fff',
                        border: '1px solid #E5E6EB',
                        float: 'right',
                        color: '#1D2129',
                        borderRadius: '6px',
                        fontWeight: 500,
                        fontSize: '14px',
                        verticalAlign: 'middle'
                      },
                      onEvent: {
                        click: {
                          actions: [
                            {
                              actionType: 'custom',
                              script: () => setShowEditIconDialog(true)
                            }
                          ]
                        }
                      }
                    }
                  ]
                },
                {
                  type: 'input-text',
                  name: 'name',
                  label: '应用模板名称',
                  placeholder: '请输入应用模板名称',
                  value: templateFormData.name,
                  required: true
                },
                {
                  type: 'textarea',
                  name: 'remark',
                  label: '应用模板描述',
                  placeholder: '这里是描述这里是描述',
                  value: templateFormData.remark
                },
                {
                  type: 'select',
                  name: 'classificationId',
                  label: '分组',
                  placeholder: '请选择',
                  options: categories.map(category => ({
                    label: category.name,
                    value: category.id
                  })),
                  value: templateFormData.classificationId
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确定',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
          onClose={() => setShowCreateTemplateModal(false)}
        />
      )}

      {showEditIconDialog && (
        <AMISRenderer
          show={showEditIconDialog}
          onClose={() => setShowEditIconDialog(false)}
          onConfirm={handleIconSave}
          schema={EditEntryIcon({
            logo: (() => {
              // 解析当前应用图标，获取正确的图标数据
              const parsedIcon = parseAppIcon(appData?.logo || '');
              return {
                icon: parsedIcon.icon,
                iconColor: parsedIcon.iconColor,
                iconBg: parsedIcon.iconBg
              };
            })()
          })}
        />
      )}
    </div>
  );
};

export default AppPublish;
