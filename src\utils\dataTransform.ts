/**
 * 数据转换工具函数
 */

/**
 * 扁平化数组工具函数（兼容性替代 flatMap）
 * @param arr 要扁平化的数组
 * @param mapFn 映射函数
 * @returns 扁平化后的数组
 */
function flatMapCompat<T, U>(arr: T[], mapFn: (item: T) => U[]): U[] {
  const result: U[] = [];
  arr.forEach(item => {
    const mapped = mapFn(item);
    result.push(...mapped);
  });
  return result;
}

interface ControlDictItem {
  applicationId: null | string;
  type: number;
  classificationId: number;
  defaultClassificationId: number;
  classificationName: string;
  id: number;
  fieldType: string;
  controlType: string;
  amisControlType: string;
  classification: null | any;
  json: string;
  status: number;
  sort: null | number;
  createTime: number;
}

interface ControlDictResponse {
  code: number;
  data: {
    list: ControlDictItem[];
  };
}

interface TreeNode {
  label: string;
  value?: string;
  children?: TreeNode[];
}

/**
 * 将 GetControlDictPage 获取到的数据转换为三级树形结构
 * @param response GetControlDictPage 的响应数据
 * @returns 三级树形结构数组
 */
export function transformControlDictToTree(
  response: ControlDictResponse
): TreeNode[] {
  if (response.code !== 0 || !response.data?.list) {
    return [];
  }

  const {list} = response.data;

  // 按 classificationName 分组
  const groupedByClassification = list.reduce((acc, item) => {
    const {classificationName} = item;
    if (!acc[classificationName]) {
      acc[classificationName] = [];
    }
    acc[classificationName].push(item);
    return acc;
  }, {} as Record<string, ControlDictItem[]>);

  // 构建三级结构
  const result: TreeNode[] = [];

  // 假设第一级是固定的"系统应用"，可以根据实际需求调整
  const systemApp: TreeNode = {
    label: '系统应用',
    children: []
  };

  // 遍历分类构建第二级
  Object.entries(groupedByClassification).forEach(
    ([classificationName, items]) => {
      const classificationNode: TreeNode = {
        label: classificationName,
        children: []
      };

      // 构建第三级（具体的控件）
      items.forEach(item => {
        classificationNode.children!.push({
          label: item.controlType,
          value: item.id.toString()
        });
      });

      systemApp.children!.push(classificationNode);
    }
  );

  result.push(systemApp);
  return result;
}

/**
 * 根据不同的应用类型构建分类选项
 * @param applicationId 应用ID
 * @returns 分类选项数组
 */
export function getComponentClassifications(applicationId: string | null) {
  const baseClassifications = [
    {label: '系统组件', value: 1},
    {label: '组织组件', value: 2}
  ];

  if (applicationId) {
    baseClassifications.push({label: '应用组件', value: 3});
  }

  return baseClassifications;
}

/**
 * 批量获取并转换组件分类数据
 * @param componentClassifications 分类配置数组
 * @param getControlDictPageFn GetControlDictPage API 函数
 * @returns Promise<TreeNode[]>
 */
export async function fetchAndTransformComponentClassifications(
  componentClassifications: Array<{label: string; value: number}>,
  getControlDictPageFn: (params: any) => Promise<ControlDictResponse>
): Promise<TreeNode[]> {
  try {
    const classificationPromises = componentClassifications.map(
      async classification => {
        const response = await getControlDictPageFn({
          pageNo: 1,
          pageSize: 999,
          type: classification.value
        });

        if (response.code === 0 && response.data?.list) {
          const {list} = response.data;

          // 按 classificationName 分组
          const groupedByClassification = list.reduce((acc, item) => {
            const {classificationName} = item;
            if (!acc[classificationName]) {
              acc[classificationName] = [];
            }
            acc[classificationName].push(item);
            return acc;
          }, {} as Record<string, ControlDictItem[]>);

          // 构建三级结构：第一级是组件类型，第二级是分类，第三级是具体组件
          const classificationChildren: TreeNode[] = [];

          Object.entries(groupedByClassification).forEach(
            ([classificationName, items]) => {
              const componentChildren: TreeNode[] = items.map(item => ({
                label: item.controlType,
                value: item.id.toString()
              }));

              classificationChildren.push({
                label: classificationName,
                children: componentChildren
              });
            }
          );

          return {
            label: classification.label,
            children: classificationChildren
          };
        }

        return {
          label: classification.label,
          children: []
        };
      }
    );

    const results = await Promise.all(classificationPromises);
    return results;
  } catch (error) {
    console.error('获取组件分类数据失败：', error);
    return [];
  }
}
