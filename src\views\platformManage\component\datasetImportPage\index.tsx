import React, {FC, useEffect, useState, useCallback} from 'react';
import './index.scss';
import {Button, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {
  getDataSet,
  queryColumnData,
  getApplicationPageAndClass,
  listAllControlDicts,
  getFormDataPage
} from '@/utils/api/api';
import {RouteComponentProps} from 'react-router-dom';
import PageDataSettingsSider from './components/PageDataSettingsSider';
import PageDataSettingsContent from './components/PageDataSettingsContent';
import PageSettingsSider from './components/PageSettingsSider';
import PageSettingsContent from './components/PageSettingsContent';
import PageDataSettings from './components/PageDataSettings'
import PageSettings from './components/PageSettings/index';
import {findCreateBody,replacedAddressBody} from '@/utils/schemaDataSet/commonEditor';
interface DatasetImportPageProps extends Omit<RouteComponentProps, 'match'> {
  match?: RouteComponentProps['match'];
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;
  };
  store?: any;
  onMenuRefresh?: () => void;
}

const DatasetImportPage: React.FC<DatasetImportPageProps> = ({
  history,
  location,
  store,
  initPathItem,
  match,
  onMenuRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [dataSetInfo, setDataSetInfo] = useState<any>(null);
  const [dataColumns, setDataColumns] = useState<any[]>([]);
  const [activeSettingsKey, setActiveSettingsKey] = useState('datasetSettings');
  const [refreshKey, setRefreshKey] = useState(0);
  const [pageData, setPageData] = React.useState<any>({});

  const [activePageSettingsKey, setActivePageSettingsKey] =
    useState('basicSettings');
  const [controlDicts, setControlDicts] = useState<any[]>([]);

  const fetchControlDicts = useCallback(async () => {
    try {
      const response = await listAllControlDicts();
      if (response.code === 0) {
        setControlDicts(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch control dicts:', error);
    }
  }, []);

  const getAmisControlType = useCallback(
    (fieldType: string) => {
      const controlDict = controlDicts.find(
        dict => dict.fieldType === fieldType
      );
      return controlDict ? controlDict.amisControlType : 'input-text';
    },
    [controlDicts]
  );

  const fetchDataSetInfo = useCallback(async () => {
    console.log(
      'fetchDataSetInfo called with dataSetId:',
      initPathItem?.dataSetId
    );
    if (!initPathItem?.dataSetId) return;

    setLoading(true);
    try {
      const dataSetRes = await getDataSet({
        id: initPathItem.dataSetId
      });

      console.log('getDataSet response:', dataSetRes);
      if (dataSetRes.code === 0) {
        setDataSetInfo(dataSetRes.data);
        const config = JSON.parse(dataSetRes.data.config);
        const fieldList = config.fieldList || [];
        console.log('fieldList:', fieldList);
        const columns = fieldList.map((field: any) => ({
          type: getAmisControlType(field.fieldType),
          label: field.fieldDesc || field.fieldName,
          name: field.fieldName,
          width: 150,
          align: 'left',
          sortable: true,
          searchable: false,
          static: true
        }));
        setDataColumns(columns);
      }
    } catch (error) {
      console.error('Failed to fetch dataset info:', error);
      toast.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [initPathItem?.dataSetId, getAmisControlType]);

  const refreshPageData = useCallback(async () => {
    if (onMenuRefresh) {
      await onMenuRefresh();
    }

    if (initPathItem?.dataSetId) {
      fetchDataSetInfo();
    }

    toast.success('页面数据已更新');
  }, [onMenuRefresh, initPathItem, fetchDataSetInfo]);

  useEffect(() => {
    fetchControlDicts();
  }, [fetchControlDicts]);

  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: initPathItem.id,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then(async (res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            // toast.success('获取表单数据成功');
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            res.data.list[0].data  = await replacedAddressBody(res.data.list[0].data);
            res.data.list[0].pageType = 13;
            // console.log('res.data.list[0]',  window.JSON.stringify(res.data.list[0].data));
            // res.data.list[0].appId = props.computedMatch.params.appId;
            setPageData(res.data.list[0]);
          }else{
            toast.success('暂无表单数据');
          }
        }else{
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  useEffect(() => {
    if (initPathItem?.id) {
      // fetchDataSetInfo();
      handleGetFormDataPage();
    }
  }, [initPathItem?.id]);

  const generateSchema = () => {
    if (!dataSetInfo || !dataColumns.length) return null;

    try {
      const columnNames = dataColumns.map((col: any) => col.name);

      const columns = [
        ...dataColumns,
        ...(dataSetInfo.source !== 1
          ? [
              {
                type: 'operation',
                label: '操作',
                width: 180,
                fixed: 'right',
                buttons: [
                  {
                    type: 'button',
                    label: '详情',
                    level: 'link',
                    size: 'xs',
                    actionType: 'dialog',
                    dialog: {
                      title: '详情',
                      size: 'lg',
                      body: {
                        type: 'form',
                        api: {
                          method: 'get',
                          url:
                            dataSetInfo?.source != 1
                              ? '/admin-api/system/data-source/query-column-data'
                              : `/admin-api/system/form-field-value/get/$id`
                        },
                        controls: dataColumns.map((col: any) => ({
                          type: 'static',
                          name: col.name,
                          label: col.label,
                          required: true
                        }))
                      }
                    }
                  },
                  {
                    type: 'button',
                    label: '编辑',
                    level: 'link',
                    size: 'xs',
                    actionType: 'dialog',
                    dialog: {
                      title: '编辑',
                      size: 'lg',
                      body: {
                        type: 'form',
                        api: {
                          method: 'put',
                          url: `/admin-api/system/form-field-value/batch-update/${initPathItem.id}/\${entries}`,
                          data: {
                            '&': '$$'
                          },
                          requestAdaptor: (api: any) => {
                            const data = {...api.data};
                            delete data.entries;
                            delete data.id;

                            return {
                              ...api,
                              data: data
                            };
                          }
                        },
                        controls: dataColumns.map((col: any) => ({
                          type:
                            col.name === 'id' || col.name === 'entries'
                              ? 'static'
                              : 'text',
                          name: col.name,
                          label: col.label,
                          required: true,
                          disabled: col.name === 'id' || col.name === 'entries'
                        }))
                      }
                    }
                  },
                  {
                    type: 'button',
                    label: '删除',
                    level: 'link',
                    size: 'xs',
                    className: 'text-danger',
                    confirmText: '确认要删除？',
                    actionType: 'ajax',
                    api: {
                      method: 'delete',
                      url: `/admin-api/system/form-field-value/delete/${initPathItem.id}/\${entries}`,
                      messages: {
                        success: '删除成功',
                        failed: '删除失败'
                      }
                    }
                  }
                ]
              }
            ]
          : [])
      ];

      const headerToolbar = [
        ...(dataSetInfo.source !== 1
          ? [
              {
                type: 'button',
                label: '新增',
                icon: 'fa fa-plus',
                level: 'primary',
                actionType: 'dialog',
                dialog: {
                  title: '新增',
                  size: 'lg',
                  body: {
                    type: 'form',
                    api: {
                      method: 'post',
                      url: `/admin-api/system/form-field-value/create/${initPathItem.id}`,
                      data: {
                        '&': '$$'
                      },
                      requestAdaptor: (api: any) => {
                        const data = {...api.data};
                        delete data.id;
                        delete data.entries;

                        return {
                          ...api,
                          data: data
                        };
                      }
                    },
                    controls: columns
                      .filter((col: any) => !['id'].includes(col.name))
                      .filter((col: any) => !['operation'].includes(col.type))
                      .map((col: any) => ({
                        type: col.type || 'text',
                        name: col.name,
                        label: col.label,
                        required: true
                      }))
                  }
                }
              }
            ]
          : []),
        {
          type: 'columns-toggler',
          className: 'mr-3',
          align: 'right'
        },
        {
          type: 'export-excel',
          align: 'right'
        }
      ];

      return {
        type: 'page',
        body: [
          {
            type: 'crud',
            syncLocation: false,
            api: {
              method: 'get',
              url: `/admin-api/system/form-field-value/page/${initPathItem.id}`,
              data: {
                page: '${pageNo}',
                perPage: '${pageSize}'
              },
              reload: columnNames.join(',')
            },
            headerToolbar: headerToolbar,
            autoGenerateFilter: false,
            autoRefresh: true,
            bulkActions: [],
            itemActions: [],
            columns: columns.map((col: any) => ({
              ...col,
              searchable: false
            })),
            id: 'u:7db41ebb7f3b',
            perPageAvailable: [5, 10, 20, 50, 100],
            messages: {},
            pageField: 'pageNo',
            perPageField: 'pageSize',
            alwaysShowPagination: true,
            footerToolbar: [
              {
                type: 'statistics'
              },
              {
                type: 'pagination'
              },
              {
                type: 'switch-per-page',
                tpl: '切换页码',
                wrapperComponent: '',
                id: 'u:a3243b41e34c'
              }
            ]
          }
        ]
      };
    } catch (error) {
      console.error('Failed to generate schema:', error);
      return null;
    }
  };

  const [activeKey, setActiveKey] = React.useState('manage');

  const TabsToolbar = () => {
    return <div className="pageTabsForm-tabsToolbar"></div>;
  };

  if (loading) {
    return <div>Loading...</div>;
  }


  return (
    <div className="pageBox">
      <div className="pageTopManage">
        <div className="pageTopManage-title">{initPathItem.name}</div>
        <div className="pageTopManage-subtitle">{initPathItem.description}</div>
      </div>
      <div className="pageTabsForm">
        <Tabs
          mode="line"
          onSelect={(key: any) => setActiveKey(key)}
          toolbar={TabsToolbar()}
          linksClassName="pageTabsForm-tabsTitle"
          activeKey={activeKey}
        >
          <Tab title="数据管理" eventKey="manage">
          <div className="pageTabsForm-tabsContent">
              <AMISRenderer
                schema={(pageData.data)}
              />
            </div>
          </Tab>
          <Tab title="数据页设置" eventKey="pageDataSettings">
          <PageDataSettings
              pageData={pageData}
              history={history}
              update={() => {
                // onMenuRefresh?.();
                handleGetFormDataPage();
                // refreshPageData();
              }}
            />
          </Tab>
          <Tab title="页面设置" eventKey="pageSettings">
          <PageSettings
              pageData={initPathItem}
              formData={pageData}
              history={history}
              store={store}
              update={() => {
                onMenuRefresh?.();
                // updatePage();
              }}
            />
          </Tab>
        </Tabs>
      </div>
    </div>
  );
};

export default DatasetImportPage;
