// 应用图标组件样式
.appicon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: all 0.3s;

  // 标题样式（"应用图标"这个标题）
  .appicon-title {
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
    transition: all 0.3s;
  }

  .appicon {
    margin: 16px 16px 16px 0;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;

    // 悬停效果
    &:hover {
      opacity: 0.8;
      transform: scale(1.02);
    }

    .appicon-plus {
      font-size: 28px;
      color: #999;
      transition: all 0.3s;
    }

    .appicon-add-btn {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      // 悬停效果
      &:hover {
        opacity: 0.8;
      }
    }
  }

  .appicon-display {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  // 静态模式样式
  .appicon-static {
    transition: all 0.3s;

    &:hover {
      opacity: 0.9;
    }
  }
}

// 水平布局模式
.appicon-wrapper.horizontal {
  .appicon-display {
    flex-direction: row;
    align-items: center;

    .appicon-title {
      margin-bottom: 0;
      margin-right: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .appicon-wrapper {
    .appicon {
      margin: 8px 8px 8px 0;
    }

    .appicon-title {
      font-size: 13px;
    }
  }
}