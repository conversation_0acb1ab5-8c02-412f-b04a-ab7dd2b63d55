import React from 'react';
import {Renderer, TreeSelection} from 'amis';
import {FormControlProps} from 'amis-core';

export interface DeptTreeSelectorProps extends FormControlProps {
  label?: string;
  name: string;
  clearable?: boolean;
  placeholder?: string;
  value?: string;
  static?: boolean;
}

@Renderer({
  type: 'department-select',
  name: 'department-select'
})
export class DeptTreeSelectRenderer extends React.PureComponent<DeptTreeSelectorProps> {
  state = {
    options: JSON.parse(localStorage.getItem('deptTreeList') || '[]'),
    loading: false,
    flattenedOptions: JSON.parse(
      localStorage.getItem('deptFlattenedOptions') || '{}'
    )
  };

  constructor(props: DeptTreeSelectorProps) {
    super(props);
    // 如果localStorage中没有数据，尝试获取
    if (
      !localStorage.getItem('deptTreeList') ||
      !localStorage.getItem('deptFlattenedOptions')
    ) {
      this.fetchOptions();
    }
  }

  // 扁平化选项，便于快速查找
  flattenOptions = (options: any[] = []): Record<string, string> => {
    const result: Record<string, string> = {};

    const process = (items: any[] = []) => {
      items.forEach(item => {
        if (item.id) {
          result[item.id.toString()] =
            item.name || item.nickname || item.label || '未命名';
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          process(item.children);
        }
      });
    };

    process(options);
    return result;
  };

  fetchOptions() {
    
    const {source, env} = this.props as any;

    if (!source || !env) {
      return;
    }

    this.setState({loading: true, error: null});

    env
      .fetcher(source, this.props.data)
      .then(this.handleFetchSuccess)
      .catch(this.handleFetchError);
  }

  handleFetchSuccess = (response: any) => {
    let options = response.data || [];

    // 扁平化选项，用于静态展示查找
    const flattenedOptions = this.flattenOptions(options);
    // 保存到localStorage
    localStorage.setItem('deptTreeList', JSON.stringify(options));
    localStorage.setItem(
      'deptFlattenedOptions',
      JSON.stringify(flattenedOptions)
    );
    this.setState(
      {
        options,
        flattenedOptions,
        loading: false
      },
      () => {
        this.forceUpdate();
      }
    );
  };

  handleFetchError = () => {
    this.setState({
      loading: false
    });
  };

  // 通过扁平化选项快速查找ID对应的标签
  findLabelById = (id: string | number): string => {
    const {flattenedOptions} = this.state;
    return flattenedOptions[id] || id.toString();
  };

  processComplexValue = (value: any): string => {
    if (!value) return '--';

    // 处理数组格式
    if (Array.isArray(value)) {
      return value
        .map(item => {
          // 处理每个数组项
          if (typeof item === 'string' && item.includes('/')) {
            // 如果是 "103/1" 这样的路径格式
            return this.processPathValue(item);
          } else {
            // 如果是简单ID，如 "108"
            return this.findLabelById(item);
          }
        })
        .filter(Boolean)
        .join('、');
    }

    // 处理字符串类型
    if (typeof value === 'string') {
      // 处理 {id=118, name=狗蛋, avatar=} 这种格式
      if (value.startsWith('{') && value.endsWith('}') && value.includes('=')) {
        try {
          // 解析类似 {id=118, name=狗蛋, avatar=} 的格式
          const content = value.substring(1, value.length - 1); // 移除花括号
          const pairs = content.split(',').map(pair => pair.trim());

          const obj: Record<string, string> = {};
          pairs.forEach(pair => {
            if (pair.includes('=')) {
              const [key, val] = pair.split('=').map(part => part.trim());
              obj[key] = val;
            }
          });

          // 如果有name属性，优先使用name
          if (obj.name) {
            return obj.name;
          }

          // 如果有id属性，尝试查找对应的标签
          if (obj.id) {
            return this.findLabelById(obj.id);
          }

          // 如果都没有，返回原始字符串
          return value;
        } catch (e) {
          return value;
        }
      }

      // 检查是否是类似 [100/101/103, 100/101/105, 100/102] 的特殊格式
      if (value.startsWith('[') && value.endsWith(']') && value.includes('/')) {
        // 直接按特殊格式处理，不尝试JSON解析
        const content = value.substring(1, value.length - 1); // 移除方括号
        const parts = content.split(/\s*,\s*/); // 按逗号分割

        return parts
          .map(part => this.processPathValue(part.trim()))
          .filter(Boolean)
          .join('、');
      }

      // 尝试解析标准JSON字符串
      if (value.startsWith('[') && value.endsWith(']')) {
        try {
          const parsedArray = JSON.parse(value);
          if (Array.isArray(parsedArray)) {
            return this.processComplexValue(parsedArray);
          }
        } catch (e) {
          // 移除方括号，按逗号分割
          const simpleArray = value
            .replace(/^\[|\]$/g, '') // 移除首尾的方括号
            .split(/\s*,\s*/); // 按逗号分割，并移除空白

          return simpleArray
            .map(item => {
              const trimmed = item.trim();
              if (trimmed.includes('/')) {
                return this.processPathValue(trimmed);
              } else {
                return this.findLabelById(trimmed);
              }
            })
            .filter(Boolean)
            .join('、');
        }
      }

      // 如果包含逗号，按逗号分割处理
      if (value.includes(',')) {
        return value
          .split(',')
          .map(v => this.processComplexValue(v.trim()))
          .filter(Boolean)
          .join('，');
      }

      // 处理部门ID/用户ID格式
      if (value.includes('/')) {
        return this.processPathValue(value);
      }

      // 简单ID值，直接查找
      return this.findLabelById(value);
    }

    // 处理对象类型的值
    if (typeof value === 'object' && value !== null) {
      const {labelField = 'name'} = this.props as any;
      return value[labelField] || '--';
    }

    if (typeof value === 'number') {
      return this.findLabelById(value);
    }

    // 兜底，直接返回字符串化的值
    return String(value);
  };

  // 处理路径格式的值，如 "103/1"
  processPathValue = (path: string): string => {
    if (!path.includes('/')) return this.findLabelById(path);

    const parts = path.split('/');

    // 构建完整的路径名称
    const pathNames = parts.map(id => {
      // 尝试从扁平化选项中查找名称
      const name = this.findLabelById(id);
      return name !== id.toString() ? name : `未知(${id})`;
    });

    // 将所有部门和人员名称用/连接
    return pathNames.join('/');
  };

  render() {
    const {
      static: isStatic,
      name,
      onlyLeaf,
      label,
      placeholder,
      enableNodePath,
      render
    } = this.props as any;

    const {options, loading} = this.state;
    const finalOptions = options || this.props.options;

    // 安全地获取值，不修改原始数据
    const {data = {}} = this.props;
    const realValue = data[name]; // 从表格行数据中取值

    const displayValue = this.processComplexValue(realValue);
    // 处理值但不修改原始data对象

    // 处理静态展示
    if (isStatic) {
      // 如果有render函数，使用它渲染
      if (render) {
        return render('static', {
          type: 'static',
          label,
          name,
          tpl: displayValue
        });
      }

      // 备用渲染方法：直接使用div
      return (
        <div className="static-value">
          <div className="static-label">{label || name}</div>
          <div className="static-content">{displayValue}</div>
        </div>
      );
    }

    if (finalOptions) {
      // 处理 themeCss，将 inputControlClassName 映射到 tree-select 组件
      const {themeCss} = this.props as any;
      let processedThemeCss = themeCss;

      if (themeCss && themeCss.inputControlClassName) {
        processedThemeCss = {
          ...themeCss,
          inputControlClassName: themeCss.inputControlClassName
        };
      }

      const treeProps = {
        ...this.props,
        type: 'tree-select',
        options: finalOptions || [],
        onlyLeaf: onlyLeaf,
        joinValues: true,
        enableNodePath,
        extractValue: true,
        hideRoot: true,
        placeholder,
        loading,
        themeCss: processedThemeCss
      };

      if (render) {
        return render('department-select', treeProps);
      }
      return <TreeSelection {...treeProps} />;
    } else {
      return <></>;
    }
  }
}
