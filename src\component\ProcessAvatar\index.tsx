// Avatar.jsx
import React from 'react';
import './index.scss';

const statusIconMap = {
  error: 'error fa fa-times-circle',
  pending: 'pending fa fa-clock',
  success: 'success fa fa-check-circle',
  cc: 'cc fa fa-paper-plane',
  comment: 'comment fa fa-comment',
  transfer: 'transfer fa fa-exchange',
  cancel: 'cancel fa fa-undo',
  recall: 'recall fa fa-reply',
  leader: 'leader fa fa-user',
  waiting: 'waiting fa fa-spinner fa-spin'
};

const Avatar: React.FC<any> = (props: any) => {
  const {
    type = 'user',
    name = '未知',
    size = 40,
    src,
    bgc = '#1989fa',
    showY = false,
    showName = true,
    closeable = false,
    status,
    onClose
  } = props;

  const alias = name.length > 2 ? name.slice(-2) : name;
  const statusIcon = status
    ? statusIconMap[status as keyof typeof statusIconMap]
    : null;

  // ... 省略 ...
  return (
    <div className={`head ${showY ? 'show-y' : ''}`}>
      <div className="a-img" style={{ '--bgc': bgc, '--size': `${size}px` } as React.CSSProperties}>
        {type === 'user' ? (
          src ? (
            <img
              src={src}
              alt={name}
              style={{ width: size, height: size, borderRadius: '50%' }}
            />
          ) : (
            <div>
              {alias}
            </div>
          )
        ) : type === 'dept' ? (
          <div style={{ background: '#f78f5f', width: size, height: size, lineHeight: `${size}px`, borderRadius: '50%', textAlign: 'center', color: '#fff' }}>
            <i className="fa fa-building" />
          </div>
        ) : (
          <div style={{ background: '#576a95', width: size, height: size, lineHeight: `${size}px`, borderRadius: '50%', textAlign: 'center', color: '#fff' }}>
            <i className="fa fa-user" />
          </div>
        )}

        {closeable && <i className="close fas fa-close" onClick={onClose}></i>}
        {statusIcon && <i className={`status ${statusIcon}`}></i>}
      </div>

      {showName && (
        <span
          className="name"
          style={{
            width: `${size + 10}px`,
            marginLeft: showY ? 0 : '10px'
          }}
        >
          {name}
        </span>
      )}
    </div>
  );
};

export default Avatar;
