import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createReportObj} from '@/utils/schemaPageTemplate/createPageObjs';

const PageContent: FC<any> = (props: any) => {
  const [link, setLink] = React.useState<any>(false)
  
  React.useEffect(() => {
    if(props.pageData.code){
      setLink('https://dashboard.ykddm.fjpipixia.com/#/dashboard/preview?code=' +
        props.pageData.code);
    }else{
      setLink(false)
    }
    
  }, [props]);
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsReport-tabsToolbar"></div>;
  };
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('manage');

  const getToBlak = () => {
    let a = document.createElement('a');
    a.href =
      'https://dashboard.ykddm.fjpipixia.com/#/dashboard/preview?code=' +
      props.pageData.code+'&applyId='+props.pageData.apply_id+'&pageId='+props.pageData.id;
    window.open(a.href, '_blank');
  };

  const editForm = () => {
    let a = document.createElement('a');
    a.href =
      'https://dashboard.ykddm.fjpipixia.com/#/dashboard/design?code=' +
      props.pageData.code+'&applyId='+props.computedMatch.params.appId+'&pageId='+props.pageData.id;
    window.open(a.href, '_blank');
  };

  return (
    <div className="pageBox">
      <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-6">
            <Button size="lg" level="default" onClick={() => getToBlak()}>
              访问
            </Button>
          </div>
          <div className="pl-1">
            <Button size="lg" level="primary" onClick={() => editForm()}>
              编辑报表
            </Button>
          </div>
        </div>
      </div>
      <div className="pageTabsReport">
        {/* activeKey */}
        <Tabs
          mode="line"
          onSelect={(key: any) => setActiveKey(key)}
          toolbar={TabsToolbar()}
          linksClassName="pageTabsReport-tabsTitle"
          setActiveKey="manage"
        >
          <Tab title="报表预览" eventKey="preview">
            <div className="pageTabsReport-tabsContent">
              {link && <AMISRenderer
                schema={createReportObj(`${link}&applyId=${props.computedMatch.params.appId}&pageId=${props.pageData.id}`)}
                embedMode={true}
              />}
            </div>
          </Tab>
          <Tab title="页面设置" eventKey="3"></Tab>
          <Tab title="页面发布" eventKey="4"></Tab>
        </Tabs>
      </div>
    </div>
  );
};

export default PageContent;
