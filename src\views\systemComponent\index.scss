.pageBox {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .pageTop {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    
    .pageTop-title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .component-header {
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    
    .search-area {
      .cxd-Form {
        .cxd-Form-item {
          margin-right: 16px;
          margin-bottom: 0;
        }
      }
    }
    
    .action-area {
      display: flex;
      gap: 12px;
    }
  }
  
  .component-container {
    flex: 1;
    display: flex;
    background: #fff;
    
    .category-sidebar {
      width: 200px;
      border-right: 1px solid #f0f0f0;
      background: #fafafa;
      
      .category-header {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        
        .add-category-btn {
          padding: 4px;
          color: #1890ff;
          
          &:hover {
            background: #e6f7ff;
          }
        }
      }
      
      .category-list {
        .category-item {
          padding: 8px 16px;
          cursor: pointer;
          border-bottom: 1px solid #f0f0f0;
          transition: all 0.2s;
          
          &:hover {
            background: #e6f7ff;
          }
          
          &.active {
            background: #1890ff;
            color: #fff;
            
            .category-icon {
              filter: brightness(0) invert(1);
            }
          }
          
          .category-item-content {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .category-icon {
              width: 16px;
              height: 16px;
            }
            
            .category-name {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .content-area {
      flex: 1;
      padding: 16px 24px;
      overflow: auto;
    }
  }
}

.customContextMenu {
  position: fixed;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  min-width: 120px;
  
  .customContextMenu-item {
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background: #f5f5f5;
    }
    
    .customContextMenu-item-name {
      font-size: 14px;
      color: #262626;
    }
  }
}