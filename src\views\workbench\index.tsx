import React, {useState, useEffect} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {RouteComponentProps, Switch} from 'react-router';
import {Layout} from 'amis';
import {
  getTreeUserList,
  getTreeDeptList,
  javaApplicationList,
  getRoleList,
  getPosList,
  getUserProfile,
  getTenant
} from '@/utils/api/api';

import CommonHeader from '@/views/components/CommonHeader';
import LayoutRenderAside from '@/views/components/LayoutRenderAside';
import SwitchRenderContent from '@/views/workbench/compontents/SwitchRenderContent';
import AMISRenderer from '@/component/AMISRenderer';

export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<{
    team?: string;
    project?: string;
  }>) {
    // 固定菜单列表
    const platformViewsList = [
      {
        id: 'm-1',
        icon: 'fa fa-home',
        name: '工作台',
        path: 'workbench',
        type: 'platform'
      },
      {
        id: 'm-2',
        icon: 'fa-solid fa-stamp',
        name: '审批',
        path: 'approval',
        type: 'platform'
      },
      {
        id: 'm-4',
        icon: 'fa-solid fa-message',
        name: 'wflow消息',
        path: 'importMessage',
        type: 'platform'
      },
      // {
      //   id: 'm-5',
      //   icon: 'fa-solid fa-table-list',
      //   name: '组件模板',
      //   path: 'componentTemplate',
      //   type: 'platform'
      // },
      // {
      //   id: 'm-6',
      //   icon: 'fas fa-border-all',
      //   name: '组织后台',
      //   path: 'organizationManage',
      //   type: 'platform'
      // },
      {
        id: 'm-7',
        icon: 'fa-solid fa-table-list',
        name: '平台后台',
        path: 'platformManage',
        type: 'platform'
      }
    ];

    // 提取数据加载逻辑为独立函数
    const loadOrganizationData = () => {
      getTreeUserList().then((res: any) => {
        let options = res.data || [];

        // 扁平化选项，用于静态展示查找
        const flattenedOptions = flattenOptions(options);
        localStorage.setItem(
          'userFlattenedOptions',
          JSON.stringify(flattenedOptions)
        );
        localStorage.setItem('userTreeList', JSON.stringify(res.data || []));
      });

      getTreeDeptList().then((res: any) => {
        let options = res.data || [];

        // 扁平化选项，用于静态展示查找
        const flattenedOptions = flattenOptions(options);
        localStorage.setItem(
          'deptFlattenedOptions',
          JSON.stringify(flattenedOptions)
        );
        localStorage.setItem('deptTreeList', JSON.stringify(res.data || []));
      });

      getRoleList().then((res: any) => {
        localStorage.setItem('roleList', JSON.stringify(res.data?.list || []));
      });

      getPosList().then((res: any) => {
        localStorage.setItem('posList', JSON.stringify(res.data?.list || []));
      });
    };

    useEffect(() => {
      // 获取用户信息
      getUserProfile().then(res => {
        if (res.code === 0) {
          const user = res.data;

          // 处理账号权限
          if (res.data.accountAuthorization) {
            user.hasAccountAuth = res.data.accountAuthorization.status === 1;
          } else {
            user.hasAccountAuth = false;
          }

          // 处理搭建权限
          if (res.data.buildAuthorizations && res.data.buildAuthorizations.length > 0) {
            // 组织搭建权限 (type 1)
            user.hasOrgAuth = res.data.buildAuthorizations.some(
              (auth: any) => auth.type === 1 && auth.status === 1
            );

            // 应用搭建权限 (type 2)
            user.hasAppBuildAuth = res.data.buildAuthorizations.some(
              (auth: any) => auth.type === 2 && auth.status === 1
            );
          } else {
            user.hasOrgAuth = false;
            user.hasAppBuildAuth = false;
          }

          // 处理资源权限
          if (res.data.resourcesAuthorizations && res.data.resourcesAuthorizations.length > 0) {
            // 应用模板管理员 (type 1)
            user.hasAppTemplateAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 1 && auth.status === 1
            );

            // 表单模板管理员 (type 2)
            user.hasFormTemplateAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 2 && auth.status === 1
            );

            // 组件管理员 (type 3)
            user.hasComponentAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 3 && auth.status === 1
            );

            // 图标管理员 (type 4)
            user.hasIconAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 4 && auth.status === 1
            );

            // 图片管理员 (type 5)
            user.hasImageAuth = res.data.resourcesAuthorizations.some(
              (auth: any) => auth.type === 5 && auth.status === 1
            );
          } else {
            user.hasAppTemplateAuth = false;
            user.hasFormTemplateAuth = false;
            user.hasComponentAuth = false;
            user.hasIconAuth = false;
            user.hasImageAuth = false;
          }

          getTenant({id: store.tenant_id})
          .then(res => {
            if (res.code === 0) {
              user.companyLogo = res.data.logo;
              user.companyName = res.data.name;
              user.companyId = res.data.contactUserId;
              store.setUserInfo(user);
            }
          })
        }
      });

      // 初始加载组织数据
      loadOrganizationData();
    }, []);

    // 监听企业切换事件
    useEffect(() => {
      const handleTenantSwitch = () => {
        // 重新加载组织相关数据
        loadOrganizationData();
      };

      // 添加事件监听器
      window.addEventListener('tenantSwitch', handleTenantSwitch as EventListener);

      // 清理事件监听器
      return () => {
        window.removeEventListener('tenantSwitch', handleTenantSwitch as EventListener);
      };
    }, []);

    // 扁平化选项，便于快速查找
    const flattenOptions = (options: any[] = []): Record<string, string> => {
      const result: Record<string, string> = {};

      const process = (items: any[] = []) => {
        items.forEach(item => {
          if (item.id) {
            result[item.id.toString()] =
              item.name || item.nickname || item.label || '未命名';
          }
          if (Array.isArray(item.children) && item.children.length > 0) {
            process(item.children);
          }
        });
      };

      process(options);
      return result;
    };

    return (
      <Layout
        aside={
          <LayoutRenderAside
            showCreateTeam={false}
            store={store}
            location={location}
            history={history}
            match={match}
            menuList={platformViewsList}
          />
        }
        asideClassName={'asideClass'}
        header={
          <CommonHeader
            store={store}
            history={history}
            type="platform"
            showTenantInfo={true}
            className="layoutRenderHeader"
          />
        }
        headerClassName={'headerClass'}
        headerFixed={true}
        folded={store.asideFolded}
        offScreen={store.offScreen}
      >
        <Switch>
          <SwitchRenderContent
            store={store}
            location={location}
            history={history}
            match={match}
          />
        </Switch>
      </Layout>
    );
  })
);
