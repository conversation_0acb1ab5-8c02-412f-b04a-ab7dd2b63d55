.dept-manage-container {
  position: relative;
  width: calc(100% - 1.5rem);
  min-height: calc(100vh - 4.625rem);
  margin: 0.75rem;
  overflow: visible;

  .page-header {
    margin-bottom: 24px;

    .title {
      font-size: 20px;
      font-weight: bold;
      color: var(--text-color);
    }

    .subtitle {
      margin-top: 4px;
      color: var(--text--muted-color);
      font-size: 14px;
    }
  }

  // 部门管理内容样式
  .dept-content {
    height: calc(100vh - 12rem);
    padding: 16px;
    overflow: visible;

    .user-crud-table {
      height: 100%;

      .cxd-Crud {
        height: 100%;
        display: flex;
        flex-direction: column;

        .cxd-Crud-toolbar {
          flex-shrink: 0;
        }

        .cxd-Crud-body {
          flex: 1;
          overflow: auto;
          min-height: 0;
        }

        .cxd-Crud-footToolbar {
          flex-shrink: 0;
        }
      }
    }
  }
}

.status-green {
  color: #52c41a;
}

.status-red {
  color: #ff4d4f;
}

.user-crud-table {
  .cxd-Table-table {
    th {
      background-color: #f7f7f7;
    }
  }
}

// 全局样式覆盖
:global {
  .tabs-wrapper {
    .cxd-Tabs-links {
      border-bottom: 1px solid var(--borderColor);
      margin-bottom: 16px;
    }

    .cxd-Tabs-link {
      padding: 12px 16px;
      font-size: 14px;
      cursor: pointer;
      color: var(--text--muted-color);
      position: relative;

      &.is-active {
        color: var(--primary);
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: var(--primary);
        }
      }
    }
  }

  .info-tip {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: rgba(var(--primary-rgb), 0.1);
    border-radius: 4px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-size: 14px;
  }

  .user-crud-table {
    background-color: var(--light-bg);
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-top: 16px;

    .cxd-Table-thead > tr > th {
      background-color: var(--light-bg);
      color: var(--text--muted-color);
      font-weight: normal;
    }

    .cxd-Table-tbody > tr > td {
      color: var(--text-color);
    }

    .cxd-Table-tbody > tr:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .cxd-Button--primary {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px;
      height: 36px;

      .fa {
        margin-right: 4px;
      }
    }

    .cxd-Operation-buttons {
      .cxd-Button {
        color: var(--text--muted-color);

        &:hover {
          color: var(--text-color);
        }
      }
    }

    .cxd-Pagination {
      justify-content: center;
    }
  }
}

.dark-Tabs-pane,
.cxd-Tabs-pane {
  padding: 0;
}

.badge-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
}

.badge-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.badge-danger {
  background-color: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.org-creator {
  color: #2468f2;
  font-size: 12px;
  margin-left: 8px;
  border-radius: 2px;
  background: #e8f3ff;
  padding: 4px 8px;
}

// 邀请成员用户信息卡片样式
.user-info-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: var(--background);
  border-radius: 6px;
  border: 1px solid var(--borderColor);
  margin-top: 8px;

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .info {
    flex: 1;

    .name {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 4px;
    }

    .mobile {
      font-size: 12px;
      color: var(--text--muted-color);
    }
  }

  .status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;

    .added {
      color: var(--text--muted-color);
    }
  }
}
