
import React, {FC, useEffect} from 'react';
import './index.scss';
import {Ava<PERSON>, Button, Modal, confirm, toast} from 'amis';
import {observer} from 'mobx-react';
import ThemeSwitch from '@/component/ThemeSwitch';
import {LogoutConfirmDialog} from '@/utils/schemaDataSet/LogoutConfirmDialog';
import AMISRenderer from '@/component/AMISRenderer';
import {
  isFromAppbuild,
  goToWorkbench,
  clearSourceInfo,
  utilsSetFromAppbuild
} from '@/utils/routeUtils';
import gongzuo from '@/image/common_icons/gongzuo.png';
import dajian from '@/image/common_icons/dajian.png';
import ziyuan from '@/image/common_icons/resourceCenter.png';
import zuzhi from '@/image/common_icons/zuzhi.png';
import {
  updateIcon,
  getTenantList,
  createTenant,
  getUserProfile,
  getTenant
} from '@/utils/api/api';

interface CommonHeaderProps {
  store: any;
  history: any;
  type?: 'platform' | 'appbuild' | 'app' | 'appNoApplication';
  showTenantInfo?: boolean;
  className?: string;
  onSwitchView?: () => void;
  buttonText?: string;
  buttonIcon?: string;
  applyData?: any;
  openApplyNameInput?: boolean;
  editApplyName?: string;
  match?: any;
  onEditApplyName?: (e: any, name: any) => void;
  onSaveApplyName?: () => void;
  // 新增中间导航相关props
  showMiddleNav?: boolean;
  middleNavItems?: Array<{
    key: string;
    label: string;
    active?: boolean;
    onClick?: () => void;
  }>;
  // 页面管理工具栏相关props
  showPageTools?: boolean;
  onSearchClick?: () => void;
  onAddCategoryClick?: () => void;
  onAddPageClick?: () => void;
  onToggleCollapse?: () => void;
  isCollapsed?: boolean;
}

const CommonHeader: FC<CommonHeaderProps> = observer(props => {
  const {
    store,
    history,
    type = 'platform',
    showTenantInfo = false,
    className = '',
    onSwitchView,
    buttonText,
    buttonIcon,
    applyData = {},
    openApplyNameInput = false,
    editApplyName = '',
    match = {},
    onEditApplyName,
    onSaveApplyName,
    // 新增props
    showMiddleNav = false,
    middleNavItems = [],
    showPageTools = false,
    onSearchClick,
    onAddCategoryClick,
    onAddPageClick,
    onToggleCollapse,
    isCollapsed = false
  } = props;

  const [showUserModal, setShowUserModal] = React.useState(false);
  const [showLogoutDialog, setShowLogoutDialog] = React.useState(false);
  const [fromAppbuild, setFromAppbuild] = React.useState(false);

  // 企业相关状态
  const [showTenantDropdown, setShowTenantDropdown] = React.useState(false);
  const [tenantList, setTenantList] = React.useState<any[]>([]);
  const [showCreateTenantModal, setShowCreateTenantModal] =
    React.useState(false);
  const [createTenantForm, setCreateTenantForm] = React.useState({
    name: '',
    logo: ''
  });

  // 切换视图按钮配置
  const switchViewsBtn: any = {
    platform: {
      name: '应用搭建',
      toViews: 'appbuild/home',
      icon: dajian
    },
    appNoApplication: {
      name: buttonText || '返回工作台',
      toViews: 'platform/workbench',
      icon: buttonIcon || gongzuo
    },
    appbuild: {
      name: '工作台',
      toViews: 'platform',
      icon: gongzuo
    },
    app: {
      name: buttonText || '返回工作台',
      toViews: 'platform/workbench',
      icon: buttonIcon || gongzuo
    }
  };

  // 生成企业 Logo
  const generateBusinessLogo = (name: string) => {
    if (!name) return '';
    return name.charAt(0).toUpperCase();
  };

  // 组件挂载时检查是否来自appbuild
  useEffect(() => {
    if (type === 'app') {
      const isFromAppbuildValue = isFromAppbuild();
      setFromAppbuild(isFromAppbuildValue);
    }
  }, [type]);

  // 监听应用信息更新事件
  useEffect(() => {
    const handleAppInfoUpdate = (event: CustomEvent) => {
      const { type: updateType, value } = event.detail;

      if (updateType === 'logo') {
        // 更新应用logo
        store.setApplyLogo(value);
      } else if (updateType === 'name') {
        // 更新应用名称
        store.setApplyName(value);
      }
    };

    // 添加事件监听器
    window.addEventListener('appInfoUpdate', handleAppInfoUpdate as EventListener);

    // 清理函数
    return () => {
      window.removeEventListener('appInfoUpdate', handleAppInfoUpdate as EventListener);
    };
  }, [store]);

  // 处理点击外部关闭企业下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showTenantDropdown && !target.closest('.header-left')) {
        setShowTenantDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showTenantDropdown]);

  // 跳转到个人设置
  const goToAccount = () => {
    setShowUserModal(false);
    history.push(`/${type === 'app' ? 'platform' : type}/account`);
    // 触发切换到账户页面的事件
    const event = new CustomEvent('switchToAccount', {
      detail: {target: 'account'}
    });
    window.dispatchEvent(event);
  };

  // 退出登录
  const LogOut = () => {
    if (type === 'app') {
      clearSourceInfo();
    }
    history.push('/login');
    setTimeout(() => {
      store.setAccessToken('');
      store.setTenantId('');
      store.setUserInfo(false);
    }, 100);
  };

  // 退出登录确认
  const handleLogout = () => {
    setShowUserModal(false);
    setShowLogoutDialog(true);
  };

  // 强制刷新并清除缓存
  const handleForceRefresh = () => {
    confirm('确定要执行更新检查吗？这将清除缓存并退出登录。', '更新检查').then(
      confirmed => {
        if (confirmed) {
          // 清除localStorage
          window.localStorage.clear();

          // 清除sessionStorage
          window.sessionStorage.clear();

          // 清除cookies
          window.document.cookie.split(';').forEach(function (c) {
            window.document.cookie = c
              .replace(/^ +/, '')
              .replace(
                /=.*/,
                '=;expires=' + new Date().toUTCString() + ';path=/'
              );
          });

          // 执行退出登录
          LogOut();

          // 强制刷新页面
          const baseUrl = window.location.origin + window.location.pathname;
          const timestamp = new Date().getTime();
          window.location.href = `${baseUrl}?clearcache=${timestamp}#/login`;
        }
      }
    );
  };

  // 处理切换视图
  const handleSwitchToViews = (views: string) => {
    if (onSwitchView) {
      onSwitchView();
    } else if (type === 'app' || type === 'appNoApplication') {
      goToWorkbench({history, store});
    } else {
      window.open(window.location.origin + `/#/${views}`, '_blank');
    }
  };

  // 处理企业信息点击
  const handleTenantInfoClick = async () => {
    if (!showTenantDropdown) {
      try {
        const response = await getTenantList({
          username: store.userInfo?.username
        });
        if (response.code === 0) {
          setTenantList(response.data || []);
        } else {
          toast.error('获取企业列表失败');
        }
      } catch (error) {
        console.error('API调用错误:', error);
        toast.error('获取企业列表失败');
      }
    }
    setShowTenantDropdown(!showTenantDropdown);
  };

  // 处理企业切换
  const handleTenantSwitch = async (tenant: any) => {
    if (store.tenant_id === tenant.id) {
      setShowTenantDropdown(false);
      return;
    }

    try {
      // 切换企业
      store.setTenantId(tenant.id);

      // 获取租户信息
      getUserProfile()
        .then(res => {
          if (res.code === 0) {
            const user = res.data;
            // 处理账号权限
            if (res.data.accountAuthorization) {
              user.hasAccountAuth = res.data.accountAuthorization.status === 1;
            } else {
              user.hasAccountAuth = false;
            }

            // 处理搭建权限
            if (
              res.data.buildAuthorizations &&
              res.data.buildAuthorizations.length > 0
            ) {
              // 组织搭建权限 (type 1)
              user.hasOrgAuth = res.data.buildAuthorizations.some(
                (auth: any) => auth.type === 1 && auth.status === 1
              );

              // 应用搭建权限 (type 2)
              user.hasAppBuildAuth = res.data.buildAuthorizations.some(
                (auth: any) => auth.type === 2 && auth.status === 1
              );
            } else {
              user.hasOrgAuth = false;
              user.hasAppBuildAuth = false;
            }

            // 处理资源权限
            if (
              res.data.resourcesAuthorizations &&
              res.data.resourcesAuthorizations.length > 0
            ) {
              // 应用模板管理员 (type 1)
              user.hasAppTemplateAuth = res.data.resourcesAuthorizations.some(
                (auth: any) => auth.type === 1 && auth.status === 1
              );

              // 表单模板管理员 (type 2)
              user.hasFormTemplateAuth = res.data.resourcesAuthorizations.some(
                (auth: any) => auth.type === 2 && auth.status === 1
              );

              // 组件管理员 (type 3)
              user.hasComponentAuth = res.data.resourcesAuthorizations.some(
                (auth: any) => auth.type === 3 && auth.status === 1
              );

              // 图标管理员 (type 4)
              user.hasIconAuth = res.data.resourcesAuthorizations.some(
                (auth: any) => auth.type === 4 && auth.status === 1
              );

              // 图片管理员 (type 5)
              user.hasImageAuth = res.data.resourcesAuthorizations.some(
                (auth: any) => auth.type === 5 && auth.status === 1
              );
            } else {
              user.hasAppTemplateAuth = false;
              user.hasFormTemplateAuth = false;
              user.hasComponentAuth = false;
              user.hasIconAuth = false;
              user.hasImageAuth = false;
            }
            getTenant({id: store.tenant_id})
              .then(res => {
                if (res.code === 0) {
                  user.companyLogo = res.data.logo;
                  user.companyName = res.data.name;
                  user.companyId = res.data.contactUserId;
                  store.setUserInfo(user);
                  setShowTenantDropdown(false);
                  history.push('/platform/workbench');
                  toast.success(`已切换到 ${tenant.name}`);

                  // 触发企业切换事件，通知其他组件更新数据
                  const tenantSwitchEvent = new CustomEvent('tenantSwitch', {
                  });
                  window.dispatchEvent(tenantSwitchEvent);
                } else {
                  store.setTenantId('');
                  store.setAccessToken('');
                  store.setRefreshToken('');
                  toast.error(res.msg);
                }
              })
              .catch(err => toast.error(err.msg || '获取租户信息失败'));
          }
        })
        .catch(err => toast.error(err.msg || '获取租户信息失败'));

      // // 创建新的用户对象，避免修改只读属性
      // const currentUser = store.userInfo;
      // const updatedUser = {
      //   ...currentUser,
      //   companyLogo: tenant.logo,
      //   companyName: tenant.name
      // };

      // store.setUserInfo(updatedUser);
      // setShowTenantDropdown(false);
      // history.push('/platform/workbench');
      // toast.success(`已切换到 ${tenant.name}`);
    } catch (error) {
      console.error('切换企业失败:', error);
      toast.error('切换企业失败');
    }
  };

  // 处理创建企业
  const handleCreateTenant = () => {
    setShowTenantDropdown(false);
    setShowCreateTenantModal(true);
  };

  // 处理logo上传
  const handleLogoUpload = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await updateIcon(formData);
      if (response.code === 0) {
        setCreateTenantForm(prev => ({
          ...prev,
          logo: response.data
        }));
        return response.data;
      } else {
        toast.error('上传失败');
        return null;
      }
    } catch (error) {
      toast.error('上传失败');
      return null;
    }
  };

  // 处理创建企业提交
  const handleCreateTenantSubmit = async () => {
    if (!createTenantForm.name.trim()) {
      toast.error('请输入企业名称');
      return;
    }

    try {
      const response = await createTenant({
        userId: store.userInfo?.id,
        name: createTenantForm.name,
        logo: createTenantForm.logo,
        packageId: '111'
      });

      if (response.code === 0) {
        toast.success('创建企业成功');
        setShowCreateTenantModal(false);
        setCreateTenantForm({name: '', logo: ''});
        handleTenantInfoClick();
      } else {
        toast.error(response.msg || '创建企业失败');
      }
    } catch (error) {
      toast.error('创建企业失败');
    }
  };

  // 渲染左侧部分
  const renderLeft = () => {
    if (showTenantInfo) {
      return (
        <div
          className={`header-left ${
            switchViewsBtn[type] === 'app' ? '' : 'padding-left-20'
          }`}
          onClick={handleTenantInfoClick}
        >
          <div className="header-left-content">
            {store.userInfo?.companyLogo ? (
              <Avatar
                src={store.userInfo?.companyLogo}
                size={24}
                className="cursor-pointer"
              />
            ) : (
              <div className="business-logo cursor-pointer">
                {generateBusinessLogo(store.userInfo?.business_name)}
              </div>
            )}
            <div className="header-left-content-nickname">
              {store.userInfo?.companyName}
            </div>
          </div>
          <div className="header-left-right">
            <i
              className={`fas fa-chevron-${showTenantDropdown ? 'up' : 'down'}`}
            ></i>
          </div>

          {/* 企业信息Modal */}
          {showTenantDropdown && (
            <Modal
              show={showTenantDropdown}
              onClose={() => setShowTenantDropdown(false)}
              onHide={() => setShowTenantDropdown(false)}
              closeOnOutside={true}
              closeOnEsc={true}
              className="tenant-dropdown-modal"
            >
              <div className="tenant-dropdown-content">
                {/* 创建企业按钮 */}
                <div className="create-tenant-btn" onClick={handleCreateTenant}>
                  <i className="fa fa-plus"></i>
                  <span>创建企业</span>
                </div>

                {/* 企业列表 */}
                <div className="tenant-list">
                  {tenantList.map((tenant: any, index: number) => (
                    <div
                      key={index}
                      className={`tenant-item ${
                        store.tenant_id === tenant.id ? 'selected' : ''
                      }`}
                      onClick={() => handleTenantSwitch(tenant)}
                    >
                      <div className="tenant-logo">
                        {tenant.logo ? (
                          <img src={tenant.logo} alt={tenant.name} />
                        ) : (
                          <div className="tenant-logo-placeholder"></div>
                        )}
                      </div>
                      <span className="tenant-name">{tenant.name}</span>
                      {store.tenant_id === tenant.id && (
                        <i className="fa fa-check selected-icon"></i>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </Modal>
          )}
        </div>
      );
    } else if (type === 'app') {
      return (
        <div className="header-left">
          <div className="btn-group">
            <Button level="link" onClick={() => handleSwitchToViews('')}>
              {fromAppbuild ? (
                <img src={dajian} alt="dajian" />
              ) : (
                <img src={gongzuo} alt="gongzuo" />
              )}
              {fromAppbuild ? '返回应用搭建' : '返回工作台'}
            </Button>
          </div>

          <div className="header-left-jg"></div>

          <div className="header-left-appname">
            <div className="app-logo-container">
              {store.apply_logo ? (
                store.apply_logo.startsWith('http') ? (
                  <Avatar
                    src={store.apply_logo}
                    size={30}
                    className="cursor-pointer"
                    shape="rounded"
                  />
                ) : (
                  <div
                    dangerouslySetInnerHTML={{__html: store.apply_logo || ''}}
                  />
                )
              ) : (
                <i className="fa fa-window-restore header-left-appname-icon"></i>
              )}
            </div>

            {openApplyNameInput ? (
              <input
                className="pagesNav-classification-input"
                type="text"
                value={editApplyName}
                autoFocus
                onChange={e => {
                  if (onEditApplyName) {
                    // 这里需要传递正确的参数，使用合成事件和新值
                    const syntheticEvent = {
                      target: {value: e.target.value},
                      detail: 2 // 模拟双击
                    };
                    onEditApplyName(syntheticEvent, e.target.value);
                  }
                }}
                onBlur={() => {
                  if (onSaveApplyName) {
                    onSaveApplyName();
                  }
                }}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.keyCode === 13) {
                    if (onSaveApplyName) {
                      onSaveApplyName();
                    }
                  }
                }}
              />
            ) : (
              <div
                className="header-left-appname-name"
                onDoubleClick={e => {
                  // 检查是否在admin模式下才允许编辑
                  if (
                    match.params &&
                    match.params.playType === 'admin' &&
                    onEditApplyName
                  ) {
                    onEditApplyName(e, applyData.name || store.apply_name);
                  }
                }}
              >
                {store.apply_name || applyData.name}
              </div>
            )}

            {!openApplyNameInput &&
              match.params &&
              match.params.playType === 'admin' && (
                <i className="fa fa-chevron-down header-left-appname-iconDown"></i>
              )}
          </div>
        </div>
      );
    } else if (type === 'appNoApplication') {
      return (
        <div className="header-left">
          <div className="btn-group">
            <Button level="link" onClick={() => handleSwitchToViews('')}>
              <img src={buttonIcon || gongzuo} />
              {buttonText || '返回工作台'}
            </Button>
          </div>
        </div>
      );
    }

    return null;
  };

  // 渲染中间导航
  const renderMiddleNav = () => {
    if (!showMiddleNav) return null;

    return (
      <div className="header-middle">
        <div className="header-middle-nav">
          {middleNavItems.map((item) => (
            <div
              key={item.key}
              className={`header-middle-nav-item ${item.active ? 'active' : ''}`}
              onClick={item.onClick}
            >
              {item.label}
            </div>
          ))}
        </div>

        {/* 页面管理工具栏 */}
        {showPageTools && (
          <div className="header-middle-tools">
            <div className="header-middle-tools-left">页面</div>
            <div className="header-middle-tools-right">
              <div className="header-middle-tools-icon" title="搜索" onClick={onSearchClick}>
                <i className="fa fa-search"></i>
              </div>
              <div className="header-middle-tools-icon" title="添加到新分类" onClick={onAddCategoryClick}>
                <i className="fa fa-bars"></i>
              </div>
              <div className="header-middle-tools-icon" title="添加到新页面" onClick={onAddPageClick}>
                <i className="fa fa-plus"></i>
              </div>
              <div className="header-middle-tools-icon" title="折叠" onClick={onToggleCollapse}>
                <i className={`fa ${isCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`}></i>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`common-header ${className}`}>
      {renderLeft()}

      {renderMiddleNav()}

      <div className="header-right">
        {type !== 'app' &&
          type !== 'appNoApplication' && store.userInfo?.hasAccountAuth && (
            <Button
              className="header-right-switchViewsBtn"
              onClick={() => handleSwitchToViews(switchViewsBtn[type].toViews)}
            >
              <img src={switchViewsBtn[type].icon} alt="icon" />
              <span>{switchViewsBtn[type].name}</span>
            </Button>
          )}

        <Button
          className="header-right-switchViewsBtn"
          onClick={() => {
            // 跳转前先设置来源为appbuild
            if (type == 'app') {
              utilsSetFromAppbuild();
            }

            history.push('/resourceCenter');
          }}
        >
          <img src={ziyuan} alt="icon" />
          <span>资源中心</span>
        </Button>

        {/* 组织后台按钮 - 不在组织后台页面时才显示 */}
        {!history.location.pathname.startsWith('/organization/manage') &&store.userInfo?.hasOrgAuth && (
          <Button
            className="header-right-switchViewsBtn"
            onClick={() => {
              if (type == 'app') {
                utilsSetFromAppbuild();
              }

              history.push('/organization/manage');
            }}
          >
            <img src={zuzhi} alt="icon" />
          </Button>
        )}

        {/* 编辑应用按钮 - 仅在非管理员模式下显示 */}
        {type === 'app' &&
          match.params &&
          match.params.playType !== 'admin' && (
            <div style={{marginRight: '10px'}}>
              <Button
                block
                level="default"
                className="settings-button"
                onClick={() => {
                  history.push(
                    `/app${match.params.appId}/admin/page${match.params.form}`
                  );
                }}
              >
                编辑应用
              </Button>
            </div>
          )}

        <div className="user-info" onClick={() => setShowUserModal(true)}>
          {store.userInfo?.avatar ? (
            <Avatar src={store.userInfo?.avatar} size={32} />
          ) : (
            <div className="business-logo">
              {generateBusinessLogo(
                store.userInfo?.business_name || store.userInfo?.nickname
              )}
            </div>
          )}
          <span>{store.userInfo?.nickname}</span>
        </div>

        {/* 用户个人信息弹窗 */}
        <Modal
          show={showUserModal}
          onClose={() => setShowUserModal(false)}
          onHide={() => setShowUserModal(false)}
          closeOnOutside={true}
          closeOnEsc={true}
          className="user-profile-modal"
        >
          <div className="pr-4 py-4" style={{width: '240px'}}>
            <div className="flex items-center">
              <Avatar
                src={store.userInfo?.avatar}
                size={40}
                className="mr-4 ml-5"
              />
              <div>
                <div className="font-bold text-sm mb-1">
                  {store.userInfo?.nickname}
                </div>
                <div className="text-xs text-muted mr-4">
                  {store.userInfo?.email}
                </div>
              </div>
            </div>
            <div className="divider"></div>
            <ThemeSwitch store={store} className="ml-3 mb-4" />
            <Button block level="link" onClick={goToAccount}>
              <i className="fa fa-cog mr-2" />
              个人设置
            </Button>
            <Button
              level="link"
              block
              className="update-check-button"
              onClick={() => {
                setShowUserModal(false);
                handleForceRefresh();
              }}
            >
              <i className="fa fa-sync-alt mr-2" />
              更新检查
            </Button>
            <Button level="link" block onClick={handleLogout}>
              <i className="fa fa-sign-out-alt mr-2" />
              退出登录
            </Button>
          </div>
        </Modal>

        {/* 创建企业模态框 */}
        <Modal
          show={showCreateTenantModal}
          onClose={() => {
            setShowCreateTenantModal(false);
            setCreateTenantForm({name: '', logo: ''});
          }}
          onHide={() => {
            setShowCreateTenantModal(false);
            setCreateTenantForm({name: '', logo: ''});
          }}
          closeOnOutside={true}
          closeOnEsc={true}
          className="create-tenant-modal"
        >
          <div className="create-tenant-content">
            <h3 className="create-tenant-title">创建新组织</h3>

            <div className="form-group">
              <label className="form-label">企业名称</label>
              <input
                type="text"
                className="form-input"
                placeholder="输入公司/组织名"
                value={createTenantForm.name}
                onChange={e =>
                  setCreateTenantForm(prev => ({
                    ...prev,
                    name: e.target.value
                  }))
                }
              />
            </div>

            <div className="form-group">
              <label className="form-label">企业/组织标识（可选）</label>
              <div className="logo-upload-area">
                <input
                  type="file"
                  accept="image/*"
                  onChange={async e => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const logoUrl = await handleLogoUpload(file);
                      if (logoUrl) {
                        setCreateTenantForm(prev => ({
                          ...prev,
                          logo: logoUrl
                        }));
                      }
                    }
                  }}
                  style={{display: 'none'}}
                  id="logo-upload"
                />
                <label htmlFor="logo-upload" className="logo-upload-label">
                  {createTenantForm.logo ? (
                    <img
                      src={createTenantForm.logo}
                      alt="logo"
                      className="uploaded-logo"
                    />
                  ) : (
                    <div className="upload-placeholder">
                      <i className="fas fa-plus"></i>
                      <span>添加图片</span>
                    </div>
                  )}
                </label>
                <div className="upload-hint">
                  推荐 140*82尺寸(支持 1M 以内的图片)
                </div>
              </div>
            </div>

            <div className="form-actions">
              <Button
                className="cancel-btn"
                level="default"
                onClick={() => {
                  setShowCreateTenantModal(false);
                }}
              >
                取消
              </Button>
              <Button
                className="create-btn"
                level="primary"
                onClick={handleCreateTenantSubmit}
              >
                立即创建
              </Button>
            </div>
          </div>
        </Modal>

        {/* 退出确认弹窗 */}
        <AMISRenderer
          show={showLogoutDialog}
          onClose={() => setShowLogoutDialog(false)}
          onConfirm={() => {
            setShowLogoutDialog(false);
            LogOut();
          }}
          schema={LogoutConfirmDialog(showLogoutDialog)}
        />
      </div>
    </div>
  );
});

export default CommonHeader;
