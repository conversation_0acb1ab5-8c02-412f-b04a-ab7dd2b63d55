import React, {FC} from 'react';
import './index.scss';
import <PERSON><PERSON><PERSON>enderer from '@/component/AMISRenderer';
import {toast, Button, Select, Checkbox, InputBox} from 'amis-ui';
import {
  getDataSourcePage,
  getTableList,
  getTableColumnList,
  createDataSet,
  updateDataSet,
  getFormFieldValuePage,
  getFormFieldPage
} from '@/utils/api/api';
import AmisTable from '@/component/AmisTable';
import AmisFieldTable from '@/component/AmisFieldTable';

const CreateDataSet: FC<any> = (props: any) => {
  // 获取 URL 中的 id 参数 编辑
  // 数据集详情

  // tab页签
  const tabsList = [
    {
      name: '数据预览',
      val: 'preview',
      id: 1
    },
    {
      name: '字段设置',
      val: 'fieldsSet',
      id: 2
    }
  ];
  // 类型列表
  const dataSourceTypeList = [
    {
      val: 1,
      name: '原始数据源'
    }
  ];
  // 数据源类型
  const [dataSourceType, setDataSourceType] = React.useState<any>(
    dataSourceTypeList[0]
  );
  // 来源列表
  const dataSourceSourceList = [
    {
      val: 1,
      name: '应用内表单'
    },
    {
      val: 2,
      name: '外部数据'
    }
  ];
  // 数据源来源
  const [dataSourceSource, setDataSourceSource] = React.useState<any>(
    dataSourceSourceList[1]
  );
  // 选中tab
  const [activeKey, setActiveKey] = React.useState<any>(0);
  // 数据源列表
  const [dataSourceList, setDataSourceList] = React.useState<any>([]);
  // 数据源选择
  const [selectedSource, setSelectedSource] = React.useState<any>('');
  // 获取数据源列表
  const handleGetDataSourcePage = () => {
    let data = {
      page: 1,
      pageSize: 100,
      applicationId: props.appId
    };
    getDataSourcePage(data).then((res: any) => {
      if (res.code === 0) {
        setDataSourceList(res.data.list);
      } else {
        toast.error(res.msg);
      }
    });
  };

  // 数据表列表
  const [dataTableList, setDataTableList] = React.useState<any>([]);
  const [selectedTable, setSelectedTable] = React.useState<any>('');

  // 获取数据表列表
  const handleGetTableList = (id: any) => {
    let data = {
      id
    };
    getTableList(data).then((res: any) => {
      if (res.code === 0) {
        let dataList = res.data;
        setDataTableList(dataList);
        if (props?.dataSetdetail && props?.dataSetdetail.associatedDataTable) {
          // 根据数据表名获取 associatedDataTable
          const dataItem = dataList.find(
            (item: any) => item.name === props.dataSetdetail.associatedDataTable
          );
          if (dataItem) {
            setSelectedTable(dataItem);
          }
        }
      }
    });
  };

  // 是否全选
  const [checkedAllField, setCheckedAllField] = React.useState<boolean>(false);
  // 字段列表
  const [fieldList, setFieldList] = React.useState<any>([]);

  // 获取字段列表
  const handleGetTableColumnList = () => {
    let data = {
      id: selectedSource.id,
      tableName: selectedTable.name
    };
    getTableColumnList(data).then((res: any) => {
      if (res.code === 0) {
        let dataList = res.data;
        // 处理字段的选中状态
        dataList.forEach((item: any) => {
          if (editConfigList && editConfigList.length > 0) {
            // 如果存在编辑配置,则根据配置设置选中状态
            const found = editConfigList.find(
              (config: any) => config.fieldName === item.columnName
            );
            item.checked = !!found; // 转换为布尔值
          } else {
            // 如果不存在编辑配置,则默认未选中
            item.checked = false;
          }
        });
        setFieldList(dataList);
      }
    });
  };

  const handleGetFormDataPage = () => {
    let applicationPageId: number = props.dataSetdetail.associatedDataTable;
    let data = {
      applicationPageId
    };
    getFormFieldPage(data).then((res: any) => {
      if (res.code === 0) {
        let dataList = res.data.list;
        dataList.forEach((item: any) => {
          item.checked = true;
          item.columnComment = item.label;
          item.columnName = item.name;
          // item.columnType = item.controlLabel;
          item.fieldType = item.controlLabel;
          
        });
        console.log('cccccccc',dataList)
        setFieldList(dataList);
      }
    });
  };

  // 选择、取消选择字段
  const handleFieldChecked = (item: any, index: number) => {
    let newFieldList = [...fieldList];
    item.checked = !item.checked;
    newFieldList[index] = item;
    setFieldList(newFieldList);
  };
  // 全选、取消全选
  const handleCheckedAllField = (value: any) => {
    let newFieldList = [...fieldList];
    newFieldList.forEach((item: any) => {
      item.checked = value;
    });
    setFieldList(newFieldList);
  };
  // 监听字段列表的变化，输出已选择字段数量
  const handleFieldListChange = () => {
    let checkedNum = 0;
    fieldList.forEach((item: any) => {
      if (item.checked) {
        checkedNum++;
      }
    });
    return checkedNum;
  };
  // 字段表格数据
  const getDataColumns = () => {
    let checkedList = fieldList
      .filter((item: any) => item.checked)
      .map((item: any) => ({
        title: item.columnComment || item.columnName,
        name: item.columnName,
        width: 150,
        align: 'left',
        sortable: true,
        searchable: true
      }));
    return checkedList;
  };

  const renderTableContent = () => {
    let columnNames = fieldList
      .filter((item: any) => item.checked)
      .map((item: any) => item.columnName);
    if (columnNames.length === 0) return null;
    return (
      <div className="tablesContent">
        <AmisTable
          columns={getDataColumns()}
          columnNames={columnNames}
          tableName={selectedTable.name}
          datasourceId={selectedSource.id}
          dataSetdetail={props?.dataSetdetail}
        />
        
      </div>
    );
  };

  // 数据集名称
  const [dataSetName, setDataSetName] = React.useState<string>('');
  //数据集名称-是否切换编辑状态
  const [dataSetNameEdit, setDataSetNameEdit] = React.useState<boolean>(false);

  const fieldTableDataRef = React.useRef<any>([]);
  // 创建、保存数据集 createDataSet
  const onSave = () => {
    //判断是否填写了数据集名称
    if (!dataSetName || dataSetName.trim() === '') {
      toast.error('请填写数据集名称');
      return;
    }
    // 判断是否选择了数据源
    if (!selectedSource) {
      toast.error('请选择数据源');
      return;
    }
    // 判断是否选择了数据表
    if (!selectedTable) {
      toast.error('请选择数据表');
      return;
    }
    // 判断是否选择了字段
    if (fieldTableDataRef.current.length === 0) {
      toast.error('请选择字段');
      return;
    }
    let fieldList = fieldTableDataRef.current.map((field: any) => ({
      fieldName: field.columnName,
      fieldDesc: field.columnComment || '',
      fieldType: field.fieldType || 'text',
      sourceTable: selectedTable.name,
      control_id: field.controlId || ''
    }));

    console.log('fieldList', fieldList);

    if (props?.dataSetdetail) {
      let data = {
        id: props?.dataSetdetail.id,
        applicationId: props.appId,
        name: dataSetName,
        applicantOrBackend: 1, //1应用内
        dataSourceId: selectedSource.id,
        type: 1, //类型(1-原始数据源)
        source: 2, //来源(1-应用内表单 2-外部数据)
        belongingDataSource: selectedSource.name,
        associatedDataTable: selectedTable.name,
        config: JSON.stringify({fieldList})
      };
      updateDataSet(data).then((res: any) => {
        if (res.code === 0) {
          toast.success('更新成功');
          //返回上一级页面
          window.history.back();
        } else {
          toast.error(res.msg);
        }
      });
    } else {
      let data = {
        applicationId: props.appId,
        name: dataSetName,
        applicantOrBackend: 1, //1应用内
        dataSourceId: selectedSource.id,
        type: 1, //类型(1-原始数据源)
        source: 2, //来源(1-应用内表单 2-外部数据)
        belongingDataSource: selectedSource.name,
        associatedDataTable: selectedTable.name,
        config: JSON.stringify({fieldList})
      };
      createDataSet(data).then((res: any) => {
        if (res.code === 0) {
          toast.success('创建成功');
          //返回上一级页面
          window.history.back();
        } else {
          toast.error(res.msg);
        }
      });
    }
  };

  // 关闭当前页
  const onClose = () => {
    window.history.back();
  };

  // 放大：收起隐藏侧边
  const [isShowSide, setIsShowSide] = React.useState<boolean>(false);

  React.useEffect(() => {
    if (fieldList.length == 0) {
      setCheckedAllField(false);
    } else {
      let isCheckedAll = true;
      fieldList.forEach((item: any) => {
        if (!item.checked) {
          isCheckedAll = false;
        }
      });
      setCheckedAllField(isCheckedAll);
    }
  }, [fieldList]);

  React.useEffect(() => {
    if (props.appId) {
      handleGetDataSourcePage();
    }
  }, [props.appId]);

  // 选择数据源后获取对应的数据表
  React.useEffect(() => {
    if (selectedSource && dataSourceSource.val != 1) {
      handleGetTableList(selectedSource.id);
    }
  }, [selectedSource]);

  React.useEffect(() => {
    if (selectedTable && dataSourceSource.val != 1) {
      handleGetTableColumnList();
    }
  }, [selectedTable]);
  const [editConfigList, setEditConfigList] = React.useState<any>([]);
  React.useEffect(() => {
    if (props?.dataSetdetail) {
      console.log('props?.dataSetdetail', props?.dataSetdetail);

      // 设置数据集名称
      setDataSetName(props?.dataSetdetail.name);
      // 根据类型值设置 dataSourceType
      const typeItem = dataSourceTypeList.find(
        item => item.val === props.dataSetdetail.type
      );
      if (typeItem) {
        setDataSourceType(typeItem);
      }
      // 根据来源值设置 dataSourceSource
      const sourceItem = dataSourceSourceList.find(
        item => item.val === props.dataSetdetail.source
      );
      
      if (sourceItem) {
        setDataSourceSource(sourceItem);
      }
      // 应用内表单
      if (props?.dataSetdetail.source == 1) {
        handleGetFormDataPage();
        setSelectedSource({
          name: props.dataSetdetail.belongingDataSource,
          id: props.dataSetdetail.dataSourceId
        });
        setSelectedTable({
          name: props.dataSetdetail.associatedDataTable
        });
        return;
      }
      // 设置数据源
      if (dataSourceList.length > 0) {
        const selectedSourceItem = dataSourceList.find(
          (item: any) => item.id === props.dataSetdetail.dataSourceId
        );
        if (selectedSourceItem) {
          setSelectedSource(selectedSourceItem);
        } else {
          const selectedSourceItem_byname = dataSourceList.find(
            (item: any) => item.name === props.dataSetdetail.belongingDataSource
          );
          if (selectedSourceItem_byname) {
            setSelectedSource(selectedSourceItem_byname);
          }
        }
      }
      // 设置关联数据表

      // 设置所选字段
      if (props?.dataSetdetail.config) {
        const editConfigList = JSON.parse(props?.dataSetdetail.config).fieldList;
        console.log('editConfigList', editConfigList);
        setEditConfigList(editConfigList);
      }
    }
  }, [props?.dataSetdetail, dataSourceList]);

  // 在组件顶部添加初始化的 useEffect
  React.useEffect(() => {
    if (fieldList && editConfigList) {
      const initialData = fieldList
        .filter((item: any) => item.checked)
        .map((item: any) => {
          // 查找对应的配置项
          const configItem = editConfigList?.find(
            (config: any) => config.fieldName === item.columnName
          );

          return {
            columnName: item.columnName,
            columnComment: item.columnComment || '',
            columnType: item.columnType,
            // 如果存在配置项,则使用配置项中的值
            fieldType: configItem?.fieldType || item.fieldType || 'text',
            controlId: configItem?.control_id || '',
            fieldDesc: configItem?.fieldDesc || item.columnComment || ''
          };
        });

      // 初始化 fieldTableDataRef
      fieldTableDataRef.current = initialData;
    }
  }, [fieldList, editConfigList]);

  return (
    <div className="boxCreateDataSet">
      <div className="boxCreateDataSetTitle">
        {!dataSetNameEdit ? (
          <div className="boxCreateDataSetTitle-name">
            {dataSetName ? (
              dataSetName
            ) : (
              <span className="boxCreateDataSetTitle-name-not">
                请设置数据集名称
              </span>
            )}
            {!props?.disabledType && (
              <i
                className="iconfont icon-edit boxCreateDataSetTitle-name-icon"
                onClick={() => setDataSetNameEdit(true)}
              />
            )}
          </div>
        ) : (
          <div className="boxCreateDataSetTitle-input">
            <div className="input-group">
              <InputBox
                value={dataSetName}
                onChange={(value: string) => setDataSetName(value)}
                placeholder="请输入内容"
                className="input-group-input"
              />
              <div className="input-group-buttons">
                <Button
                  size="sm"
                  level="primary"
                  className="mx-1"
                  onClick={() => setDataSetNameEdit(false)}
                >
                  确认
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    setDataSetNameEdit(false);
                    setDataSetName(dataSetName); // 保持原值
                  }}
                >
                  取消
                </Button>
              </div>
            </div>
          </div>
        )}
        <div className="boxCreateDataSetTitle-tabs">
          {tabsList.map((item: any, index: number) => {
            return (
              <div
                className={`boxCreateDataSetTitle-tabs-item ${
                  activeKey == index ? 'tabsItemClick' : ''
                }`}
                key={item.id}
                onClick={() => {
                  setActiveKey(index);
                }}
              >
                {item.name}
              </div>
            );
          })}
          <div
            className="tabsItemClick_block"
            style={{left: `${activeKey * 5.5}rem`}}
          ></div>
        </div>
        <div className="boxCreateDataSetTitle-right">
          {!props?.disabledType && (
            <Button level="primary" className="mx-1" onClick={() => onSave()}>
              保存
            </Button>
          )}
          <Button onClick={() => onClose()}>关闭</Button>
        </div>
      </div>
      <div className="boxCreateDataSetContent">
        <div
          className={`boxCreateDataSetContent-left ${
            isShowSide ? 'disnone' : ''
          }`}
        >
          <div className="boxCreateDataSetContent-left-changeMenu">
            <div className="changeMenuInfo">
              <div className="changeMenuInfo-line">
                <span className="changeMenuInfo-line-name">来&emsp;源：</span>
                <span className="changeMenuInfo-line-val">
                  {dataSourceSource.name}
                </span>
              </div>
              <div className="changeMenuInfo-line">
                <span className="changeMenuInfo-line-name">类&emsp;型：</span>
                <span className="changeMenuInfo-line-val">
                  {dataSourceType.name}
                </span>
              </div>
              {/* 数据源选择 */}
              <div className="changeMenuInfo-line">
                <span className="changeMenuInfo-line-name">数据源：</span>
              </div>
              <div className="changeMenuInfo-line">
                <Select
                  className="changeMenuInfo-line-select"
                  labelField="name"
                  valueField="id"
                  value={selectedSource}
                  onChange={(value: any) => setSelectedSource(value)}
                  options={dataSourceList}
                  searchable={true}
                  clearable={true}
                  placeholder="请选择数据源"
                  disabled={props?.disabledType}
                />
              </div>
              <div className="changeMenuInfo-line">
                <span className="changeMenuInfo-line-name">数据表：</span>
              </div>
              <div className="changeMenuInfo-line">
                <Select
                  className="changeMenuInfo-line-select"
                  labelField="name"
                  valueField="name"
                  value={selectedTable}
                  onChange={(value: any) => setSelectedTable(value)}
                  options={dataTableList}
                  searchable={true}
                  clearable={true}
                  placeholder="请选择数据源"
                  disabled={props?.disabledType}
                />
              </div>
            </div>
            <div className="changeMenuField">
              <div className="changeMenuField-title">
                <Checkbox
                  type="checkbox" // checkbox 或 radio
                  label={`字段选择（${handleFieldListChange()} / ${
                    fieldList.length
                  }）`} // 显示的标签文本
                  value={checkedAllField} // 选中状态
                  onChange={(value: any) => {
                    // 值改变时的回调
                    handleCheckedAllField(value);
                  }}
                  disabled={props?.disabledType} // 是否禁用
                  size="sm" // 大小，可选: sm/lg/small/large
                />
              </div>
              <div className="changeMenuField-content">
                {fieldList.map((item: any, index: number) => {
                  return (
                    <div className="changeMenuField-content-item" key={index}>
                      <Checkbox
                        className="changeMenuField-content-item-checkbox"
                        type="checkbox" // checkbox 或 radio
                        label={`${item.columnComment}`} // 显示的标签文本
                        value={item.checked} // 选中状态
                        onChange={(value: any) => {
                          // 值改变时的回调
                          handleFieldChecked(item, index);
                        }}
                        disabled={props?.disabledType} // 是否禁用
                        size="sm" // 大小，可选: sm/lg/small/large
                        description={
                          item.columnName ? `(${item.columnName})` : ''
                        }
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <div className="boxCreateDataSetContent-left-showField">
            <div className="showFieldTitle">
              已选字段（{handleFieldListChange()}）
            </div>
            <div className="showFieldContent">
              {fieldList.map((item: any, index: number) => {
                if (item.checked) {
                  return (
                    <div className="showFieldContent-item" key={index}>
                      <div className="showFieldContent-item-name">
                        {item.columnComment}
                      </div>
                      <div className="showFieldContent-item-val">
                        {item.columnName ? `(${item.columnName})` : ''}
                      </div>
                      {!props?.disabledType && (
                        <div
                          className="showFieldContent-item-icon"
                          onClick={() => {
                            handleFieldChecked(item, index);
                          }}
                        >
                          <i className="fa fa-trash-o showFieldContent-item-icon-fa"></i>
                        </div>
                      )}
                    </div>
                  );
                } else {
                  return '';
                }
              })}
            </div>
          </div>
        </div>
        <div className="boxCreateDataSetContent-right">
          <div className="boxCreateDataSetContent-right-content">
            <div className="tablesTitle">
              <div className="tablesTitle-name">{tabsList[activeKey].name}</div>
              <div className="tablesTitle-btn">
                <Button size="xs" onClick={() => setIsShowSide(!isShowSide)}>
                  {isShowSide ? '还原' : '放大'}
                </Button>
              </div>
            </div>
            {activeKey == 0 && renderTableContent()}
            {activeKey == 1 && (
              <AmisFieldTable
              match={props.match}
                dataSetdetail={props?.dataSetdetail}
                disabledType={props?.disabledType}
                onDataChange={(data: any) => {
                  let list = JSON.parse(JSON.stringify(data));
                  fieldTableDataRef.current = list;
                }}
                itemsList={fieldList
                  .filter((item: any) => item.checked)
                  .map((item: any) => {
                    if (editConfigList && editConfigList.length > 0) {
                      const configItem = editConfigList?.find(
                        (config: any) => config.fieldName === item.columnName
                      );
                      return {
                        ...item,
                        fieldType: configItem?.fieldType,
                        // 如果存在配置项,则使用配置项中的值
                        controlId: configItem?.control_id || '',
                        fieldDesc:
                          configItem?.fieldDesc || item.columnComment || ''
                        // 其他需要的字段...
                      };
                    } else {
                      return item;
                    }
                  })}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateDataSet;
