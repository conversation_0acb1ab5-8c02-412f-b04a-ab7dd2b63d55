.manageHeader {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--body-bg);
  border-bottom: 1px solid var(--borderColor);
  padding-left: 20px;

  &-left {
    display: flex;
    align-items: center;

    .btn-group {
      display: flex;
      align-items: center;
      margin-right: 10px;

      .cxd-Button--link,
      .dark-Button--link {
        color: var(--text-color);
        margin-right: 10px;
        display: inline-flex;
        align-items: center;
        line-height: 1;
        i {
          margin-right: 5px;
          display: inline-flex; /* 使图标也支持 flex 布局 */
          align-items: center; /* 图标内容垂直居中 */
        }

        &:hover {
          color: var(--primary);
        }
      }
    }
  }

  &-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    border-bottom: 1px solid var(--borderColor);
    height: 100%;

    .cxd-Button--link,
    .dark-Button--link {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      margin: 0 8px;
      padding: 10px 24px;
      border-radius: 4px;
      transition: all 0.3s;

      &.active {
        background-color: var(--layout-bg);
      }

      &:hover {
        background-color: var(--layout-bg);
      }

      .nav-text {
        margin-left: 8px;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color);
      }
    }
  }

  &-right {
    display: flex;
    // align-items: center;
    padding: 8px 30px 8px 0;

    .theme-switch {
      margin-right: 15px;
    }

    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;

      span {
        margin-left: 8px;
        color: var(--text-color);
      }

      &:hover {
        opacity: 0.9;
      }
    }
  }
  
  .business-logo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
  }
}


