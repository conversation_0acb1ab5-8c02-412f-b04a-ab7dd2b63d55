let curd =  {
    id: 'u:6be5133b77f5',
    type: 'page',
    title: '数据集列表',
    regions: ['body'],
    pullRefresh: {disabled: true},
    body: [
      {
        type: 'crud',
        syncLocation: false,
        api: {
          method: 'get',
          url: `/admin-api/system/data-set/page?applicationId=${props.applyData.id}`,
          messages: {},
          requestAdaptor: '',
          adaptor: "console.log('payload', payload);\r\nreturn payload;"
        },
        bulkActions: [],
        itemActions: [],
        id: 'u:f1f24e58104f',
        perPageAvailable: [5, 10, 20, 50, 100],
        messages: {},
        filterSettingSource: [
          'id',
          'applicationId',
          'name',
          'type',
          'source',
          'belongingDataSource',
          'associatedDataTable',
          'remarks',
          'createTime'
        ],
        filterEnabledList: [
          {label: 'name', value: 'name'},
          {label: 'type', value: 'type'},
          {label: 'source', value: 'source'},
          {label: 'createTime', value: 'createTime'},
          {label: 'remarks', value: 'remarks'},
          {label: 'associatedDataTable', value: 'associatedDataTable'},
          {label: 'belongingDataSource', value: 'belongingDataSource'},
          {label: 'applicationId', value: 'applicationId'}
        ],
        headerToolbar: [
          {
            type: 'button',
            label: '新增',
            id: 'u:63bcbfc69ed8',
            disabledOnAction: false,
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    expression: 'getBtn()'
                  }
                ]
              }
            }
          },
          {
            type: 'export-csv',
            tpl: '内容',
            wrapperComponent: '',
            id: 'u:30129f3c0998'
          },
          {
            type: 'export-excel',
            tpl: '内容',
            wrapperComponent: '',
            id: 'u:6a63d52666a1',
            disabledOnAction: false
          }
        ],
        syncResponse2Query: false,
        filter: {
          title: '查询条件',
          columnCount: 4,
          mode: 'horizontal',
          body: [
            {
              type: 'input-text',
              label: '数据集名称',
              name: 'name',
              id: 'u:abc469cccf0f'
            },
            {
              type: 'select',
              label: '类型',
              name: 'type',
              id: 'u:5862f42f60db',
              options: [
                {
                  label: '原始数据集',
                  value: '1'
                }
              ]
            },
            {
              type: 'select',
              label: '来源',
              name: 'source',
              id: 'u:b22c87b6ed92',
              options: [
                {
                  label: '应用内表单',
                  value: '1'
                },
                {
                  label: '外部数据',
                  value: '2'
                }
              ]
            },
            {
              type: 'input-datetime',
              label: '创建时间',
              name: 'createTime',
              id: 'u:14edfe635c7b',
              valueFormat: 'x',
              displayFormat: 'YYYY-MM-DD HH:mm:ss'
            },
            {
              type: 'input-text',
              label: '备注',
              name: 'remarks',
              id: 'u:5102b8114502'
            },
            {
              type: 'input-text',
              label: '关联数据表',
              name: 'associatedDataTable',
              id: 'u:8aaeed28c1af'
            },
            {
              type: 'input-text',
              label: '所属数据源',
              name: 'belongingDataSource',
              id: 'u:12bbf528040a'
            }
          ],
          id: 'u:5055940e851d',
          actions: [
            {
              type: 'reset',
              label: '重置',
              id: 'u:da879e1b84f7'
            },
            {type: 'submit', label: '搜索', primary: true, id: 'u:6296d9c78a58'}
          ],
          feat: 'Insert',
          labelAlign: 'left'
        },
        pageField: 'pageNo',
        orderField: 'id',
        perPageField: 'pageSize',
        columns: [
          {
            label: 'id',
            type: 'text',
            name: 'id',
            id: 'u:70ad4de0d3e9',
            placeholder: '-',
            remark: ''
          },
          {
            label: '应用编号',
            type: 'text',
            name: 'applicationId',
            id: 'u:bc6bd32e2494'
          },
          {
            label: '数据集名称',
            type: 'text',
            name: 'name',
            id: 'u:b59187724448'
          },
          {
            label: '类型',
            type: 'select',
            static: true,
            name: 'type', //type
            isDisabled: true,
            id: 'u:51104ccfa5c8',
            placeholder: '-',
            options: [
              {
                label: '原始数据集',
                value: '1'
              }
            ]
          },
          // 来源(1-应用内表单 2-外部数据)
          {
            label: '来源',
            type: 'select',
            name: 'source', //
            id: 'u:7271f7484f90',
            placeholder: '-',
            static: true,
            options: [
              {
                label: '应用内表单',
                value: '1'
              },
              {
                label: '外部数据',
                value: '2'
              }
            ]
          },
          {
            label: '所属数据源',
            type: 'text',
            name: 'belongingDataSource',
            id: 'u:9b172f07adde',
            placeholder: '-',
            popOver: false,
            copyable: false,
            quickEdit: false
          },
          {
            label: '关联数据表',
            type: 'text',
            name: 'associatedDataTable',
            id: 'u:2218c21e4530',
            placeholder: '-'
          },
          {label: '备注', type: 'text', name: 'remarks', id: 'u:d8cbae306c71'},
          {
            label: '创建时间',
            type: 'datetime',
            name: 'createTime',
            valueFormat: 'x',
            id: 'u:c11bc2c1abf6',
            placeholder: '-'
          }
        ],

        alwaysShowPagination: true,
        footerToolbar: [
          {
            type: 'statistics'
          },
          {
            type: 'switch-per-page',
            tpl: '切换页码',
            wrapperComponent: '',
            id: 'u:bda5e402486b'
          },
          {
            type: 'pagination',
            tpl: '分页',
            wrapperComponent: '',
            id: 'u:b2ec821b8884',
            __editorStatebaseControlClassName: 'default'
          }
        ]
      }
    ],
    asideResizor: false
  };
