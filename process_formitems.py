
import re
import json
import os

file_path = "/Users/<USER>/Desktop/html5/java-amis/src/utils/amisEditorItems/formitems.ts"

try:
    with open(file_path, 'r', encoding='utf-8') as f:
        file_content = f.read()
except FileNotFoundError:
    print(f"Error: File not found at {file_path}")
    exit(1)
except Exception as e:
    print(f"Error reading file: {e}")
    exit(1)

output_data = []

# Regex to find each item array (formItems, containerItems, etc.)
# This pattern is more robust for nested brackets by explicitly matching content within brackets.
item_array_pattern = re.compile(r"const\s+(\w+):\s*any\s*=\s*(\[(?:[^[\]]*|\s*\[[^\]]*\])*?\]);", re.DOTALL)
# Regex to find ref and children within each item array, allowing for any characters in between
ref_children_pattern = re.compile(r"ref:\s*'([^']+?)',.*?children:\s*\[(.*?)\]", re.DOTALL)
# Regex to find label and value within children
label_value_pattern = re.compile(r"{label:\s*'([^']+?)',\s*value:\s*'([^']+?)'}", re.DOTALL)

print(f"File content length: {len(file_content)}")

matches = item_array_pattern.findall(file_content)
print(f"Matches found by item_array_pattern: {len(matches)}")

for var_name, array_content in matches:
    print(f"Processing var_name: {var_name}")
    if var_name in ["formItems", "containerItems", "dataItems", "functionItems", "showItems"]:
        # array_content now contains the full array string, e.g., '[{ref: '表单项', children: [...]}]'
        # We need to extract the content of the first object within this array.
        # A simpler approach is to directly apply ref_children_pattern to array_content
        ref_children_match = ref_children_pattern.search(array_content)
        if ref_children_match:
            class_name = ref_children_match.group(1)
            children_content = ref_children_match.group(2)
            print(f"  Found class_name: {class_name}")
            
            items = []
            for label_value_match in label_value_pattern.finditer(children_content):
                label = label_value_match.group(1)
                value = label_value_match.group(2)
                items.append({"label": label, "value": value})
            
            output_data.append({"class": class_name, "items": items})
        else:
            print(f"  No ref_children_match for {var_name}")
    else:
        print(f"  Skipping {var_name}")

print(json.dumps(output_data, ensure_ascii=False, indent=2))
