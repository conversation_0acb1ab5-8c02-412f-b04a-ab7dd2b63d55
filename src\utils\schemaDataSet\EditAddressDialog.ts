import cityCodeUtil from '@/utils/cityCodeUtil/cityCodeUtil';
export const EditAddressDialog = (show: boolean, currentAddress?: string) => {
    // 解析地址字符串，分离省市区和详细地址
    let cityValue = '';
    let detailValue = '';
    
    if (currentAddress) {
      // 假设currentAddress格式为"省市区 详细地址"
      // 或者是JSON格式 {"city": "省市区", "detail": "详细地址"}
      try {
        // 尝试解析JSON格式
        const addressObj = JSON.parse(currentAddress);
        cityValue = addressObj.city || '';
        detailValue = addressObj.detail || '';
      } catch (e) {
        // 如果不是JSON格式，尝试按空格分割
        const parts = currentAddress.split(/\s+/);
        if (parts.length > 1) {
          cityValue = parts[0];
          detailValue = parts.slice(1).join(' ');
        } else {
          // 如果无法分割，则全部作为详细地址
          detailValue = currentAddress;
        }
      }
    }
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑地址</b>'
    },
    showCloseButton: false,
    data: {
      // detailAddress: currentAddress
      // 设置初始数据，分别为省市区和详细地址
      detailAddress_city: cityCodeUtil.getCodeByCityName(cityValue),
      detailAddress_detail: detailValue
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入详细地址'
      },
      body: [
        {
          required: true,
          type: 'input-address',
          name: 'detailAddress',
          id: 'detailAddress',
          // detailAddress: currentAddress,
          placeholder: '详细地址',
          maxLength: 200,
          validations: {
            maxLength: 200
          },
          inputClassName: 'name-input',
          labelClassName: 'name-label',
          // 添加以下配置
          allowCity: true, // 如果允许只选择到省级，设置为false
          allowDistrict: true, // 如果允许只选择到市级，设置为false
          clearable: true, // 允许清除选择
          searchable: true, // 允许搜索
          detailAddressPlaceholder: '请输入详细地址' // 详细地址提示
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
};
