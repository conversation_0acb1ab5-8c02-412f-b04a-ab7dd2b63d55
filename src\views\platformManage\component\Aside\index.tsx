import React, {FC, useEffect, useRef} from 'react';
import './index.scss';
import {DndProvider, useDrag, useDrop} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';

// 定义拖拽项类型
const ItemTypes = {
  MENU_ITEM: 'menuItem'
};

// 更新菜单项父级的接口方法
const updateMenuItemParent = async (
  itemId: string | number,
  newParentId: string | number
) => {
  try {
    console.log(`将菜单项 ${itemId} 移动到目录 ${newParentId} 下`);
    // 这里是接口调用的占位，后续实现
    // const response = await fetch('/api/menu/update-parent', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({
    //     id: itemId,
    //     parentId: newParentId
    //   }),
    // });
    // return await response.json();

    // 模拟成功响应
    return {success: true};
  } catch (error) {
    console.error('更新菜单项父级失败:', error);
    return {success: false, error};
  }
};

// 递归渲染菜单项的组件
const MenuItem: FC<any> = ({
  item,
  checkedNav,
  isOpenSub,
  handleNavClick,
  handleSubMenu,
  onDrop,
  level = 0
}) => {
  // 如果状态不为1，则不渲染该菜单项
  if (item.status !== undefined && item.status !== 1) {
    return null;
  }

  // 修改判断逻辑：只要有children就可以展开
  const canExpand = item.children?.length > 0 || item.system;
  // 判断是否可选中：有url或dataSetId才可选中
  const isSelectable = item.url || item.dataSetId || item.system;

  // 设置拖拽源 - 所有菜单都可以拖拽
  const [{isDragging}, drag] = useDrag({
    type: ItemTypes.MENU_ITEM,
    item: () => ({
      id: item.id,
      name: item.name,
      parentId: item.parentId
    }),
    collect: monitor => ({
      isDragging: monitor.isDragging()
    })
  });

  // 设置放置目标 - 所有菜单都可以作为放置目标
  const [{isOver, canDrop}, drop] = useDrop({
    accept: ItemTypes.MENU_ITEM,
    drop: (draggedItem: any, monitor) => {
      // 只处理直接放置在此项上的操作，忽略冒泡的事件
      if (monitor.didDrop()) {
        return;
      }

      if (draggedItem.id !== item.id) {
        onDrop(draggedItem.id, item.id);
      }
    },
    canDrop: (draggedItem: any) => {
      // 不能将项目拖放到自己下面或已经在该目录下
      return draggedItem.id !== item.id && draggedItem.parentId !== item.id;
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  // 合并拖放引用
  const ref = useRef(null);
  drag(ref);
  drop(ref);

  // 应用样式
  const opacity = isDragging ? 0.4 : 1;
  const backgroundColor =
    isOver && canDrop ? 'rgba(0, 255, 0, 0.1)' : 'transparent';

  return (
    <div key={item.id} ref={ref} style={{opacity, backgroundColor}}>
      <div
        className={`navAside-menu-item${
          checkedNav?.id === item.id ? ' itemClick' : ''
        }`}
        onClick={() => handleNavClick(item)}
        style={{paddingLeft: `${level * 10}px`}}
      >
        <div className="navAside-menu-item-icon">
          {item.icon ? (
            item.icon.includes('<svg') ? (
              <span dangerouslySetInnerHTML={{__html: item.icon}} />
            ) : (
              <i className={item.icon}></i>
            )
          ) : (
            <i className={`fa ${canExpand ? 'fa-folder' : 'fa-file'}`}></i>
          )}
        </div>

        <div className="navAside-menu-item-name">{item.name}</div>
        {canExpand && (
          <div className="navAside-menu-item-arrow">
            <i
              className={`fa fa-angle-right${
                isOpenSub.includes(item.id.toString()) ? ' open' : ''
              }`}
            ></i>
          </div>
        )}
      </div>

      {/* 子菜单 - 递归渲染 */}
      {item.children &&
        item.children.length > 0 &&
        isOpenSub.includes(item.id.toString()) && (
          <div className="navAside-submenu">
            {item.children
              .filter(
                (childItem: any) =>
                  childItem.status === undefined || childItem.status === 1
              )
              .map((childItem: any) => (
                <MenuItem
                  key={childItem.id}
                  item={childItem}
                  checkedNav={checkedNav}
                  isOpenSub={isOpenSub}
                  handleNavClick={handleNavClick}
                  handleSubMenu={handleSubMenu}
                  onDrop={onDrop}
                  level={level + 1}
                />
              ))}
          </div>
        )}
    </div>
  );
};

const renderAside: FC<any> = (props: any) => {
  const [isOpenSub, setIsOpenSub] = React.useState<string[]>([]);
  const [checkedNav, setCheckedNav] = React.useState<any>(null);
  const [menuList, setMenuList] = React.useState<any[]>([]);

  // 初始化菜单数据
  useEffect(() => {
    if (props.navMenuList) {
      setMenuList(props.navMenuList);
    }
  }, [props.navMenuList]);

  // 处理子菜单展开/收起
  const handleSubMenu = (id: string | number, event?: React.MouseEvent) => {
    event?.stopPropagation();
    // 确保id总是字符串类型
    const idStr = id.toString();
    setIsOpenSub(prev =>
      prev.includes(idStr)
        ? prev.filter(item => item !== idStr)
        : [...prev, idStr]
    );
  };

  // 处理菜单点击
  const handleNavClick = (item: any) => {
    // 如果有子菜单，则展开/收起子菜单
    if (item.children?.length > 0) {
      handleSubMenu(item.id);
    }

    // 如果有url或dataSetId，则可以选中并跳转
    if (item.url || item.dataSetId || item.system) {
      setCheckedNav(item);
      if (!item.path) {
        item.path = item.route
          ? `/platform/manage/page${item.route}`
          : `/platform/manage/page${item.id}`;
      }
      props.history.push(item.path);
    }
  };

  // 处理拖放完成事件
  const handleDrop = async (
    itemId: string | number,
    newParentId: string | number
  ) => {
    // 调用接口更新菜单项的父级
    const result = await updateMenuItemParent(itemId, newParentId);

    if (result.success) {
      // 如果接口调用成功，可以刷新菜单或者直接更新本地状态
      // 这里简单实现本地状态更新，实际项目中可能需要重新获取菜单数据

      // 通知父组件刷新菜单数据
      if (props.onMenuRefresh) {
        props.onMenuRefresh();
      }
    } else {
      // 处理错误情况
      console.error('更新菜单项父级失败');
    }
  };

  /* created 初始化 */
  useEffect(() => {
    if (props.initPathItem) {
      setCheckedNav(props.initPathItem);
    }
  }, [props.initPathItem]);

  // 初始化时展开父级菜单
  useEffect(() => {
    if (props.parentMenuIds && props.parentMenuIds.length > 0) {
      setIsOpenSub(prev => {
        // 合并现有的展开菜单和需要展开的父级菜单
        const newOpenSub = [...prev];
        props.parentMenuIds.forEach((id: string) => {
          if (!newOpenSub.includes(id)) {
            newOpenSub.push(id);
          }
        });
        return newOpenSub;
      });
    }
  }, [props.parentMenuIds]);
  /* created 初始化 end */

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="navAside">
        <div className="navAside-menu">
          {menuList
            .filter(item => item.status === undefined || item.status === 1)
            .map((item: any) => (
              <MenuItem
                key={item.id}
                item={item}
                checkedNav={checkedNav}
                isOpenSub={isOpenSub}
                handleNavClick={handleNavClick}
                handleSubMenu={handleSubMenu}
                onDrop={handleDrop}
              />
            ))}
        </div>
      </div>
    </DndProvider>
  );
};

export default renderAside;
