@import './corpus-i18n';

.Editor-header-actions {
  .header-action-btn {
    display: inline-block;
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    color: #666;
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    
    &:hover {
      background-color: #e8e8e8;
    }
    
    &.primary {
      color: #fff;
      background-color: #2468f2;
      border-color: #2468f2;
      
      &:hover {
        background-color: #1e54c9;
        border-color: #1e54c9;
      }
    }
    
    &.save-btn {
      color: #fff;
      background-color: #52c41a;
      border-color: #52c41a;
      
      &:hover {
        background-color: #3fa70e;
        border-color: #3fa70e;
      }
    }
    
    &.exit-btn {
      color: #fff;
      background-color: #ff4d4f;
      border-color: #ff4d4f;
      
      &:hover {
        background-color: #e63b3e;
        border-color: #e63b3e;
      }
    }
    
    &.m-1 {
      margin: 0 5px;
    }
  }
}
