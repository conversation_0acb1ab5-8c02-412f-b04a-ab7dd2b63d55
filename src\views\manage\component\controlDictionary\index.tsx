import React, {FC, useEffect, useState} from 'react';
import './index.scss';
import {Table, Pagination} from 'amis-ui';
import AMISRenderer from '@/component/AMISRenderer';
import {GetControlDictPage} from '@/utils/api/api';
// 表单项
import {formItems, containerItems, dataItems, functionItems, classOption, showItems} from '@/utils/amisEditorItems/formitems';
import { action } from 'mobx';

const ControlDictionary: FC<any> = (props: any) => {
  const [controlDictList, setControlDictList] = useState<any[]>([]);
  // 获取控件字典类型列表
  const handleGetControlDictPage = async () => {
    let params = {
      pageNo: 1,
      pageSize: 100
    };
    let res = await GetControlDictPage(params);
    console.log(res);
    if (res.code === 0) {
      setControlDictList(res.data.list);
    }
  };

  useEffect(() => {
    handleGetControlDictPage();
  }, []);
  useEffect(() => {
    console.log('controlDictList', controlDictList);
  }, [controlDictList]);

  const BuildTableJson = () => {
    let tableJson = {
      type: 'crud',
      syncLocation: false,
      api: {
        method: 'get',
        url: '/admin-api/system/control-dict/page'
      },
      bulkActions: [],
      itemActions: [],
      id: 'crud',
      perPageAvailable: [5, 10, 20, 50, 100],
      pageField: 'pageNo',
      perPageField: 'pageSize',
      headerToolbar: [
        {
          type: 'button',
          label: '新增',
          level: 'primary',
          id: 'u:add',
          actionType: 'dialog',
          dialog: {
            type: 'dialog',
            title: '新增',
            body: [
              {
                type: 'form',
                mode: 'horizontal',
                horizontal: {
                  left: 2,
                  right: 10,
                  offset: 2
                },
                api: {
                  method: 'post',
                  url: `/admin-api/system/control-dict/create`,
                  requestAdaptor: function (api: any, context: any) {
                    console.log(context);
                    let json = { type: context.amisControlType}
                    const jsonStr = JSON.stringify(json)
                    return {
                      ...api,
                      data: {
                        ...api.data,
                        classification: '表单项',
                        fieldType: context.amisControlType,
                        json: jsonStr,
                        status: 1
                      }
                    };
                  }
                },
                body: [
                  {type: 'input-text', label: '控件名称', name: 'controlType', id: 'u:controlType'},
                  {type: 'select',selectMode: "associated", label: '控件类型', searchable: true, name: 'amisControlType', id: 'u:amisControlType', leftOptions: classOption, options: [...formItems, ...containerItems, ...dataItems, ...functionItems, ...showItems]},
                  // {type: 'textarea', label: 'JSON', name: 'json', id: 'u:json'},

                ],
                actions: [
                  {
                    type: 'submit',
                    label: '提交',
                    primary: true
                  }
                ],

              }

            ],
          }
        },
        'reload'
      ],
      filter: {
        title: '查询条件',
        columnCount: 4,
        mode: 'horizontal',
        body: [
          {
            type: 'input-number',
            label: 'ID',
            name: 'id',
            id: 'u:09f0dce6f8dd'
          },

          // {
          //   type: 'select',
          //   label: '分类',
          //   name: 'classification',
          //   id: 'u:5f6f9ab5dfee',
          //   options: formItems
          // },
          {
            type: 'input-text',
            label: '控件名称',
            name: 'controlType',
            id: 'u:cb856afb5792',
            options: formItems[0].children,
            valueField: 'label',
            labelField: 'label'
          },
          {
            type: 'select',
            label: '控件类型',
            name: 'amisControlType',
            id: 'u:af2d33ef8d67',
            options: formItems[0].children,
            valueField: 'value',
            labelField: 'value',
            searchable: true
          }
        ],
        id: 'u:9d9858279086',
        actions: [
          {
            type: 'submit',
            label: '搜索',
            primary: true,
            id: 'u:b219601ee41d'
          }
        ],
        feat: 'Insert'
      },
      columns: [
        {
          label: 'ID',
          type: 'text',
          name: 'id',
          id: 'u:id'
        },
        {
          label: '分类',
          type: 'select',
          name: 'classification',
          id: 'u:classification',
          static: true
        },
        {
          label: '控件名称',
          type: 'text',
          name: 'controlType',
          id: 'u:controlType'
        },
        {
          label: '控件类型',
          type: 'select',
          name: 'amisControlType',
          id: 'u:amisControlType',
          static: true
        },
        {
          label: '启用',
          type: 'switch',
          name: 'status',
          id: 'u:status',
          falseValue: '0',
          trueValue: '1',
          onText: '已启用',
          offText: '未启用'
        },
        {
          label: '创建时间',
          type: 'input-datetime',
          name: 'createTime',
          id: 'u:createTime',
          valueFormat: 'x',
          displayFormat: 'YYYY-MM-DD HH:mm:ss',
          static: true
        },
        {
          type: 'operation',
          label: '操作',
          buttons: [
            {
              label: 'amis编辑',
              type: 'button',
              level: 'link',
              onClick: (event: any, actionProps: any) => {
                // console.log(event, actionProps.data.id)
                // console.log('clocked')
                // console.log()
                props.history.push(`/controlEditor/${actionProps.data.id}` + '?type=edit');
              }
                // props.history.push('/dsad/admin/1');
                // props.history.push(`/dsad/admin/${actionProps.data.id}`);
            },
            {
              label: 'JSON编辑',
              type: 'button',
              level: 'link',
              id: 'u:json_edit',
              actionType: 'dialog',
              dialog: {
                type: 'dialog',
                title: 'JSON编辑',

                body: [
                  {
                    type: 'form',
                    mode: 'horizontal',
                    horizontal: {
                      left: 2,
                      right: 10,
                      offset: 2
                    },
                    api: {
                      method: 'put',
                      url: `/admin-api/system/control-dict/update`,
                      data: {
                        id: '${id}',
                        controlType: '${controlType}',
                        amisControlType: '${amisControlType}',
                        json: '${json}',
                        status: '${status}'
                      }
                    },
                    body: [
                      {
                        type: "editor",
                        name: "json",
                        label: "编辑器",
                        language: "json",}]
                    // placeholder: "function() {\n  console.log('hello world')\n}"
                  }
                ]
              }
            },
            {
              label: '编辑',
              type: 'button',
              actionType: 'dialog',
              level: 'link',
              editorSetting: {
                behavior: 'update'
              },
              id: 'u:4d423864d6c0',
              dialog: {
                type: 'dialog',
                title: '编辑',
                body: [
                  {
                    type: 'form',
                    api: {
                      method: 'put',
                      url: '/admin-api/system/control-dict/update',
                    },
                    mode: 'horizontal',
                    horizontal: {
                      left: 2,
                      right: 10,
                      offset: 2
                    },
                    body: [{
                      type: 'input-text',
                      label: 'ID',
                      name: 'id',
                      value: '${id}',
                      hidden: true
                    },
                    {
                      type: 'input-text',
                      label: '控件名称',
                      name: 'controlType',
                      value: '${controlType}',
                    },
                    {type: 'select',selectMode: "associated", label: '控件类型', searchable: true, name: 'amisControlType', id: 'u:amisControlType', leftOptions: classOption, options: [...formItems, ...containerItems, ...dataItems, ...functionItems, ...showItems]},
                    {
                      type: 'editor',
                      label: 'JSON',
                      name: 'json',
                      language: "json",
                      value: '${json}',
                    },
                    {
                      label: '启用',
                      type: 'switch',
                      name: 'status',
                      id: 'u:status',
                      falseValue: '0',
                      trueValue: '1',
                      onText: '已启用',
                      offText: '未启用'
                    },
                  ],
                    actions: [
                      {
                        type: 'button',
                        label: '取消',
                        onEvent: {
                          click: {
                            actions: [
                              {
                                actionType: 'cancel',
                                componentId: 'u:1fb5ccb04730'
                              }
                            ]
                          }
                        },
                        level: 'default'
                      },
                      {
                        type: 'button',
                        label: '提交',
                        onEvent: {
                          click: {
                            actions: [
                              {
                                actionType: 'submit',
                                componentId: 'u:1fb5ccb04730'
                              }
                            ]
                          }
                        },
                        level: 'primary'
                      }
                    ],
                    feat: 'Edit',
                    dsType: 'api',
                    // labelAlign: 'top',
                    title: '表单',
                    resetAfterSubmit: true
                  }
                ],
                actionType: 'drawer',
                actions: [
                  {
                    type: 'button',
                    actionType: 'cancel',
                    label: '取消',
                    id: 'u:2e86c554e48e'
                  },
                  {
                    type: 'button',
                    actionType: 'confirm',
                    label: '确定',
                    primary: true,
                    id: 'u:392213d59e8d'
                  }
                ],
                showCloseButton: true,
                closeOnOutside: false,
                closeOnEsc: false,
                showErrorMsg: true,
                showLoading: true,
                draggable: false,
                size: 'lg',
                resizable: false,
                editorSetting: {
                  displayName: '编辑'
                }
              }
            },
            {
              label: '查看',
              type: 'button',
              actionType: 'drawer',
              level: 'link',
              editorSetting: {
                behavior: 'view'
              },
              drawer: {
                type: 'drawer',
                title: '查看详情',
                body: [
                  {
                    type: 'form',
                    body: [
                      {
                        label: 'ID',
                        type: 'text',
                        name: 'id',
                      },
                      {
                        label: '分类',
                        type: 'select',
                        name: 'classification',
                        static: true
                      },
                      {
                        label: '控件名称',
                        type: 'text',
                        name: 'controlType',
                      },
                      {
                        label: '控件类型',
                        type: 'select',
                        name: 'amisControlType',
                        static: true
                      },
                      {
                        label: 'JSON',
                        type: 'text',
                        name: 'json',
                      },
                      {
                        label: '启用',
                        type: 'switch',
                        name: 'status',
                        falseValue: '0',
                        trueValue: '1',
                        onText: '已启用',
                        offText: '未启用'
                      },
                      {
                        label: '创建时间',
                        type: 'input-datetime',
                        name: 'createTime',
                        valueFormat: 'x',
                        displayFormat: 'YYYY-MM-DD HH:mm:ss',
                        static: true
                      },
                    ],
                    id: 'u:2af2f2b5c2fd',
                    feat: 'View',
                    dsType: 'api',
                    labelAlign: 'top',
                    title: '表单',
                    mode: 'flex',
                    static: true,
                    actions: [
                      {
                        type: 'button',
                        label: '取消',
                        onEvent: {
                          click: {
                            actions: [
                              {
                                actionType: 'cancel',
                                componentId: 'u:e9f0e2b1f53a'
                              }
                            ]
                          }
                        },
                        level: 'default'
                      },
                      {
                        type: 'button',
                        label: '提交',
                        onEvent: {
                          click: {
                            actions: [
                              {
                                actionType: 'submit',
                                componentId: 'u:e9f0e2b1f53a'
                              }
                            ]
                          }
                        },
                        level: 'primary'
                      }
                    ]
                  }
                ],
                actionType: 'drawer',
                actions: [
                  {
                    type: 'button',
                    actionType: 'cancel',
                    label: '取消'
                  },
                  {
                    type: 'button',
                    actionType: 'confirm',
                    label: '确定',
                    primary: true
                  }
                ],
                showCloseButton: true,
                closeOnOutside: false,
                closeOnEsc: false,
                showErrorMsg: true,
                showLoading: true,
                draggable: false,
                size: 'lg',
                resizable: false,
                editorSetting: {
                  displayName: '查看详情'
                }
              }
            },
            {
              type: 'button',
              label: '删除',
              actionType: 'dialog',
              level: 'link',
              className: 'text-danger',
              dialog: {
                type: 'dialog',
                title: '',
                className: 'py-2',
                actions: [
                  {
                    type: 'action',
                    actionType: 'cancel',
                    label: '取消'
                  },
                  {
                    type: 'action',
                    actionType: 'submit',
                    label: '删除',
                    level: 'danger'
                  }
                ],
                body: [
                  {
                    type: 'form',
                    wrapWithPanel: false,
                    api:
                      `delete:/admin-api/system/control-dict/delete?id=` +
                      '${id}',
                    body: [
                      {
                        type: 'tpl',
                        className: 'py-2',
                        tpl: '确认删除选中项？'
                      }
                    ],
                    feat: 'Insert',
                    dsType: 'api',
                    labelAlign: 'left'
                  }
                ],
                actionType: 'dialog',
                showCloseButton: true,
                closeOnOutside: false,
                closeOnEsc: false,
                showErrorMsg: true,
                showLoading: true,
                draggable: false,
                editorSetting: {
                  displayName: '删除'
                }
              }
            }
          ],
          id: 'u:8b6efbd2f849'
        }
      ]
    };
    return tableJson;
  };
  return (
    <div className="controlDictionary">
      <AMISRenderer
        schema={BuildTableJson()}
        embedMode={true}
        //   data={}
      /> 
    </div>
  );
};

export default ControlDictionary;
