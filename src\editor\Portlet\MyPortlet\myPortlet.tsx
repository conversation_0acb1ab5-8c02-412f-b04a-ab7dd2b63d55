import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';
import PortletDataInteraction from './PortletDataInteraction';
import EntryItemsManager from './EntryItemsManager';
import {visibleOn} from '@/editor/EChartsEditor/Common';

export class PortletPlugin extends BasePlugin {
  static id = 'PortletPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'my-portlet';
  $schema = '/schemas/PortletSchema.json';

  // 组件基本信息
  name = '快捷入口';
  panelTitle = '快捷入口';
  icon = 'fa fa-window-maximize';
  panelIcon = 'fa fa-window-maximize';
  pluginIcon = 'icon-tabs-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '快捷入口组件';
  docLink = '/amis/zh-CN/components/portlet';
  tags = ['门户'];

  // 组件默认配置
  // 组件默认配置
  scaffold = {
    type: 'my-portlet',
    title: '快捷入口',
    remark: '帮助信息',
    icon: '',

    showHeader: true,
    showMore: true,
    showTabs: true,
    tabs: [],
    tabsMode: 'line',
    divider: true,
    scrollable: false,
    entries: []
  };

  // 添加事件定义
  events = [
    {
      eventName: 'click',
      eventLabel: '菜单项点击',
      description: '点击导航项时触发'
      // ...
    },
    {
      eventName: 'select',
      eventLabel: '菜单项选中',
      description: '选中导航项时触发'
      // ...
    },
    {
      eventName: 'expand',
      eventLabel: '菜单展开',
      description: '菜单展开时触发'
      // ...
    },
    {
      eventName: 'collapse',
      eventLabel: '菜单折叠',
      description: '菜单折叠时触发'
      // ...
    },
    {
      eventName: 'loaded',
      eventLabel: '数据加载完成',
      description: '数据加载完成时触发'
      // ...
    }
  ];

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 临时保存的数据，用于恢复状态
  private savedTabs: any[] = [];
  private timerRefs: Array<any> = []; // 使用any类型兼容所有环境的定时器返回值
  private isDestroyed = false; // 标记组件是否已被销毁

  // 安全更新schema
  safeChangeValue(context: any, newSchema: any) {
    if (this.isDestroyed) {
      console.warn('组件已销毁，不执行更新');
      return false;
    }

    try {
      if (
        context &&
        context.node &&
        !context.node.disposedNow &&
        this.manager &&
        this.manager.store &&
        typeof this.manager.store.changeValueById === 'function'
      ) {
        this.manager.store.changeValueById(context.node.id, newSchema);
        return true;
      }
    } catch (error) {
      console.warn('安全更新schema失败:', error);
    }
    return false;
  }

  // 清理计时器
  clearTimers() {
    if (this.timerRefs && this.timerRefs.length) {
      this.timerRefs.forEach(timer => {
        if (timer) {
          clearTimeout(timer);
        }
      });
      this.timerRefs = [];
    }
  }

  // 组件生命周期 - 当组件即将卸载时
  componentWillUnmount() {
    try {
      this.isDestroyed = true;
      // 清理计时器
      this.clearTimers();
      console.log('快捷入口组件销毁，已执行清理');
    } catch (error) {
      console.warn('组件清理时出错:', error);
    }
  }

  // 数据交互组件
  panelBodyCreator = (context: any) => {
    let dataOption: any[] = [];
    // 从URL中提取应用ID
    let applicationId = ''; // 默认值
    let applicationPageId = ''; // 默认值
    try {
      // 获取URL路径
      const pathname = window.location.hash;
      // 方法1：正则表达式匹配portletEditor后面的数字
      const matches = pathname.match(/\/portletEditor(\d+)/);
      if (matches && matches[1]) {
        applicationId = matches[1];
      }
      // 方法1：正则表达式匹配portletEditor后面的数字
      const pageMatches = pathname.match(/\/editCode=(\d+)/);
      if (pageMatches && pageMatches[1]) {
        applicationPageId = pageMatches[1];
      }
    } catch (e) {
      console.error('提取应用ID时出错:', e);
    }

    // 清理之前的计时器
    this.clearTimers();

    const handleOptions = (tabs: any[]) => {
      if (this.isDestroyed) {
        return;
      }

      // 如果tabs无效则停止处理
      if (!Array.isArray(tabs)) {
        console.warn('handleOptions收到无效数据:', tabs);
        return;
      }

      try {
        // 保存到插件实例中，避免状态丢失
        this.savedTabs = JSON.parse(JSON.stringify(tabs));

        // 创建新的schema
        const newSchema = {
          // ...context.schema,
          tabs: tabs
        };

        // 安全更新schema
        this.safeChangeValue(context, newSchema);

        // 重新渲染面板 - 只在上下文有效时进行
        if (
          !this.isDestroyed &&
          typeof this.panelBodyCreator === 'function' &&
          context &&
          context.node &&
          !context.node.disposedNow
        ) {
          // 使用异步操作前先检查组件状态
          const timerId = setTimeout(() => {
            if (this.isDestroyed) return;
            try {
              if (context && context.node && !context.node.disposedNow) {
                this.panelBodyCreator(context);
              }
            } catch (error) {
              console.warn('异步面板渲染出错:', error);
            }
          }, 0);
          this.timerRefs.push(timerId);
        }
      } catch (error) {
        console.error('处理选项时出错:', error);
      }
    };

    const handleEntries = (entries: any[]) => {
      if (this.isDestroyed) {
        return;
      }

      // 如果entries无效则停止处理
      if (!Array.isArray(entries)) {
        console.warn('handleEntries收到无效数据:', entries);
        return;
      }

      try {
        // 创建新的schema
        const newSchema = {
          // ...context.schema,
          entries: entries
        };

        // 安全更新schema
        this.safeChangeValue(context, newSchema);

        // 重新渲染面板
        if (
          !this.isDestroyed &&
          typeof this.panelBodyCreator === 'function' &&
          context &&
          context.node &&
          !context.node.disposedNow
        ) {
          const timerId = setTimeout(() => {
            if (this.isDestroyed) return;
            try {
              if (context && context.node && !context.node.disposedNow) {
                this.panelBodyCreator(context);
              }
            } catch (error) {
              console.warn('异步面板渲染出错:', error);
            }
          }, 0);
          this.timerRefs.push(timerId);
        }
      } catch (error) {
        console.error('处理入口项时出错:', error);
      }
    };

    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '标题栏',
              id: 'properties-basic',
              body: [
                getSchemaTpl('layout:originPosition', {
                  value: 'left-top'
                }),
                getSchemaTpl('switch', {
                  name: 'showHeader',
                  label: '显示标题栏',
                  value: false,
                  onChange: (
                    value: boolean,
                    oldValue: boolean,
                    data: any,
                    form: any
                  ) => {
                    // 保存showHeader状态
                    const schema = {
                      ...context.schema,
                      showHeader: value
                    };

                    if (value) {
                      // 如果打开标题栏，确保有默认值
                      if (!schema.header || !schema.header.title) {
                        schema.header = schema.header || {};
                        // schema.header.title = '快捷入口';
                      }
                    }

                    // 使用安全更新方法更新schema
                    setTimeout(() => {
                      this.safeChangeValue(context, schema);
                    }, 0);

                    return value;
                  }
                }),
                {
                  type: 'container',
                  className: 'ae-formItemControl',
                  visibleOn: 'this.showHeader',
                  body: [
                    getSchemaTpl('title', {
                      name: 'title',
                      label: '标题名称',
                      placeholder: '请输入标题名称'
                    }),
                    {
                      type: 'icon-selector',
                      name: 'icon',
                      label: '标题图标',
                      placeholder: '请选择图标',
                      returnSvg: true
                    },
                    getSchemaTpl('remark', {
                      name: 'remark',
                      label: '帮助提示',
                      placeholder: '请输入帮助提示'
                    }),
                    getSchemaTpl('switch', {
                      name: 'showMore',
                      label: '查看更多',
                      value: true
                    }),
                    {
                      visibleOn: 'this.showMore',
                      type: 'container',
                      className: 'mb-3',
                      body: [
                        {
                          type: 'select',
                          name: 'linkType',
                          label: '链接类型',
                          value: 'external',
                          options: [
                            {
                              label: '打开内部页面',
                              value: 'internal'
                            },
                            {
                              label: '打开外部链接',
                              value: 'external'
                            }
                          ]
                        },
                        {
                          type: 'select',
                          name: 'internalPage',
                          label: '选择页面',
                          source: {
                            method: 'GET',
                            url: `/admin-api/system/application-page-and-class/list?applicationId=${applicationId}&applicantOrBackend=1`,
                            adaptor: (payload: any) => {
                              // 过滤type=1的数据
                              const filteredData = payload.data.filter(
                                (item: any) => item.type === 1
                              );
                              const options = filteredData.map((item: any) => ({
                                label: item.name,
                                value: item.id
                              }));
                              dataOption = options;
                              return {
                                ...payload,
                                data: {
                                  options: options
                                }
                              };
                            }
                          },
                          onChange: (
                            value: any,
                            oldValue: any,
                            model: any,
                            form: any
                          ) => {
                            if (!value) return;

                            // 查找选中的数据源项
                            let selectedOption = dataOption.find(
                              item => item.value == value
                            );
                            console.log('选中的数据源项2:', selectedOption);
                            if (selectedOption && selectedOption.label) {
                              // 保存关联的数据表ID
                              form.setValueByName(
                                'internalName',
                                selectedOption.label
                              );
                            }
                          },
                          visibleOn: 'this.linkType === "internal"'
                        },
                        getSchemaTpl('title', {
                          name: 'href',
                          label: '链接地址',
                          placeholder: '请输入链接地址',
                          visibleOn: 'this.linkType !== "internal"'
                        }),
                        {
                          type: 'hidden',
                          name: 'internalName'
                        }
                      ]
                    }
                  ]
                },

                getSchemaTpl('switch', {
                  name: 'scrollable',
                  label: '溢出滚动',
                  value: false
                }),
                getSchemaTpl('switch', {
                  name: 'mountOnEnter',
                  label: '点开加载',
                  value: true
                }),
                getSchemaTpl('switch', {
                  name: 'unmountOnExit',
                  label: '隐藏销毁',
                  value: false
                })
              ]
            },
            {
              title: '分组',
              body: [
                getSchemaTpl('switch', {
                  name: 'showTabs',
                  label: '开启分组',
                  value: true
                }),
                {
                  name: 'tabsMode',
                  label: '展示形式',
                  type: 'select',
                  visibleOn: 'this.showTabs === true',
                  options: [
                    {
                      label: '线性',
                      value: 'line'
                    },
                    {
                      label: '卡片',
                      value: 'card'
                    },
                    {
                      label: '单选框',
                      value: 'radio'
                    },
                    {
                      label: '垂直',
                      value: 'vertical'
                    },
                    {
                      label: '平铺',
                      value: 'tiled'
                    }
                  ],
                  value: 'line'
                },
                <PortletDataInteraction
                  appId={applicationId}
                  tabs={context?.schema?.tabs}
                  handleTabs={handleOptions}
                  context={context}
                />
              ]
            },
            {
              title: '内容',
              body: [
                getSchemaTpl('switch', {
                  name: 'showContent',
                  label: '显示内容',
                  value: true
                }),

                <EntryItemsManager
                  appId={applicationId}
                  entries={context?.schema?.entries}
                  handleEntries={handleEntries}
                  context={context}
                />
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false,
              readonly: true
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(PortletPlugin);
