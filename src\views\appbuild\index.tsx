import React, {useState} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {RouteComponentProps, Switch} from 'react-router';
import {Layout} from 'amis';

import {
  getTeamAndProjectList //团队和项目列表
} from '@/utils/api/api';

import CommonHeader from '@/views/components/CommonHeader';
import LayoutRenderAside from '@/views/components/LayoutRenderAside';
import SwitchRenderContent from '@/views/appbuild/compontents/SwitchRenderContent';

export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<{
    team?: string;
    project?: string;
  }>) {
    // 固定菜单列表
    const appbuildViewsList = [
      {
        id: 'm-1',
        icon: 'fa-solid fa-house',
        name: '搭建首页',
        path: 'home',
        type: 'appbuild'
      },
      // {
      //   id: 'm-2',
      //   icon: 'fa-solid fa-people-roof',
      //   name: '搭建后台管理',
      //   path: 'appmanage',
      //   type: 'appbuild'
      // },
      {
        id: 'm-3',
        icon: 'fa-solid fa-stamp',
        name: '审批',
        path: 'approval',
        type: 'appbuild'
      },
      {
        id: 'm-4',
        icon: 'fa-solid fa-message',
        name: 'wflow消息',
        path: 'importMessage',
        type: 'appbuild'
      },
      {
        id: 'm-5',
        icon: 'fa-solid fa-table-list',
        name: '组件模板',
        path: 'componentTemplate',
        type: 'appbuild'
      },
      {
        id: 'm-6',
        icon: 'fa-solid fa-table-list',
        name: '应用模板',
        path: 'appTemplate',
        type: 'appbuild'
      },
      {
        id: 'm-7',
        icon: 'fa-solid fa-table-list',
        name: '表单模板',
        path: 'formTemplate',
        type: 'appbuild'
      },
      {
        id: 'm-12',
        icon: 'fa-solid fa-table-list',
        name: '页面模板',
        path: 'pageTemplate',
        type: 'appbuild'
      },
      {
        id: 'm-8',
        icon: 'fa-solid fa-image',
        name: '图片管理',
        path: 'imageManage',
        type: 'appbuild'
      },
      {
        id: 'm-9',
        icon: 'fa-solid fa-image',
        name: '图标管理',
        path: 'iconManage',
        type: 'appbuild'
      },
      {
        id: 'm-10',
        icon: 'fa fa-icons',
        name: 'Amis图标库',
        path: 'icons',
        type: 'appbuild'
      },
      {
        id: 'm-11',
        icon: 'fab fa-font-awesome',
        name: 'fontAwesome图标库',
        path: 'fontawesome',
        type: 'appbuild'
      }
    ];
    // 团队项目菜单列表
    const [teamList, setTeamList] = React.useState<any>([]);
    const [menuList, setMenuList] = React.useState<any>([]);
    // 接口是否请求完成
    const [isApiTeamList, setIsApiTeamList] = React.useState<boolean>(false);
    // 获取团队项目菜单列表
    const handleGetTeamAndProjectList = () => {
      getTeamAndProjectList()
        .then((res: any) => {
          setIsApiTeamList(true);
          if (res.code === 0) {
            setTeamList(res.data);
            // 权限过滤逻辑
            const {
              hasComponentAuth,
              hasAppTemplateAuth,
              hasFormTemplateAuth,
              hasImageAuth,
              hasIconAuth
            } = store.userInfo || {};
            const filteredAppbuildViewsList = appbuildViewsList.filter(item => {
              if (item.id === 'm-5') return hasComponentAuth;
              if (item.id === 'm-6') return hasAppTemplateAuth;
              if (item.id === 'm-7') return hasFormTemplateAuth;
              if (item.id === 'm-8') return hasImageAuth;
              if (item.id === 'm-9') return hasIconAuth;
              return true;
            });
            setMenuList([...filteredAppbuildViewsList, ...res.data]);
          } else {
            console.error(res.msg);
          }
        })
        .catch(err => {
          setIsApiTeamList(true);
          console.error(err);
        });
    };

    React.useEffect(() => {
      if (!isApiTeamList) {
        handleGetTeamAndProjectList();
      }
    }, [isApiTeamList]);

    return (
      <Layout
        aside={
          <LayoutRenderAside
            showCreateTeam={true}
            store={store}
            location={location}
            history={history}
            match={match}
            menuList={menuList}
            updateMenuData={() => handleGetTeamAndProjectList()}
          />
        }
        asideClassName={'asideClass'}
        header={
          <CommonHeader
            store={store}
            history={history}
            type='appbuild'
            showTenantInfo={true}
            className="layoutRenderHeader"
          />
        }
        headerClassName={'headerClass'}
        headerFixed={true}
        folded={store.asideFolded}
        offScreen={store.offScreen}
      >
        <Switch>
          <SwitchRenderContent
            store={store}
            location={location}
            history={history}
            match={match}
            teamList={teamList}
            updateMenuData={() => handleGetTeamAndProjectList()}
          />
        </Switch>
      </Layout>
    );
  })
);
