import React from 'react';
import './index.scss';

export interface MenuItem {
  key: string;
  label: string;
  icon?: string;
}

interface PageDataSettingsSiderProps {
  activeKey: string;
  onChange: (key: string) => void;
}

const menuItems: MenuItem[] = [
  { key: 'datasetSettings', label: '数据集设置', icon: 'fa fa-database' },
  { key: 'querySettings', label: '查询设置', icon: 'fa fa-search' },
  { key: 'topToolbar', label: '顶部工具栏', icon: 'fa fa-wrench' },
  { key: 'bottomToolbar', label: '底部工具栏', icon: 'fa fa-bars' },
  { key: 'operationSettings', label: '操作栏设置', icon: 'fa fa-cog' },
  { key: 'singleOperation', label: '单条操作', icon: 'fa fa-edit' }
];

const PageDataSettingsSider: React.FC<PageDataSettingsSiderProps> = ({
  activeKey,
  onChange
}) => {
  return (
    <div className="page-data-settings-sider">
      <ul className="settings-menu">
        {menuItems.map(item => (
          <li 
            key={item.key}
            className={`settings-menu-item ${activeKey === item.key ? 'active' : ''}`}
            onClick={() => onChange(item.key)}
          >
            {item.icon && <i className={item.icon}></i>}
            <span>{item.label}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default PageDataSettingsSider; 