.org-picker-modal {
  .modal-mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1000;
  }

  .modal-content {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: var(--background);
    z-index: 1001;
    min-width: 600px;
    min-height: 400px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--borderColor);
    font-weight: 600;
  }

  .picker {
    display: flex;
    position: relative;
    min-height: 350px;
  }

  .candidate {
    width: 320px;
    border-right: 1px solid var(--borderColor);
    flex-shrink: 0;
  }

  .search-container {
    padding: 12px 16px 0 16px;
    position: relative;
  }

  .search-input {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    border: 1px solid var(--borderColor);
    background: var(--light-bg);
    padding: 0 32px 0 8px;
  }

  .search-clear {
    position: absolute;
    right: 24px;
    top: 28px;
    cursor: pointer;
    color: var(--text-color);
    font-size: 16px;
    transform: translateY(-50%);
  }

  .dept-path {
    margin: 10px 0;
    color: var(--text-color);
    font-size: 14px;
  }

  .top-dept {
    margin-left: 20px;
    color: var(--primary);
    cursor: pointer;
  }

  .selection-controls {
    margin-bottom: 5px;
  }

  .clear-btn {
    color: var(--danger);
    margin-left: 10px;
    cursor: pointer;
  }

  .org-items {
    flex: 1;
    overflow-y: auto;
    padding: 8px 16px 16px 16px;
  }

  .loading-container {
    text-align: center;
    padding: 20px 0;
  }

  .empty-data {
    color: #aaa;
    text-align: center;
    padding: 40px 0;
  }

  .org-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 4px;
    margin-bottom: 6px;
    cursor: pointer;
    background: var(--background);
    box-sizing: border-box;

    &.selected {
      border: 1px solid var(--primary);
      padding: 7px 9px;
    }
  }

  .next-dept {
    float: right;
    color: var(--primary);
    cursor: pointer;
    margin-left: 8px;

    &.next-dept-disable {
      color: var(--text--muted-color);
      cursor: not-allowed;
    }
  }

  .selected-panel {
    width: 320px;
    flex-shrink: 0;
  }

  .count-header {
    padding: 16px 16px 8px 16px;
    font-size: 15px;
    color: var(--text-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .selected-items {
    height: 310px;
    overflow-y: auto;
    padding: 0 16px 16px 16px;
  }

  .selected-item {
    position: relative;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 8px 10px;
    margin-bottom: 8px;
  }

  .remove-icon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 18px;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 12px 24px;
    border-top: 1px solid var(--borderColor);
  }

  .cancel-btn {
    margin-right: 12px;
  }
}