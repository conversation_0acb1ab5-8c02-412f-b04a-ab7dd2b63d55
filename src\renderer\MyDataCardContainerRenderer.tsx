import React from 'react';
import {Renderer, render as amisRender} from 'amis';
import {FormControlProps} from 'amis-core';

export interface MyContainerProps extends FormControlProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'full';
  label?: string;
  name: string;
  body?: any;
  bodyClassName?: string;
  wrapperComponent?: string;
  wrapperBody?: boolean;
  draggable?: boolean | string;
  draggableConfig?: {
    axis?: 'both' | 'x' | 'y';
    defaultPosition?: {
      x: number;
      y: number;
    };
    bounds?: {
      left?: number;
      right?: number;
      top?: number;
      bottom?: number;
    } | string;
    grid?: [number, number];
    handle?: string;
    cancel?: string;
    scale?: number;
    enableUserSelect?: boolean;
  };
  style?: {
    [propName: string]: any;
  };
  // size?: string;
  className?: string;
  value?: any;
  static?: boolean;
  dataCardId?: number;
  enableDataBroadcast?: boolean;
  colorMode?: 'default' | 'theme';
  displayDevice?: 'both' | 'pc' | 'mobile';
}

@Renderer({
  type: 'my-container',
  name: 'my-container'
})
export class MyContainerRenderer extends React.PureComponent<MyContainerProps> {
  static defaultProps = {
    className: '',
    draggableConfig: {
      axis: 'both',
      scale: 1,
      enableUserSelect: false
    },
    displayDevice: 'pc',
    colorMode: 'theme'
  };

  constructor(props: MyContainerProps) {
    super(props);
  }

  renderBody() {
    const {
      body,
      render,
      bodyClassName
    } = this.props;

    return (
      <div className={bodyClassName}>
        {body ? (
          render('body', body, {
            // 可以传递一些上下文数据
          })
        ) : (
          <div className="text-center text-muted p-2">
            请选择数据卡片
          </div>
        )}
      </div>
    );
  }

  render() {
    const {
      static: isStatic,
      name,
      label,
      value,
      body,
      bodyClassName,
      wrapperComponent,
      wrapperBody,
      draggable,
      draggableConfig,
      style,
      size,
      className,
      render,
      data,
      dataCardId,
      enableDataBroadcast,
      colorMode,
      displayDevice
    } = this.props as any;

    // 处理静态展示模式
    if (isStatic) {
      if (render) {
        return render('static', {
          type: 'static',
          label,
          name,
          value: value || (data && data[name]) || '--'
        });
      }

      // 备用渲染方法：直接使用div
      return (
        <div className="static-value">
          <div className="static-label">{label || name}</div>
          <div className="static-content">
            {JSON.stringify(value || (data && data[name]) || '--')}
          </div>
        </div>
      );
    } else {
      // 构建容器样式
      const containerStyle = {
        ...style
      };

      // 添加尺寸类名
      let containerClassName = className || '';
      if (size) {
        containerClassName += ` size-${size}`;
      }

      // 添加颜色模式类名
      if (colorMode === 'theme') {
        containerClassName += ' theme-container';
      }

      // 添加设备显示类名
      if (displayDevice === 'pc') {
        containerClassName += ' pc-only';
      } else if (displayDevice === 'mobile') {
        containerClassName += ' mobile-only';
      }

      // 数据播报相关类名
      if (enableDataBroadcast) {
        containerClassName += ' data-broadcast-enabled';
      }

      // 渲染容器
      const Tag = wrapperComponent || 'div';
      return (
        <Tag 
          className={`data-card-container ${containerClassName}`}
          style={containerStyle}
        >
          {this.renderBody()}
        </Tag>
      );
    }
  }
}