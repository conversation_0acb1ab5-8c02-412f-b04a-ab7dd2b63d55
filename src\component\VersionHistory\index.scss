.version-history-modal {
  .cxd-Modal-content {
    max-height: 80vh;
    overflow: hidden;
  }

  .version-history-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0 10px;
  }

  .version-history-search {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;

    .version-search-box {
      width: 100%;
    }
  }

  .version-list {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 10px;
  }

  .version-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    position: relative;

    &:hover {
      background-color: #f7f7f7;
    }

    &.current-version {
      .version-item-dot {
        background-color: #2468f2;
      }
    }
  }

  .version-item-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ccc;
    margin-right: 15px;
    margin-top: 6px;
  }

  .version-item-content {
    flex: 1;
  }

  .version-item-time {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .version-item-info {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  .version-item-version {
    font-size: 16px;
    font-weight: 500;
    margin-right: 10px;
  }

  .version-item-author {
    font-size: 14px;
    color: #666;
  }

  .version-item-remark {
    font-size: 14px;
    color: #999;
  }

  .version-item-actions {
    display: flex;
    align-items: center;
  }

  .version-item-action {
    color: #666;
    &:hover {
      color: #2468f2;
    }
  }

  .version-loading {
    text-align: center;
    padding: 20px 0;
    color: #999;
  }

  .version-empty {
    text-align: center;
    padding: 20px 0;
    color: #999;
  }
} 