import React, {useEffect, useState, useCallback} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {Layout, toast} from 'amis';
import {Route, RouteComponentProps, Switch} from 'react-router';
import AMISRenderer from '@/component/AMISRenderer';
import {
  createIframeObj,
  createReportObj,
  createFromObj
} from '@/utils/schemaPageTemplate/createPageObjs';

// 数据源管理
import ManageDataSource from '@/views/platformManage/component/dataSource/index';
import ManageDataSet from '@/views/platformManage/component/dataSet/index';
import CreateDataSet from '@/views/platformManage/component/createDataSet/index';
import CreateDatasetImportPage from '@/views/platformManage/component/CreateDatasetImportPage/index';
import DatasetImportPage from '@/views/platformManage/component/datasetImportPage/index';
// 页面链接页面组件
import PagelinkContent from '@/component/PagelinkContent/index';
import Company from '@/views/platformManage/component/company/index';
import MenuManager from '@/views/platformManage/component/menuManager/index';

// 页面样式
import './index.scss';
// 页面组件
import CommonHeader from '@/views/components/CommonHeader';
import RenderAside from './component/Aside/index';
import ControlDictionary from './component/controlDictionary/index';
// 接口引入
import {getApplicationPageAndClassBackendList} from '@/utils/api/api';

export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<any>) {
    const baseMenuList = [
      // {
      //   id: 1,
      //   name: '企业设置',
      //   path: '/platform/manage/company',
      //   icon: 'fa fa-building',
      //   children: [],
      //   system: true
      // },
      {
        id: 2,
        name: '菜单管理',
        path: '/platform/manage/menuManager',
        icon: 'fa fa-bars',
        children: [],
        system: true
      },
      {
        id: 3,
        name: ' 数据管理',
        path: '/platform/manage/dataManage',
        icon: 'fa fa-building',
        type: 2,
        children: [
          {
            id: 21,
            name: '控件字典管理',
            path: '/platform/manage/controlDictionary',
            icon: 'fas fa-hammer',
            children: [],
            system: true
          },
          {
            id: 22,
            name: '数据源管理',
            path: '/platform/manage/dataSource',
            icon: 'fa fa-database',
            children: [],
            system: true
          },
          {
            id: 23,
            name: '数据集管理',
            path: '/platform/manage/dataSet',
            icon: 'fa fa-table',
            children: [],
            system: true
          },
          // {
          //   id: 24,
          //   name: '数据集导入页面',
          //   path: '/platform/manage/datasetImportPage',
          //   icon: 'fa fa-table',
          //   children: [],
          //   system: true
          // }
        ]
      }
    ];
    const [navMenuList, setNavMenuList] = useState<any[]>([]);

    const [loading, setLoading] = useState(true);

    // 获取后台管理的菜单：应用页面与分类
    const handleGetApplicationPageAndClassList = async () => {
      setLoading(true);
      try {
        let data = {
          applicantOrBackend: 3
        };
        const res = await getApplicationPageAndClassBackendList(data);
        if (res.code == 0) {
          // 转换API返回的数据格式
          const apiMenuList = res.data.map((item: any) => {
            return {
              ...item,
              // path: item.route?`/manage/page${item.route}`:`/manage/page${item.id}`,
              path: item.route
                ? `/platform/manage/page${item.route}`
                : `/platform/manage/page${item.id}`,
              // icon: item.icon || 'fa fa-table',
              icon: item.icon,
              children: item.children || []
            };
          });

          // 获取固定的基础菜单项
          const baseMenuItems = [
            {
              id: 1,
              name: '企业设置',
              path: '/platform/manage/company',
              icon: 'fa fa-building',
              children: [],
              system: true
            },
            {
              id: 2,
              name: '菜单管理',
              path: '/platform/manage/menuManager',
              icon: 'fa fa-bars',
              children: [],
              system: true
            },
            {
              id: 3,
              name: ' 数据管理',
              type: 2,
              path: '/platform/manage/dataManage',
              icon: 'fa fa-building',
              children: [
                {
                  id: 22,
                  name: '数据源管理',
                  path: '/platform/manage/dataSource',
                  icon: 'fa fa-database',
                  children: [],
                  system: true
                },
                {
                  id: 23,
                  name: '数据集管理',
                  path: '/platform/manage/dataSet',
                  icon: 'fa fa-table',
                  children: [],
                  system: true
                },
                // {
                //   id: 24,
                //   name: '数据集导入页面',
                //   path: '/platform/manage/datasetImportPage',
                //   icon: 'fa fa-table',
                //   children: [],
                //   system: true
                // }
              ]
            }
          ];

          // 合并基础菜单和API返回的菜单
          const newNavMenuList = [...baseMenuList, ...apiMenuList];
          setNavMenuList(newNavMenuList);
          console.log('newNavMenuList', newNavMenuList);

          // 在设置完菜单后立即查找并设置当前路径对应的菜单项
          if (location.pathname) {
            const result = findMenuItemByPath(
              newNavMenuList,
              location.pathname
            );
            if (result) {
              setInitPathItem(result.item);
              setParentMenuIds(result.parentIds);
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch menu:', error);
      } finally {
        setLoading(false);
      }
    };

    // 递归查找菜单项
    const findMenuItemByPath = (
      menuList: any[],
      path: string,
      parentIds: string[] = []
    ): any => {
      // 处理 /manage/page{id} 格式的路径
      // const isPagePath = path.match(/^\/manage\/page\d+$/);
      const isPagePath = path.match(/^\/platform\/manage\/page/);
      for (const item of menuList) {
        // 直接匹配当前项
        if (
          (isPagePath && item.path === path) ||
          (!isPagePath && item.path === path)
        ) {
          return {
            item,
            parentIds
          };
        }

        // 检查子菜单
        if (item.children && item.children.length > 0) {
          const found = findMenuItemByPath(item.children, path, [
            ...parentIds,
            item.id.toString()
          ]);
          if (found) {
            return found;
          }
        }
      }

      return null;
    };

    const [initPathItem, setInitPathItem] = useState<any>({});
    const [parentMenuIds, setParentMenuIds] = useState<string[]>([]);

    useEffect(() => {
      handleGetApplicationPageAndClassList();
    }, []); // 只在组件挂载时执行一次

    // 路由变化时更新选中项
    useEffect(() => {
      if (navMenuList.length > 0 && location.pathname) {
        // const isPagePath = location.pathname.match(/^\/manage\/page\d+$/);
        const isPagePath = location.pathname.match(/^\/platform\/manage\/page/);
        if (isPagePath) {
          const result = findMenuItemByPath(navMenuList, location.pathname);
          if (result) {
            setInitPathItem(result.item);
            setParentMenuIds(result.parentIds);
          }
        } else {
          // 对于系统菜单，也需要查找并设置选中状态
          const result = findMenuItemByPath(navMenuList, location.pathname);
          if (result) {
            setInitPathItem(result.item);
            setParentMenuIds(result.parentIds);
          } else {
            // 如果找不到匹配项，才重置
            setInitPathItem({});
            setParentMenuIds([]);
          }
        }
      }
    }, [location.pathname, navMenuList]);

    const refreshMenu = useCallback(() => {
      // 使用已定义的获取菜单数据的方法
      handleGetApplicationPageAndClassList();
    }, [handleGetApplicationPageAndClassList]);

    // 根据路由参数渲染对应的组件
    const renderComponent = () => {
      if (loading) {
        return <div>Loading...</div>; // 或使用一个加载指示器组件
      }

      const path = location.pathname;
      console.log('path', path);
      // const pageMatch = path.match(/^\/manage\/page(\d+)$/);
      const pageMatch = path.match(/^\/platform\/manage\/page/);

      switch (true) {
        case path === '/platform/manage/company':
          return <Company history={history} dataInfo="hello" />;
        case path === '/platform/manage/menuManager':
          return <MenuManager history={history} onMenuRefresh={refreshMenu} />;
        case path === '/platform/manage/dataSource':
          return <ManageDataSource history={history} />;
        case path === '/platform/manage/dataSet':
          return <ManageDataSet history={history} />;
        case path === '/platform/manage/dataSet/create':
          return <CreateDataSet history={history} location={location} />;
        case path === '/platform/manage/datasetImportPage':
          return (
            <CreateDatasetImportPage
              history={history}
              location={location}
              onMenuRefresh={refreshMenu}
            />
          );
        case path === '/platform/manage/controlDictionary':
          return (
            <ControlDictionary
              history={history}
              location={location}
              match={match}
              store={store}
            />
          );
        case !!pageMatch:
          // 判断是否有url或dataSetId
          if (initPathItem && Object.keys(initPathItem).length > 0) {
            if (initPathItem.url) {
              // 如果有url，显示PagelinkContent
              return (
                // <PagelinkContent
                //   pageData={initPathItem}
                //   history={history}
                //   match={match}
                //   updatePage={() => handleGetApplicationPageAndClassList()}
                // />
                <AMISRenderer
                  schema={createIframeObj(initPathItem.url)}
                  embedMode={true}
                  className="full-screen-amis"
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    zIndex: 100
                  }}
                />
              );
            } else if (initPathItem.dataSetId) {
              // 如果有dataSetId，显示DatasetImportPage
              return (
                <DatasetImportPage
                  history={history}
                  location={location}
                  match={match}
                  store={store}
                  initPathItem={initPathItem}
                  onMenuRefresh={refreshMenu}
                />
              );
            }
          }
        default:
          return null;
      }
    };

    return (
      <Layout
        aside={
          <RenderAside
            store={store}
            location={location}
            history={history}
            match={match}
            navMenuList={navMenuList}
            initPathItem={initPathItem}
            parentMenuIds={parentMenuIds}
          />
        }
        asideClassName={'asidePagesClass'}
        header={
          <CommonHeader
            store={store}
            history={history}
            type="appNoApplication"
            className="manageHeader"
          />
        }
        headerClassName={'asidePagesClass'}
        headerFixed={true}
        folded={store.asideFolded}
        offScreen={store.offScreen}
      >
        <Switch>
          <Route render={() => renderComponent()} />
        </Switch>
      </Layout>
    );
  })
);
