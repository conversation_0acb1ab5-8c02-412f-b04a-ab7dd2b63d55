import type {Identifier, XYCoord} from 'dnd-core';
import type {FC, ReactNode} from 'react';
import React, {useRef, memo, useMemo} from 'react';
import {useDrag, useDrop} from 'react-dnd';
import type {DragSourceMonitor} from 'react-dnd';
import './index.scss';

export interface CardProps {
  hoverItem: any;
  index: number;
  children?: ReactNode;
  parentIndex?: number;
}

interface DragItem {
  dragIndex: number;
  parentIndex: any;
}

export const MoveClassOrPage: FC<CardProps> = memo(function MoveClassOrPage({
  hoverItem,
  index,
  children,
  parentIndex,
}) {
  const ref = useRef<HTMLDivElement>(null);
  const style = {
    width: '100%'
  };

  const [{handlerId, isOver, isOverCurrent}, drop] = useDrop<
    DragItem,
    void,
    {handlerId: Identifier | null; isOver: boolean; isOverCurrent: boolean}
  >({
    accept: 'item',
    collect: monitor => ({
      handlerId: monitor.getHandlerId(),
      isOver: monitor.isOver(),
      isOverCurrent: monitor.isOver({shallow: true})
    }),
    hover(item: any, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2

      // Determine mouse position
      const clientOffset = monitor.getClientOffset()

      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }

      // Time to actually perform the action
      // moveCard(dragIndex, hoverIndex)
      console.log('moveCard',monitor.isOver({ shallow: true }),'isOver',isOver, 'dragIndex',dragIndex, 'hoverIndex',hoverIndex);
      
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    },
    drop(item: any, monitor) {
      const hoverIndex = index;
      let result: any = monitor.getDropResult();
      console.log(result,'item',item,'hoverIndex',hoverIndex);
    }
  });

  const [{isDragging}, drag] = useDrag(
    () => ({
      type: 'item',
      item: () => {
        return {dragIndex: index, parentIndex, ...hoverItem};
      },
      canDrag: true,
      collect: (monitor: DragSourceMonitor) => ({
        isDragging: monitor.isDragging()
      })
    }),
    []
  );

  const containerStyle = useMemo(
    () => ({
      ...style,
      opacity: isDragging ? 0.4 : 1
    }),
    [isDragging]
  );

  drag(drop(ref));


  return (
    <div ref={ref} data-handler-id={handlerId}>{children}</div>
  );
});
