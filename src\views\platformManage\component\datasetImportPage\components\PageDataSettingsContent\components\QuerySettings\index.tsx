import React from 'react';
import AMISRenderer from '@/component/AMISRenderer';
import './index.scss';

export interface QuerySettingsProps {
  dataSetInfo: any;
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;
  };
}

const QuerySettings: React.FC<QuerySettingsProps> = ({ dataSetInfo, initPathItem }) => {
  return (
    <div className="query-settings">
      <h2>查询设置</h2>
      <p>配置数据查询条件和筛选器</p>
      
      <AMISRenderer 
        schema={{
          type: 'form',
          initApi: `/admin-api/system/data-set/get-query-config/${dataSetInfo?.id}`,
          api: `/admin-api/system/data-set/update-query-config/${dataSetInfo?.id}`,
          body: [
            {
              type: 'switch',
              name: 'enableQuery',
              label: '启用查询',
              onText: '是',
              offText: '否'
            },
            {
              type: 'transfer',
              name: 'queryFields',
              label: '可查询字段',
              source: `/admin-api/system/data-set/fields/${dataSetInfo?.id}`,
              searchable: true
            }
          ]
        }}
      />
    </div>
  );
};

export default QuerySettings; 