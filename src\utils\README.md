# 数据转换工具

## 概述

这个模块提供了将 `GetControlDictPage` API 返回的数据转换为三级树形结构的工具函数。

## 主要功能

### `transformControlDictToTree(response)`

将 GetControlDictPage 获取到的数据转换为三级树形结构。

**输入数据格式：**
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "applicationId": null,
        "type": 1,
        "classificationId": 4,
        "defaultClassificationId": 4,
        "classificationName": "展示",
        "id": 240,
        "fieldType": "icon-show",
        "controlType": "图标展示",
        "amisControlType": "icon-show",
        "classification": null,
        "json": "{\"type\":\"icon-show\"}",
        "status": 1,
        "sort": null,
        "createTime": 1753260028000
      },
      {
        "applicationId": null,
        "type": 1,
        "classificationId": 4,
        "defaultClassificationId": 4,
        "classificationName": "展示",
        "id": 239,
        "fieldType": "plain",
        "controlType": "纯文本",
        "amisControlType": "plain",
        "classification": null,
        "json": "{\"type\":\"plain\"}",
        "status": 1,
        "sort": null,
        "createTime": 1753260028000
      }
    ]
  }
}
```

**输出数据格式：**
```json
[
  {
    "label": "系统应用",
    "children": [
      {
        "label": "展示",
        "children": [
          {
            "label": "图标展示",
            "value": "240"
          },
          {
            "label": "纯文本",
            "value": "239"
          }
        ]
      }
    ]
  }
]
```

### `getComponentClassifications(applicationId)`

根据应用ID获取组件分类配置。

**参数：**
- `applicationId`: 应用ID，如果为 null 则只返回基础分类

**返回值：**
```typescript
Array<{ label: string; value: number }>
```

### `fetchAndTransformComponentClassifications(componentClassifications, getControlDictPageFn)`

批量获取并转换组件分类数据，返回三级树形结构：
- 第一级：组件类型（系统组件、组织组件、应用组件）
- 第二级：分类（classificationName）
- 第三级：具体组件（controlType）

**参数：**
- `componentClassifications`: 分类配置数组
- `getControlDictPageFn`: GetControlDictPage API 函数

**返回值：**
```typescript
Promise<TreeNode[]>
```

## 使用示例

```typescript
import { 
  transformControlDictToTree, 
  getComponentClassifications,
  fetchAndTransformComponentClassifications 
} from '@/utils/dataTransform';
import { GetControlDictPage } from '@/utils/api/api';

// 1. 直接转换单个响应
const response = await GetControlDictPage({ pageNo: 1, pageSize: 100, type: 1 });
const treeData = transformControlDictToTree(response);

// 2. 获取分类配置
const applicationId = '123';
const classifications = getComponentClassifications(applicationId);

// 3. 批量获取并转换
const componentOptions = await fetchAndTransformComponentClassifications(
  classifications,
  GetControlDictPage
);
```

## 在 AmisFieldTable 中的使用

该工具已集成到 `AmisFieldTable` 组件中，用于处理控件类型的三级联动选择：

```typescript
// 在 AmisFieldTable 组件中
const componentClassifications = getComponentClassifications(applicationId);
const results = await fetchAndTransformComponentClassifications(
  componentClassifications,
  GetControlDictPage
);
setComponentClassificationOptions(results);
```

## 测试

运行测试：
```bash
npm test src/utils/__tests__/dataTransform.test.ts
```

## 数据结构说明

### 三级结构层次：
1. **第一级**: 系统应用（固定）
2. **第二级**: 分类名称（如：展示、输入、选择等）
3. **第三级**: 具体控件（如：纯文本、图标展示、文本输入等）

### TreeNode 接口：
```typescript
interface TreeNode {
  label: string;      // 显示名称
  value?: string;     // 选择值（仅第三级有）
  children?: TreeNode[]; // 子节点
}
```