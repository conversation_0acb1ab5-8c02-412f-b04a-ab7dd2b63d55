import React, {useRef} from 'react';
import {Button,  Icon, toast} from 'amis';
import './index.scss';
import ProcessRender from '../ProcessRender';
interface ApprovalDialogProps {
  show: boolean;
  processData:any;
  formData:any;
  onClose: () => void;
  onConfirm: (data: {processUsers: Record<string, any[]>}) => void;
}

export const ApprovalDialog: React.FC<ApprovalDialogProps> = ({
  show,
  processData,
  formData,
  onClose,
  onConfirm
}) => {
  const processRenderRef = useRef<any>(null);
  const [value, setValue] = React.useState({});

  if (!show) return null;

  const handleConfirm = () => {
    // 调用子组件的 validate 方法
    processRenderRef.current?.validate((isValid: boolean) => {
      if (isValid) {
      onConfirm({ processUsers: value });
      } else{
        toast.error('请完善审批流程节点');
      }
    });
  };

  return (
    <>
      <div className="approval-dialog-overlay"></div>
      <div className="approval-dialog">
        <div className="approval-dialog-content">
          <div className="dialog-header">
            <div className="dialog-header-title">提交</div>
            <div className="close-btn" onClick={onClose}>
              <i className="fa fa-times"></i>
            </div>
          </div>
          <div className="dialog-sec-title">审批流程</div>
          <div className="dialog-body">
            <ProcessRender
              ref={processRenderRef}
              process={processData}
              formData={formData}
              pcMode={true}
              value={value}
              onChange={setValue}
            />
          </div>
          <div className="dialog-footer">
            <Button className="cancel-btn" onClick={onClose}>取消</Button>
            <Button className="confirm-btn" level="primary" onClick={handleConfirm}>确定</Button>
          </div>
        </div>
      </div>
    </>
  );
};