import React from 'react';
import {Renderer} from 'amis';
import {FormControlProps} from 'amis-core';
import {SpinnerExtraProps} from 'amis-ui';

// 参考 App.d.ts 中的 AppPage 接口
export interface AppPage {
  /**
   * 菜单文字
   */
  label?: string;
  /**
   * 菜单图标，比如： fa fa-file
   */
  icon?: string;
  /**
   * 路由规则。比如：/banner/:id。当地址以 / 打头，则不继承上层的路径，否则将集成父级页面的路径。
   */
  url?: string;
  /**
   * 当match url 时跳转到目标地址.没有配置 schema 和 shcemaApi 时有效.
   */
  redirect?: string;
  /**
   * 当match url 转成渲染目标地址的页面.没有配置 schema 和 shcemaApi 时有效.
   */
  rewrite?: string;
  /**
   * 不要出现多个，如果出现多个只有第一个有用。在路由找不到的时候作为默认页面。
   */
  isDefaultPage?: boolean;
  /**
   * 二选一，如果配置了 url 一定要配置。否则不知道如何渲染。
   */
  schema?: any;
  schemaApi?: any;
  /**
   * 单纯的地址。可以设置外部链接。
   */
  link?: string;
  /**
   * 支持多层级。
   */
  children?: Array<AppPage>;
  /**
   * 菜单上的类名
   */
  className?: string;
  /**
   * 是否在导航中可见，适合于那种需要携带参数才显示的页面。比如具体某个数据的编辑页面。
   */
  visible?: boolean;
}

export interface MyAppProps extends FormControlProps, SpinnerExtraProps {
  // 原有的属性
  label?: string;
  name: string;
  clearable?: boolean;
  placeholder?: string;
  value?: string;
  static?: boolean;
  width?: number;
  height?: number;
  accept?: string;
  crop?: boolean;
  uploadBtnText?: string;
  borderRadius?: number | string;
  limit: undefined;
  
  // 新增 App 相关属性
  type?: 'my-dialog';
  /**
   * 系统名称
   */
  brandName?: string;
  /**
   * logo 图片地址，可以是 svg。
   */
  logo?: string;
  /**
   * 顶部区域
   */
  /**
   * 边栏菜单前面的区域
   */
  /**
   * 边栏菜单后面的区域
   */
  /**
   * 页面集合。
   */
  pages?: Array<AppPage> | AppPage;
  /**
   * 底部区域。
   */
  /**
   * css 类名。
   */
  className?: string;
  /**
   * 显示面包屑路径。
   */
  showBreadcrumb?: boolean;
  /**
   * 显示面包屑完整路径。
   */
  showFullBreadcrumbPath?: boolean;
  /**
   * 显示面包屑首页路径。
   */
  showBreadcrumbHomePath?: boolean;
}

@Renderer({
  type: 'my-dialog',
  name: 'my-dialog'
})
export class MyAppRenderer extends React.PureComponent<MyAppProps> {
  static defaultProps = {
    className: '',
    showBreadcrumb: true,
    showFullBreadcrumbPath: false,
    showBreadcrumbHomePath: true
  };

  constructor(props: MyAppProps) {
    super(props);
  }

  render() {
    const {
      static: isStatic,
      name,
      label,
      placeholder,
      width,
      height,
      accept,
      crop,
      uploadBtnText,
      borderRadius,
      render,
      frameImage,
      // App 相关属性
      brandName,
      logo,
      header,
      asideBefore,
      asideAfter,
      pages,
      footer,
      showBreadcrumb,
      showFullBreadcrumbPath,
      showBreadcrumbHomePath
    } = this.props as any;

    // 如果是头像上传模式
    if (!pages && !header && !footer) {
      // 构建基本配置
      const config: any = {
        type: 'input-image',
        label,
        name,
        frameImage: frameImage || false,
        accept: accept || 'image/jpeg,image/jpg,image/png',
        crop: crop !== false,
        placeholder: placeholder || '请选择头像',
        imageClassName: 'r w-full avatar',
        imageWidth: width || 100,
        imageHeight: height || 100,
        thumbMode: 'cover',
        receiver: {
          url: '/app-api/infra/file/upload-zerocode',
          method: 'post'
        },
        style: {
          '--avatar-width': `${width || 100}px`,
          '--avatar-height': `${height || 100}px`,
          '--avatar-border-radius':
            typeof borderRadius === 'number' ||
            (!isNaN(Number(borderRadius)) && !String(borderRadius).includes('%'))
              ? `${borderRadius}px`
              : borderRadius || '50%'
        },
        autoUpload: true,
        imageMode: 'cover',
        className: 'avatar-uploader'
      };

      if (isStatic) {
        if (render) {
          return render('input-image', config);
        }
      } else {
        const allConfig = {
          ...this.props,
          ...config
        };

        if (render) {
          return render('my-dialog', allConfig);
        }
        return <></>;
      }
    } 
    // 如果是 App 模式
    else {
      const appConfig: any = {
        type: 'app',
        brandName,
        logo,
        header,
        asideBefore,
        asideAfter,
        pages,
        footer,
        showBreadcrumb,
        showFullBreadcrumbPath,
        showBreadcrumbHomePath
      };

      if (render) {
        return render('app', appConfig);
      }
      return <></>;
    }
  }
}
