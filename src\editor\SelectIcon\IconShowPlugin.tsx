import {
  EditorManager,
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  BaseEventContext,
  getEventControlConfig,
  EditorNodeType,
  RAW_TYPE_MAP,
  getActionCommonProps
} from 'amis-editor';
export class IconShowPlugin extends BasePlugin {
  // 这里要跟对应的渲染器名字对应上
  // 注册渲染器的时候会要求指定渲染器名字
  rendererName = 'icon-show';

  // 暂时只支持这个，配置后会开启代码编辑器
  $schema = '/schemas/UnkownSchema.json';

  // 用来配置名称和描述
  name = '图标展示';
  description = 'svg图标展示';

  // tag，决定会在哪个 tab 下面显示的
  tags = ['展示'];

  // 图标
  icon = 'fa fa-picture-o';
  panelIcon = 'fa fa-picture-o';
  pluginIcon = 'icon-select-plugin';
  isBaseComponent = true;

  // 拖入组件里面时的初始数据tpl.replace(/'/g, '')
  scaffold = {
    type: 'icon-show',
    name: "icon-svg",
    tpl: ""
  };

  // 用来生成预览图的
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  notRenderFormZone = true;

  // 右侧面板相关
  panelTitle = '图标展示';

  panelBodyCreator = (context: BaseEventContext) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: [
          getSchemaTpl('collapseGroup', [
            {
              title: '常规',
              body: [
                getSchemaTpl('formItemName', {
                  required: true
                }),
                getSchemaTpl('icon-select', {
                  type: 'icon-select',
                  name: 'tpl',
                  placeholder: '请选择图标',
                  label: '默认',
                  returnSvg: true,
                })
              ]
            },

            getSchemaTpl('status', {
              isFormItem: true
            })
          ])
        ]
      },
      // {
      //   title: '外观',
      //   body: getSchemaTpl('collapseGroup', [
      //     {
      //       title: '基本样式',
      //       body: [
      //         getSchemaTpl('theme:select', {
      //           label: '尺寸',
      //           name: 'themeCss.className.iconSize'
      //         }),
      //         getSchemaTpl('theme:colorPicker', {
      //           label: '颜色',
      //           name: `themeCss.className.font.color`,
      //           labelMode: 'input'
      //         }),
      //         getSchemaTpl('theme:paddingAndMargin', {
      //           label: '边距'
      //         })
      //       ]
      //     }
      //   ],
      //   {...context?.schema, configTitle: 'style'})
      // },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(IconShowPlugin);
