import React, {FC} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {RouteComponentProps} from 'react-router';
import './index.scss';
import {toast} from 'amis-ui';
import CreateDataSet from '@/views/applySettings/component/createDataSet/index';
import {getDataSet} from '@/utils/api/api';
export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<any>) {
    /* data 数据 */
    const [editId, setEditId] = React.useState<string>('');
    const [dataSetdetail, setDataSetDetail] = React.useState<any>({});
    const [pageType, setPageType] = React.useState<string>('create');
    // 编辑情况获取详情信息
    const handleGetDataSet = () => {
      let data = {
        id: editId
      };
      getDataSet(data).then((res: any) => {
        if (res.code === 0) {
          console.log('handleGetDataSet res', res);
          setDataSetDetail(res.data);
        } else {
          toast.error(res.msg);
        }
      });
    };
    /* data 数据 end */

    /* methods 方法 */
    /* methods 方法 end */

    /* created 初始化 */
    React.useEffect(() => {
      console.log('location', location);
      if (location?.search) {
        // 从 URL 中获取 id 参数
        const searchParams = new URLSearchParams(location.search);
        const id = searchParams.get('id');
        if (id) {
          setEditId(id);
        }
        const type = searchParams.get('type');
        console.log('type', type);
        if (type) {
          setPageType(type);
        }
      }
    }, [location]);
    React.useEffect(() => {
      if (editId) {
        handleGetDataSet();
      }
    }, [editId]);
    /* created 初始化 end */

    return (
      <>
        {pageType == 'create' && (
          <CreateDataSet history={history} appId={match.params.appId} />
        )}
        {pageType == 'edit' && (
          <CreateDataSet
            history={history}
            appId={match.params.appId}
            dataSetdetail={dataSetdetail}
          />
        )}
        {pageType == 'views' && (
          <CreateDataSet
            history={history}
            appId={match.params.appId}
            dataSetdetail={dataSetdetail}
            disabledType={true}
          />
        )}
      </>
    );
  })
);
