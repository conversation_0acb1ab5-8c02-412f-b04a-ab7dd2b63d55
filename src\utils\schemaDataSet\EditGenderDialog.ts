export const EditGenderDialog = (show: boolean, currentGender?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>选择性别</b>'
    },
    showCloseButton: false,
    data: {
      sex: currentGender
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      body: [
        {
          required: true,
          type: 'select',
          name: 'sex',
          placeholder: '请选择',
          options: [
            {
              label: '男',
              value: 1
            },
            {
              label: '女',
              value: 2
            }
          ],
          inputClassName: 'name-input',
          labelClassName: 'name-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 