@import '../common.scss';

.dataset-settings {
  &-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
  }
  
  &-left {
    flex: 1;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
    }
    
    .text-muted {
      color: #8c8c8c;
      margin: 0 0 16px 0;
    }
    
    .current-dataset {
      display: flex;
      align-items: center;
      margin-top: 16px;
      
      &-label {
        font-weight: 500;
        margin-right: 8px;
      }
      
      &-value {
        color: var(--primary, #1890ff);
        font-weight: 500;
      }
    }
  }
  
  &-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  
  &-actions {
    display: flex;
    gap: 8px;
  }
}

// 暗黑模式适配
[data-theme="dark"] {
  .dataset-settings {
    .current-dataset {
      &-value {
        color: var(--primary-dark, #177ddc);
      }
    }
  }
} 