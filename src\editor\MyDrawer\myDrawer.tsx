import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class DrawerPlugin extends BasePlugin {
  static id = 'DrawerPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'drawer';
  $schema = '/schemas/DrawerSchema.json';

  // 组件基本信息
  name = 'Drawer 抽屉';
  panelTitle = 'Drawer 抽屉';
  icon = 'fa fa-window-maximize';
  panelIcon = 'fa fa-window-maximize';
  pluginIcon = 'fa fa-window-maximize';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = 'Drawer 抽出式弹框';
  docLink = '/amis/zh-CN/components/drawer';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'drawer',
    title: '抽屉标题',
    body: {
      type: 'tpl',
      tpl: '抽屉内容'
    },
    position: 'right',
    showCloseButton: true,
    closeOnEsc: false,
    closeOnOutside: false,
    size: 'md'
  };

  // 预览界面
  previewSchema = {
    type: 'drawer',
    title: '抽屉标题',
    body: '抽屉内容',
    position: 'right',
    showCloseButton: true,
    className: 'text-left'
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                {
                  type: 'input-text',
                  name: 'title',
                  label: '标题',
                  description: '抽屉标题'
                },
                {
                  type: 'select',
                  name: 'position',
                  label: '弹出位置',
                  value: 'right',
                  options: [
                    {label: '左', value: 'left'},
                    {label: '右', value: 'right'},
                    {label: '上', value: 'top'},
                    {label: '下', value: 'bottom'}
                  ],
                  description: '从什么位置弹出'
                },
                {
                  type: 'select',
                  name: 'size',
                  label: '尺寸',
                  value: 'md',
                  options: [
                    {label: '极小', value: 'xs'},
                    {label: '小', value: 'sm'},
                    {label: '中', value: 'md'},
                    {label: '大', value: 'lg'},
                    {label: '特大', value: 'xl'},
                    {label: '全屏', value: 'full'}
                  ],
                  description: 'Drawer 大小'
                },
                {
                  type: 'input-text',
                  name: 'width',
                  label: '宽度',
                  description: '自定义宽度，如 500px（当position为left/right时生效）',
                  visibleOn: "this.position === 'left' || this.position === 'right'"
                },
                {
                  type: 'input-text',
                  name: 'height',
                  label: '高度',
                  description: '自定义高度，如 400px（当position为top/bottom时生效）',
                  visibleOn: "this.position === 'top' || this.position === 'bottom'"
                },
                {
                  type: 'input-text',
                  name: 'className',
                  label: 'CSS类名',
                  description: '自定义CSS类名'
                },
                {
                  type: 'input-text',
                  name: 'headerClassName',
                  label: '头部类名',
                  description: '配置头部容器类名'
                },
                {
                  type: 'input-text',
                  name: 'bodyClassName',
                  label: '内容区域类名',
                  description: '配置Body容器类名'
                },
                {
                  type: 'input-text',
                  name: 'footerClassName',
                  label: '底部类名',
                  description: '配置底部容器类名'
                },
                {
                  type: 'switch',
                  name: 'showCloseButton',
                  label: '显示关闭按钮',
                  value: true,
                  description: '是否显示关闭按钮'
                },
                {
                  type: 'switch',
                  name: 'closeOnEsc',
                  label: 'ESC 关闭',
                  value: false,
                  description: '是否支持按 ESC 关闭'
                },
                {
                  type: 'switch',
                  name: 'closeOnOutside',
                  label: '点击外部关闭',
                  value: false,
                  description: '是否支持点击外部区域关闭'
                },
                {
                  type: 'switch',
                  name: 'overlay',
                  label: '显示蒙层',
                  value: true,
                  description: '是否显示背景蒙层'
                },
                {
                  type: 'switch',
                  name: 'resizable',
                  label: '可调整大小',
                  value: false,
                  description: '是否可以拖动弹窗大小'
                }
              ]
            },
            {
              title: '操作按钮',
              body: [
                {
                  type: 'switch',
                  name: 'confirm',
                  label: '确认按钮',
                  value: true,
                  description: '是否有确认按钮'
                },
                {
                  type: 'switch',
                  name: 'showErrorMsg',
                  label: '显示错误信息',
                  value: true,
                  description: '是否显示错误提示'
                }
              ]
            },
            {
              title: 'API配置',
              body: [
                getSchemaTpl('apiControl', {
                  name: 'api',
                  label: '数据接口',
                  description: '用于获取抽屉数据的接口'
                })
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '区域设置',
              body: [
                
              ]
            },
           
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(DrawerPlugin);
