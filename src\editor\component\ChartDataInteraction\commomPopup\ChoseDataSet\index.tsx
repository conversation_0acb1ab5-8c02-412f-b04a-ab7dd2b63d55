// 创建自定义页面页面弹窗
const ChoseDataSetPopup = (body: any) => {
  return {
    type: 'dialog',
    title: '选择数据集',
    body: body,
    actions: [],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    hideActions: false,
    size: 'md',
    editorSetting: {
      displayName: '选择数据集'
    },
    themeCss: {
      dialogClassName: {
        radius: {
          'top-left-border-radius': '8px',
          'top-right-border-radius': '8px',
          'bottom-left-border-radius': '8px',
          'bottom-right-border-radius': '8px'
        }
      }
    }
  };
};

export default ChoseDataSetPopup;
