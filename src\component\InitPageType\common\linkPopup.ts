// 创建团队弹窗
const linkPopup = () => {
  return {
    type: 'dialog',
    title: '添加地址',
    body: [
      {
        id: 'u:d717906b428d',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'input-text',
            label: '',
            name: 'name',
            row: 0,
            id: 'u:f373b3160f0c',
            placeholder: '请输入页面名称',
            inputClassName: 'name-input',
            labelClassName: 'name-label'
          },
          {
            type: 'input-url',
            label: '地址链接',
            name: 'link',
            id: 'u:2d61d62a99e9',
            required: true
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'u:d717906b428d'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: true
      }
    ],
    id: 'u:0e5cb446d447',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:d717906b428d'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'u:d717906b428d'
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '添加地址'
    }
  };
};

export {linkPopup};
