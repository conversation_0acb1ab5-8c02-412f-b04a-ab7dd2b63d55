import React, {useEffect, useState} from 'react';
import {Provider} from 'mobx-react';
import {toast, alert, confirm} from 'amis';
import axios from 'axios';
import {MainStore} from './store/index';
import RootRoute from './route/index';
import copy from 'copy-to-clipboard';
import {setDefaultTheme} from 'amis';
import {darkThemeVars, cxdThemeVars} from './component/ThemeSwitch/theme-vars';

export default function (): JSX.Element {
  const store = ((window as any).store = MainStore.create(
    {},
    {
      fetcher: ({url, method, data, config, headers}: any) => {
        const nodeEnv = process.env.NODE_ENV;

        // https://m.thinkddm.fjpipixia.com /apiTest /javaApi
        const baseURL = nodeEnv == 'development' ? '/apiTest' : '';
        url = baseURL + url;
        config = config || {};
        config.headers = config.headers || headers || {};
        config.withCredentials = true;

        let store: any = localStorage.getItem('store');
        store = JSON.parse(store);
        config.headers['http-user-id'] = store.userInfo
          ? store.userInfo?.id
          : '';
        let access_token = store.access_token;
        config.headers['authorization'] = access_token
          ? 'Bearer ' + access_token
          : '';
        config.headers['tenant-id'] = store.tenant_id ? store.tenant_id : '1';

        if (method !== 'post' && method !== 'put' && method !== 'patch') {
          if (data) {
            config.params = data;
          }
          return (axios as any)[method](url, config);
        } else if (data && data instanceof FormData) {
          // config.headers = config.headers || {};
          // config.headers['Content-Type'] = 'multipart/form-data';
        } else if (
          data &&
          typeof data !== 'string' &&
          !(data instanceof Blob) &&
          !(data instanceof ArrayBuffer)
        ) {
          data = JSON.stringify(data);
          config.headers['Content-Type'] = 'application/json';
        }

        return (axios as any)[method](url, data, config);
      },
      isCancel: (e: any) => axios.isCancel(e),
      notify: (type: 'success' | 'error' | 'info', msg: string) => {
        toast[type]
          ? toast[type](msg, type === 'error' ? '系统错误' : '系统消息')
          : console.warn('[Notify]', type, msg);
        console.log('[notify]', type, msg);
      },
      alert,
      confirm,
      copy: (contents: string, options: any = {}) => {
        const ret = copy(contents, options);
        ret &&
          (!options || options.shutup !== true) &&
          toast.info('内容已拷贝到剪切板');
        return ret;
      }
    }
  ));


  // 从localStorage获取主题设置，如果没有则默认使用cxd
  const [theme, setTheme] = useState<string>(
    localStorage.getItem('amis-theme') || 'cxd'
  );

  // 更新所有带有特定前缀的元素的类名
  const updateElementsWithPrefix = (oldTheme: string, newTheme: string) => {
    // 获取所有元素
    const allElements = document.getElementsByTagName('*');

    // 遍历所有元素
    for (let i = 0; i < allElements.length; i++) {
      const element = allElements[i];
      const classList = element.classList;

      // 遍历元素的所有类名
      for (let j = 0; j < classList.length; j++) {
        const className = classList[j];
        // 如果类名以旧主题为前缀
        if (className.startsWith(`${oldTheme}-`)) {
          // 移除旧主题类名
          classList.remove(className);
          // 添加新主题类名
          classList.add(`${newTheme}-${className.slice(oldTheme.length + 1)}`);
          // 因为classList是实时的，所以我们需要调整索引
          j--;
        }
      }
    }
  };

  // 更新 CSS 变量
  const updateCSSVariables = (theme: string) => {
    const root = document.documentElement;
    const vars = theme === 'dark' ? darkThemeVars : cxdThemeVars;

    Object.entries(vars).forEach(([key, value]) => {
      root.style.setProperty(key, value as string);
    });
  };

  // 当主题发生变化时，更新localStorage和document.body的class
  useEffect(() => {
    const oldTheme = theme === 'cxd' ? 'dark' : 'cxd';

    localStorage.setItem('amis-theme', theme);

    // 移除所有主题相关的class
    document.body.classList.remove('cxd', 'dark');
    document.body.classList.add(theme);

    // 更新 amis 默认主题
    setDefaultTheme(theme);

    // 更新 store 中的主题
    if (store) {
      store.updateTheme(theme);
    }

    // 更新所有主题相关的类名
    updateElementsWithPrefix(oldTheme, theme);

    // 更新 CSS 变量
    updateCSSVariables(theme);

    // 触发自定义事件，通知其他组件主题已变更
    const event = new CustomEvent('themeChange', {detail: {theme}});
    document.dispatchEvent(event);

    // 添加一个小延迟后刷新所有 amis 组件的样式
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
      // 再次检查并更新类名，以防有延迟加载的组件
      updateElementsWithPrefix(oldTheme, theme);
    }, 100);
  }, [theme, store]);

  return (
    <Provider store={store}>
      <RootRoute store={store} />
    </Provider>
  );
}
