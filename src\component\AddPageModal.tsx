import {schema2component} from './AMISRenderer';

export default schema2component(
  {
    type: 'dialog',
    title: '新增页面',
    body: {
      type: 'form',
      controls: [
        {
          type: 'text',
          label: '名称',
          name: 'label',
          validations: {
            maxLength: 20
          },
          required: true
        },
        {
          type: 'select',
          label: '页面类型',
          name: 'pageType',
          options: [
            {
              label: '默认',
              value: 'default'
            },
            {
              label: '普通表单',
              value: 'generalForm'
            },
            {
              label: '仪表盘设计器',
              value: 'dashboard'
            }
          ],
          multiple: false,
          placeholder: '请选择您所新建的页面类型',
          selectFirst: false,
          required: true,
          value: 'generalForm'
        },
        {
          type: 'input-url',
          label: '链接',
          name: 'link',
          id: 'u:178f305d6a27',
          validations: {},
          validationErrors: {},
          visibleOn: "${pageType == 'dashboard'}",
          hidden: false,
          required: true
        },
        // {
        //   type: 'text',
        //   label: '路径',
        //   name: 'path',
        //   validations: {
        //     isUrlPath: true
        //   },
        //   required: true,
        //   validate(values: any, value: string) {
        //     const exists = !!values.pages.filter(
        //       (item: any) => item.path === value
        //     ).length;
        //     return exists ? '当前路径已被占用，请换一个' : '';
        //   }
        // },

        {
          type: 'icon-picker',
          label: '图标',
          name: 'icon'
        }
      ]
    }
  },
  ({onConfirm, pages, ...rest}: any) => {
    return {
      ...rest,
      data: {
        pages
      },
      onConfirm: (values: Array<any>) => onConfirm && onConfirm(values[0])
    };
  }
);
