import React, {FC, Fragment} from 'react';
import './index.scss';
import {toast, Input as TextInput, Checkbox, Radios, Transfer, Combo, Button, Form} from 'amis';

// 图片引入
import btn_icon from './image/page_btn_icon.png';
import mssage_icon from './image/page_mssage_icon.png';
import nav_icon from './image/page_nav_icon.png';
import permissions_icon from './image/page_permissions_icon.png';
import print_icon from './image/page_print_icon.png';
import qrCode_icon from './image/page_qrCode_icon.png';
import set_icon from './image/page_set_icon.png';

import app_default from '@/image/common_icons/app_default_icon.png';

import AMISRenderer from '@/component/AMISRenderer';
// 引入修改应用名称弹窗
import {modifyPageNamePopup} from './common/modifyPageNamePopup';

import {modifyPageDescPopup} from './common/modifyPageDescPopup';

import {modifyColumnSizePopup} from './common/modifyColumnSizePopup';

import {modifyDataSetPopup} from './common/modifyDataSetPopup';

import {editRute,amisCodeUpdate,amisCodeGet, updateDatasetIdBindingRute, getShowField, ruteCodeSave, updateFormData, updateApplicationPageAndClass, getFormDataPage, updateBatchOperate} from '@/utils/api/api';

import {createDatasetImportObj} from '@/utils/schemaPageTemplate/createPageObjs';
import {singleOperationObj} from './common/singleOperation';
import {mutiOperationObj} from './common/MutiOperation';
import {confirmModal} from './common/confirmModal';
import {columnSettingObj} from './common/columnSetting';
import {apiSettingObj} from './common/apiSettingObj';
import renderApiConfigTabs from './common/apiConfig';
import {
  findCreateBody
} from '@/utils/schemaDataSet/commonEditor';

const PageSettings: FC<any> = (props: any) => {

  /* data 数据 */
  const menuList = [
    {
      title: '数据集设置',
      icon: set_icon,
      path: 'datacollection'
    },
    {
      title: '查询设置',
      icon: set_icon,
      path: 'querySetting'
    },
    {
      title: '顶部工具栏',
      icon: set_icon,
      path: 'topToolbarSetting'
    },
    {
      title: '底部工具栏',
      icon: set_icon,
      path: 'bottomToolbarSetting'
    },
    {
      title: '操作栏设置',
      icon: set_icon,
      path: 'actionBarSetting'
    },
    // {
    //   title: '批量操作设置',
    //   icon: set_icon,
    //   path: 'batchOperationSetting'
    // },
    {
      title: '单条操作',
      icon: set_icon,
      path: 'singleOperationSetting'
    },
    {
      title: '批量操作',
      icon: set_icon,
      path: 'mutiOperationSetting'
    },
    {
      title: '列设置',
      icon: set_icon,
      path: 'columnSetting'
    },
  ];

  // 选中的菜单item
  const [activeItem, setActiveItem] = React.useState<any>(menuList[0]);


  const [options, setOptions] = React.useState<any>([]);

  const [transferValue, setTransferValue] = React.useState<any>([]);

  // 打开修改每列显示几个字段弹窗
  const [openColumnSizePopup, setOpenColumnSizePopup] = React.useState(false);

  // 是否启用查询条件
  const [isEnabled, setIsEnabled] = React.useState<boolean>(false);


  // 每列显示几个字段
  const [columnSize, setColumnSize] = React.useState<number>(2);

  const [headerToolbarArray, setHeaderToolbarArray] = React.useState<any>([]);
  const [bottomToolbarArray, setBottomToolbarArray] = React.useState<any>([]);

  const [optEditEnabled, setOptEditEnabled] = React.useState<boolean>(false);
  const [optViewEnabled, setOptViewEnabled] = React.useState<boolean>(false);
  const [optDeleteEnabled, setOptDeleteEnabled] = React.useState<boolean>(false);
  const [singleOptEnabled, setSingleOptEnabled] = React.useState<boolean>(false);
  
  const [mutiOptEnabled, setMutiOptEnabled] = React.useState<boolean>(false);
  

  const [dataSetList, setDataSetList] = React.useState<any>([]);

  const [openModifyDataSetPopup, setOpenModifyDataSetPopup] = React.useState<boolean>(false);
  const showFieldData = React.useRef<any>([]);

  // 添加确认弹框状态
  const [showConfirmModal, setShowConfirmModal] = React.useState(false);
  const [confirmModalSchema, setConfirmModalSchema] = React.useState<any>(null);

  /* data 数据 end */

  const headerToolbarSchema = {
    name: 'headerToolbar',
    type: 'combo',
    draggable: true,
    draggableTip: '',
    descrition: '非内建内容请在预览区选中后编辑',
    label: '顶部工具栏配置',
    value: headerToolbarArray,
    pipeIn: (value: any) => {
      return value.map((item: any) => {
        let type = item.type;
  
        if (
          typeof item === 'string' &&
          ~[
            'bulkActions',
            'bulk-actions',
            'pagination',
            'statistics',
            'switch-per-page',
            'filter-toggler',
            'load-more',
            'export-csv',
            'export-excel',
            'reload',
            'export-excel-template',
            'bulkActions',
            'columns-toggler'
          ].indexOf(item)
        ) {
          type = item === 'bulkActions' ? 'bulk-actions' : item;
          item = {type};
        } else if (typeof item === 'string') {
          type = 'tpl';
          item =
            typeof item === 'string'
              ? {type: 'tpl', tpl: item, wrapperComponent: ''}
              : item;
        }
        return {
          type,
          ...item
        };
      });
    },
    pipeOut: (value: any) => {
      if (Array.isArray(value)) {
        // 接口网络请求处理
        handleHeadChange(value)
        return value.map((item: any) => {
          return item;
        });
      }
  
      return [];
    },
    scaffold: {
      type: 'tpl',
      align: 'left',
      tpl: '内容'
    },
    multiple: true,
    items: [
      {
        type: 'select',
        name: 'type',
        columnClassName: 'w-ssm',
        overlay: {
          align: 'left',
          width: 150
        },
        options: [
          {
            value: 'pagination',
            label: '分页'
          },
  
          {
            value: 'statistics',
            label: '统计数据'
          },
  
          {
            value: 'switch-per-page',
            label: '切换页码'
          },
  
          {
            value: 'load-more',
            label: '加载更多'
          },
  
          {
            value: 'export-csv',
            label: '导出 CSV'
          },
  
          {
            value: 'export-excel',
            label: '导出 Excel'
          },
          {
            value: 'tpl',
            label: '文本'
          },
  
          {
            value: 'reload',
            label: '刷新'
          },
          {
            value: 'export-excel-template',
            label: '导出Excel模板'
          },
          {
            value: 'bulkActions',
            label: '批量操作'
          },
          {
            value: 'columns-toggler',
            label: '列切换器'
          }
        ]
      },
  
      {
        name: 'align',
        placeholder: '对齐方式',
        type: 'select',
        size: 'xs',
        options: [
          {
            label: '左对齐',
            value: 'left'
          },
  
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      },
      {
        name: 'tpl',
        type: 'input-text',
        visibleOn: "${type === 'tpl'}",
        placeholder: "文本内容"
      }
    ]
  }

  const bottomToolbarSchema = {
    name: 'footerToolbar',
    type: 'combo',
    draggable: true,
    draggableTip: '',
    descrition: '非内建内容请在预览区选中后编辑',
    label: '底部工具栏配置',
    value: bottomToolbarArray,
    pipeIn: (value: any) => {
      return value.map((item: any) => {
        let type = item.type;
  
        if (
          typeof item === 'string' &&
          ~[
            'bulkActions',
            'bulk-actions',
            'pagination',
            'statistics',
            'switch-per-page',
            'filter-toggler',
            'load-more',
            'export-csv',
            'export-excel',
            'reload',
            'export-excel-template',
            'bulkActions',
            'columns-toggler'
          ].indexOf(item)
        ) {
          type = item === 'bulkActions' ? 'bulk-actions' : item;
          item = {type};
        } else if (typeof item === 'string') {
          type = 'tpl';
          item =
            typeof item === 'string'
              ? {type: 'tpl', tpl: item, wrapperComponent: ''}
              : item;
        }
        return {
          type,
          ...item
        };
      });
    },
    pipeOut: (value: any) => {
      if (Array.isArray(value)) {
        // 接口网络请求处理
        handleBottomChange(value)
        return value.map((item: any) => {
          return item;
        });
      }
  
      return [];
    },
    scaffold: {
      type: 'tpl',
      align: 'left',
      tpl: '内容'
    },
    multiple: true,
    items: [
      {
        type: 'select',
        name: 'type',
        columnClassName: 'w-ssm',
        overlay: {
          align: 'left',
          width: 150
        },
        options: [
          {
            value: 'pagination',
            label: '分页'
          },
  
          {
            value: 'statistics',
            label: '统计数据'
          },
  
          {
            value: 'switch-per-page',
            label: '切换页码'
          },
  
          {
            value: 'load-more',
            label: '加载更多'
          },
  
          {
            value: 'export-csv',
            label: '导出 CSV'
          },
  
          {
            value: 'export-excel',
            label: '导出 Excel'
          },
          {
            value: 'tpl',
            label: '文本'
          },
  
          {
            value: 'reload',
            label: '刷新'
          },
          {
            value: 'export-excel-template',
            label: '导出Excel模板'
          },
          {
            value: 'bulkActions',
            label: '批量操作'
          },
          {
            value: 'columns-toggler',
            label: '列切换器'
          }
        ]
      },
  
      {
        name: 'align',
        placeholder: '对齐方式',
        type: 'select',
        size: 'xs',
        options: [
          {
            label: '左对齐',
            value: 'left'
          },
  
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      },
      {
        name: 'tpl',
        type: 'input-text',
        visibleOn: "${type === 'tpl'}",
        placeholder: "文本内容"
      }
    ]
  }
  /* methods 方法 */
  // 修改是否启用查询
  const modifyIsEnabled = (val: any) => {
    let originSchema = props.pageData.data;
    if (val) {
      originSchema.body[0].autoGenerateFilter = {
        columnsNum: columnSize,
        showBtnToolbar: true,
        defaultCollapsed: false
      };
    } else {
      originSchema.body[0].autoGenerateFilter = null;
    }
    updateFromSchema(originSchema)
  };

  const modifyOperationEdit = (val: any) => {
    let columns = props.pageData.data.body[0].columns;
    const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
    const editBtn = operationBarBtns.find((item: any) => item.label == '编辑');
    editBtn.hidden = !val;

    let originSchema = props.pageData.data;
    originSchema.body[0].columns = columns;
    updateFromSchema(originSchema)
  }

  const modifyOperationView = (val: any) => {
    let columns = props.pageData.data.body[0].columns;
    const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
    const viewBtn = operationBarBtns.find((item: any) => item.label == '查看');
    viewBtn.hidden = !val;

    let originSchema = props.pageData.data;
    originSchema.body[0].columns = columns;
    updateFromSchema(originSchema)
  }

  const modifyOperationDelete = (val: any) => {
    let columns = props.pageData.data.body[0].columns;
    const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
    const deleteBtn = operationBarBtns.find((item: any) => item.label == '删除');
    deleteBtn.hidden = !val;

    let originSchema = props.pageData.data;
    originSchema.body[0].columns = columns;
    updateFromSchema(originSchema)
  }

  const modifyOperationSingle = (val: any) => {
    let originSchema = props.pageData.data;

    originSchema.body[0].singleOptEnabled = val;
    //
    originSchema.body[0].headerToolbar.forEach((item: any) => {
      if (item.cusSingleAction) {
        item.hidden = !val;
      }
    })

    updateFromSchema(originSchema)
  }

  const modifyOperationMuti = (val: any) => {
    let originSchema = props.pageData.data;

    originSchema.body[0].mutiOptEnabled = val;
    //todo: - fix
    // originSchema.body[0].headerToolbar.forEach((item: any) => {
    //   if (item.cusMutiAction) {
    //     item.hidden = !val;
    //   }
    // })

    updateFromSchema(originSchema)
  }

  // 修改每列显示几个字段
  const modifyColumnSize = (val: any) => {
    setColumnSize(val["0"].columnSize);
    setOpenColumnSizePopup(false);

    let originSchema = props.pageData.data;
    originSchema.body[0].columnSize = val["0"].columnSize;
    if (isEnabled) {
      originSchema.body[0].autoGenerateFilter = {
        columnsNum: val["0"].columnSize,
        showBtnToolbar: true,
        defaultCollapsed: false
      };
    }
    updateFromSchema(originSchema)
  };

  // 处理Transfer值变化
  const handleTransferChange = (values: any) => {
    setTransferValue(values);
    // 获取原始schema
    let originSchema = props.pageData.data;
    let columns = originSchema.body[0].columns;
    
    // 将values数组map为value strings
    let selectedValues = values.map((item: any) => item.value);
    
  // 创建新的columns数组，而不是直接修改原有对象
  const newColumns = columns.map((column: any) => {
    // 创建列的浅拷贝
    const newColumn = { ...column };
    
    if (selectedValues.includes(newColumn.name)) {
      var type = newColumn.type;
      type = 'input-text';

      newColumn.searchable = {
        type: type,
        name: newColumn.name,
        label: newColumn.label || newColumn.name,
        placeholder: `输入${newColumn.label || newColumn.name}`,
        mode: 'v',
      };
    } else {
      newColumn.searchable = null;
    }
    
    return newColumn;
  });

  // 更新schema中的columns
  originSchema.body[0].columns = newColumns;

  updateFromSchema(originSchema);
  };

    // 处理head值变化
  const handleHeadChange = (values: any) => {
    setHeaderToolbarArray(values);
    // 获取原始schema
    let originSchema = props.pageData.data;
    let headerToolbar = originSchema.body[0].headerToolbar;

    let cusSingleAction = headerToolbar.filter((item: any) => item.cusSingleAction === true);
    let newHeaderToolbar = [headerToolbar[0]];
    values.forEach((item: any) => {
      if (item.type === 'columns-toggler') {
        item.draggable = true;
      }
      newHeaderToolbar.push(item);
    });
    newHeaderToolbar.push(...cusSingleAction);
    originSchema.body[0].headerToolbar = newHeaderToolbar;
    updateFromSchema(originSchema)
  };

  const handleBottomChange = (values: any) => {
    setBottomToolbarArray(values);
    // 获取原始schema
    let originSchema = props.pageData.data;
    let footerToolbar = originSchema.body[0].footerToolbar;

    let newFooterToolbar: any = [];
    values.forEach((item: any) => {
      if (item.type === 'columns-toggler') {
        item.draggable = true;
      }
      newFooterToolbar.push(item);
    });
    originSchema.body[0].footerToolbar = newFooterToolbar;
    updateFromSchema(originSchema)
  };

  const modifyDataSet = (val: any) => {
    setOpenModifyDataSetPopup(false);
    let data: any = {
      id: props.pageData.applicationPageId,
      dataSetId: val["0"].dataSetId,
      applicantOrBackend: 3
    };

    updateApplicationPageAndClass(data).then((res: any) => {
      if (res.code === 0) {
        handleGetFormDataPage(props.pageData.applicationPageId)
        
      } else {
        toast.error(res.msg);
      }
    });
  }

  const handleGetFormDataPage = (pageId: any) => {
    console.log('handleGetFormDataPage')
    let data = {
      applicationPageId: pageId,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            let pageData = res.data.list[0]
            let field = JSON.parse(pageData.field)
            let title = props.pageData.data?.title || ''
            let schema = createDatasetImportObj(title, pageId, field, pageData.dataSetId, true)
            updateFromSchema(schema, pageData.id)
            // updateFromSchema(pageId, info, res.data.list[0])
          }else{
          }
        }else{
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  const saveSigleAction = (values: any) => {
    let originSchema = props.pageData.data;
    
    originSchema.body[0].itemActions = values.list.filter((item: any) => item.isEnabled === 1).map((item: any) => {
      let field = showFieldData.current.find((field: any) => field.name === item.fieldName);

      return {
        label: item.buttonName,
        type: 'button',
        hiddenOnHover: true,
        name: item.buttonName,
        actionType: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
        cusSingleAction: true,
        visibleOn: "${selectedItems.length === 1}", 
        id: item.id,
        drawer: {
          type: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
          title: item.buttonDepict,
          body: [
            {
              type: 'form',
              label: item.label,
              api: item.api ? JSON.parse(item.api) : {
                method: 'put',
                url: '/admin-api/system/form-field-value/updateOneFieldVlue'
              },
              body: [
                {
                  type: field.type || 'input-text',
                  name: 'value',
                  label: field.label
                },
                {
                  type: 'input-text',
                  name: 'id',
                  value: '${selectedItems[0].id}',
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'applicationPageId',
                  value: props.pageData.applicationPageId,
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'fieldName',
                  value: item.fieldName,
                  hidden: true
                }
              ]
            }
          ]
        },
        dialog: {
          type: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
          title: item.buttonName,
          body: [
            {
              type: 'form',
              label: item.buttonName,
              api: item.api ? JSON.parse(item.api) : {
                method: 'put',
                url: '/admin-api/system/form-field-value/updateOneFieldVlue'
              },
              body: [
                {
                  type: field.type || 'input-text',
                  name: 'value',
                  label: field.label
                },
                {
                  type: 'input-text',
                  name: 'id',
                  value: '${selectedItems[0].id}',
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'applicationPageId',
                  value: props.pageData.applicationPageId,
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'fieldName',
                  value: item.fieldName,
                  hidden: true
                }
              ]
            }
          ]
        }
      }
    })


    let headerToolbar = originSchema.body[0].headerToolbar.filter((item: any) => item.cusSingleAction != true)

    for (let i = 0; i < originSchema.body[0].itemActions.length; i++) {
      headerToolbar.push(originSchema.body[0].itemActions[i])
    }
    originSchema.body[0].headerToolbar = headerToolbar
    originSchema.body[0].selectable = true;
    originSchema.body[0].multiple = true;

    updateFromSchema(originSchema)
  }

  const saveMutiAction = (values: any) => {
    let originSchema = props.pageData.data;
    
    originSchema.body[0].bulkActions = values.list.filter((item: any) => item.isEnabled === 1).map((item: any) => {
      // 将字段名分割为数组
      let arr = item.fieldName ? item.fieldName.split(',') : [];
      
      // 获取所有字段的信息
      let fields = arr.map((fieldName: string) => 
        showFieldData.current.find((field: any) => field.name === fieldName)
      ).filter(Boolean); // 过滤掉未找到的字段

      return {
        label: item.buttonName,
        type: 'button',
        name: item.buttonName,
        actionType: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
        id: item.id,
        drawer: {
          type: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
          title: item.buttonDepict,
          body: [
            {
              type: 'form',
              label: item.label,
              api: item.api ? JSON.parse(item.api) : {
                method: 'put',
                url: `/admin-api/system/form-field-value/batch-update-multiline/${props.pageData.applicationPageId}` + '?ids=${ids}'
              },
              body: fields.map((field: any) => ({
                type: field.type || 'input-text',
                name: field.name,
                label: field.label
              }))
            }
          ]
        },
        dialog: {
          type: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
          title: item.buttonName,
          body: [
            {
              type: 'form',
              label: item.buttonName,
              api: item.api ? JSON.parse(item.api) : {
                method: 'put',
                url: `/admin-api/system/form-field-value/batch-update-multiline/${props.pageData.applicationPageId}` + '?ids=${ids}'
              },
              body: fields.map((field: any) => ({
                type: field.type || 'input-text',
                name: field.name,
                label: field.label
              }))
            }
          ]
        }
      }
    })

    originSchema.body[0].selectable = true;
    originSchema.body[0].multiple = true;

    updateFromSchema(originSchema)
  }

  const updateFromSchema = (schema: any, id: any = props.pageData.id) => {
    let data = {
      id: id,
      data: JSON.stringify(schema),
      field: props.pageData.field,
      applicationPageId: props.pageData.applicationPageId
    };
    updateFormData(data).then((res: any) => {
      if (res.code == 0) {
        // handleGetApplicationPageAndClass();
        props.update({
          ...props.pageData,
          data: schema
        });
        toast.success('保存成功');
      } else {
        toast.error(res.msg);
      }
    });
  }

  // 设置活动菜单项并更新URL
  const handleMenuItemClick = (item: any) => {
    setActiveItem(item);
    
    // 更新URL参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeMenu', item.path);
    
    // 使用history.replace更新URL而不创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  // 添加确认弹框处理函数
  const handleConfirmModal = (title: string, content: string, onConfirm: () => void) => {
    setConfirmModalSchema(confirmModal(title, content, () => {
      onConfirm();
      setShowConfirmModal(false);
    }));
    setShowConfirmModal(true);
  };

  const renderApiDialog = () => {
    // const {messageDesc: string} = this.props;

    return {
      label: '',
      type: 'action',
      acitonType: 'dialog',
      size: 'sm',
      icon: 'fa fa-code',
      actionType: 'dialog',
      dialog: {
        title: '高级设置',
        size: 'md',
        className: 'ae-ApiControl-dialog',
        headerClassName: 'font-bold',
        bodyClassName: 'ae-ApiControl-dialog-body',
        closeOnEsc: true,
        closeOnOutside: false,
        showCloseButton: true,
        body: [renderApiConfigTabs(false, (value: any) => {
          console.log('confirm', value)
        })]
      }
    };
  }

  const mutiApiConfirm = (value: any) => {
    updateBatchOperate({
      id: value.__super.__super.id,
      applicationPageId: value.__super.__super.applicationPageId,
      api: JSON.stringify(value)
    }).then((res: any) => {
      if (res.code == 0) {
        toast.success('保存成功');
        handleGetFormDataPage(props.pageData.applicationPageId)
      } else {
        toast.error(res.msg);
      }
    })
  }

  const singleApiConfirm = (value: any) => {
    console.log('confirm', value)
    let originSchema = props.pageData.data;
    let columns = originSchema.body[0].columns;
    let operationColumn = columns.find((item: any) => item.type == 'operation');
    let buttons = operationColumn.buttons;


    if (value.__super.__super.label == '编辑') {
      let editBtn = buttons.find((item: any) => item.label == '编辑');
      editBtn.drawer.body[0].api = value;
      updateFromSchema(originSchema)
    } else if (value.__super.__super.label == '查看') {
      let viewBtn = buttons.find((item: any) => item.label == '查看');
      viewBtn.drawer.body[0].initApi = value;
      updateFromSchema(originSchema)
    } else if (value.__super.__super.label == '删除') {
      let deleteBtn = buttons.find((item: any) => item.label == '删除');
      deleteBtn.dialog.body[0].api = value;
      updateFromSchema(originSchema)
    } else if (value.__super.__super.label == '新增') {
      let headerToolbar = originSchema.body[0].headerToolbar;
      let addBtn = headerToolbar.find((item: any) => item.label == '新增');
      addBtn.drawer.body[0].api = value;
      updateFromSchema(originSchema)
    }

  }

  /* created 初始化 */
  React.useEffect(() => {
    let schema = props.pageData.data;

    showFieldData.current = JSON.parse(props.pageData?.field || '[]')
    // let datasetName = props.data.pageType == 13 ?  schema?.title
    let datasetName = props.pageData?.dataSetName
    setDataSetList([datasetName])
    
    let columns =  schema?.body[0]?.columns;
    //  JSON.parse()["body"]["0"]["columns"];
    //没有schema返回
    if (!columns) {
      return;
    }


    // 创建新的options数组
    const newOptions = columns
      .filter((item: any) => item.name) // 过滤出name不为空的选项
      .map((item: any) => ({
        label: item.label || item.name,
        value: item.name
      }));

    setOptions(newOptions);

    // 获取已经配置了searchable的字段，并按新格式处理
    const searchableColumns = columns.filter((item: any) => item.searchable !== null && item.searchable !== undefined && item.searchable !== false);
    const initialTransferValue = searchableColumns.map((item: any) => ({
      label: item.label || item.name,
      value: item.name
    }));
    setTransferValue(initialTransferValue);

    // // 获取autoGenerateFilter
    const autoGenerateFilter = schema?.body[0].autoGenerateFilter;
    setIsEnabled(!!autoGenerateFilter);

        // 获取singleOptEnabled
    const singleOptEnabled = schema?.body[0].singleOptEnabled;
    setSingleOptEnabled(!!singleOptEnabled);

    const mutiOptEnabled = schema?.body[0].mutiOptEnabled;
    setMutiOptEnabled(!!mutiOptEnabled);
        
    // // 获取每列显示几个字段
    const columnSize = schema?.body[0].columnSize || 2;
    setColumnSize(columnSize);


    // // 获取headerToolbar
    const headerToolbar = schema?.body[0].headerToolbar;
    // // 转为数组[{align: 'left', type: 'bulk-actions'}]
    const typesArray = [
      'pagination',
      'statistics',
      'switch-per-page',
      'load-more',
      'export-csv',
      'export-excel',
      'reload',
      'tpl',
      'export-excel-template',
      'bulkActions',
      'columns-toggler'
    ]

    const headerToolbarArray = headerToolbar.filter((item: any) => typesArray.includes(item.type)).map((item: any) => ({
      align: item.align || 'left',
      type: item.type,
      tpl: item.tpl
    }));



    setHeaderToolbarArray(headerToolbarArray)

    const footerToolbar = schema?.body[0].footerToolbar;
    if (footerToolbar) {
      const footerToolbarArray = footerToolbar.filter((item: any) => typesArray.includes(item.type)).map((item: any) => ({
        align: item.align || 'left',
        type: item.type,
        tpl: item.tpl
      }));

      setBottomToolbarArray(footerToolbarArray)
    } else {
      let s1 = {align: 'right', type: 'pagination'}
      let s2 = {align: 'left', type: 'statistics'}
      setBottomToolbarArray([s1, s2])
    }


    // // 获取操作栏设置 type == operation
    const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
    // label == 编辑
    const editBtn = operationBarBtns.find((item: any) => item.label == '编辑');
    // label == 查看
    const viewBtn = operationBarBtns.find((item: any) => item.label == '查看');
    // label == 删除
    const deleteBtn = operationBarBtns.find((item: any) => item.label == '删除');

    // // 获取按钮的hiddenOn
    const editBtnHidden = editBtn?.hidden || false;
    const viewBtnHidden = viewBtn?.hidden || false;
    const deleteBtnHidden = deleteBtn?.hidden || false;

      setOptEditEnabled(!editBtnHidden);
      setOptViewEnabled(!viewBtnHidden);
      setOptDeleteEnabled(!deleteBtnHidden);

    // 从URL参数中获取activeMenu
    const urlParams = new URLSearchParams(props.history.location.search);
    const activeMenuPath = urlParams.get('activeMenu');
    
    // 如果URL中有activeMenu参数，则设置对应的菜单项为活动项
    if (activeMenuPath) {
      const menuItem = menuList.find(item => item.path === activeMenuPath);
      if (menuItem) {
        setActiveItem(menuItem);
      }
    }
  }, [props.pageData, props.history.location]);

  /* created 初始化 end */
  return (
    <div className="pageSettingsBox">
      <div className="pageSettingsBox-aside">
        {menuList.map((item: any) => {
          return (
            <div
              key={item.path}
              className={
                activeItem.path == item.path
                  ? 'pageSettingsBox-aside-item active_item'
                  : 'pageSettingsBox-aside-item'
              }
              onClick={() => handleMenuItemClick(item)}
            >
              <img className="pageSettingsBox-aside-item-img" src={item.icon} />
              <span>{item.title}</span>
            </div>
          );
        })}
      </div>
      <div className="pageSettingsBox-content">
        {activeItem.path == 'datacollection' && (
          <Fragment>
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  数据集设置
                </div>
                <div className="pageSettingsBox-content-item-left-name">
                  请选择数据源、数据集
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  {dataSetList.map((item: any) => {
                    return <div key={item} className="pageSettingsBox-content-item-left-content-dataSetItem" style={{
                      backgroundColor: props.pageData.pageType == 13 ? '#ffffff' : '#fafafa',
                      border: props.pageData.pageType == 13 ? '1px solid #333' : 'none',
                      color: props.pageData.pageType == 13 ? '#333' : '#999'
                    }}>{item}</div>
                  })}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right" style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '10px',
                marginRight: '20px'
              }}>
                {
                  props.pageData.pageType == 13 && (
                    <Button
                      type="button"
                  onClick={() => {
                    setOpenModifyDataSetPopup(true);
                    // props.history.push(`/app_${props.pageData.apply_id}/admin/appSetting/dataSet`)
                  }}
                >
                  修改
                </Button>
                  )
                }
                <Button
                  type="button"
                  onClick={() => {
                    props.history.push(`/platform/manage/dataSet/create?id=${props.pageData.dataSetId}&type=views`)
                  }}
                >
                  查看数据集
                </Button>
                {
                  props.pageData.pageType == 13 && (
                    <Button
                      type="button"
                      onClick={() => {
                        props.history.push(`/platform/manage/dataSet/create?id=${props.pageData.dataSetId}&type=edit`)
                      }}
                    >
                  配置数据集
                    </Button>
                  )
                }
              </div>
            </div>
            
          </Fragment>
        )}
        
        {activeItem.path == 'querySetting' && (
          <Fragment>
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  启用查询条件
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: isEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {isEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <Button
                  type="button"
                  style={{
                    backgroundColor: isEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  onClick={() => {
                    if (isEnabled) {
                      handleConfirmModal(
                        '确认停用',
                        '确定要停用查询条件吗？',
                        () => modifyIsEnabled(false)
                      );
                    } else {
                      modifyIsEnabled(true);
                    }
                  }}
                > {isEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>
            {/* 每列显示几个字段 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  每列显示几个字段
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  {columnSize}个
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <Button
                  type="button"
                  onClick={() => {
                    setOpenColumnSizePopup(true);
                  }}
                >
                  修改
                </Button>
              </div>
            </div>

            <div className="pageSettingsBox-content-item transfer-container">
              <div className="transfer-header">
                <div className="transfer-header-left-name">
                  启用的查询字段
                </div>
                <div className="transfer-header-left-hint">
                  添加要查询的字段
                </div>
              </div>
              <div className="transfer-body">
                <Transfer
                  options={options}
                  value={transferValue}
                  onChange={(value: any) => handleTransferChange(value)}
                />
              </div>
            </div>
          </Fragment>
        )}
        {activeItem.path == 'topToolbarSetting' && (
          <AMISRenderer
            show={true}
            schema={headerToolbarSchema}
            onClose={() => {}}
            onConfirm={(val: any) => {}}
          />
        )}
        {activeItem.path == 'bottomToolbarSetting' && (
          <AMISRenderer
          show={true}
          schema={bottomToolbarSchema}
          onClose={() => {}}
          onConfirm={(val: any) => {}}
        />
        )}
        {activeItem.path == 'actionBarSetting' &&  (
          <Fragment>
            
            {/* <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  操作栏-编辑
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: optEditEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {optEditEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <Button
                  type="button"
                  style={{
                    backgroundColor: optEditEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  onClick={() => {
                    if (optEditEnabled) {
                      handleConfirmModal(
                        '确认停用',
                        '确定要停用编辑功能吗？',
                        () => modifyOperationEdit(false)
                      );
                    } else {
                      modifyOperationEdit(true);
                    }
                  }}
                > {optEditEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>

            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  操作栏-查看详情
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: optViewEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {optViewEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
              <Button
                  type="button"
                  style={{
                    backgroundColor: optViewEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  // title="停用"
                  // actionType="dialog"
                  onClick={() => {
                    // 处理按钮点击事件
                    if (optViewEnabled) {
                      handleConfirmModal(
                        '确认停用',
                        '确定要停用查看详情功能吗？',
                        () => modifyOperationView(false)
                      );
                    } else {
                      modifyOperationView(true);
                    }
                  }}
                > {optViewEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>

            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  操作栏-删除
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: optDeleteEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {optDeleteEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <Button
                  type="button"
                  style={{
                    backgroundColor: optDeleteEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  onClick={() => {
                    if (optDeleteEnabled) {
                      handleConfirmModal(
                        '确认停用',
                        '确定要停用删除功能吗？',
                        () => modifyOperationDelete(false)
                      );
                    } else {
                      modifyOperationDelete(true);
                    }
                  }}
                > {optDeleteEnabled ? '停用' : '启用'}</Button>
              </div>
            </div> */}
           
            <AMISRenderer
              schema={apiSettingObj(props.pageData.data, props.pageData.id, props.pageData.applicationPageId, props.pageData.field, singleApiConfirm)}
            />    
          </Fragment>
        )}

        {activeItem.path == 'singleOperationSetting' &&  (
          <Fragment>
            
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  单条操作
                </div>
                <div className="pageSettingsBox-content-item-left-sub" style={{
                  color: '#B8BABF'
                }}>
                选中一个表格项后，可以实现表格项的编辑功能，支持自定义设置。<a>了解更多</a>
                </div>
              </div>
            </div>

            <div className="pageSettingsBox-content-item">
            <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  启用单条操作
                </div>
                <div className="pageSettingsBox-content-item-left-sub" style={{
                  color: '#B8BABF'
                }}>
                启用后，在表格顶部展示单条操作区
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: singleOptEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {singleOptEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <Button
                  type="button"
                  style={{
                    backgroundColor: singleOptEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  onClick={() => {
                    if (singleOptEnabled) {
                      handleConfirmModal(
                        '确认停用',
                        '确定要停用单条操作功能吗？',
                        () => modifyOperationSingle(false)
                      );
                    } else {
                      modifyOperationSingle(true);
                    }
                  }}
                > {singleOptEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>
            {singleOptEnabled && (
              <AMISRenderer
                schema={singleOperationObj(props.pageData.apply_id, showFieldData.current, props.pageData.applicationPageId, saveSigleAction, singleApiConfirm)}
              />
            )}
           
          </Fragment>
        )}

{activeItem.path == 'mutiOperationSetting' &&  (
          <Fragment>
            
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  批量操作
                </div>
                <div className="pageSettingsBox-content-item-left-sub" style={{
                  color: '#B8BABF'
                }}>
                选中多个表格项后，可以实现表格项的编辑功能，支持自定义设置。<a>了解更多</a>
                </div>
              </div>
            </div>

            <div className="pageSettingsBox-content-item">
            <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  启用批量操作
                </div>
                <div className="pageSettingsBox-content-item-left-sub" style={{
                  color: '#B8BABF'
                }}>
                启用后，在表格顶部展示批量操作区
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: mutiOptEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {mutiOptEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <Button
                  type="button"
                  style={{
                    backgroundColor: mutiOptEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  onClick={() => {
                    if (mutiOptEnabled) {
                      handleConfirmModal(
                        '确认停用',
                        '确定要停用批量操作功能吗？',
                        () => modifyOperationMuti(false)
                      );
                    } else {
                      modifyOperationMuti(true);
                    }
                  }}
                > {mutiOptEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>
            {mutiOptEnabled && (
              <AMISRenderer
                schema={mutiOperationObj(props.pageData.apply_id, showFieldData.current, props.pageData.applicationPageId, saveMutiAction, mutiApiConfirm)}
              />
            )}
           
          </Fragment>
        )}

{activeItem.path == 'columnSetting' &&  (
          <Fragment>
            
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  列设置
                </div>
                <div className="pageSettingsBox-content-item-left-sub" style={{
                  color: '#B8BABF'
                }}>
                列的配置项，可以帮助更好的展示数据。<a>了解更多</a>
                </div>
              </div>
            </div>

            <AMISRenderer
              schema={columnSettingObj(props.pageData.data, props.pageData.id, props.pageData.applicationPageId, props.pageData.field)}
            />            
           
          </Fragment>
        )}
      </div>
      {openColumnSizePopup && (
        <AMISRenderer
          show={openColumnSizePopup}
          schema={modifyColumnSizePopup(columnSize)}
          onClose={() => setOpenColumnSizePopup(false)}
          onConfirm={(val: any) => modifyColumnSize(val)}
        />
      )}
      {openModifyDataSetPopup && (
        <AMISRenderer
          show={openModifyDataSetPopup}
          schema={modifyDataSetPopup(props.pageData.appId)}
          onClose={() => setOpenModifyDataSetPopup(false)}
          onConfirm={(val: any) => modifyDataSet(val)}
        />
      )}

      {/* 添加确认弹框 */}
      {showConfirmModal && (
        <AMISRenderer
          show={showConfirmModal}
          schema={confirmModalSchema}
          onClose={() => setShowConfirmModal(false)}
        />
      )}

    </div>
  );
};

export default PageSettings;
