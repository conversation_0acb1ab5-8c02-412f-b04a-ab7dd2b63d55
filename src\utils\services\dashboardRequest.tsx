// 引入 axios 和类型定义
import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';

const nodeEnv = process.env.NODE_ENV;

// https://m.thinkddm.fjpipixia.com
const baseURL =
  nodeEnv == 'development' ? '/dashboardApiTest' : 'https://dashboard.ykddm.fjpipixia.com';

const instance: AxiosInstance = axios.create({
  baseURL: baseURL,
  timeout: 10000
});

// Add a request interceptor
// 全局请求拦截，发送请求之前执行
instance.interceptors.request.use(
  (config: AxiosRequestConfig): AxiosRequestConfig => {
    // 设置请求的 token 等等
    // config.headers["authorization"] = "Bearer " + getToken();
    let store: any = localStorage.getItem('store');
    store = JSON.parse(store);
    config.headers['http-user-id'] = store.userInfo ? store.userInfo?.id : '';

    let access_token = store.access_token;

    config.headers['authorization'] = access_token
      ? 'Bearer ' + access_token
      : '';

    return config;
  },
  (error: any): Promise<any> => {
    // Do something with request error
    return Promise.reject(error);
  }
);

// Add a response interceptor
// 请求返回之后执行
instance.interceptors.response.use(
  (response: AxiosResponse): any => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    return response.data;
  },
  (error: any): Promise<any> => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    return Promise.reject(error);
  }
);

/**
 * get 请求
 * @param url 请求地址
 * @param params 参数
 */
export function get(url: string, params?: any): Promise<any> {
  return instance.get(url, {params});
}

/**
 * post 请求
 * @param url 请求地址
 * @param data 数据
 */
export function post(url: string, data?: any): Promise<any> {
  return instance.post(url, data);
}

/**
 * put 请求
 * @param url 请求地址
 * @param data 数据
 */
export function put(url: string, data?: any): Promise<any> {
  return instance.put(url, data);
}

/**
 * delete 请求
 * @param url 请求地址
 */
export function del(url: string): Promise<any> {
  return instance.delete(url);
}
