.pageBox {
    position: relative;
    width: 100%;
    height: calc(100vh - 3.125rem);
    overflow: auto;
    /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
    -webkit-scrollbar {
      display: none;
    }
    /* 隐藏Firefox浏览器的滚动条 */
    scrollbar-width: none;
    
  }
  
  .pageTopManage {
    position: relative;
    left: 0;
    width: 100%;
    height: 4.125rem;
    padding: 0 1.875rem;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    text-align: left;
    &-title {
      width: 100%;
      color: var(--color-text-1);
      font-size: 1.25rem;
      text-align: left;
    }

    &-subtitle {
      width: 100%;
      color: var(--color-text-2);
      font-size: 0.75rem;
      text-align: left;
    }
  }
  
  .pageTabsForm {
    position: relative;
    width: 100%;
  
    &-tabsTitle {
      padding: 0 1.875rem;
    }
  
    &-tabsContent{
      width: 100%;
      min-height: 30rem;
      padding: 1.25rem  1.125rem;
  
      &-form{
        width: 50%;
        max-width: 32.5rem;
      }
    }
  }
  
  .field-list {
    margin-top: 16px;
    
    .field-item {
      padding: 12px;
      border-bottom: 1px solid #eee;
      display: flex;
      gap: 24px;
      
      span {
        color: #666;
      }
    }
  }
  
  .cxd-Layout-content, .dark-Layout-content{
    padding: 0;
  }

.page-data-settings {
  display: flex;
  height: 100%;
  
  &-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
  }
}



.page-settings-content {
  flex: 1;
  padding: 24px;

  .form-item {
    margin-bottom: 24px;
  }
}


// 在现有样式的基础上添加
.basic-settings {
  .settings-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: var(--colors-neutral-text-1);
  }

  .settings-desc {
    color: var(--colors-neutral-text-5);
    margin: 0 0 24px 0;
  }

  .settings-section {
    border-radius: 4px;
    padding: 24px;
  }

  .settings-item {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .item-label {
      font-size: 14px;
      color: var(--colors-neutral-text-4);
      margin-bottom: 8px;
    }

    .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .content-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .icon-preview {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        
        i {
          font-size: 16px;
          color: var(--colors-neutral-text-4);
        }
      }

      .content-text {
        color: var(--colors-neutral-text-2);
        font-size: 14px;
      }

      .edit-btn {
        padding: 4px 12px;
        background: var(--colors-neutral-fill-10);
        border-radius: 4px;
        color: var(--colors-neutral-text-6);
        font-size: 12px;
        border: none;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background: var(--colors-neutral-fill-9);
          color: var(--colors-neutral-text-2);
        }
      }
    }
  }
}

// 暗黑模式适配
[data-theme="dark"] {
  .basic-settings {
    .settings-title {
      color: #fff;
    }

    .settings-desc {
      color: #86909c;
    }

    .settings-section {
      background: var(--backgroundDark);
    }

    .settings-item {
      .item-label {
        color: #c9cdd4;
      }

      .item-content {
        .icon-preview {
          background: #2a2a2a;
          
          i {
            color: #c9cdd4;
          }
        }

        .content-text {
          color: #fff;
        }

        .edit-btn {
          background: #2a2a2a;
          color: #c9cdd4;
          
          &:hover {
            background: #363e4a;
            color: #fff;
          }
        }
      }
    }
  }
}