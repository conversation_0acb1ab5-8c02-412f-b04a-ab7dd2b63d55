const createImportDatasetCRUD = (
    otherFields: any[],
    datasetId: string
  ): any => {
  
    const waitAddFields = [
      {
        name: 'id',
        control: 'input-text',
        label: 'ID'
      },
      {
        name: 'creator',
        control: 'select',
        label: '创建者'
      },
      {
        name: 'createTime',
        control: 'input-datetime',
        label: '创建时间',
        valueFormat: 'x'
      }
    ];

    otherFields = [...waitAddFields, ...otherFields];
    
    const needFilterFields = ['id', 'creator', 'createTime'];
    const filterFields = otherFields.filter(
      (item: any) => !needFilterFields.includes(item.name)
    );
  
    const otherColumns = otherFields.map((item: any) => {
      if (item.controlType == 'input-image') {
        return {
          label: item.label,
          type: 'image',
          name: item.name,
          enlargeAble: true,
          sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false
        };
      } else if (item.controlType == 'input-file') {
        return {
          label: item.label,
          type: 'link',
          name: item.name,
          blank: true,
          sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
  
        };
      } else if (item.controlType == 'icon') {
        return {
          label: item.label,
          type: 'icon',
          //name: item.name,
          icon: '${' + item.name + '|raw}',
          sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
        };
      } else if (item.controlType == 'select') {
        let dictType = item.dictType;
        if (dictType) {
          let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: true,
            sortable: true,
            textOverflow: 'ellipsis',
            needWordBreak: false,
            source: {
              method: 'get',
              url: api,
              responseData: {
                options: "${list}"
              }
            },
            height: "50px",
          className: "h-8"
          };
        } else {
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: true,
            sortable: true,
            textOverflow: 'ellipsis',
            needWordBreak: false,
            height: "50px",
          className: "h-8"
          };
        }
      }  else {
        return {
          ...item.json,
          label: item.label,
          // type: 'text',
          type: item.controlType ?? 'input-text',
          name: item.name,
          static: true,
          valueFormat: 'x',
          sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
        };
      }
    });
  
  
    const addColumns = filterFields.map((item: any) => {
      if (item.controlType == 'select') {
        let dictType = item.dictType;
        if (dictType) {
          let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            source: {
              method: 'get',
              url: api,
              responseData: {
                options: "${list}"
              }
            },
            static: item.isRead === 1
          };
        } else {
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: item.isRead === 1
          };
        }
      }
  
      return {
        ...item.json,
        label: item.label,
        type: item.controlType ?? 'input-text',
        name: item.name,
        valueFormat: 'x',
        static: item.isRead === 1
      };
    });
  
    const editColumns = filterFields.map((item: any) => {
      if (item.controlType == 'select') {
        let dictType = item.dictType;
        if (dictType) {
          let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            source: {
              method: 'get',
              url: api,
              responseData: {
                options: "${list}"
              }
            },
            static: item.isRead === 1
          };
        } else {
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: item.isRead === 1
          };
        }
      }
  
      return {
        ...item.json,
        label: item.label,
        type: item.controlType ?? 'input-text',
        name: item.name,
        valueFormat: 'x',
        static: item.isRead === 1
      };
    });
  
  
    const apiData: {[key: string]: any} = { // 添加索引签名
      dateSetId: datasetId,
      orderBy: '${orderBy|default:undefined}',
      orderDir: '${orderDir|default:undefined}'
    };
  
    otherFields.forEach((item: any) => {
      //amis内置参数，会有冲突
      if (item.name != 'params') {
        apiData[item.name] = `\${${item.name}|default:undefined}`;
      }
    });
  
  
    return         {
        type: 'crud-dataset',
        lineHeight: 'middle',
        syncLocation: false,
        api: {
          method: 'get',
          url: `/admin-api/system/form-field-value/page/dataSetId/${datasetId}`,
          data: {...apiData,
            pageNo: '${pageNo}',
            pageSize: '${pageSize}'
          }
        },
        bulkActions: [],
        itemActions: [],
        perPageAvailable: [5, 10, 20, 50, 100],
        messages: {},
        filterSettingSource: ['id', 'creator', 'createTime'],
        headerToolbar: [
          {
            label: '新增',
            type: 'button',
            actionType: 'drawer',
            level: 'primary',
            editorSetting: {
              behavior: 'create'
            },
            drawer: {
              type: 'drawer',
              title: '新增',
              position: "right",
              body: [
                {
                  type: 'form',
                  api: {
                    method: 'post',
                    url: `/admin-api/system/form-field-value/create/dataSetId/${datasetId}`
                  },
                  mode: 'horizontal',
                  horizontal: {
                    left: 2,
                    right: 10,
                    offset: 2
                  },
                  body: [...addColumns],
                  actions: [
                    {
                      type: 'submit',
                      label: '提交',
                      primary: true
                    }
                  ],
                  feat: 'Insert'
                }
              ],
              actionType: 'drawer',
              actions: [
                {
                  type: 'button',
                  actionType: 'cancel',
                  label: '取消'
                },
                {
                  type: 'button',
                  actionType: 'confirm',
                  label: '确定',
                  primary: true
                }
              ],
              showCloseButton: true,
              closeOnOutside: false,
              closeOnEsc: false,
              showErrorMsg: true,
              showLoading: true,
              draggable: false,
              size: 'lg',
              resizable: false,
              editorSetting: {
                displayName: '新增'
              }
            }
          },
          {
            type: 'export-csv',
            tpl: '内容',
            wrapperComponent: ''
          },
          {
            type: 'export-excel',
            tpl: '内容',
            wrapperComponent: ''
          }
        ],
        columns: [
          ...otherColumns,
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                label: '编辑',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'update'
                },
                id: 'u:4d423864d6c0',
                drawer: {
                  type: 'drawer',
                  title: '编辑',
                  position: "right",
                  body: [
                    {
                      type: 'form',
                      api:
                        `put:/admin-api/system/form-field-value/batch-update/dataSetId/${datasetId}/` +
                        '${entries}',
                      mode: 'horizontal',
                      horizontal: {
                        left: 2,
                        right: 10,
                        offset: 2
                      },
                      body: [...editColumns, {
                        type: 'input-text',
                        label: 'ID',
                        name: 'id',
                        value: '${id}',
                        hidden: true
                      }],
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ],
                      feat: 'Edit',
                      dsType: 'api',
                      // labelAlign: 'top',
                      title: '表单',
                      resetAfterSubmit: true
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:2e86c554e48e'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:392213d59e8d'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '编辑'
                  }
                }
              },
              {
                label: '查看',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'view'
                },
                drawer: {
                  type: 'drawer',
                  title: '查看详情',
                  body: [
                    {
                      type: 'form',
                      initApi:
                        `/admin-api/system/form-field-value/get/dataSetId/${datasetId}/` +
                        '${entries}',
                      body: [...otherColumns],
                      id: 'u:2af2f2b5c2fd',
                      feat: 'View',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      static: true,
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ]
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '查看详情'
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                actionType: 'dialog',
                level: 'link',
                className: 'text-danger',
                dialog: {
                  type: 'dialog',
                  title: '',
                  className: 'py-2',
                  actions: [
                    {
                      type: 'action',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'action',
                      actionType: 'submit',
                      label: '删除',
                      level: 'danger'
                    }
                  ],
                  body: [
                    {
                      type: 'form',
                      wrapWithPanel: false,
                      api:
                        `delete:/admin-api/system/form-field-value/delete/dataSetId/${datasetId}/` +
                        '${entries}',
                      body: [
                        {
                          type: 'tpl',
                          className: 'py-2',
                          tpl: '确认删除选中项？'
                        }
                      ],
                      feat: 'Insert',
                      dsType: 'api',
                      labelAlign: 'left'
                    }
                  ],
                  actionType: 'dialog',
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  editorSetting: {
                    displayName: '删除'
                  }
                }
              }
            ]
          }
        ],
        pageField: 'pageNo',
        perPageField: 'pageSize',
        alwaysShowPagination: true
      }
  };
  
  export {createImportDatasetCRUD};

  const createImportDatasetCRUD2 = (
    otherFields: any[],
    datasetId: string
  ): any => {
  
    const waitAddFields = [
      {
        name: 'id',
        controlType: 'input-text',
        label: 'ID'
      },
      {
        name: 'creator',
        controlType: 'user-select',
        label: '创建者'
      },
      {
        name: 'createTime',
        controlType: 'input-datetime',
        label: '创建时间',
        valueFormat: 'x'
      }
    ];

    otherFields = [...waitAddFields, ...otherFields];
    
    const needFilterFields = ['id', 'creator', 'createTime'];
    const filterFields = otherFields.filter(
      (item: any) => !needFilterFields.includes(item.name)
    );
  
    const otherColumns = otherFields.map((item: any) => {
      if (item.controlType == 'input-image') {
        return {
          title: item.label,
          type: 'image',
          name: item.name,
          enlargeAble: true,
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false
        };
      } else if (item.controlType == 'input-file') {
        return {
            title: item.label,
          type: 'link',
          name: item.name,
          blank: true,
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
  
        };
      } else if (item.controlType == 'icon') {
        return {
            title: item.label,
          type: 'icon',
          //name: item.name,
          icon: '${' + item.name + '|raw}',
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
        };
      } else if (item.controlType == 'select') {
        let dictType = item.dictType;
        if (dictType) {
          let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
          return {
            ...item.json,
            title: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: true,
            // sortable: true,
            textOverflow: 'ellipsis',
            needWordBreak: false,
            source: {
              method: 'get',
              url: api,
              responseData: {
                options: "${list}"
              }
            },
            height: "50px",
          className: "h-8"
          };
        } else {
          return {
            ...item.json,
            title: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: true,
            // sortable: true,
            textOverflow: 'ellipsis',
            needWordBreak: false,
            height: "50px",
          className: "h-8"
          };
        }
      }  else {
        return {
          ...item.json,
          title: item.label,
          // type: 'text',
          type: item.controlType ?? 'input-text',
          name: item.name,
          static: true,
          valueFormat: 'x',
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
        };
      }
    });
  
  
    const addColumns = filterFields.map((item: any) => {
      if (item.controlType == 'select') {
        let dictType = item.dictType;
        if (dictType) {
          let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            source: {
              method: 'get',
              url: api,
              responseData: {
                options: "${list}"
              }
            },
            static: item.isRead === 1
          };
        } else {
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: item.isRead === 1
          };
        }
      }
  
      return {
        ...item.json,
        label: item.label,
        type: item.controlType ?? 'input-text',
        name: item.name,
        valueFormat: 'x',
        static: item.isRead === 1
      };
    });
  
    const filterColumns = filterFields.map((item: any) => {
      return {
        ...item.json,
        label: item.label,
        type: item.controlType ?? 'input-text',
        name: item.name,
        valueFormat: 'x',
        required: false,
        behavior: 'SimpleQuery',
        size: 'full'
      };
    });
    // const editColumns = filterFields.map((item: any) => {
    //   if (item.controlType == 'select') {
    //     let dictType = item.dictType;
    //     if (dictType) {
    //       let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
    //       return {
    //         ...item.json,
    //         label: item.label,
    //         type: item.controlType ?? 'input-text',
    //         name: item.name,
    //         valueFormat: 'x',
    //         source: {
    //           method: 'get',
    //           url: api,
    //           responseData: {
    //             options: "${list}"
    //           }
    //         },
    //         static: item.isRead === 1
    //       };
    //     } else {
    //       return {
    //         ...item.json,
    //         label: item.label,
    //         type: item.controlType ?? 'input-text',
    //         name: item.name,
    //         valueFormat: 'x',
    //         static: item.isRead === 1
    //       };
    //     }
    //   }
  
    //   return {
    //     ...item.json,
    //     label: item.label,
    //     type: item.controlType ?? 'input-text',
    //     name: item.name,
    //     valueFormat: 'x',
    //     static: item.isRead === 1
    //   };
    // });
  
  
    const apiData: {[key: string]: any} = { // 添加索引签名
      dateSetId: datasetId,
      orderBy: '${orderBy|default:undefined}',
      orderDir: '${orderDir|default:undefined}'
    };
  
    otherFields.forEach((item: any) => {
      //amis内置参数，会有冲突
      if (item.name != 'params') {
        apiData[item.name] = `\${${item.name}|default:undefined}`;
      }
    });
  
  
    return {
        type: 'crud2-dataset',
        mode: 'table2',
        lineHeight: 'middle',
        id: 'cruddataset',
        syncLocation: false,
        selectable: true,
        multiple: true,
        api: {
          method: 'get',
          url: `/admin-api/system/form-field-value/page/dataSetId/${datasetId}`
        },
        filter: {
            type: "form",
            title: "条件查询",
            mode: "inline",
            columnCount: 3,
            clearValueOnHidden: true,
            behavior: [
              "SimpleQuery"
            ],
            body: [
              ...filterColumns
            ],
            actions: [
              {
                type: "reset",
                label: "重置",
                id: "u:54003f762287"
              },
              {
                type: "submit",
                label: "查询",
                level: "primary",
                id: "u:b7d566b5ec1e"
              }
            ],
            id: "u:4c54d33e18de",
            feat: "Insert"
          },
        quickSaveItemApi: `put:/admin-api/system/form-field-value/batch-update/dataSetId/${datasetId}/` +
        '${entries}',
        headerToolbar: [
            {
              "type": "flex",
              "direction": "row",
              "justify": "flex-start",
              "alignItems": "stretch",
              "style": {
                "position": "static"
              },
              "items": [
                {
                  "type": "container",
                  "align": "left",
                  "behavior": [
                    "Insert",
                    "BulkEdit",
                    "BulkDelete"
                  ],
                  "body": [
                    {
                      "type": "button",
                      "label": "新增",
                      "level": "primary",
                      "className": "m-r-xs",
                      "behavior": "Insert",
                      "onEvent": {
                        "click": {
                          "actions": [
                            {
                              "actionType": "dialog",
                              "dialog": {
                                "body": {
                                  "id": "u:7e110e33de8c",
                                  "type": "form",
                                  "title": "新增数据",
                                  "mode": "flex",
                                  "labelAlign": "top",
                                  "dsType": "api",
                                  "feat": "Insert",
                                  "api": `post:/admin-api/system/form-field-value/create/dataSetId/${datasetId}`,
                                  "body": [
                                    ...addColumns
                                  ],
                                  "resetAfterSubmit": true,
                                  "actions": [
                                    {
                                      "type": "button",
                                      "actionType": "cancel",
                                      "label": "取消"
                                    },
                                    {
                                      "type": "button",
                                      "actionType": "submit",
                                      "label": "提交",
                                      "level": "primary"
                                    }
                                  ],
                                  "onEvent": {
                                    "submitSucc": {
                                      "actions": [
                                        {
                                          "actionType": "reload",
                                          "componentId": "cruddataset"
                                        }
                                      ]
                                    }
                                  }
                                },
                                "title": "新增数据",
                                "size": "md",
                                "actions": [
                                  {
                                    "type": "button",
                                    "actionType": "cancel",
                                    "label": "取消"
                                  },
                                  {
                                    "type": "button",
                                    "actionType": "submit",
                                    "label": "提交",
                                    "level": "primary"
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      },
                      "id": "u:e7628c74d83d"
                    },
                    {
                      "type": "button",
                      "label": "批量编辑",
                      "className": "m-r-xs",
                      "disabledOn": "${selectedItems != null && selectedItems.length < 1}",
                      "behavior": "BulkEdit",
                      "onEvent": {
                        "click": {
                          "actions": [
                            {
                              "actionType": "dialog",
                              "dialog": {
                                "body": {
                                  "id": "u:24ff19d248a8",
                                  "type": "form",
                                  "title": "批量编辑",
                                  "mode": "flex",
                                  "labelAlign": "top",
                                  "dsType": "api",
                                  "feat": "BulkEdit",
                                  "api": {
                                    method: "put",
                                    url: `/admin-api/system/form-field-value/batch-update-multiline/dataSetId/${datasetId}` + "?ids=${ids}"
                                  },
                                  "body": [
                                    ...addColumns
                                  ],
                                  "resetAfterSubmit": true,
                                  "actions": [
                                    {
                                      "type": "button",
                                      "actionType": "cancel",
                                      "label": "取消"
                                    },
                                    {
                                      "type": "button",
                                      "actionType": "submit",
                                      "label": "提交",
                                      "level": "primary"
                                    }
                                  ],
                                  "onEvent": {
                                    "submitSucc": {
                                      "actions": [
                                        {
                                          "actionType": "search",
                                          "groupType": "component",
                                          "componentId": "cruddataset"
                                        }
                                      ]
                                    }
                                  }
                                },
                                "title": "批量编辑",
                                "size": "md",
                                "actions": [
                                  {
                                    "type": "button",
                                    "actionType": "cancel",
                                    "label": "取消"
                                  },
                                  {
                                    "type": "button",
                                    "actionType": "submit",
                                    "label": "提交",
                                    "level": "primary"
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      },
                      "id": "u:7e0943a2ae62"
                    },
                    {
                      "type": "button",
                      "label": "批量删除",
                      "behavior": "BulkDelete",
                      "level": "danger",
                      "className": "m-r-xs",
                      "confirmText": "确认要批量删除数据「${JOIN(ARRAYMAP(selectedItems, item => item.id), ',')}」",
                      "disabledOn": "${selectedItems != null && selectedItems.length < 1}",
                      "onEvent": {
                        "click": {
                          "actions": [
                            {
                              "actionType": "ajax",
                              "api": {
                                method: "delete",
                                url: `/admin-api/system/form-field-value/delete-batch/dataSetId/${datasetId}` + "?ids=${ids}"
                              }
                            },
                            {
                              "actionType": "search",
                              "groupType": "component",
                              "componentId": "cruddataset"
                            }
                          ]
                        }
                      },
                      "id": "u:01b6b0aab089"
                    }
                  ],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-start"
                  },
                  "id": "u:d47d622907a0"
                },
                {
                  "type": "container",
                  "align": "right",
                  "behavior": [
                    "FuzzyQuery"
                  ],
                  "body": [],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-end"
                  },
                  "id": "u:8b972efb9fc6"
                }
              ],
              "id": "u:6a3da68d801e"
            }
          ],
          footerToolbar: [
            {
              "type": "flex",
              "direction": "row",
              "justify": "flex-start",
              "alignItems": "stretch",
              "style": {
                "position": "static"
              },
              "items": [
                {
                  "type": "container",
                  "align": "left",
                  "body": [],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-start"
                  },
                  "id": "u:d928a7684f57"
                },
                {
                  "type": "container",
                  "align": "right",
                  "body": [
                    {
                      "type": "pagination",
                      "behavior": "Pagination",
                      "layout": [
                        "total",
                        "perPage",
                        "pager"
                      ],
                      "perPage": 10,
                      "perPageAvailable": [
                        10,
                        20,
                        50,
                        100
                      ],
                      "align": "right",
                      "id": "u:5417593f00f1"
                    }
                  ],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-end"
                  },
                  "id": "u:0627838437d5"
                }
              ],
              "id": "u:14c5e2b9e4b2"
            }
          ],
        perPageAvailable: [5, 10, 20, 50, 100],
        columns: [
          ...otherColumns,
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                label: '编辑',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'update'
                },
                id: 'u:4d423864d6c0',
                drawer: {
                  type: 'drawer',
                  title: '编辑',
                  position: "right",
                  body: [
                    {
                      type: 'form',
                      api:
                        `put:/admin-api/system/form-field-value/batch-update/dataSetId/${datasetId}/` +
                        '${entries}',
                      mode: 'horizontal',
                      horizontal: {
                        left: 2,
                        right: 10,
                        offset: 2
                      },
                      body: [...addColumns, {
                        type: 'input-text',
                        label: 'ID',
                        name: 'id',
                        value: '${id}',
                        hidden: true
                      }],
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ],
                      feat: 'Edit',
                      dsType: 'api',
                      // labelAlign: 'top',
                      title: '表单',
                      resetAfterSubmit: true
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:2e86c554e48e'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:392213d59e8d'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '编辑'
                  }
                }
              },
              {
                label: '查看',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'view'
                },
                drawer: {
                  type: 'drawer',
                  title: '查看详情',
                  body: [
                    {
                      type: 'form',
                      initApi:
                        `/admin-api/system/form-field-value/get/dataSetId/${datasetId}/` +
                        '${entries}',
                      body: [...otherColumns],
                      id: 'u:2af2f2b5c2fd',
                      feat: 'View',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      static: true,
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ]
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '查看详情'
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                actionType: 'dialog',
                level: 'link',
                className: 'text-danger',
                dialog: {
                  type: 'dialog',
                  title: '',
                  className: 'py-2',
                  actions: [
                    {
                      type: 'action',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'action',
                      actionType: 'submit',
                      label: '删除',
                      level: 'danger'
                    }
                  ],
                  body: [
                    {
                      type: 'form',
                      wrapWithPanel: false,
                      api:
                        `delete:/admin-api/system/form-field-value/delete/dataSetId/${datasetId}/` +
                        '${entries}',
                      body: [
                        {
                          type: 'tpl',
                          className: 'py-2',
                          tpl: '确认删除选中项？'
                        }
                      ],
                      feat: 'Insert',
                      dsType: 'api',
                      labelAlign: 'left'
                    }
                  ],
                  actionType: 'dialog',
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  editorSetting: {
                    displayName: '删除'
                  }
                }
              }
            ]
          }
        ]
      }
  };
  
  export {createImportDatasetCRUD2};


  const createRelatedform = (
    otherFields: any[],
    datasetId: string,
    pickCol?: string
  ): any => {

    const waitAddFields = [
      {
        name: 'id',
        controlType: 'input-text',
        label: 'ID'
      },
      {
        name: 'creator',
        controlType: 'user-select',
        label: '创建者'
      },
      {
        name: 'createTime',
        controlType: 'input-datetime',
        label: '创建时间',
        valueFormat: 'x'
      }
    ];
    let pickColArr = pickCol?.split(',') ?? []
    //id需要单独处理，如果pickColArr不包含id，需要添加，并且给一个标识符
    const showId = pickColArr.includes('id')
    if (pickColArr && !pickColArr.includes('id')) {
      pickColArr.unshift('id'); 
    }
    

    otherFields = [...waitAddFields, ...otherFields];
    if (pickColArr && pickColArr.length > 0) {
      otherFields = pickColArr
        .map((name: string) => otherFields.find((item: any) => item.name === name))
        .filter(Boolean); // 过滤掉未找到的
    }

    const needFilterFields = ['id', 'creator', 'createTime'];
    const filterFields = otherFields.filter(
      (item: any) => !needFilterFields.includes(item.name)
    );
  
    const otherColumns = otherFields.map((item: any) => {
      if (item.controlType == 'input-image') {
        return {
          title: item.label,
          type: 'image',
          name: item.name,
          enlargeAble: true,
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false
        };
      } else if (item.controlType == 'input-file') {
        return {
            title: item.label,
          type: 'link',
          name: item.name,
          blank: true,
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
  
        };
      } else if (item.controlType == 'icon') {
        return {
            title: item.label,
          type: 'icon',
          //name: item.name,
          icon: '${' + item.name + '|raw}',
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
        };
      } else if (item.controlType == 'select') {
        let dictType = item.dictType;
        if (dictType) {
          let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
          return {
            ...item.json,
            title: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: true,
            // sortable: true,
            textOverflow: 'ellipsis',
            needWordBreak: false,
            source: {
              method: 'get',
              url: api,
              responseData: {
                options: "${list}"
              }
            },
            height: "50px",
          className: "h-8"
          };
        } else {
          return {
            ...item.json,
            title: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: true,
            // sortable: true,
            textOverflow: 'ellipsis',
            needWordBreak: false,
            height: "50px",
          className: "h-8"
          };
        }
      }  else {
        if (item.name == "id") {
          return {
            ...item.json,
            title: item.label,
            // type: 'text',
            type: item.controlType ?? 'input-text',
            name: item.name,
            static: true,
            valueFormat: 'x',
          //   sortable: true,
            textOverflow: 'ellipsis',
            needWordBreak: false,
            height: "50px",
            className: "h-8",
            hidden: !showId
          }
        }
        return {
          ...item.json,
          title: item.label,
          // type: 'text',
          type: item.controlType ?? 'input-text',
          name: item.name,
          static: true,
          valueFormat: 'x',
        //   sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
          className: "h-8"
        };
      }
    });
  
  
    const addColumns = filterFields.map((item: any) => {
      if (item.controlType == 'select') {
        let dictType = item.dictType;
        if (dictType) {
          let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            source: {
              method: 'get',
              url: api,
              responseData: {
                options: "${list}"
              }
            },
            static: item.isRead === 1
          };
        } else {
          return {
            ...item.json,
            label: item.label,
            type: item.controlType ?? 'input-text',
            name: item.name,
            valueFormat: 'x',
            static: item.isRead === 1
          };
        }
      }
  
      return {
        ...item.json,
        label: item.label,
        type: item.controlType ?? 'input-text',
        name: item.name,
        valueFormat: 'x',
        static: item.isRead === 1
      };
    });
  
    const filterColumns = filterFields.map((item: any) => {
      return {
        ...item.json,
        label: item.label,
        type: item.controlType ?? 'input-text',
        name: item.name,
        valueFormat: 'x',
        required: false,
        behavior: 'SimpleQuery',
        size: 'full'
      };
    });
  
    const apiData: {[key: string]: any} = { // 添加索引签名
      dateSetId: datasetId,
      orderBy: '${orderBy|default:undefined}',
      orderDir: '${orderDir|default:undefined}'
    };
  
    otherFields.forEach((item: any) => {
      //amis内置参数，会有冲突
      if (item.name != 'params') {
        apiData[item.name] = `\${${item.name}|default:undefined}`;
      }
    });
  
  
    return {
        type: 'relatedform',
        mode: 'table2',
        datasetId: datasetId,
        pickCol: pickCol,
        lineHeight: 'middle',
        id: 'relatedform',
        syncLocation: false,
        selectable: true,
        multiple: true,
        api: {
          method: 'get',
          url: `/admin-api/system/form-field-value/page/dataSetId/${datasetId}`
        },
        filter: {
            type: "form",
            title: "条件查询",
            mode: "inline",
            columnCount: 3,
            clearValueOnHidden: true,
            behavior: [
              "SimpleQuery"
            ],
            body: [
              ...filterColumns
            ],
            actions: [
              {
                type: "reset",
                label: "重置",
                id: "u:54003f762287"
              },
              {
                type: "submit",
                label: "查询",
                level: "primary",
                id: "u:b7d566b5ec1e"
              }
            ],
            id: "u:4c54d33e18de",
            feat: "Insert"
          },
        quickSaveItemApi: `put:/admin-api/system/form-field-value/batch-update/dataSetId/${datasetId}/` +
        '${entries}',
        headerToolbar: [
            {
              "type": "flex",
              "direction": "row",
              "justify": "flex-start",
              "alignItems": "stretch",
              "visible": true,
              "style": {
                "position": "static"
              },
              "items": [
                {
                  "type": "container",
                  "align": "left",
                  "behavior": [
                    "Insert",
                    "BulkEdit",
                    "BulkDelete"
                  ],
                  "body": [
                    {
                      "type": "button",
                      "label": "新增",
                      "level": "primary",
                      "className": "m-r-xs",
                      "behavior": "Insert",
                      "onEvent": {
                        "click": {
                          "actions": [
                            {
                              "actionType": "dialog",
                              "dialog": {
                                "body": {
                                  "id": "u:7e110e33de8c",
                                  "type": "form",
                                  "title": "新增数据",
                                  "mode": "flex",
                                  "labelAlign": "top",
                                  "dsType": "api",
                                  "feat": "Insert",
                                  "api": `post:/admin-api/system/form-field-value/create/dataSetId/${datasetId}`,
                                  "body": [
                                    ...addColumns
                                  ],
                                  "resetAfterSubmit": true,
                                  "actions": [
                                    {
                                      "type": "button",
                                      "actionType": "cancel",
                                      "label": "取消"
                                    },
                                    {
                                      "type": "button",
                                      "actionType": "submit",
                                      "label": "提交",
                                      "level": "primary"
                                    }
                                  ],
                                  "onEvent": {
                                    "submitSucc": {
                                      "actions": [
                                        {
                                          "actionType": "reload",
                                          "componentId": "relatedform"
                                        }
                                      ]
                                    }
                                  }
                                },
                                "title": "新增数据",
                                "size": "md",
                                "actions": [
                                  {
                                    "type": "button",
                                    "actionType": "cancel",
                                    "label": "取消"
                                  },
                                  {
                                    "type": "button",
                                    "actionType": "submit",
                                    "label": "提交",
                                    "level": "primary"
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      },
                      "id": "u:e7628c74d83d"
                    }
                    
                  ],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-start"
                  },
                  "id": "u:d47d622907a0"
                },
                {
                  "type": "container",
                  "align": "right",
                  "behavior": [
                    "FuzzyQuery"
                  ],
                  "body": [],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-end"
                  },
                  "id": "u:8b972efb9fc6"
                }
              ],
              "id": "u:6a3da68d801e"
            }
          ],
          footerToolbar: [
            {
              "type": "flex",
              "direction": "row",
              "justify": "flex-start",
              "alignItems": "stretch",
              "style": {
                "position": "static"
              },
              "items": [
                {
                  "type": "container",
                  "align": "left",
                  "body": [],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-start"
                  },
                  "id": "u:d928a7684f57"
                },
                {
                  "type": "container",
                  "align": "right",
                  "body": [
                    {
                      "type": "pagination",
                      "behavior": "Pagination",
                      "layout": [
                        "total",
                        "perPage",
                        "pager"
                      ],
                      "perPage": 10,
                      "perPageAvailable": [
                        10,
                        20,
                        50,
                        100
                      ],
                      "align": "right",
                      "id": "u:5417593f00f1"
                    }
                  ],
                  "wrapperBody": false,
                  "style": {
                    "flexGrow": 1,
                    "flex": "1 1 auto",
                    "position": "static",
                    "display": "flex",
                    "flexBasis": "auto",
                    "flexDirection": "row",
                    "flexWrap": "nowrap",
                    "alignItems": "stretch",
                    "justifyContent": "flex-end"
                  },
                  "id": "u:0627838437d5"
                }
              ],
              "id": "u:14c5e2b9e4b2"
            }
          ],
        perPageAvailable: [5, 10, 20, 50, 100],
        columns: [
          ...otherColumns,
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                label: '编辑',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'update'
                },
                id: 'u:4d423864d6c0',
                drawer: {
                  type: 'drawer',
                  title: '编辑',
                  position: "right",
                  body: [
                    {
                      type: 'form',
                      api:
                        `put:/admin-api/system/form-field-value/batch-update/dataSetId/${datasetId}/` +
                        '${entries}',
                      mode: 'horizontal',
                      horizontal: {
                        left: 2,
                        right: 10,
                        offset: 2
                      },
                      body: [...addColumns, {
                        type: 'input-text',
                        label: 'ID',
                        name: 'id',
                        value: '${id}',
                        hidden: true
                      }],
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ],
                      feat: 'Edit',
                      dsType: 'api',
                      // labelAlign: 'top',
                      title: '表单',
                      resetAfterSubmit: true
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:2e86c554e48e'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:392213d59e8d'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '编辑'
                  }
                }
              },
              {
                label: '查看',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'view'
                },
                drawer: {
                  type: 'drawer',
                  title: '查看详情',
                  body: [
                    {
                      type: 'form',
                      initApi:
                        `/admin-api/system/form-field-value/get/dataSetId/${datasetId}/` +
                        '${entries}',
                      body: [...otherColumns],
                      id: 'u:2af2f2b5c2fd',
                      feat: 'View',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      static: true,
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ]
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '查看详情'
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                actionType: 'dialog',
                level: 'link',
                className: 'text-danger',
                dialog: {
                  type: 'dialog',
                  title: '',
                  className: 'py-2',
                  actions: [
                    {
                      type: 'action',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'action',
                      actionType: 'submit',
                      label: '删除',
                      level: 'danger'
                    }
                  ],
                  body: [
                    {
                      type: 'form',
                      wrapWithPanel: false,
                      api:
                        `delete:/admin-api/system/form-field-value/delete/dataSetId/${datasetId}/` +
                        '${entries}',
                      body: [
                        {
                          type: 'tpl',
                          className: 'py-2',
                          tpl: '确认删除选中项？'
                        }
                      ],
                      feat: 'Insert',
                      dsType: 'api',
                      labelAlign: 'left'
                    }
                  ],
                  actionType: 'dialog',
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  editorSetting: {
                    displayName: '删除'
                  }
                }
              }
            ]
          }
        ]
      }
  };
  
  export {createRelatedform};