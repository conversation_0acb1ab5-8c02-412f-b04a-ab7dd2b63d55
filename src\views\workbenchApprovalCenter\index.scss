.pageTabsForm {
    position: relative;
    overflow: auto;
    scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // Internet Explorer 10+
    width: 100%;
    &-tabsTitle {
      padding: 0 1.875rem;
    }
  
    &-tabsContent{
      width: 100%;
      height: 100vh;
      padding: 1rem;
    }
  }

.approvalCenterBox {
  position: relative;
  width: 100%;
  overflow: hidden;

  &-top {
    position: relative;
    width: 100%;
    height: 4.875rem;
    padding: 0.875rem 1.25rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    

    &-name {
      color: var(--text-color);
      font-size: 1rem;
      font-weight: bold;
    }

    &-desc {
      color: var(--text--muted-color);
      font-size: 0.875rem;
    }
  }

  &-main {
    position: relative;
    width: calc(100% - 1.5rem);
    margin: 0.75rem;
    display: flex;

    &-sidebar {
      padding-top: 0.5rem;
      width: 15rem;
      border-right: 1px solid rgba($color: #000000, $alpha: 0.08);
      min-height: calc(100vh - 9.5rem);

      &-list {
        width: 100%;

        &-item {
          width: 14rem;
          height: 2.5rem;
          cursor: pointer;
          display: flex;
          margin: 0 auto;
          border-radius: 0.5rem;
          align-items: center;
          padding-left: 2.5rem;
          margin-top: 0.25rem;

          &:hover {
            background-color: rgba($color: #e6e6e6, $alpha: 0.5);
          }

          &-img {
            width: 1rem;
            height: 1rem;
            display: block;
          }

          &-text {
            color: #333333;
            font-size: 0.875rem;
            margin-left: 0.5rem;
          }
        }
      }
    }

    &-content {
      flex: 1;
      padding: 0.875rem 1.25rem;
    }
  }
}

.item-click {
  background-color: var(--light-bg);
}
