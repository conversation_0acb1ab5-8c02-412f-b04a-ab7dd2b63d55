.page-data-settings-sider {
  width: 200px;
  border-right: 1px solid var(--borderColor);
  height: 100%;
  background-color: var(--background, #f7f7f7);
  
  .settings-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    
    &-item {
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      color: var(--text-color, rgba(0, 0, 0, 0.85));
      
      i {
        margin-right: 8px;
        font-size: 16px;
        width: 20px;
        text-align: center;
        color: var(--icon-color, #8c8c8c);
      }
      
      &:hover {
        background-color: var(--hover-bg, #e6f7ff);
        color: var(--primary, #1890ff);
        
        i {
          color: var(--primary, #1890ff);
        }
      }
      
      &.active {
        background-color: var(--primary-bg, #e6f7ff);
        color: var(--primary, #1890ff);
        border-right: 3px solid var(--primary, #1890ff);
        
        i {
          color: var(--primary, #1890ff);
        }
      }
    }
  }
}

// 暗黑模式适配
[data-theme="dark"] {
  .page-data-settings-sider {
    background-color: var(--backgroundDark, #141414);
    border-right-color: var(--borderColorDark, #303030);
    
    .settings-menu {
      &-item {
        color: var(--text-color-dark, rgba(255, 255, 255, 0.85));
        
        i {
          color: var(--icon-color-dark, rgba(255, 255, 255, 0.45));
        }
        
        &:hover {
          background-color: var(--hover-bg-dark, #111b26);
          color: var(--primary-dark, #177ddc);
          
          i {
            color: var(--primary-dark, #177ddc);
          }
        }
        
        &.active {
          background-color: var(--primary-bg-dark, #111b26);
          color: var(--primary-dark, #177ddc);
          border-right-color: var(--primary-dark, #177ddc);
          
          i {
            color: var(--primary-dark, #177ddc);
          }
        }
      }
    }
  }
} 