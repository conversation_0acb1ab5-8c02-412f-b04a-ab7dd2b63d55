#!/bin/zsh

# 配置信息
FTP_SERVER="www.javaddm.fjpipixia.com"
FTP_USER="javaddm"
FTP_PASS="${FTPJavaddm_TEST_PWD}"
REMOTE_DIR="./index/ddm"
LOCAL_DIR="./ddm" # 假设 npm build 生成在 dist 目录

if [ -z "$FTPddm_TEST_PWD" ]; then
    echo "错误: 未设置 FTPJavaddm_TEST_PWD 环境变量"
    # echo 'export FTPJavaddm_TEST_PWD="123456"' >> ~/.zshrc
    exit 1
fi

# 执行构建
npm run build || { echo "Build failed"; exit 1; }

# 移动 dist 目录下的fonts到css目录下  无效
mv ddm/fonts ddm/css/fonts

# 通过 LFTP 同步目录
lftp -e "
  set ssl:verify-certificate no
  open ftp://$FTP_SERVER
  user $FTP_USER $FTP_PASS
  mirror --reverse --delete --verbose $LOCAL_DIR $REMOTE_DIR
  bye
"