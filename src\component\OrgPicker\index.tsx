import React, {
  useState,
  useEffect,
  useCallback,
  forwardRef,
  useImperativeHandle,
  useRef
} from 'react';
import Avatar from '../ProcessAvatar';
import classNames from 'classnames';
import './index.scss';
import {getUserTree, getDepartmentTree, getRoleTree} from '@/utils/api/api';
import {Button} from 'amis-ui';
interface OrgItem {
  id: string;
  name: string;
  type: 'user' | 'dept' | 'role';
  parentId?: string;
  selected?: boolean;
  children?: OrgItem[];
  avatar?: string;
  nickname?: string;
  [key: string]: any;
}

interface OrgPickerProps {
  title?: string;
  multiple?: boolean;
  value?: OrgItem[];
  onChange?: (value: OrgItem[]) => void;
  onCancel?: () => void;
  show?: boolean;
  pcMode?: boolean;
  type?: 'user' | 'dept' | 'role' | 'org';
  selected?: OrgItem[];
  onOk?: (users: OrgItem[]) => void;
}

const OrgPicker = forwardRef<any, OrgPickerProps>((props, ref) => {
  const {
    title = '请选择',
    multiple = false,
    value = [],
    onChange,
    onCancel,
    show = false,
    pcMode = true,
    type = 'user',
    selected = [],
    onOk
  } = props;

  const [visible, setVisible] = useState(show);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [deptStack, setDeptStack] = useState<
    {id: string | null; name: string}[]
  >([]);
  const [nowDeptId, setNowDeptId] = useState<string | null>(null);
  const [deptList, setDeptList] = useState<OrgItem[]>([]);
  const [nodes, setNodes] = useState<OrgItem[]>([]);
  const [searchUsers, setSearchUsers] = useState<OrgItem[]>([]);
  const select = useRef<OrgItem[]>(selected);
  const [checkAll, setCheckAll] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => setVisible(false),
    show: () => setVisible(true),
    getSelected: () => select
  }));

  useEffect(() => {
    setVisible(show);
  }, [show]);

  useEffect(() => {
    if (visible) {
      console.log('select', select);
      console.log('type', type);
      init();
      getOrgList();
    }
    // eslint-disable-next-line
  }, [visible, type]);

  const init = () => {
    setCheckAll(false);
    setNowDeptId(null);
    setNodes([]);
    setDeptStack([]);
    select.current = selected || [];
  };

  const getOrgList = useCallback(() => {
    setLoading(true);
    if (type === 'role') {
      getRoleTree({})
        .then((rsp: any) => {
          console.log('rsp1', rsp);
          setLoading(false);
          const list = rsp.data?.list || [];
          list.forEach((node: any) => (node.type = 'role'));
          setNodes(list);
        })
        .catch(() => setLoading(false));
    } else {
      getDepartmentTree({})
        .then((rsp: any) => {
          console.log('rsp', rsp);
          setLoading(false);
          const depts = rsp.data || [];
          depts.forEach((node: any) => (node.type = 'dept'));
          setDeptList(depts);
          showDeptByParentId(depts, 0);
        })
        .catch(() => setLoading(false));
    }
    // eslint-disable-next-line
  }, [type]);

  const showDeptByParentId = (depts: OrgItem[], parentId: string | number) => {
    let deptNodes = depts
      .filter(dept => dept.parentId === parentId)
      .map(dept => ({
        ...dept,
        type: 'dept',
        selected: false,
        hasChildren: depts.some(d => d.parentId === dept.id)
      }))
      .sort((a, b) => ((a as any).sort || 0) - ((b as any).sort || 0));
    setNodes(deptNodes as OrgItem[]);
    selectToLeft(deptNodes as OrgItem[], select.current);

    if (deptNodes.length === 0) {
      getUserTree({deptId: nowDeptId}).then((rsp: any) => {
        const userList = rsp.data?.list || [];
        userList.forEach((node: any) => {
          node.name = node.nickname || node.name;
          node.type = 'user';
        });
        setNodes(userList);
        selectToLeft(userList, select.current);
      });
    }
  };

  const selectToLeft = (nodesList: OrgItem[], selectList: OrgItem[]) => {
    console.log('nodesList', nodesList);
    console.log('selectList', selectList);
    nodesList.forEach(node => {
      node.selected = selectList.some(
        sel => sel.id === node.id && sel.type === node.type
      );
    });
    setNodes([...nodesList]);
  };

  const searchUser = useCallback(
    (keyword: string) => {
      setSearchUsers([]);
      setLoading(true);
      // 只有有内容时才传 username，否则不传
      if (type === 'user') {
        const params: any = {};
        if (keyword.trim()) params.username = keyword.trim();
        getUserTree(params)
          .then((rsp: any) => {
            setLoading(false);
            const list = rsp.data?.list || [];
            list.forEach((node: any) => {
              node.name = node.nickname || node.name;
              node.type = 'user';
            });
            setSearchUsers(list);
            selectToLeft(list, select.current);
          })
          .catch(() => setLoading(false));
      } else if (type === 'dept' || type === 'org') {
        const params: any = {};
        if (keyword.trim()) params.name = keyword.trim();
        getDepartmentTree(params)
          .then((rsp: any) => {
            setLoading(false);
            const list = rsp.data || [];
            list.forEach((node: any) => (node.type = 'dept'));
            setSearchUsers(list);
            selectToLeft(list, select.current);
          })
          .catch(() => setLoading(false));
      } else if (type === 'role') {
        const params: any = {};
        if (keyword.trim()) params.name = keyword.trim();
        getRoleTree(params)
          .then((rsp: any) => {
            setLoading(false);
            const list = rsp.data?.list || [];
            list.forEach((node: any) => (node.type = 'role'));
            setSearchUsers(list);
            selectToLeft(list, select.current);
          })
          .catch(() => setLoading(false));
      }
      // eslint-disable-next-line
    },
    [type, select]
  );

  // 输入框 onChange 事件
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.trim() === '') {
      setSearch('');
      getOrgList();
    } else {
      setSearch(value);
      searchUser(value);
    }
  };

  // 清空输入框
  const handleClearInput = () => {
    setSearch('');
    getOrgList(); // 恢复部门列表
  };

  const selectChange = (node: OrgItem) => {
    let newSelect = [...select.current];
    if (node.selected) {
      setCheckAll(false);
      newSelect = newSelect.filter(
        sel => !(sel.id === node.id && sel.type === node.type)
      );
      node.selected = false;
    } else if (!disableDept(node)) {
      node.selected = true;
      if (!multiple) {
        newSelect = [node];
        nodes.forEach(nd => {
          if (node.id !== nd.id) nd.selected = false;
        });
      } else {
        if (node.type === 'dept') {
          newSelect.unshift(node);
        } else {
          newSelect.push(node);
        }
      }
    }
    select.current = newSelect;
    selectToLeft(nodes, newSelect);
    onChange?.(newSelect);
  };

  const disableDept = (node: OrgItem) =>
    type === 'user' && node.type === 'dept';

  const handleCheckAllChange = () => {
    let newSelect = [...select.current];
    nodes.forEach(node => {
      if (checkAll) {
        node.selected = false;
        newSelect = newSelect.filter(
          sel => !(sel.id === node.id && sel.type === node.type)
        );
      } else if (!node.selected && !disableDept(node)) {
        node.selected = true;
        newSelect.push(node);
      }
    });
    setCheckAll(!checkAll);
    select.current = newSelect;
    selectToLeft(nodes, newSelect);
    onChange?.(newSelect);
  };

  const nextNode = (dept: OrgItem) => {
    if (dept.selected) return;
    setDeptStack([...deptStack, {id: nowDeptId, name: dept.name}]);
    setNowDeptId(dept.id);
    showDeptByParentId(deptList, dept.id);
  };

  const beforeNode = () => {
    if (deptStack.length === 0) return;
    const parentDept = deptStack[deptStack.length - 1];
    setDeptStack(deptStack.slice(0, -1));
    setNowDeptId(parentDept.id);
    showDeptByParentId(deptList, parentDept.id || 0);
  };

  const clearSelected = () => {
    setCheckAll(false);
    select.current = [];
    nodes.forEach(nd => (nd.selected = false));
    setNodes([...nodes]);
    onChange?.([]);
  };

  const noSelected = (index: number) => {
    const removeItem = select.current[index];
    nodes.forEach(nd => {
      if (nd.id === removeItem.id) nd.selected = false;
    });
    select.current = select.current.filter((_, i) => i !== index);
    setCheckAll(false);
    onChange?.(select.current.filter((_, i) => i !== index));
  };

  const handleOk = () => {
    if (!select || select.current.length === 0) {
      alert('选择的项为空');
      return;
    }
    onOk?.([...select.current]);
    setVisible(false);
    setSearchUsers([]);
    setSearch('');
    select.current = [];
    nodes.forEach(nd => (nd.selected = false));
    setNodes([...nodes]);
  };

  const handleCancel = () => {
    setVisible(false);
    select.current = [];
    nodes.forEach(nd => (nd.selected = false));
    setNodes([...nodes]);
    onCancel?.();
  };

  const deptStackStr = deptStack
    .map(v => v.name)
    .filter(Boolean)
    .join(' > ');
  const orgs = search.trim() === '' ? nodes : searchUsers;

  if (!visible) return null;
  return (
    <div className="org-picker-modal">
      <div className="modal-mask" onClick={handleCancel}></div>
      <div className="modal-content">
        <div className="modal-header">{title}</div>
        <div className="picker">
          <div className="candidate">
            <div className="search-container">
              <input
                value={search}
                onChange={handleInputChange}
                placeholder={
                  type === 'role'
                    ? '搜索角色，支持拼音、姓名'
                    : type === 'dept'
                    ? '搜索部门，支持拼音、姓名'
                    : '搜索人员，支持拼音、姓名'
                }
                className="search-input"
              />
              {search && (
                <span
                  className="search-clear"
                  onClick={handleClearInput}
                  title="清空"
                >
                  ×
                </span>
              )}
              <div className="dept-path">
                <span>
                  <i className="fa fa-building" style={{marginRight: 4}} />
                  {deptStackStr}
                </span>
                <span className="top-dept" onClick={beforeNode}>
                  {deptStack.length > 0 ? '上一级' : ''}
                </span>
              </div>
              <div className="selection-controls">
                <label>
                  <input
                    type="checkbox"
                    checked={checkAll}
                    onChange={handleCheckAllChange}
                    disabled={!multiple}
                  />{' '}
                  全选
                </label>
                <span style={{marginLeft: 10, color: '#8c8c8c'}}>
                  已选 [{select.current.length}]
                </span>
                {select.current.length > 0 && multiple && (
                  <span className="clear-btn" onClick={clearSelected}>
                    清空
                  </span>
                )}
              </div>
            </div>
            <div className="org-items">
              {loading ? (
                <div className="loading-container">
                  <span>加载中...</span>
                </div>
              ) : orgs.length === 0 ? (
                <div className="empty-data">似乎没有数据</div>
              ) : (
                orgs.map((org, index) => (
                  <div
                    key={org.id}
                    className={classNames('org-item', {
                      'org-dept-item': org.type === 'dept',
                      'org-user-item': org.type === 'user',
                      'org-role-item': org.type === 'role',
                      'selected': org.selected
                    })}
                    onClick={() => selectChange(org)}
                  >
                    <input
                      type="checkbox"
                      checked={!!org.selected}
                      disabled={disableDept(org)}
                      style={{marginRight: 10}}
                      readOnly
                    />
                    {org.type === 'dept' ? (
                      <>
                        <i className="fa fa-folder-open" />
                        <span className="name">{org.name}</span>
                        <span
                          className={classNames('next-dept', {
                            'next-dept-disable': org.selected
                          })}
                          onClick={e => {
                            e.stopPropagation();
                            nextNode(org);
                          }}
                        >
                          <i className="fa fa-sitemap" /> 下级
                        </span>
                      </>
                    ) : org.type === 'user' ? (
                      <div style={{display: 'flex', alignItems: 'center'}}>
                        <Avatar size={35} name={org.name} src={org.avatar} />
                      </div>
                    ) : (
                      <div style={{display: 'flex', alignItems: 'center'}}>
                        <i className="fa fa-users" />
                        <span className="name" style={{marginLeft: 8}}>
                          {org.name}
                        </span>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
          <div className="selected-panel">
            <div className="count-header">
              <span>已选 {select.current.length} 项</span>
              <span className="clear-btn" onClick={clearSelected}>
                清空
              </span>
            </div>
            <div className="selected-items">
              {select.current.length === 0 ? (
                <div className="empty-data">请点击左侧列表选择数据</div>
              ) : (
                select.current.map((org, index) => (
                  <div
                    key={org.id}
                    className={classNames('selected-item', {
                      'org-dept-item': org.type === 'dept',
                      'org-user-item': org.type === 'user',
                      'org-role-item': org.type === 'role'
                    })}
                  >
                    {org.type === 'dept' ? (
                      <>
                        <i className="fa fa-folder-open" />
                        <span className="name">{org.name}</span>
                      </>
                    ) : org.type === 'user' ? (
                      <div style={{display: 'flex', alignItems: 'center'}}>
                        <Avatar size={35} name={org.name} src={org.avatar} />
                      </div>
                    ) : (
                      <div style={{display: 'flex', alignItems: 'center'}}>
                        <i className="fa fa-users" />
                        <span className="name" style={{marginLeft: 8}}>
                          {org.name}
                        </span>
                      </div>
                    )}
                    <i
                      className="fa fa-close remove-icon"
                      onClick={() => noSelected(index)}
                    />
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <Button className="cancel-btn" onClick={handleCancel}>
            取消
          </Button>
          <Button level="primary" className="confirm-btn" onClick={handleOk}>
            确定
          </Button>
        </div>
      </div>
    </div>
  );
});

export default OrgPicker;
