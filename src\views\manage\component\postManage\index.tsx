import React, {FC, useState} from 'react';
import {observer} from 'mobx-react';
import AMISRenderer from '@/component/AMISRenderer';

import './index.scss';

const PostManage: FC<any> = () => {
  const [amisKey, setAmisKey] = useState(0);

  // 岗位列表Schema
  const postManageSchema = {
    type: 'page',
    body: [
      {
        type: 'crud',
        autoGenerateFilter: {
          columnsNum: 2,
          showBtnToolbar: false
        },
        id: 'post-crud',
        api: {
          method: 'GET',
          url: '/admin-api/system/post/page',
          data: {
            name: '${name}',
            code: '${code}',
            status: '${status}',
            pageNo: '${page}',
            pageSize: '${perPage}'
          },
          // 过滤掉没有值的参数
          requestAdaptor: function (api: any) {
            const data = {...api.data};
            Object.keys(data).forEach(key => {
              if (
                data[key] === undefined ||
                data[key] === '' ||
                data[key] === null
              ) {
                delete data[key];
              }
            });
            api.data = data;
            return api;
          },
          adaptor: (payload: any) => {
            if (payload.code === 0 && payload.data) {
              return {
                total: payload.data.total,
                items: payload.data.list
              };
            }
            return {
              total: 0,
              items: []
            };
          }
        },
        headerToolbar: [
          {
            type: 'button',
            label: '新增岗位',
            icon: 'fa fa-plus',
            level: 'primary',
            actionType: 'drawer',
            drawer: {
              title: '新增岗位',
              size: 'md',
              position: 'right',
              body: {
                type: 'form',
                api: {
                  url: '/admin-api/system/post/create',
                  method: 'post',
                  adaptor: `
  const event = new CustomEvent('tenantSwitch',{});
window.dispatchEvent(event);
  return payload;
`
                },
                body: [
                  {
                    type: 'input-text',
                    name: 'name',
                    label: '岗位名称',
                    required: true,
                    maxLength: 30,
                    placeholder: '请输入岗位名称'
                  },
                  {
                    type: 'input-text',
                    name: 'code',
                    label: '岗位编码',
                    required: true,
                    maxLength: 30,
                    placeholder: '请输入岗位编码'
                  },
                  {
                    type: 'input-number',
                    name: 'sort',
                    label: '岗位排序',
                    value: 0,
                    placeholder: '请输入排序值'
                  },
                  {
                    type: 'textarea',
                    name: 'remark',
                    label: '岗位备注',
                    required: true,
                    maxLength: 200,
                    placeholder: '请输入岗位备注'
                  },
                  {
                    type: 'switch',
                    name: 'status',
                    label: '状态',
                    value: 0,
                    trueValue: 0,
                    falseValue: 1
                  }
                ]
              }
            }
          }
        ],
        columns: [
          {
            name: 'id',
            label: '序号',
            width: 80,
            type: 'tpl',
            tpl: '${(page - 1) * perPage + index + 1}'
          },
          {
            name: 'id',
            label: '岗位编号',
            width: 100,
            type: 'tpl',
            tpl: '${id}'
          },
          {
            name: 'name',
            label: '岗位名称',
            width: 150,
            searchable: {
              type: 'input-text',
              name: 'name',
              label: '岗位名称',
              placeholder: '输入岗位名称'
            }
          },
          {
            type: 'tpl',
            tpl: '${code || "-"}',
            name: 'code',
            label: '岗位编码',
            width: 120
          },
          {
            name: 'remark',
            label: '岗位备注',
            width: 150,
            type: 'tpl',
            tpl: '${remark || "-"}'
          },
          {
            name: 'sort',
            label: '岗位排序',
            width: 100,
            type: 'tpl',
            tpl: '${sort || 0}'
          },
          {
            name: 'status',
            label: '状态',
            type: 'mapping',
            width: 100,
            map: {
              '0': '<span class="badge-status badge-success">已启用</span>',
              '1': '<span class="badge-status badge-danger">已停用</span>'
            },
            searchable: {
              type: 'select',
              name: 'status',
              label: '状态',
              placeholder: '请选择状态',
              options: [
                {label: '已启用', value: 0},
                {label: '已停用', value: 1}
              ]
            }
          },
          {
            name: 'updater',
            label: '编辑人',
            width: 100
          },
          {
            name: 'updateTime',
            label: '编辑时间',
            type: 'input-datetime',
            static: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            width: 160
          },
          {
            name: 'creator',
            label: '创建人',
            width: 100
          },
          {
            name: 'createTime',
            label: '创建时间',
            type: 'input-datetime',
            static: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            width: 160
          },
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                type: 'button',
                label: '查看',
                icon: 'fa fa-eye',
                level: 'link',
                actionType: 'drawer',
                drawer: {
                  title: '查看岗位详情',
                  size: 'md',
                  position: 'right',
                  body: {
                    type: 'form',
                    mode: 'horizontal',
                    static: true,
                    body: [
                      {
                        type: 'input-text',
                        name: 'id',
                        label: '岗位编号'
                      },
                      {
                        type: 'input-text',
                        name: 'name',
                        label: '岗位名称'
                      },
                      {
                        type: 'input-text',
                        name: 'code',
                        label: '岗位编码'
                      },
                      {
                        type: 'textarea',
                        name: 'remark',
                        label: '岗位备注'
                      },
                      {
                        type: 'input-text',
                        name: 'sort',
                        label: '岗位排序'
                      },
                      {
                        type: 'input-text',
                        name: 'updater',
                        label: '编辑人'
                      },
                      {
                        type: 'input-datetime',
                        name: 'updateTime',
                        label: '编辑时间',
                        static: true,
                        format: 'YYYY-MM-DD HH:mm:ss'
                      },
                      {
                        type: 'input-text',
                        name: 'creator',
                        label: '创建人'
                      },
                      {
                        type: 'input-datetime',
                        name: 'createTime',
                        label: '创建时间',
                        static: true,
                        format: 'YYYY-MM-DD HH:mm:ss'
                      },
                      {
                        type: 'mapping',
                        name: 'status',
                        label: '状态',
                        map: {
                          '0': '已启用',
                          '1': '已停用'
                        }
                      }
                    ]
                  }
                }
              },
              {
                type: 'button',
                label: '编辑',
                icon: 'fa fa-edit',
                level: 'link',
                actionType: 'drawer',
                drawer: {
                  title: '编辑岗位',
                  size: 'md',
                  position: 'right',
                  body: {
                    type: 'form',
                    api: {
                      url: '/admin-api/system/post/update',
                      method: 'put'
                    },
                    body: [
                      {
                        type: 'hidden',
                        name: 'id'
                      },
                      {
                        type: 'input-text',
                        name: 'name',
                        label: '岗位名称',
                        required: true,
                        maxLength: 30,
                        placeholder: '请输入岗位名称'
                      },
                      {
                        type: 'input-text',
                        name: 'code',
                        label: '岗位编码',
                        required: true,
                        maxLength: 30,
                        placeholder: '请输入岗位编码'
                      },
                      {
                        type: 'input-number',
                        name: 'sort',
                        label: '岗位排序',
                        placeholder: '请输入排序值'
                      },
                      {
                        type: 'textarea',
                        name: 'remark',
                        label: '岗位备注',
                        required: true,
                        maxLength: 200,
                        placeholder: '请输入岗位备注'
                      },
                      {
                        type: 'switch',
                        name: 'status',
                        label: '状态',
                        trueValue: 0,
                        falseValue: 1
                      }
                    ]
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                icon: 'fa fa-trash',
                level: 'link',
                className: 'text-danger',
                confirmTitle: '确定要删除吗？',
                confirmText: '删除后不可恢复，确定要删除该岗位吗？',
                actionType: 'ajax',
                api: {
                  method: 'DELETE',
                  url: '/admin-api/system/post/delete?id=${id}'
                }
              }
            ]
          }
        ],
        bulkActions: [],
        itemActions: [],
        footerToolbar: ['statistics', 'pagination', 'switch-per-page'],
        syncLocation: false,
        affixHeader: true,
        initFetch: true,
        className: 'user-crud-table'
      }
    ]
  };

  return (
    <div className="post-manage-container">
      <div className="page-header">
        <div className="title">岗位管理</div>
        <div className="subtitle">组织岗位管理与维护</div>
      </div>

      <div className="post-content">
        <AMISRenderer
          key={amisKey}
          schema={postManageSchema}
          onAction={(type: string, data: any) => {
            console.log(type, data);
          }}
        />
      </div>
    </div>
  );
};

export default observer(PostManage);
