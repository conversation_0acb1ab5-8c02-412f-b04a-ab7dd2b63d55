import React, {FC, useState} from 'react';
import {observer} from 'mobx-react';
import AMISRenderer from '@/component/AMISRenderer';

import './index.scss';

const DeptManage: FC<any> = () => {
  const [amisKey, setAmisKey] = useState(0);

  // 部门列表Schema
  const deptManageSchema = {
    type: 'page',
    body: [
      {
        type: 'crud',
        autoGenerateFilter: {
          columnsNum: 2,
          showBtnToolbar: false
        },
        id: 'dept-crud',
        api: {
          method: 'GET',
          url: '/admin-api/system/dept/list-all-nested',
          data: {
            name: '${name}',
            status: '${status}'
          },
          // 过滤掉没有值的参数
          requestAdaptor: function (api: any) {
            const data = {...api.data};
            Object.keys(data).forEach(key => {
              if (
                data[key] === undefined ||
                data[key] === '' ||
                data[key] === null
              ) {
                delete data[key];
              }
            });
            api.data = data;
            return api;
          },
          // adaptor: (payload: any) => {
          //   if (payload.code === 0 && payload.data) {
          //     // 递归处理部门数据，为每个部门添加 memberCount
          //     const processDepts = (depts: any[]): any[] => {
          //       return depts.map(dept => {
          //         const processedDept = {
          //           ...dept,
          //           // 处理部门主管
          //           memberCount: dept.children ? dept.children.length : 0
          //         };

          //         // 如果有子部门，递归处理子部门
          //         if (dept.children && dept.children.length > 0) {
          //           processedDept.children = processDepts(dept.children);
          //         }

          //         return processedDept;
          //       });
          //     };

          //     const processedData = processDepts(payload.data);
          //     return {
          //       total: processedData.length,
          //       items: processedData
          //     };
          //   }
          //   return {
          //     total: 0,
          //     items: []
          //   };
          // }
        },
        headerToolbar: [
          {
            type: 'button',
            label: '新增部门',
            icon: 'fa fa-plus',
            level: 'primary',
            actionType: 'drawer',
            drawer: {
              title: '新增部门',
              size: 'md',
              position: 'right',
              body: {
                type: 'form',
                api: {
                  url: '/admin-api/system/dept/create',
                  method: 'post',
                  adaptor: `
  const event = new CustomEvent('tenantSwitch',{});
window.dispatchEvent(event);
  return payload;
`
                },
                body: [
                  {
                    type: 'department-select',
                    name: 'parentId',
                    label: '上级部门',
                    labelField: 'name',
                    valueField: 'id',
                    placeholder: '请选择'
                  },
                  {
                    type: 'input-text',
                    name: 'name',
                    label: '部门名称',
                    required: true,
                    maxLength: 30,
                    placeholder: '请输入'
                  },
                  {
                    type: 'input-number',
                    name: 'sort',
                    label: '部门排序',
                    value: 0,
                    placeholder: '请输入'
                  },
                  {
                    type: 'user-select',
                    name: 'leaderUserId',
                    label: '部门主管',
                    placeholder: '请选择',
                    labelField: 'name',
                    valueField: 'id'
                  },
                  {
                    type: 'switch',
                    name: 'status',
                    label: '部门状态',
                    value: 0,
                    trueValue: 0,
                    falseValue: 1
                  }
                ]
              }
            }
          }
        ],
        columns: [
          {
            name: 'name',
            label: '部门名称',
            width: 150,
            searchable: {
              type: 'input-text',
              name: 'name',
              label: '部门名称',
              placeholder: '输入部门名称'
            }
          },
          {
            type: 'user-select',
            static: true,
            labelField: 'name',
            valueField: 'id',
            name: 'leaderUserId',
            label: '部门主管',
            width: 120
          },
          {
            name: 'userCount',
            label: '成员数',
            width: 100,
            type: 'tpl',
            tpl: '${userCount || 0}'
          },
          {
            name: 'status',
            label: '状态',
            type: 'mapping',
            width: 100,
            map: {
              '0': '<span class="badge-status badge-success">已启用</span>',
              '1': '<span class="badge-status badge-danger">已停用</span>'
            },
            searchable: {
              type: 'select',
              name: 'status',
              label: '状态',
              placeholder: '请选择状态',
              options: [
                {label: '已启用', value: 0},
                {label: '已停用', value: 1}
              ]
            }
          },
          {
            name: 'updater',
            label: '编辑人',
            width: 100
          },
          {
            name: 'updateTime',
            label: '最近编辑时间',
            type: 'input-datetime',
            static: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            width: 160
          },
          {
            name: 'creator',
            label: '创建人',
            width: 100
          },
          {
            name: 'createTime',
            label: '创建时间',
            type: 'input-datetime',
            static: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            width: 160
          },
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                type: 'button',
                label: '查看',
                icon: 'fa fa-eye',
                level: 'link',
                actionType: 'drawer',
                drawer: {
                  title: '查看部门详情',
                  size: 'md',
                  position: 'right',
                  body: {
                    type: 'form',
                    mode: 'horizontal',
                    static: true,
                    body: [
                      {
                        type: 'input-text',
                        name: 'name',
                        label: '部门名称'
                      },
                      {
                        type: 'user-select',
                        labelField: 'name',
                        valueField: 'id',
                        name: 'leaderUserId',
                        label: '部门主管'
                      },
                      {
                        type: 'input-text',
                        name: 'userCount',
                        label: '成员数'
                      },
                      {
                        type: 'input-text',
                        name: 'updater',
                        label: '编辑人'
                      },
                      {
                        type: 'input-datetime',
                        name: 'updateTime',
                        label: '最近编辑时间',
                        static: true,
                        format: 'YYYY-MM-DD HH:mm:ss'
                      },
                      {
                        type: 'input-text',
                        name: 'creator',
                        label: '创建人'
                      },
                      {
                        type: 'input-datetime',
                        name: 'createTime',
                        label: '创建时间',
                        static: true,
                        format: 'YYYY-MM-DD HH:mm:ss'
                      },
                      {
                        type: 'mapping',
                        name: 'status',
                        label: '状态',
                        map: {
                          '0': '已启用',
                          '1': '已停用'
                        }
                      }
                    ]
                  }
                }
              },
              {
                type: 'button',
                label: '编辑',
                icon: 'fa fa-edit',
                level: 'link',
                actionType: 'drawer',
                drawer: {
                  title: '编辑部门',
                  size: 'md',
                  position: 'right',
                  body: {
                    type: 'form',
                    api: {
                      url: '/admin-api/system/dept/update',
                      method: 'put'
                    },
                    body: [
                      {
                        type: 'hidden',
                        name: 'id'
                      },
                      {
                        type: 'department-select',
                        name: 'parentId',
                        label: '上级部门',
                        onlyLeaf: false,
                        labelField: 'name',
                        valueField: 'id',
                        placeholder: '请选择'
                      },
                      {
                        type: 'input-text',
                        name: 'name',
                        label: '部门名称',
                        required: true,
                        maxLength: 30,
                        placeholder: '请输入'
                      },
                      {
                        type: 'input-number',
                        name: 'sort',
                        label: '部门排序',
                        placeholder: '请输入'
                      },
                      {
                        type: 'user-select',
                        name: 'leaderUserId',
                        label: '部门主管',
                        placeholder: '请选择',
                        labelField: 'name',
                        valueField: 'id'
                      },
                      {
                        type: 'switch',
                        name: 'status',
                        label: '部门状态',
                        trueValue: 0,
                        falseValue: 1
                      }
                    ]
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                icon: 'fa fa-trash',
                level: 'link',
                className: 'text-danger',
                confirmTitle: '确定要删除吗？',
                confirmText: '删除后不可恢复，确定要删除该部门吗？',
                actionType: 'ajax',
                api: {
                  method: 'DELETE',
                  url: '/admin-api/system/dept/delete?id=${id}'
                }
              }
            ]
          }
        ],
        bulkActions: [],
        itemActions: [],
        footerToolbar: ['statistics', 'pagination', 'switch-per-page'],
        syncLocation: false,
        affixHeader: true,
        initFetch: true,
        className: 'user-crud-table'
      }
    ]
  };

  return (
    <div className="dept-manage-container">
      <div className="page-header">
        <div className="title">部门管理</div>
        <div className="subtitle">组织部门管理与维护</div>
      </div>

      <div className="dept-content">
        <AMISRenderer
          key={amisKey}
          schema={deptManageSchema}
          onAction={(type: string, data: any) => {
            console.log(type, data);
          }}
        />
      </div>
    </div>
  );
};

export default observer(DeptManage);
