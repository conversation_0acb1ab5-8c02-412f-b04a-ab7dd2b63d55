.bind-wx-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .bind-wx-container {
    box-shadow: var(--boxShadow);
    width: 400px;
    padding-top: 60px;
    border-radius: 10px;
    text-align: center;

    .bind-wx-header {
      margin-bottom: 60px;
      .wx-logo {
        width: 48px;
        height: 48px;
        margin-bottom: 10px;
      }

      .bind-title {
        font-size: 20px;
        color: var(--text-color);
        margin-bottom: 4px;
        font-weight: bold;
      }

      .bind-subtitle {
        font-size: 16px;
        color: var(--text--muted-color);
      }
    }

    .bind-container {
      display: flex;
      justify-content: center;
      width: 154px;
      height: 154px;
      margin: 0 auto;

      &-code {
        display: flex;
        width: 154px !important;
        height: 154px !important;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        iframe {
          display: block;
          margin: 0 auto;
          width: 154px !important;
          height: 154px !important;
          border: none;
        }
      }
    }

    .bind-tips {
      margin-top: 24px;
      margin-bottom: 60px;
      p {
        color: var(--text-color);
        font-size: 14px;
      }
    }
  }
}
