import { types, getEnv, applySnapshot, getSnapshot } from 'mobx-state-tree';
import { PageStore } from './Page';
import { when, reaction } from 'mobx';

export const MainStore = types.model('MainStore', {
    pages: types.optional(types.array(PageStore), [
      {
        id: 1,
        path: 'hello-world',
        label: 'Hello world',
        icon: 'fa fa-file',
        schema: {
          "type": "page",
          "id": "u:e0f3ccc4ef3a",
          "asideResizor": false,
          "pullRefresh": {
            "disabled": true
          }
        }
      }
    ]),
    theme: 'cxd',
    asideFixed: true,
    asideFolded: false,
    offScreen: false,
    addPageIsOpen: false,
    preview: false,
    isMobile: false,
    schema: types.frozen(),
    access_token: '',
    userInfo: types.frozen(),
    apply_id:'',
    apply_name: types.optional(types.string, ''),
    apply_logo: types.optional(types.string, ''),
    refresh_token: '',
    tenant_id: types.frozen(),
    last_tenant_id: types.frozen(),
    current_page_data: types.frozen()
  }).views(self => ({
    get fetcher() {
      return getEnv(self).fetcher;
    },
    get notify() {
      return getEnv(self).notify;
    },
    get alert() {
      return getEnv(self).alert;
    },
    get copy() {
      return getEnv(self).copy;
    }
  })).actions(self => {
    function toggleAsideFolded() {
      self.asideFolded = !self.asideFolded;
    }

    function toggleAsideFixed() {
      self.asideFixed = !self.asideFixed;
    }

    function toggleOffScreen() {
      self.offScreen = !self.offScreen;
    }

    function setAddPageIsOpen(isOpened: boolean) {
      self.addPageIsOpen = isOpened;
    }

    function addPage(data: {
      id: string | number;
      label: string;
      path: string;
      icon?: string;
      schema?: any;
      table_name?: string;
      create_user?: string;
      type?: number | string;
      parent_id?: number | string;
    }) {
      self.pages.push(
        PageStore.create(data)
      );
    }

    function removePageAt(index: number) {
      self.pages.splice(index, 1);
    }

    function updatePageSchemaVal(index: number,schema: any) {
      self.pages[index].updateSchema(schema);
    }

    function updatePageSchemaAt(index: number) {
      self.pages[index].updateSchema(self.schema);
    }

    function updateSchema(value: any) {
      self.schema = value;
    }

    function setPreview(value: boolean) {
      self.preview = value;
    }

    function setIsMobile(value: boolean) {
      self.isMobile = value;
    }

    function setAccessToken(value: string) {
      self.access_token = value;
    }

    function setUserInfo(value: any) {
      self.userInfo = value;
    }
    
    function setApplyName(value: string) {
      self.apply_name = value;
    }

    function setApplyLogo(value: string) {
      self.apply_logo = value;
    }
    
    function setCurrentPageData(value: any) {
      self.current_page_data = value;
    }

    function setRefreshToken(value: string) {
      self.refresh_token = value;
    }

    function setTenantId(value: any) {
      self.tenant_id = value;
    }

    function setLastTenantId(value: any) {
      self.last_tenant_id = value;
    }

    function updateTheme(value: string) {
      self.theme = value;
    }

    return {
      toggleAsideFolded,
      toggleAsideFixed,
      toggleOffScreen,
      setAddPageIsOpen,
      addPage,
      removePageAt,
      updatePageSchemaVal,
      updatePageSchemaAt,
      updateSchema,
      setPreview,
      setIsMobile,
      setUserInfo,
      setAccessToken,
      setRefreshToken,
      setApplyName,
      setApplyLogo,
      setCurrentPageData,
      setTenantId,
      setLastTenantId,
      updateTheme,
      afterCreate() {
        // persist store
        if (typeof window !== 'undefined' && window.localStorage) {
          const storeData = window.localStorage.getItem('store');
          if (!storeData ) window.localStorage.setItem('store', JSON.stringify(getSnapshot(self)));

          if (storeData ) applySnapshot(self, JSON.parse(storeData));

          reaction(
            () => getSnapshot(self),
            json => {
              window.localStorage.setItem('store', JSON.stringify(json));
            }
          );
        }
      }
    };
  });

export type IMainStore = typeof MainStore.Type;
