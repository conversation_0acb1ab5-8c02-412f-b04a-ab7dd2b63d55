import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class InputVerificationCodePlugin extends BasePlugin {
  static id = 'InputVerificationCodePlugin';
  static scene = ['form'];

  // 关联渲染器名字
  rendererName = 'input-verification-code';
  $schema = '/schemas/VerificationCodeSchema.json';

  // 组件基本信息
  name = '验证码输入框';
  panelTitle = '验证码输入框';
  icon = 'fa fa-keyboard-o';
  panelIcon = 'fa fa-keyboard-o';
  pluginIcon = 'fa fa-keyboard-o';
  isBaseComponent = true;
  panelJustify = true;

  // 组件描述信息
  description = '验证码输入框，用于输入验证码';
  docLink = '/amis/zh-CN/components/form/input-verification-code';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'input-verification-code',
    label: '验证码',
    name: 'code',
    length: 6,
    placeholder: '请输入验证码'
  };

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    wrapWithPanel: false,
    mode: 'horizontal',
    body: [
      {
        ...this.scaffold
      }
    ]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl('collapseGroup', [
          {
            title: '基本',
            body: [
              getSchemaTpl('formItemName', {
                required: true
              }),
              getSchemaTpl('formItemLabel'),
              {
                type: 'input-number',
                name: 'length',
                label: '输入长度',
                value: 6,
                description: '验证码的长度',
                min: 1,
                step: 1
              },
              {
                type: 'switch',
                name: 'masked',
                label: '密码模式',
                value: false,
                description: '是否使用密码模式'
              },
              {
                type: 'input-text',
                name: 'separator',
                label: '分隔符',
                description: '输入框之间的分隔符'
              },
              getSchemaTpl('formItemPlaceholder', {
                placeholder: '请输入验证码'
              }),
              getSchemaTpl('formItemDescription'),
              getSchemaTpl('remark')
            ]
          },
          {
            title: '校验',
            body: [
              getSchemaTpl('formItemValidations'),
              getSchemaTpl('validateOnChange')
            ]
          },
          {
            title: '外观',
            body: [
              getSchemaTpl('formItemClassName'),
              getSchemaTpl('formItemSize'),
              getSchemaTpl('formItemInline'),
              getSchemaTpl('formItemDisabled'),
              getSchemaTpl('formItemVisible')
            ]
          },
          {
            title: '状态',
            body: [
              getSchemaTpl('formItemRequired'),
              getSchemaTpl('formItemReadOnly')
            ]
          }
        ])
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(InputVerificationCodePlugin);
