import React, {FC, useEffect, useState, useRef, RefObject} from 'react';
import {observer, inject} from 'mobx-react';
import {withRouter, RouteComponentProps} from 'react-router-dom';
import AMISRenderer from '@/component/AMISRenderer';
import {toast, Layout, Button, Icon, Avatar, Drawer, Tabs, Tab} from 'amis';
import {IMainStore} from '@/store';
import './index.scss';
import itemShow from '../workbench/component/ProjectMain/image/itemShow.png';
import {getFormDataPage} from '@/utils/api/api';

// 定义路由参数接口
interface RouteParams {
  app: string;
  form: string;
}

// 提交页面组件 - 用于展示提交的表单数据
interface SubmissionPageProps extends RouteComponentProps<RouteParams> {
  store?: IMainStore;
}

const SubmissionPage: FC<SubmissionPageProps> = inject('store')(
  observer((props: SubmissionPageProps) => {
    const {store, location, history} = props;
    const [formData, setFormData] = React.useState<any>({});
    const [pageData, setPageData] = useState<any>(null);

    // 页面id
    const urlParams = new URLSearchParams(location.search);
    const editCode: any = urlParams.get('editCode');
    const pageName: any = urlParams.get('pageName');
    let edit_id = parseInt(editCode, 10);


      // 获取表单数据 formData
  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: edit_id,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      console.log('handleGetFormDataPage res', res);
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            // res.data.list[0].appId = props.computedMatch.params.appId;
            setFormData(res.data.list[0]);
          } else {
            toast.success('暂无表单数据');
          }
        } else {
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

    // 头部导航
    const renderHeader = () => {
      return (
        <div className="submission-header">
          <div className="submission-header-left">
            <div className="submission-header-left-appname">
              <img
                className="submission-header-left-appname-icon"
                src={itemShow}
              />
              <div className="submission-header-left-appname-name">
                {store?.apply_name || '应用'}
              </div>
            </div>
          </div>
          <div className="submission-header-right">
            {store?.userInfo && (
              <div className="submission-header-right-user">
                <Avatar
                  className="submission-header-right-user-avatar"
                  src={store.userInfo?.avatar}
                  icon={!store.userInfo?.avatar ? 'user' : undefined}
                />
              </div>
            )}
          </div>
        </div>
      );
    };

    useEffect(() => {
      if (!pageData) {
        handleGetFormDataPage();
      }
    }, []);

    // 渲染内容
    const renderContent = () => {
      // 表单填写页面
      return (
        <div className="submission-form-box">
          <div className='submission-form-title'>{pageName}</div>
          <div className="submission-form-content">
            {formData ? (
              <AMISRenderer schema={(formData.data)} />
            ) :
            null}
          </div>
        </div>
      );
    };

    // 渲染表单提交页面
    return (
      <Layout className="submission-layout" header={renderHeader()}>
        <div className="submission-page-container">
          {renderContent()}
        </div>
      </Layout>
    );
  })
);

export default withRouter(SubmissionPage);
