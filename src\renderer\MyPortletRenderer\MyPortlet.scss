/* 快捷入口组件样式 */
.Portlet {
  margin-bottom: 1rem;
  background-color: var(--background);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  overflow: hidden;

  /* 标题栏样式 */
  .Portlet-header {
    display: flex;
    justify-content: space-between;
    height: 48px;
    margin: 0 16px;
    align-items: center;

    .Portlet-header-title-area {
      display: flex;
      justify-content: center;
      align-items: center;

      .Portlet-header-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        margin-right: 8px;
      }

      .Portlet-header-titles {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .Portlet-header-main-title {
          font-size: 16px;
          color: var(--text-color);
        }

        .Portlet-header-hint-titles {
          margin-left: 8px;
        }
        .Portlet-header-hint-title {
          font-size: 12px;
          color: var(--text--muted-color);
          margin-left: 8px;
          margin-right: 8px;
        }
      }
    }

    /* 更多文字样式 */
    .Portlet-header-more {
      display: flex;
      align-items: center;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        color: var(--primary);
      }

      span {
        margin-right: 4px;
      }

      i {
        font-size: 12px;
        margin-left: 4px;
      }
    }
  }

  /* 标签页相关样式 */
  .cxd-Portlet-tab,
  .dark-Portlet-tab {
    margin-left: 16px;
    &.hide-tabs {
      .Tabs-links {
        display: none !important;
      }

      .Tabs-content {
        padding-top: 0;
      }
    }

    &.no-divider {
      border-bottom: none;
    }

    .Tabs-content {
      padding: 16px;
    }
  }

  /* 针对标题栏和标签页同时存在的情况 */
  &.with-custom-header {
    .Portlet-tab {
      .Tabs-content {
        padding-top: 16px;
      }
    }
  }
}

/* 快捷入口组件卡片式样式 */
.portal-entrance {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  padding: 8px 0;

  .entrance-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 16px;
    border-radius: 4px;
    background-color: var(--light-bg);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(36, 104, 242, 0.15);
      transform: translateY(-2px);
    }

    .entrance-icon {
      width: 50px;
      height: 50px;
      margin-bottom: 8px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
    }

    .entrance-title {
      font-size: 14px;
      color: var(--text-color);
      margin-top: 8px;
      line-height: 1.4;
    }
  }
}
