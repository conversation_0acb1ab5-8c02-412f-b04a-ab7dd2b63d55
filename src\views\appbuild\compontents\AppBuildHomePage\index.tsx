import React, {useState, useEffect} from 'react';
import './index.scss';
import itemShow from '@/views/workbench/component/ProjectMain/image/itemShow.png';
import AMISRenderer from '@/component/AMISRenderer';
import {recentlyUsedApplicationList} from '@/utils/api/api';
import {Tabs, toast, Avatar, Button} from 'amis';
import {
  utilsSetFromAppbuild
} from '@/utils/routeUtils';
const AppBuildHomePage: React.FC<any> = props => {
  // 默认激活的Tab
  const [activeTab, setActiveTab] = useState('recentlyUsed');
  const [recentUsedAppList, setRecentUsedAppList] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  // 我的应用的视图模式：卡片或表格
  const [myAppViewMode, setMyAppViewMode] = useState('card');

  // 从URL参数中获取activeTabKey
  useEffect(() => {
    utilsSetFromAppbuild();
    const urlParams = new URLSearchParams(props.location.search);
    const tabKey = urlParams.get('activeTabKey');
    if (tabKey) {
      setActiveTab(tabKey);
    }
  }, [props.location.search]);

  // 加载最近使用的应用
  const getRecentlyUsedApplicationList = async () => {
    setIsLoading(true);
    try {
      // 获取最近使用的应用
      const res = await recentlyUsedApplicationList();
      if (res.code === 0) {
        setRecentUsedAppList(res.data || []);
      } else {
        toast.error(res.msg);
      }
    } catch (error) {
      toast.error('加载最近使用应用列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    getRecentlyUsedApplicationList();
  }, []);

  // Tab切换时更新URL
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    props.history.push(`/appbuild/home?activeTabKey=${key}`);
  };

  // 跳转到应用页面
  const handleInAppPage = (app: any) => {
    props.history.push(`/app${app.id}/admin`);
  };

  // 渲染最近使用的应用
  const renderRecentlyUsedApps = () => {
    if (isLoading) {
      return <div className="app-loading">加载中...</div>;
    }

    if (!recentUsedAppList || recentUsedAppList.length === 0) {
      return <div className="app-empty">暂无最近使用的应用</div>;
    }

    return (
      <div className="app-cards-container">
        <div className="app-cards-grid">
          {recentUsedAppList.map((app: any) => (
            <div
              key={app.id}
              className="app-card"
              onClick={() => handleInAppPage(app)}
            >
              <div className="app-card-content">
                <div className="app-card-icon">
                  {app.logo ? (
                    app.logo.startsWith('http') ? (
                      <Avatar src={app.logo} size={36} />
                    ) : (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: app.logo || ''
                        }}
                      />
                    )
                  ) : (
                    <img
                      src={itemShow}
                      className="app-card-icon-img"
                      alt={app.name}
                    />
                  )}
                </div>
                <div className="app-card-info">
                  <div className="app-card-title">{app.name}</div>
                  <div className="app-card-desc">
                    {app.description || '暂无描述'}
                  </div>
                </div>
              </div>
              <div className="app-card-footer">
                <div className="app-card-status">
                  {app.isEnable === 1 ? (
                    <span className="status-enabled">已启用</span>
                  ) : (
                    <span className="status-disabled">未启用</span>
                  )}
                </div>

                {app.isPublishBuildCenter === 1 && (
                  <div className="app-card-action">
                    <i
                      className="fa fa-home home-icon"
                      title="已发布到搭建中心"
                    ></i>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 生成我的应用CRUD的schema - 卡片视图
  const generateMyAppCardSchema = () => {
    return {
      type: 'page',
      body: [
        {
          type: 'crud',
          syncLocation: false,
          api: {
            method: 'get',
            url: '/admin-api/system/application/my-application-list?userId=' + props.store.userInfo.id,
            adaptor: function (payload: any, response: any, api: any) {
              const list = payload.data || [];
          
              const newList = list.map((item: any) => {
                const logo = item.logo || '';
                const trimmedLogo = typeof logo === 'string' ? logo.trim() : '';
          
                return {
                  ...item,
                  // 新增字段用于模板判断
                  isHttpLogo: trimmedLogo.startsWith('http'),
                  isSvgLogo: trimmedLogo.startsWith('<svg') || trimmedLogo.startsWith('<span'),
                  finalLogo: trimmedLogo || '', // 可统一处理为 img 或 svg 字符串
                };
              });
          
              return {
                ...payload,
                data: newList
              };
            }
          },
          
          bulkActions: [],
          mode: 'cards',
          className: 'app-cards-container',
          columnsCount: 4,
          card: {
            className: 'app-card',
            body: [
              {
                type: 'html',
                html: `
                  <div class="app-card-content">
                  <div class="app-card-icon">
                   \${isHttpLogo 
      ? '<img src="' + finalLogo + '" width="42" height="42" />' 
      : (isSvgLogo ? finalLogo : '<img src="${itemShow}" width="42" height="42" />')}
                    </div>
                      <div class="app-card-info">
                        <div class="app-card-title" >\${name}</div>
                        <div class="app-card-desc" >\${description || '这里是描述'}</div>
                      </div>
                    </div>
                    <div class="app-card-footer">
                      <div class="app-card-status">
                        \${isEnable === 1 ? '<span class="status-enabled">已启用</span>' : '<span class="status-disabled">未启用</span>'}
                      </div>
                      \${isPublishBuildCenter === 1 ? '<div class="app-card-action"><i class="fa fa-home"  title="已发布到搭建中心"></i></div>' : ''}
                  </div>
                `
              }
            ],
            itemAction: {
              actionType: 'url',
              url: '/#/app${id}/admin'
            }
          },
          filter: {
            title: '',
            autoLoad: true,
            submitOnChange: true,
            className: 'my-app-filter filter-layout',
            mode: 'inline',
            bodyClassName: 'filter-body',
            body: [
              {
                type: 'wrapper',
                className: 'search-wrapper',
                style: {
                  width: '280px'
                },
                body: [
                  {
                    type: 'input-text',
                    name: 'name',
                    size: 'md',
                    clearable: true,
                    placeholder: '搜索应用名称',
                    className: 'search-input-with-icon'
                  }
                ]
              },
              {
                type: 'wrapper',
                className: 'filter-wrapper',
                style: {
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                  marginLeft: 'auto'
                },
                body: [
                  {
                    type: 'select',
                    name: 'role',
                    value: '',
                    clearable: true,
                    placeholder: '全部应用',
                    style: {
                      marginLeft: '8px',
                      minWidth: '140px'
                    },
                    options: [
                      {label: '全部应用', value: ''},
                      {label: '我创建的', value: '1'},
                      {label: '我管理的', value: '2'},
                      {label: '我是成员的', value: '3'}
                    ]
                  },
                  {
                    type: 'select',
                    name: 'sortType',
                    clearable: true,
                    value: '1',
                    placeholder: '按创建时间排序',
                    style: {
                      marginLeft: '8px',
                      minWidth: '140px'
                    },
                    options: [
                      {label: '按创建时间排序', value: '1'},
                      {label: '按更新时间排序', value: '2'}
                    ]
                  },
                  {
                    type: 'select',
                    name: 'isEnable',
                    clearable: true,
                    value: '',
                    placeholder: '全部状态',
                    style: {
                      marginLeft: '8px',
                      minWidth: '140px'
                    },
                    options: [
                      {label: '全部状态', value: ''},
                      {label: '启用', value: '1'},
                      {label: '未启用', value: '0'}
                    ]
                  },
                  {
                    type: 'button-group',
                    buttons: [
                      {
                        type: 'button',
                        icon: 'fa fa-th',
                        level: myAppViewMode === 'card' ? 'primary' : 'default',
                        onClick: () => setMyAppViewMode('card')
                      },
                      {
                        type: 'button',
                        icon: 'fa fa-table',
                        level: myAppViewMode === 'table' ? 'primary' : 'default',
                        onClick: () => setMyAppViewMode('table')
                      }
                    ]
                  }
                ]
              }
            ],
            actions: []
          },
          footerToolbar: ['switch-per-page', 'pagination']
        }
      ]
    };
  };

  // 生成我的应用CRUD的schema - 表格视图
  const generateMyAppTableSchema = () => {
    return {
      type: 'page',
      body: [
        {
          type: 'crud',
          syncLocation: false,
          api: {
            method: 'get',
            url: '/admin-api/system/application/my-application-list?userId=' + props.store.userInfo.id,
            adaptor: function (payload: any, response: any, api: any) {
              const list = payload.data || [];
          
              const newList = list.map((item: any) => {
                const logo = item.logo || '';
                const trimmedLogo = typeof logo === 'string' ? logo.trim() : '';
          
                return {
                  ...item,
                  // 新增字段用于模板判断
                  isHttpLogo: trimmedLogo.startsWith('http'),
                  isSvgLogo: trimmedLogo.startsWith('<svg') || trimmedLogo.startsWith('<span'),
                  finalLogo: trimmedLogo || '', // 可统一处理为 img 或 svg 字符串
                };
              });
          
              return {
                ...payload,
                data: newList
              };
            }
          },
          filter: {
            title: '',
            autoLoad: true,
            submitOnChange: true,
            className: 'my-app-filter filter-layout',
            mode: 'inline',
            bodyClassName: 'filter-body',
            body: [
              {
                type: 'wrapper',
                className: 'search-wrapper',
                style: {
                  width: '280px'
                },
                body: [
                  {
                    type: 'input-text',
                    name: 'name',
                    size: 'md',
                    clearable: true,
                    placeholder: '搜索应用名称',
                    className: 'search-input-with-icon'
                  }
                ]
              },
              {
                type: 'wrapper',
                className: 'filter-wrapper',
                style: {
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                  marginLeft: 'auto'
                },
                body: [
                  {
                    type: 'select',
                    name: 'role',
                    value: '',
                    clearable: true,
                    placeholder: '全部应用',
                    style: {
                      marginLeft: '8px',
                      minWidth: '140px'
                    },
                    options: [
                      {label: '全部应用', value: ''},
                      {label: '我创建的', value: '1'},
                      {label: '我管理的', value: '2'},
                      {label: '我是成员的', value: '3'}
                    ]
                  },
                  {
                    type: 'select',
                    name: 'sortType',
                    value: '1',
                    clearable: true,
                    placeholder: '按创建时间排序',
                    style: {
                      marginLeft: '8px',
                      minWidth: '140px'
                    },
                    options: [
                      {label: '按创建时间排序', value: '1'},
                      {label: '按更新时间排序', value: '2'}
                    ]
                  },
                  {
                    type: 'select',
                    name: 'isEnable',
                    value: '',
                    clearable: true,
                    placeholder: '全部状态',
                    style: {
                      marginLeft: '8px',
                      minWidth: '140px'
                    },
                    options: [
                      {label: '全部状态', value: ''},
                      {label: '启用', value: '1'},
                      {label: '未启用', value: '0'}
                    ]
                  },
                  {
                    type: 'button-group',
                    style: {
                      marginLeft: '24px'
                    },
                    buttons: [
                      {
                        type: 'button',
                        icon: 'fa fa-th',
                        level: myAppViewMode === 'card' ? 'primary' : 'default',
                        onClick: () => setMyAppViewMode('card')
                      },
                      {
                        type: 'button',
                        icon: 'fa fa-table',
                        level: myAppViewMode === 'table' ? 'primary' : 'default',
                        onClick: () => setMyAppViewMode('table')
                      }
                    ]
                  }
                ]
              }
            ],
            actions: []
          },
          columns: [
            {
              name: 'logo',
              label: '应用图标',
              type: 'html',
              html: `
   <div class="center-card-content">
                    <div class="center-card-logo">
                      \${isHttpLogo 
      ? '<img src="' + finalLogo + '" width="42" height="42" />' 
      : (isSvgLogo ? finalLogo : '<img src="${itemShow}" width="42" height="42" />')}
                    </div>
                  </div>
              `
            },
            {
              name: 'name',
              label: '应用名称'
            },
            {
              name: 'description',
              label: '应用描述'
            },
            {
              name: 'status',
              label: '启用状态',
              type: 'tpl',
              tpl: '${isEnable === 1 ? "<span class=\'status-enabled-crud\'>已启用</span>" : "<span class=\'status-disabled-crud\'>未启用</span>"}'
            },
            {
              name: 'isPublish',
              label: '发布状态',
              type: 'tpl',
              tpl: '${isRelease === 1 ? "<span class=\'status-enabled-crud\'>已发布</span>" : "<span class=\'status-disabled-crud\'>未发布</span>"}'
            },
            {
              name: 'isPublishBuildCenter',
              label: '搭建中心状态',
              type: 'tpl',
              tpl: '${isPublishBuildCenter === 1 ? "<span class=\'status-enabled-crud\'>已发布</span>" : "<span class=\'status-disabled-crud\'>未发布</span>"}'
            },
            {
              name: 'role',
              label: '应用权限',
              type: 'tpl',
              tpl: '${role === 1 ? "创建者" : role === 2 ? "管理员" : "成员"}'
            },
            {
              name: 'updateTime',
              label: '最近编辑时间',
              type: 'input-datetime',
              valueFormat: 'x',
              static: true
            },
            {
              name: 'updater',
              label: '编辑人',
              type: 'tpl',
              tpl: '${updater || "-"}'
            },
            {
              name: 'createTime',
              label: '创建时间',
              type: 'input-datetime',
              valueFormat: 'x',
              static: true
            },
            {
              name: 'creator',
              label: '创建人',
              type: 'tpl',
              tpl: '${creator || "-"}'
            }
          ],
          footerToolbar: ['switch-per-page', 'pagination']
        }
      ]
    };
  };

  // 生成搭建中心CRUD的schema
  const generateCenterCrudSchema = () => {
    return {
      type: 'page',
      body: [
        {
          type: 'tpl',
          tpl: '<div class="center-title">组织下全部应用</div>',
          inline: false
        },
        {
          type: 'crud',
          syncLocation: false,
          mode: 'cards',
          api: {
            method: 'get',
            url: '/admin-api/system/application/get-publish-center-list',
            adaptor: function (payload: any, response: any, api: any) {
              const list = payload.data || [];
          
              const newList = list.map((item: any) => {
                const logo = item.logo || '';
                const trimmedLogo = typeof logo === 'string' ? logo.trim() : '';
          
                return {
                  ...item,
                  // 新增字段用于模板判断
                  isHttpLogo: trimmedLogo.startsWith('http'),
                  isSvgLogo: trimmedLogo.startsWith('<svg') || trimmedLogo.startsWith('<span'),
                  finalLogo: trimmedLogo || '', // 可统一处理为 img 或 svg 字符串
                };
              });
          
              return {
                ...payload,
                data: newList
              };
            }
          },
          columnsCount: 4,
          switchPerPage: false,
          className: 'center-crud',
          card: {
            className: 'build-center-card',
            body: [
              {
                type: 'html',
                html: `
                  <div class="center-card-content">
                    <div class="center-card-logo">
                      \${isHttpLogo 
      ? '<img src="' + finalLogo + '" width="42" height="42" />' 
      : (isSvgLogo ? finalLogo : '<img src="${itemShow}" width="42" height="42" />')}
                    </div>
                    <div class="center-card-title">\${name}</div>
                  </div>
                `
              }
            ],
            itemAction: {
              actionType: 'url',
              url: '/#/app${id}/admin'
            }
          },
          footerToolbar: ['statistics', 'switch-per-page', 'pagination']
        }
      ]
    };
  };

  return (
    <div className="app-build-home-page">
      <div className="app-build-home-page-main">
        <Tabs
          theme={localStorage.getItem('amis-theme') || 'cxd'}
          mode="line"
          activeKey={activeTab}
          onSelect={handleTabChange}
          linksClassName="app-build-tabs-title"
        >
          <Tabs.Tab title="最近使用" eventKey="recentlyUsed">
            <div className="app-build-content">{renderRecentlyUsedApps()}</div>
          </Tabs.Tab>
          <Tabs.Tab title="我的应用" eventKey="myapp">
            <div className="app-build-content">
              <AMISRenderer
                schema={
                  myAppViewMode === 'card'
                    ? generateMyAppCardSchema()
                    : generateMyAppTableSchema()
                }
                embedMode={true}
              />
            </div>
          </Tabs.Tab>
          <Tabs.Tab title="搭建中心" eventKey="appCenter">
            <div className="app-build-content">
              <AMISRenderer
                schema={generateCenterCrudSchema()}
                embedMode={true}
              />
            </div>
          </Tabs.Tab>
        </Tabs>
      </div>
    </div>
  );
};

export default AppBuildHomePage;
