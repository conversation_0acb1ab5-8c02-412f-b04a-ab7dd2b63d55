.workbench {
  width: 877px;
  min-height: calc(100vh - 80px);
  padding: 30px 24px;
  background-color: var(--background);
  margin: 0 auto;
  border-radius: 8px;

  &-header {
    width: 100%;
    height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-title {
      font-size: 20px;
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 20px;
      padding: 0px;
      text-align: left;
      position: relative;
    }

    &-settings {
      cursor: pointer;

      &:hover {
        .workbench-header-settings-text {
          color: var(--primary);
        }
      }

      &-text {
        font-size: 14px;
        margin-left: 8px;
        color: var(--text--muted-color);
        line-height: 24px; /* 设置统一的行高 */
        vertical-align: middle;
      }
    }
  }

  &-group {
    width: 100%;
    display: flex;
    position: relative;
    margin: 0 auto;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;

    &-tabs {
      width: calc(100% - 110px);
      position: relative;
      display: flex;
      padding: 10px 0;
      background-color: transparent;
      flex-wrap: wrap;
      gap: 10px;
    }
  }

  // 自定义分组按钮样式
  .group-btn {
    margin-right: 8px;
    margin-bottom: 10px;
    padding: 6px 15px;
    border-radius: 4px;
    font-size: 14px;
    background-color: var(--light-bg);
    color: var(--text-color);
    border: none;
    max-width: 150px;
    height: auto;
    min-height: 32px;
    display: inline-block;
    text-align: center;
    white-space: normal;
    word-break: break-word;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      background-color: #e8ecf3 !important;
      color: var(--primary) !important;
      border: none !important;
    }

    &-active {
      background-color: #f0f4ff !important;
      color: #4285f4 !important;

      &:after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 24px;
        height: 3px;
        background-color: #4285f4;
        border-radius: 1.5px;
      }
    }

    // 拖拽相关样式
    &.dragging {
      opacity: 0.6;
      transform: rotate(3deg);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      z-index: 1000;
    }

    &.drag-over {
      // border: 2px dashed var(--primary) !important;
      background-color: #f0f8ff !important;
    }

    .drag-handle {
      margin-right: 6px;
      color: #d9d9d9;
      font-size: 10px;
      cursor: move;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &:hover .drag-handle {
      opacity: 1;
      color: #999;
    }

    &.group-btn-active .drag-handle {
      color: rgba(255, 255, 255, 0.8);
    }

    &.group-btn-active:hover .drag-handle {
      color: rgba(255, 255, 255, 1);
    }
  }

  // 拖拽分组容器样式
  .group-tabs-draggable {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;

    .drag-handle {
      margin-right: 6px;
      color: #d9d9d9;
      font-size: 10px;
      cursor: move;
      opacity: 0;
      transition: opacity 0.2s;
    }

    .group-btn:hover .drag-handle {
      opacity: 1;
      color: #999;
    }

    .group-btn.group-btn-active .drag-handle {
      color: rgba(255, 255, 255, 0.8);
    }

    .group-btn.group-btn-active:hover .drag-handle {
      color: rgba(255, 255, 255, 1);
    }
  }

  &-apps {
    &-grid {
      margin-top: 10px;
    }
  }

  // 分组管理样式
  &-group-manager {
    .cxd-ListItem-dragBtn,
    .dark-ListItem-dragBtn {
      margin: 15px 0;
    }

    .group-manager-list-item {
      width: 100%;

      .group-item-row {
        margin: 15px 0;

        .group-item-name {
          font-size: 14px;
          color: var(--text-color);
          padding-left: 8px;
        }

        .group-item-right {
          display: flex;
          align-items: center;

          .group-item-scope {
            margin-right: 4px;

            .group-scope-all {
              display: inline-block;
              border-radius: 4px;
              padding: 3px 8px;
              font-size: 12px;
              background-color: #e8f3ff;
              color: #165dff;
            }

            .group-scope-part {
              display: inline-block;
              border-radius: 4px;
              padding: 3px 8px;
              font-size: 12px;
              background-color: #e8ffea;
              color: #00b42a;
            }
          }

          .group-item-arrow {
            color: var(--text--muted-color);
          }
        }
      }
    }
  }

  &-application-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 30px;
    margin: 0 auto;
    justify-content: center;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(1, 1fr);
    }

    &-app {
      padding: 16px;
      display: flex;
      flex-direction: column;
      width: 192px;
      height: 72px;
      border: none;
      align-items: center;
      justify-content: center;

      &:hover {
        transform: translateY(-5px);
        background-color: #f0f4ff;
        transition: background-color 0.2s;
      }

      &-logo {
        width: 40px;
        height: 40px;
        //padding: 12.5px;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 8px;
        }
      }

      &-name {
        font-size: 14px;
        color: var(--text--muted-color);
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
        width: 100%;
        padding: 10px;
      }
      &-loading {
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.app-add-circle-button {
  width: 100%;
  margin-bottom: 10px;

  // width: 48px;
  // height: 48px;
  // /* borders-radius-5 8px */
  // border-radius: 8px;
  // /* 中性色-Amis/填充-amis/填充 9 */
  // background: var(--light-bg);

  // &:hover {
  //   .fa {
  //     color: var(--primary);
  //   }
  // }

  // .fa {
  //   color: var(--text-color);
  // }
}

.custom-icon-button {
  align-items: center;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  color: var(--text-color);
  width: 72px; /* 增加宽度为按钮留出空间 */
  position: relative;
  padding: 12px; /* 增加内边距 */
}

.app-icon-box {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 8px;
  background: #f2f3f5;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible; /* 确保编辑按钮可以显示在外面 */
  margin: 12px; /* 为按钮留出足够的空间 */
}

.del-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #ff4d4f;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  color: #fff;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20; /* 进一步提高z-index */
  border: 2px solid white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3); /* 更强的阴影 */

  &:hover {
    background-color: #ff7875;
    transform: scale(1.15);
    transition: all 0.2s ease;
  }

  i {
    font-size: 11px;
    color: white;
  }
}

.edit-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #1890ff;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  color: #fff;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20; /* 进一步提高z-index */
  border: 2px solid white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3); /* 更强的阴影 */

  &:hover {
    background-color: #40a9ff;
    transform: scale(1.15);
    transition: all 0.2s ease;
  }

  i {
    font-size: 11px;
    color: white;
  }
}

.app-icon-label {
  margin-top: 6px;
  font-size: 12px;
  color: var(--text-color);
  align-items: center;
  justify-content: center;
  display: flex;
  text-align: center;
  width: 100%;
  word-break: break-word;
  line-height: 1.2;
}

.main-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  // 图片样式
  img {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
  }

  // SVG图标容器样式
  span {
    width: 48px !important;
    height: 48px !important;
    margin: 0 !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    svg {
      width: 24px !important;
      height: 24px !important;
      color: currentColor;
    }
  }
}

// 默认应用图标样式
.default-app-icon {
  width: 48px;
  height: 48px;
  background-color: #f5f5f5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  // 可以添加一个默认图标，比如应用图标
  &::before {
    content: '📱';
    font-size: 24px;
    opacity: 0.5;
  }
}

// 默认应用小图标样式（用于分组管理中的小图标）
.default-app-mini-icon {
  width: 24px;
  height: 24px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: inline-block;

  &::before {
    content: '📱';
    font-size: 12px;
    opacity: 0.5;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}

.group-item-name {
  font-size: 14px;
  color: var(--text-color);
  margin-left: 8px;
}

.app-edit-menu {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 120px;

  .app-edit-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.delete-group-btn {
  width: 100%;
}

// 应用排序抽屉样式 - 表格模式
.app-sort-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-color: #4285f4;
  margin: 0 auto;

  .app-sort-icon {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    // 图片样式
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }

    // SVG图标容器样式
    span {
      width: 40px !important;
      height: 40px !important;
      margin: 0 !important;
      border-radius: 8px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;

      svg {
        width: 24px !important;
        height: 24px !important;
        color: currentColor;
      }
    }
  }

  .app-sort-default-icon {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &::before {
      content: '📱';
      font-size: 20px;
      opacity: 0.8;
      color: white;
    }
  }
}

// 移动到分组抽屉样式
.move-app-tip {
  padding: 16px 20px;
  color: #666;
  font-size: 14px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  margin: 0;
  background-color: #fafafa;
}

.group-item-container {
  padding: 0 !important;
  margin: 0 !important;
  border-bottom: 1px solid #f0f0f0;
  background: white;

  &:last-child {
    border-bottom: none;
  }

  .group-select-btn {
    width: 100% !important;
    padding: 16px 20px !important;
    text-align: left !important;
    border: none !important;
    background: white !important;
    color: #333 !important;
    font-size: 14px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    transition: background-color 0.2s !important;
    cursor: pointer !important;
    min-height: 56px !important;
    border-radius: 0 !important;
    box-shadow: none !important;

    &:hover {
      background-color: #f5f5f5 !important;
      color: #333 !important;
    }

    &:focus {
      background-color: #f5f5f5 !important;
      color: #333 !important;
      box-shadow: none !important;
    }

    &:active {
      background-color: #f5f5f5 !important;
      color: #333 !important;
    }

    // 右侧箭头
    &::after {
      content: '';
      width: 6px;
      height: 6px;
      border-right: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
      transform: rotate(-45deg);
      margin-left: auto;
      flex-shrink: 0;
    }

    &:hover::after {
      border-color: #999;
    }
  }
}

// 确保抽屉内容区域样式正确
.cxd-Drawer-content {
  .move-app-tip + .group-item-container:first-of-type {
    border-top: none;
  }
}

// 拖拽排序时的占位符样式
.cxd-ListItem-dragBtn {
  display: none; // 隐藏默认的拖拽按钮，使用自定义的
}

// 空状态样式
.cxd-Crud-placeholder {
  text-align: center;
  color: var(--text--muted-color);
  padding: 40px 20px;
  font-size: 14px;
}

// 拖拽时的视觉反馈
.cxd-ListItem.is-dragging {
  opacity: 0.6;
  transform: rotate(1deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  background-color: #fff;
}

// 拖拽目标区域样式
.cxd-ListItem.is-drop-allowed {
  border-top: 2px solid #4285f4;
}

// 编辑分组应用Cards样式
.edit-group-apps-cards {
  padding: 20px 0; /* 增加内边距，为按钮留出空间 */

  // 确保Cards组件使用四等分布局
  .cxd-Cards {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    grid-gap: 30px !important; /* 增加间距，避免按钮重叠 */
    justify-items: center !important;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(1, 1fr) !important;
    }
  }

  // 重置Cards的默认样式
  .cxd-Cards-items {
    display: contents !important;
  }

  // 确保每个卡片项居中，并有足够空间显示按钮
  .cxd-Cards-item {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 15px !important; /* 增加内边距为按钮留出更多空间 */
    overflow: visible !important; /* 确保按钮不被裁剪 */
    min-height: 100px !important; /* 确保有足够的高度 */
  }
}

// 新建分组应用Cards样式
.create-group-apps-cards {
  padding: 20px 0px; /* 增加内边距，为按钮留出空间 */

  // 确保Cards组件使用四等分布局
  .cxd-Cards {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    grid-gap: 30px !important; /* 增加间距，避免按钮重叠 */
    justify-items: center !important;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(1, 1fr) !important;
    }
  }

  // 重置Cards的默认样式
  .cxd-Cards-items {
    display: contents !important;
  }

  // 确保每个卡片项居中，并有足够空间显示按钮
  .cxd-Cards-item {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 15px !important; /* 增加内边距为按钮留出更多空间 */
    overflow: visible !important; /* 确保按钮不被裁剪 */
    min-height: 100px !important; /* 确保有足够的高度 */
  }
}

// 编辑分组和新建分组应用卡片样式
.edit-group-app-card,
.create-group-app-card {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;

  .cxd-Card-body {
    padding: 0 !important;
  }
}

.cxd-Table-heading {
  margin-left: 0 !important;
}
