import React, {FC} from 'react';
import './index.scss';
import {Button, toast} from 'amis';
import choseDataSetPopup from './commomPopup/ChoseDataSet/index';
import ChoseDataSetMain from './commomPopup/ChoseDataSet/main';
import AMISRenderer from '@/component/AMISRenderer';
import {DndProvider, DropTargetMonitor} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';
import {useDrag} from 'react-dnd';

import {getFormFieldPage} from '@/utils/api/api';

import dataset_fillin_icon from '@/image/common_icons/dataset_fillin_icon.png';
import dataset_delete_icon from '@/image/common_icons/dataset_delete_icon.png';

import {FieldItemBox} from './commomPopup/ChoseDataSet/fieldItem';

import {ChartConfiguration} from './commomPopup/ChoseDataSet/chartConfiguration';

interface ChartDataInteractionProps {
  context: any;
  config: any;
  handleConfig: (config: any) => void;
  selectDataSet: (dataset: any) => void;
  datasetInfo?: any;
  xAxisField?: any[];
  yAxisField?: any[];
}

const ChartDataInteraction: FC<ChartDataInteractionProps> = (props: ChartDataInteractionProps) => {
  const typeField: any = {
    timestamp: '23',
    int: '123',
    varchar: 'abc'
  };

  const [openChoseDataSetPopup, setOpenChoseDataSetPopup] =
    React.useState(false);

  // 选择的数据集
  const [datasetInfo, setDatasetInfo] = React.useState<any>('');
  //选择的数据集字段
  const [datasetFieldList, setDatasetFieldList] = React.useState<any>(false);
  // x轴字段
  const [dropResultListX, setDropResultListX] = React.useState<any>([]);
  // y轴字段
  const [dropResultListY, setDropResultListY] = React.useState<any>([]);

  // 初始化时从props中读取已保存的数据
  React.useEffect(() => {
    console.log('ChartDataInteraction', props);
    
    // 回显数据集信息
    if (props.datasetInfo && props.datasetInfo.id) {
      setDatasetInfo(props.datasetInfo);
    }
    
    // 回显坐标轴字段
    if (props.xAxisField && props.xAxisField.length) {
      setDropResultListX(props.xAxisField);
    }
    
    if (props.yAxisField && props.yAxisField.length) {
      setDropResultListY(props.yAxisField);
    }
  }, []);

  const handleChoseDataSet = (val: any) => {
    props.selectDataSet(val);
    setDatasetInfo(val);
    setDropResultListX([]);
    setDropResultListY([]);
    setOpenChoseDataSetPopup(false);
  };

  // 获取数据集表字段 getFieldList
  const getFieldListInfo = () => {
    let applicationPageId: number = datasetInfo.associatedDataTable;
    let data = {
      applicationPageId
    };
    getFormFieldPage(data).then((res: any) => {
      if (res.code == 0) {
        setDatasetFieldList(res.data.list);
        toast.success(res.msg);
      } else {
        toast.error(res.msg);
      }
    });
  };
  
  const handleMoveInBoxItem = (dropResult: any, item: any) => {
    console.log('dropResult', dropResult, item);
    let list = [];
    list.push(item);
    if (dropResult.name == '分类字段') {
      setDropResultListX(list);
    }
    if (dropResult.name == '数值字段') {
      setDropResultListY(list);
    }
  };

  // 删除字段
  const handleDeleteDropResultList = (title:string,index:number) => {
    if(title == '分类字段'){
      let list = [...dropResultListX];
      list.splice(index,1);
      setDropResultListX(list);
    }
    if(title == '数值字段'){
      let list = [...dropResultListY];
      list.splice(index,1);
      setDropResultListY(list);
    }
  }

  React.useEffect(() => {
    if (datasetInfo.id) {
      getFieldListInfo();
    }
  }, [datasetInfo]);

  React.useEffect(() => {
    console.log('dropResultListX', dropResultListX);
    console.log('dropResultListY', dropResultListY);
    props.handleConfig({
      xAxis: dropResultListX,
      yAxis: dropResultListY
    });
  }, [dropResultListX, dropResultListY]);

  return (
    <div className="chartDataBox">
      <DndProvider backend={HTML5Backend}>
        {/* 图表配置 */}
        <div className="chartConfiguration">
          <ChartConfiguration title="分类字段" dropResultList={dropResultListX} handleDelete={handleDeleteDropResultList} />
          <ChartConfiguration title="数值字段" dropResultList={dropResultListY} handleDelete={handleDeleteDropResultList} />
        </div>
        {/* 数据集数据 */}
        <div className="dataSetData">
          <div className="dataSetData-title">数据集</div>
          {datasetInfo.id ? (
            <div className="dataSetData-changeBtn">
              <div className="dataSetData-changeBtn-name">
                {datasetInfo.name}
              </div>
              <div
                className="dataSetData-changeBtn-btn"
                onClick={() => {
                  setOpenChoseDataSetPopup(true);
                }}
              >
                更改数据集
              </div>
            </div>
          ) : (
            <div
              className="dataSetData-choseBtn"
              onClick={() => {
                setOpenChoseDataSetPopup(true);
              }}
            >
              选择数据集
            </div>
          )}
          {/* 数据集字段 */}
          {datasetInfo.id && (
            <div className="dataSetData-fieldtitle">
              <span>字段</span>
              <Button
                level="link"
                size="sm"
                className="px-4"
                onClick={() => {
                  getFieldListInfo();
                }}
              >
                刷新
              </Button>
            </div>
          )}
          {datasetFieldList &&
            datasetFieldList.map((item: any, index: number) => {
              return (
                <FieldItemBox
                  fieldItem={item}
                  key={index + '-' + item.id}
                  handleMoveInBoxItem={handleMoveInBoxItem}
                ></FieldItemBox>
              );
            })}
        </div>
      </DndProvider>
      {openChoseDataSetPopup && (
        <AMISRenderer
          show={openChoseDataSetPopup}
          schema={choseDataSetPopup(
            <ChoseDataSetMain
              onConfirm={handleChoseDataSet}
              onClose={() => setOpenChoseDataSetPopup(false)}
            />
          )}
          onClose={() => setOpenChoseDataSetPopup(false)}
        />
      )}
    </div>
  );
};

export default ChartDataInteraction;
