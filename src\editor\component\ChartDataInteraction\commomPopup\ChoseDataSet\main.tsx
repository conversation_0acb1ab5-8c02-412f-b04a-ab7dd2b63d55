import React, {<PERSON>} from 'react';
import {toast, SearchBox, Pagination,Button} from 'amis';
import './main.scss';
import datasetCheckIcon from '@/image/common_icons/dataset_check_icon.png';
import {getDataSetPage} from '@/utils/api/api';
import {set} from 'lodash';

const ChoseDataSetMain: FC<any> = (props: any) => {
  // 应用id
  const [applyId, setApplyId] = React.useState<string>('');
  // 数据集名称
  const [datasetName, setDatasetName] = React.useState<string>('');
  // 数据集列表
  const [datasetList, setDatasetList] = React.useState<any>([]);
  // 分页
  const [pageCurrent, setPageCurrent] = React.useState<number>(1);
  // 每页条数
  const [pageSize, setPageSize] = React.useState<number>(10);
  // 总条数
  const [totalCount, setTotalCount] = React.useState<number>(0);
  // 获取数据集列表
  const getDatasetListInfo = () => {
    let data = {
      applicationId: applyId,
      name: datasetName,
      pageSize: pageSize,
      pageNo: pageCurrent,
      applicantOrBackend: 1
    };
    getDataSetPage(data).then((res: any) => {
      if (res.code === 0) {
        setDatasetList(res.data.list);
        setTotalCount(res.data.total);
      } else {
        toast.error(res.msg);
      }
    });
  };

  // 选中的数据集
  const [checkItem, setCheckItem] = React.useState<any>(false);
  // 选择数据集
  const handleChoseDataSet = (item: any) => {
    setCheckItem(item);
  };

  // React.useEffect(() => {
  //   console.log('checkItem', checkItem);
  // }, [checkItem]);

  React.useEffect(() => {
    console.log(window.location.hash);
    // let param = window.location.hash;
    const param = window.location.hash;
    const match = param.match(/app(\d+)/);
    const appId = match ? match[1] : '';
    setApplyId(appId);
    // const parts = param.split('#/')[1].split('/')[0].split('app')[1];
    // setApplyId(parts);
    // setApplyId('100002');
  }, []);

  React.useEffect(() => {
    if (applyId) {
      getDatasetListInfo();
    }
  }, [applyId]);

  return (
    <div className="dataSetMain">
      <div className="dataSetMain-searchBox">
        <SearchBox
          placeholder="搜索数据集"
          label="搜索数据集"
          mini={false}
          searchImediately={false}
          className="dataSetMain-searchBox-search"
          onSearch={(val: any) => {
            console.log(val);
          }}
        />
      </div>
      <div className="dataSetMain-list">
        {datasetList.map((item: any, index: number) => {
          return (
            <div
              key={item.id}
              className="dataSetMain-list-item"
              onClick={() => handleChoseDataSet(item)}
            >
              <div className="dataSetMain-list-item-chose">
                {checkItem?.id == item.id && (
                  // <img
                  //   className="dataSetMain-list-item-chose-icon"
                  //   src={datasetCheckIcon}
                  // />
                  <i className="fa-solid fa-check dataSetMain-list-item-chose-icon"></i>
                )}
              </div>
              <div className="dataSetMain-list-item-name">{item.name}</div>
            </div>
          );
        })}
      </div>
      <div className="dataSetMain-pagination">
        <Pagination
          total={totalCount}
          perPageAvailable={[10, 20, 50, 100]}
          maxButtons="7"
          showPerPage={true}
          perPage={pageSize}
          activePage={pageCurrent}
          size="sm"
        />
      </div>

      <div className='dataSetMain-btnBox'>
        <Button
          label="取消"
          level="default"
          size="sm"
          className="dataSetMain-btnBox-cancel px-4"
          onClick={() => {
            props.onClose();
          }}
        >取消</Button>
        <Button
          label="确定"
          level="primary"
          size="sm"
          className="dataSetMain-btnBox-confirm px-4 ml-4"
          onClick={() => {
            props.onConfirm(checkItem);
          }}
        >确定</Button>
      </div>
    </div>
  );
};
export default ChoseDataSetMain;
