.projectMain {
  position: relative;
  width: calc(100% - 1.5rem);
  height: calc(100vh - 4.625rem);
  margin: 0.75rem;
  padding-top: 40px;
  overflow: auto;
  /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
  -webkit-scrollbar {
    display: none;
  }
  /* 隐藏Firefox浏览器的滚动条 */
  scrollbar-width: none;

  &-top {
    position: relative;
    width: 67.5rem;
    height: 7.5rem;
    margin: auto;
    padding: 0 1.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-left {
      &-name {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.375rem;
        font-weight: bold;
      }

      &-describe {
        font-size: 0.875rem;
        color: #999999;
        margin-top: 0.375rem;
      }
    }

    &-right {
      &-btn {
        position: relative;
        width: 7.5rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-addIcon {
          font-size: 1.25rem;
          color: var(--button-primary-default-font-color);
          margin-right: 0.5rem;
        }

        &-name {
          font-size: 0.875rem;
          color: var(--button-primary-default-font-color);
        }
      }
    }
  }

  &-main {
    position: relative;
    width: 67.5rem;
    overflow: hidden;
    margin: auto;
    padding: 0 1.25rem;

    &-tabsToolbar {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .cxd-Tabs-pane,
    .dark-Tabs-pane {
      padding: 1.25rem 0;
    }

    &-tabsAppList {
      position: relative;
      width: 100%;
      display: grid;
      grid-template-columns: repeat(auto-fill, 15.3125rem);
      grid-gap: 1.25rem 1.25rem;

      &-item {
        width: 15.3125rem;
        height: 6.625rem;
        box-shadow: 0 0 0.625rem 0 var(--borderColor);
        background-color: var(--light-bg);
        border-radius: 0.375rem;

        padding: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &:hover {
          box-shadow: 0 0 0.625rem 0 var(--borderColor);
          cursor: pointer;
        }

        &:active {
          opacity: 0.6;
        }

        &-content {
          width: 100%;
          height: 2.625rem;
          display: flex;
          align-items: center;
          justify-content: space-between;

          &-itemshow {
            position: relative;
            width: 2.625rem;
            height: 2.625rem;

            &-img {
              width: 100%;
              height: 100%;
              border-radius: 8px;
            }

            &-icon {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 8px;
              overflow: hidden;

              span {
                width: 100% !important;
                height: 100% !important;
                border-radius: 8px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
              }

              i {
                font-size: 20px;
              }
            }
          }

          &-content {
            flex: 1;
            margin-left: 0.875rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            &-title {
              font-size: 0.875rem;
              color: var(--text-color);
              text-overflow: ellipsis;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              word-break: break-all;
            }

            &-describe {
              font-size: 0.625rem;
              color: var(--text--muted-color);
              text-overflow: ellipsis;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              word-break: break-all;
            }
          }
        }

        &-play {
          width: 100%;
          height: 1.25rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
		  
		  &-status {
		  	height: 1.25rem;
		  	display: flex;
		  	font-size: 0.63rem;
		  	align-items: center;
		  	justify-content: center;
		  }
		  
		  &-released {
		  	color: var(--primary);
		  }
		  
		  &-unreleased {
		  	color: var(--text--muted-color);
		  }

          &-btn {
            width: 1.25rem;
            height: 1.25rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              border-radius: 2px;

              &-icon {
                color: var(--text-color);
              }
            }

            &:active {
              opacity: 0.6;
            }

            &-icon {
              font-size: 1rem;
              color: var(--text--muted-color);
            }
          }
        }
      }
    }

    &-setCenter {
      width: 100%;
      height: 6.25rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      border-bottom: 1px solid var(--borderColor);

      &-left {
        &-name {
          font-size: 0.875rem;
          color: var(--text-color);
          font-weight: bold;
          margin-bottom: 0.25rem;
        }

        &-content {
          font-size: 0.875rem;
          color: var(--text--muted-color);
          margin-top: 0.25rem;
        }

        &-pjImage {
          width: 100%;
          display: flex;
          align-items: center;
          margin-bottom: 0.25rem;

          &-image {
            width: 2.25rem;
            height: 2.25rem;
            margin-bottom: 0.25rem;
            border-radius: 50%;
            background-color: red;

            &-img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              display: block;
            }
          }

          &-name {
            font-size: 0.875rem;
            color: var(--text-color);
            font-weight: bold;
            margin-left: 0.75rem;
          }
        }
      }

      &-right {
        width: 6.875rem;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          width: 6rem;
          height: 2.5rem;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid var(--borderColor);
          color: var(--text-color);
          font-size: 1rem;
          background-color: var(--light-bg);
          cursor: pointer;

          &:hover {
            background-color: var(--light-bg);
            opacity: 0.8;
          }

          &:active {
            opacity: 0.6;
          }
        }

        &-delbtn{
          width: 6rem;
          height: 2.5rem;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #FA5151;
          color: var(--button-primary-default-font-color);
          font-size: 1rem;
          cursor: pointer;

          &:hover {
            opacity: 0.8;
          }

          &:active {
            opacity: 0.6;
          }
        }
      }
    }
  }
}

