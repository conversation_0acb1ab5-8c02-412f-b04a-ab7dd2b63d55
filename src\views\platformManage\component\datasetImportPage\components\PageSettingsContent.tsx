import React, {useState} from 'react';
import AMISRenderer from '@/component/AMISRenderer';
import {updateApplicationPageAndClass} from '@/utils/api/api';
import {Button, toast} from 'amis';

interface PageSettingsContentProps {
  activeKey: string;
  pageInfo?: any;
  initPathItem: any;
  onRefresh?: () => void;
}

const PageSettingsContent: React.FC<PageSettingsContentProps> = ({
  activeKey,
  pageInfo,
  initPathItem,
  onRefresh
}) => {
  // 定义状态来控制对话框的显示
  const [nameDialogVisible, setNameDialogVisible] = useState(false);
  const [descDialogVisible, setDescDialogVisible] = useState(false);
  
  // 处理名称更新
  const handleUpdateName = async (values: any) => {
    values = values[0];
    console.log('handleUpdateName values',values)
    try {
      const response = await updateApplicationPageAndClass({
        id: initPathItem.id,
        name: values.name
      });
      
      if (response.code === 0) {
        toast.success('名称修改成功');
        // 关闭对话框
        setNameDialogVisible(false);
        // 刷新数据
        if (onRefresh) {
          onRefresh();
        }
      } else {
        toast.error(response.msg || '修改失败');
      }
    } catch (error) {
      console.error('更新名称失败:', error);
      toast.error('修改失败');
    }
  };
  
  // 处理描述更新
  const handleUpdateDescription = async (values: any) => {
    values = values[0];
    try {
      const response = await updateApplicationPageAndClass({
        id: initPathItem.id,
        description: values.description
      });
      
      if (response.code === 0) {
        toast.success('描述修改成功');
        // 关闭对话框
        setDescDialogVisible(false);
        // 刷新数据
        if (onRefresh) {
          onRefresh();
        }
      } else {
        toast.error(response.msg || '修改失败');
      }
    } catch (error) {
      console.error('更新描述失败:', error);
      toast.error('修改失败');
    }
  };

  const renderBasicSettings = () => {
    return (
      <div className="basic-settings">
        <h2 className="settings-title">基础设置</h2>
        <p className="settings-desc">设置页面名称、页面图标等</p>
        
        <div className="settings-section">
          <div className="settings-item">
            <div className="item-label">页面名称</div>
            <div className="item-content">
              <div className="content-left">
                <span className="content-text">{initPathItem.name}</span>
              </div>
              <Button 
                onClick={() => setNameDialogVisible(true)}
              >
                修改名称
              </Button>
            </div>
          </div>

          <div className="settings-item">
            <div className="item-label">页面描述</div>
            <div className="item-content">
              <div className="content-left">
                <span className="content-text">{initPathItem.description || '-'}</span>
              </div>
              <Button 
                onClick={() => setDescDialogVisible(true)}
              >
                修改描述
              </Button>
            </div>
          </div>
        </div>

          {/* 名称修改对话框 */}
          {nameDialogVisible && (
                <AMISRenderer
                  schema={{
                    type: 'dialog',
                    title: '修改名称',
                    body: {
                      type: 'form',
                      body: [
                        {
                          type: 'input-text',
                          name: 'name',
                          label: '页面名称',
                          value: initPathItem.name,
                          required: true
                        }
                      ],
                      submitText: '保存',
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          actionType: 'cancel'
                        },
                        {
                          type: 'submit',
                          label: '保存',
                          primary: true
                        }
                      ]
                    },
                    show: true,
                    showCloseButton: true,
                    onClose: () => setNameDialogVisible(false),
                    onConfirm: handleUpdateName
                  }}
                />
              )}

        {/* 描述修改对话框 */}
        {descDialogVisible && (
                <AMISRenderer
                  schema={{
                    type: 'dialog',
                    title: '修改描述',
                    body: {
                      type: 'form',
                      body: [
                        {
                          type: 'textarea',
                          name: 'description',
                          label: '页面描述',
                          value: initPathItem.description
                        }
                      ],
                      submitText: '保存',
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          actionType: 'cancel'
                        },
                        {
                          type: 'submit',
                          label: '保存',
                          primary: true
                        }
                      ]
                    },
                    show: true,
                    showCloseButton: true,
                    onClose: () => setDescDialogVisible(false),
                    onConfirm: handleUpdateDescription
                  }}
                />
              )}
      </div>
    );
  };

  const renderContent = () => {
    switch (activeKey) {
      case 'basicSettings':
        return renderBasicSettings();
      case 'permissionSettings':
        return <div>权限设置内容</div>;
      case 'advancedSettings':
        return <div>高级设置内容</div>;
      default:
        return null;
    }
  };

  return (
    <div className="page-settings-content">
      {renderContent()}
    </div>
  );
};

export default PageSettingsContent; 