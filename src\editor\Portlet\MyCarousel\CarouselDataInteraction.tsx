import React, {FC, useEffect, useRef} from 'react';
import './index.scss';
import {Button, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {DndProvider, useDrag, useDrop} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';
import {Tpl} from 'amis/lib/renderers/Tpl';

// 定义拖拽项类型
const ItemTypes = {
  CAROUSEL_ITEM: 'carouselItem'
};

interface CarouselItem {
  image: string;
  title?: string;
  href?: string;
  internalPage?: string;
  internalName?: string;
  // blank?: boolean;
  id?: number;
  linkType?: string;
}

interface CarouselDataInteractionProps {
  appId: any;
  context: any;
  options: CarouselItem[];
  handleOptions: (options: CarouselItem[]) => void;
}

// 轮播项组件
const CarouselItemRow: FC<any> = ({item, index, onEdit, onDelete, onMove}) => {
  // 设置拖拽源
  const [{isDragging}, drag] = useDrag({
    type: ItemTypes.CAROUSEL_ITEM,
    item: () => ({
      index
    }),
    collect: monitor => ({
      isDragging: monitor.isDragging()
    })
  });

  // 设置放置目标
  const [{isOver, canDrop}, drop] = useDrop({
    accept: ItemTypes.CAROUSEL_ITEM,
    drop: (draggedItem: any, monitor) => {
      // 只处理直接放置在此项上的操作，忽略冒泡的事件
      if (monitor.didDrop()) {
        return;
      }

      if (draggedItem.index !== index) {
        onMove(draggedItem.index, index);
      }
    },
    canDrop: (draggedItem: any) => {
      // 不能将项目拖放到自己上
      return draggedItem.index !== index;
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  // 合并拖放引用
  const ref = useRef(null);
  drag(drop(ref));

  // 应用样式
  const opacity = isDragging ? 0.4 : 1;
  const backgroundColor =
    isOver && canDrop ? 'rgba(0, 255, 0, 0.1)' : 'transparent';

  return (
    <div
      ref={ref}
      className="carousel-item-row"
      style={{opacity, backgroundColor}}
      key={`carousel-item-${index}`}
    >
      <div className="carousel-item-info">
        <i
          className="fa fa-bars"
          style={{cursor: 'move', marginRight: '8px'}}
        ></i>
        <span className="item-title">{`轮播页 ${index + 1}`}</span>
      </div>
      <div className="carousel-item-actions">
        <Button level="link" size="sm" onClick={() => onEdit(index)}>
          <i className="fa fa-edit"></i>
        </Button>
        <Button level="link" size="sm" onClick={() => onDelete(index)}>
          <i className="fa fa-trash"></i>
        </Button>
      </div>
    </div>
  );
};

const CarouselDataInteraction: FC<CarouselDataInteractionProps> = (
  props: CarouselDataInteractionProps
) => {
  const [refreshListKey, setRefreshListKey] = React.useState(0);
  const [carouselItems, setCarouselItems] = React.useState<CarouselItem[]>([]);
  const [currentEditIndex, setCurrentEditIndex] = React.useState<number>(-1);
  const [showEditDialog, setShowEditDialog] = React.useState<boolean>(false);
  // 批量编辑状态
  const [showBatchEditDialog, setShowBatchEditDialog] =
    React.useState<boolean>(false);
  const [batchItems, setBatchItems] = React.useState<CarouselItem[]>([]);
  const isMounted = useRef(true);
  const isInitialized = useRef(false);

  const [showConfirmDialog, setShowConfirmDialog] = React.useState(false);
  const [deleteIndex, setDeleteIndex] = React.useState(-1);
  const [deleteItemId, setDeleteItemId] = React.useState<any>(null);
  const [deleteRow, setDeleteRow] = React.useState<any>(null);

  // 组件挂载状态跟踪
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
      // 清理对话框状态
      setShowEditDialog(false);
      setShowBatchEditDialog(false);
      setCurrentEditIndex(-1);
    };
  }, []);

  // 初始化时从props中读取已保存的数据
  React.useEffect(() => {
    if (!isMounted.current) return;

    try {
      // 防止重复初始化，但允许数据更新
      if (props.options && props.options.length) {
        console.log(
          'CarouselDataInteraction接收到新的options数据:',
          props.options
        );
        // 深拷贝选项，避免直接引用
        const safeOptions = JSON.parse(JSON.stringify(props.options));
        setCarouselItems(safeOptions);

        // 如果是首次初始化，设置标志
        if (!isInitialized.current) {
          isInitialized.current = true;
        }
        return;
      }

      if (!isInitialized.current) {
        // 检查是否有保存的静态选项数据
        const savedOptions = props.context?.schema?.__savedStaticOptions;
        if (savedOptions && savedOptions.length) {
          console.log(
            'CarouselDataInteraction从schema中恢复数据:',
            savedOptions
          );
          const safeOptions = JSON.parse(JSON.stringify(savedOptions));
          setCarouselItems(safeOptions);
          // 更新到主数据中，使用setTimeout避免状态冲突
          setTimeout(() => {
            if (isMounted.current) {
              props.handleOptions(safeOptions);
            }
          }, 0);
          isInitialized.current = true;
        } else {
          // 设置默认轮播项
          const defaultItems = [
            {
              image: '',
              title: '标题1'
            }
          ];
          setCarouselItems(defaultItems);
          // 更新到主数据中，使用setTimeout避免状态冲突
          setTimeout(() => {
            if (isMounted.current) {
              props.handleOptions(defaultItems);
            }
          }, 0);
          isInitialized.current = true;
        }
      }
    } catch (error) {
      console.error('初始化轮播项数据失败:', error);
      // 设置默认轮播项作为备用
      const defaultItems = [
        {
          image: '',
          title: '标题1'
        }
      ];
      setCarouselItems(defaultItems);
    }
  }, [props.options]);

  // 添加轮播项
  const handleAddItem = () => {
    if (!isMounted.current) return;

    try {
      const newItems = [
        ...carouselItems,
        {
          image: '',
          title: `标题${carouselItems.length + 1}`
        }
      ];

      // 先更新本地状态
      setCarouselItems(newItems);

      // 使用setTimeout延迟更新全局状态，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          // 深拷贝，避免引用问题
          props.handleOptions(JSON.parse(JSON.stringify(newItems)));
        }
      }, 0);
    } catch (error) {
      console.error('添加轮播项失败:', error);
      toast.error('添加轮播项失败');
    }
  };

  // 确认删除
  const handleConfirmDelete = () => {
    if (deleteIndex < 0&&!deleteItemId) return;

    if (deleteItemId) {
      const newItems = batchItems.filter(item => item.id !== deleteRow.id);
      setBatchItems(newItems);
      setDeleteItemId(null);
      setDeleteRow(null);
      setRefreshListKey(prev => prev + 1);
    }else{
      const newItems = carouselItems.filter((_, i) => i !== deleteIndex);

      // 先更新本地状态
      setCarouselItems(newItems);
  
      // 深拷贝，避免引用问题
      props.handleOptions(JSON.parse(JSON.stringify(newItems)));
      setDeleteIndex(-1);
    }

    setShowConfirmDialog(false);
    toast.success('删除成功');
  };
  // 删除轮播项
  const handleDeleteItem = (index: number) => {
    if (!isMounted.current) return;
    setDeleteIndex(index);
    setShowConfirmDialog(true);
  };

  // 编辑轮播项
  const handleEditItem = (index: number) => {
    if (!isMounted.current) return;

    try {
      // 先设置索引
      setCurrentEditIndex(index);

      // 使用setTimeout延迟显示对话框，避免状态树更新冲突
      setTimeout(() => {
        if (isMounted.current) {
          setShowEditDialog(true);
        }
      }, 50);
    } catch (error) {
      console.error('打开编辑对话框失败:', error);
      toast.error('打开编辑对话框失败');
    }
  };

  // 保存编辑
  const handleSaveEdit = (values: any) => {
    if (!isMounted.current) return;

    try {
      if (currentEditIndex >= 0 && currentEditIndex < carouselItems.length) {
        // 处理blank值转换
        // if (values.blank === 'external') {
        // values.blank = true;
        // } else if (values.blank === 'internal') {
        //   values.blank = false;
        // }

        // 如果是内部页面，处理链接
        if (values.linkType === 'internal' && values.internalPage) {
          values.href = `#/app${props.appId}/admin/page${values.internalPage}`;
        } else if (values.linkType === 'external') {
          // 如果切换为外部链接，清除internalPage
          values.internalPage = '';
          values.internalName = '';
        }

        // 创建新数组，避免直接修改原数组
        const newItems = carouselItems.map((item, index) => {
          if (index === currentEditIndex) {
            return {...item, ...values};
          }
          return item;
        });

        // 先更新本地状态
        setCarouselItems(newItems);

        // 关闭对话框
        setShowEditDialog(false);

        // 使用setTimeout延迟更新全局状态和重置索引，避免状态冲突
        setTimeout(() => {
          if (isMounted.current) {
            // 深拷贝，避免引用问题
            props.handleOptions(JSON.parse(JSON.stringify(newItems)));
            setCurrentEditIndex(-1);
            toast.success('编辑成功');
          }
        }, 50);
      }
    } catch (error) {
      console.error('编辑保存失败:', error);
      toast.error('编辑保存失败');

      // 关闭对话框并重置索引
      setShowEditDialog(false);
      setTimeout(() => {
        if (isMounted.current) {
          setCurrentEditIndex(-1);
        }
      }, 50);
    }
  };

  // 安全关闭对话框
  const handleCloseDialog = () => {
    if (!isMounted.current) return;

    try {
      // 先关闭对话框
      setShowEditDialog(false);

      // 延迟重置编辑索引，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          setCurrentEditIndex(-1);
        }
      }, 50);
    } catch (error) {
      console.error('关闭对话框失败:', error);
    }
  };

  // 处理拖拽排序
  const handleMoveItem = (dragIndex: number, hoverIndex: number) => {
    if (!isMounted.current) return;

    try {
      const dragItem = carouselItems[dragIndex];
      if (!dragItem) return;

      // 创建新数组，避免直接修改原数组
      const newItems = [...carouselItems];

      // 删除拖拽项
      newItems.splice(dragIndex, 1);

      // 在新位置插入拖拽项
      newItems.splice(hoverIndex, 0, dragItem);

      // 先更新本地状态
      setCarouselItems(newItems);

      // 使用setTimeout延迟更新全局状态，避免状态冲突
      setTimeout(() => {
        if (isMounted.current) {
          // 深拷贝，避免引用问题
          props.handleOptions(JSON.parse(JSON.stringify(newItems)));
          // toast.success('排序成功');
        }
      }, 0);
    } catch (error) {
      console.error('拖拽排序失败:', error);
      toast.error('拖拽排序失败');
    }
  };

  // 打开批量编辑对话框
  const handleBatchEdit = () => {
    if (!isMounted.current) return;

    try {
      // 准备批量编辑数据，为每个项目添加序号
      const batchData = carouselItems.map((item, index) => ({
        ...item,
        id: index + 1 // 添加ID用于CRUD
        // 转换blank为下拉选择的值
        // linkType: item.blank ? 'external' : 'internal'
      }));
      console.log('准备批量编辑数据:', batchData);

      // 先设置批量编辑数据
      setBatchItems(batchData);

      // 使用setTimeout延迟显示对话框，延长时间以确保状态更新完成
      setTimeout(() => {
        if (isMounted.current) {
          // 再次检查确保batchItems已正确设置
          console.log('打开对话框时的数据状态:', batchData.length);
          setShowBatchEditDialog(true);
        }
      }, 100); // 增加延时确保状态更新
    } catch (error) {
      console.error('打开批量编辑对话框失败:', error);
      toast.error('打开批量编辑对话框失败');
    }
  };

  // 批量编辑对话框配置
  const getBatchEditDialogSchema = () => {
    let dataOption: any[] = [];
    console.log('准备获取批量编辑对话框配置', batchItems);
    try {
      // 复制一份数据，避免直接引用state可能导致的问题
      const safeItems = JSON.parse(JSON.stringify(batchItems || []));
      console.log('将要传递给CRUD的安全数据:', safeItems);

      return {
        type: 'dialog',
        title: '批量设置',
        size: 'lg',
        closeOnEsc: true,
        closeOnOutside: false,
        className: 'carousel-batch-edit-dialog',
        data: {
          items: safeItems
          // 添加一个时间戳，强制AMIS每次打开对话框时重新渲染数据
          // _timestamp: Date.now()
        },
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'close'
          },
          {
            type: 'button',
            label: '确认',
            level: 'primary',
            actionType: 'submit',
            // 直接将确认按钮与表单关联
            target: 'batch-edit-form'
          }
        ],
        body: {
          type: 'form',
          id: 'batch-edit-form',
          submitOnChange: false,
          submitText: '',
          wrapWithPanel: false,
          onSubmit: (values: any) => {
            // 获取表单中的数据
            const formData = values.items || [];
            if (Array.isArray(formData) && formData.length > 0) {
              // 直接转换并保存数据
              const newCarouselItems = formData.map((item: any) => ({
                image: item.image || '',
                title: item.title || '',
                href: item.href || '',
                linkType: item.linkType || 'external',
                internalPage: item.internalPage || '',
                internalName: item.internalName || ''
                // blank: item.linkType === 'external'
                // blank: true
              }));

              // 先关闭对话框
              setShowBatchEditDialog(false);

              // 更新当前组件状态
              setCarouselItems(newCarouselItems);

              // 更新父组件状态
              props.handleOptions(JSON.parse(JSON.stringify(newCarouselItems)));

              toast.success('批量更新成功');
            } else {
              toast.error('没有获取到有效的表单数据');
            }

            return false;
          },
          body: [
            {
              type: 'crud',
              name: 'items',
              source: '${items}',
              syncLocation: false,
              api: null,
              id: 'batch-edit-crud',
              perPage: 100,
              headerToolbar: [],
              footerToolbar: [
                {
                  type: 'pagination',
                  align: 'right'
                }
              ],
              columns: [
                {
                  name: 'id',
                  label: '序号',
                  width: 60
                },
                {
                  name: 'title',
                  label: '图片标题',
                  // quickEdit: {
                  type: 'static',
                  // placeholder: '请输入标题',
                  // saveImmediately: true,
                  mode: 'inline'
                  // }
                },
                {
                  name: 'image',
                  label: '图片地址',
                  // quickEdit: {
                  type: 'static',
                  // placeholder: '请输入图片URL',
                  // saveImmediately: true,
                  mode: 'inline'
                  // }
                },
                // {
                //   label: '链接类型',
                //   name: 'linkType',
                //   type: 'tpl',
                //   tpl: "${linkType&&linkType === 'internal'? '打开内部页面' : '打开外部链接'}",
                //   static: true
                // },

                {
                  type: 'static',
                  name: 'linkType',
                  label: '链接类型',
                  tpl: '${linkType === "internal" ? "打开内部页面" : "打开外部链接"}'
                },
                // {
                //   type: 'static',
                //   name: 'internalPage',
                //   label: '内部页面',
                //   visibleOn: '${linkType === "internal"}'
                // },
                {
                  type: 'static',
                  name: 'href',
                  label: '链接地址',
                  tpl: '${linkType === "external"? href : internalName}'
                },
                {
                  type: 'operation',
                  label: '操作',
                  width: 180,
                  buttons: [
                    {
                      type: 'button',
                      label: '查看',
                      level: 'link',
                      actionType: 'dialog',
                      dialog: {
                        title: '查看轮播图',
                        body: {
                          type: 'form',
                          body: [
                            {
                              type: 'static',
                              name: 'title',
                              label: '图片标题'
                            },
                            {
                              type: 'static-image',
                              name: 'image',
                              label: '图片预览',
                              thumbMode: 'contain',
                              thumbRatio: '16:9'
                            },
                            {
                              type: 'static',
                              name: 'linkType',
                              label: '链接类型',
                              tpl: '${linkType === "internal" ? "打开内部页面" : "打开外部链接"}'
                            },
                            // {
                            //   type: 'static',
                            //   name: 'internalPage',
                            //   label: '内部页面',
                            //   visibleOn: '${linkType === "internal"}'
                            // },
                            {
                              type: 'static',
                              name: 'href',
                              label: '链接地址',
                              tpl: '${linkType === "external"? href : internalName}'
                              // visibleOn: '${linkType === "external"}'
                            }
                          ]
                        }
                      }
                    },
                    {
                      type: 'button',
                      label: '编辑',
                      level: 'link',
                      actionType: 'dialog',
                      dialog: {
                        title: '编辑轮播图',
                        size: 'md',
                        body: {
                          type: 'form',
                          wrapWithPanel: false,
                          submitText: '确定',
                          body: [
                            {
                              type: 'hidden',
                              name: 'id'
                            },
                            {
                              type: 'input-text',
                              name: 'title',
                              label: '图片标题',
                              placeholder: '请输入标题'
                            },
                            {
                              type: 'input-text',
                              name: 'image',
                              label: '图片地址',
                              required: true,
                              placeholder: '请输入图片URL'
                            },
                            {
                              type: 'select',
                              name: 'linkType',
                              label: '链接类型',
                              value: 'external',
                              options: [
                                {
                                  label: '打开内部页面',
                                  value: 'internal'
                                },
                                {
                                  label: '打开外部链接',
                                  value: 'external'
                                }
                              ]
                            },
                            {
                              type: 'select',
                              name: 'internalPage',
                              label: '选择页面',
                              source: {
                                method: 'GET',
                                url: `/admin-api/system/application-page-and-class/list?applicationId=${props.appId}&applicantOrBackend=1`,
                                adaptor: (payload: any) => {
                                  // 过滤type=1的数据
                                  const filteredData = payload.data.filter(
                                    (item: any) => item.type === 1
                                  );
                                  const options = filteredData.map(
                                    (item: any) => ({
                                      label: item.name,
                                      value: item.id
                                    })
                                  );
                                  dataOption = options;
                                  return {
                                    ...payload,
                                    data: {
                                      options: options
                                    }
                                  };
                                }
                              },
                              onChange: (
                                value: any,
                                oldValue: any,
                                model: any,
                                form: any
                              ) => {
                                if (!value) return;

                                // 查找选中的数据源项
                                let selectedOption = dataOption.find(
                                  item => item.value == value
                                );
                                console.log('选中的数据源项2:', selectedOption);
                                if (selectedOption && selectedOption.label) {
                                  // 保存关联的数据表ID
                                  form.setValueByName(
                                    'internalName',
                                    selectedOption.label
                                  );
                                }
                              },
                              visibleOn: 'this.linkType === "internal"'
                            },
                            {
                              type: 'input-text',
                              name: 'href',
                              label: '链接地址',
                              placeholder: '请输入链接地址',
                              visibleOn: 'this.linkType !== "internal"'
                            },
                            {
                              type: 'hidden',
                              name: 'internalName'
                            }
                          ],
                          onSubmit: (values: any, action: any) => {
                            try {
                              console.log('编辑表单提交:', values);
                              // 本地更新数据
                              const updatedItems = batchItems.map(item => {
                                if (item.id === values.id) {
                                  return {...item, ...values};
                                }
                                return item;
                              });

                              // 更新状态
                              setBatchItems(updatedItems);

                              console.log('编辑成功后的数据:', updatedItems);
                              setRefreshListKey(prev => prev + 1);
                              // toast.success('编辑成功');

                              // 关闭对话框
                              return true;
                            } catch (error) {
                              console.error('编辑处理失败:', error);
                              toast.error('编辑失败');
                              return false;
                            }
                          }
                        }
                      }
                    },
                    {
                      type: 'button',
                      label: '删除',
                      level: 'link',
                      className: 'text-danger',
                      // confirmText: '确认要删除该轮播图吗？',
                      onClick: (e: any, props: any) => {
                        try {
                          console.log('删除操作，数据:', props);
                          const row = props?.data;

                          if (!row || row.id === undefined) {
                            toast.error('无法获取行数据');
                            return;
                          }

                          console.log('删除操作，数据:', row);

                          // 设置要删除的项ID并显示确认对话框
                          setDeleteItemId(row.id);
                          setDeleteRow(row);
                          setShowConfirmDialog(true);
                          // 检查是否至少保留一项
                          // if (batchItems.length <= 1) {
                          //   toast.error('至少保留一个轮播项');
                          //   return;
                          // }

                          // 使用React状态更新方法来修改数据
                          // const newItems = batchItems.filter(
                          //   item => item.id !== row.id
                          // );
                          // setBatchItems(newItems);
                          // setRefreshListKey(prev => prev + 1);

                          // toast.success('删除成功');
                        } catch (error) {
                          console.error('删除操作失败:', error);
                          toast.error('删除失败：' + (error as Error).message);
                        }
                      }
                    }
                  ]
                }
              ]
            }
          ]
        }
      };
    } catch (error) {
      console.error('获取批量编辑对话框配置失败:', error);
      return {};
    }
  };

  // 编辑对话框配置
  const getEditDialogSchema = () => {
    try {
      // 每次获取时创建新的schema对象，避免引用问题
      let currentItem = null;
      if (currentEditIndex >= 0 && currentEditIndex < carouselItems.length) {
        currentItem = JSON.parse(
          JSON.stringify(carouselItems[currentEditIndex])
        );
      }
      let initialInternalPage = currentItem.internalPage || '';
      let initialInternalName = currentItem.internalName || '';

      if (!currentItem) {
        currentItem = {
          image: '',
          title: '',
          href: '',
          internalPage: '',
          internalName: '',
          linkType: 'external'
          // blank: true
        };
      }

      // 确保blank值正确转换为radios组件需要的格式
      // const blankValue = currentItem.blank ? 'external' : 'internal';

      return {
        type: 'dialog',
        title: '轮播页配置',
        closeOnEsc: true,
        closeOnOutside: false,
        data: {
          ...currentItem,
          internalPage: initialInternalPage,
          internalName: initialInternalName
        },
        body: {
          type: 'form',
          submitText: '确认',
          onSubmit: handleSaveEdit,
          body: [
            {
              type: 'input-text',
              name: 'title',
              label: '图片标题',
              placeholder: '请输入标题',
              value: currentItem.title || ''
            },
            {
              type: 'input-text',
              name: 'image',
              label: '图片地址',
              required: true,
              placeholder: '请输入图片URL',
              value: currentItem.image || ''
            },
            {
              type: 'radios',
              name: 'linkType',
              label: '链接类型',
              mode: 'horizontal',
              value: currentItem.linkType || 'external',
              options: [
                {
                  label: '打开内部页面',
                  value: 'internal'
                },
                {
                  label: '打开外部链接',
                  value: 'external'
                }
              ]
              // ,
              // onChange: (value: string) => {
              //   return {
              //     blank: value === 'external'
              //   };
              // }
            },
            {
              type: 'select',
              name: 'internalPage',
              label: '选择页面',
              source: {
                method: 'GET',
                url: `/admin-api/system/application-page-and-class/list?applicationId=${props.appId}&applicantOrBackend=1`,
                adaptor: function (payload: any) {
                  // 过滤type=1的数据
                  const filteredData = payload.data.filter(
                    (item: any) => item.type === 1
                  );
                  const options = filteredData.map((item: any) => ({
                    label: item.name,
                    value: item.id
                  }));
                  return {
                    ...payload,
                    data: {
                      options: options
                    }
                  };
                }
              },
              visibleOn: 'this.linkType === "internal"'
            },
            {
              type: 'input-text',
              name: 'href',
              label: '链接地址',
              placeholder: '请输入链接地址',
              value: currentItem.href || '',
              visibleOn: 'this.linkType !== "internal"'
            }
          ]
        }
      };
    } catch (error) {
      console.error('获取编辑对话框配置失败:', error);
      return {};
    }
  };

  // 渲染前检查数据有效性
  const safeCarouselItems = Array.isArray(carouselItems) ? carouselItems : [];

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="carousel-data-box">
        <div className="carousel-items-container">
          <div className="carousel-items-list">
            {safeCarouselItems.map((item, index) => (
              <CarouselItemRow
                key={`carousel-item-${index}`}
                item={item}
                index={index}
                onEdit={handleEditItem}
                onDelete={handleDeleteItem}
                onMove={handleMoveItem}
              />
            ))}
          </div>

          <div className="carousel-footer">
            <Button level="default" size="sm" onClick={handleAddItem}>
              <i className="fa fa-plus"></i> 添加轮播页
            </Button>
            <Button level="default" size="sm" onClick={handleBatchEdit}>
              <i className="fa fa-list"></i> 批量编辑
            </Button>
          </div>
        </div>

        {showEditDialog && (
          <AMISRenderer
            schema={getEditDialogSchema()}
            show={showEditDialog}
            onClose={handleCloseDialog}
          />
        )}

        {showBatchEditDialog && (
          <AMISRenderer
            key={refreshListKey}
            schema={getBatchEditDialogSchema()}
            show={showBatchEditDialog}
            onClose={() => setShowBatchEditDialog(false)}
          />
        )}

        {showConfirmDialog && (
          <AMISRenderer
            show={showConfirmDialog}
            schema={{
              type: 'dialog',
              title: '删除轮播图',
              body: {
                type: 'tpl',
                tpl: '确定要删除该轮播图吗？'
              },
              actions: [
                {
                  type: 'button',
                  label: '取消',
                  actionType: 'close'
                },
                {
                  type: 'button',
                  label: '确认',
                  level: 'danger',
                  onClick: handleConfirmDelete
                }
              ]
            }}
            onClose={() => setShowConfirmDialog(false)}
          />
        )}
      </div>
    </DndProvider>
  );
};

export default CarouselDataInteraction;
