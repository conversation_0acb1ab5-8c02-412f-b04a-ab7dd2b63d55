export const EditTeamNameDialog = (show: boolean, currentTeamName?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑团队名称</b>'
    },
    showCloseButton: false,
    data: {
      name: currentTeamName
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入团队名称'
      },
      body: [
        {
          type: 'input-text',
          name: 'name',
          placeholder: '团队名称',
          required: true,
          maxLength: 50,
          validations: {
            maxLength: 50
          },
          inputClassName: 'team-name-input',
          labelClassName: 'team-name-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
};
