// 新增 body数据
const findCreateBody = (obj: any) => {
  if (obj && obj.body && obj.body.length > 0) {
    const firstBodyItem = obj.body[0];
    if (firstBodyItem.headerToolbar && firstBodyItem.headerToolbar.length > 0) {
      for (const toolbarItem of firstBodyItem.headerToolbar) {
        if (
          toolbarItem.actionType &&
          toolbarItem.editorSetting.behavior == 'create'
        ) {
          for (const formitem of toolbarItem[toolbarItem.actionType].body) {
            if (formitem.type === 'form') {
              return formitem.body;
            }
          }
        }
      }
    }
  }
  return [];
};

// 新增
const replacedCreateBody = (obj: any, body: any) => {
  if (obj && obj.body && obj.body.length > 0) {
    const firstBodyItem = obj.body[0];
    if (firstBodyItem.headerToolbar && firstBodyItem.headerToolbar.length > 0) {
      for (const toolbarItem of firstBodyItem.headerToolbar) {
        if (
          toolbarItem.actionType &&
          toolbarItem.editorSetting.behavior == 'create'
        ) {
          for (const formitem of toolbarItem[toolbarItem.actionType].body) {
            if (formitem.type === 'form') {
              formitem.body = body;
              return obj;
            }
          }
        }
      }
    }
  }
  return obj;
};

// 编辑
const replacedUpdateBody = (obj: any, body: any) => {
  if (obj && obj.body && obj.body.length > 0) {
    const firstBodyItem = obj.body[0];

    if (firstBodyItem.columns && firstBodyItem.columns.length > 0) {
      for (const columnItem of firstBodyItem.columns) {
        if (
          columnItem.type == 'operation' &&
          columnItem.buttons &&
          columnItem.buttons.length > 0
        ) {
          for (const buttonItem of columnItem.buttons) {
            if (
              buttonItem.actionType &&
              buttonItem.editorSetting.behavior == 'update'
            ) {
              for (const formitem of buttonItem[buttonItem.actionType].body) {
                if (formitem.type === 'form') {
                  formitem.body = body;
                  return obj;
                }
              }
            }
          }
        }
      }
    }
  }
  return obj;
};

let defaultColumns = [
  {
    label: 'ID',
    type: 'input-text',
    name: 'id',
    sortable: true,
    static: true
  },
  {
    label: '创建者',
    type: 'user-select',
    name: 'creator',
    sortable: true,
    static: true
  },
  {
    label: '创建时间',
    type: 'input-datetime',
    name: 'createTime',
    sortable: true,
    static: true,
    valueFormat: 'x'
  },
  {
    label: '更新者',
    type: 'user-select',
    name: 'updater',
    sortable: true,
    static: true
  },
  {
    label: '更新时间',
    type: 'input-datetime',
    name: 'updateTime',
    sortable: true,
    static: true,
    valueFormat: 'x'
  }
];

// 展示
const replacedcolumns = (obj: any, body: any) => {
  if (obj && obj.body && obj.body.length > 0) {
    const firstBodyItem = obj.body[0];

    if (firstBodyItem.columns && firstBodyItem.columns.length > 0) {
      let columnsbody = JSON.parse(JSON.stringify(body));
      for (let i = 0; i < columnsbody.length; i++) {
        columnsbody[i].sortable = true;
        columnsbody[i].static = true;
        columnsbody[i].placeholder = '--';
      }
      let columns = JSON.parse(JSON.stringify(defaultColumns));
      columns.splice(1, 0, ...columnsbody);
      let columnsList = firstBodyItem.columns.slice(-1);
      columnsList.splice(0, 0, ...columns);

      firstBodyItem.columns = columnsList;
      return obj;
    }
  }
  return obj;
};
// 查看详情
const replacedViewBody = (obj: any, body: any) => {
  if (obj && obj.body && obj.body.length > 0) {
    const firstBodyItem = obj.body[0];
    if (firstBodyItem.columns && firstBodyItem.columns.length > 0) {
      for (const columnItem of firstBodyItem.columns) {
        if (
          columnItem.type == 'operation' &&
          columnItem.buttons &&
          columnItem.buttons.length > 0
        ) {
          for (const buttonItem of columnItem.buttons) {
            if (
              buttonItem.actionType &&
              buttonItem.editorSetting.behavior == 'view'
            ) {
              for (const formitem of buttonItem[buttonItem.actionType].body) {
                if (formitem.type === 'form') {
                  let columns = JSON.parse(JSON.stringify(defaultColumns));
                  for (let i = 0; i < columns.length; i++) {
                    columns[i].type = 'input-text';
                  }
                  columns.splice(1, 0, ...body);
                  formitem.body = columns;
                  return obj;
                }
              }
            }
          }
        }
      }
    }
  }
  return obj;
};

// 去掉地址选择器多余字段
const replacedAddressBody = (obj: any) => {
  if (obj && obj.body && obj.body.length > 0) {
    const firstBodyItem = obj.body[0];
    if (firstBodyItem.columns && firstBodyItem.columns.length > 0) {
      // 处理新增按钮
      if (
        firstBodyItem.headerToolbar &&
        firstBodyItem.headerToolbar.length > 0
      ) {
        for (const toolbarItem of firstBodyItem.headerToolbar) {
          if (
            (toolbarItem.actionType &&
              toolbarItem.editorSetting.behavior == 'create') ||
            (toolbarItem.actionType &&
              toolbarItem.editorSetting.behavior == 'update')
          ) {
            console.log('toolbarItem2', toolbarItem);
            for (const formitem of toolbarItem[toolbarItem.actionType].body) {
              if (formitem.type === 'form') {
                formitem.api.requestAdaptor = (api: any) => {
                  const data = {...api.data};
                  Object.keys(data).forEach(key => {
                    if (key.endsWith('_city') || key.endsWith('_detail')) {
                      delete data[key];
                    }
                  });
                  return {...api, data};
                };
              }
            }
          }
        }
      }

      // 处理编辑按钮
      for (const columnItem of firstBodyItem.columns) {
        if (
          columnItem.type == 'operation' &&
          columnItem.buttons &&
          columnItem.buttons.length > 0
        ) {
          for (const buttonItem of columnItem.buttons) {
            if (
              buttonItem.actionType &&
              buttonItem.editorSetting &&
              buttonItem.editorSetting.behavior == 'update'
            ) {
              for (const formitem of buttonItem[buttonItem.actionType].body) {
                if (formitem.type === 'form') {
                  console.log('formitem2', formitem);
                  //如果formitem.api是字符串就不新增
                  if (typeof formitem.api === 'string') {
                    continue;
                  }
                  formitem.api.requestAdaptor = (api: any) => {
                    const data = {...api.data};
                    Object.keys(data).forEach(key => {
                      if (key.endsWith('_city') || key.endsWith('_detail')) {
                        delete data[key];
                      }
                    });
                    return {...api, data};
                  };
                }
              }
            }
          }
        }
      }
    }
    if (firstBodyItem.api) {
      // 添加responseAdaptor处理返回数据
      firstBodyItem.api.adaptor = (payload: any) => {
        console.log('payload', payload);
        const data = {...payload.data};
        console.log('data', data && data.list && Array.isArray(data.list));
        // 处理列表数据
        if (data && data.list && Array.isArray(data.list)) {
          data.list = data.list.map((item: any) => {
            // 处理postIds字段
            if (item.postIds) {
              console.log('item.postIds', item.postIds);
              // 如果是字符串格式的数组，去掉方括号
              if (
                typeof item.postIds === 'string' &&
                item.postIds.startsWith('[') &&
                item.postIds.endsWith(']')
              ) {
                try {
                  // 解析JSON字符串为数组
                  const parsed = JSON.parse(item.postIds);
                  if (Array.isArray(parsed)) {
                    // 将数组转为逗号分隔的字符串
                    item.postIds = parsed.join(',');
                  }
                } catch (e) {
                  console.error('解析postIds失败', e);
                  item.postIds = '';
                }
              }
            }
            return item;
          });
        }

        return {...payload, data};
      };
    }
    return obj;
  }
  return obj;
};

export {
  findCreateBody, // 新增 body数据
  replacedCreateBody, // 新增
  replacedUpdateBody, // 编辑
  replacedcolumns, // 展示
  replacedViewBody, //查看详情
  replacedAddressBody // 去掉地址选择器多余字段
};
