import React, {FC, useState, useRef} from 'react';
import './index.scss';
import {Button, Tabs, Tab, toast, Avatar} from 'amis';
import itemShow from './image/itemShow.png';
import {
  javaApplicationList,
  updateTeamProject,
  updateIcon,
  getTeamMember,
  selectMember
} from '@/utils/api/api';
import {EditTeamNameDialog} from '@/utils/schemaDataSet/EditTeamNameDialog';
import {EditTeamDescribeDialog} from '@/utils/schemaDataSet/EditTeamDescribeDialog';
import {EditTeamMember} from '@/utils/schemaDataSet/EditTeamMember';
import {GetTeamMember} from '@/utils/schemaDataSet/GetTeamMember';
import AMISRenderer from '@/component/AMISRenderer';
import {CreateProjectSchema} from '@/utils/schemaDataSet/CreateProjectPopup';
import {DeleteTeamSecondaryConfirm} from '@/utils/schemaDataSet/DeleteTeamSecondaryConfirm';
import {utilsSetFromAppbuild} from '@/utils/routeUtils';
import {javaApplicationCreate, delTeam} from '@/utils/api/api';

const TeamMain: FC<any> = (props: any) => {
  const store = props.store;

  const [totalAll, setTotalAll] = React.useState(0);

  // 是否打开创建应用弹窗 createApplyPopup
  const [openCreateApplyPopup, setOpenCreateApplyPopup] =
    React.useState<boolean>(false);
  // 是否打开删除团队弹窗
  const [delTeamPopup, setDelTeamPopup] = React.useState<boolean>(false);

  // 创建项目弹窗
  const [createProjectOpen, setCreateProjectOpen] =
    React.useState<boolean>(false);
  // 创建项目ID
  const [createProjectId, setCreateProjectId] = React.useState<any>('');

  // 添加一个状态来跟踪成员列表的刷新
  const [memberListKey, setMemberListKey] = React.useState(0);

  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="teamMain-main-tabsToolbar"></div>;
  };

  // 从URL查询参数中获取activeTabKey
  const getActiveTabFromUrl = () => {
    // 使用props.location获取查询参数
    const search = props.location?.search || '';
    const urlParams = new URLSearchParams(search);
    const tabKey = urlParams.get('activeTabKey');

    // 映射URL参数到标签页索引
    const tabMapping: {[key: string]: string} = {
      app: '1',
      myapp: '2',
      member: '3',
      setting: '4',
      trash: '5'
    };

    return tabKey && tabMapping[tabKey] ? tabMapping[tabKey] : '1'; // 默认为应用标签
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromUrl());
  //团队的信息
  const [teamInfo, setTeamInfo] = React.useState<any>(false);
  // 团队的 各个项目
  const [teamGroupList, setTeamGroupList] = React.useState<any>([]);
  // 团队的 回收站列表
  const [teamTrashList, setTeamTrashList] = React.useState<any>([]);
  // 团队的 我创建的 应用列表
  const [myAppList, setMyAppList] = React.useState<any[]>([]);
  // 团队的 成员列表
  const [roleId, setroleId] = React.useState(0);
  // 编辑团队名称
  const [showEditTeamNameDialog, setshowEditTeamNameDialog] = useState(false);
  // 编辑团队描述
  const [showEditTeamDescribeDialog, setshowEditTeamDescribeDialog] =
    useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const [selectMemberIds, setSelectMemberIds] = React.useState(null);

  const [showAddMemberDialog, setShowAddMemberDialog] = React.useState(false);

  const inviteMemberPopup = () => {
    setShowAddMemberDialog(true);
  };

  React.useEffect(() => {
    setTeamGroupList([]);
    setMyAppList([]);
    if (props.dataInfo.id) {
      setTeamInfo(props.dataInfo);
      if (props.dataInfo.children) {
        setTeamGroupList(props.dataInfo.children);
      }
    }
  }, [props.dataInfo]);

  // 前往应用页面
  const handleInAppPage = (app: any) => {
    // console.log(app);
    utilsSetFromAppbuild();
    props.history.push(`/app${app.id}/admin`);
  };

  // 添加 handleMoreClick 方法
  const handleAppListClick = (projectId: string) => {
    // 根据项目 ID 生成目标页面的 URL
    const targetUrl = `/platform/team${props.dataInfo.id}/project${projectId}`; // 这里假设目标页面的 URL 格式为 /project/:projectId/more
    // 使用 props.history.push 进行页面跳转
    props.history.push(targetUrl);
  };

  const handleMyAppListClick = (projectId: string) => {
    // 根据项目 ID 生成目标页面的 URL
    const targetUrl = `/platform/team${props.dataInfo.id}/project${projectId}?activeTabKey=myapp`; // 这里假设目标页面的 URL 格式为 /project/:projectId/more
    // 使用 props.history.push 进行页面跳转
    props.history.push(targetUrl);
  };

  // 通用的更新项目方法
  const updateProfile = (
    data: any,
    fieldName: string,
    value: any,
    closeDialog: () => void
  ) => {
    console.log(`edit ${fieldName}:`, value);
    data.id = props.dataInfo.id;
    updateTeamProject(data)
      .then(res => {
        console.log('update user profile:', res);
        if (res.code === 0) {
          setTeamInfo({
            ...teamInfo,
            [fieldName]: value
          });
          closeDialog();
        } else {
          toast.error(res.message);
        }
      })
      .catch(err => {
        toast.error(err.message);
      });
  };

  const handleEditName = (values: any) => {
    const name = values[0]?.name;
    if (name) {
      updateProfile({name: name}, 'name', name, () => {
        setshowEditTeamNameDialog(false);
        // 确保 handleGetTeamAndProjectList 存在
        if (typeof props.handleGetTeamAndProjectList === 'function') {
          props.handleGetTeamAndProjectList();
        }
      });
    }
  };

  const handleEditdescribe = (values: any) => {
    // 处理编辑项目名称的逻辑
    const description = values[0]?.description;
    if (description) {
      updateProfile(
        {description: description},
        'description',
        description,
        () => setshowEditTeamDescribeDialog(false)
      );
    }
  };

  React.useEffect(() => {
    const data = {
      teamOrProjectOrApplicationId: props.dataInfo.id,
      userId: props.store.userInfo?.id,
      pageNo: 1,
      pageSize: 10,
      type: '1'
    };
    getTeamMember(data)
      .then(res => {
        if (res.data.list > 0 && res.data.list[0].role > 0) {
          setroleId(res.data.list[0].role);
        }
      })
      .catch(error => {
        console.error('获取团队成员失败:', error);
      });
  }, []);

  // 处理头像上传
  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadFile(file, 'file', updateIcon, {
        allowedTypes: /^image\/(jpeg|png)$/,
        maxSize: 2 * 1024 * 1024,
        errorMessages: {
          typeError: '只支持 jpg、png 格式的图片',
          sizeError: '图片大小不能超过 2M'
        },
        extraParams: {
          id: props.dataInfo.id
        }
      });
    }
    // 清空 input 的值，这样同一个文件可以重复选择
    event.target.value = '';
  };

  // 删除团队操作
  // id	是	string	团队id
  // team_name	是	string	团队名称
  const onDelTeam = (id: string) => {
    let data = {
      id: id
      // team_name: ''
    };
    delTeam(data)
      .then((res: any) => {
        setDelTeamPopup(false);
        if (res.code === 0) {
          props.updateMenuData();
          toast.success('已删除该团队');
          // 删除团队后导航到首页，取消显示团队页面
          props.history.push('/appbuild/home');
        } else {
          console.error(res.msg);
        }
      })
      .catch((err: any) => {
        console.error(err);
      });
  };
  // 二次确认删除弹窗- 确认处理
  const onDelTeamConfirm = (val: any) => {
    if (val[0].text == teamInfo.name) {
      onDelTeam(teamInfo.id);
    } else {
      toast.error('删除失败,请输入正确的团队名称');
    }
  };

  const openCreateProjectPopup = (id: any) => {
    setCreateProjectId(id);
    setCreateProjectOpen(true);
  };

  // 创建项目完成
  const createProject = (val: any) => {
    setCreateProjectOpen(false);
    props.updateMenuData();
  };

  // 通用的文件上传方法
  const uploadFile = (
    file: File,
    fieldName: string,
    uploadApi: (formData: FormData, params?: any) => Promise<any>,
    options: {
      maxSize?: number;
      allowedTypes?: RegExp;
      errorMessages?: {
        typeError?: string;
        sizeError?: string;
      };
      extraParams?: any;
    } = {}
  ) => {
    // 默认配置
    const defaultOptions = {
      maxSize: 2 * 1024 * 1024, // 默认2MB
      allowedTypes: /^image\/(jpeg|png)$/, // 默认jpg和png
      errorMessages: {
        typeError: '只支持 jpg、png 格式的图片',
        sizeError: '图片大小不能超过 2M'
      }
    };

    const config = {...defaultOptions, ...options};

    // 验证文件类型
    if (!file.type.match(config.allowedTypes)) {
      store.alert(config.errorMessages.typeError || '文件类型不支持');
      return;
    }

    // 验证文件大小
    if (file.size > config.maxSize) {
      store.alert(config.errorMessages.sizeError || '文件大小超出限制');
      return;
    }

    // 创建 FormData
    const formData = new FormData();
    formData.append(fieldName, file);

    // 临时预览（如果是图片）
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = e => {
        setTeamInfo({
          ...teamInfo,
          [fieldName]: e.target?.result as string
        });
      };
      reader.readAsDataURL(file);
    }

    // 调用上传API
    return uploadApi(formData, config.extraParams)
      .then(res => {
        if (res.code === 0) {
          teamInfo.icon = res.data; //res.data
          updateTeamProject(teamInfo);
          setTeamInfo({
            ...teamInfo
            //icon: res.data.url
          });
        } else {
          console.log('上传失败');
          toast.error(res.message);
        }
      })
      .catch(err => {
        toast.error(err.message);
        throw err;
      });
  };


  // 获取应用列表页面 javaApplicationList
  const getJavaApplicationList = async (item: any, index: number) => {
    let data = {
      projectId: item.id,
      pageNo: 1,
      pageSize: 10
    };
    let res = await javaApplicationList(data);
    let list = teamGroupList;
    if (res.code == 0 && res.data?.list) {
      list[index].apply = res.data.list;
      list[index].total = res.data.total;
    } else {
      list[index].apply = [];
      list[index].total = 0; // 设置默认值
    }
    setTeamGroupList(list);
  };

  React.useEffect(() => {
    if (teamGroupList.length > 0) {
      let list = teamGroupList;
      for (let i = 0; i < list.length; i++) {
        getJavaApplicationList(list[i], i);
      }
    }
  }, [teamGroupList]);

  // 获取我创建的应用列表页面 javaMyApplicationList
  const getMyJavaApplicationList = async (item: any, index: number) => {
    try {
      // 请求参数
      let data = {
        projectId: item.id, // 使用传入项目的 ID
        pageNo: 1, // 分页页码
        pageSize: 10, // 每页条数
        creator: props.store.userInfo?.id // 当前用户 ID
      };

      // 调用 API
      let res = await javaApplicationList(data);

      // 处理返回结果
      if (res.code === 0 && res.data?.list) {
        let list = myAppList;
        if (!list[index]) {
          list[index] = {...item, apply: res.data.list}; // 确保项目对象存在
        } else {
          list[index].apply = res.data.list;
          list[index].total = res.data.total;
        }
        setMyAppList([...list]); // 使用新的数组触发重新渲染
      } else {
        let list = myAppList;
        if (!list[index]) {
          list[index] = {...item, apply: []}; // 确保项目对象存在
        } else {
          list[index].apply = [];
          list[index].total = 0; // 设置默认值
        }
        setMyAppList([...list]); // 使用新的数组触发重新渲染
      }
    } catch (error) {
      console.error('获取我创建的应用列表失败:', error);
    }
  };

  // 在 useEffect 中调用
  React.useEffect(() => {
    if (teamGroupList.length > 0) {
      let list = teamGroupList;
      for (let i = 0; i < list.length; i++) {
        getMyJavaApplicationList(list[i], i);
      }
    }
  }, [teamGroupList]);

  // 处理标签页切换，更新URL
  const handleTabChange = (key: string) => {
    // 先更新本地状态，确保UI立即响应
    setActiveTab(key);

    // 映射标签页索引到URL参数
    const tabMapping: {[key: string]: string} = {
      '1': 'app',
      '2': 'myapp',
      '3': 'member',
      '4': 'setting',
      '5': 'trash'
    };

    // 使用props.history更新URL，但不触发组件重新渲染
    if (props.history) {
      const location = props.location || window.location;
      const pathname = location.pathname;

      // 创建新的查询参数
      const urlParams = new URLSearchParams(location.search);
      urlParams.set('activeTabKey', tabMapping[key]);
      // 使用replace而不是push，避免在历史记录中创建多余的条目
      props.history.replace({
        pathname,
        search: urlParams.toString()
      });
    }
  };

  // 初始化时从URL获取activeTabKey
  React.useEffect(() => {
    // 初始化时设置正确的标签页
    setActiveTab(getActiveTabFromUrl());
  }, []);

  // 监听URL变化
  React.useEffect(() => {
    // 使用props.history监听路由变化
    const unlisten = props.history?.listen(() => {
      // 当URL变化时，更新activeTab状态
      const newActiveTab = getActiveTabFromUrl();
      if (newActiveTab !== activeTab) {
        setActiveTab(newActiveTab);
      }
    });

    return () => {
      // 清理监听器
      if (unlisten) unlisten();
    };
  }, [props.history, props.location, activeTab]);

  return (
    <div className="teamMain">
      <div className="teamMain-top">
        <div className="teamMain-top-left">
          <div className="teamMain-top-left-name">{teamInfo.name}</div>
          <div className="teamMain-top-left-describe">
            {teamInfo.description}
          </div>
        </div>
        {(activeTab === '3' || props.store.userInfo?.hasAppBuildAuth) && (
          <div className="teamMain-top-right">
            <div className="teamMain-top-right-btn">
              <Button
                size="lg"
                level="primary"
                onClick={() =>
                  activeTab === '3'
                    ? inviteMemberPopup()
                    : openCreateProjectPopup(teamInfo.id)
                }
              >
                <span className="teamMain-top-right-btn-addIcon">+</span>
                <span className="teamMain-top-right-btn-name">
                  {activeTab === '3' ? '邀请成员' : '创建项目'}
                </span>
              </Button>
            </div>
          </div>
        )}
      </div>
      <div className="teamMain-main">
        {/* Tabs:配置选项卡+工具栏  toolbar*/}

        <Tabs
          mode="line"
          theme={localStorage.getItem('amis-theme') || 'cxd'}
          activeKey={activeTab}
          onSelect={handleTabChange}
          toolbar={TabsToolbar()}
          linksClassName="teamMain-main-tabsTitle"
          className="teamMain-main-tabs"
        >
          <Tab title="应用" eventKey="1">
            {teamGroupList.map((item: any, index: number) => {
              return (
                <div className="teamGroupList" key={item.id}>
                  <div className="teamGroupList-title">
                    <div className="teamGroupList-title-left">{item.name}</div>
                    <div className="teamGroupList-title-right">
                      <span
                        className="teanGroupList-title-right-span"
                        onClick={() => handleAppListClick(item.id)}
                      >
                        更多（{item.total}）
                      </span>
                      <i className="fas fa-right"></i>
                    </div>
                  </div>
                  <div className="teamMain-main-tabsAppList">
                    {item?.apply &&
                      item.apply.map((itemApply: any, indexApply: number) => {
                        return (
                          <div
                            key={itemApply.id}
                            className="teamMain-main-tabsAppList-item"
                            onClick={() => handleInAppPage(itemApply)}
                          >
                            <div className="teamMain-main-tabsAppList-item-content">
                              <div className="teamMain-main-tabsAppList-item-content-itemshow">
                                {itemApply.logo ? (
                                  itemApply.logo.startsWith('http') ? (
                                    <Avatar src={itemApply.logo} size={36} />
                                  ) : (
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: itemApply.logo || ''
                                      }}
                                    />
                                  )
                                ) : (
                                  //
                                  <img
                                    className="apply_basicSetting-item-left-icon-img"
                                    src={itemShow}
                                  />
                                )}
                              </div>
                              <div className="teamMain-main-tabsAppList-item-content-content">
                                <div className="teamMain-main-tabsAppList-item-content-content-title">
                                  {itemApply.name}
                                </div>
                                <div className="teamMain-main-tabsAppList-item-content-content-describe">
                                  {itemApply.description}
                                </div>
                              </div>
                            </div>
                            <div className="teamMain-main-tabsAppList-item-play">
                              <div className="teamMain-main-tabsAppList-item-holder"></div>
                              <div className="teamMain-main-tabsAppList-item-play-btn">
                                <i className="fa-solid fa-ellipsis teamMain-main-tabsAppList-item-play-btn-icon"></i>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              );
            })}
          </Tab>
          <Tab title="我创建的" eventKey="2">
            {myAppList.map((item: any, index: number) => {
              return (
                <div className="teamGroupList" key={item.id}>
                  <div className="teamGroupList-title">
                    <div className="teamGroupList-title-left">{item.name}</div>
                    <div className="teamGroupList-title-right">
                      <span onClick={() => handleMyAppListClick(item.id)}>
                        更多（{item.total}）
                      </span>
                      <i className="fas fa-right"></i>
                    </div>
                  </div>
                  <div className="teamMain-main-tabsAppList">
                    {item?.apply &&
                      item.apply.map((itemApply: any, indexApply: number) => {
                        return (
                          <div
                            key={itemApply.id}
                            className="teamMain-main-tabsAppList-item"
                            onClick={() => handleInAppPage(itemApply)}
                          >
                            <div className="teamMain-main-tabsAppList-item-content">
                              <div className="teamMain-main-tabsAppList-item-content-itemshow">
                                {itemApply.logo ? (
                                  itemApply.logo.startsWith('http') ? (
                                    <Avatar src={itemApply.logo} size={36} />
                                  ) : (
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: itemApply.logo || ''
                                      }}
                                    />
                                  )
                                ) : (
                                  //
                                  <img
                                    className="apply_basicSetting-item-left-icon-img"
                                    src={itemShow}
                                  />
                                )}
                              </div>
                              <div className="teamMain-main-tabsAppList-item-content-content">
                                <div className="teamMain-main-tabsAppList-item-content-content-title">
                                  {itemApply.name}
                                </div>
                                <div className="teamMain-main-tabsAppList-item-content-content-describe">
                                  {itemApply.description}
                                </div>
                              </div>
                            </div>
                            <div className="teamMain-main-tabsAppList-item-play">
                              <div className="teamMain-main-tabsAppList-item-holder"></div>
                              <div className="teamMain-main-tabsAppList-item-play-btn">
                                <i className="fa-solid fa-ellipsis teamMain-main-tabsAppList-item-play-btn-icon"></i>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              );
            })}
          </Tab>
          <Tab title="成员" eventKey="3">
            <div className="teamMain-main-memberList">
              <div className="teamMain-main-memberList-header">
                <div className="teamMain-main-memberList-header-title">
                  <span className="teamMain-main-memberList-header-title-text">
                    团队成员数量
                  </span>
                  <span className="teamMain-main-memberList-header-title-count">
                    ({totalAll})
                  </span>
                </div>
                <div className="teamMain-main-memberList-header-view">
                  <span className="teamMain-main-memberList-header-title-text">
                    {/* {props.dataInfo.creator == props.store.userInfo?.id ? '创建者视角' : '管理员和成员视角'} */}
                  </span>
                </div>
              </div>

              {showAddMemberDialog && (
                <AMISRenderer
                  show={showAddMemberDialog}
                  schema={GetTeamMember()}
                  onClose={() => {
                    setShowAddMemberDialog(false);
                    setMemberListKey(prev => prev + 1);
                  }}
                  data={{
                    teamOrProjectId: teamInfo?.id,
                    selectMemberIds: selectMemberIds,
                    type: '1'
                  }}
                />
              )}
              {roleId >= 0 ? (
                <AMISRenderer
                  key={`${teamInfo?.id}-${memberListKey}`}
                  schema={EditTeamMember(setroleId, setTotalAll)}
                  embedMode={true}
                  data={{
                    teamOrProjectId: teamInfo?.id,
                    creator: props.dataInfo.creator,
                    user_id: props.store.userInfo?.id,
                    roleId: roleId,
                    type: '1'
                  }}
                  id="memberCrud"
                />
              ) : (
                <div>Loading roleId...</div>
              )}
            </div>
          </Tab>
          <Tab title="设置" eventKey="4">
            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-name">
                  团队名称
                </div>
                <div className="teamMain-main-setCenter-left-content">
                  {teamInfo.name || '团队名称'}
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <div
                  className="teamMain-main-setCenter-right-btn"
                  onClick={() => setshowEditTeamNameDialog(true)}
                >
                  编辑资料
                </div>
              </div>
            </div>
            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-pjImage">
                  <div className="teamMain-main-setCenter-left-pjImage-image">
                    {teamInfo.icon && (
                      <img
                        className="projectMain-main-setCenter-left-pjImage-image-img"
                        src={teamInfo.icon}
                      />
                    )}
                    {/* <Avatar src={projectInfo.icon} size={48} /> */}
                  </div>
                  <div className="teamMain-main-setCenter-left-pjImage-name">
                    团队图标
                  </div>
                </div>

                <div className="teamMain-main-setCenter-left-content">
                  支持 2M 以内 JPG、PNG 图片
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  style={{display: 'none'}}
                  onChange={handleAvatarUpload}
                />
                <div
                  className="projectMain-main-setCenter-right-btn"
                  onClick={() => fileInputRef.current?.click()}
                >
                  上传图标
                </div>
              </div>
            </div>
            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-name">
                  团队描述
                </div>
                <div className="teamMain-main-setCenter-left-content">
                  {teamInfo.description || '显示在团队主页'}
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <div
                  className="teamMain-main-setCenter-right-btn"
                  onClick={() => setshowEditTeamDescribeDialog(true)}
                >
                  编辑描述
                </div>
              </div>
            </div>
            <div className="teamMain-main-setCenter">
              <div className="teamMain-main-setCenter-left">
                <div className="teamMain-main-setCenter-left-name">
                  删除团队
                </div>
                <div className="teamMain-main-setCenter-left-content">
                  永久删除团队所有应用，且不可恢复
                </div>
              </div>
              <div className="teamMain-main-setCenter-right">
                <div
                  className="teamMain-main-setCenter-right-delbtn"
                  onClick={() => {
                    setDelTeamPopup(true);
                  }}
                >
                  删除团队
                </div>
              </div>
            </div>
          </Tab>
          <Tab title="回收站" eventKey="5">
            {teamTrashList.map((item: any, index: number) => {
              return (
                <div
                  key={item.id}
                  className="teamMain-main-tabsAppList-item"
                  onClick={() => {
                    props.history.push(`/${item.route}/admin`);
                  }}
                >
                  <div className="teamMain-main-tabsAppList-item-content">
                    <div className="teamMain-main-tabsAppList-item-content-itemshow">
                      {item.logo ? (
                        <Avatar src={item.logo} shape="rounded" />
                      ) : (
                        <img
                          className="teamMain-main-tabsAppList-item-content-itemshow-img"
                          src={itemShow}
                        />
                      )}
                    </div>
                    <div className="teamMain-main-tabsAppList-item-content-content">
                      <div className="teamMain-main-tabsAppList-item-content-content-title">
                        {item.apply_name}
                      </div>
                      <div className="teamMain-main-tabsAppList-item-content-content-describe">
                        {item.depict}
                      </div>
                    </div>
                  </div>
                  <div className="teamMain-main-tabsAppList-item-play">
                    <div
                      className={`teamMain-main-tabsAppList-item-play-status ${
                        item.is_release === '1'
                          ? 'teamMain-main-tabsAppList-item-play-released'
                          : 'teamMain-main-tabsAppList-item-play-unreleased'
                      }`}
                    >
                      {item.is_release == '1' ? '已发布' : '未启用'}
                    </div>
                    <div className="teamMain-main-tabsAppList-item-play-btn">
                      <i className="fa-solid fa-ellipsis teamMain-main-tabsAppList-item-play-btn-icon"></i>
                    </div>
                  </div>
                </div>
              );
            })}
          </Tab>
        </Tabs>
      </div>
      {showEditTeamNameDialog && (
        <AMISRenderer
          show={showEditTeamNameDialog}
          onClose={() => setshowEditTeamNameDialog(false)}
          onConfirm={handleEditName}
          schema={EditTeamNameDialog(showEditTeamNameDialog, teamInfo.name)}
        />
      )}

      {showEditTeamDescribeDialog && (
        <AMISRenderer
          show={showEditTeamDescribeDialog}
          onClose={() => setshowEditTeamDescribeDialog(false)}
          onConfirm={handleEditdescribe}
          schema={EditTeamDescribeDialog(
            showEditTeamDescribeDialog,
            teamInfo.description
          )}
        />
      )}

      {/* 删除团队 - 二次确认弹窗 */}
      <AMISRenderer
        show={delTeamPopup}
        onClose={() => setDelTeamPopup(false)}
        onConfirm={(val: any) => onDelTeamConfirm(val)}
        schema={DeleteTeamSecondaryConfirm(delTeamPopup)}
      />
      {/* 创建项目弹窗：CreateProjectSchema */}
      <AMISRenderer
        show={createProjectOpen}
        onClose={() => setCreateProjectOpen(false)}
        onConfirm={(val: any) => createProject(val)}
        schema={CreateProjectSchema(createProjectId)}
      />
    </div>
  );
};

export default TeamMain;
