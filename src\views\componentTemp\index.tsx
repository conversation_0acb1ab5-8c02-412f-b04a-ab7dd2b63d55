import React, {FC} from 'react';
import './index.scss';
import {Button, toast, Modal, Input, Select, responseAdaptor} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj, createCustompageObj} from '@/utils/schemaPageTemplate/createPageObjs';
import fileAudio from '@/image/common_icons/file_audio.png';
import filePdf from '@/image/common_icons/file_pdf.png';
import fileWord from '@/image/common_icons/file_word.png';
import fileZip from '@/image/common_icons/file_zip.png';
import type_folder from '@/image/page_icons/type_folder.png';
import PagePanDataSettings from '@/component/PagePanDataSettings/index';

import {
  getFilePage, //获取表单数据
  getFormDataPage,
  createFormData,
  getComponentClassificationPage,
  createComponentClassification,
  updateComponentClassification,
  deleteComponentClassification
} from '@/utils/api/api';
import PagePanSettings from '@/component/PagePanSettings';
import { height, visibleOn } from '@/editor/EChartsEditor/Common';

// 声明全局方法
declare global {
  interface Window {
    openUploadDialog: () => void;
    setShowFolderModal: (show: boolean) => void;
    handleDeleteFile: (id: number) => void;
  }
}

const ComponentTemplatePageContent: FC<any> = (props: any) => {
  const [pageData, setPageData] = React.useState<any>({});
  const type = props.type
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');
  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }
    
    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  const [fileList, setFileList] = React.useState<any>([]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [showUploadModal, setShowUploadModal] = React.useState(false);
  const [showFolderModal, setShowFolderModal] = React.useState(false);
  const [showCreateCategoryModal, setShowCreateCategoryModal] = React.useState(false);
  const [newFolderName, setNewFolderName] = React.useState('');
  const [newCategoryName, setNewCategoryName] = React.useState('');
  const [categoryEditMode, setCategoryEditMode] = React.useState('create'); // 'create' 或 'rename'
  const [categoryToEdit, setCategoryToEdit] = React.useState<any>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const appId = props.history.location.pathname.split('/')[1].slice(3);

  //card ,list
  const [mode, setMode] = React.useState('card');
  const [categories, setCategories] = React.useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = React.useState<any>(null);
  const [rightMenuVisible, setRightMenuVisible] = React.useState(false);
  const [rightMenuPosition, setRightMenuPosition] = React.useState({ x: 0, y: 0 });
  const [rightMenuItem, setRightMenuItem] = React.useState<any>(null);

  const [showDeleteCategoryModal, setShowDeleteCategoryModal] = React.useState(false);
  const [categoryToDelete, setCategoryToDelete] = React.useState<any>(null);

  const [searchKeyword, setSearchKeyword] = React.useState('');
  const [searchStatus, setSearchStatus] = React.useState('');
  const [searchCategory, setSearchCategory] = React.useState('');

  const [showCreateTemplateModal, setShowCreateTemplateModal] = React.useState(false);

  const [amisKey, setAmisKey] = React.useState(0);

  const pageTitle = React.useMemo(() => {
    switch (type) {
      case '1':
        return '组件模板';
      case '3':
        return '表单模板';
      case '4':
        return '页面模板';
      default:
        return '组件模板';
    }
  }, [type]);

  const getToBlak = () => {
    let schema = JSON.parse(props.pageData.schema)
    let a = document.createElement('a');
    a.href = schema.body[0].src;
    window.open(a.href, '_blank');
  };

  // 获取表单数据 pageData
  // const handleGetFormDataPage = () => {
  //   let data = {
  //     applicationPageId: 1,
  //     pageNo: 1,
  //     pageSize: 10
  //   };
  //   getFormDataPage(data).then((res: any) => {
  //     if (res.code == 0) {
  //       if (res.data.list.length > 0) {
  //         if (res.data.list[0]) {
  //           // toast.success('获取表单数据成功');
  //           res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
  //           res.data.list[0].pageType = 13;
  //           res.data.list[0].appId = 1;
  //           setPageData(res.data.list[0]);
  //         }else{
  //           toast.success('暂无表单数据');
  //         }
  //       }else{
  //         toast.success('暂无表单数据');
  //       }
  //     } else {
  //       toast.error(res.msg);
  //     }
  //   });
  // };

  const initCustompageData = (pageId: any, info: any) => {
    // let data = {
    //   applicationPageId: pageId,
    //   data: window.JSON.stringify(createCustompageObj(info.name)),
    //   field: ''
    // };
    // createFormData(data).then((res: any) => {
    //   if (res.code == 0) {
    //     toast.success('初始化数据完成~');
        // setOpenCustomPagePopup(false);
        if(type == '3'){
          props.history.push(
            `/formTempEditor/${pageId}`
          );
        }else{
          props.history.push(
            `/componentTempEditor/${pageId}`
          );
        }
        // handleGetFormDataPage();
        // props.onSaveSuccess(pageId);
    //   } else {
    //     toast.error('初始化数据失败 ' + res.msg);
    //   }
    // });
  };

  React.useEffect(() => {
    // if (props.pageData?.id) {
      // handleGetFormDataPage();
    // }
    const urlParams = new URLSearchParams(props.history.location.search);
    let tabkey =  urlParams.get('activeTabKey') || 'preview';
    console.log(appId);

    setActiveKey(tabkey);
  }, [props.location]);

  // 创建新分类
  const handleCreateCategory = () => {
    setShowCreateCategoryModal(true);
    setNewCategoryName('');
    setCategoryEditMode('create');
    setCategoryToEdit(null);
  };

  // 重命名分类
  const handleRenameCategory = (category: any) => {
    if (!category || category.id === 'all') return; // 不允许修改"全部分类"
    setShowCreateCategoryModal(true);
    setCategoryEditMode('rename');
    setCategoryToEdit(category);
  };

  // 删除分类
  const handleDeleteCategory = (category: any) => {
    console.log(category);
    if (!category || category.id === 'all') return; // 不允许删除"全部分类"
    
    // 显示删除确认弹窗
    setCategoryToDelete(category);
    setShowDeleteCategoryModal(true);
    setRightMenuVisible(false);
  };
  
  // 执行删除分类操作
  const confirmDeleteCategory = () => {
    if (!categoryToDelete) return;
    
    // 调用删除分类API
    deleteComponentClassification({
      id: categoryToDelete.id
    }).then(result => {
        if (result.code === 0) {
          toast.success('分类删除成功');
          // 更新本地状态
          const updatedCategories = categories.filter(cat => cat.id !== categoryToDelete.id);
          setCategories(updatedCategories);
          if (selectedCategory?.id === categoryToDelete.id) {
            setSelectedCategory({
              id: 'all',
              name: '全部分类',
              type: 2
            });
          }
        } else {
          toast.error(result.msg || '删除失败');
        }
        setShowDeleteCategoryModal(false);
      })
      .catch(error => {
        console.error('删除分类失败', error);
        toast.error('删除分类失败，请稍后重试');
        setShowDeleteCategoryModal(false);
      });
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, category: any) => {
    e.preventDefault();
    const rect = document.documentElement.getBoundingClientRect();
    const windowHeight = rect.bottom - rect.top;
    
    // 计算菜单位置
    const isAboveHalf = e.clientY < windowHeight / 2;
    setRightMenuPosition({
      x: e.clientX,
      y: isAboveHalf ? e.clientY : e.clientY - 100
    });
    
    setRightMenuItem(category);
    setRightMenuVisible(true);
  };

  // 点击其他地方关闭右键菜单
  React.useEffect(() => {
    const handleClickOutside = () => {
      setRightMenuVisible(false);
    };

    if (rightMenuVisible) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [rightMenuVisible]);

  // 添加默认分类
  React.useEffect(() => {
    // 从接口获取分类列表
    fetchCategoryList();
  }, []);

  // 获取分类列表函数
  const fetchCategoryList = () => {
    getComponentClassificationPage({
      pageNo: 1,
      pageSize: 100,
      type: type
    }).then(result => {
      if (result.code === 0) {
        setCategories([
            {
              id: 'all',
              name: '全部分类',
              type: 2
            },
            ...result.data.list
          ]);
        }
      })
      .catch(error => {
        console.error('获取分类列表失败：', error);
        // 加载失败时使用默认分类
        setCategories([
          {
            id: 'all',
            name: '全部分类',
            type: 2
          },
          {
            id: 1,
            name: '报表组件',
            type: 2
          },
          {
            id: 2,
            name: '表单组件',
            type: 2
          },
          {
            id: 3,
            name: '弹窗基础组件',
            type: 2
          }
        ]);
      });
    setSelectedCategory({
      id: 'all',
      name: '全部分类',
      type: 2
    });
  };

  return (
    <div className="pageBox">
      <div className="pageTop">
        <div className="pageTop-title">{pageTitle}</div>
      </div>
      
      {/* 顶部查询区域 */}
      <div className="component-header">
        <div className="search-area">
          <AMISRenderer
            schema={{
              type: 'form',
              wrapWithPanel: false,
              mode: 'inline',
              target: 'crudId',
              submitOnChange: false,
              body: [
                {
                  type: 'input-text',
                  name: 'name',
                  label: '模板名称',
                  placeholder: '请选择',
                  clearable: true,
                  value: searchKeyword
                },
                {
                  type: 'select',
                  name: 'status',
                  label: '状态',
                  placeholder: '请选择',
                  clearable: true,
                  value: searchStatus,
                  options: [
                    { label: '启用', value: '1' },
                    { label: '禁用', value: '0' }
                  ]
                },
                {
                  type: 'submit',
                  label: '搜索',
                  level: 'primary',
                  onClick: (e: any, context: any) => {
                    setSearchKeyword(context.data.name || '');
                    setSearchStatus(context.data.status || '');
                    setAmisKey(Date.now());
                  }
                },
                {
                  type: 'reset',
                  label: '重置',
                  onClick: () => {
                    setSearchKeyword('');
                    setSearchStatus('');
                    setAmisKey(Date.now());
                  }
                }
              ]
            }}
          />
        </div>
      </div>

      {/* 顶部操作区域 */}
      <div className="component-header" style={{display: 'flex', justifyContent: 'space-between'}}>
        <div className="action-area">
          <Button level="primary" onClick={() => setShowCreateTemplateModal(true)}>
            新建模板
          </Button>
        </div>
        <div className="mode-switch">
          <Button level="default" onClick={() => setMode('card')} className={mode === 'card' ? 'active' : ''}>
            卡片
          </Button>
          <Button level="default" onClick={() => setMode('list')} className={mode === 'list' ? 'active' : ''}>
            表格
          </Button>
        </div>
      </div>
      
      <div className="component-container">
        {/* 左侧分类栏 */}
        <div className="category-sidebar">
          <div className="category-header">
            <span>分类</span>
            <Button level="link" size="sm" onClick={handleCreateCategory} className="add-category-btn">
              <i className="fa fa-plus"></i>
            </Button>
          </div>
          <div className="category-list">
            {categories.map((category) => (
              <div
                key={category.id}
                className={`category-item ${selectedCategory?.id === category.id ? 'active' : ''}`}
                onClick={() => {
                  setSelectedCategory(category);
                  // 通过设置新的key来强制刷新AMIS渲染器
                  setAmisKey(Date.now());
                }}
                onContextMenu={(e) => handleContextMenu(e, category)}
              >
                <div className="category-item-content">
                  <img src={type_folder} className="category-icon" />
                  <span className="category-name">{category.name}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧内容区 */}
        <div className="content-area">
          {mode === 'card' && (
            <AMISRenderer
              key={amisKey}
              schema={{
                type: 'page',
                body: [
                  {
                    type: 'crud',
                    syncLocation: false,
                    id: 'crudId',
                    api: {
                      method: 'get',
                      url: '/admin-api/system/component-template/page',
                      data: {
                        pageNo: '${page}',
                        pageSize: '${perPage}',
                        classificationId: selectedCategory && selectedCategory.id !== 'all' 
                          ? selectedCategory.id 
                          : '${classificationId}',
                        name: searchKeyword,
                        status: searchStatus,
                        type: type
                      },
                      // 过滤掉没有值的参数
                      requestAdaptor: function(api: any) {
                        const data = {...api.data};
                        Object.keys(data).forEach(key => {
                          if (data[key] === undefined || data[key] === '' || data[key] === null) {
                            delete data[key];
                          }
                        });
                        api.data = data;
                        return api;
                      }
                    },
                    mode: 'cards',
                    columnsCount: 4,
                    card: {
                      className: "border-none",
                      itemAction: {
                        type: "button",
                        actionType: "dialog",
                        dialog: {
                          title: '组件模板预览',
                          size: 'xl',
                          body: [
                            {
                              type: 'editor',
                              value: type == '4' ? '${jsonData}' : '${jsonData|bodyParse}',
                              name: 'cardBody',
                              hidden: true,
                            },
                            {
                              type: 'amis',
                              name: 'cardBody',
                            }
                          ]
                        }
                      },
                      body: [
                        {
                          type: 'service',
                          body: [
                            {
                              type: 'wrapper',
                              style: {
                                border: '1px solid #e8e9eb',
                                borderRadius: '4px',
                              },
                              body: [
                                {
                                  type: 'image',
                                  src: '${coverImage}',
                                  thumbRatio: '16:9',
                                  thumbMode: 'cover',
                                  imageMode: 'original',
                                  hiddenOn: "${coverImage === '' || coverImage === null}",
                                  style: {
                                    width: '355px',
                                    height: '200px',
                                    objectFit: 'cover'
                                  }
                                },
                                {
                                  type: 'wrapper',
                                  visibleOn: "${coverImage === '' || coverImage === null}",
                                  style: {
                                    height: 200,
                                    backgroundColor: '#F7F8FA',
                                    overflow: 'auto'
                                  },
                                  body: [
                                    {
                                      type: 'editor',
                                      name: 'cardBody',
                                      value: type == '4' ? '${jsonData}' : '${jsonData|bodyParse}',
                                      hidden: true,
                                    },
                                    {
                                      type: 'amis',
                                      name: 'cardBody',
                                      className: 'h-8',
                                      style: {
                                        height: 200,
                                        overflow: 'auto'
                                      },
                                    },
                                  ]
                                },
                                {
                                  type: 'flex',
                                  items: [
                                    {
                                      type: 'container',
                                      body: [
                                        {
                                          type: 'tpl',
                                          tpl: '${name}',
                                          style: {
                                            flex: 1
                                          }
                                        }
                                      ],
                                      style: {
                                        display: 'flex',
                                        alignItems: 'center',
                                        flex: 1
                                      }
                                    },
                                    {
                                      type: 'switch',
                                      name: 'status',
                                      value: '${status}',
                                      onText: '启用',
                                      offText: '禁用',
                                      // size: 'sm',
                                      mode: 'inline',
                                      onEvent: {
                                        change: {
                                          actions: [
                                            {
                                              actionType: 'ajax',
                                              api: {
                                                method: 'put',
                                                url: '/admin-api/system/component-template/update',
                                                data: {
                                                  "status": "${status ? 1 : 0}",
                                                  "id": "${id}"
                                                },
                                                adaptor: function(payload: any, response: any, api: any, context:any) {
                                                  //刷新
                                                  if (response) {
                                                    setAmisKey(Date.now());
                                                  }
                                                  return {
                                                    ...payload
                                                  }
                                                }
                                              }
                                            }
                                          ]
                                        }
                                      }
                                    },
                                    {
                                      type: 'dropdown-button',
                                      label: '',
                                      hideCaret: true,
                                      icon: 'fa fa-ellipsis-h',
                                      align: 'right',
                                      trigger: 'hover',
                                      size: 'sm',
                                      btnClassName: 'border-0',
                                      buttons: [
                                        {
                                          type: 'button',
                                          label: '编辑',
                                          actionType: 'link',
                                          link: '/componentTempEditor/${id}',
                                          hidden: type == '3'
                                        },
                                        {
                                          type: 'button',
                                          label: '编辑',
                                          actionType: 'link',
                                          link: '/formTempEditor/${id}',
                                          hidden: type == '1' || type == '2'
                                        },
                                        {
                                          type: 'button',
                                          label: '复制代码',
                                          actionType: 'copy',
                                          content: '${jsonData}'
                                        },
                                        {
                                          type: 'button',
                                          label: '设置',
                                          actionType: 'dialog',
                                          dialog: {
                                            title: '设置',
                                            body: {
                                              type: 'form',
                                              api: {
                                                method: 'put',
                                                url: '/admin-api/system/component-template/update',
                                              },
                                              body: [
                                                {
                                                  type: 'input-text',
                                                  name: 'name',
                                                  label: '组件模板名称',
                                                  value: '${name}'
                                                },
                                                {
                                                  type: 'hidden',
                                                  name: 'id',
                                                  value: '${id}'
                                                },
                                                {
                                                  type: 'input-image',
                                                  name: 'coverImage',
                                                  label: '封面',
                                                  receiver: {
                                                    url: '/app-api/infra/file/upload-zerocode',
                                                    method: 'post'
                                                  }
                                                },
                                                {
                                                  type: 'select',
                                                  name: 'classificationId',
                                                  label: '分类',
                                                  options: categories.filter((category) => category.id !== 'all').map((category) => ({
                                                    label: category.name,
                                                    value: category.id
                                                  })),
                                                  value: '${classificationId}'
                                                },
                                                {
                                                  type: 'switch',
                                                  name: 'status',
                                                  label: '状态',
                                                  value: '${status ? 1 : 0}'
                                                },
                                                {
                                                  type: 'textarea',
                                                  name: 'remark',
                                                  label: '描述',
                                                  value: '${remark}'
                                                }
                                              ]
                                            }
                                          }
                                        },
                                        {
                                          type: 'button',
                                          label: '复制',
                                          actionType: 'ajax',
                                          api: {
                                            method: 'post',
                                            url: '/admin-api/system/component-template/create',
                                            data: {
                                              name: '${name}',
                                              jsonData: '${jsonData}',
                                              type: type
                                            }
                                          }
                                        },
                                        {
                                          type: 'button',
                                          label: '删除',
                                          actionType: 'ajax',
                                          confirmText: '确认要删除该组件模板吗？',
                                          api: {
                                            method: 'delete',
                                            url: '/admin-api/system/component-template/delete?id=${id}'
                                          }
                                        }
                                      ]
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    },
                    footerToolbar: [
                      'statistics',
                      'switch-per-page',
                      'pagination'
                    ]
                  }
                ]
              }}
              embedMode={true}
            />
          )}

          {mode === 'list' && (
            <AMISRenderer
              key={amisKey}
              schema={{
                type: 'page',
                body: [
                  {
                    type: 'crud',
                    syncLocation: false,
                    id: 'crudId',
                    quickSaveItemApi:{
                      method: 'put',
                      url: '/admin-api/system/component-template/update',
                      data: {
                        "status": "${status ? 1 : 0}",
                        "id": "${id}"
                      }
                    },
                    api: {
                      method: 'get',
                      url: '/admin-api/system/component-template/page',
                      data: {
                        pageNo: '${page}',
                        pageSize: '${perPage}',
                        classificationId: selectedCategory && selectedCategory.id !== 'all' 
                          ? selectedCategory.id 
                          : '${classificationId}',
                        name: searchKeyword,
                        status: searchStatus,
                        type: type
                      },
                      // 过滤掉没有值的参数
                      requestAdaptor: function(api: any) {
                        const data = {...api.data};
                        Object.keys(data).forEach(key => {
                          if (data[key] === undefined || data[key] === '' || data[key] === null) {
                            delete data[key];
                          }
                        });
                        api.data = data;
                        return api;
                      }
                    },
                    columns: [
                      {
                        type: 'tpl',
                        tpl: '${name}',
                        label: '名称',
                      },
                      {
                        type: 'image',
                        name: 'coverImage',
                        label: '封面',
                      },
                      {
                        label: '描述',
                        name: 'remark',
                        type: 'tpl'
                      },
                      {
                        label: '分类',
                        name: 'classificationName',
                        static: true,
                        type: 'tpl'
                      },
                      {
                        label: '状态',
                        name: 'status',
                        quickEdit: {
                          mode: "inline",
                          type: "switch",
                          onText: '启用',
                          offText: '禁用',
                          saveImmediately: true,
                          resetOnFailed: true,

                        }
                      },
                      {
                        type: 'user-select',
                        name: 'creator',
                        label: '创建人',
                        static: true,
                      },
                      {
                        type: 'input-datetime',
                        name: 'createTime',
                        label: '创建时间',
                        valueFormat: 'x',
                        static: true,
                      },
                      {
                        type: 'operation',
                        label: '操作',
                        buttons: [
                          {
                            label: '查看',
                            type: 'button',
                            actionType: 'dialog',
                            level: 'link',
                            dialog: {
                              title: '组件模板预览',
                              size: 'lg',
                              body: [
                                {
                                  type: 'editor',
                                  value: type == '4' ? '${jsonData}' : '${jsonData|bodyParse}',
                                  name: 'cardBody',
                                  hidden: true,
                                },
                                {
                                  type: 'amis',
                                  name: 'cardBody'
                                }
                              ]
                            }
                          },
                          {
                            label: '编辑',
                            type: 'button',
                            level: 'link',
                            actionType: 'link',
                            link: '/componentTempEditor/${id}',
                            hidden: type == '3'
                          },
                          {
                            label: '编辑',
                            type: 'button',
                            level: 'link',
                            actionType: 'link',
                            link: '/formTempEditor/${id}',
                            hidden: type == '1' || type == '2'
                          },
                          {
                            label: '复制',
                            type: 'button',
                            level: 'link',
                            actionType: 'ajax',
                            api: {
                              method: 'post',
                              url: '/admin-api/system/component-template/create',
                              data: {
                                name: '${name}',
                                jsonData: '${jsonData}',
                                type: type
                              }
                            },
                            messages: {
                              success: '复制成功'
                            }
                          },
                          {
                            label: '复制代码',
                            type: 'button',
                            level: 'link',
                            actionType: 'copy',
                            content: '${jsonData}'
                          },
                          {
                            label: '设置',
                            type: 'button',
                            actionType: 'dialog',
                            level: 'link',
                            dialog: {
                              title: '设置',
                              body: [
                                {
                                  type: 'form',
                                  api: {
                                    method: 'put',
                                    url: '/admin-api/system/component-template/update',
                                  },
                                  body: [
                                    {
                                      type: 'input-text',
                                      name: 'name',
                                      label: '组件模板名称',
                                    },
                                    {
                                      type: 'input-text',
                                      name: 'id',
                                      label: '组件模板ID',
                                      hidden: true,
                                    },
                                    {
                                      type: 'input-image',
                                      name: 'coverImage',
                                      label: '封面',
                                      receiver: {
                                        url: '/app-api/infra/file/upload-zerocode',
                                        method: 'post'
                                      }
                                    },
                                    {
                                      type: 'select',
                                      name: 'classificationId',
                                      label: '分类',
                                      options: categories.filter((category) => category.id !== 'all').map((category) => ({
                                        label: category.name,
                                        value: category.id
                                      })),
                                      value: '${classificationId}'
                                    },
                                    {
                                      type: 'switch',
                                      name: 'status',
                                      label: '状态',
                                      value: '${status ? 1 : 0}'
                                    },
                                    {
                                      type: 'textarea',
                                      name: 'remark',
                                      label: '描述',
                                      value: '${remark}'
                                    }
                                  ]
                                }
                              ]
                            }
                          },
                          {
                            type: 'button',
                            label: '删除',
                            actionType: 'dialog',
                            level: 'link',
                            className: 'text-danger',
                            dialog: {
                              type: 'dialog',
                              title: '',
                              className: 'py-2',
                              actions: [
                                {
                                  type: 'action',
                                  actionType: 'cancel',
                                  label: '取消'
                                },
                                {
                                  type: 'action',
                                  actionType: 'submit',
                                  label: '删除',
                                  level: 'danger'
                                }
                              ],
                              body: [
                                {
                                  type: 'form',
                                  wrapWithPanel: false,
                                  api: {
                                    method: 'delete',
                                    url: '/admin-api/system/component-template/delete?id=${id}',
                                  },
                                  body: [
                                    {
                                      type: 'tpl',
                                      className: 'py-2',
                                      tpl: '确认删除选中项？'
                                    }
                                  ],
                                  feat: 'Insert',
                                  dsType: 'api',
                                  labelAlign: 'left'
                                }
                              ]
                            }
                          }
                        ]
                      }
                    ],

                    footerToolbar: [
                      'statistics',
                      'switch-per-page',
                      'pagination'
                    ]
                  }
                ]
              }}
              embedMode={true}
            />
          )}
        </div>
      </div>

      {/* 分类编辑模态框 */}
      {showCreateCategoryModal && (
        <AMISRenderer
          show={showCreateCategoryModal}
          onClose={() => setShowCreateCategoryModal(false)}
          schema={{
            type: 'dialog',
            title: categoryEditMode === 'create' ? '新建分类' : '编辑分类',
            body: {
              type: 'form',
              api: {
                method: categoryEditMode === 'create' ? 'post' : 'put',
                url: categoryEditMode === 'create' ? '/admin-api/system/component-classification/create' : '/admin-api/system/component-classification/update',
                data: {
                  name: '${name}',
                  id: categoryEditMode === 'rename' && categoryToEdit ? categoryToEdit.id : '',
                  type: type
                },
                adaptor: function (payload: any, response: any, api: any, context: any) {
                  setShowCreateCategoryModal(false);
                  // 刷新分类列表
                  fetchCategoryList();
                  return payload;
                }
              },
              body: [
                {
                  type: 'input-text',
                  name: 'name',
                  label: '分类名称',
                  placeholder: '请输入分类名称',
                  value: categoryEditMode === 'rename' && categoryToEdit ? categoryToEdit.name : '',
                  required: true
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确定',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
        />
      )}

      {/* 删除分类确认对话框 */}
      {showDeleteCategoryModal && (
        <AMISRenderer
          show={showDeleteCategoryModal}
          schema={{
            type: 'dialog',
            title: '删除确认',
            body: {
              type: 'form',
              body: [
                {
                  type: 'tpl',
                  tpl: `确定要删除"${categoryToDelete.name}"分类吗？`,
                  className: 'py-2 text-center'
                }
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel',
                onClick: () => setShowDeleteCategoryModal(false)
              },
              {
                type: 'button',
                label: '确定',
                level: 'danger',
                actionType: 'confirm',
                onClick: confirmDeleteCategory
              }
            ]
          }}
          onClose={() => setShowDeleteCategoryModal(false)}
        />
      )}

      {/* 右键菜单 */}
      {rightMenuVisible && rightMenuItem && (
        <div
          className="customContextMenu"
          style={{
            left: rightMenuPosition.x + 'px',
            top: rightMenuPosition.y + 'px'
          }}
        >
          <div
            className="customContextMenu-item"
            onClick={() => {
              handleRenameCategory(rightMenuItem);
              setRightMenuVisible(false);
            }}
          >
            <div className="customContextMenu-item-name">重命名</div>
          </div>
          <div
            className="customContextMenu-item"
            onClick={() => handleDeleteCategory(rightMenuItem)}
          >
            <div className="customContextMenu-item-name">删除分类</div>
          </div>
        </div>
      )}

      {/* 创建组件模板对话框 */}
      {showCreateTemplateModal && (
        <AMISRenderer
          show={showCreateTemplateModal}
          schema={{
            type: 'dialog',
            title: '创建组件模板',
            body: {
              type: 'form',
              api: {
                method: 'post',
                url: '/admin-api/system/component-template/create',
                data: {
                  name: '${name}',
                  classificationId: '${classificationId}',
                  status: '${status ? 1 : 0}',
                  remark: '${remark}',
                  type: type,
                  coverImage: '${coverImage}'
                },
                adaptor: function (payload: any, response: any, api: any, context: any) {
                  setShowCreateTemplateModal(false);
                  initCustompageData(response.data.data, context);
                  return payload;
                }
              },
              body: [
                {
                  type: 'input-text',
                  name: 'name',
                  label: '组件模板名称',
                  required: true
                },
                {
                  type: 'input-image',
                  name: 'coverImage',
                  label: '封面',
                  receiver: {
                    url: '/app-api/infra/file/upload-zerocode',
                    method: 'post'
                  }
                },
                {
                  type: 'select',
                  name: 'classificationId',
                  label: '分类',
                  options: categories.filter((category) => category.id !== 'all').map((category) => ({
                    label: category.name,
                    value: category.id
                  })),
                },
                {
                  type: 'switch',
                  name: 'status',
                  label: '状态',
                  value: 1
                },
                {
                  type: 'textarea',
                  name: 'remark',
                  label: '描述',
                },
                
              ]
            },
            actions: [
              {
                type: 'button',
                label: '取消',
                actionType: 'cancel'
              },
              {
                type: 'button',
                label: '确定',
                level: 'primary',
                actionType: 'submit'
              }
            ]
          }}
          onClose={() => setShowCreateTemplateModal(false)}
        />
      )}
    </div>
  );
};

export default ComponentTemplatePageContent;
