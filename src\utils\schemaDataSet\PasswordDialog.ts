export const PasswordDialog = (show: boolean, isReset: boolean) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: `<b>${isReset ? '重置密码' : '设置密码'}</b>`
    },
    showCloseButton: false,
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: isReset ? '请输入新密码' : '请输入密码'
      },
      body: [
        {
          maxLength: 32,
          type: 'input-password',
          name: 'password',
          placeholder: isReset ? '新密码' : '请输入密码',
          required: true,
          inputClassName: 'password-input',
          labelClassName: 'password-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel'
      },
      {
        type: 'button',
        label: isReset ? '确认' : '设置完成',
        level: 'primary',
        actionType: 'confirm'
      }
    ],
    show: show
  };
}; 