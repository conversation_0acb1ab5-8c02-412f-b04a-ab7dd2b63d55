export const EditBioDialog = (show: boolean, currentBio?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑简介</b>'
    },
    showCloseButton: false,
    data: {
      bio: currentBio
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入个人简介'
      },
      body: [
        {
          required: true,
          type: 'textarea',
          name: 'bio',
          placeholder: '个人简介',
          maxLength: 500,
          validations: {
            maxLength: 500
          },
          inputClassName: 'name-input',
          labelClassName: 'name-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 