// 流水号组件样式
.serial-fields-combo {
  // 移除左侧缩进，与标签对齐
  .cxd-Form-item {
    margin-left: 0 !important;
    padding-left: 0 !important;
  }
  
  // combo容器样式
  .cxd-Combo {
    margin-left: 0 !important;
    padding-left: 0 !important;
  }
  
  // combo项目容器
  .cxd-Combo-item {
    margin-left: 0 !important;
    padding-left: 0 !important;
    border-left: none !important;
  }
  
  // 规则行样式
  .serial-field-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    padding: 8px 0;
    align-items: center;

    // 类型选择器
    .field-type-select {
      width: 100%;

      .cxd-Select {
        width: 100%;
      }
    }

    // 值输入框
    .field-value-input {
      width: 100%;

      .cxd-TextControl {
        width: 100%;
      }
    }

    // 值选择框
    .field-value-select {
      width: 100%;

      .cxd-Select {
        width: 100%;
      }
    }

    // 设置按钮 - 与输入框/下拉框宽度一致
    .field-setting-button {
      width: 100%;

      .cxd-Button {
        width: 100% !important;
        text-align: center;
      }
    }
  }

  // 添加按钮样式 - 撑满宽度
  .cxd-Combo-addBtn {
    margin-left: 0 !important;
    margin-top: 8px;
    width: 100% !important;

    .cxd-Button {
      width: 100% !important;
      justify-content: center;
    }
  }
}

// 编号规则预览样式
.serial-rule-preview {
  .cxd-Form-item {
    margin-bottom: 16px;
  }

  .cxd-PlainText {
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #1890ff;
    min-height: 32px;
    display: flex;
    align-items: center;
  }
}
