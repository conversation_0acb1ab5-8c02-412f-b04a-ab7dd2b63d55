import {Button} from 'amis-ui';
import React from 'react';
import {registerEditorPlugin} from 'amis-editor-core';
import {
  BaseEventContext,
  BasePlugin,
  BasicRendererInfo,
  InsertEventContext,
  PluginEvent,
  PluginInterface,
  RegionConfig,
  RendererInfo,
  RendererInfoResolveEventContext,
  VRendererConfig,
  diff,
//   generateId
} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

import {generateId} from 'amis-editor/lib/util';
import {defaultValue, getSchemaTpl} from 'amis-editor-core';
import flatten from 'lodash/flatten';
import {VRenderer, tipedLabel} from 'amis-editor-core';
// import {generateId} from '../util';

export class IconCardPlugin extends BasePlugin {
  static id = 'IconCardPlugin';
  static scene = ['layout'];
  // 关联渲染器名字
  rendererName = 'icon-card';
  $schema = '/schemas/CardSchema.json';

  // 组件名称
  name = '应用卡片';
  isBaseComponent = true;
  description = '展示单个应用卡片。';
  docLink = '/amis/zh-CN/components/card';
  tags = ['展示'];
  icon = '';
  pluginIcon = 'card-plugin';
  scaffold = {
    type: 'icon-card',
    header: {
      titleVisible: true,
      title: '标题',
      subTitle: '副标题',
      subTitleVisible: true,
      imageClassName: "w-full h-full",

      avatarClassName: "pull-left avatar b-3x m-r",
      avatarVisible: true,
      avatarType: 'image',
      avatarBorderRadius: '50%',
      avatarWidth: '64px',
      avatarHeight: '64px',

      descVisible: true,
      desc: '描述',
      iconVisible: true,
    },
    icon: [
        {
          type: 'icon',
          icon: 'fa fa-star',
          id: generateId(),
          height: '64px',
          width: '64px'
        }
      ],
    body: '内容',
    clickType: 'link',
    footerToolbarVisible: true,
    contentVisible: true,
    headerToolbarVisible: true,
    actions: [
      {
        type: 'button',
        label: '按钮',
        actionType: 'dialog',
        id: generateId(),
        dialog: {
          title: '标题',
          body: '内容'
        }
      }
    ]
  };
  previewSchema = {
    ...this.scaffold
  };

  regions: Array<RegionConfig> = [
    {
      key: 'body',
      label: '内容区',
      renderMethod: 'renderBody',
      preferTag: '展示'
    },
    {
      key: 'icon',
      label: '图标区',
      renderMethod: 'renderIcon',
      preferTag: '图标'
    },
    {
      key: 'actions',
      label: '按钮组',
      renderMethod: 'renderActions',
      wrapperResolve: (dom: HTMLElement) => dom,
      preferTag: '按钮'
    }
  ];

  handleActionEdit(id: string, index: number) {
    const store = this.manager.store;
    const schema = store.getSchema(id);
    const action = schema?.actions[index];
    if (action && action.$$id) {
      store.setActiveId(action.$$id);
    }
  }

  handleToolbarEdit(id: string, index: number) {
    const store = this.manager.store;
    const schema = store.getSchema(id);
    const action = schema?.toolbar[index];
    if (action && action.$$id) {
      store.setActiveId(action.$$id);
    }
  }

  editDetail(id: string) {
    console.log('editDetail', id);
    const manager = this.manager;
    const store = manager.store;
    const node = store.getNodeById(id);
    const value = store.getValueOf(id);
    const data = value.body;

    let originValue = data.type
      ? ['container', 'wrapper'].includes(data.type)
        ? data
        : {
            // schema中存在容器，用自己的就行
            type: 'container',
            body: [data]
          }
      : {
        type: 'container',
        body: [data]
      };

    node &&
      value &&
      this.manager.openSubEditor({
        title: '配置显示模板',
        value: originValue,
        // slot: {
        //   type: 'container',
        //   body: '$$'
        // },
        onChange: (newValue: any) => {
          newValue = {...originValue, body: newValue};
          manager.panelChangeValue(newValue, diff(originValue, newValue));
        },
        data: {
          [value.labelField || 'label']: '选项名',
          [value.valueField || 'value']: '选项值',
          item: '假数据'
        }
      });
  }

  panelTitle = '应用卡片';

  panelJustify = true;

  panelBodyCreator = (context: BaseEventContext) => {
    const id = context.id;
    return getSchemaTpl('tabs', [
        {
            title: '常规',
            body: getSchemaTpl(
              'collapseGroup',
              [
                {
                  title: '基本',
                  id: 'properties-basic',
                  body: [
                    getSchemaTpl('layout:originPosition', {value: 'left-top'}),
                    getSchemaTpl('switch', {
                      name: 'header.iconVisible',
                      label: tipedLabel('显示应用图标', '是否显示应用图标'),
                      pipeIn: defaultValue(true),
                    }),
                    getSchemaTpl('switch', {
                      name: 'header.titleVisible',
                      label: tipedLabel('显示标题', '是否显示标题'),
                      pipeIn: defaultValue(true),
                    }),
                    getSchemaTpl('tplFormulaControl', {
                      name: 'header.title',
                      label: '标题',
                      visibleOn: 'this.header.titleVisible'
                    }),
                    getSchemaTpl('switch', {
                      name: 'header.subTitleVisible',
                      label: '显示副标题',
                      pipeIn: defaultValue(true),
                    }),
                    getSchemaTpl('tplFormulaControl', {
                      name: 'header.subTitle',
                      label: '副标题',
                      visibleOn: 'this.header.subTitleVisible'
                    }),
                    getSchemaTpl('tplFormulaControl', {
                      name: 'header.subTitlePlaceholder',
                      label: '副标题占位',
                    }),
                    //显示描述
                    getSchemaTpl('switch', {
                      name: 'header.descVisible',
                      label: '显示描述',
                      pipeIn: defaultValue(true),
                    }),
                    getSchemaTpl('textareaFormulaControl', {
                      name: 'header.desc',
                      label: '描述',
                    }),
                    getSchemaTpl('tplFormulaControl', {
                      name: 'header.descriptionPlaceholder',
                      label: '描述占位',
                    }),
                    {
                      type: 'button-group-select',
                      size: 'xs',
                      label: '卡片点击行为',
                      name: 'clickType',
                      // className: 'inline-flex justify-between',
                      mode: 'horizontal',
                      pipeIn: defaultValue('link'),
                      pipeOut: (value: any) => {
                        if(value === 'link') {
                          context.node && this.manager.store.changeValueById(context.node.id, {
                            ...context.schema,
                            itemAction: {}
                          });
                        } else {
                          context.node && this.manager.store.changeValueById(context.node.id, {
                            ...context.schema,
                            itemAction: {
                              type: 'button',
                              actionType: 'dialog',
                              dialog: {
                                title: '详情',
                                body: '当前描述'
                              }
                            }
                          });
                        }
                        return value;
                      },
                      options: [
                        {
                          label: '链接',
                          value: 'link'
                        },
                        {
                          label: '行为',
                          value: 'action'
                        }
                      ]
                    },
                    // getSchemaTpl('eventControl', {
                    //   name: 'itemAction',
                    //   ...getEventControlConfig(this.manager, context)
                    // }),
                    getSchemaTpl('tplFormulaControl', {
                      name: 'header.href',
                      label: '卡片跳转链接'
                      }),
                    getSchemaTpl('switch', {
                      name: 'header.blank',
                      label: '新窗口打开',
                      pipeIn: defaultValue(true),
                    }),
                    // 通过设置 itemAction 可以设置整个卡片的点击行为
                    // 注意它和前面的 href 配置冲突，如果设置了 href 这个将不会生效
                    getSchemaTpl('tplFormulaControl', {
                    name: 'href',
                    label: '打开外部链接',
                    visibleOn: 'this.clickType === "link"'
                    }),
                    {
                      name: 'header.highlight',
                      type: 'input-text',
                      label: '是否高亮表达式',
                      description: '如： <code>this.isOwner</code>'
                    },
                  ]
                },
                {
                  title: '内容区',
                  id: 'properties-content',
                  body: [
                    getSchemaTpl('layout:originPosition', {value: 'left-top'}),
                    getSchemaTpl('switch', {
                      name: 'contentVisible',
                      label: '显示内容区',
                      pipeIn: defaultValue(true),
                    }),
                    {
                      children: (
                        <div>
                          <Button
                            block
                            level="primary"
                            size="sm"
                            onClick={this.editDetail.bind(this, context.id)}
                          >
                            内容区配置
                          </Button>
                        </div>
                      )
                    },
                  ]
                },
                {
                  title: '顶部工具栏',
                  id: 'properties-header-toolbar',
                  body: [
                    getSchemaTpl('switch', {
                      name: 'headerToolbarVisible',
                      label: '显示顶部工具栏',
                      pipeIn: defaultValue(true),
                    }),
                    getSchemaTpl('combo-container', {
                      label: '卡片顶部按钮管理',
                      name: 'toolbar',
                      type: 'combo',
                      inputClassName: 'ae-BulkActions-control',
                      multiple: true,
                      draggable: true,
                      draggableTip: '',
                      scaffold: {
                        label: '按钮',
                        type: 'button'
                      },
                      // labelRemark: {
                      //   className: 'm-l-xs',
                      //   trigger: 'click',
                      //   rootClose: true,
                      //   content:
                      //     '',
                      //   placement: 'left'
                      // },
                      items: [
                        getSchemaTpl('tpl:btnLabel'),
          
                        {
                          columnClassName: 'p-t-xs col-edit',
                          children: ({index}: any) => (
                            <button
                              onClick={this.handleToolbarEdit.bind(this, id, index)}
                              data-tooltip="修改"
                              data-position="bottom"
                              className="text-muted"
                            >
                              <i className="fa fa-pencil" />
                            </button>
                          )
                        }
                      ]
                    }),
                    // toolbar
                  ]
                },
                {
                  title: '卡片底部按钮管理',
                  id: 'properties-footer-toolbar',
                  body: [
                    getSchemaTpl('switch', {
                      name: 'footerToolbarVisible',
                      label: '显示底部工具栏',
                      pipeIn: defaultValue(true),
                    }),
                    // actions
                    {
                      type: 'input-range',
                      name: 'actionsCount',
                      pipeIn: defaultValue(4),
                      min: 1,
                      max: 10,
                      step: 1,
                      label: '每行按钮个数'
                    },
                    getSchemaTpl('combo-container', {
                      label: '卡片底部按钮管理',
                      name: 'actions',
                      type: 'combo',
                      inputClassName: 'ae-BulkActions-control',
                      multiple: true,
                      draggable: true,
                      draggableTip: '',
                      scaffold: {
                        label: '按钮',
                        type: 'button'
                      },
                      // labelRemark: {
                      //   className: 'm-l-xs',
                      //   trigger: 'click',
                      //   rootClose: true,
                      //   content:
                      //     '',
                      //   placement: 'left'
                      // },
                      items: [
                        getSchemaTpl('tpl:btnLabel'),
          
                        {
                          columnClassName: 'p-t-xs col-edit',
                          children: ({index}: any) => (
                            <button
                              onClick={this.handleActionEdit.bind(this, id, index)}
                              data-tooltip="修改"
                              data-position="bottom"
                              className="text-muted"
                            >
                              <i className="fa fa-pencil" />
                            </button>
                          )
                        }
                      ]
                    }),
                  ]
                },
                {
                  title: "其他",
                  id: 'properties-other',
                  body: [
                    getSchemaTpl('switch', {
                      name: 'dragging',
                      label: tipedLabel('拖拽', '')
                    }),
                    getSchemaTpl('switch', {
                      name: 'selectable',
                      label: tipedLabel('可选', '')
                    }),
                    getSchemaTpl('switch', {
                      name: 'multiple',
                      label: tipedLabel('多选', '是否支持多选')
                    }),
                    getSchemaTpl('switch', {
                      name: 'checkable',
                      label: tipedLabel('选择按钮禁用', '')
                    }),
                    getSchemaTpl('switch', {
                      name: 'selected',
                      label: tipedLabel('选择按钮选中', '')
                    }),
                    getSchemaTpl('switch', {
                      name: 'hideCheckToggler',
                      label: tipedLabel('选择按钮隐藏', '')
                    }),
                    getSchemaTpl('switch', {
                      name: 'useCardLabel',
                      label: tipedLabel('内部标签', '内容区的表单项 label 是否使用 Card 内部的样式')
                    }),
                  ]
                }

              ]
            )
        },
        {
          title: '外观',
          className: 'p-none',
          body: [
            getSchemaTpl('collapseGroup', [
              ...getSchemaTpl('theme:base', {
                classname: 'baseControlClassName',
                title: '基本样式',
              }),
              ...getSchemaTpl('theme:base', {
                classname: 'headerControlClassName',
                title: '头部样式',
              }),
              ...getSchemaTpl('theme:base', {
                classname: 'bodyControlClassName',
                title: '内容区样式',

              }),
              ...getSchemaTpl('theme:base', {
                classname: 'titleControlClassName',
                title: '标题样式',
                extra: [
                  getSchemaTpl('theme:font', {
                    label: '文字',
                     name: 'themeCss.titleControlClassName.font'
                    })
                ]
              }),
              ...getSchemaTpl('theme:base', {
                classname: 'subTitleControlClassName',
                title: '副标题样式',
                extra: [
                  getSchemaTpl('theme:font', {
                    label: '文字',
                     name: 'themeCss.subTitleControlClassName.font'
                    })
                ]
              }),
              ...getSchemaTpl('theme:base', {
                classname: 'descriptionControlClassName',
                title: '描述样式',
                extra: [
                  getSchemaTpl('theme:font', {
                    label: '文字',
                     name: 'themeCss.descriptionControlClassName.font'
                    })
                ]
              }),
            ])
          ]
        },

        {
          title: '显隐',
          body: [getSchemaTpl('ref'), getSchemaTpl('visible')]
        }
      ])
  };

  /*exchangeRenderer(id: string) {
    this.manager.showReplacePanel(id, '展示');
  }*/


    
    
      wrapperProps = {
        unmountOnExit: true,
        mountOnEnter: true
      };

  fieldWrapperResolve = (dom: HTMLElement) => dom;

  overrides = {
    renderFeild: function (
      this: any,
      region: string,
      field: any,
      index: any,
      props: any
    ) {
      const dom = this.super(region, field, index, props);
      const info: RendererInfo = this.props.$$editor;

      if (!info || !field.$$id) {
        return dom;
      }

      const plugin = info.plugin as IconCardPlugin;
      const id = field.$$id;
      return (
        <VRenderer
          type={info.type}
          plugin={info.plugin}
          renderer={info.renderer}
          multifactor
          key={id}
          $schema="/schemas/CardBodyField.json"
          hostId={info.id}
          memberIndex={index}
          name={`${`字段${index + 1}`}`}
          id={id}
          draggable={false}
          wrapperResolve={plugin.fieldWrapperResolve}
          schemaPath={`${info.schemaPath}/body/${index}`}
          path={`${this.props.$path}/${index}`} // 好像没啥用
          data={this.props.data} // 好像没啥用
        >
          {dom}
        </VRenderer>
      );
    }
  };

  vRendererConfig: VRendererConfig = {
    panelTitle: '字段',
    panelBodyCreator: (context: BaseEventContext) => {
      return [
        getSchemaTpl('label'),
        getSchemaTpl('className', {
          name: 'labelClassName',
          label: 'Label CSS 类名',
          visibleOn: 'this.label'
        })
        /*{
          children: (
            <Button
              size="sm"
              level="info"
              className="m-b"
              block
              onClick={this.exchangeRenderer.bind(this, context.id)}
            >
              更改渲染器类型
            </Button>
          )
        }*/
      ];
    }
  };

  // 自动插入 label
  beforeInsert(event: PluginEvent<InsertEventContext>) {
    const context = event.context;

    if (
      (context.info.plugin === this ||
        context.node.sameIdChild?.info.plugin === this) &&
      context.region === 'body'
    ) {
      context.data = {
        ...context.data,
        label: context.data.label ?? context.subRenderer?.name ?? '列名称'
      };
    }
  }
}

registerEditorPlugin(IconCardPlugin);
