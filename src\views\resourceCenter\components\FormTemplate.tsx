import React, { FC, useState, useEffect } from 'react';
import { Schema } from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import './FormTemplate.scss';
import { getComponentClassificationPage } from '@/utils/api/api';
import { visibleOn } from '@/editor/EChartsEditor/Common';

interface FormTemplateProps {
  onSelect?: (item: any) => void;
}

const FormTemplate: FC<FormTemplateProps> = (props) => {
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ id: 'all', name: '全部分类' });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [amisKey, setAmisKey] = useState(0);
  const [type, setType] = useState('1'); // 默认为表单模板类型3

  // 监听模板类型变更事件
  useEffect(() => {
    const handleTemplateTypeChange = (event: any) => {
      const newType = event.detail.type;
      setType(newType);
      // 重新获取对应类型的分类列表
      fetchCategoryList(newType);
      // 重置分类选择
      setSelectedCategory({ id: 'all', name: '全部分类' });
      // 重置搜索关键词
      setSearchKeyword('');
      // 刷新AMIS渲染
      setAmisKey(Date.now());
    };

    window.addEventListener('templateTypeChange', handleTemplateTypeChange);

    return () => {
      window.removeEventListener('templateTypeChange', handleTemplateTypeChange);
    };
  }, []);

  // 获取分类列表
  useEffect(() => {
    fetchCategoryList(type);
  }, []);

  // 获取分类列表函数
  const fetchCategoryList = (templateType = type) => {
    getComponentClassificationPage({
      pageNo: 1,
      pageSize: 100,
      type: templateType
    }).then(result => {
      if (result.code === 0) {
        setCategories([
          {
            id: 'all',
            name: '全部分类',
            type: 2
          },
          ...result.data.list
        ]);
      }
    })
    .catch(error => {
      console.error(`获取分类列表失败：${templateType}`, error);
      // 加载失败时使用默认分类
      setCategories([
        {
          id: 'all',
          name: '全部分类',
          type: 2
        },
        {
          id: 1,
          name: '人事行政',
          type: 2
        },
        {
          id: 2,
          name: 'CRM销售',
          type: 2
        },
        {
          id: 3,
          name: '项目管理',
          type: 2
        },
        {
          id: 4,
          name: '财务报销',
          type: 2
        },
        {
          id: 5,
          name: '工单售后',
          type: 2
        }
      ]);
    });
  };

  // 处理分类切换
  const handleCategoryChange = (category: any) => {
    setSelectedCategory(category);
    setAmisKey(Date.now());
  };

  // 处理搜索
  const handleSearch = (keyword: string) => {
    setSearchKeyword(keyword);
    setAmisKey(Date.now());
  };

  // 获取当前模板类型名称
  const getTemplateTypeName = () => {
    switch (type) {
      case '1':
        return '组件模板';
      case '2':
        return '应用模板';
      case '3':
      default:
        return '表单模板';
    }
  };

  // 定义AMIS Schema
  const schema: Schema = {
    type: 'page',
    body: [
      {
        type: 'grid',
        className: 'p-3',
        columns: [
          {
            body: [
              {
                type: 'button-group',
                buttons: categories.map(category => ({
                  type: 'button',
                  label: category.name,
                  level: selectedCategory.id === category.id ? 'primary' : 'default',
                  className: 'category-button',
                  onClick: () => handleCategoryChange(category)
                }))
              }
            ],
            md: 9
          },
          {
            body: [
              {
                type: 'input-group',
                body: [
                  {
                    type: 'input-text',
                    placeholder: '请输入',
                    inputClassName: 'search-input',
                    name: 'keyword',
                    value: searchKeyword,
                    onChange: (value: string) => setSearchKeyword(value),
                    onEnter: () => handleSearch(searchKeyword)
                  },
                  {
                    type: 'button',
                    icon: 'fa fa-search',
                    onClick: () => handleSearch(searchKeyword)
                  }
                ]
              }
            ],
            md: 3
          }
        ]
      },
      {
        type: 'crud',
        api: {
          method: 'get',
          url: '/admin-api/system/component-template/page',
          data: {
            pageNo: '${page}',
            pageSize: '${perPage}',
            classificationId: selectedCategory && selectedCategory.id !== 'all' 
              ? selectedCategory.id 
              : undefined,
            name: searchKeyword || undefined,
            type: type,
            status: 1
          },
          requestAdaptor: function(api: any) {
            const data = {...api.data};
            Object.keys(data).forEach(key => {
              if (data[key] === undefined || data[key] === '' || data[key] === null) {
                delete data[key];
              }
            });
            api.data = data;
            return api;
          }
        },
        syncLocation: false,
        mode: 'cards',
        columnsCount: 4,
        card: {
          itemAction: {
            type: "button",
            actionType: type === '2' ? "link" : "dialog",
            link: type === '2' ? '/app${applicationId}/admin' : undefined,
            dialog: type !== '2' ? {
              title: `${getTemplateTypeName()}预览`,
              size: 'lg',
              body: [
                {
                  type: 'editor',
                  value: type == "4" ? '${jsonData}' : '${jsonData|bodyParse}',
                  name: 'cardBody',
                  hidden: true,
                },
                {
                  type: 'amis',
                  name: 'cardBody',
                }
              ]
            } : undefined
          },
          body: [
            {
              type: 'image',
              src: '${coverImage}',
              thumbRatio: '16:9',
              thumbMode: 'cover',
              imageMode: 'original',
              hiddenOn: "${coverImage === '' || coverImage === null}",
              style: {
                width: '355px',
                height: '200px',
                objectFit: 'cover'
              }
            },
            {
              type: 'container',
              visibleOn: "${coverImage === '' || coverImage === null}",
              style: {
                height: 200,
                backgroundColor: '#F7F8FA',
                overflow: 'auto'
              },
              body: [

                {
                  type: 'amis',
                  value: type == "4" ? '${jsonData|jsonParse}' : '${jsonData|bodyParse}',
                }
              ]
            },
            {
              type: 'flex',
              justify: 'space-between',
              align: 'center',
              items: [
                {
                  type: 'wrapper',
                  body: [
                    {
                      type: 'tpl',
                      tpl: '<div class="card-title">${name}</div>'
                    },
                    {
                      type: 'tpl',
                      tpl: '<div class="card-desc">${remark || "暂无描述"}</div>'
                    }
                  ]
                },
                {
                  type: 'dropdown-button',
                  label: '',
                  hideCaret: true,
                  icon: 'fa fa-ellipsis-h',
                  align: 'right',
                  trigger: 'hover',
                  className: 'p-4',
                  size: 'sm',
                  btnClassName: 'border-0',
                  buttons: [
                    {
                      type: 'button',
                      label: '查看',
                      actionType: 'dialog',
                      dialog:  {
                        title: `${getTemplateTypeName()}预览`,
                        size: 'lg',
                        body: [
                          {
                            type: 'editor',
                            value: type == '4' ? '${jsonData}' : '${jsonData|bodyParse}',
                            name: 'cardBody',
                            hidden: true,
                          },
                          {
                            type: 'amis',
                            name: 'cardBody',
                          }
                        ]
                      }
                    },
                    {
                      type: 'button',
                      label: '复制json',
                      actionType: 'copy',
                      content: '${jsonData}'
                    }
                  ]
                }
              ]
            },
          ]
        },
        footerToolbar: [
          'statistics',
          'switch-per-page',
          'pagination'
        ]
      }
    ]
  };

  return <AMISRenderer key={amisKey} schema={schema} />;
};

export default FormTemplate; 