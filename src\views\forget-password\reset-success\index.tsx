import React, {useState, useEffect} from 'react';
import {RouteComponentProps} from 'react-router-dom';
import {Button} from 'amis-ui';
import './index.scss';

export default function ResetSuccess({history}: RouteComponentProps) {
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(time => {
        if (time <= 1) {
          clearInterval(timer);
          history.push('/login');
          return 0;
        }
        return time - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="reset-success-page">
      <div className="form-content">
        <div className="page-title">找回密码</div>
        <div className="success-tip">密码重置成功，请前往登录</div>
        <div className="countdown-tip">
          <span className="countdown">{countdown}</span>秒后，页面自动跳转
        </div>
        <Button
          type="submit"
          level="primary"
          className="login-btn"
          block
          onClick={() => history.push('/login')}
        >
          登录
        </Button>
      </div>
    </div>
  );
} 