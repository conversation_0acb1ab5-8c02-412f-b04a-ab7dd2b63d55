import React, { useEffect, useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/component/AMISRenderer';

interface AmisTableProps {
  columns: any[];
  tableName: string;
  datasourceId: string;
  columnNames: any[];
  dataSetdetail: any;
  dataSetNameEditState?: boolean;
}

const AmisTable: React.FC<AmisTableProps> = ({
  columns,
  columnNames,
  tableName,
  datasourceId,
  dataSetdetail,
  dataSetNameEditState
}) => {
  // 状态管理集中在一起
  const [schema, setSchema] = useState<any>({});

  // 副作用处理
  useEffect(() => {
    if (columnNames.length > 0 && !dataSetNameEditState) {
      // 移除了schemaKey的更新，直接使用一个随机值或时间戳作为key
      const uniqueKey = Date.now();
      
      // 构建schema对象
      const newSchema = {
        type: 'page',
        title: '表数据',
        key: uniqueKey,
        regions: ['body'],
        pullRefresh: {
          disabled: true
        },
        body: [
          {
            type: 'crud',
            syncLocation: false,
            api: {
              method: dataSetdetail?.source != 1 ? 'post' : 'get',
              url: dataSetdetail?.source != 1 
                ? '/admin-api/system/data-source/query-column-data' 
                : `/admin-api/system/form-field-value/page/${dataSetdetail.associatedDataTable}`,
              data: dataSetdetail?.source != 1 
                ? {
                    columnNames,
                    tableName,
                    datasourceId,
                    pageNo: '${pageNo}',
                    pageSize: '${pageSize}'
                  } 
                : {
                    page: '${pageNo}',
                    perPage: '${pageSize}',
                  },
              reload: columnNames.join(',')
            },
            autoGenerateFilter: true,
            autoRefresh: true,
            bulkActions: [],
            itemActions: [],
            __filterColumnCount: 3,
            __features: [],
            __LastFeatures: [],
            __step: 0,
            columns: columns.map(col => ({
              label: col.title,
              name: col.name,
              type: 'text'
            })),
            id: 'u:7db41ebb7f3b',
            perPageAvailable: [5, 10, 20, 50, 100],
            messages: {},
            pageField: 'pageNo',
            perPageField: 'pageSize',
            alwaysShowPagination: true,
            footerToolbar: [
              {
                type: 'statistics'
              },
              {
                type: 'pagination'
              },
              {
                type: 'switch-per-page',
                tpl: '切换页码',
                wrapperComponent: '',
                id: 'u:a3243b41e34c'
              }
            ]
          }
        ],
        id: 'u:b79c1791b226',
        __editorStatebaseControlClassName: 'default',
        __editorStatebodyControlClassName: 'default'
      };
      
      setSchema(newSchema);
    }
  }, [columnNames, datasourceId]); // 移除了schemaKey依赖

  // 渲染
  return <AMISRenderer schema={schema} />;
};

export default AmisTable;
