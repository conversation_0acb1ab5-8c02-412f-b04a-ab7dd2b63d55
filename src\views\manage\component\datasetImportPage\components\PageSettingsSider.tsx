import React from 'react';

interface PageSettingsSiderProps {
  activeKey: string;
  onChange: (key: string) => void;
}

interface MenuItem {
  label: string;
  key: string;
}

const PageSettingsSider: React.FC<PageSettingsSiderProps> = ({activeKey, onChange}) => {
  const items: MenuItem[] = [
    {
      label: '基础设置',
      key: 'basicSettings'
    },
    {
      label: '权限设置',
      key: 'permissionSettings'
    },
    {
      label: '高级设置',
      key: 'advancedSettings'
    }
  ];

  return (
    <div className="page-data-settings-sider">
      <ul className="settings-menu">
        {items.map(item => (
          <li
            key={item.key}
            className={`settings-menu-item ${activeKey === item.key ? 'active' : ''}`}
            onClick={() => onChange(item.key)}
          >
            {item.label}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default PageSettingsSider; 