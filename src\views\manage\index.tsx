import React, {useEffect, useState, useCallback} from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {Layout, toast} from 'amis';
import {Route, RouteComponentProps, Switch} from 'react-router';
import AMIS<PERSON>enderer from '@/component/AMISRenderer';
import {
  createIframeObj,
  createReportObj,
  createFromObj
} from '@/utils/schemaPageTemplate/createPageObjs';

// 数据源管理
import ManageDataSource from '@/views/manage/component/dataSource/index';
import ManageDataSet from '@/views/manage/component/dataSet/index';
import CreateDataSet from '@/views/manage/component/createDataSet/index';
import CreateDatasetImportPage from '@/views/manage/component/CreateDatasetImportPage/index';
import DatasetImportPage from '@/views/manage/component/datasetImportPage/index';
// 页面链接页面组件
import PagelinkContent from '@/component/PagelinkContent/index';
import Company from '@/views/manage/component/company/index';
import MenuManager from '@/views/manage/component/menuManager/index';

import AccountAuth from '@/views/manage/component/accountAuth/index';
import AppPermission from '@/views/manage/component/appPermission/index';
import OrgPermission from '@/views/manage/component/orgPermission/index';
import ResourceManage from '@/views/manage/component/resourceManage/index';
import UserManage from '@/views/manage/component/userManage/index';
import DeptManage from '@/views/manage/component/deptManage/index';
import PostManage from '@/views/manage/component/postManage/index';
import SystemComponent from '@/views/systemComponent/index'
import OrgComponent from '@/views/orgComponent/index'
// 页面样式
import './index.scss';
// 页面组件
import CommonHeader from '@/views/components/CommonHeader';
import RenderAside from './component/Aside/index';
import ControlDictionary from './component/controlDictionary/index';
// 接口引入
import {
  getTreeUserList,
  getTreeDeptList,
  getApplicationPageAndClassBackendList,
  getRoleList,
  getPosList,
} from '@/utils/api/api';
export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<any>) {
     

    const baseMenuList = [
      {
        id: 1,
        name: '企业设置',
        path: '/organization/manage/company',
        icon: 'fa fa-building',
        children: [],
        system: true
      },
      {
        id: 7,
        name: '组件管理',
        path: '/organization/manage/componentManager',
        icon: 'fa fa-bars',
        children: [
          {
            id: 71,
            name: '系统组件管理',
            path: '/organization/manage/systemComponentManager',
            icon: 'fa fa-database',
            children: [],
            system: true
          },
          {
            id: 72,
            name: '组织组件管理',
            path: '/organization/manage/orgComponentManager',
            icon: 'fa fa-database',
            children: [],
            system: true
          },
        ],
        system: true
      },
      {
        id: 2,
        name: '菜单管理',
        path: '/organization/manage/menuManager',
        icon: 'fa fa-bars',
        children: [],
        system: true
      },
      {
        id: 3,
        name: '数据管理',
        path: '/organization/manage/dataManage',
        icon: 'fa fa-building',
        type: 2,
        children: [
          {
            id: 31,
            name: '控件字典管理',
            path: '/organization/manage/controlDictionary',
            icon: 'fas fa-hammer',
            children: [],
            system: true
          },
          {
            id: 32,
            name: '数据源管理',
            path: '/organization/manage/dataSource',
            icon: 'fa fa-database',
            children: [],
            system: true
          },
          {
            id: 33,
            name: '数据集管理',
            path: '/organization/manage/dataSet',
            icon: 'fa fa-table',
            children: [],
            system: true
          }
          // {
          //   id: 24,
          //   name: '数据集导入页面',
          //   path: '/organization/manage/datasetImportPage',
          //   icon: 'fa fa-table',
          //   children: [],
          //   system: true
          // }
        ]
      },
      {
        id: 4,
        name: '搭建权限',
        path: '/organization/manage/buildPermission/manage',
        icon: 'fa fa-unlock',
        children: [
          {
            id: 41,
            name: '账号授权',
            path: '/organization/manage/buildPermission/userAuthorization',
            icon: 'fas fa-stamp',
            children: [],
            system: true
          },
          {
            id: 42,
            name: '应用管理员',
            path: '/organization/manage/buildPermission/appAdmin',
            icon: 'fa fa-unlock',
            children: [],
            system: true
          },
          {
            id: 43,
            name: '资源权限',
            path: '/organization/manage/buildPermission/resourceManage',
            icon: 'fa fa-unlock',
            children: [],
            system: true
          }
        ],
        
        system: true
      },
      {
        id: 5,
        name: '组织权限',
        path: '/organization/manage/orgAdmin',
        icon: 'fa fa-unlock',
        children: [
        ],
        
        system: true
      },
      {
        id: 6,
        name: '组织架构',
        path: '/organization/manage/org',
        icon: 'fa fa-users',
        type: 2,
        children: [
          {
            id: 61,
            name: '人员管理',
            path: '/organization/manage/org/users',
            icon: 'fas fa-user',
            children: [],
            system: true
          },
          {
            id: 62,
            name: '部门管理',
            path: '/organization/manage/org/dept',
            icon: 'fas fa-user',
            children: [],
            system: true
          },
          {
            id: 63,
            name: '岗位管理',
            path: '/organization/manage/org/post',
            icon: 'fas fa-user',
            children: [],
            system: true
          }
        ]
      },
    ];
    const [navMenuList, setNavMenuList] = useState<any[]>([]);

    const [loading, setLoading] = useState(true);

       // 提取数据加载逻辑为独立函数
       const loadOrganizationData = () => {
        getTreeUserList().then((res: any) => {
          let options = res.data || [];
  
          // 扁平化选项，用于静态展示查找
          const flattenedOptions = flattenOptions(options);
          localStorage.setItem(
            'userFlattenedOptions',
            JSON.stringify(flattenedOptions)
          );
          localStorage.setItem('userTreeList', JSON.stringify(res.data || []));
        });
  
        getTreeDeptList().then((res: any) => {
          let options = res.data || [];
  
          // 扁平化选项，用于静态展示查找
          const flattenedOptions = flattenOptions(options);
          localStorage.setItem(
            'deptFlattenedOptions',
            JSON.stringify(flattenedOptions)
          );
          localStorage.setItem('deptTreeList', JSON.stringify(res.data || []));
        });
  
        getRoleList().then((res: any) => {
          localStorage.setItem('roleList', JSON.stringify(res.data?.list || []));
        });
  
        getPosList().then((res: any) => {
          localStorage.setItem('posList', JSON.stringify(res.data?.list || []));
        });
      };
      
    // 监听企业切换事件
    useEffect(() => {
      const handleTenantSwitch = () => {
        // 重新加载组织相关数据
        loadOrganizationData();
      };

      // 添加事件监听器
      window.addEventListener('tenantSwitch', handleTenantSwitch as EventListener);

      // 清理事件监听器
      return () => {
        window.removeEventListener('tenantSwitch', handleTenantSwitch as EventListener);
      };
    }, []);

    // 扁平化选项，便于快速查找
    const flattenOptions = (options: any[] = []): Record<string, string> => {
      const result: Record<string, string> = {};

      const process = (items: any[] = []) => {
        items.forEach(item => {
          if (item.id) {
            result[item.id.toString()] =
              item.name || item.nickname || item.label || '未命名';
          }
          if (Array.isArray(item.children) && item.children.length > 0) {
            process(item.children);
          }
        });
      };

      process(options);
      return result;
    };

    // 获取后台管理的菜单：应用页面与分类
    const handleGetApplicationPageAndClassList = async () => {
      setLoading(true);
      try {
        let data = {
          applicantOrBackend: 2
        };
        const res = await getApplicationPageAndClassBackendList(data);
        if (res.code == 0) {
          // 转换API返回的数据格式
          const apiMenuList = res.data.map((item: any) => {
            return {
              ...item,
              // path: item.route?`/manage/page${item.route}`:`/manage/page${item.id}`,
              path: item.route
                ? `/organization/manage/page${item.route}`
                : `/organization/manage/page${item.id}`,
              // icon: item.icon || 'fa fa-table',
              icon: item.icon,
              children: item.children || []
            };
          });

          // 获取固定的基础菜单项
          const baseMenuItems = [
            {
              id: 1,
              name: '企业设置',
              path: '/organization/manage/company',
              icon: 'fa fa-building',
              children: [],
              system: true
            },
            {
              id: 2,
              name: '菜单管理',
              path: '/organization/manage/menuManager',
              icon: 'fa fa-bars',
              children: [],
              system: true
            },
            {
              id: 3,
              name: ' 数据管理',
              type: 2,
              path: '/organization/manage/dataManage',
              icon: 'fa fa-building',
              children: [
                {
                  id: 22,
                  name: '数据源管理',
                  path: '/organization/manage/dataSource',
                  icon: 'fa fa-database',
                  children: [],
                  system: true
                },
                {
                  id: 23,
                  name: '数据集管理',
                  path: '/organization/manage/dataSet',
                  icon: 'fa fa-table',
                  children: [],
                  system: true
                }
                // {
                //   id: 24,
                //   name: '数据集导入页面',
                //   path: '/organization/manage/datasetImportPage',
                //   icon: 'fa fa-table',
                //   children: [],
                //   system: true
                // }
              ]
            }
          ];

          // 合并基础菜单和API返回的菜单
          const newNavMenuList = [...baseMenuList, ...apiMenuList];
          setNavMenuList(newNavMenuList);

          // 在设置完菜单后立即查找并设置当前路径对应的菜单项
          if (location.pathname) {
            const result = findMenuItemByPath(
              newNavMenuList,
              location.pathname
            );
            if (result) {
              setInitPathItem(result.item);
              setParentMenuIds(result.parentIds);
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch menu:', error);
      } finally {
        setLoading(false);
      }
    };

    // 递归查找菜单项
    const findMenuItemByPath = (
      menuList: any[],
      path: string,
      parentIds: string[] = []
    ): any => {
      // 处理 /manage/page{id} 格式的路径
      // const isPagePath = path.match(/^\/manage\/page\d+$/);
      const isPagePath = path.match(/^\/organization\/manage\/page/);
      for (const item of menuList) {
        // 直接匹配当前项
        if (
          (isPagePath && item.path === path) ||
          (!isPagePath && item.path === path)
        ) {
          return {
            item,
            parentIds
          };
        }

        // 检查子菜单
        if (item.children && item.children.length > 0) {
          const found = findMenuItemByPath(item.children, path, [
            ...parentIds,
            item.id.toString()
          ]);
          if (found) {
            return found;
          }
        }
      }

      return null;
    };

    const [initPathItem, setInitPathItem] = useState<any>({});
    const [parentMenuIds, setParentMenuIds] = useState<string[]>([]);

    useEffect(() => {
      handleGetApplicationPageAndClassList();
    }, []); // 只在组件挂载时执行一次

    // 路由变化时更新选中项
    useEffect(() => {
      if (navMenuList.length > 0 && location.pathname) {
        // const isPagePath = location.pathname.match(/^\/manage\/page\d+$/);
        const isPagePath = location.pathname.match(
          /^\/organization\/manage\/page/
        );
        if (isPagePath) {
          const result = findMenuItemByPath(navMenuList, location.pathname);
          if (result) {
            setInitPathItem(result.item);
            setParentMenuIds(result.parentIds);
          }
        } else {
          // 对于系统菜单，也需要查找并设置选中状态
          const result = findMenuItemByPath(navMenuList, location.pathname);
          if (result) {
            setInitPathItem(result.item);
            setParentMenuIds(result.parentIds);
          } else {
            // 如果找不到匹配项，才重置
            setInitPathItem({});
            setParentMenuIds([]);
          }
        }
      }
    }, [location.pathname, navMenuList]);

    const refreshMenu = useCallback(() => {
      // 使用已定义的获取菜单数据的方法
      handleGetApplicationPageAndClassList();
    }, [handleGetApplicationPageAndClassList]);

    // 根据路由参数渲染对应的组件
    const renderComponent = () => {
      if (loading) {
        return <div>Loading...</div>; // 或使用一个加载指示器组件
      }

      const path = location.pathname;
      console.log('path', path);
      // const pageMatch = path.match(/^\/manage\/page(\d+)$/);
      const pageMatch = path.match(/^\/organization\/manage\/page/);

      switch (true) {
        case path === '/organization/manage/buildPermission/userAuthorization':
          return <AccountAuth history={history} location={location}/>;
        case path === '/organization/manage/buildPermission/appAdmin':
          return <AppPermission history={history} location={location}/>;
        case path === '/organization/manage/orgAdmin':
          return <OrgPermission history={history} location={location}/>;
        case path === '/organization/manage/buildPermission/resourceManage':
          return <ResourceManage history={history} location={location}/>;
        case path === '/organization/manage/org/users':
          return <UserManage history={history} location={location}/>;
          case path === '/organization/manage/org/dept': 
          return <DeptManage history={history} location={location}/>;
        case path === '/organization/manage/org/post':
          return <PostManage history={history} location={location}/>;
        case path === '/organization/manage/company':
          return <Company history={history} dataInfo="hello" />;
        case path === '/organization/manage/menuManager':
          return <MenuManager history={history} onMenuRefresh={refreshMenu} />;
        case path === '/organization/manage/dataSource':
          return <ManageDataSource history={history} />;
        case path === '/organization/manage/dataSet':
          return <ManageDataSet history={history} />;
        case path === '/organization/manage/dataSet/create':
          return <CreateDataSet history={history} location={location} />;
        case path === '/organization/manage/systemComponentManager':
          return <SystemComponent history={history} location={location}></SystemComponent>
        case path === '/organization/manage/orgComponentManager':
          // return <div>hello</div>
          return <OrgComponent history={history} location={location}></OrgComponent>
        case path === '/organization/manage/datasetImportPage':
          return (
            <CreateDatasetImportPage
              history={history}
              location={location}
              onMenuRefresh={refreshMenu}
            />
          );
        case path === '/organization/manage/controlDictionary':
          return (
            <ControlDictionary
              history={history}
              location={location}
              match={match}
              store={store}
            />
          );
        case !!pageMatch:
          // 判断是否有url或dataSetId
          if (initPathItem && Object.keys(initPathItem).length > 0) {
            if (initPathItem.url) {
              // 如果有url，显示PagelinkContent
              return (
                // <PagelinkContent
                //   pageData={initPathItem}
                //   history={history}
                //   match={match}
                //   updatePage={() => handleGetApplicationPageAndClassList()}
                // />
                <AMISRenderer
                  schema={createIframeObj(initPathItem.url)}
                  embedMode={true}
                  className="full-screen-amis"
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    zIndex: 100
                  }}
                />
              );
            } else if (initPathItem.dataSetId) {
              // 如果有dataSetId，显示DatasetImportPage
              return (
                <DatasetImportPage
                  history={history}
                  location={location}
                  match={match}
                  store={store}
                  initPathItem={initPathItem}
                  onMenuRefresh={refreshMenu}
                />
              );
            }
          }
        default:
          return null;
      }
    };

    return (
      <Layout
        aside={
          <RenderAside
            store={store}
            location={location}
            history={history}
            match={match}
            navMenuList={navMenuList}
            initPathItem={initPathItem}
            parentMenuIds={parentMenuIds}
          />
        }
        asideClassName={'asidePagesClass'}
        header={
          <CommonHeader
            store={store}
            history={history}
            type="appNoApplication"
            className="manageHeader"
          />
        }
        headerClassName={'asidePagesClass'}
        headerFixed={true}
        folded={store.asideFolded}
        offScreen={store.offScreen}
      >
        <Switch>
          <Route render={() => renderComponent()} />
        </Switch>
      </Layout>
    );
  })
);
