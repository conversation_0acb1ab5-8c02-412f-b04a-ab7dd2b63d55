.rebind-mobile-dialog {
  margin: 0 !important;
  padding: 0 24px !important;

  .dialog-header {
    margin-bottom: 16px;
    font-size: 14px;
    color: var(--text-color);
    font-weight: bold;
  }

  
  .verify-tabs {
    margin-bottom: 16px;
    
    .cxd-Tabs-link,
    .dark-Tabs-link{
      font-size: 14px;
      color: var(--text-color);
      padding: 12px 0;

      &:hover {
        color: var(--primary);
      }

      &.is-active {
        color: var(--primary);
      }
    }
  }

  .cxd-Tabs--line > .cxd-Tabs-linksContainer-wrapper::before,
  .dark-Tabs--line > .dark-Tabs-linksContainer-wrapper::before{
    border: none !important;
  }


  .cxd-Tabs-pane,
  .dark-Tabs-pane {
    padding: 0;
  }

  


  .form-item {
    margin-bottom: 20px;

    .form-control {
      width: 100%;
      height: 40px;
      padding: 10px;
      font-size: 14px;
  
      border-width: 1px;
      border-style: solid;
      border-color: var(--colors-neutral-fill-none);
      background: var(--light-bg)!important;
      border-radius: 4px;
      color: var(--text-color)!important;
  
      &:hover:not(:disabled) {
        border-color: var(--primary);
        color: var(--primary)!important;
      }
  
      &::placeholder {
        color: var(--text--muted-color);
      }
  
      &:focus {
        border-color: var(--primary);
        outline: none;
      }
    }

    &.code-item {
      display: flex;
      gap: 16px;

    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 4px;

    button {
      height: 32px;
      padding: 6px 21px;
      border: 1px solid var(--borderColor);
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;

      &:last-child {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.verify-code-btn {
  border: 1px solid var(--borderColor);
  border-radius: 4px;
  color: var(--text-color);
  font-size: 14px;
  height: 40px !important;
  padding: 10px 25px;
  cursor: pointer;
  background-color: var(--light-bg);

  &:hover:not(:disabled) {
    border-color: var(--primary);
    color: var(--primary);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
} 