import React, {useState, useEffect} from 'react';
import {setDefaultTheme} from 'amis';
import {IMainStore} from '../../store';
import './index.scss';
import {toggleDarkMode} from '@/utils/darkMode/darkMode';

interface ThemeSwitchProps {
  className?: string;
  store?: IMainStore;
  type?: 'button' | 'switch';
}

/**
 * 主题切换组件
 */
const ThemeSwitch: React.FC<ThemeSwitchProps> = ({
  className = '',
  store
}) => {
  // 从localStorage获取主题设置，如果没有则默认使用cxd
  const [theme, setTheme] = useState<Boolean>(true);


  
  // 当主题发生变化时，更新localStorage和document.body的class
  useEffect(() => {
    if(theme){
      toggleDarkMode(true);
    }else{
      toggleDarkMode(false);
    }
  }, [theme, store]);

  // 根据类型渲染不同的UI
    return (
      <div className={`theme-switch ${className}`}>
        <label className="switch-wrapper">
          <input
            type="checkbox"
            checked={!theme}
            onChange={()=>{setTheme(!theme)}}
          />
          <span className="slider">
            <span className="mode-text">
              {!theme ? '暗黑2' : '明亮2'}
            </span>
          </span>
        </label>
      </div>
    );
};

export default ThemeSwitch;
