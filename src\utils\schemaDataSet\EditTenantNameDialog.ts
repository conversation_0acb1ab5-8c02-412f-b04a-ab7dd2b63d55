export const EditTenantNameDialog = (show: boolean, currentName?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑企业全称</b>'
    },
    showCloseButton: false,
    data: {
      name: currentName
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入企业全称'
      },
      body: [
        {
          type: 'input-text',
          name: 'name',
          placeholder: '企业全称',
          required: true,
          maxLength: 50,
          validations: {
            maxLength: 50
          },
          inputClassName: 'name-input',
          labelClassName: 'name-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 