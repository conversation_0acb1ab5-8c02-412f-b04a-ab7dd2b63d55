body {
  background-color: var(--body-bg);
}

.appTitleContainer {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  padding: 0px 28px;
  gap: 14px;
  align-self: stretch;
  z-index: 0;
}

.appTitleBox {
  width: 100%;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0px;
  gap: 6px;
  align-self: stretch;
  z-index: 0;
  margin-bottom: 20px; /* 标题框和内容之间的间距 */
  background-color: var(--background-color);
}

.appTitle {
  width: 80px;
  height: 28px;
  z-index: 1;
  font-weight: bold;
  font-family: PingFang SC;
  font-size: 20px;
  line-height: normal;
  text-align: left; /* 文字左对齐 */
  letter-spacing: normal;
  color: var(--text-color);
}

.appDescription {
  width: 252px;
  height: 20px;
  z-index: 1;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  text-align: left; /* 文字左对齐 */
  letter-spacing: normal;
  color: var(--description-color);
}

.appPublishContainer {
  width: 1280px;
  height: 1280px;
  display: flex;
  flex-direction: column;
  padding: 0px;
  flex-grow: 1;
  z-index: 0;
}

.appPublishBox {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
  width: 100%;
  min-height: 100vh; /* 确保内容占满整个视口高度 */
  padding: 20px; /* 添加内边距以避免内容紧贴边缘 */
  box-sizing: border-box;
  background-color: var(--background-color);
}

.appPublishSection {
  background-color: var(--section-background-color); /* 白色背景 */
  margin-bottom: 20px; /* 每个块之间的间距 */
  padding: 20px; /* 内边距 */
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  width: 100%;
  max-width: 1200px; /* 设置最大宽度以限制内容宽度 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid var(--colors-neutral-text-8); /* 添加边框颜色变量 */
}

.appPublishLeft {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0px;
  gap: 2px;
  z-index: 0;
}

.appPublishRow {
  display: flex;
  align-items: center;
  gap: 10px; /* 按钮和文字之间的间距 */
}

.appPublishSeparator {
  width: 2px;
  height: 24px; /* 竖线的高度 */
  background-color: var(--separator-color); /* 浅灰色 */
}

.appPublishText {
  height: 16px;
  z-index: 2;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  text-align: justify; /* 浏览器可能不支持 */
  letter-spacing: normal;
  color: var(--colors-success-6);
}

.appPublishDescription {
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: justify; /* 浏览器可能不支持 */
  letter-spacing: normal;
  color: var(--description-color);
}

.appPublishInput {
  width: 822px;
  height: 28px;
  display: flex;
  align-items: center;
  padding: 6px 10px;
  align-self: stretch;
  z-index: 0;
  border-radius: 4px;
  background: var(--colors-neutral-text-9);
  box-sizing: border-box;
  border: 1px solid var(--colors-neutral-text-8);
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  letter-spacing: normal;
  color: var(--input-text-color);
}

.appPublishRight {
  display: flex;
  align-items: center;
  padding: 0px;
  gap: 14px;
  align-self: stretch;
  z-index: 0;
}

.appPublishSuccessButton {
  width: 52px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px 8px;
  gap: 4px;
  z-index: 0;
  background: var(--colors-success-6);
  box-sizing: border-box;
  border: 1px solid #30BF13;
  //color: var(--danger-button-text-color); /* 字体颜色设为白色 */
  color: #ffffff; /* 字体颜色设为白色 */
}

.appPublishDangerButton {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 4px;
  z-index: 1;
  border-radius: 4px;
  background: #F23D3D;
  border: 1px solid #F23D3D;
  //color: var(--danger-button-text-color); /* 字体颜色设为白色 */
  color: #ffffff; /* 字体颜色设为白色 */
}

.appPublishSection h2, .appPublishSection h3 {
  margin-right: 20px; /* 文字和按钮之间的间距 */
}

.appPublishSection p {
  margin-right: 20px; /* 文字和按钮之间的间距 */
}

.appPublishButtons {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.appPublishButtons button {
  margin-top: 10px; /* 按钮之间的间距 */
}

.address-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 输入框和按钮组居中 */
  gap: 10px;

  label {
    text-align: left; /* 标签文字居左 */
  }

  input {
    width: 100%; /* 输入框宽度占满父容器 */
    max-width: 600px; /* 限制输入框最大宽度 */
  }

  button {
    width: auto; /* 按钮宽度自适应 */
  }
}
