.manage-layout {
  min-height: 100vh;
  background-color: #F6F6F6;

  &-content {
    margin-left: 200px;
    padding-top: 56px;
    min-height: calc(100vh - 56px);
    transition: background-color 0.3s;
  }
}

[data-theme="dark"] {
  .manage-layout {
    background-color: var(--colors-neutral-fill-1);
    
    &-content {
      background-color: var(--colors-neutral-fill-1);
    }
  }
}

:root {
  --layout-header-bg: #fff;
  --layout-aside-bg: #fff;
  --layout-content-bg: #F6F6F6;
}

[data-theme="dark"] {
  --layout-header-bg: var(--colors-neutral-fill-2);
  --layout-aside-bg: var(--colors-neutral-fill-2);
  --layout-content-bg: var(--colors-neutral-fill-1);
}
