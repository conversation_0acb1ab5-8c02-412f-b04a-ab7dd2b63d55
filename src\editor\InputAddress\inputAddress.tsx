import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class InputAddressPlugin extends BasePlugin {
  static id = 'InputAddressPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'input-address';
  $schema = '/schemas/AvatarControlSchema.json';

  // 组件基本信息
  name = '地址';
  panelTitle = '地址';
  icon = 'fa fa-map-marker';  // 更改为地址相关图标
  panelIcon = 'fa fa-map-marker';
  pluginIcon = 'icon-image-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '省市区+详细地址输入';
  docLink = '/amis/zh-CN/components/form/input-city';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'input-address',
    label: '地址',
    name: 'address',
    clearable: true,
    allowCity: true,
    allowDistrict: true,
    searchable: true,
    required: false,
    detailAddressPlaceholder: '请输入详细地址',
  };

    // 添加事件定义
    events = [
      {
        eventName: 'click',
        eventLabel: '菜单项点击',
        description: '点击导航项时触发'
        // ...
      },
      {
        eventName: 'select',
        eventLabel: '菜单项选中',
        description: '选中导航项时触发'
        // ...
      },
      {
        eventName: 'expand',
        eventLabel: '菜单展开',
        description: '菜单展开时触发'
        // ...
      },
      {
        eventName: 'collapse',
        eventLabel: '菜单折叠',
        description: '菜单折叠时触发'
        // ...
      },
      {
        eventName: 'loaded',
        eventLabel: '数据加载完成',
        description: '数据加载完成时触发'
        // ...
      }
    ];
  

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                // getSchemaTpl('layout:originPosition', {
                //   value: 'left-top'
                // }),
                // getSchemaTpl('formItemName', {
                //   required: true
                // }),
                // getSchemaTpl('label'),
                getSchemaTpl('required'),
                getSchemaTpl('clearable'),
                getSchemaTpl('switch', {
                  name: 'allowCity',
                  label: '可选城市',
                  value: true,
                }),
                getSchemaTpl('switch', {
                  name: 'allowDistrict',
                  label: '可选区域',
                  value: true,
                }),
                getSchemaTpl('switch', {
                  name: 'searchable',
                  label: '可搜索',
                  value: true,
                }),
                getSchemaTpl('title', {
                  name: 'detailAddressPlaceholder',
                  label: '详细地址占位符',
                  value: '请输入详细地址',
                })
              ]
            },
            
            getSchemaTpl('status', {
              isFormItem: true,
              readonly: true
            })
          ]
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(InputAddressPlugin);
