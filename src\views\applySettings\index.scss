.asidePagesClassSet {
    background-color: var(--body-bg);
    color: var(--text-color);
    border-right: 1px solid var(--borderColor);

    .cxd-Layout-asideWrap,
    .dark-Layout-asideWrap{
      position: relative;
      top: 0;
    }
  }
  
  .headerPagesClass {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #fcfdfc !important;
    border-bottom: 1px solid #e8e8e8;
  }
  

  .myIframe{
    width: 100%;
    height: 100%;
    border-top-width: 0px;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    overflow-clip-margin: content-box !important;
    overflow: clip !important;
    border-style: inset;
    border-color: initial;
    border-image: initial;
  }

