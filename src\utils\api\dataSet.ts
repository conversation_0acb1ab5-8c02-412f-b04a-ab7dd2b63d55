import {post} from '@/utils/services/request';
import {get} from '@/utils/services/request';

// // 登录接口/login/login
// interface loginData {
//     username: string;
//     password: string;
//     captcha: string | number;
//     key: string;
//     remember: boolean | string;
//   }
//   const login = (data: loginData): Promise<any> => post('/login/login', data);
//   export {login};

//   // 获取用户信息
//   const getUserInfo = (): Promise<any> => get('/index/getUserInfo');
//   export {getUserInfo};
/**
 * 获取数据集列表 Dataset/getDatasetList GET
 * @param params
 * @param params.apply_id - 应用id
 * @param params.id	否	string	数据集id
 * @param params.dataset_name	否	string	数据集名称
 * @param params.remark	否	string	备注
 * @param params.source_id	否	string	数据源id
 * @returns {*}
 */
interface getDatasetListData {
  apply_id: string; //应用id
  id?: string; //数据集id
  dataset_name?: string; //数据集名称
  remark?: string; //备注
  source_id?: string; //数据源id
  
}
const getDatasetList = (data: getDatasetListData): Promise<any> =>
  get('/Dataset/getDatasetList', data);
export {getDatasetList};

/**
 * 获取数据集表字段 /report/getFieldList
 * @param params
 * @param params.dataset_id - 	数据集id
 * @returns {*}
 */
interface getFieldListData {
  dataset_id: string; //数据集id
}
const getFieldList = (data: getFieldListData): Promise<any> => get('/report/getFieldList', data);
export {getFieldList};

/**
 * 获取数据集表字段 /dataset/getTableDataList
 * @param params
 * @param params.source_id - 	数据源id
 * @param params.table_name - 表名
 * @param params.page
 * @param params.limit
 * @returns {*}
 */
interface getTableDataListData {
  source_id: string; //数据源id
  table_name: string; //表名
  page: number;
  limit: number;
}
const getTableDataList =  (data: getTableDataListData): Promise<any> => get("/dataset/getTableDataList", data);
  
  export { getTableDataList };