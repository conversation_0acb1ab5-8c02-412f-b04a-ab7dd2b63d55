import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class MyAppPlugin extends BasePlugin {
  static id = 'MyAppPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'app';
  $schema = '/schemas/MyAppSchema.json';

  // 组件基本信息
  name = 'App 应用';
  panelTitle = 'App 应用';
  icon = 'fa fa-window-maximize';
  panelIcon = 'fa fa-window-maximize';
  pluginIcon = 'fa fa-window-maximize';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = 'App 应用容器，适合做多页渲染';
  docLink = '/amis/zh-CN/components/app';
  tags = ['门户'];

  // 组件默认配置
  scaffold = {
    type: 'app',
    brandName: '应用名称',
    logo: '',
    showBreadcrumb: true,
    showFullBreadcrumbPath: false,
    showBreadcrumbHomePath: true,
    className: 'app-wrapper',
    header: {
      type: 'tpl',
      tpl: '顶部区域'
    },
    asideBefore: {
      type: 'tpl',
      tpl: '边栏前区域'
    },
    asideAfter: {
      type: 'tpl',
      tpl: '边栏后区域'
    },
    footer: {
      type: 'tpl',
      tpl: '底部区域'
    },
    pages: [
      {
        label: '首页',
        url: '/',
        icon: 'fa fa-home',
        isDefaultPage: true,
        schema: {
          type: 'page',
          title: '首页',
          body: '欢迎使用'
        }
      },
      {
        label: '示例页面',
        url: '/example',
        icon: 'fa fa-file',
        schema: {
          type: 'page',
          title: '示例页面',
          body: '这是一个示例页面'
        }
      }
    ]
  };

  // 预览界面
  previewSchema = {
    type: 'app',
    className: 'text-left',
    brandName: '应用名称',
    showBreadcrumb: true,
    pages: [
      {
        label: '首页',
        url: '/',
        icon: 'fa fa-home',
        schema: {
          type: 'page',
          title: '首页',
          body: '欢迎使用'
        }
      }
    ]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                {
                  type: 'input-text',
                  name: 'brandName',
                  label: '应用名称',
                  description: '系统名称'
                },
                {
                  type: 'input-text',
                  name: 'logo',
                  label: 'Logo',
                  description: 'Logo图片地址，可以是svg'
                },
                {
                  type: 'input-text',
                  name: 'className',
                  label: 'CSS类名',
                  description: '自定义CSS类名'
                },
                {
                  type: 'switch',
                  name: 'showBreadcrumb',
                  label: '显示面包屑',
                  value: true,
                  description: '是否显示面包屑导航'
                },
                {
                  type: 'switch',
                  name: 'showFullBreadcrumbPath',
                  label: '完整面包屑',
                  value: false,
                  visibleOn: 'this.showBreadcrumb',
                  description: '是否显示完整的面包屑路径'
                },
                {
                  type: 'switch',
                  name: 'showBreadcrumbHomePath',
                  label: '首页面包屑',
                  value: true,
                  visibleOn: 'this.showBreadcrumb',
                  description: '是否显示面包屑首页路径'
                }
              ]
            },
            {
              title: 'API配置',
              body: [
                getSchemaTpl('apiControl', {
                  name: 'api',
                  label: '数据接口',
                  description: '用于获取页面配置的接口，返回的数据中需要包含pages字段'
                })
              ]
            },
            {
              title: '页面配置',
              body: [
                {
                  type: 'combo',
                  name: 'pages',
                  label: '页面列表',
                  multiple: true,
                  draggable: true,
                  addButtonText: '添加页面',
                  items: [
                    {
                      type: 'input-text',
                      name: 'label',
                      label: '菜单文字',
                      required: true,
                      description: '菜单显示的文字'
                    },
                    {
                      type: 'input-text',
                      name: 'url',
                      label: '路由规则',
                      required: true,
                      description: '如：/user/:id。当地址以 / 打头，则不继承上层的路径，否则将继承父级页面的路径'
                    },
                    {
                      type: 'icon-picker',
                      name: 'icon',
                      label: '图标',
                      placeholder: '请选择图标',
                      description: '菜单图标，例如：fa fa-file'
                    },
                    {
                      type: 'switch',
                      name: 'isDefaultPage',
                      label: '默认页面',
                      description: '路由找不到时的默认页面，不要出现多个，如果出现多个只有第一个有用'
                    },
                    {
                      type: 'switch',
                      name: 'visible',
                      label: '导航可见',
                      value: true,
                      description: '是否在导航中可见，适合于那种需要携带参数才显示的页面'
                    },
                    {
                      type: 'input-text',
                      name: 'className',
                      label: 'CSS类名',
                      description: '菜单上的类名'
                    },
                    {
                      type: 'input-text',
                      name: 'redirect',
                      label: '重定向地址',
                      description: '当match url 时跳转到目标地址，没有配置 schema 和 schemaApi 时有效'
                    },
                    {
                      type: 'input-text',
                      name: 'rewrite',
                      label: '重写地址',
                      description: '当match url 转成渲染目标地址的页面，没有配置 schema 和 schemaApi 时有效'
                    },
                    {
                      type: 'input-text',
                      name: 'link',
                      label: '外部链接',
                      description: '单纯的地址，可以设置外部链接'
                    },
                    getSchemaTpl('apiControl', {
                      name: 'schemaApi',
                      label: '页面接口',
                      description: '如果想通过接口拉取页面配置，可以设置该选项'
                    }),
                    {
                      type: 'combo',
                      name: 'children',
                      label: '子页面',
                      multiple: true,
                      draggable: true,
                      addButtonText: '添加子页面',
                      description: '支持多层级',
                      items: [
                        {
                          type: 'input-text',
                          name: 'label',
                          label: '菜单文字',
                          required: true
                        },
                        {
                          type: 'input-text',
                          name: 'url',
                          label: '路由规则',
                          required: true
                        },
                        {
                          type: 'icon-picker',
                          name: 'icon',
                          label: '图标',
                          placeholder: '请选择图标'
                        },
                        {
                          type: 'switch',
                          name: 'visible',
                          label: '导航可见',
                          value: true
                        },
                        {
                          type: 'input-text',
                          name: 'className',
                          label: 'CSS类名'
                        },
                        {
                          type: 'input-text',
                          name: 'redirect',
                          label: '重定向地址'
                        },
                        {
                          type: 'input-text',
                          name: 'rewrite',
                          label: '重写地址'
                        },
                        {
                          type: 'input-text',
                          name: 'link',
                          label: '外部链接'
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            {
              title: '加载配置',
              body: [
                {
                  type: 'switch',
                  name: 'loadingConfig.show',
                  label: '显示加载动画',
                  value: true,
                  description: '是否显示加载动画'
                },
                {
                  type: 'select',
                  name: 'loadingConfig.size',
                  label: '加载动画大小',
                  value: 'lg',
                  options: [
                    {label: '小', value: 'sm'},
                    {label: '中', value: 'md'},
                    {label: '大', value: 'lg'}
                  ],
                  visibleOn: 'this.loadingConfig && this.loadingConfig.show'
                },
                {
                  type: 'input-text',
                  name: 'loadingConfig.text',
                  label: '加载文字',
                  placeholder: '加载中...',
                  visibleOn: 'this.loadingConfig && this.loadingConfig.show'
                }
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '区域设置',
              body: [
                
              ]
            },
           
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(MyAppPlugin);
