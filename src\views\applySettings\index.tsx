import React from 'react';
import {observer, inject} from 'mobx-react';
import {IMainStore} from '@/store';
import {Layout, toast} from 'amis';
import {RouteComponentProps, Switch} from 'react-router';
import AMISRenderer from '@/component/AMISRenderer';
import {createReportObj} from '@/utils/schemaPageTemplate/createPageObjs';

// 页面样式
import './index.scss';

// 页面组件
import renderHeader from './component/Header/index';
import renderAside from './component/Aside/index';
import DataSet from './component/dataSet/index';
import DataSource from './component/dataSource/index';

// 接口api
import {javaApplication} from '@/utils/api/api';

import BasicSetting from './component/basicSetting/index';
import ApplyPermission from './component/applyPermission/index';
import PagePermission from './component/pagePermission/index';
import DataCardManage from './component/dataCardManage/index';
import AppComponent from '@/views/appComponent/index';
import APIFactory from '@/views/apiFactory';

export default inject('store')(
  observer(function ({
    store,
    location,
    history,
    match
  }: {store: IMainStore} & RouteComponentProps<any>) {
    /* data 数据 */
    // 导航菜单
    const navMenuList = [
      {
        id: 1,
        name: '基础设置',
        path: 'basicSetting',
        // icon: baseSet_icon,
        icon: 'cog',
        level: '1'
      },
      {
        id: 2,
        name: '导航设置',
        path: 'navSetting',
        // icon: nav_icon,
        icon: 'bars',
        level: '2'
      },
      {
        id: 3,
        name: '应用管理员',
        path: 'applyPermission',
        // icon: administrators_icon,
        icon: 'user', 
        level: '3'
      },
      {
        id: 4,
        name: '页面权限',
        path: 'pagePermission',
        // icon: permission_icon,
        icon: 'unlock-keyhole',
        level: '4'
      },
            {
        id: 8,
        name: '应用组件管理',
        path: 'appComponentManage',
        // icon: permission_icon,
        icon: 'unlock-keyhole',
        level: '8'
      },
      {
        id: 5,
        name: '运维部署',
        path: 'deployMaintenance',
        // icon: deployment_icon,
        icon: 'screwdriver-wrench',
        level: '5'
      },
      {
        id: 6,
        name: '酷卡片管理',
        path: 'cardManage',
        // icon: kuCard_icon,
        icon: 'server',
        level: '6'
      },
      {
        id: 9,
        name: 'API工厂',
        path: 'APIFactory',
        // icon: kuCard_icon,
        icon: 'server',
        level: '9'
      },
      {
        id: 7,
        name: '数据管理',
        path: 'dataSet',
        // icon: dataManage_icon,
        icon: 'database',
        level: '7',
        children: [
          {
            id: 1,
            name: '数据集',
            path: 'dataSet',
            level: '7-1'
          },
          {
            id: 2,
            name: '数据源',
            path: 'dataSource',
            level: '7-2'
          }
        ]
      }
    ];
    // 应用信息
    const [applyData, setApplyData] = React.useState<any>(false);

    // 选中的导航菜单
    const [checkNavItem, setCheckNavItem] = React.useState<any>(false);

    /* data 数据 end */

    /* methods 方法 */
    React.useEffect(() => {
      if (!applyData ) {
        getApplyData();
      }
    }, [applyData]);
    // 获取应用详情
    const getApplyData = () => {
      let data = {
        id: match.params.appId
      };
      javaApplication(data).then((res: any) => {
        if (res.code === 0 ) {
          // 存储应用信息 
          setApplyData(res.data);
        }
      });
    };
    // 事件，判断match.params.appSetMenu 是导航菜单navMenuList中元素的path的哪一个，然后选中checkNavItem
    const getCheckNavItem = () => {
      let path = match.params.appSetMenu;
      let list: any = navMenuList;
      for (let i = 0; i < list.length; i++) {
        if (!!list[i].children) {
          for (let j = 0; j < list[i].children.length; j++) {
            if (list[i].children[j].path === path) {
              setCheckNavItem(list[i].children[j]);
            }
          }
        } else {
          if (list[i].path === path) {
            setCheckNavItem(list[i]);
          }
        }
      }
    };

    /* methods 方法 end */

    /* created 初始化 */
    React.useEffect(() => {
      if (!applyData) {
        getApplyData();
      }
    }, [applyData]);
    React.useEffect(() => {
      if (match.params.appSetMenu) {
        getCheckNavItem();
      } else {
        // 如果没有appSetMenu参数，默认选中第一个菜单项（基础设置）
        if (navMenuList.length > 0) {
          setCheckNavItem(navMenuList[0]);
        }
      }
    }, [match.params.appSetMenu]);
    /* created 初始化 end */

    // 接收消息
    const [pageType, setPageType] = React.useState<any>('')
    React.useEffect(() => {
      // 从 location.search 获取查询参数 urlpage
      const searchParams = new URLSearchParams(location.search);
      const pageTypeData = searchParams.get('pageType'); // 获取 pageType 参数值
      if(pageTypeData){
        setPageType(pageTypeData);
      }
    }, [location]);
    

    return (
      <Layout
        aside={renderAside({
          store,
          location,
          history,
          match,
          applyData,
          navMenuList,
          checkNavItem,
          setCheckNavItem
        })}
        asideFixed={false}
        asideClassName={'asidePagesClassSet'}
        // header={pageType == 'urlpage' ? '' : renderHeader({store, location, history, match, applyData})}
        headerClassName={'headerPagesClass'}
        headerFixed={false}
        folded={store.asideFolded}
        offScreen={store.offScreen}
      >
        <Switch>
          {checkNavItem.link && applyData.id && (
            <AMISRenderer
              schema={createReportObj(
                `${checkNavItem.link}?applyId=${applyData.id}&iframeType=1&userId=${store.userInfo?.id}`
              )}
              embedMode={true}
            />
          )}
          {checkNavItem.path == 'basicSetting' && (
            <BasicSetting
              update={() => getApplyData()}
              history={history}
              store={store}
              applyData={applyData}
            />
          )}
          {checkNavItem.path == 'applyPermission' && (
            <ApplyPermission
              history={history}
              store={store}
              applyData={applyData} 

            />
          )}

          {
            checkNavItem.path == 'appComponentManage' && (
              <AppComponent
                history={history}
                store={store}
                match={match}
                applyData={applyData}
              />
            )
          }

          {checkNavItem.path == 'pagePermission' && (
            <PagePermission
              history={history}
              store={store}
              applyData={applyData}
            />
          )}
          {checkNavItem.path == 'dataSet' && (
            <DataSet
              history={history}
              store={store}
              applyData={applyData}
            />
          )}
          {checkNavItem.path == 'dataSource' && (
            <DataSource
              history={history}
              store={store}
              applyData={applyData}
            />
          )}
          {checkNavItem.path == 'dataCardManage' && (
            <DataCardManage
              history={history}
              store={store}
              applyData={applyData}
            />
          )}
          {checkNavItem.path == 'APIFactory' && (
            <APIFactory
              history={history}
              store={store}
              applyData={applyData}
            />
          )}
          
        </Switch>
      </Layout>
    );
  })
);
