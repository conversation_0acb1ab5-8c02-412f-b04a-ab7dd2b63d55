import React, { FC, useState, useEffect, useCallback } from 'react';
import './index.scss';
import { <PERSON><PERSON>, Tabs, Tab } from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import { GetPagePermission } from '@/utils/schemaDataSet/GetPagePermission';
import { GetPagePermissionStatus } from '@/utils/schemaDataSet/GetPagePermissionStatus';
import { editInterest } from '@/utils/schemaDataSet/editInterest';
import { editInterestStatus } from '@/utils/schemaDataSet/editInterestStatus';
import { editPermissionDialog } from '@/utils/schemaDataSet/editPermissionDialog';

import { getApplicationPagePermissionList, deleteApplicationPagePermission, createApplicationPagePermission } from '@/utils/api/api'

const PagePermission: FC<any> = (props: any) => {
  const [activeTab, setActiveTab] = useState<'submit' | 'view'>('submit');
  const [addInterest, setAddInterest] = useState<boolean>(false);
  const [addInterestStatus, setAddInterestStatus] = useState<boolean>(false);
  const [permissionList, setPermissionList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPermission, setCurrentPermission] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'card' | 'table'>('card');
  const [amisKey, setAmisKey] = useState<number>(0);
  const userTreeList = JSON.parse(localStorage.getItem('userTreeList') || '[]');
  
  // 添加防抖钩子
  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);
    
    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);
      
      return () => {
        clearTimeout(timer);
      };
    }, [value, delay]);
    
    return debouncedValue;
  };
  
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const debouncedSearchKeyword = useDebounce(searchKeyword, 300); // 300ms 防抖延迟

  const crudSchema = {
    type: "crud",
    syncLocation: false,
    showIndex: true,
    perPage: 100,
    api: {
      url: "/admin-api/system/application-page-permission/page",
      method: "get",
      data: {
        '&': '$$',
        applicationPageId: props.pageData?.id,
        type: activeTab === 'submit' ? '1' : '2',
        name: searchKeyword,
      },
      adaptor: function(payload: any, response: any, api: any, context: any) {
        console.log(payload.data.list);
        // 处理 payload.data.list，根据 userIds 设置 memberType
        if (payload.data && payload.data.list && Array.isArray(payload.data.list)) {
          payload.data.list = payload.data.list.map((item: any) => {
            return {
              ...item,
              memberType: item.userIds === 'all' ? 'all' : 'memberTypeCustom'
            };
          });
        }
        return {
          ...payload,
          data: {
            list: payload.data.list,
          }
        }
      }
    },
    columns: [
      {
        label: "权限组名称",
        name: "name",
      },
      {
        label: "描述",
        name: "remark",
      },
      {
        label: "成员",
        name: "userIds",
        type:  "user-multi-select",
        static: true,
      },
      {
        label: "操作权限",
        name: "type",
        type: "select",
        options: [
          {
            label: "提交",
            value: "1",
          },
          {
            label: "查看",
            value: "2",
          },
        ],
        static: true,
      },
      {
        label: "创建人",
        name: "creator",
        type: "user-select",
        static: true,
      },
      {
        label: '创建时间',
        type: 'input-datetime',
        name: 'createTime',
        sortable: true,
        static: true,
        valueFormat: 'x'
      },
      {
        label: '更新人',
        name: 'updater',
        type: 'user-select',
        static: true,
      },
      {
        label: '更新时间',
        type: 'input-datetime',
        name: 'updateTime',
        sortable: true,
        static: true,
        valueFormat: 'x'
      },
      {
        label: '操作',
        name: 'action',
        type: 'operation',
        buttons: [
          {
            label: '编辑',
            type: 'button',
            level: 'link',
            actionType: 'dialog',

            dialog: {
              type: 'dialog',
              title: '编辑',
              body: [
                {
                  type: 'form',
                  componentId: 'editForm',
                  api: {
                    url: "/admin-api/system/application-page-permission/update",
                    method: "put",
                    data: {
                      id: '${id}',
                      name: '${name}',
                      remark: '${remark}',
                      userIds: '${userIds}',
                    },
                    requestAdaptor: function(api: any, context: any) {
                      if (context.memberType == 'memberTypeCustom') {
                          context.userIds = context.userIds
                          return {
                              ...api,
                              data: {
                                  ...api.data,
                                  userIds: context.userIds,
                                  
                              }
                          }
                      } else {
                          return {
                              ...api,
                              data: {
                                  ...api.data,
                                  userIds: 'all'
                              }
                          }
                      }
                    }
                  },
                  body: [
                    {
                      name: 'id',
                      label: 'id',
                      type: 'static',
                      hidden: true
                    },
                    {
                      type: "input-text",
                      name: "name",
                      label: "名称",
                      required: true,
                      placeholder: "请输入权限组名称",
                  },
                  {
                      type: "textarea",
                      name: "remark",
                      label: "描述",
                      placeholder: "请输入描述信息",
                  },
                  {
                      type: "radios",
                      name: "memberType",
                      label: "权限成员",
                      required: true,
                      options: [
                          {
                              label: "全部成员",
                              value: "all",
                          },
                          {
                              label: "自定义",
                              value: "memberTypeCustom",
                          }
                      ],
                  },
                  {
                      type: "user-multi-select",
                      name: "userIds",
                      label: "成员",
                      placeholder: "请选择成员",
                      visibleOn: "${memberType == 'memberTypeCustom'}",
                      clearable: true,
                      multiple: true,
                      labelField: "name",
                      valueField: "id",
                      joinValues: true,
                      extractValue: true,
                      enableNodePath: false,
                      withChildren: true,
                      onlyChildren: true,
                      onlyLeaf: true,
                      cascade: true,
                      delimiter: ',',
                      source: {
                        data: JSON.parse(localStorage.getItem('userTreeList') || '[]')
                      }
                  },
                  {
                      type: "checkboxes",
                      name: "operationPermissions",
                      label: "操作权限",
                      disabled: true,
                      options: [
                          {
                              "label": activeTab == 'submit' ? "提交" : "查看",
                              "value": activeTab == 'submit' ? "submit" : "view"
                          }
                      ],
                      value: [activeTab == 'submit' ? "submit" : "view"],
                      style: {  // 单独设置某个选项的样式
                          clear: "both"
                      },
                  },
                    
                  ]
                }
              ]
            }
          },
          {
            label: '复制',
            type: 'button',
            actionType: 'ajax',
            level: 'link',
            // confirm: true,
            confirmText: '确定复制该权限组吗？',
            api: {
              url: "/admin-api/system/application-page-permission/create",
              method: "post",
              data: {
                applicationPageId: props.pageData?.id,
                type: activeTab === 'submit' ? '1' : '2',
                name: '${name} - 副本',
                remark: '${remark}',
                userIds: '${userIds}',
              },
              
            }
          },
          {
            label: '删除',
            type: 'button',
            actionType: 'ajax',
            level: 'link',
            className: 'text-danger',
            confirm: true,
            confirmText: '确定删除该权限组吗？',
            confirmLevel: 'danger',

            api: {
              url: "/admin-api/system/application-page-permission/delete",
              method: "delete",
              data: {
                id: '${id}'
              }
            }
          },
        ],
      },

    ]
  };

  const openAddInterest = () => {
    setCurrentPermission(null);
    setAddInterest(true);
  };

  // const openAddInterestStatus = () => {
  //   setAddInterestStatus(true);
  // };

  // 获取权限列表数据
  const fetchPermissionList = async () => {
    if (!props.pageData || !props.pageData.id) return;
    
    setLoading(true);
    try {
      const response = await getApplicationPagePermissionList({
        pageNo: 1,
        pageSize: 100,
        applicationPageId: props.pageData.id,
        type: activeTab === 'submit' ? '1' : '2'
      });
      
      if (response && response.data) {
        setPermissionList(response.data.list || []);
        setAmisKey(Date.now());
      }
    } catch (error) {
      console.error('获取权限列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 监听防抖后的搜索关键词变化
  useEffect(() => {
    setAmisKey(Date.now());
  }, [debouncedSearchKeyword]);

  // 根据搜索关键词过滤权限列表
  const filteredPermissionList = permissionList.filter(item => 
    item.name.toLowerCase().includes(debouncedSearchKeyword.toLowerCase())
  );

  useEffect(() => {
    console.log('PageContent props', props);
    fetchPermissionList();
  }, [props.pageData]);

  // 监听activeTab变化，重新请求数据
  useEffect(() => {
    if (props.pageData && props.pageData.id) {
      fetchPermissionList();
    }
  }, [activeTab]);

  // 权限组添加成功后刷新列表
  const handlePermissionAdded = () => {
    fetchPermissionList();
    setAddInterest(false);
    setAmisKey(Date.now());
  };

  // 编辑权限组
  const handleEditPermission = (permission: any) => {
    setCurrentPermission(permission);
    setAddInterest(true);
  };

  // 复制权限组
  const handleCopyPermission = (permission: any) => {
    const copiedPermission = {
      applicationPageId: props.pageData?.id || '',
      type: permission.type,
      name: `${permission.name} - 副本`,
      remark: permission.remark,
      userIds: permission.userIds
    };
    createApplicationPagePermission(copiedPermission).then((res: any) => {
      if (res.code == 0) {
        fetchPermissionList();
        setAmisKey(Date.now());
      }
    });
  };

  // 删除权限组
  const handleDeletePermission = (permissionId: string) => {
    deleteApplicationPagePermission(permissionId).then((res: any) => {
      if (res.code == 0) {
        fetchPermissionList();
        setAmisKey(Date.now());
      }
    });
  };

  // 根据userIds获取用户名称
  const getUserNamesByIds = (userIds?: string) => {
    if (userIds === 'all' || userIds == undefined) return '全部成员';
    
    try {
      const idArray = userIds.split(',').map(id => parseInt(id.trim(), 10));
      const userNames: string[] = [];
      
      // 递归查找用户
      const findUserInTree = (treeNodes: any[]) => {
        treeNodes.forEach(node => {
          if (node.children && node.children.length) {
            node.children.forEach((user: any) => {
              if (user.id && idArray.includes(user.id)) {
                userNames.push(user.name);
              }
            });
          }
          
          // 如果部门下还有部门，继续递归查找
          if (node.children && node.children.some((item: any) => item.children)) {
            node.children.forEach((item: any) => {
              if (item.children) {
                findUserInTree([item]);
              }
            });
          }
        });
      };
      
      findUserInTree(userTreeList);
      
      return userNames.length > 0 ? userNames.join(', ') : userIds;
    } catch (error) {
      console.error('解析用户ID失败:', error);
      return userIds;
    }
  };

  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveTab(key as 'submit' | 'view');
    setAmisKey(Date.now()); // 更新amisKey强制AMIS重新渲染
  };

  return (
    <div className="projectMain">
      <div className="projectMain-top_s">
        <div className="projectMain-top_s-left">
          <div className="projectMain-top_s-left-name">权限设置</div>
          <div className="projectMain-top_s-left-describe">
            对用户设置「表单的提交权限」和「数据的管理权限」，实现对数据的精细化管理。
          </div>
        </div>
      </div>

      <div className="projectMain-main_s">
        <Tabs
          mode="line"
          activeKey={activeTab}
          onSelect={handleTabChange}
          linksClassName="permission-tabs"
        >
          <Tab title="表单提交权限" eventKey="submit">
            <div className="permission-content">
              <div className="submit-content">
                <div className="itemList" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Button 
                    className="auto-button"
                    size="lg"
                    level="primary"
                    onClick={openAddInterest}
                  >
                    <span className="teamMain-top_s-right-btn-name">
                      新增权限组
                    </span>
                  </Button>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <div className="search-box" style={{ marginRight: '15px' }}>
                      <input 
                        type="text" 
                        placeholder="搜索" 
                        value={searchKeyword}
                        onChange={(e) => {
                          setSearchKeyword(e.target.value);
                        }}
                        style={{ 
                          padding: '5px 10px', 
                          borderRadius: '4px', 
                          border: '1px solid #ddd',
                          height: '32px'
                        }}
                      />
                    </div>
                    <div className="view-mode-switch">
                      <Button 
                        className={`mode-btn ${viewMode === 'card' ? 'active' : ''}`} 
                        size="sm" 
                        onClick={() => {
                          setViewMode('card');
                          setAmisKey(Date.now());
                        }}
                      >
                        卡片模式
                      </Button>
                      <Button 
                        className={`mode-btn ${viewMode === 'table' ? 'active' : ''}`} 
                        size="sm" 
                        onClick={() => {
                          setViewMode('table');
                          setAmisKey(Date.now());
                        }}
                      >
                        表格模式
                      </Button>
                    </div>
                  </div>
                </div>
                {viewMode === 'card' && filteredPermissionList.map((item: any, index: number) => (
                  <div key={item.id} className="permission-item">
                    <div className="permission-item-header">
                      <div className="permission-item-title-box">
                        <div className="permission-item-title">{item.name}</div>
                        <div className="permission-item-remark">{item.remark || ''}</div>
                      </div>
                      <div className="permission-item-actions">
                        <Button size="sm" className="action-btn" onClick={() => handleEditPermission(item)}>编辑</Button>
                        <Button size="sm" className="action-btn" onClick={() => handleCopyPermission(item)}>复制</Button>
                        
                        {index !== 0 && (
                          <Button size="sm" className="action-btn" onClick={() => handleDeletePermission(item.id)}>删除</Button>
                        )}
                      </div>
                    </div>
                    <div className="permission-item-content">
                      <div className="permission-row">
                        <div className="permission-label">权限成员:</div>
                        <div className="permission-value">{getUserNamesByIds(item.userIds)}</div>
                      </div>
                      <div className="permission-row">
                        <div className="permission-label">操作权限:</div>
                        <div className="permission-value">{item.type == '1' ? '提交' : '查看'}</div>
                      </div>
                    </div>
                  </div>
                ))}
                {viewMode === 'table' && (
                  <AMISRenderer
                    key={amisKey}
                    schema={crudSchema}
                    embedMode={true}
                  />
                )}
              </div>
            </div>
          </Tab>
          <Tab title="表单查看权限" eventKey="view">
            <div className="permission-content">
              <div className="view-content">
                <div className="itemList" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Button 
                    className="auto-button"
                    size="lg"
                    level="primary"
                    onClick={openAddInterest}
                  >
                    <span className="teamMain-top_s-right-btn-name">
                      新增权限组
                    </span>
                  </Button>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div className="search-box" style={{ marginRight: '15px' }}>
                      <input 
                        type="text" 
                        placeholder="按名称搜索" 
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        style={{ 
                          padding: '5px 10px', 
                          borderRadius: '4px', 
                          border: '1px solid #ddd',
                          height: '32px'
                        }}
                      />
                    </div>
                    <div className="view-mode-switch">
                      <Button 
                        className={`mode-btn ${viewMode === 'card' ? 'active' : ''}`} 
                        size="sm" 
                        onClick={() => {
                          setViewMode('card');
                          setAmisKey(Date.now());
                        }}
                      >
                        卡片模式
                      </Button>
                      <Button 
                        className={`mode-btn ${viewMode === 'table' ? 'active' : ''}`} 
                        size="sm" 
                        onClick={() => {
                          setViewMode('table');
                          setAmisKey(Date.now());
                        }}
                      >
                        表格模式
                      </Button>
                    </div>
                  </div>
                </div>   
                {viewMode === 'card' && filteredPermissionList.map((item: any, index: number) => (
                  <div key={item.id} className="permission-item">
                    <div className="permission-item-header">
                      <div className="permission-item-title-box">
                        <div className="permission-item-title">{item.name}</div>
                        <div className="permission-item-remark">{item.remark || ''}</div>
                      </div>
                      <div className="permission-item-actions">
                        <Button size="sm" className="action-btn" onClick={() => handleEditPermission(item)}>编辑</Button>
                        <Button size="sm" className="action-btn" onClick={() => handleCopyPermission(item)}>复制</Button>
                        
                        {index !== 0 && (
                          <Button size="sm" className="action-btn" onClick={() => handleDeletePermission(item.id)}>删除</Button>
                        )}
                      </div>
                    </div>
                    <div className="permission-item-content">
                      <div className="permission-row">
                        <div className="permission-label">权限成员:</div>
                        <div className="permission-value">{getUserNamesByIds(item.userIds)}</div>
                      </div>
                      <div className="permission-row">
                        <div className="permission-label">操作权限:</div>
                        <div className="permission-value">{item.type == '1' ? '提交' : '查看'}</div>
                      </div>
                    </div>
                  </div>
                ))}
                {viewMode === 'table' && (
                  <AMISRenderer
                    key={amisKey}
                    schema={crudSchema}
                    embedMode={true}
                  />
                )}
              </div>
            </div>
          </Tab>
        </Tabs>
      </div>
      
      {/* 表单提交权限/新增权限组 */}
      <AMISRenderer
        schema={editPermissionDialog(props.pageData?.id || '',  currentPermission, activeTab === 'submit' ? '1' : '2')}
        show={addInterest}
        onClose={() => setAddInterest(false)}
        onConfirm={handlePermissionAdded}
      />

    </div>
  );
};

export default PagePermission;
