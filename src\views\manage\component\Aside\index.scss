.navAside {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  border-right: 1px solid var(--borderColor);
  background-color: var(--colors-neutral-fill-11);
  transition: background-color 0.3s;
  
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--colors-neutral-fill-6);
    border-radius: 3px;
  }

  &-menu {
    position: relative;
    width: 100%;
    padding: 10px 10px;

    &-item {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      border-radius: 6px;
      cursor: pointer;
      color: var(--colors-neutral-text-3);
      transition: all 0.3s;

      &:hover {
        background-color: var(--colors-neutral-fill-9);
        color: var(--colors-brand-5);
        
        .navAside-menu-item-name {
          color: var(--colors-brand-5);
        }
      }

      &.itemClick {
        background-color: var(--colors-neutral-fill-12);
        color: var(--colors-brand-5);
        
        .navAside-menu-item-name {
          color: var(--colors-brand-5);
          font-weight: 500;
        }
      }

      svg {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        text-align: center;
        transition: color 0.3s;
      }

      &-icon{
        margin-left: 10px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        text-align: center;
        transition: color 0.3s;
      }

      &-name {
        flex: 1;
        margin-left: 8px;
        font-size: 14px;
        transition: color 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-arrow {
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        i {
          font-size: 12px;
          transition: transform 0.3s;
          
          &.open {
            transform: rotate(90deg);
          }
        }
      }
    }
  }



  &-submenu {
    overflow: hidden;
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding-left: 10px;
  }
}

// 暗黑模式
[data-theme="dark"] .navAside {
  background-color: var(--colors-neutral-fill-10);
  
  &::-webkit-scrollbar-thumb {
    background: var(--colors-neutral-fill-4);
  }
  
  &-menu-item, &-submenu-item {
    color: var(--colors-neutral-text-2);
    
    &:hover {
      background-color: var(--colors-neutral-fill-9);
      color: var(--colors-brand-5);
      
      .navAside-menu-item-name {
        color: var(--colors-brand-5);
      }
    }
    
    &.itemClick {
      background-color: var(--colors-brand-9);
      color: var(--colors-brand-5);
      
      .navAside-menu-item-name {
        color: var(--colors-brand-5);
      }
    }
  }
}

.arrowDown {
  transform: rotate(90deg);
}

.itemClick {
  background-color: var(--light-bg);
}
