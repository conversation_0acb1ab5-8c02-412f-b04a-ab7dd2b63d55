.boxCreateDataSet {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.boxCreateDataSetTitle {
  position: relative;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 20px;
  border-bottom: 1px solid var(--border-bottom-line-color);
  box-sizing: border-box;

  &-name {
    width: 200px;
    font-size: 1.25rem;
    color: var(--text-color);
    cursor: pointer;

    &:active {
      opacity: 0.6;
    }

    &:hover {
      opacity: 0.8;
    }

    &-not {
      color: var(--text-color);
      opacity: 0.6;
    }

    &-icon {
      margin-left: 12px;
      font-size: 16px;
    }
  }
  &-input {
    // width: 300px; // 可以根据需要调整输入框宽度

    .input-group {
      display: flex;
      align-items: center;
      gap: 8px;

      &-input{
        width: 200px;
      }

      &-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  &-right {
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  &-tabs {
    position: relative;
    width: 10.5rem;
    height: 2.375rem;
    display: flex;
    justify-content: space-between;
    background-color: var(--light-bg);
    border-radius: 0.25rem;
    z-index: 10;

    &-item {
      width: 4.75rem;
      height: 2.125rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.375rem;
      margin: 0.125rem;
      color: rgba($color: var(--text-color), $alpha: 0.88);
      font-size: 0.875rem;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }

    .tabsItemClick {
      color: var(--button-primary-default-font-color);
    }

    .tabsItemClick_block {
      position: absolute;
      top: 0;
      left: 0;
      width: 4.75rem;
      height: 2.125rem;
      border-radius: 0.375rem;
      margin: 0.125rem;
      background-color: var(--colors-info-5);
      color: var(--button-primary-default-font-color);
      z-index: -10;
      transition: left 0.2s;
    }
  }
}

.linksClassName {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.disnone{
  display: none !important;
}

.boxCreateDataSetContent {
  position: relative;
  width: 100%;
  display: flex;
  flex: 1;
  background-color: var(--light-bg);

  &-left {
    position: relative;
    width: 480px;
    background-color: var(--page-background);
    display: flex;

    &-changeMenu {
      width: 240px;
      box-sizing: border-box;
      border-right: 1px solid var(--border-bottom-line-color);
      padding: 12px;

      .changeMenuInfo {
        position: relative;
        width: 100%;

        &-line {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          font-size: 12px;

          &-name {
            margin-right: 12px;
            width: 48px;
          }

          &-val {
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
          }

          &-select {
            font-size: 12px;
            max-width: 100%;
            flex: 1;
          }
        }
      }

      .changeMenuField {
        position: relative;

        &-title {
          padding: 12px 0;
          margin: 12px 0;
          border-top: 1px solid var(--border-bottom-line-color);
          border-bottom: 1px solid var(--border-bottom-line-color);
          font-size: 12px;
        }

        &-content {
          position: relative;

          &-item {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            font-size: 12px;
            padding: 10px 0;

            &-checkbox {
              margin: 0;
            }
          }
        }
      }
    }

    &-showField {
      width: 240px;
      box-sizing: border-box;
      border-right: 1px solid var(--border-bottom-line-color);
      padding: 12px;

      .showFieldTitle {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 12px;

        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
      }
      
      .showFieldContent {
        position: relative;
        width: 100%;

        &-item {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          font-size: 12px;
          padding: 6px 12px;
          margin-bottom: 4px;
          background-color: var(--colors-neutral-text-8);
          border-radius: 4px;

          &:hover {
            background-color: var(--colors-neutral-text-6);
          }

          &-name {
            margin-right: 12px;
            // width: 48px;
          }

          &-val {
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
          }

          &-icon {
            width: 20px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;

            &-fa {
              font-size: 12px;
              color: var(--text-color);
              cursor: pointer;
            }

            &:active {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
  &-right {
    position: relative;
    flex: 1;
    padding: 12px;
    box-sizing: border-box;
    overflow: hidden;

    &-content {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      background-color: var(--page-background);
      padding: 12px 20px;

      .tablesTitle {
        width: 100%;
        height: 40px;
        margin-bottom: 12px;

        display: flex;
        align-items: center;
        justify-content: space-between;

        border-bottom: 1px solid var(--border-bottom-line-color);

        &-name {
          font-size: 12px;
          color: var(--text-color);
        }
      }

      .tablesContent {
        position: relative;
        width: 100%;
        // max-width: 1359px;

        &-table {
          width: 100%;
          // max-width: 1359px;
        }
      }
    }
  }
}
