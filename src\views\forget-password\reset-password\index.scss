.reset-password-page {
  width: 460px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: var(--boxShadow);

  .form-content {
    padding: 30px;
    width: 420px;
    border: 1px dashed var(--borderColor);
    margin: 20px;

    .page-title {
      font-size: 30px;
      color: var(--text-color);
      margin-bottom: 30px;
      font-weight: bold;
      text-align: center;
    }

    .form-item {
      margin-bottom: 20px;

      .password-input {
        position: relative;

        .form-control {
          width: 100%;
          height: 40px;
          padding: 10px;
          padding-right: 40px;  // 为图标留出空间
          border-width: 1px;
          border-style: solid;
          border-color: var(--colors-neutral-fill-none);
          border-radius: 4px;
          font-size: 14px;
          color: var(--text-color);
          background: var(--light-bg);
          &::placeholder {
            color: var(--text--muted-color);
          }

          &:focus {
            border-color: var(--primary);
            outline: none;
          }

          &:hover:not(:disabled) {
            border-color: var(--primary);
            color: var(--primary);
          }
        }

        i {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: var(--text--muted-color);
          cursor: pointer;
          font-size: 16px;

          &:hover {
            color: var(--primary);
          }
        }
      }
    }

    .confirm-btn {
      height: 40px;
      font-size: 14px;
      color: var(--button-primary-default-font-color);
      background: var(--primary);
      border: none;
      border-radius: 4px;
    }
  }
} 