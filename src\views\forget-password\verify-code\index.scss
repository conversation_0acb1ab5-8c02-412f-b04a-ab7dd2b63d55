.verify-code-page {
  width: 460px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: var(--boxShadow);

  .form-content {
    padding: 30px;
    width: 420px;
    border: 1px dashed var(--borderColor);
    margin: 20px;

    .page-title {
      font-size: 30px;
      color: var(--text-color);
      margin-bottom: 20px;
      font-weight: bold;
      text-align: center;
    }

    .verify-tip {
      text-align: center;
      color: var(--text--muted-color);
      font-size: 14px;
      margin-bottom: 30px;
    }

    .code-input {
      margin: 0 auto;
      margin-bottom: 20px;
      width: 360px;
      display: flex;
      justify-content: center;

      :global(.cxd-VerificationCode) {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }

      .cxd-Verification-code,
      .dark-Verification-code {
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 16px;

        input{
            width: 32px !important;
            height: 32px !important;
            padding: 0;
            border-width: 1px;
            border-style: solid;
            border-color: var(--colors-neutral-fill-none);
            border-radius: 4px;
            font-size: 12px;
            color: var(--text-color);
            text-align: center;
            background: var(--light-bg);
    
            &:focus {
              border-color: var(--primary);
              outline: none;
              box-shadow: none;
              background: var(--light-bg);
            }
        }
      }
    }

    .resend-code {
      text-align: center;
      color: var(--text--muted-color);
      font-size: 14px;
      margin-top: 30px;

      .countdown {
        color: var(--primary);
      }

      button {
        color: var(--primary);
        padding: 0;
      }
    }
  }
} 