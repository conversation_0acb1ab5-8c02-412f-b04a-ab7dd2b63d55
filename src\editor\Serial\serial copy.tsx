import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import './serial.scss';

export class SerialPlugin extends BasePlugin {
  static id = 'SerialPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'serial';
  $schema = '/schemas/SerialSchema.json';

  // 生成默认的流水号规则
  generateDefaultSerialRule() {
    const now = new Date();
    const dateStr =
      now.getFullYear() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0');
    return `IT${dateStr}001`;
  }

  // 组件基本信息
  name = '流水号';
  panelTitle = '流水号';
  icon = 'fa fa-barcode';
  panelIcon = 'fa fa-barcode';
  pluginIcon = 'fa fa-barcode';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于生成和显示流水号的组件';
  docLink = '/amis/zh-CN/components/serial';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'serial',
    label: '流水号',
    name: 'serial',
    serialRule: this.generateDefaultSerialRule(),
    autoSettings: {}, // 自动编号设置（完全照搬轮播图的模式）
    serialFields: [
      {
        name: '文本字符',
        type: 'text',
        value: 'IT',
        removable: false
      },
      {
        name: '创建时间',
        type: 'date',
        value: 'YYYY',
        removable: true
      },
      {
        name: '自动编号',
        type: 'auto',
        startValue: 1,
        increment: 2,
        resetType: 'never',
        removable: true
      }
    ]
  };

  // 预览界面
  previewSchema = {
    type: 'serial',
    label: '流水号',
    autoSettings: {}, // 自动编号设置（完全照搬轮播图的模式）
    serialFields: [
      {
        name: '文本字符',
        type: 'text',
        value: 'IT',
        removable: false
      },
      {
        name: '创建时间',
        type: 'date',
        value: 'YYYY',
        removable: true
      },
      {
        name: '自动编号',
        type: 'auto',
        startValue: 1,
        increment: 2,
        resetType: 'never',
        removable: true
      }
    ]
  };

  // 安全更新schema（参考轮播组件的实现）
  safeChangeValue(context: any, newSchema: any) {
    try {
      if (
        context &&
        context.node &&
        !context.node.disposedNow &&
        this.manager &&
        this.manager.store &&
        typeof this.manager.store.changeValueById === 'function'
      ) {
        console.log('安全更新schema:', context.node.id, newSchema);
        this.manager.store.changeValueById(context.node.id, newSchema);
        return true;
      }
    } catch (error) {
      console.warn('安全更新schema失败:', error);
    }
    return false;
  }

  // 处理自动编号设置（完全照搬轮播图的模式）
  handleAutoSettings = (context: any, fieldIndex: number, settings: any) => {
    try {
      console.log('=== 保存自动编号设置 ===');
      console.log('字段索引:', fieldIndex);
      console.log('设置数据:', settings);

      // 获取当前schema
      const currentSchema = context.schema || {};
      console.log('当前schema:', currentSchema);

      // 更新autoSettings
      const newAutoSettings = {
        ...currentSchema.autoSettings,
        [fieldIndex]: {
          startValue: parseInt(settings.startValue) || 1,
          increment: parseInt(settings.increment) || 2,
          resetType: settings.resetType || 'never',
          timestamp: Date.now()
        }
      };

      // 创建新的schema
      const newSchema = {
        ...currentSchema,
        autoSettings: newAutoSettings
      };

      console.log('新的schema:', newSchema);

      // 使用safeChangeValue更新schema
      const result = this.safeChangeValue(context, newSchema);
      console.log('更新结果:', result);

      return result;
    } catch (error) {
      console.error('处理自动编号设置时出错:', error);
      return false;
    }
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('collapseGroup', [
      {
        title: '表单项',
        body: [getSchemaTpl('formItemMode'), getSchemaTpl('formItemSize')]
      },
      {
        title: '基本',
        body: [
          getSchemaTpl('formItemName', {
            required: true
          }),
          getSchemaTpl('label'),
          getSchemaTpl('labelRemark'),
          getSchemaTpl('remark'),
          getSchemaTpl('description')
        ]
      },
      {
        title: '编号规则',
        body: [
          {
            type: 'input-text',
            name: 'serialRule',
            label: '编号规则',
            className: 'serial-rule-preview',
            value: this.generateDefaultSerialRule(),
            description: '流水号生成规则预览',
            disabled: true
          },
          {
            type: 'combo',
            name: 'serialFields',
            label: '',
            multiple: true,
            addable: true,
            removable: true,
            draggable: true,
            addButtonText: '添加规则',
            className: 'serial-fields-combo',
            labelAlign: 'left',
            mode: 'normal',
            onChange: (value: any, _oldValue: any, _model: any, form: any) => {
              // 生成预览规则
              if (value && Array.isArray(value)) {
                let preview = '';
                value.forEach((item: any, index: number) => {
                  if (item.type === 'text') {
                    preview += item.value || '';
                  } else if (item.type === 'date') {
                    const now = new Date();
                    const format = item.value || 'YYYYMMDD';
                    if (format === 'YYYY') {
                      preview += now.getFullYear();
                    } else if (format === 'YYYYMM') {
                      preview +=
                        now.getFullYear() +
                        String(now.getMonth() + 1).padStart(2, '0');
                    } else if (format === 'YYYYMMDD') {
                      preview +=
                        now.getFullYear() +
                        String(now.getMonth() + 1).padStart(2, '0') +
                        String(now.getDate()).padStart(2, '0');
                    } else if (format === 'YY') {
                      preview += String(now.getFullYear()).slice(-2);
                    } else if (format === 'MM') {
                      preview += String(now.getMonth() + 1).padStart(2, '0');
                    } else if (format === 'DD') {
                      preview += String(now.getDate()).padStart(2, '0');
                    } else if (format === 'MMDD') {
                      preview +=
                        String(now.getMonth() + 1).padStart(2, '0') +
                        String(now.getDate()).padStart(2, '0');
                    }
                  } else if (item.type === 'auto') {
                    let startValue = item.startValue || 1;
                    let digits = item.increment || 2; // increment字段实际存储的是位数

                    // 从schema中读取保存的设置（完全照搬轮播图的模式）
                    try {
                      const autoSettings = form.data?.autoSettings || {};
                      const fieldSettings = autoSettings[index];
                      if (fieldSettings) {
                        startValue = fieldSettings.startValue || startValue;
                        digits = fieldSettings.increment || digits;
                      }
                    } catch (e) {
                      // 如果读取失败，使用默认值
                    }

                    // 预览显示第一个编号（就是起始值），并按固定位数格式化
                    const previewNumber = startValue;
                    preview += String(previewNumber).padStart(digits, '0');
                  }
                });
                form.setValueByName('serialRule', preview || '请添加编号规则');
              } else {
                form.setValueByName('serialRule', '请添加编号规则');
              }
            },
            items: [
              {
                type: 'group',
                className: 'serial-field-row',
                body: [
                  {
                    type: 'select',
                    name: 'type',
                    label: '',
                    required: true,
                    className: 'field-type-select',
                    options: [
                      {label: '文本字符', value: 'text'},
                      {label: '创建时间', value: 'date'},
                      {label: '自动编号', value: 'auto'}
                    ],
                    onChange: (
                      value: any,
                      _oldValue: any,
                      _model: any,
                      form: any
                    ) => {
                      // 根据类型设置默认值
                      if (value === 'text') {
                        form.setValueByName('name', '文本字符');
                        form.setValueByName('value', '');
                      } else if (value === 'date') {
                        form.setValueByName('name', '创建时间');
                        form.setValueByName('value', 'YYYY');
                      } else if (value === 'auto') {
                        form.setValueByName('name', '自动编号');
                        form.setValueByName('startValue', 1);
                        form.setValueByName('increment', 2);
                        form.setValueByName('resetType', 'never');
                      }
                    }
                  },
                  {
                    type: 'input-text',
                    name: 'value',
                    label: '',
                    placeholder: '字段值',
                    visibleOn: 'this.type === "text"',
                    className: 'field-value-input'
                  },
                  {
                    type: 'select',
                    name: 'value',
                    label: '',
                    visibleOn: 'this.type === "date"',
                    className: 'field-value-select',
                    options: [
                      {label: '年 (YYYY)', value: 'YYYY'},
                      {label: '年月', value: 'YYYYMM'},
                      {label: '年月日', value: 'YYYYMMDD'},
                      {label: '年 (YY)', value: 'YY'},
                      {label: '月', value: 'MM'},
                      {label: '日', value: 'DD'}
                    ]
                  },
                  {
                    type: 'button',
                    label: '设置',
                    visibleOn: 'this.type === "auto"',
                    className: 'field-setting-button',
                    block: true,
                    // 通过data传递当前项的索引
                    data: {
                      fieldIndex: '${index}'
                    },
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'custom',
                            script: `
                              // 从事件数据中获取当前项的索引
                              const fieldIndex = event.data?.fieldIndex !== undefined ? event.data.fieldIndex : 0;

                              console.log('=== 打开自动编号设置对话框 ===');
                              console.log('字段索引:', fieldIndex);
                              console.log('事件数据:', event.data);

                              // 获取已保存的设置（需要通过其他方式获取schema）
                              const fieldSettings = {
                                startValue: 1,
                                increment: 2,
                                resetType: 'never'
                              };

                              // 打开弹窗并传入数据
                              doAction({
                                actionType: 'dialog',
                                dialog: {
                                  title: '编号设置',
                                  size: 'md',
                                  data: {
                                    ...fieldSettings,
                                    _fieldIndex: fieldIndex
                                  },
                                  body: {
                                    type: 'form',
                                    body: [
                                      {
                                        type: 'hidden',
                                        name: '_fieldIndex'
                                      },
                                      {
                                        type: 'input-number',
                                        name: 'startValue',
                                        label: '起始值',
                                        min: 1,
                                        required: true
                                      },
                                      {
                                        type: 'input-number',
                                        name: 'increment',
                                        label: '固定位数',
                                        min: 1,
                                        max: 10,
                                        value: 2,
                                        required: true,
                                        description:
                                          '自动编号的总位数，不足位数前面补0'
                                      },
                                      {
                                        type: 'select',
                                        name: 'resetType',
                                        label: '重复',
                                        options: [
                                          {label: '请选择', value: 'never'},
                                          {label: '每天重复', value: 'daily'},
                                          {label: '每周重复', value: 'weekly'},
                                          {label: '每月重复', value: 'monthly'},
                                          {
                                            label: '每季重复',
                                            value: 'quarterly'
                                          },
                                          {label: '每年重复', value: 'yearly'}
                                        ]
                                      }
                                    ]
                                  },
                                  actions: [
                                    {
                                      type: 'button',
                                      label: '取消',
                                      actionType: 'cancel'
                                    },
                                    {
                                      type: 'button',
                                      label: '确认',
                                      level: 'primary',
                                      actionType: 'confirm',
                                      onEvent: {
                                        click: {
                                          actions: [
                                            {
                                              actionType: 'custom',
                                              script: \`
                                                try {
                                                  const formData = event.data;
                                                  const { startValue, increment, resetType, _fieldIndex } = formData;

                                                  console.log('保存自动编号设置:', { startValue, increment, resetType, fieldIndex: _fieldIndex });

                                                  // 显示成功提示（暂时先显示成功，实际保存逻辑后续完善）
                                                  doAction({
                                                    actionType: 'toast',
                                                    args: {
                                                      msgType: 'success',
                                                      msg: '自动编号设置保存成功!'
                                                    }
                                                  });

                                                } catch (e) {
                                                  console.error('保存失败:', e);
                                                  doAction({
                                                    actionType: 'toast',
                                                    args: {
                                                      msgType: 'error',
                                                      msg: '保存失败: ' + e.message
                                                    }
                                                  });
                                                }
                                              \`
                                            }
                                          ]
                                        }
                                      }
                                    }
                                  ]
                                }
                              });
                            `
                          }
                        ]
                      }
                    }
                  },
                  {
                    type: 'hidden',
                    name: 'name'
                  }
                ]
              }
            ],
            scaffold: {
              type: 'text',
              name: '文本字符',
              value: ''
            }
          }
        ]
      },
      {
        title: '状态',
        body: [...getSchemaTpl('status').body]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(SerialPlugin);
