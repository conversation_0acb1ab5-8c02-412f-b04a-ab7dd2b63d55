import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class RadioPlugin extends BasePlugin {
  static id = 'RadioPlugin';
  static scene = ['form'];

  // 关联渲染器名字
  rendererName = 'radio';
  $schema = '/schemas/RadioSchema.json';

  // 组件基本信息
  name = '单选框';
  panelTitle = '单选框';
  icon = 'fa fa-dot-circle-o';
  panelIcon = 'fa fa-dot-circle-o';
  pluginIcon = 'fa fa-dot-circle-o';
  isBaseComponent = true;
  panelJustify = true;

  // 组件描述信息
  description = 'Radio 单选框';
  docLink = '/amis/zh-CN/components/form/radios';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'radio',
    option: '选项说明',
    label: '单选框',
    name: 'radio'
  };

  // 预览界面
  previewSchema = {
    type: 'radio',
    option: '选项说明',
    label: '单选框'
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                getSchemaTpl('formItemName', {
                  required: true
                }),
                getSchemaTpl('label'),
                {
                  type: 'input-text',
                  name: 'option',
                  label: '选项说明',
                  description: '单选框文字说明'
                },
                {
                  type: 'combo',
                  name: 'value',
                  label: '值',
                  multiLine: true,
                  description: '单选框值，默认为布尔值',
                  items: [
                    {
                      type: 'input-text',
                      name: 'trueValue',
                      label: '勾选值',
                      value: true
                    },
                    {
                      type: 'input-text',
                      name: 'falseValue',
                      label: '未勾选值',
                      value: false
                    }
                  ]
                },
                {
                  type: 'select',
                  name: 'optionType',
                  label: '样式',
                  options: [
                    {label: '默认', value: 'default'},
                    {label: '按钮', value: 'button'}
                  ],
                  description: '单选框样式'
                },
                getSchemaTpl('labelRemark'),
                getSchemaTpl('remark'),
                getSchemaTpl('description'),
                getSchemaTpl('placeholder'),
                getSchemaTpl('autoFillApi')
              ]
            },
            {
              title: '校验',
              body: [
                getSchemaTpl('required'),
                getSchemaTpl('validations')
              ]
            },
            {
              title: '状态',
              body: [
                getSchemaTpl('disabled'),
                getSchemaTpl('visible'),
                getSchemaTpl('static')
              ]
            },
            {
              title: 'API配置',
              body: [
                getSchemaTpl('apiControl', {
                  name: 'source',
                  label: '数据源',
                  description: '通过接口获取选项内容'
                })
              ]
            }
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                getSchemaTpl('className'),
                getSchemaTpl('labelClassName')
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(RadioPlugin);
