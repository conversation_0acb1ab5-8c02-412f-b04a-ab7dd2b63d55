.navAside {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
  -webkit-scrollbar {
    display: none;
  }
  /* 隐藏Firefox浏览器的滚动条 */
  scrollbar-width: none;
}

.navAside-menu {
  position: relative;
  width: 100%;

  &-item {
    width: calc(100% - 1rem);
    height: 2.5rem;
    display: flex;
    align-items: center;
    margin: 0.5rem 0.5rem 0 0.5rem;
    border-radius: 0.5rem;
    cursor: default;

    &:hover {
      background-color: var(--light-bg);
    }

    &-icon {
      width: 2rem;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;

      &-img {
        width: 1rem;
        height: 1rem;
        display: block;
      }
    }

    &-name {
      flex: 1;
      margin-left: 0.5rem;
      font-size: 0.875rem;
      color: var(--text-color);
    }

    &-arrow {
      width: 1.5rem;
      height: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;

      &-img {
        width: 0.625rem;
        height: 0.625rem;
        display: block;
        transform: rotate(0deg);
        transition: transform 0.15s;
      }
    }
  }

  &-subitem {
    width: calc(100% - 1rem);
    height: 2.5rem;
    display: flex;
    align-items: center;
    margin: 0 0.5rem 0 0.5rem;
    border-radius: 0.5rem;
    padding-left: 3.5rem;
    padding-right: 0.5rem;
    cursor: default;

    &:hover {
      background-color: var(--light-bg);
    }

    &-name {
      flex: 1;
      padding: 0.25rem;
    }
  }
}

.arrowDown {
  transform: rotate(90deg);
}

.itemClick {
  background-color: var(--light-bg);
}
