.apply_basicSetting {
  width: 100%;

  &-title {
    width: 100%;
    min-height: 5rem;
    padding: 0.75rem 1.25rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-name {
      font-size: 1rem;
      color: var(--text-color);
      font-weight: bold;
    }
    &-desc {
      color: var(--text--muted-color);
      font-size: 0.875rem;
    }
  }

  &-item {
    width: 100%;
    min-height: 6.875rem;
    padding: 1.25rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--borderColor);

    &-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &-name {
        font-size: 0.875rem;
        color: var(--text-color);
        padding-bottom: 0.375rem;
      }

      

      &-content {
        font-size: 0.875rem;
        color: var(--text--muted-color);
        padding-top: 0.375rem;

        &-input {
          width: 20rem;
          height: 2rem;
          background-color: var(--light-bg);
          font-size: 0.875rem;
          color: var(--text-color);
          border: none;
          outline: none;
          padding: 0;
          margin: 0;
          padding: 0 1rem;


          ::placeholder {
            color: var(--text--muted-color);
            font-size: 0.875rem;
          }
        }
      }

      &-icon {
        padding-top: 0.375rem;
        width: 2.625rem;
        height: 3rem;
        overflow: hidden;
        border-radius: 0.5rem;

        &-img {
          width: 100%;
          height: 100%;
          display: block;
        }

        &-html {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          span {
            border-radius: 8px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }
        }

        &-font {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f7fa;
          border-radius: 8px;

          i {
            font-size: 20px;
            color: #666;
          }
        }
      }
    }

    &-right {
      width: 6.875rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-end;

      &-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 6rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        border: 1px solid var(--borderColor);
        background-color: var(--light-bg);
        color: var(--text-color);
        font-size: 1rem;

        &:hover {
          opacity: 0.8;
          background-color: var(--light-bg);
        }
      }
    }
  }
}
