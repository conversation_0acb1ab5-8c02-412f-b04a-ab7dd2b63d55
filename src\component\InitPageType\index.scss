.initPageTypeContent {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &-title {
    font-size: 1.5rem;
    color: var(--text-color)
  }

  &-tips {
    margin: 1.25rem auto;
    font-size: 1rem;
    color: var(--text--muted-color);

    &-link {
      color: #319bfb;
      cursor: pointer;

      &:hover {
        color: #2684f5;
      }
    }
  }

  &-menuList {
    position: relative;
    width: 70.375rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, 12.875rem);
    grid-gap: 1.5rem 1.5rem;

    &-item {
        width: 12.875rem;
        height: 10.9375rem;
        background-color: var(--colors-neutral-text-11);
        box-shadow: 0 0 0.125rem var(--borderColor);
        border-radius: 0.5rem;

        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        cursor: pointer;

        &:hover{
            box-shadow: 0 0 0.125rem var(--borderColor);
        }

        &:active{
            box-shadow: 0 0 0.125rem var(--borderColor);
            opacity: .8;
        }

        &-icon{
            width: 2.5rem;
            height: 2.5rem;

            &-img{
                display: block;
                width: 100%;
                height: 100%;
            }
        }

        &-name{
            color: var(--text-color);
            font-size: 1.125rem;
            margin-top: 1.625rem;
            margin-bottom: 1.5rem;
        }

        &-tips{
            color: var(--text--muted-color);
            font-size: 1rem;
        }
    }
  }
}

// Excel导入类型选择样式
.excel-type-option {
  &:hover {
    border-color: #319bfb !important;
    background-color: #f8fbff !important;
  }

  &.selected {
    border-color: #319bfb !important;
    background-color: #f0f8ff !important;
  }
}
