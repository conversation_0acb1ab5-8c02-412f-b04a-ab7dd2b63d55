import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class SelectMemberControlPlugin extends BasePlugin {
  static id = 'SelectMemberControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'user-multi-select';
  $schema = '/schemas/SelectMemberControlSchema.json';

  // 组件基本信息
  name = '人员多选';
  panelTitle = '人员多选';
  icon = 'fa fa-user';
  panelIcon = 'fa fa-picture-o';
  pluginIcon = 'icon-select-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '选择人员';
  docLink = '/amis/zh-CN/components/form/tree-select';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'user-multi-select',
    label: '人员多选',
    name: 'users',
    clearable: true,
    placeholder: '请选择人员',
    multiple: true,
    labelField: 'name',
    valueField: 'id',
    joinValues: true,
    extractValue: true,
    enableNodePath: true,
    onlyLeaf: true,
    cascade: true,
    delimiter: ',',
    // 人员数据接口
    // source: {
    //   method: 'GET',
    //   url: '/admin-api/system/user/list-dept-user'
    // }

    source: {
      data: JSON.parse(localStorage.getItem('userTreeList') || '[]')
    }
  };

    // 添加事件定义
    events = [
      {
        eventName: 'click',
        eventLabel: '菜单项点击',
        description: '点击导航项时触发'
        // ...
      },
      {
        eventName: 'select',
        eventLabel: '菜单项选中',
        description: '选中导航项时触发'
        // ...
      },
      {
        eventName: 'expand',
        eventLabel: '菜单展开',
        description: '菜单展开时触发'
        // ...
      },
      {
        eventName: 'collapse',
        eventLabel: '菜单折叠',
        description: '菜单折叠时触发'
        // ...
      },
      {
        eventName: 'loaded',
        eventLabel: '数据加载完成',
        description: '数据加载完成时触发'
        // ...
      }
    ];

    
  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                // getSchemaTpl('layout:originPosition', {
                //   value: 'left-top'
                // }),
                // getSchemaTpl('formItemName', {
                //   required: true
                // }),
                // getSchemaTpl('label'),
                getSchemaTpl('required'),  // 添加必填项配置
                getSchemaTpl('clearable'),
                getSchemaTpl('placeholder'),
                getSchemaTpl('labelRemark'),
                getSchemaTpl('remark'),
                getSchemaTpl('description')
              ]
            },
            {
              title: '选项设置',
              body: [
                getSchemaTpl('title', {
                  name: 'labelField',
                  label: '标签字段',
                  value: 'name'
                }),
                getSchemaTpl('title', {
                  name: 'valueField',
                  label: '值字段',
                  value: 'id'
                })
              ]
            },
            {
              title: '接口设置',
              body: [
                getSchemaTpl('apiControl', {
                  name: 'searchApi',
                  label: '搜索接口',
                  renderLabel: true,
                  showPrefix: false
                }),
                getSchemaTpl('apiControl', {
                  name: 'deferApi',
                  label: '加载接口',
                  renderLabel: true,
                  showPrefix: false
                })
              ]
            },
            {
              title: '高级',
              body: [
                getSchemaTpl('switch', {
                  name: 'showRadio',
                  label: '显示复选框',
                  visibleOn: '!this.multiple',
                }),
                getSchemaTpl('switch', {
                  name: 'cascade',
                  label: '级联选择模式',
                  value: false,
                }),
                getSchemaTpl('switch', {
                  name: 'withChildren',
                  label: '选项中包含下级',
                  value: false,
                }),
                getSchemaTpl('switch', {
                  name: 'onlyChildren',
                  label: '选项中仅包含下级',
                  value: false,
                  visibleOn: 'this.withChildren',
                }),
                getSchemaTpl('switch', {
                  name: 'enableNodePath',
                  label: '选项中包含父节点',
                  value: true,
                }),
                getSchemaTpl('switch', {
                  name: 'onlyLeaf',
                  label: '只能选择叶子节点',
                  value: false,
                }),
              ]
            },
            getSchemaTpl('status', {
              isFormItem: true,
              readonly: true
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            getSchemaTpl('theme:formItem'),
            getSchemaTpl('theme:form-label'),
            getSchemaTpl('theme:form-description'),
            {
              title: '选择框样式',
              body: [
                ...inputStateTpl(
                  'themeCss.inputControlClassName',
                  '--tree-select-base'
                )
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(SelectMemberControlPlugin);
