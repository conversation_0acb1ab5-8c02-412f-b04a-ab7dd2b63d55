import React from 'react';
import {Renderer} from 'amis';
import {FormControlProps} from 'amis-core';
export interface InputAddressProps extends FormControlProps {
  label?: string;
  name: string;
  clearable?: boolean;
  placeholder?: string;
  value?: string;
  static?: boolean;
  detailAddress?: string; // 添加详细地址字段
  detailAddressPlaceholder?: string; // 详细地址占位符
  delimiter?: string; // 分隔符
}

@Renderer({
  type: 'input-address',
  name: 'input-address'
})
export class InputAddressRenderer extends React.PureComponent<InputAddressProps> {
  state = {
    cityValue: '',
    detailValue: '',
    combinedValue: ''
  };
  // 添加初始化标志
  initialized: boolean = false;
  constructor(props: InputAddressProps) {
    super(props);
    // 初始化时解析已有的值
    this.initValues();
  }

  componentDidUpdate(prevProps: InputAddressProps) {
    // 当props中的value变化时，重新解析值
    if (prevProps.value !== this.props.value) {
      this.initValues();
    }
  }

  // 初始化解析值
  initValues() {
    const {value, data, id} = this.props as any;

    // 尝试从data中获取分离的字段值
    if (data && id) {
      const cityValue = data[`${id}_city`] || '';
      const detailValue = data[`${id}_detail`] || '';

      if (cityValue || detailValue) {
        const combinedValue = this.combineValues(cityValue, detailValue);
        this.setState({
          cityValue,
          detailValue,
          combinedValue
        });
        return;
      }
    }

    // 如果没有分离的字段值，尝试解析合并的值
    if (value) {
      try {
        const delimiter = this.props.delimiter || '&';
        if (typeof value === 'string' && value.includes(delimiter)) {
          const parts = value.split(delimiter);
          const cityValue = parts[0] || '';
          const detailValue = parts[1] || '';
          this.setState({
            cityValue,
            detailValue,
            combinedValue: value
          });
          return;
        }

        // 如果没有分隔符，假设整个值是城市
        this.setState({
          cityValue: value,
          detailValue: '',
          combinedValue: value
        });
      } catch (e) {
        console.error('解析地址值失败', e);
      }
    }
  }

  // 合并城市和详细地址值
  combineValues(cityValue: string, detailValue: string) {
    const delimiter = this.props.delimiter || '&';

    // 如果有城市值
    if (cityValue) {
      // 如果有详细地址，使用分隔符合并
      if (detailValue) {
        return `${cityValue}${delimiter}${detailValue}`;
      } else {
        return cityValue;
      }
    } else if (detailValue) {
      // 只有详细地址
      return `${delimiter}${detailValue}`;
    }

    return '';
  }

  // 处理城市值变化
  handleCityChange = (value: any) => {
    // const combinedValue = this.combineValues(value, this.state.detailValue);
    // 从 props.data 中获取详细地址，而不是从 state 中获取
    const {data, id, delimiter = '&'} = this.props as any;
    console.log('城市值变化', data);
    let detailValue =
      data && id ? data[`${id}_detail`] || '' : this.state.detailValue;
    if (!detailValue) {
      detailValue = data[`undefined_detail`] || '';
    }
    const combinedValue = this.combineValues(value, detailValue);
    this.setState(
      {
        cityValue: value,
        detailValue: detailValue, // 同时更新 state 中的详细地址
        combinedValue
      },
      () => {
        const {onChange, name, id} = this.props as any;
        if (onChange) {
          const diff: any = {};
          diff[name] = combinedValue;
          // 不要清除这些字段，而是更新它们
          diff[`${id}_city`] = value;
          diff[`${id}_detail`] = detailValue;
          onChange(combinedValue, name, diff);
        }
      }
    );
  };

  // 处理详细地址变化
  handleDetailChange = (e: any) => {
    const value = e && e.target ? e.target.value : e;

    const {data, id, delimiter = '&'} = this.props as any;

    let cityValue =
      data && id ? data[`${id}_city`] || '' : this.state.cityValue;
    if (!cityValue) {
      cityValue = data[`undefined_city`] || '';
    }
    const combinedValue = this.combineValues(cityValue, value);

    this.setState(
      {
        cityValue,
        detailValue: value,
        combinedValue
      },
      () => {
        const {onChange, name, id} = this.props as any;
        if (onChange) {
          const diff: any = {};
          diff[name] = combinedValue;
          diff[`${id}_city`] = cityValue;
          diff[`${id}_detail`] = value;
          onChange(combinedValue, name, diff);
        }
      }
    );
  };

  render() {
    const {
      static: isStatic,
      id,
      name,
      label,
      placeholder,
      render,
      detailAddressPlaceholder,
      data,
      allowCity,
      allowDistrict,
      searchable,
      required,
      delimiter = '&'
    } = this.props as any;

    let {cityValue, detailValue, combinedValue} = this.state;
    if (data[name]) {
      cityValue = data[name].split(delimiter)[0] || '';
      detailValue = data[name].split(delimiter)[1] || '';
    }
    // 静态模式下的处理逻辑，与之前的代码保持一致
    if (isStatic) {
      if (render) {
        // 静态模式下，创建一个组合的静态展示
        let value = this.props.value || '';
        let cityDisplay = '';
        let detailDisplay = '';

        // 如果value为空，尝试从data中获取对应name的值
        if (!value && data && name && data[name]) {
          value = data[name];
        }

        // 解析值
        if (value) {
          if (value.includes(delimiter)) {
            const parts = value.split(delimiter);
            cityDisplay = parts[0];
            detailDisplay = parts[1] || '';
          } else {
            cityDisplay = value;
          }
        } else if (data && id) {
          cityDisplay = data[`${id}_city`] || data[`u:${id}_city`] || '';
          detailDisplay = data[`${id}_detail`] || data[`u:${id}_detail`] || '';
        }

        // 有详细地址，使用组合显示
        if (detailDisplay) {
          return render('flex', {
            type: 'flex',
            label,
            items: [
              {
                type: 'input-city',
                static: true,
                value: cityDisplay,
                inputClassName: 'no-border',
                className: 'no-padding',
                style: {
                  padding: '0',
                  margin: '0'
                }
              },
              {
                type: 'static',
                value: detailDisplay,
                style: {
                  padding: '0',
                  margin: '0'
                }
              }
            ],
            className: 'address-display',
            style: {
              padding: '0',
              margin: '0',
              marginLeft: '10px',
              display: 'flex',
              alignItems: 'left',
              justifyContent: 'flex-start'
              // flexDirection: 'column',
            }
          });
        } else if (cityDisplay) {
          // 只有城市信息
          return render('input-city', {
            type: 'input-city',
            static: true,
            value: cityDisplay,
            style: {
              padding: '0',
              marginLeft: '10px',
              marginBottom: '0',
              display: 'flex',
              alignItems: 'left',
              justifyContent: 'flex-start',
              flexDirection: 'column'
            }
          });
        } else {
          // 没有任何地址信息
          return render('static', {
            type: 'static',
            value: '--',
            style: {
              padding: '0',
              marginLeft: '10px',
              display: 'flex',
              alignItems: 'left',
              justifyContent: 'flex-start',
              flexDirection: 'column'
            }
          });
        }
      }
    } else {
      // 编辑模式
      // 使用自定义渲染器直接处理事件
      const cityConfig = {
        type: 'input-city',
        label,
        allowCity,
        allowDistrict,
        searchable,
        required,
        id: `${id}_city`,
        name: `${id}_city`,
        placeholder: placeholder || '请选择省市区',
        value: cityValue,
        onChange: this.handleCityChange, // 添加这一行
        style: {
          marginBottom: '10px'
        }
      };

      const detailAddressConfig = {
        type: 'input-text',
        id: `${id}_detail`,
        name: `${id}_detail`,
        placeholder: detailAddressPlaceholder || '请输入详细地址',
        value: detailValue,
        onChange: this.handleDetailChange, // 添加这一行
        clearable: true,
        validations: {
          matchRegexp: `^[^${delimiter}]*$` // 不允许包含分隔符
        },
        validationErrors: {
          matchRegexp: `详细地址不能包含 "${delimiter}" 字符`
        },
        label: '　　', // 保持原有的空白标签
        style: {
          marginBottom: '0'
        }
      };

      // 隐藏字段，用于保存合并后的值
      const hiddenField = {
        type: 'hidden',
        name: name, // 使用组件的name属性
        value: combinedValue
      };

      // 组合省市区选择和详细地址输入框 - 不再使用custom类型
      const combinedConfig = {
        type: 'container',
        body: [cityConfig, detailAddressConfig, hiddenField],
        style: {
          marginBottom: '20px'
        }
      };

      const allConfig = {
        ...this.props,
        ...combinedConfig
      };

      if (render) {
        return render('container', allConfig);
      }
      //兜底
      return <></>;
    }
  }
}
