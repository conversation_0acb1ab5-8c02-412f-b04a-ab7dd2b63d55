// src/component/ProcessRender/index.tsx
import React, {
  useImperativeHandle,
  forwardRef,
  useState,
  useRef,
  useEffect
} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, Icon} from 'amis'; // 使用 amis 组件
import ProcessNodeRender from '../ProcessNodeRender';
import {forEachNode} from '@/utils/ProcessUtil';
import OrgPicker from '../OrgPicker';
import {
  getGroupModels,
  getDepartmentTree,
  getRoleTree,
  getUserList
} from '@/utils/api/api';
import {debounce} from 'lodash';
import './index.scss';

// 缺失的API函数
// const getLeaderByDepts = async (deptIds: (string | null)[]) => {
//   console.log('调用getLeaderByDepts', deptIds);
//   return Promise.resolve({data: []});
// };

// const getUsersByRoles = async (roleIds: string[]) => {
//   console.log('调用getUsersByRoles', roleIds);
//   return Promise.resolve({data: []});
// };

const getTrueConditions = async (params: any) => {
  // console.log('调用getTrueConditions', params);
  return Promise.resolve({data: []});
};

interface ProcessRenderProps {
  forms?: any[];
  pcMode?: boolean;
  processDefId?: string;
  process?: any;
  formData?: any;
  value?: any;
  deptId?: string;
  userInfo?: any;
  onChange?: (value: any) => void;
  onRenderOk?: () => void;
}

const ProcessRender = React.forwardRef<any, ProcessRenderProps>(
  (props, ref) => {
    const {
      forms = [],
      pcMode = false,
      processDefId,
      process = {},
      formData = {},
      value = {},
      deptId = null,
      userInfo = (() => {
        const storeStr = localStorage.getItem('store');
        if (!storeStr) return {};
        try {
          const store = JSON.parse(storeStr);
          return store?.userInfo || {};
        } catch {
          return {};
        }
      })(),
      onChange,
      onRenderOk
    } = props;
    // 标记部门树和用户列表是否加载完成
    const [cacheReady, setCacheReady] = useState(false);
console.log('formData11111111111111111111', formData);
    // 处理 formData
    useImperativeHandle(ref, () => ({
      validate
    }));

    // 组件初始化时全局请求一次
    useEffect(() => {
      Promise.all([
        getDepartmentTree({}).then((res: any) => {
          if (res.code === 0) {
            deptTreeCache.current = res.data || [];
          }
        }),
        getUserList().then((res: any) => {
          if (res.code === 0) {
            userListCache.current = res.data || [];
          }
        }),
        getRoleTree({}).then((res: any) => {
          if (res.code === 0) {
            roleListCache.current = res.data.list || [];
          }
        })
      ]).then(() => setCacheReady(true));
    }, []);

    const deptTreeCache = useRef<any[]>([]);
    const userListCache = useRef<any[]>([]);
    const roleListCache = useRef<any[]>([]);
    // 状态管理
    const [selectUserNodes, setSelectUserNodes] = useState<Set<string>>(
      new Set()
    );
    const [loading, setLoading] = useState(false);
    const [selectedNode, setSelectedNode] = useState<any>({});
    const [userCatch, setUserCatch] = useState<any>({});
    const userCatchRef = useRef<any>({}); // 新增
    const [oldFormData, setOldFormData] = useState(formData);
    const [models, setModels] = useState<any>(null);
    const [processTasks, setProcessTasks] = useState<any[]>([]);
    const [conditionFormItem, setConditionFormItem] = useState<Set<string>>(
      new Set()
    );
    const [branchNodeMap, setBranchNodeMap] = useState<Map<string, any>>(
      new Map()
    );
    const [loadingReqs, setLoadingReqs] = useState<Promise<any>[]>([]);
    const [calls, setCalls] = useState<Function[]>([]);

    // 为了处理task.active的状态
    const [taskActive, setTaskActive] = useState<Record<string, string>>({});

    // Refs
    const orgPickerRef = useRef<any>(null);
    const selectedUser = useRef<any>([]);
    const nodeRefs = useRef<{[key: string]: any}>({});

    // 新增状态
    const [data, setData] = useState<any>({});
    const [showPicker, setShowPicker] = useState(false);
    const [currentNode, setCurrentNode] = useState<any>(null);

    // 添加用户
    const addUser = (node: any) => {
      console.log('添加用户:', selectedNode);
      setSelectedNode(node);
      setShowPicker(true);
    };

    // 处理用户选择
    const handleSelected = (users: any[]) => {
      selectedUser.current = users;
      console.log('选择的用户:', users);
      if (!selectedNode || !selectedNode.id) {
        return;
      }

      const newValue = {...value};
      newValue[selectedNode.id] = users;

      const updatedTask = processTasks.find(
        task => task.id === selectedNode.id
      );
      if (updatedTask) {
        updatedTask.users = users.map(u => ({...u, enableEdit: true}));
      }

      setShowPicker(false);
      onChange?.(newValue);
    };

    // 删除用户
    const delUser = (nodeId: string, index: number) => {
      console.log('删除用户:', nodeId, index);
      setProcessTasks(prevTasks => {
        const newTasks = prevTasks.map(t => {
          if (t.id === nodeId) {
            const updatedUsers = [...t.users];
            updatedUsers.splice(index, 1);
            return {...t, users: updatedUsers};
          }
          return t;
        });
        return newTasks;
      });

      const newValue = {...value};
      newValue[nodeId] = []; // 或者 newValue[nodeId] = task.users (如果你想保持同步)
      onChange?.(newValue);
    };

    // 获取子流程模型
    const getSubModel = (call: Function) => {
      if (models) {
        call();
      } else {
        setCalls(prev => [...prev, call]);
        if (calls.length === 0) {
          getGroupModels({}, true).then((rsp: any) => {
            const newModels: any = {};
            rsp.data.forEach((group: any) => {
              group.items.forEach((v: any) => {
                newModels[v.procCode] = v.procName;
              });
            });
            setModels(newModels);

            // 执行所有等待的调用
            calls.forEach(callFun => callFun());
            setCalls([]);
          });
        }
      }
    };

    // 解析部门节点
    const resolveDeptNode = (
      depts: any[],
      deptProp: any,
      result: any,
      isApproval: boolean
    ) => {
      // let deptIds = (depts || []).map(d => d.id);
      switch (deptProp?.type) {
        case 'ALL':
          result.desc = `部门内所有人${isApproval ? '审批' : '办理'}`;
          result.users = depts;
          break;
        case 'ROLE':
          result.desc = `部门内角色[${(deptProp.roles || []).map(
            (r: any) => r.name
          )}]${isApproval ? '审批' : '办理'}`;
          // const roleReq = getDeptUsersByRoles(
          //     {
          //         roles: (deptProp.roles || []).map((r: any) => r.id),
          //         depts: deptIds
          //     }
          //   )
          //   .then((res: any) => {
          //     result.users = res.data;
          //   });
          // setLoadingReqs(prev => [...prev, roleReq]);
          break;
        default:
          // 直接用缓存数据查找
          // const leaderUserIds: number[] = [];
          // deptIds.forEach((deptId: number | string) => {
          //   const leaderUserId = findDeptLeaderByLevel(deptId, 1, true);
          //   if (leaderUserId) {
          //     leaderUserIds.push(leaderUserId);
          //   }
          // });

          // // 处理用户格式，补充 name 字段等
          // const users = (userListCache.current || [])
          //   .filter((u: any) => leaderUserIds.includes(u.id))
          //   .map((u: any) => ({
          //     ...u,
          //     name: u.nickname || u.name || '', // 兼容后续流程
          //     enableEdit: false
          //   }));
          //根据 100/101/103 从本地缓存中获取
          const deptTreeList = JSON.parse(
            localStorage.getItem('deptTreeList') || '{}'
          );

          const userTreeList = JSON.parse(
            localStorage.getItem('userTreeList') || '{}'
          );

          const ids = Array.isArray(depts) ? depts : String(depts).split('/'); // 处理数组或字符串类型
          const lastId = ids[ids.length - 1]; // 获取最后一个元素
          const leaderUserId = findLeaderUserId(deptTreeList,lastId);
          const users = findUserById(userTreeList, leaderUserId);
          result.users = [users];
          result.desc = `部门负责人${isApproval ? '审批' : '办理'}`;
          break;
      }
    };

    function findLeaderUserId(list: any[], targetId: number | string):any{
      for (const item of list) {
        if (item.id == targetId) {
          return item.leaderUserId;
        }
        if (item.children) {
          const result = findLeaderUserId(item.children, targetId);
          if (result !== undefined) {
            return result;
          }
        }
      }
      return undefined; // 找不到返回 undefined
    }
  
    
    // 递归查找用户对象
    function findUserById(departments: any[], userId: number | string): any {
      for (const dept of departments) {
        // 检查当前部门的children数组中是否有匹配的用户
        if (dept.children && Array.isArray(dept.children)) {
          for (const user of dept.children) {
            if (Number(user.id) === Number(userId)) {
              return {
                name: user.name,
                avatar: user.avatar || '',
                type: 'user',
                enableEdit: false
              };
            }
          }
        }

        // 如果当前部门有子部门，递归查找
        if (
          dept.children &&
          Array.isArray(dept.children) &&
          dept.children.some((item: any) => item.children)
        ) {
          const childDepts = dept.children.filter((item: any) => item.children);
          const found = findUserById(childDepts, userId);
          if (found) {
            return found;
          }
        }
      }

      return {
        name: '未知',
        avatar: '',
        type: 'user',
        enableEdit: false
      }; // 如果找不到该用户
    }

    // 递归查找部门负责人
    function findDeptLeader(deptId: number | string): number | null {
      let current = (deptTreeCache.current || []).find(
        (d: any) => Number(d.id) === Number(deptId)
      );
      while (current) {
        if (current.leaderUserId) {
          return current.leaderUserId;
        }
        if (!current.parentId || current.parentId === 0) {
          break;
        }
        current = (deptTreeCache.current || []).find(
          (d: any) => Number(d.id) === Number(current.parentId)
        );
      }
      return null;
    }

    // 查找全部上级主管（支持TOP和endLevel两种模式）
    function findAllDeptLeaders(
      deptId: number | string,
      endCondition: 'TOP' | number,
      skipEmpty: boolean
    ): number[] {
      let current = (deptTreeCache.current || []).find(
        (d: any) => Number(d.id) === Number(deptId)
      );
      let foundLevel = 0;
      const leaders: number[] = [];
      while (current) {
        if (current.leaderUserId) {
          foundLevel++;
          leaders.push(current.leaderUserId);
        } else if (!skipEmpty) {
          // 空主管也算一级
          foundLevel++;
          leaders.push(0); // 用 0 代替 null 作为占位符
        }
        if (
          (endCondition !== 'TOP' && foundLevel >= Number(endCondition)) ||
          !current.parentId ||
          current.parentId === 0
        ) {
          break;
        }
        current = (deptTreeCache.current || []).find(
          (d: any) => Number(d.id) === Number(current.parentId)
        );
      }
      // 过滤掉无主管的情况
      return leaders.filter(id => !!id);
    }

    // 递归查找多级主管
    function findDeptLeaderByLevel(
      deptId: number | string,
      level: number,
      skipEmpty: boolean
    ): number | null {
      let current = (deptTreeCache.current || []).find(
        (d: any) => Number(d.id) === Number(deptId)
      );
      let foundLevel = 0;
      while (current) {
        if (current.leaderUserId) {
          foundLevel++;
          if (foundLevel === level) {
            return current.leaderUserId;
          }
        } else if (!skipEmpty) {
          // 空主管也算一级
          foundLevel++;
          if (foundLevel === level) {
            return null;
          }
        }
        if (!current.parentId || current.parentId === 0) {
          break;
        }
        current = (deptTreeCache.current || []).find(
          (d: any) => Number(d.id) === Number(current.parentId)
        );
      }
      return null;
    }

    // 获取子流程节点
    const getSubProcNode = (node: any) => {
      // 添加类型定义
      let user: {id?: string | number; name?: string} = {};
      switch (node.props.staterUser.type) {
        case 'ROOT':
          user = userInfo;
          break;
        case 'FORM':
          const fd = formData[node.props.staterUser.value];
          user = Array.isArray(fd) && fd.length > 0 ? fd[0] : {name: '请选人'};
          break;
        case 'SELECT':
          user = node.props.staterUser.value || {};
          break;
      }

      const procNode = {
        id: node.id,
        title: `${node.name} [由${user.id ? user.name : '?'}发起]`,
        name: '子流程',
        desc: '',
        icon: 'fa fa-sitemap',
        enableEdit: false,
        users: [user]
      };

      getSubModel(() => {
        procNode.desc = `调用子流程 [${models[node.props.subProcCode]}]`;
      });

      return procNode;
    };

    // 获取审批节点
    const getApprovalNode = (node: any, isApproval = true) => {
      console.log('获取审批节点', node);
      let result = {
        id: node.id,
        title: node.name,
        name: isApproval ? '审批人' : '办理人',
        icon: isApproval ? 'fa fa-check-circle' : 'fa fa-user-circle',
        enableEdit: false,
        multiple: false,
        mode: node.props.mode,
        users: [] as any[], // 明确指定类型为 any[]
        desc: ''
      };

      // 只查找和set缓存一次
      if (userCatchRef.current[node.id]?.length > 0) {
        result.users = userCatchRef.current[node.id];
        return result;
      }

      switch (node.props.assignedType) {
        case 'ASSIGN_USER':
          result.users = [...(node.props.assignedUser || [])];
          result.desc = isApproval ? '指定审批人' : '指定办理人';
          break;
        case 'ASSIGN_LEADER':
          resolveDeptNode(
            node.props.assignedDept,
            node.props.deptProp,
            result,
            isApproval
          );
          break;
        case 'SELF':
          // 如果没有发起人，使用当前登录用户
          result.users =
            process.props.assignedUser && process.props.assignedUser.length > 0
              ? process.props.assignedUser
              : [
                  {
                    id: userInfo.id,
                    name:
                      userInfo.nickname ||
                      userInfo.realname ||
                      userInfo.username,
                    avatar: userInfo.avatar,
                    nickname: userInfo.nickname,
                    type: 'user'
                  }
                ];
          result.desc = `发起人自己${isApproval ? '审批' : '办理'}`;
          break;
        case 'SELF_SELECT':
          result.enableEdit = true;
          setSelectUserNodes(prev => {
            const newSet = new Set(prev);
            newSet.add(node.id);
            return newSet;
          });
          result.multiple = node.props.selfSelect.multiple || false;
          result.desc = isApproval ? '自选审批人' : '自选办理人';
          break;
        case 'LEADER_TOP':
          result.desc = `连续多级主管${isApproval ? '审批' : '办理'}`;
          const leaderTop = node.props.leaderTop;
          const leaderUserIdsMore = findAllDeptLeaders(
            deptId
              ? deptId
              : JSON.parse(localStorage.getItem('store') || '{}')?.userInfo
                  ?.dept?.id,
            leaderTop.endCondition,
            leaderTop.skipEmpty
          );
          const usersMore = (userListCache.current || [])
            .filter((u: any) => leaderUserIdsMore.includes(u.id))
            .map((u: any) => ({
              ...u,
              name: u.nickname || u.name || '',
              enableEdit: false
            }));
          result.users = usersMore;
          // const leaderTop = node.props.leaderTop;
          // const leaderTopReq = getUserLeader({
          //     endCondition: 'TOP' === leaderTop.endCondition ? 0 : leaderTop.endLevel,
          //     deptId,
          //     skipEmpty: leaderTop.skipEmpty
          //   })
          //   .then((res: any) => {
          //     result.users = res.data;
          //   });
          // setLoadingReqs(prev => [...prev, leaderTopReq]);

          break;
        case 'LEADER':
          // 兼容多级主管查找
          const leaderUserIds: number[] = [];
          result.desc =
            node.props.leader.level === 1
              ? `直接主管${isApproval ? '审批' : '办理'}`
              : `第${node.props.leader.level}级主管${
                  isApproval ? '审批' : '办理'
                }`;

          const leaderUserId = findDeptLeaderByLevel(
            deptId
              ? deptId
              : JSON.parse(localStorage.getItem('store') || '{}')?.userInfo
                  ?.dept?.id,
            node.props.leader.level,
            node.props.leader.skipEmpty
          );
          if (leaderUserId) {
            leaderUserIds.push(leaderUserId);
          }

          // 处理用户格式，补充 name 字段等
          const users = (userListCache.current || [])
            .filter((u: any) => leaderUserIds.includes(u.id))
            .map((u: any) => ({
              ...u,
              name: u.nickname || u.name || '',
              enableEdit: false
            }));

          result.users = users;
          break;
        case 'ROLE':
          result.desc = `由角色[${(node.props.role || []).map(
            (r: any) => r.name
          )}]${isApproval ? '审批' : '办理'}`;
          if (!userCatchRef.current[node.id]) {
            // 直接用 node.props.role 作为 users
            const users = (node.props.role || []).map((role: any) => ({
              id: role.id,
              name: role.name,
              nickname: role.name,
              avatar: role.avatar || '/default-role-avatar.png',
              type: 'role',
              desc: role.remark || '',
              enableEdit: false
            }));
            setProcessTasks(prev =>
              prev.map(task => (task.id === node.id ? {...task, users} : task))
            );
            userCatchRef.current[node.id] = users;
          }
          break;
        case 'OTHER_SELECT':
          result.desc = `由其他节点指定${isApproval ? '审批人' : '办理人'}`;
          break;
        case 'FORM_USER':
          result.desc = `由表单字段内人员${isApproval ? '审批' : '办理'}`;
          const userTreeList = JSON.parse(
            localStorage.getItem('userTreeList') || '{}'
          );
          console.log('formUser', formData[node.props.formUser]);
          console.log('formUser2222222222', node.props.formUser);
          const ids = Array.isArray(formData[node.props.formUser]) ? formData[node.props.formUser] : String(formData[node.props.formUser]).split('/'); // 处理数组或字符串类型
          const lastId = ids[ids.length - 1]; // 获取最后一个元素
          const users2 = findUserById(userTreeList, lastId);
          result.users = [users2];

          // node.props.formUser = {
          //   ...users2,
          //   enableEdit: false
          // };
          // setConditionFormItem(prev => {
          //   const newSet = new Set(prev);
          //   newSet.add(node.props.formUser);
          //   return newSet;
          // });

   


          // result.desc = `部门负责人${isApproval ? '审批' : '办理'}`;
          // result.users = formData[node.props.formUser] || [];
          break;
        case 'FORM_DEPT':
          // setConditionFormItem(prev => {
          //   const newSet = new Set(prev);
          //   newSet.add(node.props.formDept);
          //   return newSet;
          // });
          resolveDeptNode(
            formData[node.props.formDept],
            node.props.deptProp,
            result,
            isApproval
          );
          break;
        case 'REFUSE':
          result.desc = `流程此处将被自动驳回`;
          break;
      }

      // if (userCatch[node.id]?.length > 0) {
      //   result.users = userCatch[node.id];
      // }

      // 只查找和set缓存一次
      if (userCatchRef.current[node.id]?.length > 0) {
        result.users = userCatchRef.current[node.id];
        return result;
      }
      // 只在首次查找后set缓存
      userCatchRef.current[node.id] = result.users;

      return result;
    };

    // 获取抄送节点
    const getCcNode = (node: any) => {
      let result = {
        id: node.id,
        title: node.name,
        icon: 'fa fa-paper-plane',
        name: '抄送人',
        enableEdit: node.props.shouldAdd,
        type: 'org',
        multiple: true,
        desc: node.props.shouldAdd ? '可添加抄送人' : '流程将会抄送到他们',
        users: [...(node.props.assignedUser || [])]
      };

      // if (userCatch[node.id]?.length > 0) {
      //   result.users = userCatch[node.id];
      // }
      if (userCatchRef.current[node.id]?.length > 0) {
        result.users = userCatchRef.current[node.id];
        return result;
      }

      // setUserCatch((prev: any) => ({
      //   ...prev,
      //   [node.id]: result.users
      // }));

      userCatchRef.current[node.id] = result.users; // 只用ref缓存

      return result;
    };

    // 获取包容分支节点
    const getInclusiveNode = (node: any, pbnode: any, pbid: string) => {
      let branchTasks = {
        id: node.id,
        title: node.name,
        name: '包容分支',
        icon: 'fa fa-code-fork',
        enableEdit: false,
        active: node.branchs[0].id,
        options: [],
        desc: '满足条件的分支均会执行',
        branchs: {}
      };

      // 设置初始 active 状态
      setTaskActive(prev => ({
        ...prev,
        [node.id]: node.branchs[0].id
      }));

      const req = getTrueConditions({
        processDfId: processDefId,
        conditionNodeId: node.id,
        multiple: true,
        context: {...formData, deptId}
      })
        .then((rsp: any) => {
          const cds = new Set(rsp.data || []);
          let activeId = node.branchs[0].id;

          const options = node.branchs.map((branch: any) => {
            const isSkip = !cds.has(branch.id);
            if (!isSkip) {
              activeId = branch.id;
            }
            return {
              id: branch.id,
              title: branch.name,
              skip: isSkip
            };
          });

          // 更新 active 状态
          setTaskActive(prev => ({
            ...prev,
            [node.id]: activeId
          }));

          // 为每个分支创建空数组
          const branchs: {[key: string]: any[]} = {};
          node.branchs.forEach((branch: any) => {
            branchs[branch.id] = [];
            // 设置分支映射关系
            setBranchNodeMap(prev => {
              const newMap = new Map(prev);
              newMap.set(branch.id, {node: pbnode, id: pbid});
              return newMap;
            });

            // 加载子流程
            loadProcess(
              branch.children,
              branchs[branch.id],
              branchTasks,
              branch.id
            );
          });

          // 更新选项和分支
          setProcessTasks(prev => {
            return prev.map(task => {
              if (task.id === node.id) {
                return {
                  ...task,
                  options,
                  active: activeId,
                  branchs
                };
              }
              return task;
            });
          });
        })
        .catch((err: any) => {
          // 处理错误
          const errorDesc = `<span style="color:#CE5266;">条件解析异常，渲染失败😢</span>`;
          setProcessTasks(prev => {
            return prev.map(task => {
              if (task.id === node.id) {
                return {
                  ...task,
                  desc: errorDesc
                };
              }
              return task;
            });
          });
          console.error('解析条件失败:', err);
        });

      setLoadingReqs(prev => [...prev, req]);
      return branchTasks;
    };

    // 获取条件节点
    const getConditionNode = (node: any, pbnode: any, pbid: string) => {
      let branchTasks = {
        id: node.id,
        title: node.name,
        name: '条件分支',
        icon: 'fa fa-random',
        enableEdit: false,
        active: node.branchs[0].id,
        options: [],
        desc: '只执行第一个满足条件的分支',
        branchs: {}
      };

      // 设置初始 active 状态
      setTaskActive(prev => ({
        ...prev,
        [node.id]: node.branchs[0].id
      }));

      const req = getTrueConditions({
        processDfId: processDefId,
        conditionNodeId: node.id,
        multiple: false,
        context: {...formData, deptId}
      })
        .then((rsp: any) => {
          const cds = new Set(rsp.data || []);
          let activeId = node.branchs[0].id;

          const options = node.branchs.map((branch: any) => {
            const isSkip = !cds.has(branch.id);
            if (!isSkip) {
              activeId = branch.id;
            }
            return {
              id: branch.id,
              title: branch.name,
              skip: isSkip
            };
          });

          // 更新 active 状态
          setTaskActive(prev => ({
            ...prev,
            [node.id]: activeId
          }));

          // 为每个分支创建空数组
          const branchs: {[key: string]: any[]} = {};
          node.branchs.forEach((branch: any) => {
            branchs[branch.id] = [];
            // 设置分支映射关系
            setBranchNodeMap(prev => {
              const newMap = new Map(prev);
              newMap.set(branch.id, {node: pbnode, id: pbid});
              return newMap;
            });

            // 加载子流程
            loadProcess(
              branch.children,
              branchs[branch.id],
              branchTasks,
              branch.id
            );
          });

          // 更新选项和分支
          setProcessTasks(prev => {
            return prev.map(task => {
              if (task.id === node.id) {
                return {
                  ...task,
                  options,
                  active: activeId,
                  branchs
                };
              }
              return task;
            });
          });
        })
        .catch((err: any) => {
          // 处理错误
          const errorDesc = `<span style="color:#CE5266;">条件解析异常，渲染失败😢</span>`;
          setProcessTasks(prev => {
            return prev.map(task => {
              if (task.id === node.id) {
                return {
                  ...task,
                  desc: errorDesc
                };
              }
              return task;
            });
          });
          console.error('解析条件失败:', err);
        });

      setLoadingReqs(prev => [...prev, req]);
      return branchTasks;
    };

    // 获取并行分支节点
    const getConcurrentNode = (node: any, pbnode: any, pbid: string) => {
      let concurrentTasks = {
        id: node.id,
        title: node.name,
        name: '并行分支',
        icon: 'fa fa-bars',
        enableEdit: false,
        active: node.branchs[0].id,
        options: [],
        desc: '所有分支都将同时执行',
        branchs: {}
      };

      // 设置初始 active 状态
      setTaskActive(prev => ({
        ...prev,
        [node.id]: node.branchs[0].id
      }));

      const options = node.branchs.map((branch: any) => ({
        id: branch.id,
        title: branch.name,
        skip: false
      }));

      // 为每个分支创建空数组
      const branchs: {[key: string]: any[]} = {};
      node.branchs.forEach((branch: any) => {
        branchs[branch.id] = [];
        // 设置分支映射关系
        setBranchNodeMap(prev => {
          const newMap = new Map(prev);
          newMap.set(branch.id, {node: pbnode, id: pbid});
          return newMap;
        });

        // 加载子流程
        loadProcess(
          branch.children,
          branchs[branch.id],
          concurrentTasks,
          branch.id
        );
      });

      // 更新选项和分支
      concurrentTasks.options = options;
      concurrentTasks.branchs = branchs;

      return concurrentTasks;
    };

    // 加载流程
    const loadProcess = (
      processNode: any,
      processTasks: any[],
      bnode?: any,
      bid?: string
    ) => {
      forEachNode(processNode, (node: any) => {
        if (bnode) {
          setBranchNodeMap(prev => {
            const newMap = new Map(prev);
            newMap.set(node.id, {node: bnode, id: bid});
            return newMap;
          });
        }

        switch (node.type) {
          case 'ROOT': {
            // 判断发起人为空时，使用当前登录用户
            const starterUsers =
              process.props.assignedUser &&
              process.props.assignedUser.length > 0
                ? process.props.assignedUser
                : [
                    {
                      id: userInfo.id,
                      name:
                        userInfo.nickname ||
                        userInfo.realname ||
                        userInfo.username,
                      avatar: userInfo.avatar,
                      nickname: userInfo.nickname,
                      type: 'user'
                    }
                  ];
            processTasks.push({
              id: node.id,
              title: node.name,
              name: '发起人',
              desc: `${starterUsers
                .map((user: any) => user.name)
                .join('、')} 将发起本流程`,
              icon: 'fa fa-user',
              enableEdit: false,
              users: starterUsers
            });
            break;
          }
          case 'APPROVAL':
            processTasks.push(getApprovalNode(node));
            break;
          case 'TASK':
            processTasks.push(getApprovalNode(node, false));
            break;
          case 'SUBPROC':
            processTasks.push(getSubProcNode(node));
            break;
          case 'CC':
            processTasks.push(getCcNode(node));
            break;
          case 'CONDITIONS':
            processTasks.push(getConditionNode(node, bnode, bid || ''));
            loadProcess(node.children, processTasks);
            return true;
          case 'INCLUSIVES':
            processTasks.push(getInclusiveNode(node, bnode, bid || ''));
            loadProcess(node.children, processTasks);
            return true;
          case 'CONCURRENTS':
            processTasks.push(getConcurrentNode(node, bnode, bid || ''));
            loadProcess(node.children, processTasks);
            return true;
        }
        return false;
      });
    };

    // 加载流程渲染
    const loadProcessRender = async () => {
      setLoading(true);
      const newProcessTasks: any[] = [];
      setSelectUserNodes(new Set());
      setLoadingReqs([]);

      loadProcess(process, newProcessTasks);

      newProcessTasks.push({
        title: '结束',
        name: 'END',
        icon: 'fa fa-check',
        enableEdit: false
      });

      setProcessTasks(newProcessTasks);

      if (loadingReqs.length > 0) {
        Promise.all(loadingReqs)
          .then(() => {
            setLoading(false);
            onRenderOk?.();
          })
          .catch(() => setLoading(false));
      } else {
        onRenderOk?.();
        setLoading(false);
      }
    };

    // 表单值比较
    const formValDiff = (newVal: any, oldVal: any) => {
      if (typeof newVal === 'object') {
        return JSON.stringify(newVal) !== JSON.stringify(oldVal);
      }
      return newVal !== oldVal;
    };

    // 表单变化处理
    const formChangeHandler = (newVal: any, oldVal: any) => {
      let isChanged = false;
      for (const key of conditionFormItem) {
        if (formValDiff(newVal[key], oldVal[key])) {
          isChanged = true;
          break;
        }
      }

      if (isChanged) {
        console.log('执行流程重渲染');
        loadProcessRender();
      }

      setOldFormData(newVal);
    };

    // 校验流程步骤
    const validate = (call?: (isOk: boolean) => void) => {
      let isOk = true;
      for (let nodeId of selectUserNodes) {
        if ((props.value?.[nodeId] || []).length === 0) {
          isOk = false;
          let brNode = branchNodeMap.get(nodeId);
          while (brNode && brNode.id) {
            setTaskActive(prev => ({
              ...prev,
              [brNode.node.id]: brNode.id
            }));
            brNode = branchNodeMap.get(brNode.id);
          }
          if (nodeRefs.current[nodeId]) {
            nodeRefs.current[nodeId].errorShake();
          }
          break;
        }
      }
      call?.(isOk);
    };

    // 渲染节点列表
    const renderNode = (tasks: any[]) => {
      if (!tasks || tasks.length === 0) return null;

      return (
        <div className="process-timeline">
          {tasks.map((task, index) => {
            const isLast = index === tasks.length - 1;
            let iconClass = 'fa fa-user';

            // 根据节点类型设置图标类名
            if (task.type === 'STARTER') {
              iconClass = 'fa fa-user';
            } else if (task.type === 'APPROVAL') {
              iconClass = 'fa fa-check-circle';
            } else if (task.type === 'CC') {
              iconClass = 'fa fa-paper-plane';
            } else if (task.type === 'HANDLER') {
              iconClass = 'fa fa-cog';
            } else if (task.type === 'END') {
              iconClass = 'fa fa-flag';
            } else if (task.icon) {
              iconClass = task.icon;
            }

            // 状态图标样式类
            const statusClass = task.completed
              ? 'completed'
              : task.active
              ? 'active'
              : '';

            // 检查是否是分支节点
            const isBranchNode =
              task.name === '条件分支' ||
              task.name === '包容分支' ||
              task.name === '并行分支';

            // 检查是否是结束节点
            const isEndNode = task.name === 'END';

            return (
              <div
                key={task.id || index}
                className={`timeline-item ${
                  task.enableEdit ? 'task' : ''
                } ${statusClass} ${isEndNode ? 'timeline-item-end' : ''}`}
              >
                <div className="timeline-item-icon">
                  <div className="node-icon">
                    <i className={iconClass}></i>
                  </div>
                </div>
                <div className="timeline-item-content">
                  {isEndNode ? (
                    <div className="end-node-content">{task.title}</div>
                  ) : isBranchNode ? (
                    <>
                      <div className="branch-title">
                        {task.title || '分支节点'}
                      </div>
                      <div className="branch-desc">{task.desc}</div>

                      {/* 分支选项按钮 */}
                      {task.options && task.options.length > 0 && (
                        <div className="branch-options">
                          {task.options.map((option: any) => (
                            <Button
                              key={option.id}
                              className={`branch-option-btn ${
                                taskActive[task.id] === option.id
                                  ? 'active'
                                  : ''
                              } ${option.skip ? 'skip' : ''}`}
                              onClick={() => {
                                setTaskActive(prev => ({
                                  ...prev,
                                  [task.id]: option.id
                                }));
                              }}
                            >
                              {option.title}
                              {option.skip && (
                                <span className="skip-label">跳过</span>
                              )}
                            </Button>
                          ))}
                        </div>
                      )}

                      {/* 分支内容 */}
                      {task.branchs &&
                        Object.keys(task.branchs).length > 0 &&
                        taskActive[task.id] && (
                          <div className="branch-content">
                            {renderNode(
                              task.branchs[taskActive[task.id]] || []
                            )}
                          </div>
                        )}
                    </>
                  ) : (
                    <ProcessNodeRender
                      ref={el => (nodeRefs.current[task.id] = el)}
                      task={task}
                      desc={task.desc}
                      error={task.error}
                      onAddUser={() => addUser(task)}
                      onDelUser={delUser}
                    />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      );
    };

    // 副作用
    useEffect(() => {
      if (cacheReady) {
        setOldFormData(formData);
        loadProcessRender();
      }
    }, [cacheReady]);

    // useEffect(() => {
    //   const debouncedLoad = debounce(loadProcessRender, 800);
    //   debouncedLoad();
    //   return () => debouncedLoad.cancel();
    // }, [formData]);

    const prevConditionValues = useRef<any>({});

    useEffect(() => {
      // 只比较流程依赖的字段
      let changed = false;
      for (const key of conditionFormItem) {
        if (formData[key] !== prevConditionValues.current[key]) {
          changed = true;
          break;
        }
      }
      if (changed) {
        loadProcessRender();
        // 更新记录
        const newValues: any = {};
        for (const key of conditionFormItem) {
          newValues[key] = formData[key];
        }
        prevConditionValues.current = newValues;
      }
    }, [formData, conditionFormItem]);

    return (
      <div className="process-render">
        {loading ? (
          <div className="loading-container">
            <div className="loading-mask">
              <Spinner size="lg" />
            </div>
          </div>
        ) : (
          renderNode(processTasks)
        )}

        <OrgPicker
          ref={orgPickerRef}
          show={showPicker}
          multiple={selectedNode?.multiple}
          title={`选择${selectedNode?.title || ''}人员`}
          onCancel={() => setShowPicker(false)}
          onOk={handleSelected}
          selected={selectedNode?.users || []}
          pcMode={pcMode}
          type={selectedNode?.type}
        />
      </div>
    );
  }
);

export default ProcessRender;
