import type {CSSProperties, FC} from 'react';
import {useDrop} from 'react-dnd';

import dataset_fillin_icon from '@/image/common_icons/dataset_fillin_icon.png';
import dataset_delete_icon from '@/image/common_icons/dataset_delete_icon.png';

import React from 'react';
import {isObservableArray} from 'mobx';

const style: CSSProperties = {};

export const ChartConfiguration: FC<any> = (props: any) => {
  const [list, setList] = React.useState<any>([]);

  const [{canDrop, isOver}, drop] = useDrop(() => ({
    accept: 'fieldItem',
    drop: () => ({name: props.title}),
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    }),

    hover(item: any, monitor: any) {
      console.log('ChartConfiguration hover', props.title);
    }
  }));

  React.useEffect(() => {
    setList(props.dropResultList);
    console.log(props.title, props.dropResultList);
  }, [props.dropResultList]);

  const typeField: any = {
    timestamp: '23',
    int: '123',
    varchar: 'abc'
  };

  // 删除字段
  const handleDelete = (index:number) => {
    props.handleDelete(props.title,index)
  }

  return (
    <div className="chartConfiguration-xAxis">
      <div className="chartConfiguration-xAxis-title">
        <span>{props.title}</span>
        <span className="chartConfiguration-xAxis-title-fx">fx</span>
      </div>
      <div className="chartConfiguration-xAxis-box" ref={drop}>
        {list.length == 0 && (
          <div className="chartConfiguration-xAxis-box-tips">
            最多一个字段
          </div>
        )}
        {list.length > 0 &&
          list.map((fieldItem: any, index: number) => {
            return (
              <div className="chartConfiguration-xAxis-box-item" key={index}>
                {fieldItem.controlLabel != '' ? (
                  <div className="chartConfiguration-xAxis-box-item-type">
                    {fieldItem.controlLabel}
                  </div>
                ) : (
                  <div className="chartConfiguration-xAxis-box-item-type">
                    {typeField[fieldItem.type]}*
                  </div>
                )}
                <div className="chartConfiguration-xAxis-box-item-name">
                  {fieldItem.label}
                </div>
                {/* 编辑 */}
                <div className="chartConfiguration-xAxis-box-item-fillin">
                  <img
                    className="chartConfiguration-xAxis-box-item-fillin-icon"
                    src={dataset_fillin_icon}
                  />
                </div>
                {/* 删除字段 */}
                <div className="chartConfiguration-xAxis-box-item-delete" onClick={()=>handleDelete(index)}>
                  <img
                    className="chartConfiguration-xAxis-box-item-delete-icon"
                    src={dataset_delete_icon}
                  />
                </div>
              </div>
            );
          })}
        {list.length == 1 && (
          <div className="chartConfiguration-xAxis-box-tipsItem">
            最多一个字段
          </div>
        )}
      </div>
    </div>
  );
};
