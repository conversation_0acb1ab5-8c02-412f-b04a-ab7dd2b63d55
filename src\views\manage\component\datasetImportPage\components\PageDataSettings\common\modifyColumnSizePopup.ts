// 修改每列显示的字段数量
const modifyColumnSizePopup = (val:any) => {
  return {
    type: 'dialog',
    title: '每列显示的字段数量',
    body: [
      {
        id: 'u:457705a47f9f',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'input-number',
            label: '',
            name: 'columnSize',
            value: val,
            row: 0,
            id: 'u:24a573f872be',
            required: true,
            placeholder: '请输入每列显示的字段数量'
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'u:457705a47f9f'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: false
      }
    ],
    id: 'u:bfb96764b1ea',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:459e4efe729b'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'u:4255fde736dd'
      }
    ],
    showCloseButton: false,
    closeOnOutside: false,
    closeOnEsc: true,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    themeCss: {
      dialogClassName: {
        radius: {
          'top-left-border-radius': '8px',
          'top-right-border-radius': '8px',
          'bottom-left-border-radius': '8px',
          'bottom-right-border-radius': '8px'
        }
      }
    },
    editorSetting: {
      displayName: '每列显示的字段数量'
    },
    hideActions: false
  };
};

export {modifyColumnSizePopup};
