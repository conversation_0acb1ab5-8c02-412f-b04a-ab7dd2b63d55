import React, {FC, useContext, useEffect, useState} from 'react';
import {observer} from 'mobx-react';
import AMISRenderer from '@/component/AMISRenderer';
import {IMainStore} from '@/store';
import {MobXProviderContext} from 'mobx-react';
import './index.scss';

const ResourceManage: FC<any> = (props: any) => {
  function useStore(): IMainStore {
    const {store} = useContext(MobXProviderContext);
    return store as IMainStore;
  }

  const store = useStore();
  const [activeTab, setActiveTab] = useState(0);
  const {history, location} = props;

  // 监听URL参数变化
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const activeTabKey = searchParams.get('activeTabKey');
    const tabKeys = [
      'appTemplateAdmin',
      'pageTemplateAdmin',
      'formTemplateAdmin',
      'componentTemplateAdmin',
      'iconManageAdmin',
      'imageManageAdmin'
    ];

    if (activeTabKey && tabKeys.includes(activeTabKey)) {
      setActiveTab(tabKeys.indexOf(activeTabKey));
    } else {
      // 如果URL中没有参数，添加默认参数
      history.replace({
        pathname: location.pathname,
        search: `?activeTabKey=${tabKeys[0]}`
      });
    }
  }, [location, history]);

  // 处理Tab切换事件
  const handleTabChange = (tabIndex: number) => {
    setActiveTab(tabIndex);
    const tabKeys = [
      'appTemplateAdmin',
      'pageTemplateAdmin',
      'formTemplateAdmin',
      'componentTemplateAdmin',
      'iconManageAdmin',
      'imageManageAdmin'
    ];

    // 使用history对象更新URL
    history.push({
      pathname: location.pathname,
      search: `?activeTabKey=${tabKeys[tabIndex]}`
    });
  };

  // 创建通用的CRUD配置生成函数
  const createCrudConfig = (title: string, type: number) => {
    return {
      type: 'crud',
      api: {
        url: `/admin-api/system/resources-authorization/page?userId=${store.userInfo.id}&type=${type}`,
        method: 'get',
        adaptor: (payload: any) => {
          if (payload.code === 0 && payload.data) {
            // 获取创建者ID，如果API没有直接提供isCreator字段
            const creatorId = store.userInfo.companyId; // 假设当前用户ID就是创建者ID
            console.log(creatorId);
            return {
              total: payload.data.total,
              items: payload.data.list.map((item: any) => ({
                ...item,
                // 假设userId为1的是创建者，或者根据其他规则判断
                isCreator: item.userId == creatorId
              }))
            };
          }
          return {
            total: 0,
            items: []
          };
        }
      },
      headerToolbar: [
        {
          type: 'button',
          label: '添加成员',
          icon: 'fa fa-plus',
          level: 'primary',
          actionType: 'dialog',
          dialog: {
            title: '',
            size: 'md',
            body: {
              type: 'form',
              api: {
                url: '/admin-api/system/resources-authorization/batch-create',
                method: 'post',
                data: {
                  '&': '$$',
                  'type': type
                }
              },
              body: [
                {
                  label: '',
                  type: 'transfer',
                  name: 'userIds',
                  selectMode: 'tree',
                  resultListModeFollowSelect: false,
                  onlyChildren: true,
                  autoCheckChildren: true,
                  source: '/admin-api/system/user/list-dept-user',
                  labelField: 'name',
                  valueField: 'id',
                  searchable: true,
                  statistics: true,
                  required: true,
                  placeholder: '请选择需要授权的成员'
                }
              ]
            }
          }
        },
        {
          type: 'tpl',
          tpl: '成员（${total}）',
          className: 'auth-count-label'
        }
      ],
      columns: [
        {
          name: 'username',
          label: '姓名',
          type: 'tpl',
          tpl: '${username} ${isCreator ? "<span class=\\"org-creator\\">组织创建者</span>" : ""}'
        },
        {
          name: 'nickname',
          label: '昵称'
        },
        {
          name: 'deptName',
          label: '部门'
        },
        {
          name: 'status',
          label: '账号状态',
          type: 'mapping',
          map: {
            '1': '<span class="badge-status badge-success">已启用</span>',
            '2': '<span class="badge-status badge-danger">已停用</span>'
          }
        },
        {
          name: 'updater',
          label: '编辑人'
        },
        {
          name: 'updateTime',
          label: '编辑时间',
          type: 'input-datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          static: true
        },
        {
          name: 'creator',
          label: '创建人'
        },
        {
          name: 'createTime',
          label: '创建时间',
          type: 'input-datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          static: true
        },
        {
          type: 'operation',
          label: '操作',
          buttons: [
            {
              type: 'dropdown-button',
              icon: 'fa fa-ellipsis-h',
              hideCaret: true,
              visibleOn: '${!isCreator}', // 如果是组织创建者则不显示操作按钮
              buttons: [
                {
                  type: 'button',
                  label: "${status == 1 ? '停用账号' : '启用账号'}",
                  confirmTitle:
                    "${status == 1 ? '确定要停用吗？' : '确定要启用吗？'}",
                  confirmText:
                    "${status == 1 ? '停用后，该账号将不能访问' : '启用后，该账号可以访问'}" +
                    (type == 1
                      ? '应用模板'
                      : type == 2
                      ? '表单模板'
                      : type == 3
                      ? '组件模板'
                      : type == 4
                      ? '图标模板'
                      : type == 5
                      ? '图片模板'
                      : type == 6
                      ? '页面模板'
                      : '资源') +
                    "${status == 1 ? '，确定要停用吗？' : '，确定要启用吗？'}",
                  actionType: 'ajax',
                  api: {
                    method: 'PUT',
                    url: '/admin-api/system/resources-authorization/update',
                    data: {
                      id: '${id}',
                      userId: '${userId}',
                      type: '${type}',
                      status: "${status == 1 ? '2' : '1'}"
                    }
                  }
                },
                {
                  type: 'button',
                  label: '移除成员',
                  className: 'text-danger',
                  confirmTitle: '确定要移除吗？',
                  confirmText:
                    '移除后，该账号将不能访问' +
                    (type == 1
                      ? '应用模板'
                      : type == 2
                      ? '表单模板'
                      : type == 3
                      ? '组件模板'
                      : type == 4
                      ? '图标模板'
                      : type == 5
                      ? '图片模板'
                      : type == 6
                      ? '页面模板'
                      : '资源') +
                    '，确定要移除吗？',
                  actionType: 'ajax',
                  api: 'DELETE:/admin-api/system/resources-authorization/delete?id=${id}'
                }
              ]
            }
          ]
        }
      ],
      bulkActions: [],
      itemActions: [],
      footerToolbar: ['statistics', 'pagination', 'switch-per-page'],
      autoGenerateFilter: false,
      syncLocation: false,
      affixHeader: true,
      initFetch: true,
      className: 'resource-crud-table'
      // 数据处理适配器
    };
  };

  // 资源权限管理Schema
  const resourceCrudSchema = {
    type: 'page',
    body: [
      {
        type: 'tabs',
        tabsMode: 'line',
        className: 'tabs-wrapper',
        activeKey: activeTab,
        onSelect: handleTabChange,
        tabs: [
          {
            title: '应用模板管理员',
            body: [
              {
                type: 'html',
                html: '<div class="info-alert">应用模版管理员 具有管理应用模版所有的权限。</div>'
              },
              createCrudConfig('应用模板管理员', 1)
            ]
          },
          {
            title: '页面模板管理员',
            body: [
              {
                type: 'html',
                html: '<div class="info-alert">页面模版管理员 具有管理页面模版所有的权限。</div>'
              },
              createCrudConfig('页面模板管理员', 6)
            ]
          },
          {
            title: '表单模板管理员',
            body: [
              {
                type: 'html',
                html: '<div class="info-alert">表单模版管理员 具有管理表单模版所有的权限。</div>'
              },
              createCrudConfig('表单模板管理员', 2)
            ]
          },
          {
            title: '组件管理员',
            body: [
              {
                type: 'html',
                html: '<div class="info-alert">组件管理员 具有管理组件模版所有的权限。</div>'
              },
              createCrudConfig('组件管理员', 3)
            ]
          },
          {
            title: '图标管理员',
            body: [
              {
                type: 'html',
                html: '<div class="info-alert">图标管理员 具有管理图标所有的权限。</div>'
              },
              createCrudConfig('图标管理员', 4)
            ]
          },
          {
            title: '图片管理员',
            body: [
              {
                type: 'html',
                html: '<div class="info-alert">图片管理员 具有管理图片所有的权限。</div>'
              },
              createCrudConfig('图片管理员', 5)
            ]
          }
        ]
      }
    ]
  };

  return (
    <div className="resource-container">
      <div className="page-header">
        <div className="title">资源权限管理</div>
        <div className="subtitle">资源管理员及其权限维护</div>
      </div>

      <AMISRenderer
        schema={resourceCrudSchema}
        onAction={(type: string, data: any) => {
          console.log(type, data);
        }}
      />
    </div>
  );
};

export default observer(ResourceManage);
