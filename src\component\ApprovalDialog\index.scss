// 遮罩层
.approval-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1300;
  background: rgba(0, 0, 0, 0.5);
}

// 弹框容器
.approval-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1400;
  width: 500px;
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  max-height: 90vh;

  .approval-dialog-content {
    background: var(--background);
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    
    .dialog-header {
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: none;

      &-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-color);
      }

      .close-btn {
        cursor: pointer;
        color: var(--text--muted-color);
        font-size: 16px;
      }
    }

    .dialog-sec-title {
      padding: 8px 24px 0;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color);
    }

    .dialog-body {
      padding: 16px 0 24px;
      overflow-y: auto;
      flex: 1;
      max-height: 400px;
      
      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
    }

    .dialog-footer {
      padding: 10px 24px 16px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      border-top: 0.5px solid var(--borderColor);
    }
  }
}
