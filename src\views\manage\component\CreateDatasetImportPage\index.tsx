import React, {FC, useEffect, useState} from 'react';
import {render as renderAmis, toast} from 'amis';
import {createApplicationPageAndClass, createFormData, getDataSetPage, getDataSet, updateFormData, updateApplicationPageAndClass, getFormDataPage} from '@/utils/api/api';
import './index.scss';
import {createFromObj, createDatasetImportObj} from '@/utils/schemaPageTemplate/createPageObjs';
const CreateDatasetImportPage: FC<any> = (props: any) => {
    // 添加数据集列表状态
    const [dataSetOptions, setDataSetOptions] = useState([]);
    // 添加当前选中的数据集信息
    const [currentDataSet, setCurrentDataSet] = useState<any>(null);

    // 获取数据集列表
    useEffect(() => {
        getDataSetPage({
            applicantOrBackend: 2,  // 限制后台
            pageNo: 1,
            pageSize: 100
        }).then((res: any) => {
            if (res.code === 0 && res.data.list) {
                setDataSetOptions(res.data.list);
            } else {
                toast.error('获取数据集列表失败');
            }
        });
    }, []);

    // 获取数据集详情
    const handleDataSetChange = (value: any, oldValue: any, item: any, form: any) => {
        // origin 包含了选项的完整信息
        let dataSetItem:any = dataSetOptions.find((option: any) => option.id == value);
        if (dataSetItem) {
            setCurrentDataSet(dataSetItem);
            // 自动填充名称
            form.setValueByName('name', dataSetItem.name);
        } else {
            setCurrentDataSet(null);
        }
    };

    const handleGetFormDataPage = (pageId: any, pageName: any) => {
        let data = {
          applicationPageId: pageId,
          pageNo: 1,
          pageSize: 10
        };
        getFormDataPage(data).then((res: any) => {
          if (res.code == 0) {
            if (res.data.list.length > 0) {
              if (res.data.list[0]) {
                let pageData = res.data.list[0]
                let field = JSON.parse(pageData.field)
                let schema = createDatasetImportObj(pageName, pageId, field, pageData.dataSetId)
                updateFromSchema(schema, pageData.applicationPageId, pageData.id)
                // updateFromSchema(pageId, info, res.data.list[0])
              }else{

              }
            }else{
            }
          } else {
            toast.error(res.msg);
          }
        });
      };

      const updateFromSchema = (schema: any, applicationPageId: any, id: any) => {
        let data = {
          id: id,
          data: JSON.stringify(schema),
          applicationPageId: applicationPageId
        };
        updateFormData(data).then((res: any) => {
          if (res.code == 0) {
            // handleGetApplicationPageAndClass();
            // props.update({
            //   ...props.pageData,
            //   data: schema
            // });
            toast.success('保存成功');
          } else {
            toast.error(res.msg);
          }
        });
      }
    // 创建页面
    const handleCreatePage = (item: any, val?: any) => {
        let data: any = {
            url: val?.link ? val.link : '',
            applicantOrBackend: 2,
            pageType: 13,
            parentId: 0,
            name: item.name || '',
            type: 1,
            applicationId: props.applicationId,
            dataSetId: item.dataSetId || '',
        };

        // 如果有数据集信息，添加相关参数
        // if (currentDataSet) {
        //     data = {
        //         ...data,
        //         dataSetInfo: currentDataSet,
        //         // 可以添加其他需要的数据集参数
        //     };
        // }
        createApplicationPageAndClass(data).then((res: any) => {
            if (res.code == 0) {
                console.log(res.data.id)
                handleGetFormDataPage(res.data.id, item.name || '')
                props.onMenuRefresh();
                // toast.success('创建成功');
            } else {
                toast.error('创建失败 ', res.msg);
            }
        });
    };

    // 初始化表单数据-创建表单数据
    const initFormData = (pageId: any, info: any) => {
        let data = {
            applicationPageId: pageId,
            data: '',
            field: ''
        };
        createFormData(data).then((res: any) => {
            if (res.code == 0) {
                toast.success('初始化数据完成~');
            } else {
                toast.error('初始化数据失败 ' + res.msg);
            }
        });
    };

    const schema = {
        type: 'page',
        body: {
            type: 'form',
            onSubmit: (values: any) => {
                if (!values.dataSetId) {
                    toast.error('请选择数据集');
                    return;
                }

                if (!currentDataSet) {
                    toast.error('未获取到数据集信息');
                    return;
                }

                const submitData = {
                    ...values,
                    applicantOrBackend: 2,
                    pageType: 1,
                    parentId: 0,
                    url: '',
                    type: 1,
                    typeName: 'dataSetImport',
                    applicationId: props.applicationId,
                    dataSetId: values.dataSetId
                };

                handleCreatePage(submitData, {
                    link: values.url || '',
                    dataSetId: values.dataSetId
                });

                return {
                    status: 0,
                    msg: '提交成功'
                };
            },
            body: [
                {
                    type: 'select',
                    name: 'applicantOrBackend',
                    label: '应用类型',
                    value: 2,
                    options: [
                        {label: '应用内', value: 1},
                        {label: '后台', value: 2}
                    ],
                    disabled: true
                },
                {
                    type: 'input-number',
                    name: 'parentId',
                    label: '父级编号',
                    value: 0,
                    disabled: true
                },
                {
                    type: 'select',
                    name: 'type',
                    label: '类型',
                    value: 1,
                    options: [
                        {label: '页面', value: 1},
                        {label: '分类', value: 2}
                    ],
                    required: true,
                    disabled: true
                },
                {
                    type: 'input-text',
                    name: 'name',
                    label: '名称',
                    required: true,
                    placeholder: '请输入页面名称'
                },
                {
                    type: 'select',
                    name: 'pageType',
                    label: '页面类型',
                    value: 1,
                    options: [
                        {label: '类型1', value: 1},
                        {label: '类型2', value: 2}
                    ],
                    disabled: true
                },
                {
                    type: 'select',
                    name: 'dataSetId',
                    label: '选择数据集',
                    required: true,
                    searchable: true,
                    options: dataSetOptions.map((item:any) => ({
                        label: item.name,  // 使用数据集的名称作为显示标签
                        value: item.id     // 使用数据集的ID作为实际值
                    })),
                    placeholder: '请选择数据集',
                    onChange: handleDataSetChange
                },
                // 显示选中的数据集信息
                {
                    type: 'static',
                    name: 'dataSetInfo',
                    label: '数据集信息',
                    visibleOn: 'this.dataSetId',
                    value: currentDataSet ? `${currentDataSet.name} (${currentDataSet.belongingDataSource})` : ''
                },
                {
                    type: 'submit',
                    label: '提交',
                    level: 'primary'
                }
            ],
            messages: {
                saveSuccess: '创建成功',
                saveFailed: '创建失败'
            }
        }
    };

    return (
        <div className="create-dataset-import-page">
            {renderAmis(schema, {
                data: {
                    applicantOrBackend: 2,
                    pageType: 1,
                    parentId: 0,
                    type: 1,
                    applicationId: props.applicationId
                }
            })}
        </div>
    );
};

export default CreateDatasetImportPage;
