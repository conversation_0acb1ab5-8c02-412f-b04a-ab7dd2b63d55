import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class DataCardContainerPlugin extends BasePlugin {
  static id = 'DataCardContainerPlugin';
  static scene = ['layout'];

  // 缓存数据卡片选项
  private _dataCardOptions: any[] = [];

  // 关联渲染器名字
  rendererName = 'my-container';
  $schema = '/schemas/ContainerSchema.json';

  // 组件基本信息
  name = '数据卡片';
  panelTitle = '数据卡片';
  icon = 'fa fa-square-o';
  panelIcon = 'fa fa-square-o';
  pluginIcon = 'icon-container-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = false;
  description = '数据卡片容器组件，用于展示数据卡片';
  docLink = '/amis/zh-CN/components/container';
  tags = ['门户'];

  // 组件默认配置
  scaffold = {
    type: 'my-container',
    body: [],
    style: {
      minWidth: '300px',
      minHeight: '300px',
      backgroundColor: '#f5f5f5',
      borderRadius: '4px'
    }
  };

  // 添加事件定义
  events = [
    {
      eventName: 'click',
      eventLabel: '菜单项点击',
      description: '点击导航项时触发'
      // ...
    },
    {
      eventName: 'select',
      eventLabel: '菜单项选中',
      description: '选中导航项时触发'
      // ...
    },
    {
      eventName: 'expand',
      eventLabel: '菜单展开',
      description: '菜单展开时触发'
      // ...
    },
    {
      eventName: 'collapse',
      eventLabel: '菜单折叠',
      description: '菜单折叠时触发'
      // ...
    },
    {
      eventName: 'loaded',
      eventLabel: '数据加载完成',
      description: '数据加载完成时触发'
      // ...
    }
  ];

  // 预览界面
  previewSchema = {
    type: 'my-container',
    body: [],
    style: {
      backgroundColor: '#f5f5f5',
      borderRadius: '4px',
      padding: '10px'
    }
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    // 从URL中提取应用ID
    let applicationId = ''; // 默认值
    try {
      // 获取URL路径
      const pathname = window.location.hash;
      // 方法1：正则表达式匹配portletEditor后面的数字
      const matches = pathname.match(/\/portletEditor(\d+)/);
      if (matches && matches[1]) {
        applicationId = matches[1];
      }
    } catch (e) {
      console.error('提取应用ID时出错:', e);
    }

    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '数据卡片',
              body: [
                {
                  type: 'select',
                  name: 'dataCardId',
                  label: '数据卡片',
                  source: {
                    method: 'get',
                    url: `/admin-api/system/application-page-and-class/get-data-card-page?pageNo=1&pageSize=10&applicationId=${applicationId}`,
                    adaptor: (payload: any) => {
                      if (!payload.data || !payload.data.list) {
                        return {
                          data: {
                            options: []
                          },
                          status: 0,
                          msg: ''
                        };
                      }

                      const options = payload.data.list.map((item: any) => ({
                        label: item.name,
                        value: item.id,
                        pageData: item.pageData
                      }));

                      // 缓存options以便在onChange中使用
                      this._dataCardOptions = options;

                      return {
                        data: {
                          options: options
                        },
                        status: 0,
                        msg: ''
                      };
                    }
                  },
                  clearable: true,
                  onChange: (
                    value: any,
                    oldValue: any,
                    data: any,
                    form: any
                  ) => {
                    if (!value) {
                      return;
                    }

                    // // 从缓存获取选中的选项
                    let selectedOption = this._dataCardOptions.find(
                      item => item.value == value
                    );
                    console.log('selectedOption:', selectedOption);

                    if (selectedOption && selectedOption.pageData) {
                      try {
                        const pageData = JSON.parse(selectedOption.pageData);
                        let body = pageData.body || [];
                        if (body && body.length > 0) {
                          body[0].isEdit = false;
                        }
                        if (
                          context.node &&
                          typeof context.node.updateSchema === 'function'
                        ) {
                          context.node.updateSchema({
                            ...context.node.schema,
                            body: body
                          });
                        }
                      } catch (e) {
                        console.error('解析数据卡片内容失败', e);
                      }
                      return;
                    }
                  }
                }
              ]
            },
            {
              title: '数据播报',
              body: [
                getSchemaTpl('switch', {
                  name: 'enableDataBroadcast',
                  label: '数据播报',
                  value: false,
                })
              ]
            },
            {
              title: '样式',
              body: [
                {
                  type: 'select',
                  name: 'colorMode',
                  label: '颜色模式',
                  options: [
                    {
                      label: '默认',
                      value: 'default'
                    },
                    {
                      label: '跟随门户主题',
                      value: 'theme'
                    }
                  ],
                  value: 'theme'
                }
              ]
            },
            {
              title: '双端显示控制',
              body: [
                {
                  type: 'radios',
                  name: 'displayDevice',
                  label: '显示设备',
                  options: [
                    {
                      label: '双端',
                      value: 'both'
                    },
                    {
                      label: '仅PC',
                      value: 'pc'
                    }
                  ],
                  value: 'both'
                }
              ]
            }
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本样式',
              body: [
                getSchemaTpl('layout:originPosition', {
                  value: 'left-top'
                }),
                getSchemaTpl('theme:colorPicker', {
                  label: '背景色',
                  name: 'style.backgroundColor',
                  labelMode: 'input'
                }),
                getSchemaTpl('theme:shadow', {
                  name: 'themeCss.baseControlClassName.boxShadow:default'
                }),
                getSchemaTpl('theme:radius', {
                  name: 'themeCss.baseControlClassName.radius:default',
                  label: '圆角'
                }),
                getSchemaTpl('theme:paddingAndMargin', {
                  name: 'themeCss.baseControlClassName.padding-and-margin:default',
                  label: '内外边距'
                })
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(DataCardContainerPlugin);
