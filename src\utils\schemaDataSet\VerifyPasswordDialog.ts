export const VerifyPasswordDialog = (show: boolean) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>验证密码</b>'
    },
    showCloseButton: false,
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入当前密码'
      },
      body: [
        {
          maxLength: 32,  
          required: true,
          type: 'input-password',
          name: 'password',
          placeholder: '当前密码',
          inputClassName: 'password-input',
          labelClassName: 'password-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm'
      }
    ],
    show: show
  };
}; 