/**
 * @file entry of this example.
 */
import * as React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import '@fortawesome/fontawesome-free/css/all.css';
import '@fortawesome/fontawesome-free/css/v4-shims.css';
// 根据当前主题选择引入对应的 CSS
// const theme = localStorage.getItem('amis-theme') || 'cxd';
// if (theme === 'cxd') {
//   require('amis/lib/themes/cxd.css');
// } else if (theme === 'dark') {
//   require('amis/lib/themes/dark.css');
// }
import 'amis/lib/themes/cxd.css';
import 'amis/lib/themes/dark.css';
import 'amis/lib/helper.css';
import 'amis/sdk/iconfont.css';
import 'amis-editor-core/lib/style.css';
import './scss/style.scss';
import {setDefaultTheme, setSvgIconList, mountIconSpriteToDom} from 'amis';
import {setThemeConfig} from 'amis-editor-core';
import themeConfig from 'amis-theme-editor-helper/lib/systemTheme/cxd';
import {registerFilter} from 'amis';
import * as echarts from 'echarts';
// 确保全局只有一个 echarts 实例
window.echarts = echarts;
import './themes/chartTheme.js';

registerFilter('radarParse', (input: any, fieldConfig: any) => {
  // 获取列表数据
  const list = input;
  // 如果没有数据或配置，则返回空数组
  if (!list.length || !fieldConfig) {
    return [];
  }
  
  // 返回雷达图所需格式的数据
  return fieldConfig.map((item:any) => {
    const fieldName = item.name;

    const values = list.map((item: any) => item[fieldName]);

    return {
      name: item.label,
      value: values
    }
  })
});

registerFilter('conditionParse', (input: any) => {
  const list = input;
  return list.map((item:any) => {
    return {
      label: item.label,
      name: item.name,
      type: 'text'
    }
  })
});

registerFilter('bodyParse', (input: any) => {

  let page = JSON.parse(input)
  return page?.body || []
});

registerFilter('jsonParse', (input: any) => {

  let page = JSON.parse(input)
  return page || {}
});

import './utils/darkMode/darkMode.css';
const iconSelectList =
  require('@/utils/amisJsonForm/iconSelectList').iconSelectList;
// // 初始化SVG图标
if (iconSelectList && iconSelectList.data) {
  mountIconSpriteToDom(iconSelectList.data.spirite);
  setSvgIconList(iconSelectList.data.icons);
}

// 从本地存储获取保存的主题设置，如果没有则默认使用cxd主题
const theme = localStorage.getItem('amis-theme') || 'cxd';
setDefaultTheme(theme);
setThemeConfig(themeConfig);

// 在body上添加主题类名
document.body.classList.add(theme);

// react < 18
ReactDOM.render(<App />, document.getElementById('root'));
