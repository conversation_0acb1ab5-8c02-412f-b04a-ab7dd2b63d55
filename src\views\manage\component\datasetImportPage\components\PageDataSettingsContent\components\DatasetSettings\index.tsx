import React from 'react';
import AMISRenderer from '@/component/AMISRenderer';
import './index.scss';

interface DatasetSettingsProps {
  dataSetInfo: any;
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;  // 为其他可能的属性添加索引签名
  };
  onRefresh: (newDataSetId?: number) => void; // 修改刷新方法，添加参数
}

const DatasetSettings: React.FC<DatasetSettingsProps> = ({ dataSetInfo, initPathItem, onRefresh }) => {
  
  return (
    <div className="dataset-settings">
      <div className="dataset-settings-container">
        <div className="dataset-settings-left">
          <h2>数据集设置</h2>
          <p className="text-muted">请选择数据源、数据集</p>
          
          <div className="current-dataset">
            <div className="current-dataset-label">当前数据集：</div>
            <div className="current-dataset-value">{dataSetInfo?.name || '-'}</div>
          </div>
        </div>
        
        <div className="dataset-settings-right">
          <div className="dataset-settings-actions">
            <AMISRenderer 
              schema={{
                type: 'button',
                label: '修改',
                level: 'primary',
                size: 'sm',
                actionType: 'dialog',
                dialog: {
                  title: '修改数据源',
                  size: 'md',
                  body: {
                    type: 'form',
                    api: {
                      method: 'put',
                      url: '/admin-api/system/application-page-and-class/update',
                      data: {
                        id: initPathItem.id || '',
                        dataSetId: '${dataSetId}',
                        applicantOrBackend: 2
                      },
                      messages: {
                        success: '修改成功'
                      }
                    },
                    onFinished: (values: any, action: any) => {
                      console.log('Form submission finished with values:', values);
                      // 获取选择的数据集ID
                      const dataSetId = values?.data?.dataSetId;
                      console.log('Extracted dataSetId from response:', dataSetId);
                      
                      if (dataSetId) {
                        console.log('Calling onRefresh with dataSetId:', dataSetId);
                        onRefresh(Number(dataSetId));
                      } else {
                        // 尝试从表单值中获取
                        const formDataSetId = values?.dataSetId;
                        console.log('Extracted dataSetId from form values:', formDataSetId);
                        
                        if (formDataSetId) {
                          console.log('Calling onRefresh with form dataSetId:', formDataSetId);
                          onRefresh(Number(formDataSetId));
                        } else {
                          console.error('Failed to get dataSetId from response or form');
                        }
                      }
                    },
                    body: [
                      {
                        type: 'select',
                        name: 'dataSetId',
                        label: '选择数据集',
                        source: {
                          method: 'get',
                          url: '/admin-api/system/data-set/page',
                          data: {
                            pageNo: 1,
                            pageSize: 100,
                            applicantOrBackend: 2 // 后台数据集
                          },
                          responseData: {
                            options: "${list|pick:label~name,value~id}"
                          }
                        },
                        value: dataSetInfo?.id || '',
                        required: true,
                        searchable: true,
                        clearable: false
                      }
                    ]
                  },
                  actions: [
                    {
                      type: 'button',
                      label: '取消',
                      actionType: 'close'
                    },
                    {
                      type: 'button',
                      label: '确认',
                      level: 'primary',
                      actionType: 'submit',
                      close: true // 提交后关闭对话框
                    }
                  ]
                }
              }}
            />
            
            <AMISRenderer 
              schema={{
                type: 'button',
                label: '查看数据集',
                level: 'link',
                actionType: 'link',
                link: `/manage/dataSet?id=${dataSetInfo?.id}`
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatasetSettings; 