import React, {FC, useEffect, useState} from 'react';
import './index.scss';

import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj} from '@/utils/schemaPageTemplate/createPageObjs';
import {navigateWithSource} from '@/utils/routeUtils';
// 工作台
import WorkbenchView from '@/views/workbench/component/WorkbenchView/index';
// 审批
import WorkbenchApproval from '@/views/workbenchApprovalCenter/indexTabs';
// wflow消息
import DatasetImportPage from '@/views/manage/component/datasetImportPage/index';
// 个人设置
import Account from '@/views/account/index';
// 组件模板
import ComponentTemplatePageContent from '@/views/componentTemp/index';


const SwitchRenderContent: FC<any> = (props: any) => {
  let [params, setParams] = useState<any>(null);

  useEffect(() => {
    setParams(props.match.params);
  }, [props.location.pathname]);

  useEffect(() => {
    console.log('workbench SwitchRenderContent params',params)
  },[params])

  return (
    
    <div className="switchRenderContent">
      {
        // 组织后台
        params?.menu && params?.menu == 'organizationManage' &&(
          (() => {
            sessionStorage.setItem('fromAppbuild', 'false');
            navigateWithSource(props, '/organization/manage/company');
            return null;
          })()
        )
      }
      {
        // 平台后台
        params?.menu && params?.menu == 'platformManage' &&(
          (() => {
            sessionStorage.setItem('fromAppbuild', 'false');
            navigateWithSource(props, '/platform/manage');
            return null;
          })()
        )
      }
      {
        //工作台
        params?.menu && params?.menu == 'workbench' &&(
          <WorkbenchView history={props.history} />
        )
      }
      {
        // 审批
        params?.menu && params?.menu == 'approval' && (
          <WorkbenchApproval
            store={props.store}
            history={props.history}
            match={props.match}
            location={props.location}
          />
        )
      }
      {
        // 消息
        params?.menu && params?.menu == 'message' && (
          <AMISRenderer
            schema={createIframeObj(
              `${
                process.env.NODE_ENV === 'development'
                  ? 'http://localhost:88/#/workspace/notice'
                  : 'https://javaddm.fjpipixia.com/wflow/#/workspace/notice'
              }?userID=${props.store.userInfo?.id}&tenantId=${
                props.store.tenant_id
              }&authorization=${props.store.access_token}`
            )}
          />
        )
      }
      {
        //wflow消息
        params?.menu && params?.menu == 'importMessage' && (
          <DatasetImportPage
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            initPathItem={{
              id: 1480,
              applicantOrBackend: 2,
              applicationId: null,
              parentId: 0,
              dataSetId: 720,
              type: 1,
              sort: null,
              name: 'wflow消息',
              description: null,
              pageType: 13,
              url: '',
              jmalUserId: null,
              children: [],
              createTime: 1744074411000,
              path: '/platform/importMessage',
              icon: 'fa-solid fa-message'
            }}
          />
        )
      }
      {
        // 组件模板
        params?.menu && params?.menu == 'componentTemplate' && (
          <ComponentTemplatePageContent
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
          />
        )
      }
      {
        //个人设置
        params?.menu && params?.menu == 'account' && (
          <Account
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
          />
        )
      }
    </div>
  );
};

export default SwitchRenderContent;
