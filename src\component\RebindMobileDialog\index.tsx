import React from 'react';
import {Mo<PERSON>, Tabs, Tab, Input, Button, toast} from 'amis';
import {BindMobileDialog} from '@/component/BindMobileDialog';
import './index.scss';

export interface RebindMobileDialogProps {
  show: boolean;
  currentMobile?: string;
  emailCountdown: number;
  mobileCountdown: number;
  onClose: () => void;
  onConfirm: (mobile: string, code: string) => void;
  onSendEmailCode: (email: string) => void;
  onSendMobileCode: (mobile: string) => void;
}

export const RebindMobileDialog: React.FC<RebindMobileDialogProps> = ({
  show,
  currentMobile,
  emailCountdown,
  mobileCountdown,
  onClose,
  onConfirm,
  onSendEmailCode,
  onSendMobileCode
}) => {
  const [email, setEmail] = React.useState('');
  const [code, setCode] = React.useState('');
  const [phone, setPhone] = React.useState('');
  const [phoneCode, setPhoneCode] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [showBindMobile, setShowBindMobile] = React.useState(false);
  const [verifyType, setVerifyType] = React.useState('mobile');

  const handleSendCode = (type: 'email' | 'mobile') => {
    // if (type === 'email') {
    //   if (!value || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    //     toast.error('请输入正确的邮箱');
    //     return;
    //   }
    //   onSendEmailCode(value);
    // } else if (type === 'mobile') {
      // if (!value || !/^1\d{10}$/.test(value)) {
      //   toast.error('请输入正确的手机号');
      //   return;
      // }
      if (currentMobile) {
        onSendMobileCode(currentMobile);
      }
    // }
  };

  const handleConfirm = () => {
    if (verifyType === 'email') {
      if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        toast.error('请输入正确的邮箱');
        return;
      }

      if (!code || code.length !== 6) {
        toast.error('请输入正确的验证码');
        return;
      }
    } else if (verifyType === 'mobile') {
      // if (!phone || !/^1\d{10}$/.test(phone)) {
      //   toast.error('请输入正确的手机号');
      //   return;
      // }

      if (!phoneCode || phoneCode.length !== 6) {
        toast.error('请输入正确的验证码');
        return;
      }
    } else if (verifyType === 'password') {
      if (!password) {
        toast.error('请输入密码');
        return;
      }
    }

      setShowBindMobile(true);
      onClose();
  };

  return (
    <>
      <Modal
        show={show}
        onClose={onClose}
        closeOnEsc
        closeOnOutside={false}
      >
        <div className="rebind-mobile-dialog">
          <div className="dialog-header">
            <b>更换手机号</b>
          </div>
          <div className="dialog-body">
            {/* <Tabs
              mode="line"
              activeKey={verifyType}
              onSelect={(key: string) => setVerifyType(key as 'email' | 'mobile' | 'password')}
              linksClassName="verify-tabs"
            >
              <Tab eventKey="mobile" title="手机验证"> */}
                {verifyType === 'mobile' && (
                  <>
                    <div className="form-item">
                      <Input
                        maxLength={11}
                        value={currentMobile}
                        disabled={true}
                        onChange={e => setPhone(e.target.value)}
                        placeholder="手机号"
                        className="form-control"
                      />
                    </div>
                    <div className="form-item code-item">
                      <Input
                        value={phoneCode}
                        onChange={e => setPhoneCode(e.target.value)}
                        placeholder="验证码"
                        maxLength={6}
                        className="form-control"
                      />
                      <Button
                        className="verify-code-btn"
                        disabled={mobileCountdown > 0}
                        onClick={() => handleSendCode('mobile')}
                      >
                        {mobileCountdown > 0 ? `${mobileCountdown}秒后重新获取` : '获取验证码'}
                      </Button>
                    </div>
                  </>
                )}
              {/* </Tab> */}
              {/* <Tab eventKey="email" title="邮箱验证">
                {verifyType === 'email' && (
                  <>
                    <div className="form-item">
                      <Input
                        maxLength={32}
                        value={email}
                        onChange={e => setEmail(e.target.value)}
                        placeholder="邮箱"
                        className="form-control"
                      />
                    </div>
                    <div className="form-item code-item">
                      <Input
                        value={code}
                        onChange={e => setCode(e.target.value)}
                        placeholder="验证码"
                        maxLength={6}
                        className="form-control"
                      />
                      <Button
                        className="verify-code-btn"
                        disabled={emailCountdown > 0}
                        onClick={() => handleSendCode('email', email)}
                      >
                        {emailCountdown > 0 ? `${emailCountdown}秒后重新获取` : '获取验证码'}
                      </Button>
                    </div>
                  </>
                )}
              </Tab>
              <Tab eventKey="password" title="密码验证">
                {verifyType === 'password' && (
                  <div className="form-item">
                    <Input
                      maxLength={32}
                      type="password"
                      placeholder="当前密码"
                      value={password}
                      onChange={e => setPassword(e.target.value)}
                      className="form-control"
                    />
                  </div>
                )}
              </Tab> */}
            {/* </Tabs> */}
          </div>
          <div className="dialog-footer">
            <Button
              onClick={onClose}
              className="cancel-btn"
            >
              取消
            </Button>
            <Button
              onClick={handleConfirm}
              level="primary"
              className="confirm-btn"
            >
              下一步
            </Button>
          </div>
        </div>
      </Modal>

      <BindMobileDialog
        show={showBindMobile}
        isRebind={true}
        countdown={mobileCountdown}
        currentMobile={currentMobile}
        onClose={() => setShowBindMobile(false)}
        onConfirm={onConfirm}
        onSendCode={onSendMobileCode}
      />
    </>
  );
};

export default RebindMobileDialog; 