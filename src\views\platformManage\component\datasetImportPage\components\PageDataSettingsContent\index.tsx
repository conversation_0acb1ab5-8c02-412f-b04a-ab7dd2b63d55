import React from 'react';
import './index.scss';
import DatasetSettings from './components/DatasetSettings';
import QuerySettings from './components/QuerySettings';
import TopToolbar from './components/TopToolbar';
import BottomToolbar from './components/BottomToolbar';
import OperationSettings from './components/OperationSettings';
import SingleOperation from './components/SingleOperation';

interface PageDataSettingsContentProps {
  activeKey: string;
  dataSetInfo: any;
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;  // 为其他可能的属性添加索引签名
  };
  onRefresh: (newDataSetId?: number) => void; // 修改刷新方法，添加参数
}

const PageDataSettingsContent: React.FC<PageDataSettingsContentProps> = ({
  activeKey,
  dataSetInfo,
  initPathItem,
  onRefresh
}) => {
  const renderContent = () => {
    switch (activeKey) {
      case 'datasetSettings':
        return <DatasetSettings dataSetInfo={dataSetInfo} initPathItem={initPathItem} onRefresh={onRefresh} />;
      case 'querySettings':
        return <QuerySettings dataSetInfo={dataSetInfo} initPathItem={initPathItem} />;
      case 'topToolbar':
        return <TopToolbar dataSetInfo={dataSetInfo} initPathItem={initPathItem} />;
      case 'bottomToolbar':
        return <BottomToolbar dataSetInfo={dataSetInfo} initPathItem={initPathItem} />;
      case 'operationSettings':
        return <OperationSettings dataSetInfo={dataSetInfo} initPathItem={initPathItem} />;
      case 'singleOperation':
        return <SingleOperation dataSetInfo={dataSetInfo} initPathItem={initPathItem} />;
      default:
        return <div>请选择设置项</div>;
    }
  };

  return (
    <div className="page-data-settings-content">
      {renderContent()}
    </div>
  );
};

export default PageDataSettingsContent; 