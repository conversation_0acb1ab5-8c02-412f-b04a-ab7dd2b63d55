import React, {FC} from 'react';
import './index.scss';
import approval_icon from './image/approval_icon.png'
// 审批人
const APPROVAL: FC<any> = (props: any) => {
    return (
        <div className="process_approval">
      <div className="process_approval-info">
        <div className="process_approval-info-box">
          <img className="process_approval-info-box-icon" src={approval_icon} />
        </div>

        <div className="process_approval-info-text">
          <div className="process_approval-info-text-name">{props.item.name}</div>
          <div className="process_approval-info-text-desc">{props.item.desc}</div>
        </div>

        <div className="process_approval-info-member">
          {props.item.props.assignedUser.map((userItem: any) => {
            return (
              <div className="process_approval-info-member-item" key={userItem.id}>
                <div className="process_approval-info-member-item-header">
                {userItem.avatar ? (
                    <img
                      className="process_cc-info-member-item-header-img"
                      src={userItem.avatar}
                    />
                  ) : (
                    <span>{userItem.name}</span>
                  )}
                </div>
                <div className="process_approval-info-member-item-name">{userItem.name}</div>
              </div>
            );
          })}
        </div>
      </div>
      <div className="process_approval-line"></div>
    </div>
    )
}

export default APPROVAL;