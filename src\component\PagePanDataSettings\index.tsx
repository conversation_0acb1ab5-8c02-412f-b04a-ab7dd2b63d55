import React, {FC, Fragment} from 'react';
import './index.scss';
import {toast, Input as TextInput, Checkbox, Radios, Transfer, Combo, Button, Form} from 'amis';

// 图片引入
import btn_icon from './image/page_btn_icon.png';
import mssage_icon from './image/page_mssage_icon.png';
import nav_icon from './image/page_nav_icon.png';
import permissions_icon from './image/page_permissions_icon.png';
import print_icon from './image/page_print_icon.png';
import qrCode_icon from './image/page_qrCode_icon.png';
import set_icon from './image/page_set_icon.png';

import app_default from '@/image/common_icons/app_default_icon.png';

import AMISRenderer from '@/component/AMISRenderer';
// 引入修改应用名称弹窗
import {modifyPageNamePopup} from './common/modifyPageNamePopup';

import {modifyPageDescPopup} from './common/modifyPageDescPopup';

import {modifyColumnSizePopup} from './common/modifyColumnSizePopup';

import {modifyDataSetPopup} from './common/modifyDataSetPopup';

import {editRute,amisCodeUpdate,amisCodeGet, updateDatasetIdBindingRute, getShowField, ruteCodeSave, updateFormData, updateApplicationPageAndClass, getFormDataPage} from '@/utils/api/api';

import {createDatasetImportObj} from '@/utils/schemaPageTemplate/createPageObjs';
import {singleOperationObj} from './common/singleOperation';

import {
  findCreateBody
} from '@/utils/schemaDataSet/commonEditor';

const PageSettings: FC<any> = (props: any) => {
  /* data 数据 */
  const menuList = [
    {
      label: '数据集设置',
      icon: 'fas fa-database',
      customIcon: 'fas fa-database',
      key: 'datacollection'
    },
    {
      label: '查询设置',
      icon: 'fas fa-search',
      customIcon: 'fas fa-search',
      key: 'querySetting'
    },
    {
      label: '顶部工具栏',
      icon: 'fas fa-bars',
      customIcon: 'fas fa-bars',
      key: 'topToolbarSetting'
    },
    {
      label: '底部工具栏',
      icon: 'fas fa-ellipsis-h',
      customIcon: 'fas fa-ellipsis-h',
      key: 'bottomToolbarSetting'
    },
    {
      label: '操作栏设置',
      icon: 'fas fa-cogs',
      customIcon: 'fas fa-cogs',
      key: 'actionBarSetting'
    },
    // {
    //   label: '批量操作设置',
    //   icon: 'fas fa-tasks',
    //   customIcon: 'fas fa-tasks',
    //   key: 'batchOperationSetting'
    // },
    {
      label: '单条操作',
      icon: 'fas fa-mouse-pointer',
      customIcon: 'fas fa-mouse-pointer',
      key: 'singleOperationSetting'
    }];

  // 选中的菜单item
  const [activeItem, setActiveItem] = React.useState<any>(menuList[0]);

  const RadiosOptions = [
    {
      label: '默认页面',
      value: '1'
    },
    {
      label: '应用内页面',
      value: '2'
    },
    {
      label: '外部链接',
      value: '3'
    }
  ];

  const [options, setOptions] = React.useState<any>([]);

  const [transferValue, setTransferValue] = React.useState<any>([]);

  // 打开修改页面名称弹窗
  const [openPageNamePopup, setOpenPageNamePopup] = React.useState(false);
  // 打开修改页面描述弹窗
  const [openPageDescPopup, setOpenPageDescPopup] = React.useState(false);

  // 打开修改每列显示几个字段弹窗
  const [openColumnSizePopup, setOpenColumnSizePopup] = React.useState(false);

  const [amisCode , setAmisCode] = React.useState<any>(false);

  // 是否启用查询条件
  const [isEnabled, setIsEnabled] = React.useState<boolean>(false);


  // 每列显示几个字段
  const [columnSize, setColumnSize] = React.useState<number>(2);

  const [headerToolbarArray, setHeaderToolbarArray] = React.useState<any>([]);
  const [bottomToolbarArray, setBottomToolbarArray] = React.useState<any>([]);

  const [optEditEnabled, setOptEditEnabled] = React.useState<boolean>(false);
  const [optViewEnabled, setOptViewEnabled] = React.useState<boolean>(false);
  const [optDeleteEnabled, setOptDeleteEnabled] = React.useState<boolean>(false);
  const [singleOptEnabled, setSingleOptEnabled] = React.useState<boolean>(false);

  const [dataSetList, setDataSetList] = React.useState<any>([]);

  const [openModifyDataSetPopup, setOpenModifyDataSetPopup] = React.useState<boolean>(false);
  const showFieldData = React.useRef<any>([]);

  /* data 数据 end */

  const headerToolbarSchema = {
    name: 'headerToolbar',
    type: 'combo',
    draggable: true,
    draggableTip: '',
    descrition: '非内建内容请在预览区选中后编辑',
    label: '顶部工具栏配置',
    value: headerToolbarArray,
    pipeIn: (value: any) => {
      return value.map((item: any) => {
        let type = item.type;
  
        if (
          typeof item === 'string' &&
          ~[
            'bulkActions',
            'bulk-actions',
            'pagination',
            'statistics',
            'switch-per-page',
            'filter-toggler',
            'load-more',
            'export-csv',
            'export-excel',
            'reload',
            'export-excel-template'
          ].indexOf(item)
        ) {
          type = item === 'bulkActions' ? 'bulk-actions' : item;
          item = {type};
        } else if (typeof item === 'string') {
          type = 'tpl';
          item =
            typeof item === 'string'
              ? {type: 'tpl', tpl: item, wrapperComponent: ''}
              : item;
        }
        return {
          type,
          ...item
        };
      });
    },
    pipeOut: (value: any) => {
      if (Array.isArray(value)) {
        // 接口网络请求处理
        handleHeadChange(value)
        return value.map((item: any) => {
          return item;
        });
      }
  
      return [];
    },
    scaffold: {
      type: 'tpl',
      align: 'left',
      tpl: '内容'
    },
    multiple: true,
    items: [
      {
        type: 'select',
        name: 'type',
        columnClassName: 'w-ssm',
        overlay: {
          align: 'left',
          width: 150
        },
        options: [
          {
            value: 'pagination',
            label: '分页'
          },
  
          {
            value: 'statistics',
            label: '统计数据'
          },
  
          {
            value: 'switch-per-page',
            label: '切换页码'
          },
  
          {
            value: 'load-more',
            label: '加载更多'
          },
  
          {
            value: 'export-csv',
            label: '导出 CSV'
          },
  
          {
            value: 'export-excel',
            label: '导出 Excel'
          },
          {
            value: 'tpl',
            label: '文本'
          },
  
          {
            value: 'reload',
            label: '刷新'
          },
          {
            value: 'export-excel-template',
            label: '导出Excel模板'
          }
        ]
      },
  
      {
        name: 'align',
        placeholder: '对齐方式',
        type: 'select',
        size: 'xs',
        options: [
          {
            label: '左对齐',
            value: 'left'
          },
  
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      },
      {
        name: 'tpl',
        type: 'input-text',
        visibleOn: "${type === 'tpl'}",
        placeholder: "文本内容"
      }
    ]
  }

  const bottomToolbarSchema = {
    name: 'footerToolbar',
    type: 'combo',
    draggable: true,
    draggableTip: '',
    descrition: '非内建内容请在预览区选中后编辑',
    label: '底部工具栏配置',
    value: bottomToolbarArray,
    pipeIn: (value: any) => {
      return value.map((item: any) => {
        let type = item.type;
  
        if (
          typeof item === 'string' &&
          ~[
            'bulkActions',
            'bulk-actions',
            'pagination',
            'statistics',
            'switch-per-page',
            'filter-toggler',
            'load-more',
            'export-csv',
            'export-excel',
            'reload',
            'export-excel-template'
          ].indexOf(item)
        ) {
          type = item === 'bulkActions' ? 'bulk-actions' : item;
          item = {type};
        } else if (typeof item === 'string') {
          type = 'tpl';
          item =
            typeof item === 'string'
              ? {type: 'tpl', tpl: item, wrapperComponent: ''}
              : item;
        }
        return {
          type,
          ...item
        };
      });
    },
    pipeOut: (value: any) => {
      if (Array.isArray(value)) {
        // 接口网络请求处理
        handleBottomChange(value)
        return value.map((item: any) => {
          return item;
        });
      }
  
      return [];
    },
    scaffold: {
      type: 'tpl',
      align: 'left',
      tpl: '内容'
    },
    multiple: true,
    items: [
      {
        type: 'select',
        name: 'type',
        columnClassName: 'w-ssm',
        overlay: {
          align: 'left',
          width: 150
        },
        options: [
          {
            value: 'pagination',
            label: '分页'
          },
  
          {
            value: 'statistics',
            label: '统计数据'
          },
  
          {
            value: 'switch-per-page',
            label: '切换页码'
          },
  
          {
            value: 'load-more',
            label: '加载更多'
          },
  
          {
            value: 'export-csv',
            label: '导出 CSV'
          },
  
          {
            value: 'export-excel',
            label: '导出 Excel'
          },
          {
            value: 'tpl',
            label: '文本'
          },
  
          {
            value: 'reload',
            label: '刷新'
          },
          {
            value: 'export-excel-template',
            label: '导出Excel模板'
          }
        ]
      },
  
      {
        name: 'align',
        placeholder: '对齐方式',
        type: 'select',
        size: 'xs',
        options: [
          {
            label: '左对齐',
            value: 'left'
          },
  
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      },
      {
        name: 'tpl',
        type: 'input-text',
        visibleOn: "${type === 'tpl'}",
        placeholder: "文本内容"
      }
    ]
  }
  /* methods 方法 */
  // 修改是否启用查询
  const modifyIsEnabled = (val: any) => {
    let originSchema = props.pageData.data;
    if (val) {
      originSchema.body[0].autoGenerateFilter = {
        columnsNum: columnSize,
        showBtnToolbar: true,
        defaultCollapsed: false
      };
    } else {
      originSchema.body[0].autoGenerateFilter = null;
    }
    updateFromSchema(originSchema)
  };

  const modifyOperationEdit = (val: any) => {
    console.log('val', val);
    let columns = props.pageData.data.body[0].columns;
    const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
    const editBtn = operationBarBtns.find((item: any) => item.label == '预览');
    editBtn.hidden = !val;

    let originSchema = props.pageData.data;
    originSchema.body[0].columns = columns;
    updateFromSchema(originSchema)
  }

  const modifyOperationView = (val: any) => {
    console.log('val', val);
    let columns = props.pageData.data.body[0].columns;
    const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
    const viewBtn = operationBarBtns.find((item: any) => item.label == '下载');
    viewBtn.hidden = !val;

    let originSchema = props.pageData.data;
    originSchema.body[0].columns = columns;
    updateFromSchema(originSchema)
  }

  const modifyOperationDelete = (val: any) => {
    console.log('val', val);
    let columns = props.pageData.data.body[0].columns;
    const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
    const deleteBtn = operationBarBtns.find((item: any) => item.label == '删除');
    deleteBtn.hidden = !val;

    let originSchema = props.pageData.data;
    originSchema.body[0].columns = columns;
    updateFromSchema(originSchema)
  }

  const modifyOperationSingle = (val: any) => {
    let originSchema = props.pageData.data;

    originSchema.body[0].singleOptEnabled = val;
    //
    originSchema.body[0].headerToolbar.forEach((item: any) => {
      if (item.cusSingleAction) {
        item.hidden = !val;
      }
    })

    updateFromSchema(originSchema)
  }

  // 修改每列显示几个字段
  const modifyColumnSize = (val: any) => {
    console.log('val', val["0"]);
    setColumnSize(val["0"].columnSize);
    setOpenColumnSizePopup(false);

    let originSchema = props.pageData.data;
    originSchema.body[0].columnSize = val["0"].columnSize;
    if (isEnabled) {
      originSchema.body[0].autoGenerateFilter = {
        columnsNum: val["0"].columnSize,
        showBtnToolbar: true,
        defaultCollapsed: false
      };
    }
    updateFromSchema(originSchema)
  };

  // 处理Transfer值变化
  const handleTransferChange = (values: any) => {
    console.log('values', values);
    setTransferValue(values);
    // 获取原始schema
    let originSchema = props.pageData.data;
    let columns = originSchema.body[0].columns;
    
    // 将values数组map为value strings
    let selectedValues = values.map((item: any) => item.value);
    
  // 创建新的columns数组，而不是直接修改原有对象
  const newColumns = columns.map((column: any) => {
    // 创建列的浅拷贝
    const newColumn = { ...column };
    
    if (selectedValues.includes(newColumn.name)) {
      var type = newColumn.type;
      type = 'input-text';

      newColumn.searchable = {
        type: type,
        name: newColumn.name,
        label: newColumn.label || newColumn.name,
        placeholder: `输入${newColumn.label || newColumn.name}`,
        mode: 'v',
      };
    } else {
      newColumn.searchable = null;
    }
    
    return newColumn;
  });

  // 更新schema中的columns
  originSchema.body[0].columns = newColumns;

  updateFromSchema(originSchema);
  };

    // 处理head值变化
  const handleHeadChange = (values: any) => {
    setHeaderToolbarArray(values);
    // 获取原始schema
    let originSchema = props.pageData.data;
    let headerToolbar = originSchema.body[0].headerToolbar;

    let cusSingleAction = headerToolbar.filter((item: any) => item.cusSingleAction === true);
    let newHeaderToolbar = [headerToolbar[0]];
    values.forEach((item: any) => {
      newHeaderToolbar.push(item);
    });
    newHeaderToolbar.push(...cusSingleAction);
    originSchema.body[0].headerToolbar = newHeaderToolbar;
    updateFromSchema(originSchema)
  };

  const handleBottomChange = (values: any) => {
    setBottomToolbarArray(values);
    // 获取原始schema
    let originSchema = props.pageData.data;
    let footerToolbar = originSchema.body[0].footerToolbar;

    let newFooterToolbar: any = [];
    values.forEach((item: any) => {
      newFooterToolbar.push(item);
    });
    originSchema.body[0].footerToolbar = newFooterToolbar;
    updateFromSchema(originSchema)
  };

  const handleGetFormDataPage = (pageId: any) => {
    let data = {
      applicationPageId: pageId,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            let pageData = res.data.list[0]
            let field = JSON.parse(pageData.field)
            let title = props.pageData.data.title
            let schema = createDatasetImportObj(title, pageId, field, props.pageData.dataSetId)
            updateFromSchema(schema, pageData.id)
            // updateFromSchema(pageId, info, res.data.list[0])
          }else{
          }
        }else{
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  const saveSigleAction = (values: any) => {
    let originSchema = props.pageData.data;
    
    originSchema.body[0].itemActions = values.list.filter((item: any) => item.isEnabled === 1).map((item: any) => {
      let field = showFieldData.current.find((field: any) => field.name === item.fieldName);

      return {
        label: item.buttonName,
        type: 'button',
        hiddenOnHover: true,
        name: item.buttonName,
        actionType: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
        cusSingleAction: true,
        visibleOn: "${selectedItems.length === 1}", 
        id: item.id,
        drawer: {
          type: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
          title: item.buttonDepict,
          body: [
            {
              type: 'form',
              label: item.label,
              api: {
                method: 'put',
                url: '/admin-api/system/form-field-value/updateOneFieldVlue'
              },
              body: [
                {
                  type: field.type || 'input-text',
                  name: 'value',
                  label: field.label
                },
                {
                  type: 'input-text',
                  name: 'id',
                  value: '${selectedItems[0].id}',
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'applicationPageId',
                  value: props.pageData.applicationPageId,
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'fieldName',
                  value: item.fieldName,
                  hidden: true
                }
              ]
            }
          ]
        },
        dialog: {
          type: item.popWinWay == '弹窗' ? 'dialog' : 'drawer',
          title: item.buttonName,
          body: [
            {
              type: 'form',
              label: item.buttonName,
              api: {
                method: 'put',
                url: '/admin-api/system/form-field-value/updateOneFieldVlue'
              },
              body: [
                {
                  type: field.type || 'input-text',
                  name: 'value',
                  label: field.label
                },
                {
                  type: 'input-text',
                  name: 'id',
                  value: '${selectedItems[0].id}',
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'applicationPageId',
                  value: props.pageData.applicationPageId,
                  hidden: true
                },
                {
                  type: 'input-text',
                  name: 'fieldName',
                  value: item.fieldName,
                  hidden: true
                }
              ]
            }
          ]
        }
      }
    })


    let headerToolbar = originSchema.body[0].headerToolbar.filter((item: any) => item.cusSingleAction != true)

    for (let i = 0; i < originSchema.body[0].itemActions.length; i++) {
      headerToolbar.push(originSchema.body[0].itemActions[i])
    }
    // console.log("headerToolbar", headerToolbar)
    originSchema.body[0].headerToolbar = headerToolbar
    originSchema.body[0].selectable = true;
    originSchema.body[0].multiple = true;

    updateFromSchema(originSchema)
  }

  const updateFromSchema = (schema: any, id: any = props.pageData.id) => {
    let data = {
      id: id,
      data: JSON.stringify(schema),
      field: props.pageData.field,
      applicationPageId: props.pageData.applicationPageId
    };
    updateFormData(data).then((res: any) => {
      if (res.code == 0) {
        // handleGetApplicationPageAndClass();
        props.update({
          ...props.pageData,
          data: schema
        });
        toast.success('保存成功');
      } else {
        toast.error(res.msg);
      }
    });
  }

  // 设置活动菜单项并更新URL
  const handleMenuItemClick = (item: any) => {
    console.log('🔥 PagePanDataSettings handleMenuItemClick:', item.label, 'key:', item.key);

    // 设置活动菜单项
    setActiveItem(item);

    // 更新URL参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', 'setting'); // 确保设置activeTabKey为setting
    urlParams.set('activeMenu', item.key);

    console.log('🔥 PagePanDataSettings Final URL:', props.history.location.pathname + '?' + urlParams.toString());

    // 使用history.replace更新URL而不创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  /* created 初始化 */
  React.useEffect(() => {
    // if (props.pageData.id && activeItem.path == 'print' ) {
    //   // editProess();
    // }

    let schema = props.pageData.data ?? {};

    // showFieldData.current = JSON.parse(props.pageData?.field || '[]')
    // // let datasetName = props.data.pageType == 13 ?  schema?.title
    // let datasetName = props.pageData?.dataSetName
    // setDataSetList([datasetName])
    let body = schema?.body && schema?.body[0] ? schema?.body[0] : {};
    let columns = body?.columns ? body?.columns : [];
    console.log("columns", columns)
    // //  JSON.parse()["body"]["0"]["columns"];
    // //没有schema返回
    // if (!columns) {
    //   return;
    // }


    // // 创建新的options数组
    const newOptions = columns
      .filter((item: any) => item.name) // 过滤出name不为空的选项
      .map((item: any) => ({
        label: item.label || item.name,
        value: item.name
      }));

    setOptions(newOptions);

    // // 获取已经配置了searchable的字段，并按新格式处理
    const searchableColumns = columns.filter((item: any) => item.searchable !== null && item.searchable !== undefined && item.searchable !== false);
    const initialTransferValue = searchableColumns.map((item: any) => ({
      label: item.label || item.name,
      value: item.name
    }));
    setTransferValue(initialTransferValue);

    // // 获取autoGenerateFilter
    const autoGenerateFilter = body?.autoGenerateFilter;
    setIsEnabled(!!autoGenerateFilter);

        // 获取singleOptEnabled
    const singleOptEnabled = body?.singleOptEnabled;
    setSingleOptEnabled(!!singleOptEnabled);

        
    // // 获取每列显示几个字段
    const columnSize = body?.columnSize || 2;
    setColumnSize(columnSize);


    // // 获取headerToolbar
    const headerToolbar = body?.headerToolbar ?? [];
    // // 转为数组[{align: 'left', type: 'bulk-actions'}]
    const typesArray = [
      'pagination',
      'statistics',
      'switch-per-page',
      'load-more',
      'export-csv',
      'export-excel',
      'reload',
      'tpl',
      'export-excel-template'
    ]

    const headerToolbarArray = headerToolbar.filter((item: any) => typesArray.includes(item.type)).map((item: any) => ({
      align: item.align || 'left',
      type: item.type,
      tpl: item.tpl
    }));



    setHeaderToolbarArray(headerToolbarArray)

    const footerToolbar = body?.footerToolbar ?? [];
    if (footerToolbar) {
      const footerToolbarArray = footerToolbar.filter((item: any) => typesArray.includes(item.type)).map((item: any) => ({
        align: item.align || 'left',
        type: item.type,
        tpl: item.tpl
      }));

      setBottomToolbarArray(footerToolbarArray)
    } else {
      let s1 = {align: 'right', type: 'pagination'}
      let s2 = {align: 'left', type: 'statistics'}
      setBottomToolbarArray([s1, s2])
    }


    // // 获取操作栏设置 type == operation
    if (columns.filter((item: any) => item.type == 'operation').length > 0) {
      const operationBarBtns = columns.filter((item: any) => item.type == 'operation')[0]['buttons'];
      // label == 编辑
      const editBtn = operationBarBtns.find((item: any) => item.label == '预览');
      // label == 查看
      const viewBtn = operationBarBtns.find((item: any) => item.label == '下载');
      // label == 删除
      const deleteBtn = operationBarBtns.find((item: any) => item.label == '删除');
  
      // // 获取按钮的hiddenOn
      const editBtnHidden = editBtn?.hidden || false;
      const viewBtnHidden = viewBtn?.hidden || false;
      const deleteBtnHidden = deleteBtn?.hidden || false;
  
        setOptEditEnabled(!editBtnHidden);
        setOptViewEnabled(!viewBtnHidden);
        setOptDeleteEnabled(!deleteBtnHidden);
    }

    // 从URL参数中获取activeMenu
    const urlParams = new URLSearchParams(props.history.location.search);
    const activeMenuPath = urlParams.get('activeMenu');
    const activeTabKey = urlParams.get('activeTabKey');

    console.log('🔥 PagePanDataSettings URL params - activeTabKey:', activeTabKey, 'activeMenu:', activeMenuPath);

    // 如果URL中有activeMenu参数，则设置对应的菜单项为活动项
    if (activeMenuPath) {
      const menuItem = menuList.find(item => item.key === activeMenuPath);
      if (menuItem) {
        console.log('🔥 PagePanDataSettings Setting activeItem to:', menuItem.label);
        setActiveItem(menuItem);
      }
    }
  }, [props.pageData, props.history.location]);

  // 自定义渲染器，用于显示自定义图标
  const navItemRender = (item: any, index: number) => {
    return {
      type: 'flex',
      className: 'nav-item-custom',
      items: [
        {
          type: 'image',
          src: item.customIcon,
          className: 'pageSettingsBox-aside-item-img'
        },
        {
          type: 'tpl',
          tpl: '${label}'
        }
      ]
    };
  };

  // MyNav组件的配置
  const navSchema = {
    type: 'my-nav',
    stacked: true, // 垂直布局
    className: 'pageSettingsBox-aside',
    links: menuList,
    itemRender: navItemRender,
    onSelect: (item: any) => handleMenuItemClick(item),
    activeKey: activeItem.key
  };

  /* created 初始化 end */
  return (
    <div className="pageSettingsBox">
      <AMISRenderer
        schema={navSchema}
        data={{}}
      />
      <div className="pageSettingsBox-content">


        {activeItem.key == 'datacollection' && (
          <Fragment>
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  启用查询条件
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: isEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {isEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
              <Button
                  type="button"
                  style={{
                    backgroundColor: isEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  // title="停用"
                  // actionType="dialog"
                  onClick={() => {
                    // 处理按钮点击事件
                    modifyIsEnabled(!isEnabled);
                  }}
                > {isEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>
            {/* 每列显示几个字段 */}
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  每列显示几个字段
                </div>
                <div className="pageSettingsBox-content-item-left-content">
                  {columnSize}个
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
                <Button
                  type="button"
                  onClick={() => {
                    setOpenColumnSizePopup(true);
                  }}
                >
                  修改
                </Button>
              </div>
            </div>

            <div className="pageSettingsBox-content-item transfer-container">
              <div className="transfer-header">
                <div className="pageSettingsBox-content-item-left-name">
                  启用的查询字段
                </div>
                <div className="pageSettingsBox-content-item-left-hint">
                  添加要查询的字段
                </div>
              </div>
              <div className="transfer-body">
                <Transfer
                  options={options}
                  value={transferValue}
                  onChange={(value: any) => handleTransferChange(value)}
                />
              </div>
            </div>
          </Fragment>
        )}
        {activeItem.key == 'topToolbarSetting' && (
          <AMISRenderer
            show={true}
            schema={headerToolbarSchema}
            onClose={() => {}}
            onConfirm={(val: any) => {}}
          />
        )}
        {activeItem.key == 'bottomToolbarSetting' && (
          <AMISRenderer
          show={true}
          schema={bottomToolbarSchema}
          onClose={() => {}}
          onConfirm={(val: any) => {}}
        />
        )}
        {activeItem.key == 'actionBarSetting' &&  (
          <Fragment>
            
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  操作栏-预览
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: optEditEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {optEditEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
              <Button
                  type="button"
                  style={{
                    backgroundColor: optEditEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  // title="停用"
                  // actionType="dialog"
                  onClick={() => {
                    // 处理按钮点击事件
                    modifyOperationEdit(!optEditEnabled);
                  }}
                > {optEditEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>

            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  操作栏-下载
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: optViewEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {optViewEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
              <Button
                  type="button"
                  style={{
                    backgroundColor: optViewEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  // title="停用"
                  // actionType="dialog"
                  onClick={() => {
                    // 处理按钮点击事件
                    modifyOperationView(!optViewEnabled);
                  }}
                > {optViewEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>

            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  操作栏-删除
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: optDeleteEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {optDeleteEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
              <Button
                  type="button"
                  style={{
                    backgroundColor: optDeleteEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  // title="停用"
                  // actionType="dialog"
                  onClick={() => {
                    // 处理按钮点击事件
                    modifyOperationDelete(!optDeleteEnabled);
                  }}
                > {optDeleteEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>
           
          </Fragment>
        )}

        {activeItem.key == 'singleOperationSetting' &&  (
          <Fragment>
            
            <div className="pageSettingsBox-content-item">
              <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  单条操作
                </div>
                <div className="pageSettingsBox-content-item-left-sub" style={{
                  color: '#B8BABF'
                }}>
                选中一个表格项后，可以实现表格项的编辑功能，支持自定义设置。<a>了解更多</a>
                </div>
              </div>
            </div>

            <div className="pageSettingsBox-content-item">
            <div className="pageSettingsBox-content-item-left">
                <div className="pageSettingsBox-content-item-left-name">
                  启用单条操作
                </div>
                <div className="pageSettingsBox-content-item-left-sub" style={{
                  color: '#B8BABF'
                }}>
                启用后，在表格顶部展示单条操作区
                </div>
                <div className="pageSettingsBox-content-item-left-hint" style={{
                  backgroundColor: singleOptEnabled ? '#30BF13' : '#F23D3D',
                  color: 'white'
                }}>
                  {singleOptEnabled ? '已启用' : '未启用'}
                </div>
              </div>
              <div className="pageSettingsBox-content-item-right">
              <Button
                  type="button"
                  style={{
                    backgroundColor: singleOptEnabled ? '#F23D3D' : '#30BF13',
                    color: 'white'
                  }}
                  onClick={() => {
                    // 处理按钮点击事件
                    modifyOperationSingle(!singleOptEnabled);
                  }}
                > {singleOptEnabled ? '停用' : '启用'}</Button>
              </div>
            </div>
            {singleOptEnabled && (
              <AMISRenderer
                schema={singleOperationObj(props.pageData.apply_id, showFieldData.current, props.pageData.applicationPageId, saveSigleAction)}
              />
            )}
           
          </Fragment>
        )}
      </div>
      {openColumnSizePopup && (
        <AMISRenderer
          show={openColumnSizePopup}
          schema={modifyColumnSizePopup(columnSize)}
          onClose={() => setOpenColumnSizePopup(false)}
          onConfirm={(val: any) => modifyColumnSize(val)}
        />
      )}

    </div>
  );
};

export default PageSettings;
