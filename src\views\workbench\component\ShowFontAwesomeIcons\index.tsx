import React, { useEffect, useState, useCallback } from 'react';
import { toast, Spinner } from 'amis';
import './index.scss';
import '@fortawesome/fontawesome-free/css/all.css';

// Font Awesome 图标样式前缀
const iconStyles = ['fas', 'far', 'fab', 'fa'];

const ShowFontAwesomeIcons: React.FC = () => {
  // 状态管理
  const [icons, setIcons] = useState<{[key: string]: string[]}>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [iconColor, setIconColor] = useState('#333333');
  const [collapsedGroups, setCollapsedGroups] = useState<{[key: string]: boolean}>({});
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [currentPages, setCurrentPages] = useState<{[key: string]: number}>({});
  const itemsPerPage = 66;

  // 加载图标数据
  useEffect(() => {
    const loadIcons = async () => {
      try {
        setLoading(true);
        const faIcons: {[key: string]: Set<string>} = {
          'Solid Icons (fas)': new Set(),
          'Regular Icons (far)': new Set(),
          'Brand Icons (fab)': new Set(),
          'Legacy Icons (fa)': new Set()
        };

        await document.fonts.ready;

        const styleSheets = Array.from(document.styleSheets).filter(sheet => {
          try {
            return sheet.cssRules && Array.from(sheet.cssRules).some(rule => 
              rule instanceof CSSStyleRule && rule.selectorText?.includes('fa-')
            );
          } catch {
            return false;
          }
        });

        for (const sheet of styleSheets) {
          try {
            const rules = Array.from(sheet.cssRules || []);
            for (const rule of rules) {
              if (!(rule instanceof CSSStyleRule)) continue;
              
              const selector = rule.selectorText || '';
              if (!selector.includes('fa-')) continue;

              const match = selector.match(/\.(?:(fas|far|fab|fa)\.)?fa-([^:,\s]+)(?:::?before)?/);
              if (!match) continue;

              const style = match[1] || 'fas';
              const iconName = match[2];

              if (iconName && 
                  typeof iconName === 'string' && 
                  !iconName.includes('\\') && 
                  !iconName.includes('/') &&
                  !iconName.includes('.')) {
                const category = style === 'fas' ? 'Solid Icons (fas)' 
                  : style === 'far' ? 'Regular Icons (far)'
                  : style === 'fab' ? 'Brand Icons (fab)'
                  : 'Legacy Icons (fa)';

                if (!faIcons[category].has(iconName)) {
                  const temp = document.createElement('i');
                  temp.style.visibility = 'hidden';
                  temp.className = `${style} fa-${iconName}`;
                  document.body.appendChild(temp);
                  const isValid = window.getComputedStyle(temp, ':before').content !== '';
                  document.body.removeChild(temp);

                  if (isValid) {
                    faIcons[category].add(iconName);
                  }
                }
              }
            }
          } catch (e) {
            console.warn('处理样式表时出错:', e);
          }
        }

        const processedIcons = Object.keys(faIcons).reduce<{[key: string]: string[]}>((acc, category) => {
          acc[category] = Array.from(faIcons[category]).sort();
          return acc;
        }, {});

        setIcons(processedIcons);
        setError(null);
      } catch (e) {
        setError('加载图标失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    loadIcons();
  }, []);

  // 切换分组展开/收起
  const toggleGroup = (category: string) => {
    setCollapsedGroups(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // 监听滚动
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 返回顶部方法
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // 辅助函数
  const getIconPrefix = (category: string) => {
    return category.includes('(fas)') ? 'fas'
      : category.includes('(far)') ? 'far'
      : category.includes('(fab)') ? 'fab'
      : 'fa';
  };

  const handleCopyIcon = (prefix: string, name: string) => {
    const className = `${prefix} fa-${name}`;
    navigator.clipboard.writeText(className).then(() => {
      toast.success(`已复制图标类名: ${className}`);
    }).catch(() => {
      toast.error('复制失败，请重试');
    });
  };

  const filterIcons = (iconNames: string[], searchTerm: string) => {
    return !searchTerm ? iconNames : 
      iconNames.filter(name => name.toLowerCase().includes(searchTerm.toLowerCase()));
  };

  const handlePageChange = (category: string, page: number) => {
    setCurrentPages(prev => ({
      ...prev,
      [category]: page
    }));
  };

  if (loading) {
    return (
      <div className="fontawesome-showcase loading">
        <Spinner size="lg" />
        <p>正在加载图标...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fontawesome-showcase error">
        <h2>出错了</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>重试</button>
      </div>
    );
  }

  return (
    <div className="fontawesome-showcase">
      <h1 className="page-title">Font Awesome 图标库</h1>
      <div className="search-controls">
        <div className="search-box">
          <input
            type="text"
            placeholder="搜索图标..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        {/* <div className="color-picker">
          <span className="color-label">图标颜色：</span>
          <input
            type="color"
            value={iconColor}
            onChange={(e) => setIconColor(e.target.value)}
            className="color-input"
          />
        </div> */}
      </div>

      {Object.entries(icons).map(([category, iconNames]) => {
        const filteredIcons = filterIcons(iconNames, searchTerm);
        const currentPage = currentPages[category] || 1;
        const totalPages = Math.ceil(filteredIcons.length / itemsPerPage);
        const startIndex = (currentPage - 1) * itemsPerPage;
        const paginatedIcons = filteredIcons.slice(startIndex, startIndex + itemsPerPage);
        
        return filteredIcons.length > 0 ? (
          <div key={category} className="icon-group">
            <h2 
              className={`group-title ${collapsedGroups[category] ? 'collapsed' : ''}`}
              onClick={() => toggleGroup(category)}
            >
              <i className={`fas ${collapsedGroups[category] ? 'fa-chevron-right' : 'fa-chevron-down'}`} />
              <span className="title-text">{category}</span>
              <span className="icon-count">({filteredIcons.length})</span>
            </h2>
            <div className={`icon-grid ${collapsedGroups[category] ? 'hidden' : ''}`}>
              {paginatedIcons.map(name => {
                const prefix = getIconPrefix(category);
                return (
                  <div 
                    key={name}
                    className="icon-item"
                    onClick={() => handleCopyIcon(prefix, name)}
                    title={`${prefix} fa-${name}`}
                    style={{ color: iconColor }}
                  >
                    <div className="icon-display">
                      <i className={`${prefix} fa-${name}`} />
                    </div>
                    <div className="icon-name">{name}</div>
                  </div>
                );
              })}
            </div>
            {totalPages > 1 && !collapsedGroups[category] && (
              <div className="pagination">
                <button 
                  onClick={() => handlePageChange(category, currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <i className="fas fa-chevron-left" />
                </button>
                <span>{currentPage} / {totalPages}</span>
                <button 
                  onClick={() => handlePageChange(category, currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <i className="fas fa-chevron-right" />
                </button>
              </div>
            )}
          </div>
        ) : null;
      })}

      <div 
        className={`back-to-top ${showBackToTop ? 'visible' : ''}`}
        onClick={scrollToTop}
        title="返回顶部"
      >
        <i className="fas fa-arrow-up" />
      </div>
    </div>
  );
};

export default ShowFontAwesomeIcons;