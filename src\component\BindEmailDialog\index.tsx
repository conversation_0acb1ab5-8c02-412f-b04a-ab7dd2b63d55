import React from 'react';
import {Modal, Input , Button, toast} from 'amis';
import './index.scss';

interface BindEmailDialogProps {
  show: boolean;
  isRebind: boolean;
  countdown: number;
  currentEmail?: string;
  onClose: () => void;
  onConfirm: (email: string, code: string) => void;
  onSendCode: (email: string) => void;
}

export const BindEmailDialog: React.FC<BindEmailDialogProps> = ({
  show,
  isRebind,
  countdown,
  currentEmail,
  onClose,
  onConfirm,
  onSendCode
}) => {
  const [email, setEmail] = React.useState(currentEmail || '');
  const [code, setCode] = React.useState('');

  React.useEffect(() => {
    setEmail(currentEmail || '');
  }, [currentEmail]);

  const handleSendCode = () => {
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      toast.error('请输入正确的邮箱');
      return;
    }
    onSendCode(email);
  };

  const handleConfirm = () => {
    if (!email) {
      toast.error('请输入邮箱');
      return;
    }

    if (!code) {
      toast.error('请输入验证码');
      return;
    }
    onConfirm(email, code);
  };

  return (
    <Modal
      show={show}
      onClose={onClose}
      closeOnEsc
      closeOnOutside={false}
    >
      <div className="bind-email-dialog">
        <div className="dialog-header">
          <b>{isRebind ? '绑定新邮箱' : '绑定邮箱'}</b>
        </div>
        <div className="dialog-body">
          <div className="form-item">
            <Input
              required
              value={email}
              onChange={e => setEmail(e.target.value)}
              placeholder="请输入邮箱"
              className="form-control"
            />
          </div>
          <div className="form-item code-item">
            <Input
              required
              value={code}
              onChange={e => setCode(e.target.value)}
              placeholder="验证码"
              maxLength={6}
              className="form-control"
            />
            <Button
              className="verify-code-btn"
              disabled={countdown > 0}
              onClick={() => handleSendCode()}
            >
              {countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码'}
            </Button>
          </div>
        </div>
        <div className="dialog-footer">
          <Button
            onClick={onClose}
            className="cancel-btn"
          >
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            level="primary"
            className="confirm-btn"
          >
            确定
          </Button>
        </div>
      </div>
    </Modal>
  );
}; 