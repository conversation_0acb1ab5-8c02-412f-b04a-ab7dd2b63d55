const amisJsonForm: any = {
  // 文本
  'input-text': {
    type: 'input-text',
    label: '标题',
    name: 'text_name',
    __label: false,
    __editorStatethemeCss: {
      inputControlClassName: 'default'
    },
    static: false,
    placeholder: '请输入',
    description: '',
    value: ''
  },
  //   邮箱
  'input-email': {
    type: 'input-email',
    label: '标题',
    name: 'text_name',
    __label: false,
    __editorStatethemeCss: {
      inputControlClassName: 'default'
    },
    static: false,
    placeholder: '请输入',
    description: '',
    value: ''
  },
  //   密码
  'input-password': {
    type: 'input-password',
    label: '标题',
    name: 'text_name',
    __label: false,
    __editorStatethemeCss: {
      inputControlClassName: 'default'
    },
    static: false,
    placeholder: '请输入',
    description: '',
    value: ''
  },
  //   链接
  'input-url': {
    type: 'input-url',
    label: '标题',
    name: 'text_name',
    __label: false,
    __editorStatethemeCss: {
      inputControlClassName: 'default'
    },
    static: false,
    placeholder: '请输入',
    description: '',
    value: ''
  },
  //   数字
  'input-number': {
    type: 'input-number',
    label: '数字',
    name: 'text_name',
    __label: false,
    __editorStatethemeCss: {
      inputControlClassName: 'default'
    },
    static: false,
    placeholder: '请输入',
    description: '',
    value: ''
  },
  //   多行文本
  'textarea': {
    type: 'textarea',
    label: '标题',
    name: 'text_name',
    __label: false,
    minRows: 3,
    maxRows: 20,
    __editorStatethemeCss: {
      inputControlClassName: 'default'
    },
    static: false,
    placeholder: '请输入',
    description: '',
    value: '',
    maxLength: ''
  },
  //   下拉选项
  // options: [
  //     {
  //       label: '选项A',
  //       value: 'A'
  //     }
  //   ],
  'select': {
    type: 'select',
    label: '标题',
    name: 'name_select',
    options: [],
    multiple: false,
    __label: false,
    __editorStatethemeCss: {
      selectControlClassName: 'default',
      selectPopoverClassName: 'default'
    },
    static: false,
    value: ''
  },
  //   级联选择器
  //   options: [
  //     {
  //       label: '选项A',
  //       value: 'A'
  //     },
  //     {
  //       label: '选项B',
  //       value: 'B',
  //       children: [
  //         {
  //           label: '选项b1',
  //           value: 'b1'
  //         },
  //         {
  //           label: '选项b2',
  //           value: 'b2'
  //         }
  //       ]
  //     }
  //   ],
  'nested-select': {
    type: 'nested-select',
    label: '标题',
    name: 'name_nestedSelect',
    onlyChildren: true,
    options: [],
    onlyLeaf: true, //必须选择到末级
    multiple: false,
    __label: false,
    __editorStatethemeCss: {
      nestedSelectControlClassName: 'default',
      nestedSelectPopoverClassName: 'default'
    },
    joinValues: true,
    clearable: true,
    static: false
  },
  //   复选框

  'checkboxes': {
    type: 'checkboxes',
    label: '标题',
    name: 'name_checkboxes',
    multiple: true,
    options: [
      {
        label: '选项A',
        value: 'A'
      },
      {
        label: '选项B',
        value: 'B'
      }
    ],
    checkAll: false,
    joinValues: true,
    __label: false,
    optionType: 'default',
    __editorStatethemeCss: {
      checkboxesControlClassName: 'checkbox-default',
      checkboxesClassName: 'checkbox-default'
    },
    static: false,
    value: 'A|B',
    delimiter: ','
  },
  //   单选
  //   options: [
  //     {
  //       label: '选项A',
  //       value: 'A'
  //     }
  // ]
  'radios': {
    type: 'radios',
    label: '标题',
    name: 'name_radios',
    options: [],
    __label: false,
    optionType: 'default',
    __editorStatethemeCss: {
      radiosControlClassName: 'radios-default',
      radiosClassName: 'radios-default'
    },
    value: '',
    static: false
  },
  //   勾选框
  'checkbox': {
    type: 'checkbox',
    option: '说明（描述）',
    name: 'name_checkbox',
    label: '标题',
    __label: false,
    optionType: 'default',
    __editorStatethemeCss: {
      checkboxControlClassName: 'checkbox-default',
      checkboxClassName: 'checkbox-default'
    },
    value: false,
    static: false
  },
  //   开关
  'switch': {
    type: 'switch',
    label: '标题',
    option: '', //说明（描述）
    name: 'name_switch',
    falseValue: false,
    trueValue: true,
    static: false,
    value: false
  },
  //   日期选择
  'input-date': {
    type: 'input-date',
    label: '日期',
    name: 'date_name',
    valueFormat: 'X', //X秒级，x毫秒级
    value: '',
    static: false,
    displayFormat: 'YYYY-MM-DD'
  },
  //   日期时间选择
  'input-datetime': {
    type: 'input-date',
    label: '日期时间',
    name: 'date_name',
    valueFormat: 'X', //X秒级，x毫秒级
    value: '',
    static: false,
    displayFormat: 'YYYY-MM-DD HH:mm:ss'
  },
  //   时间选择
  'input-time': {
    type: 'input-time',
    label: '时间',
    name: 'time_name',
    valueFormat: 'X', //X秒级，x毫秒级
    value: '',
    static: false,
    displayFormat: 'HH:mm:ss'
  },
  //   月份选择
  'input-month': {
    type: 'input-month',
    label: '月份',
    name: 'month_name',
    valueFormat: 'X', //X秒级，x毫秒级
    value: '',
    static: false,
    displayFormat: 'YYYY-MM'
  },
  //季度选择
  'input-quarter': {
    type: 'input-quarter',
    label: '季度',
    name: 'quarter_name',
    valueFormat: 'X', //X秒级，x毫秒级
    value: '',
    static: false,
    displayFormat: 'YYYY [Q]Q'
  },
  //   选择年
  'input-year': {
    type: 'input-year',
    label: '年',
    name: 'year_name',
    valueFormat: 'X', //X秒级，x毫秒级
    value: '',
    static: false,
    displayFormat: 'YYYY'
  },
  //   日期范围
  'input-date-range': {
    type: 'input-date-range',
    label: '日期范围',
    name: 'range_name',
    value: '',
    static: false,
    valueFormat: 'X'
  },
  //   日期时间范围
  'input-datetime-range': {
    type: 'input-datetime-range',
    label: '日期时间范围',
    name: 'range_name',
    value: '',
    static: false,
    valueFormat: 'X',
    displayFormat: 'YYYY-MM-DD HH:mm:ss',
    placeholder: '请选择日期时间范围',
    shortcuts: [
      //快捷选择字段
      'yesterday',
      '7daysago',
      'prevweek',
      'thismonth',
      'prevmonth',
      'prevquarter'
    ]
  },
  //   月份范围
  'input-month-range': {
    type: 'input-month-range',
    label: '月份范围',
    name: 'range_name',
    value: '',
    static: false,
    valueFormat: 'X',
    displayFormat: 'YYYY-MM',
    placeholder: '请选择月份范围'
  },
  //   季度范围
  'input-quarter-range': {
    type: 'input-quarter-range',
    label: '季度范围',
    name: 'range_name',
    value: '',
    static: false,
    valueFormat: 'X',
    extraName: '',
    displayFormat: 'YYYY [Q]Q',
    placeholder: '请选择季度范围',
    minDate: '',
    maxDate: '',
    shortcuts: ['thisquarter', 'prevquarter']
  },
  //   年范围
  'input-year-range': {
    type: 'input-year-range',
    label: '年范围',
    name: 'range_name',
    value: '',
    static: false,
    valueFormat: 'X',
    extraName: '',
    displayFormat: 'YYYY',
    placeholder: '请选择年范围',
    minDate: '',
    maxDate: '',
    shortcuts: ['thisyear', 'lastYear']
  },
  //   城市选择（默认值格式是行政编码）
  'input-city': {
    type: 'input-city',
    label: '城市选择',
    name: 'city_name',
    allowCity: true,
    extractValue: true, //行政编码
    value: '',
    static: false
  },
  //   评分
  'input-rating': {
    type: 'input-rating',
    label: '评分',
    name: 'rating_1744353096513',
    id: 'u:89163c957ec5',
    allowClear: false,
    half: true,
    colors: {
      2: '#abadb1',
      3: '#787b81',
      5: '#ffa900'
    },
    value: 2,
    count: 5,
    static: false
  },
  //   富文本
  'input-rich-text': {
    type: 'input-rich-text',
    label: '富文本',
    name: 'text_name',
    vendor: 'tinymce',
    options: {
      plugins:
        'advlist,autolink,link,image,lists,charmap,preview,anchor,pagebreak,searchreplace,wordcount,visualblocks,visualchars,code,fullscreen,insertdatetime,media,nonbreaking,table,emoticons,template,help',
      toolbar:
        'undo redo formatselect bold italic backcolor alignleft aligncenter alignright alignjustify bullist numlist outdent indent removeformat help',
      menubar: true
    },
    value: '',
    static: false
  },
  //   图片展示 （用于图片上传的展示）
  'image': {
    type: 'image',
    enlargeAble: true,
    maxScale: 200,
    minScale: 50,
    style: {
      display: 'inline-block'
    },
    __editorStateimageControlClassName: 'default',
    value: ''
  },
  // 图片上传
  'input-image': {
    type: 'input-image',
    label: '图片上传',
    name: 'image_name',
    autoUpload: true,
    proxy: true,
    uploadType: 'fileReceptor',
    imageClassName: 'r w-full',
    accept: '.jpeg, .jpg, .png, .gif',
    multiple: false,
    hideUploadButton: false,
    __editorState: 'default',
    receiver: {
      url: '/app-api/infra/file/upload-zerocode',
      method: 'post'
    }
  },
  //   链接（用于文件上传的展示）
  'link': {
    type: 'link',
    value: '',
    href: '', //访问链接
    body: '', //文本
    blank: true
  },
  // 文件上传
  'input-file': {
    type: 'input-file',
    label: '文件上传',
    autoUpload: true,
    proxy: true,
    uploadType: 'fileReceptor',
    name: 'file_name',
    btnLabel: '文件上传',
    multiple: false,
    useChunk: false,
    drag: false,
    receiver: {
      url: '/app-api/infra/file/upload-zerocode',
      method: 'post'
    }
  },
  //静态展示
  'static': {
    type: 'static',
    name:'',
    label: '静态展示',
    tpl: ''
  }
};
const getAmisJsonFormValue = (key: string) => {
  // 检查 amisJsonForm 中是否存在该键
  if (amisJsonForm[key]) {
    return amisJsonForm[key]; // 返回对应的对象
  }
  return false; // 如果不存在，返回 false
};

export {getAmisJsonFormValue};
