/* 导航项设置按钮样式 */
.nav-item-settings-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 6px !important;
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  // background: rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  position: relative !important;
  vertical-align: middle !important;
}

/* 访问模式下隐藏设置按钮 */
[data-playtype="workbench"] .nav-item-settings-btn {
  display: none !important;
}

/* 访问模式下隐藏导航头部 */
[data-playtype="workbench"] .nav-header {
  display: none !important;
}

[data-playtype="workbench"] .my-nav-wrapper .nav-header {
  display: none !important;
}

/* 访问模式下优化搜索框样式 */
[data-playtype="workbench"] .cxd-Nav-searchbox,
[data-playtype="workbench"] .cxd-SearchBox {
  margin-bottom: 12px !important;
  padding: 8px 12px !important;
}

[data-playtype="workbench"] .page-nav-search {
  margin-bottom: 12px !important;
}

/* 图标与文字对齐样式 */
.cxd-Nav-Menu-item-icon,
.cxd-Nav-Menu-item .cxd-Nav-Menu-item-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  margin-right: 8px !important;
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
}

/* 确保图标内的元素也对齐 */
.cxd-Nav-Menu-item-icon i,
.cxd-Nav-Menu-item-icon span,
.cxd-Nav-Menu-item-icon svg {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  line-height: 1 !important;
}

/* 菜单项标签对齐 */
.cxd-Nav-Menu-item-label {
  display: inline-flex !important;
  align-items: center !important;
  line-height: 1.4 !important;
  vertical-align: middle !important;
}

/* 拖拽按钮定位 - 绝对定位在右侧 */
/* 一级分类的拖拽按钮位置 */
.cxd-Nav-Menu-submenu .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu-title .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu [class*="dragBtn"],
.cxd-Nav-Menu-submenu [class*="drag-btn"],
.cxd-Nav-Menu-submenu .drag-handle,
.cxd-Nav-Menu-submenu [class*="drag"] {
  position: absolute !important;
  right: 28px !important; /* 一级分类拖拽按钮移到右侧，与设置按钮对齐 */
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  z-index: 1 !important;
}

/* 二级页面的拖拽按钮位置 - 针对submenu内部的页面项 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item-dragBtn,
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item-wrap .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item [class*="dragBtn"],
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item [class*="drag-btn"],
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item .drag-handle,
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item [class*="drag"] {
  position: absolute !important;
  right: 28px !important; /* 二级页面拖拽按钮移到右侧，与设置按钮对齐 */
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  z-index: 1 !important;
}

/* 一级页面和没有内容的分类的拖拽按钮位置 */
.cxd-Nav-Menu-item .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-item-dragBtn,
.cxd-Nav-Menu-item [class*="dragBtn"],
.cxd-Nav-Menu-item [class*="drag-btn"],
.cxd-Nav-Menu-item .drag-handle,
.cxd-Nav-Menu-item [class*="drag"] {
  position: absolute !important;
  right: 28px !important; /* 一级项目拖拽按钮移到右侧，与设置按钮对齐 */
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  z-index: 1 !important;
}

/* 其他通用拖拽按钮样式 - 使用更高优先级的选择器 */
.cxd-ListItem-dragBtn,
.cxd-Nav .cxd-ListItem-dragBtn,
.cxd-Nav-Menu .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-item .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu-title .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-item-wrap .cxd-ListItem-dragBtn {
  position: absolute !important;
  right: 28px !important; /* 通用拖拽按钮移到右侧，与设置按钮对齐 */
  left: auto !important; /* 覆盖默认的left定位 */
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  z-index: 1 !important;
}

/* 强制覆盖amis默认的拖拽按钮样式 - 最高优先级 */
.cxd-Nav .cxd-Nav-Menu .cxd-ListItem-dragBtn,
.cxd-Nav .cxd-Nav-Menu-item .cxd-ListItem-dragBtn,
.cxd-Nav .cxd-Nav-Menu-submenu .cxd-ListItem-dragBtn,
.cxd-Nav .cxd-Nav-Menu-submenu-title .cxd-ListItem-dragBtn,
.cxd-Nav .cxd-Nav-Menu-item-wrap .cxd-ListItem-dragBtn,
.cxd-Nav .cxd-Nav-Menu-item-link .cxd-ListItem-dragBtn {
  position: absolute !important;
  right: 28px !important; /* 与设置按钮对齐 */
  left: auto !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  z-index: 10 !important;
}

/* 全局拖拽按钮右侧定位 - 覆盖所有可能的amis默认样式 */
[class*="cxd-ListItem-dragBtn"],
[class*="dragBtn"],
[class*="drag-btn"],
.drag-handle,
[class*="drag"] {
  &:not(.drag-preview):not(.drag-source):not(.drag-target) {
    position: absolute !important;
    right: 30px !important;
    left: auto !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    margin: 0 !important;
    z-index: 10 !important;
  }
}

/* 最终覆盖规则 - 确保所有拖拽按钮都在右侧 */
body .cxd-Nav .cxd-ListItem-dragBtn,
body .cxd-Nav-Menu .cxd-ListItem-dragBtn,
body .cxd-Nav-Menu-item .cxd-ListItem-dragBtn,
body .cxd-Nav-Menu-submenu .cxd-ListItem-dragBtn,
body .cxd-Nav-Menu-submenu-title .cxd-ListItem-dragBtn,
body .cxd-Nav-Menu-item-wrap .cxd-ListItem-dragBtn,
body .cxd-Nav-Menu-item-link .cxd-ListItem-dragBtn,
body [class*="cxd-ListItem-dragBtn"] {
  position: absolute !important;
  right: 28px !important; /* 调整位置，与设置按钮更好对齐 */
  left: auto !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  z-index: 10 !important;
  width: 16px !important; /* 设置固定宽度 */
  height: 16px !important; /* 设置固定高度 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 精确对齐拖拽按钮和设置按钮 */
.cxd-Nav-Menu-item:hover .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu:hover .cxd-ListItem-dragBtn {
  opacity: 0.7 !important; /* 悬浮时显示拖拽按钮 */
}

.cxd-Nav-Menu-item .cxd-ListItem-dragBtn,
.cxd-Nav-Menu-submenu .cxd-ListItem-dragBtn {
  opacity: 0 !important; /* 默认隐藏拖拽按钮 */
  transition: opacity 0.2s ease !important;
}

/* 确保设置按钮在导航项中垂直居中 */
.cxd-Nav-Menu-item .nav-item-settings-btn {
  position: absolute !important;
  right: 6px !important; /* 调整位置，与拖拽按钮更好对齐 */
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* 分类项中的设置按钮定位 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-submenu-title .nav-item-settings-btn {
  position: absolute !important;
  right: 6px !important; /* 调整位置，与拖拽按钮更好对齐 */
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* 导航项悬浮时显示设置按钮 */
.cxd-Nav-Menu-item:hover .nav-item-settings-btn {
  opacity: 1;
}

/* 分类项悬浮时显示设置按钮 */
.cxd-Nav-Menu-submenu:hover .nav-item-settings-btn {
  opacity: 1 !important;
}

.cxd-Nav-Menu-submenu .cxd-Nav-Menu-submenu-title:hover .nav-item-settings-btn {
  opacity: 1 !important;
}

/* 确保分类项（submenu）有足够的左右边距 - 为拖拽按钮预留空间 */
.cxd-Nav-Menu-submenu {
  position: relative !important;
}

/* 一级分类的样式 - 应用到submenu-title */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-submenu-title {
  position: relative !important;
  padding-left: 25px !important; /* 一级分类左侧边距 */
  padding-right: 35px !important; /* 增加右侧边距，为拖拽按钮和设置按钮预留空间 */
}

/* 一级分类内部的item-wrap样式 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-wrap {
  padding-left: 0 !important; /* 重置内部padding，由父级控制 */
}

/* 一级分类内部的item-link样式 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link {
  padding-left: 0 !important; /* 重置内部padding，由父级控制 */
}

/* 首先设置所有一级项目的基础缩进 - 包括一级页面和没有内容的分类 */
.cxd-Nav-Menu-item {
  position: relative !important;
  padding-right: 35px !important; /* 增加右侧边距，为拖拽按钮和设置按钮预留空间 */
  padding-left: 25px !important; /* 一级项目的基础缩进，与有内容的分类对齐 */
}

/* 然后专门设置二级页面项的缩进 - 针对submenu内部的页面项 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item {
  position: relative !important;
  padding-right: 35px !important; /* 增加右侧边距，为拖拽按钮和设置按钮预留空间 */
  /* 为拖拽按钮预留空间，二级页面项需要额外的左侧边距 */
  padding-left: 50px !important; /* 二级页面相对于一级分类增加35px缩进 */
  margin-left: 0 !important; /* 重置margin，确保只使用padding控制缩进 */
}

/* 更高优先级的选择器，确保二级页面缩进生效 */
.cxd-Nav .cxd-Nav-Menu .cxd-Nav-Menu-submenu .cxd-Nav-Menu-item {
  padding-left: 50px !important; /* 增加二级页面缩进到60px，相对于一级分类的25px增加35px */
  /* 强制重置所有可能影响缩进的属性 */
  margin-left: 0 !important;
  text-indent: 0 !important;
  /* 确保内部元素不会重置padding */
  box-sizing: border-box !important;
}

/* 使用更强的CSS优先级 - 多重选择器 */
.cxd-Nav .cxd-Nav-Menu .cxd-Nav-Menu-submenu .cxd-Nav-Menu-item.cxd-Nav-Menu-item {
  padding-left: 50px !important; /* 双重强制设置 */
}

/* 移除过于宽泛的属性选择器，避免影响所有菜单项 */

/* 移除可能影响显示的通用选择器 */

/* 二级页面内部的item-wrap样式 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item .cxd-Nav-Menu-item-wrap {
  padding-left: 0 !important; /* 重置内部padding，由父级控制 */
  margin-left: 0 !important; /* 重置内部margin */
}

/* 二级页面内部的item-link样式 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link {
  padding-left: 0 !important; /* 重置内部padding，由父级控制 */
  margin-left: 0 !important; /* 重置内部margin */
}

/* 导航项链接样式调整 - 为拖拽按钮预留空间 */
/* 注释：链接的padding由父级.cxd-Nav-Menu-item控制 */

/* 方案1：完全隐藏展开图标，但保留点击区域功能 */
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-submenu-arrow,
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item-arrow,
.cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-submenu-arrow,
.cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-arrow,
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-submenu-caret,
.cxd-Nav-Menu-submenu .cxd-Nav-Menu-item-caret,
.cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-submenu-caret,
.cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-caret,
.cxd-Nav-Menu-submenu .fa-angle-right,
.cxd-Nav-Menu-submenu .fa-angle-down,
.cxd-Nav-Menu-submenu .fa-caret-right,
.cxd-Nav-Menu-submenu .fa-caret-down,
.cxd-Nav-Menu-submenu-title .fa-angle-right,
.cxd-Nav-Menu-submenu-title .fa-angle-down,
.cxd-Nav-Menu-submenu-title .fa-caret-right,
.cxd-Nav-Menu-submenu-title .fa-caret-down {
  visibility: hidden !important; /* 使用visibility而不是display，保留点击区域 */
  width: 0 !important; /* 不占用空间 */
  margin: 0 !important;
  padding: 0 !important;
}

/* 方案2：让整个分类标题都可以点击展开，而不只是图标 */
.cxd-Nav-Menu-submenu-title {
  cursor: pointer !important;
  user-select: none !important;
}

/* 设置按钮悬浮效果 */
.nav-item-settings-btn:hover {
  background: rgba(0, 0, 0, 0.2) !important;
  transform: translateY(-50%) scale(1.1) !important;
}

/* 设置按钮图标样式 */
.nav-item-settings-btn .fa {
  font-size: 12px;
  color: #666;
}

/* 下拉菜单样式 */
.nav-item-settings-btn .cxd-DropDown-menu {
  min-width: 120px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  padding: 8px 0;
  background: white;
}

/* 下拉菜单项样式 */
.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item {
  justify-content: flex-start !important;
  padding: 12px 16px !important;
  border-radius: 0 !important;
  border: none !important;
  background: transparent !important;
  color: #333 !important;
  font-size: 14px !important;
  width: 100% !important;
  text-align: left !important;
  font-weight: normal !important;
  line-height: 1.4 !important;
}

.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item:hover {
  background: #f5f5f5 !important;
  color: #333 !important;
}

/* 删除按钮特殊样式 */
.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item-danger {
  background: #ff4d4f !important;
  color: white !important;
  margin: 4px 8px 0 8px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
}

.nav-item-settings-btn .cxd-DropDown-menu .nav-menu-item-danger:hover {
  background: #ff7875 !important;
  color: white !important;
}


/* 固定展开按钮样式 */
.fixed-expand-btn {
  position: absolute !important;
  top: 10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  box-shadow: none !important;

  &:hover {
    background: transparent !important;
    background-color: transparent !important;
  }

  &:focus {
    background: transparent !important;
    background-color: transparent !important;
    outline: none !important;
  }

  &:active {
    background: transparent !important;
    background-color: transparent !important;
  }
}

/* 固定展开按钮中的 SVG 样式 */
.fixed-expand-btn svg {
  width: 16px !important;
  height: 16px !important;
  stroke: currentColor !important;
  fill: none !important;
  transition: all 0.2s ease !important;
}

/* 右键菜单样式增强 - 与 index.scss 中的样式配合使用 */
.customContextMenu {
  z-index: 999999 !important; /* 确保最高优先级，覆盖 index.scss 中的 99999 */
}
