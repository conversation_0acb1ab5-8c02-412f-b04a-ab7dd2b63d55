import React, {FC, useState, useEffect, useCallback, useRef} from 'react';
import './index.scss';
import AMISRenderer from '@/component/AMISRenderer';
import {
  getPublishApplicationGroup,
  getPublishApplicationGroupList,
  updatePublishApplicationGroup
} from '@/utils/api/api';
import {inject, observer} from 'mobx-react';
import {IMainStore} from '@/store';

interface WorkbenchViewProps {
  history: any;
  store?: IMainStore;
}

const WorkbenchView: FC<WorkbenchViewProps> = inject('store')(
  observer((props: WorkbenchViewProps) => {
    const [applications, setApplications] = useState<any[]>([]);
    const selectGroupApplications = useRef<any[]>([]);
    const [applicationGroups, setApplicationGroups] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [schema, setSchema] = useState<any>({});
    const [activeGroupId, setActiveGroupId] = useState<string>('');

    //获取已发布应用分组列表数据
    const fetchApplicationGroups = useCallback(async () => {
      try {
        const res = await getPublishApplicationGroupList({
          userId: props?.store?.userInfo.id
        });
        if (res.code === 0 && res.data) {
          setApplicationGroups(res.data);
          // 默认使用全部应用作为初始标签
          if (res.data.length > 0) {
            setActiveGroupId(res.data[0].id);
          }
        } else {
          setApplicationGroups([]);
        }
      } catch (error) {
        setApplicationGroups([]);
      }
    }, [activeGroupId]);

    // 处理应用拖拽排序函数
    const handleAppSort = useCallback(
      async (fromIndex: number, toIndex: number) => {
        if (fromIndex === toIndex) {
          return;
        }

        try {
          // 获取当前应用列表和分组信息
          const appList = (window as any).currentAppList || [];
          const groupInfo = (window as any).currentGroupInfo;

          if (!groupInfo) {
            return;
          }

          if (!appList || appList.length === 0) {
            return;
          }

          // 重新排序应用列表
          const newAppList = [...appList];
          const [movedApp] = newAppList.splice(fromIndex, 1);
          newAppList.splice(toIndex, 0, movedApp);

          // 构造更新数据
          const updateData = {
            id: groupInfo.id,
            name: groupInfo.name,
            userIds: groupInfo.userIds || '',
            applicationIds: newAppList
              .map((app: any) => app.applicationId)
              .join(','),
            sort: groupInfo.sort || 0
          };

          // 调用更新接口
          const result = await updatePublishApplicationGroup(updateData);

          if (result.code !== 0) {
            return;
          }

          // 更新全局应用列表
          (window as any).currentAppList = newAppList;

          // 更新 window.currentGroupInfo 中的应用列表
          if ((window as any).currentGroupInfo) {
            (window as any).currentGroupInfo.applicationIds = newAppList
              .map((app: any) => app.applicationId)
              .join(',');
          }

          // 更新 localStorage 中的应用列表，确保编辑分组中的应用显示也会更新
          const currentApplicationList = JSON.parse(
            localStorage.getItem('applicationList') || '[]'
          );

          if (currentApplicationList && currentApplicationList.length > 0) {
            // 更新 localStorage 中的应用列表的排序
            const updatedApplicationList = currentApplicationList.map(
              (app: any) => {
                const newAppIndex = newAppList.findIndex(
                  (newApp: any) => newApp.applicationId === app.applicationId
                );
                if (newAppIndex !== -1) {
                  return {
                    ...app,
                    sort: newAppIndex
                  };
                }
                return app;
              }
            );

            localStorage.setItem(
              'applicationList',
              JSON.stringify(updatedApplicationList)
            );
          }

          // 刷新分组数据
          setTimeout(() => {
            // 刷新主页面的分组数据
            if ((window as any).refreshGroups) {
              (window as any).refreshGroups(result.data);
            }
          }, 0);
        } catch (error) {
          console.log('应用排序 - 更新过程中出错:', error);
        }
      },
      []
    );

    // 将函数暴露到全局，供拖拽事件调用
    useEffect(() => {
      (window as any).handleAppSort = handleAppSort;
      return () => {
        delete (window as any).handleAppSort;
      };
    }, [handleAppSort]);

    // 获取移动到分组抽屉的 schema
    const getMoveToGroupDrawerSchema = () => {
      return {
        title: '分组列表',
        size: 'md',
        className: 'move-to-group-drawer', // 添加特定的类名用于识别
        body: {
          type: 'service',
          api: {
            method: 'get',
            url: `/admin-api/system/publish-application-groups/list`,
            adaptor: `
              if (payload.code === 0) {
                // 获取当前分组ID
                const currentGroupInfo = window.currentGroupInfo || {};
                const currentGroupId = currentGroupInfo.id;

                // 过滤掉当前分组
                const otherGroups = (payload.data || []).filter(group => group.id !== currentGroupId);

                return {
                  status: 0,
                  msg: 'success',
                  data: {
                    groups: otherGroups
                  }
                };
              }

              return {
                status: 1,
                msg: payload.msg || '获取分组列表失败',
                data: { groups: [] }
              };
            `
          },
          body: [
            {
              type: 'each',
              name: 'groups',
              items: {
                type: 'container',
                className: 'group-item-container',
                wrapperComponent: '',
                style: {
                  padding: 0,
                  margin: 0,
                  border: 'none'
                },
                body: [
                  {
                    type: 'button',
                    label: '${name}',
                    level: 'link',
                    className: 'group-select-btn',
                    style: {
                      width: '100%',
                      textAlign: 'left',
                      border: 'none',
                      background: 'white',
                      borderRadius: 0
                    },
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'custom',
                            script: `
                              const targetGroupId = event.data.id;
                              const targetGroupName = event.data.name;
                              const applicationId = window.currentMoveApplicationId;

                              // 存储移动参数到全局变量，供确认对话框使用
                              window.pendingMoveParams = {
                                applicationId,
                                targetGroupId,
                                targetGroupName
                              };
                            `
                          },
                          {
                            actionType: 'dialog',
                            dialog: {
                              type: 'dialog',
                              title: '确认移动',
                              body: {
                                type: 'tpl',
                                tpl: '该应用将从原分组移动到你选定的新分组'
                              },
                              actions: [
                                {
                                  type: 'button',
                                  label: '取消',
                                  actionType: 'cancel'
                                },
                                {
                                  type: 'button',
                                  label: '确定',
                                  level: 'primary',
                                  onEvent: {
                                    click: {
                                      actions: [
                                        {
                                          actionType: 'custom',
                                          script: `
                                            // 从全局变量获取移动参数（在点击分组时已存储）
                                            const moveParams = window.pendingMoveParams || {};
                                            const targetGroupId = moveParams.targetGroupId;
                                            const targetGroupName = moveParams.targetGroupName;
                                            const applicationId = window.currentMoveApplicationId;

                                            if (applicationId && targetGroupId && targetGroupName) {
                                              // 调用移动应用的函数
                                              window.moveApplicationToGroup(applicationId, targetGroupId, targetGroupName);
                                              setTimeout(() => {
                                                doAction({
                                                  actionType: 'reload',
                                                  componentId: 'editGroupAppsDisplay'
                                                });
                                              }, 1000);
                                              // 先关闭确认对话框，然后关闭分组列表抽屉
                                              setTimeout(() => {
                                                try {
                                                  // 查找确认对话框并关闭
                                                  const confirmDialog = document.querySelector('.cxd-Modal[role="dialog"]');
                                                  if (confirmDialog) {
                                                    const closeBtn = confirmDialog.querySelector('.cxd-Modal-close');
                                                    if (closeBtn) {
                                                      closeBtn.click();

                                                      // 确认对话框关闭后，再关闭分组列表抽屉
                                                      setTimeout(() => {
                                                        window.closeMoveToGroupDrawer();
                                                     
                                                      }, 200);
                                                    }
                                                  } else {
                                                    // 如果没找到确认对话框，直接关闭分组列表抽屉
                                                    window.closeMoveToGroupDrawer();
                                                  }
                                                } catch (error) {
                                                  // 出错时也要尝试关闭分组列表抽屉
                                                  window.closeMoveToGroupDrawer();
                                                }
                                              }, 100);
                                            }
                                          `
                                        }
                                      ]
                                    }
                                  }
                                }
                              ]
                            },
                            data: {
                              targetGroupId: '${id}',
                              targetGroupName: '${name}'
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            }
          ]
        },
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'close',
            className: 'dialog-cancel-btn'
          }
        ]
      };
    };

    // 获取应用排序抽屉的 schema
    const getAppSortDrawerSchema = (groupId: string) => {
      return {
        title: '应用排序',
        size: 'md',
        body: {
          type: 'service',
          id: 'app-sort-service',
          api: {
            method: 'get',
            url: `/admin-api/system/publish-application-groups/get?id=${groupId}`,
            adaptor: `
              if (payload.code === 0 && payload.data && payload.data.applicationList) {
                // 存储分组信息到全局变量，供排序保存时使用
                // 注意：保留已存在的 applicationIds，避免覆盖排序后的数据
                const existingApplicationIds = window.currentGroupInfo && window.currentGroupInfo.applicationIds
                  ? window.currentGroupInfo.applicationIds
                  : (payload.data.applicationList || []).map(app => app.applicationId).join(',');

                window.currentGroupInfo = {
                  id: payload.data.id,
                  name: payload.data.name,
                  userIds: payload.data.userIds,
                  sort: payload.data.sort,
                  applicationIds: existingApplicationIds
                };

                // 按 sort 字段排序应用列表
                const apps = payload.data.applicationList.map((app, index) => ({
                  applicationId: app.applicationId,
                  applicationName: app.applicationName,
                  applicationLogo: app.applicationLogo,
                  sort: app.sort || index
                }));

                // 按 sort 字段排序
                apps.sort((a, b) => (a.sort || 0) - (b.sort || 0));

                // 存储应用列表到全局变量
                window.currentAppList = apps;

                return {
                  status: 0,
                  msg: 'success',
                  data: {
                    applications: apps
                  }
                };
              }

              return {
                status: payload.code === 0 ? 0 : 1,
                msg: payload.msg,
                data: {
                  applications: []
                }
              };
            `
          },
          body: {
            type: 'crud',
            mode: 'table',
            draggable: true,
            api: {
              method: 'get',
              url: `/admin-api/system/publish-application-groups/get?id=${groupId}`,
              adaptor: `
                if (payload.code === 0 && payload.data && payload.data.applicationList) {
                  // 存储分组信息到全局变量，供排序保存时使用
                  // 注意：保留已存在的 applicationIds，避免覆盖排序后的数据
                  const existingApplicationIds = window.currentGroupInfo && window.currentGroupInfo.applicationIds
                    ? window.currentGroupInfo.applicationIds
                    : (payload.data.applicationList || []).map(app => app.applicationId).join(',');

                  window.currentGroupInfo = {
                    id: payload.data.id,
                    name: payload.data.name,
                    userIds: payload.data.userIds,
                    sort: payload.data.sort,
                    applicationIds: existingApplicationIds
                  };

                  // 按 sort 字段排序应用列表
                  const apps = payload.data.applicationList.map((app, index) => ({
                    id: app.applicationId,
                    applicationId: app.applicationId,
                    applicationName: app.applicationName,
                    applicationLogo: app.applicationLogo,
                    sort: app.sort || index
                  }));

                  // 按 sort 字段排序
                  apps.sort((a, b) => (a.sort || 0) - (b.sort || 0));

                  // 存储应用列表到全局变量
                  window.currentAppList = apps;

                  return {
                    status: 0,
                    msg: 'success',
                    data: {
                      items: apps,
                      total: apps.length
                    }
                  };
                }

                return {
                  status: payload.code === 0 ? 0 : 1,
                  msg: payload.msg,
                  data: {
                    items: [],
                    total: 0
                  }
                };
              `
            },
            saveOrderApi: {
              url: '/admin-api/system/publish-application-groups/update',
              method: 'put',
              requestAdaptor: `
                // 获取当前分组信息
                const groupInfo = window.currentGroupInfo || {};
                // 从排序后的数据中提取应用ID列表
                let sortedAppIds = [];
                if (api.data.ids) {
                  if (typeof api.data.ids === 'string') {
                    sortedAppIds = api.data.ids.split(',').filter(id => id.trim());
                  } else if (Array.isArray(api.data.ids)) {
                    sortedAppIds = api.data.ids;
                  }
                }

                // 构造更新数据
                const updateData = {
                  id: groupInfo.id,
                  name: groupInfo.name || '',
                  userIds: groupInfo.userIds || '',
                  applicationIds: sortedAppIds.join(','),
                  sort: groupInfo.sort || 0
                };

                // 更新全局分组信息中的应用ID列表
                if (window.currentGroupInfo) {
                  window.currentGroupInfo.applicationIds = sortedAppIds.join(',');
                }

                // 替换请求数据
                api.data = updateData;

                return api;
              `,
              adaptor: `
                if (payload.code === 0) {
                  // 获取当前分组信息和排序后的应用ID列表
                  const groupInfo = window.currentGroupInfo || {};
                  const sortedAppIds = groupInfo.applicationIds ? groupInfo.applicationIds.split(',') : [];

                  // 调用React函数来刷新编辑分组应用显示
                  if (window.refreshEditGroupAppsAfterSort && sortedAppIds.length > 0) {
                    window.refreshEditGroupAppsAfterSort(sortedAppIds);
                  } else {
                    console.log('应用排序 - React刷新函数不可用或应用ID列表为空');
                  }
                }

                // 返回成功状态，但通过其他方式防止抽屉关闭
                return {
                  status: payload.code === 0 ? 0 : 1,
                  msg: payload.code === 0 ? '应用排序已保存' : payload.msg,
                  data: payload.data || {}
                };
              `
            },
            headerToolbar: [],
            footerToolbar: [],
            syncLocation: false,
            perPage: 100,
            loadDataOnce: true,
            columns: [
              {
                name: 'applicationLogo',
                label: '图标',
                type: 'tpl',
                width: 80,
                tpl: `
                  <div class="app-sort-icon-wrapper">
                    \${IF(applicationLogo,
                      IF(STARTSWITH(applicationLogo, 'http'),
                        '<img src="' + applicationLogo + '" class="app-sort-icon" alt="' + applicationName + '" />',
                        '<div class="app-sort-icon">' + applicationLogo + '</div>'
                      ),
                      '<div class="app-sort-icon app-sort-default-icon"></div>'
                    )}
                  </div>
                `
              },
              {
                name: 'applicationName',
                label: '应用名称',
                type: 'text'
              }
            ],
            placeholder: '暂无应用数据'
          }
        },
        actions: [
          {
            type: 'button',
            label: '完成',
            level: 'primary',
            actionType: 'close',
            className: 'confirm-btn'
          }
        ]
      };
    };

    const getEditGroupDrawerSchema = (id: any) => {
      return {
        title: '编辑分组',
        size: 'md',
        closeOnEsc: false,
        closeOnOutside: false,
        showCloseButton: false,
        resizable: false,
        body: {
          type: 'form',
          id: `edit-group-form`,
          wrapWithPanel: false,
          className: 'group-edit-form',
          mode: 'normal',
          submitText: '',
          initApi: {
            method: 'get',
            url: `/admin-api/system/publish-application-groups/get?id=${id}`,
            adaptor: `
  // 清理所有编辑分组相关的全局标志位
  window.editGroupDataReady = false;

  // 清理 localStorage 中可能残留的数据
  localStorage.removeItem('applicationList');
  localStorage.removeItem('applicationAddList');

  if (payload.code == 0) {
    payload.data.useScope = payload.data.userIds === 'all' ? 'all' : 'part';

    // ✅ 设置当前分组信息到全局变量，供移动应用功能使用
    window.currentGroupInfo = {
      id: payload.data.id,
      name: payload.data.name,
      userIds: payload.data.userIds,
      applicationIds: (payload.data.applicationList || []).map(app => app.applicationId).join(','),
      sort: payload.data.sort
    };

    // ✅ 重新设置 localStorage 数据
    localStorage.setItem('applicationList', JSON.stringify(payload.data.applicationList || []));

    // ✅ 编辑分组时，初始化已选应用列表为当前分组的应用ID数组
    const currentAppIds = (payload.data.applicationList || []).map(app => app.applicationId);
    localStorage.setItem('applicationAddList', JSON.stringify(currentAppIds));

    window.setSelectGroup(payload.data);

    // 设置数据准备完成标志位
    window.editGroupDataReady = true;
  }

  return {
    status: payload.code === 0 ? 0 : 1,
    msg: payload.msg,
    data: payload.data
  };
`
          },
          api: {
            url: '/admin-api/system/publish-application-groups/update',
            method: 'put',
            adaptor: `
  if (payload.code === 0) {
    // 调用通用刷新函数
    window.refreshGroups(payload.data);

    // 直接刷新分组管理CRUD（确保分组管理界面更新）
    setTimeout(() => {
      try {
        // 通过DOM事件刷新（备用方案）
        const groupManagerCrud = document.querySelector('#workbench-group-manager');
        if (groupManagerCrud) {
          const event = new CustomEvent('reload', { bubbles: true });
          groupManagerCrud.dispatchEvent(event);
        }
      } catch (error) {
        console.log('编辑分组：刷新分组管理CRUD时出错:', error);
      }
    }, 500);
  }
  return {
    status: payload.code === 0 ? 0 : 1,
    msg: payload.msg,
    data: payload.data
  };
`,
            requestAdaptor: `
            // 根据 pageType 处理不同字段
            api.data.id = api.body.id;
            api.data.sort = api.body.sort||0;

            // 获取当前分组的应用ID列表
            // 优先使用 window.currentGroupInfo.applicationIds，这是最新的数据
            let applicationIds = '';
            if (window.currentGroupInfo && window.currentGroupInfo.applicationIds) {
              applicationIds = window.currentGroupInfo.applicationIds;
            } else {
              // 备用方案：从 localStorage 获取
              const selectedAppIds = JSON.parse(localStorage.getItem('applicationAddList') || '[]');
              applicationIds = selectedAppIds.join(',');
            }
            api.data.applicationIds = applicationIds;

            if (api.data.useScope === 'all') {
              api.data.userIds = api.data.useScope;
              delete api.data.useScope;
            } else {
              delete api.data.useScope;
            }
            return api;
            `
          },
          body: [
            {
              type: 'input-text',
              name: 'name',
              label: '分组名称',
              maxLength: 12,
              required: true,
              placeholder: '请输入',
              clearable: true,
              inputClassName: 'group-name-input'
            },
            {
              type: 'select',
              name: 'useScope',
              label: '使用范围',
              required: true,
              value: 'all',
              placeholder: '请选择',
              clearable: false,
              options: [
                {
                  label: '全部成员',
                  value: 'all'
                },
                {
                  label: '部分成员',
                  value: 'part'
                }
              ],
              onEvent: {
                change: {
                  actions: [
                    {
                      actionType: 'dialog',
                      dialog: {
                        type: 'dialog',
                        title: '请选择',
                        body: [
                          {
                            label: '',
                            type: 'transfer',
                            name: 'transfer',
                            selectMode: 'tree',
                            resultListModeFollowSelect: false,
                            id: 'u:d1646dc0a71c',
                            onlyChildren: true,
                            autoCheckChildren: true,
                            source: '/admin-api/system/user/list-dept-user',
                            labelField: 'name',
                            valueField: 'id',
                            searchable: true,
                            statistics: true
                          }
                        ],
                        actions: [
                          {
                            type: 'button',
                            actionType: 'cancel',
                            label: '取消',
                            id: 'u:d2f9d50b6428'
                          },
                          {
                            type: 'button',
                            label: '确定',
                            close: true,
                            primary: true,
                            actionType: 'custom',
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'setValue',
                                    componentId: `edit-group-form`,
                                    args: {
                                      value: {
                                        userIds: '${transfer}'
                                      }
                                    }
                                  },
                                  {
                                    actionType: 'close'
                                  }
                                ]
                              }
                            }
                          }
                        ],
                        size: 'md'
                      },
                      expression: "this.value === 'part'"
                    }
                  ]
                }
              }
            },
            {
              visibleOn: "${useScope === 'part'}",
              type: 'user-multi-select',
              static: true,
              name: 'userIds',
              label: '已选成员',
              placeholder: '请选择'
            },

            {
              type: 'tpl',
              name: 'applications',
              label: '应用',
              tpl: JSON.parse(localStorage.getItem('applicationList') || '[]')
            },

            {
              type: 'container',
              className: 'group-apps-container',
              label: false,
              body: [
                {
                  type: 'flex',
                  direction: 'column',
                  items: [
                    {
                      type: 'tpl',
                      tpl: '应用',
                      className: 'group-apps-label'
                    },
                    {
                      type: 'container',
                      className: 'workbench-apps-grid',
                      body: [
                        // 显示当前分组的应用（动态过滤）
                        {
                          type: 'service',
                          id: 'editGroupAppsDisplay',
                          initFetch: true, // 允许初始加载，通过 adaptor 控制返回结果
                          // 添加轮询机制，定期检查数据变化
                          interval: 1000, // 每1秒检查一次
                          silentPolling: true, // 静默轮询，不显示加载状态
                          stopAutoRefreshWhen: 'false', // 始终保持轮询
                          api: {
                            method: 'get',
                            url: '/admin-api/system/application/page',
                            data: {
                              pageNo: 1,
                              pageSize: 100,
                              isRelease: 1
                            },

                            adaptor: `
                              // 获取当前分组信息
                              const currentGroupInfo = window.currentGroupInfo || {};

                              // 如果数据还没准备好，返回空结果
                              if (!window.editGroupDataReady || !currentGroupInfo.id) {
                                return {
                                  status: 0,
                                  msg: 'success',
                                  data: {
                                    filteredApps: []
                                  }
                                };
                              }

                              // 从当前分组信息中获取应用ID列表
                              const groupAppIds = currentGroupInfo.applicationIds
                                ? currentGroupInfo.applicationIds.split(',').filter(id => id.trim())
                                : [];

                              // 检查数据是否发生变化（用于轮询检测）
                              const currentAppIdsStr = groupAppIds.join(',');
                              if (window.lastEditGroupAppIds !== currentAppIdsStr) {
                                window.lastEditGroupAppIds = currentAppIdsStr;
                              }

                              // 获取原始应用列表
                              const originalAppList = JSON.parse(localStorage.getItem('applicationList') || '[]');

                              // 创建应用映射，用于快速查找
                              const appMap = new Map();
                              originalAppList.forEach(app => {
                                const appId = app.applicationId || app.id;
                                appMap.set(appId.toString(), app);
                              });

                              // 如果原始应用列表中没有某些选中的应用，从API响应中获取
                              if (payload.code === 0 && payload.data && payload.data.list) {
                                payload.data.list.forEach(app => {
                                  const appId = app.id || app.applicationId;
                                  if (!appMap.has(appId.toString())) {
                                    appMap.set(appId.toString(), {
                                      applicationId: appId,
                                      applicationName: app.name || app.applicationName,
                                      applicationLogo: app.logo || app.applicationLogo,
                                      sort: app.sort || 0
                                    });
                                  }
                                });
                              }

                              // 过滤出当前分组的应用
                              const filteredApps = [];
                              groupAppIds.forEach(appId => {
                                const app = appMap.get(appId.toString());
                                if (app) {
                                  filteredApps.push({
                                    applicationId: app.applicationId || app.id,
                                    applicationName: app.applicationName || app.name,
                                    applicationLogo: app.applicationLogo || app.logo,
                                    sort: app.sort || 0
                                  });
                                }
                              });

                              // 按 sort 字段排序
                              filteredApps.sort((a, b) => (a.sort || 0) - (b.sort || 0));

                              return {
                                status: 0,
                                msg: 'success',
                                data: {
                                  filteredApps: filteredApps
                                }
                              };
                            `
                          },
                          body: {
                            type: 'cards',
                            className: 'edit-group-apps-cards',
                            columnsCount: 4,
                            source: '$filteredApps',
                            card: {
                              className: 'edit-group-app-card',
                              body: {
                                type: 'container',
                                className: 'custom-icon-button',
                                body: [
                                  {
                                    type: 'container',
                                    className: 'app-icon-box',
                                    body: [
                                      {
                                        type: 'tpl',
                                        tpl: `
                                          <div class="main-icon">
                                            \${IF(applicationLogo,
                                              IF(STARTSWITH(applicationLogo, 'http'),
                                                '<img src="' + applicationLogo + '" style="width: 48px; height: 48px; border-radius: 8px; object-fit: cover;" />',
                                                applicationLogo
                                              ),
                                              '<div class="default-app-icon" style="width: 48px; height: 48px; background: #f5f5f5; border-radius: 8px;"></div>'
                                            )}
                                          </div>
                                        `
                                      },
                                      {
                                        type: 'tpl',
                                        tpl: `
                                          <div class="edit-badge" onclick="event.stopPropagation(); window.handleApplicationEdit('\${applicationId}', event.clientX, event.clientY + 10)">
                                            <i class="fa fa-pencil"></i>
                                          </div>
                                        `
                                      }
                                    ]
                                  },
                                  {
                                    type: 'tpl',
                                    className: 'app-icon-label',
                                    tpl: '${applicationName}'
                                  }
                                ]
                              }
                            }
                          }
                        },
                        // 添加应用按钮
                        {
                          type: 'button',
                          label: '添加应用',
                          // icon: 'fa fa-plus',
                          className: 'app-add-circle-button',
                          actionType: 'dialog',
                          dialog: {
                            title: '全部应用',
                            size: 'lg',
                            closeOnEsc: true,
                            className: 'add-apps-dialog',
                            actions: [
                              {
                                type: 'button',
                                label: '取消',
                                actionType: 'cancel',
                                className: 'dialog-cancel-btn'
                              },
                              {
                                type: 'button',
                                label: '确认',
                                level: 'primary',
                                actionType: 'confirm',
                                className: 'dialog-confirm-btn'
                              }
                            ],
                            body: {
                              type: 'form',
                              api: null,
                              className: 'add-apps-form',
                              body: [
                                {
                                  type: 'input-text',
                                  name: 'applicationName',
                                  placeholder: '搜索',
                                  label: false,
                                  addOn: {
                                    type: 'icon',
                                    icon: 'fa fa-search',
                                    className: 'search-icon'
                                  },
                                  clearable: true,
                                  onEvent: {
                                    change: {
                                      actions: [
                                        {
                                          actionType: 'broadcast',
                                          eventName: 'searchChange',
                                          args: {
                                            value: '${event.data.value}'
                                          }
                                        }
                                      ]
                                    }
                                  }
                                },
                                {
                                  type: 'service',
                                  id: 'dynamicTabsService',
                                  api: {
                                    method: 'get',
                                    url: `/admin-api/system/publish-application-groups/list`,
                                    adaptor: `
                                      if (payload.code === 0) {
                                        return {
                                          status: 0,
                                          msg: payload.msg,
                                          data: {
                                            items: payload.data || []
                                          }
                                        };
                                      }
                                      return {
                                        status: 1,
                                        msg: payload.msg,
                                        data: { items: [] }
                                      };
                                    `
                                  },
                                  body: {
                                    type: 'tabs',
                                    tabsMode: 'line',
                                    swipeable: true,
                                    source: '$items',
                                    tabs: [
                                      {
                                        title: '${name}',
                                        name: '${id}',
                                        body: {
                                          type: 'service',
                                          data: {
                                            id: '${id}'
                                          },
                                          body: {
                                            type: 'crud',
                                            id: 'crud',
                                            onEvent: {
                                              searchChange: {
                                                actions: [
                                                  {
                                                    actionType: 'reload',
                                                    data: {
                                                      applicationName:
                                                        '${event.data.value}'
                                                    }
                                                  }
                                                ]
                                              },
                                              refreshAllTabs: {
                                                actions: [
                                                  {
                                                    actionType: 'reload'
                                                  }
                                                ]
                                              }
                                            },
                                            mode: 'list',
                                            api: {
                                              url: '/admin-api/system/publish-application-groups/get',
                                              method: 'get',
                                              adaptor: `
                                                // 检查是否因为ID为空导致的错误
                                                if (payload.code === 400 && payload.msg && payload.msg.includes('请求参数缺失:id')) {
                                                  // 创建空的数据结构，表示该分组没有应用
                                                  const appAddList = JSON.parse(localStorage.getItem("applicationAddList") || '[]');
                                                  return {
                                                    status: 0,
                                                    msg: 'success',
                                                    data: {
                                                      applicationList: [],
                                                      appAddList: appAddList
                                                    }
                                                  };
                                                }

                                                // 确保 payload.data 存在，如果不存在则创建空对象
                                                if (!payload.data) {
                                                  payload.data = { applicationList: [] };
                                                }

                                                // 获取当前已选择的应用ID列表
                                                const appAddList = JSON.parse(localStorage.getItem("applicationAddList") || '[]');
                                                payload.data.appAddList = appAddList;

                                                return {
                                                  status: payload.code === 0 ? 0 : 1,
                                                  msg: payload.msg,
                                                  data: payload.data
                                                };
                                              `,
                                              data: {
                                                id: '${id}',
                                                applicationName:
                                                  '${applicationName}'
                                              }
                                            },
                                            loadDataOnceFetchOnFilter: false,
                                            loadDataOnce: true,
                                            placeholder: '',
                                            syncLocation: false,
                                            className:
                                              'workbench-group-manager',
                                            headerToolbar: [],
                                            footerToolbar: ['pagination'],
                                            listItem: {
                                              className:
                                                'group-manager-list-item',
                                              body: {
                                                type: 'flex',
                                                className: 'group-item-row',
                                                justify: 'space-between',
                                                alignItems: 'center',
                                                items: [
                                                  {
                                                    type: 'flex',
                                                    alignItems: 'center',
                                                    className:
                                                      'group-item-left',
                                                    items: [
                                                      {
                                                        type: 'tpl',
                                                        tpl: `
                                                          <div class="main-icon">
                                                            \${IF(applicationLogo,
                                                              IF(STARTSWITH(applicationLogo, 'http'),
                                                                '<img src="' + applicationLogo + '" style="width: 48px; height: 48px; border-radius: 8px; object-fit: cover;" />',
                                                                applicationLogo
                                                              ),
                                                              '<div class="default-app-icon" style="width: 48px; height: 48px; background: #f5f5f5; border-radius: 8px;"></div>'
                                                            )}
                                                          </div>
                                                        `,
                                                        className:
                                                          'group-item-icon'
                                                      },
                                                      {
                                                        type: 'tpl',
                                                        tpl: '${applicationName}',
                                                        className:
                                                          'group-item-name'
                                                      }
                                                    ]
                                                  },
                                                  {
                                                    type: 'button',
                                                    label: '添加',
                                                    visibleOn:
                                                      '!(appAddList && appAddList.includes(applicationId))',
                                                    level: 'primary',
                                                    size: 'sm',
                                                    className: 'btn-add',
                                                    onEvent: {
                                                      click: {
                                                        actions: [
                                                          {
                                                            actionType:
                                                              'custom',
                                                            script: `
                                                              const currentAppId = event.data.applicationId;

                                                                                         // 判断当前是否在编辑分组模式
                                                              const isEditMode = window.currentGroupInfo && window.currentGroupInfo.id;
                                                                                           if (isEditMode) {
                                                                // 编辑分组模式：调用接口添加应用
                                                                if (window.handleEditGroupAddApp) {
                                                              window.handleEditGroupAddApp(currentAppId);
                                                                }
                                                              }

                                                              let appIdsRaw = JSON.parse(localStorage.getItem("applicationAddList")) || [];
                                                              let appIds = Array.isArray(appIdsRaw) ? appIdsRaw : (typeof appIdsRaw === 'string' ? appIdsRaw.split(',') : []);

                                                              if (!appIds.includes(currentAppId)) {
                                                                appIds.push(currentAppId);
                                                                localStorage.setItem("applicationAddList",JSON.stringify(appIds));

                                                                doAction({
                                                                  actionType: 'setValue',
                                                                  componentId: 'edit-group-form',
                                                                  value: {
                                                                    appAddList: appIds
                                                                  }
                                                                });

                                                                doAction({
                                                                  actionType: 'reload',
                                                                  componentId: 'crud'
                                                                });

                                                                // 刷新编辑分组中的应用显示
                                                                doAction({
                                                                  actionType: 'reload',
                                                                  componentId: 'editGroupAppsDisplay'
                                                                });

                                                                // 刷新所有分组标签页的数据，确保状态同步
                                                                setTimeout(() => {
                                                                  try {
                                                                    // 使用广播事件刷新所有CRUD组件
                                                                    doAction({
                                                                      actionType: 'broadcast',
                                                                      eventName: 'refreshAllTabs'
                                                                    });
                                                                  } catch (error) {
                                                                    console.log('添加应用：广播刷新时出错:', error);
                                                                  }
                                                                }, 200);

                                                                // 参考移除应用的刷新逻辑，确保分组管理页面也更新
                                                                setTimeout(() => {
                                                                  try {
                                                                    // 通过DOM事件刷新（备用方案）
                                                                    const groupManagerCrud = document.querySelector('#workbench-group-manager');
                                                                    if (groupManagerCrud) {
                                                                      const event = new CustomEvent('reload', { bubbles: true });
                                                                      groupManagerCrud.dispatchEvent(event);
                                                                    }
                                                                  } catch (error) {
                                                                    console.log('添加应用：刷新分组管理CRUD时出错:', error);
                                                                  }
                                                                }, 200);
                                                              }
                                                            `
                                                          }
                                                        ]
                                                      }
                                                    }
                                                  },
                                                  {
                                                    type: 'button',
                                                    label: '已添加',
                                                    visibleOn:
                                                      'appAddList && appAddList.includes(applicationId)',
                                                    level: 'default',
                                                    size: 'sm',
                                                    className:
                                                      'btn-already-added',
                                                    onEvent: {
                                                      click: {
                                                        actions: [
                                                          {
                                                            actionType:
                                                              'custom',
                                                            script: `
                                                              const currentAppId = event.data.applicationId;

                                                                                              // 判断当前是否在编辑分组模式
                                                              const isEditMode = window.currentGroupInfo && window.currentGroupInfo.id;
                                                                                           if (isEditMode) {
                                                                // 编辑分组模式：调用接口添加应用
                                                                if (window.handleEditGroupRemoveApp) {
                                                              window.handleEditGroupRemoveApp(currentAppId);
                                                                }
                                                              }

                                                              let appIdsRaw = JSON.parse(localStorage.getItem("applicationAddList")) || [];
                                                              let appIds = Array.isArray(appIdsRaw) ? appIdsRaw : (typeof appIdsRaw === 'string' ? appIdsRaw.split(',') : []);

                                                              appIds = appIds.filter(id => id !== currentAppId && id !== currentAppId.toString());

                                                              localStorage.setItem("applicationAddList", JSON.stringify(appIds));

                                                              doAction({
                                                                actionType: 'setValue',
                                                                componentId: 'edit-group-form',
                                                                value: {
                                                                  appAddList: appIds
                                                                }
                                                              });

                                                              doAction({
                                                                actionType: 'reload',
                                                                componentId: 'crud'
                                                              });

                                                              // 刷新编辑分组中的应用显示
                                                              doAction({
                                                                actionType: 'reload',
                                                                componentId: 'editGroupAppsDisplay'
                                                              });

                                                              // 刷新所有分组标签页的数据，确保状态同步
                                                              setTimeout(() => {
                                                                try {
                                                                  // 使用广播事件刷新所有CRUD组件
                                                                  doAction({
                                                                    actionType: 'broadcast',
                                                                    eventName: 'refreshAllTabs'
                                                                  });
                                                                } catch (error) {
                                                                  console.log('移除应用：广播刷新时出错:', error);
                                                                }
                                                              }, 200);

                                                              // 参考移除应用的刷新逻辑，确保分组管理页面也更新
                                                              setTimeout(() => {
                                                                try {
                                                                  // 通过DOM事件刷新（备用方案）
                                                                  const groupManagerCrud = document.querySelector('#workbench-group-manager');
                                                                  if (groupManagerCrud) {
                                                                    const event = new CustomEvent('reload', { bubbles: true });
                                                                    groupManagerCrud.dispatchEvent(event);
                                                                  }
                                                                } catch (error) {
                                                                  console.log('移除应用：刷新分组管理CRUD时出错:', error);
                                                                }
                                                              }, 200);
                                                            `
                                                          }
                                                        ]
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            },
                                            loadingConfig: {
                                              className:
                                                'workbench-application-cards-loading'
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            {
              type: 'button',
              label: '删除分组',
              level: 'default',
              className: 'delete-group-btn',
              actionType: 'dialog',
              dialog: {
                title: '确认删除',
                body: '该应用将停用并删除，可前往“应用搭建”中心重新启用',
                size: 'xs',
                actions: [
                  {
                    type: 'button',
                    label: '取消',
                    actionType: 'cancel',
                    className: 'dialog-cancel-btn'
                  },
                  {
                    type: 'button',
                    label: '停用',
                    level: 'danger',
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'ajax',
                            api: {
                              url: '/admin-api/system/publish-application-groups/delete',
                              method: 'delete',
                              adaptor: `
                              if (payload.code === 0) {
                                window.refreshGroups(''); // 调用refreshGroups刷新数据
                              }
                              return {
                                status: payload.code === 0 ? 0 : 1,
                                msg: payload.msg,
                                data: payload.data
                              };
                            `,
                              data: {
                                id: '${id}'
                              }
                            }
                          },
                          {
                            actionType: 'wait',
                            args: {
                              time: 200
                            }
                          },
                          {
                            actionType: 'reload',
                            componentId: 'workbench-group-manager'
                          },
                          {
                            actionType: 'custom',
                            script: 'window.delGroupAndClose();'
                          }
                        ]
                      }
                    },
                    className: 'dialog-confirm-btn',
                    close: true
                  }
                ]
              }
            }
          ]
        },
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'close',
            className: 'cancel-btn',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script: `
                      // 取消时清理编辑分组的应用数据
                      localStorage.setItem('applicationAddList', JSON.stringify([]));
                    `
                  }
                ]
              }
            }
          },
          {
            type: 'button',
            label: '保存',
            actionType: 'submit',
            level: 'primary'
          },
          // 隐藏的应用排序按钮
          {
            type: 'button',
            label: '应用排序',
            className: 'hidden-app-sort-btn',
            style: {
              display: 'none'
            },
            actionType: 'drawer',
            drawer: getAppSortDrawerSchema('${id}')
          },
          // 隐藏的移动到分组按钮
          {
            type: 'button',
            label: '移动到分组',
            className: 'hidden-move-to-group-btn',
            style: {
              display: 'none'
            },
            actionType: 'drawer',
            drawer: getMoveToGroupDrawerSchema()
          }
        ]
      };
    };

    const getCreateGroupDrawerSchema = () => {
      localStorage.setItem('applicationType', '2');
      // 新建分组时清空已选应用列表
      localStorage.setItem('applicationAddList', JSON.stringify([]));
      return {
        title: '新建分组',
        size: 'md',
        closeOnEsc: false,
        closeOnOutside: false,
        showCloseButton: false,
        resizable: false,
        body: {
          type: 'form',
          id: 'create-group-form',
          wrapWithPanel: false,
          className: 'group-create-form',
          mode: 'normal',
          submitText: '',
          api: {
            url: '/admin-api/system/publish-application-groups/create',
            method: 'post',
            adaptor: `
              if(payload.code === 0) {
                // 刷新主页面分组数据
                window.refreshGroups(payload.data);
              }
              return {status: payload.code === 0 ? 0 : 1, msg: payload.msg};
            `,
            requestAdaptor: `
            // 根据 pageType 处理不同字段
            // api.data.sort = api.body.sort||0;

            // 获取当前选中的应用ID列表
            const selectedAppIds = JSON.parse(localStorage.getItem('applicationAddList') || '[]');
            api.data.applicationIds = selectedAppIds.join(',');
            //  api.data.applicationIds = '28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70';

            if (api.data.useScope === 'all') {
              api.data.userIds = api.data.useScope;
              delete api.data.useScope;
            } else {
              delete api.data.useScope;
            }
            return api;
            `
          },
          body: [
            {
              type: 'input-text',
              name: 'name',
              label: '分组名称',
              maxLength: 12,
              required: true,
              validateOnChange: true,
              placeholder: '请输入',
              clearable: true,
              inputClassName: 'group-name-input'
            },
            {
              type: 'select',
              name: 'useScope',
              label: '使用范围',
              required: true,
              value: 'all',
              placeholder: '请选择',
              clearable: false,
              options: [
                {
                  label: '全部成员',
                  value: 'all'
                },
                {
                  label: '部分成员',
                  value: 'part'
                }
              ],
              onEvent: {
                change: {
                  actions: [
                    {
                      actionType: 'dialog',
                      dialog: {
                        type: 'dialog',
                        title: '请选择',
                        body: [
                          {
                            label: '',
                            type: 'transfer',
                            name: 'transfer',
                            selectMode: 'tree',
                            resultListModeFollowSelect: false,
                            id: 'u:d1646dc0a71c',
                            onlyChildren: true,
                            autoCheckChildren: true,
                            source: '/admin-api/system/user/list-dept-user',
                            labelField: 'name',
                            valueField: 'id',
                            searchable: true,
                            statistics: true
                          }
                        ],
                        actions: [
                          {
                            type: 'button',
                            actionType: 'cancel',
                            label: '取消',
                            id: 'u:d2f9d50b6428'
                          },
                          {
                            type: 'button',
                            label: '确定',
                            close: true,
                            primary: true,
                            actionType: 'custom',
                            onEvent: {
                              click: {
                                actions: [
                                  {
                                    actionType: 'setValue',
                                    componentId: 'create-group-form',
                                    args: {
                                      value: {
                                        userIds: '${transfer}'
                                      }
                                    }
                                  },
                                  {
                                    actionType: 'close'
                                  }
                                ]
                              }
                            }
                          }
                        ],
                        size: 'md'
                      },
                      expression: "this.value === 'part'"
                    }
                  ]
                }
              }
            },
            {
              visibleOn: "${useScope === 'part'}",
              type: 'user-multi-select',
              static: true,
              name: 'userIds',
              label: '已选成员',
              placeholder: '请选择'
            },
            {
              type: 'container',
              className: 'group-apps-container',
              label: false,
              body: [
                {
                  type: 'flex',
                  direction: 'column',
                  items: [
                    {
                      type: 'tpl',
                      tpl: '应用',
                      className: 'group-apps-label'
                    },
                    {
                      type: 'container',
                      className: 'workbench-apps-grid',
                      body: [
                        // 显示选中的应用（新建分组时）
                        {
                          type: 'service',
                          id: 'selectedAppsDisplay',
                          interval: 2000, // 每2秒轮询一次
                          silentPolling: true, // 静默轮询，不显示加载状态
                          stopAutoRefreshWhen: 'false', // 始终保持轮询
                          api: {
                            method: 'get',
                            url: '/admin-api/system/application/page',
                            data: {
                              pageNo: 1,
                              pageSize: 100,
                              isRelease: 1
                            },
                            adaptor: `
                              // 获取选中的应用ID列表
                              const selectedAppIds = JSON.parse(localStorage.getItem('applicationAddList') || '[]');

                              // 从所有应用中筛选出选中的应用
                              const allApps = [];

                              // 检查不同可能的数据结构
                              let appList = null;
                              if (payload.code === 0) {
                                if (payload.data && payload.data.list) {
                                  appList = payload.data.list;
                                } else if (payload.data && Array.isArray(payload.data)) {
                                  appList = payload.data;
                                } else if (Array.isArray(payload)) {
                                  appList = payload;
                                }

                                if (appList && Array.isArray(appList)) {
                                  appList.forEach(app => {
                                    const appId = app.id || app.applicationId;
                                    if (selectedAppIds.includes(appId) || selectedAppIds.includes(appId.toString())) {
                                      allApps.push({
                                        applicationId: appId,
                                        applicationName: app.name || app.applicationName,
                                        applicationLogo: app.logo || app.applicationLogo
                                      });
                                    }
                                  });
                                }
                              }

                              return {
                                status: 0,
                                msg: 'success',
                                data: {
                                  selectedApps: allApps
                                }
                              };
                            `
                          },
                          body: {
                            type: 'cards',
                            className: 'create-group-apps-cards',
                            columnsCount: 4,
                            source: '$selectedApps',
                            card: {
                              className: 'create-group-app-card',
                              body: {
                                type: 'container',
                                className: 'custom-icon-button',
                                body: [
                                  {
                                    type: 'container',
                                    className: 'app-icon-box',
                                    body: [
                                      {
                                        type: 'tpl',
                                        tpl: `
                                          <div class="main-icon">
                                            \${IF(applicationLogo,
                                              IF(STARTSWITH(applicationLogo, 'http'),
                                                '<img src="' + applicationLogo + '" style="width: 48px; height: 48px; border-radius: 8px; object-fit: cover;" />',
                                                applicationLogo
                                              ),
                                              '<div class="default-app-icon" style="width: 48px; height: 48px; background: #f5f5f5; border-radius: 8px;"></div>'
                                            )}
                                          </div>
                                        `
                                      },
                                      {
                                        type: 'tpl',
                                        tpl: `
                                          <div class="del-badge" onclick="event.stopPropagation(); window.handleCreateGroupRemoveApp('\${applicationId}')">
                                            <i class="fa fa-trash"></i>
                                          </div>
                                        `
                                      }
                                    ]
                                  },
                                  {
                                    type: 'tpl',
                                    className: 'app-icon-label',
                                    tpl: '${applicationName}'
                                  }
                                ]
                              }
                            }
                          }
                        },
                        // 添加应用按钮
                        {
                          type: 'button',
                          label: '添加应用',
                          // icon: 'fa fa-plus',
                          className: 'app-add-circle-button',
                          actionType: 'dialog',
                          dialog: {
                            title: '全部应用',
                            size: 'lg',
                            closeOnEsc: true,
                            className: 'add-apps-dialog',
                            actions: [
                              {
                                type: 'button',
                                label: '取消',
                                actionType: 'cancel',
                                className: 'dialog-cancel-btn'
                              },
                              {
                                type: 'button',
                                label: '确认',
                                level: 'primary',
                                actionType: 'confirm',
                                className: 'dialog-confirm-btn'
                              }
                            ],
                            body: {
                              type: 'form',
                              api: null,
                              className: 'add-apps-form',
                              body: [
                                {
                                  type: 'input-text',
                                  name: 'applicationName',
                                  placeholder: '搜索',
                                  label: false,
                                  addOn: {
                                    type: 'icon',
                                    icon: 'fa fa-search',
                                    className: 'search-icon'
                                  },
                                  clearable: true,
                                  onEvent: {
                                    change: {
                                      actions: [
                                        {
                                          actionType: 'broadcast',
                                          eventName: 'searchChange',
                                          args: {
                                            value: '${event.data.value}'
                                          }
                                        }
                                      ]
                                    }
                                  }
                                },
                                {
                                  type: 'service',
                                  id: 'dynamicTabsService',
                                  api: {
                                    url: `/admin-api/system/publish-application-groups/list`,
                                    method: 'get',
                                    cache: 0, // 禁用缓存
                                    adaptor: `
                                      if (payload.code === 0) {
                                        return {
                                          status: 0,
                                          msg: 'success',
                                          data: {
                                            items: payload.data || []
                                          }
                                        };
                                      }
                                      return {
                                        status: 1,
                                        msg: payload.msg,
                                        data: { items: [] }
                                      };
                                    `
                                  },
                                  body: {
                                    type: 'tabs',
                                    tabsMode: 'line',
                                    swipeable: true,
                                    source: '$items',
                                    tabs: [
                                      {
                                        title: '${name}',
                                        name: '${id}',
                                        body: {
                                          type: 'service',
                                          data: {
                                            id: '${id}'
                                          },
                                          body: {
                                            type: 'crud',
                                            id: 'crud',
                                            onEvent: {
                                              searchChange: {
                                                actions: [
                                                  {
                                                    actionType: 'reload',
                                                    data: {
                                                      applicationName:
                                                        '${event.data.value}'
                                                    }
                                                  }
                                                ]
                                              },
                                              refreshAllTabs: {
                                                actions: [
                                                  {
                                                    actionType: 'reload'
                                                  }
                                                ]
                                              }
                                            },
                                            mode: 'list',
                                            api: {
                                              url: '/admin-api/system/publish-application-groups/get',
                                              method: 'get',
                                              adaptor: `
                                                // 检查是否因为ID为空导致的错误
                                                if (payload.code === 400 && payload.msg && payload.msg.includes('请求参数缺失:id')) {
                                                  // 创建空的数据结构，表示该分组没有应用
                                                  const appAddList = JSON.parse(localStorage.getItem("applicationAddList") || '[]');
                                                  return {
                                                    status: 0,
                                                    msg: 'success',
                                                    data: {
                                                      applicationList: [],
                                                      appAddList: appAddList
                                                    }
                                                  };
                                                }

                                                // 确保 payload.data 存在，如果不存在则创建空对象
                                                if (!payload.data) {
                                                  payload.data = { applicationList: [] };
                                                }

                                                // 获取当前已选择的应用ID列表
                                                const appAddList = JSON.parse(localStorage.getItem("applicationAddList") || '[]');
                                                payload.data.appAddList = appAddList;

                                                return {
                                                  status: payload.code === 0 ? 0 : 1,
                                                  msg: payload.msg,
                                                  data: payload.data
                                                };
                                              `,
                                              data: {
                                                id: '${id}',
                                                applicationName:
                                                  '${applicationName}'
                                              }
                                            },
                                            loadDataOnceFetchOnFilter: false,
                                            loadDataOnce: true,
                                            placeholder: '',
                                            syncLocation: false,
                                            className:
                                              'workbench-group-manager',
                                            headerToolbar: [],
                                            footerToolbar: ['pagination'],
                                            listItem: {
                                              className:
                                                'group-manager-list-item',
                                              body: {
                                                type: 'flex',
                                                className: 'group-item-row',
                                                justify: 'space-between',
                                                alignItems: 'center',
                                                items: [
                                                  {
                                                    type: 'flex',
                                                    alignItems: 'center',
                                                    className:
                                                      'group-item-left',
                                                    items: [
                                                      {
                                                        type: 'tpl',
                                                        tpl: `
                                                          <div class="main-icon">
                                                            \${IF(applicationLogo,
                                                              IF(STARTSWITH(applicationLogo, 'http'),
                                                                '<img src="' + applicationLogo + '" style="width: 48px; height: 48px; border-radius: 8px; object-fit: cover;" />',
                                                                applicationLogo
                                                              ),
                                                              '<div class="default-app-icon" style="width: 48px; height: 48px; background: #f5f5f5; border-radius: 8px;"></div>'
                                                            )}
                                                          </div>
                                                        `,
                                                        className:
                                                          'group-item-icon'
                                                      },
                                                      {
                                                        type: 'tpl',
                                                        tpl: '${applicationName}',
                                                        className:
                                                          'group-item-name'
                                                      }
                                                    ]
                                                  },
                                                  {
                                                    type: 'button',
                                                    label: '添加',
                                                    visibleOn:
                                                      '!(appAddList && appAddList.includes(applicationId))',
                                                    level: 'primary',
                                                    size: 'sm',
                                                    className: 'btn-add',
                                                    onEvent: {
                                                      click: {
                                                        actions: [
                                                          {
                                                            actionType:
                                                              'custom',
                                                            script: `
                                                              const currentAppId = event.data.applicationId;
                                                              let appIdsRaw = JSON.parse(localStorage.getItem("applicationAddList")) || [];
                                                              let appIds = Array.isArray(appIdsRaw) ? appIdsRaw : (typeof appIdsRaw === 'string' ? appIdsRaw.split(',') : []);

                                                              if (!appIds.includes(currentAppId)) {
                                                                appIds.push(currentAppId);
                                                                localStorage.setItem("applicationAddList",JSON.stringify(appIds))

                                                                doAction({
                                                                  actionType: 'setValue',
                                                                  componentId: 'create-group-form',
                                                                  value: {
                                                                    appAddList: appIds
                                                                  }
                                                                });

                                                                doAction({
                                                                  actionType: 'reload',
                                                                  componentId: 'crud'
                                                                });

                                                                // 刷新新建分组中的应用显示
                                                                doAction({
                                                                  actionType: 'reload',
                                                                  componentId: 'selectedAppsDisplay'
                                                                });

                                                                // 刷新所有分组标签页的数据，确保状态同步
                                                                setTimeout(() => {
                                                                  try {
                                                                    // 使用广播事件刷新所有CRUD组件
                                                                    doAction({
                                                                      actionType: 'broadcast',
                                                                      eventName: 'refreshAllTabs'
                                                                    });
                                                                  } catch (error) {
                                                                    console.log('新建分组添加应用：广播刷新时出错:', error);
                                                                  }
                                                                }, 200);
                                                              }
                                                            `
                                                          }
                                                        ]
                                                      }
                                                    }
                                                  },
                                                  {
                                                    type: 'button',
                                                    label: '已添加',
                                                    visibleOn:
                                                      'appAddList && appAddList.includes(applicationId)',
                                                    level: 'default',
                                                    size: 'sm',
                                                    className:
                                                      'btn-already-added',
                                                    onEvent: {
                                                      click: {
                                                        actions: [
                                                          {
                                                            actionType:
                                                              'custom',
                                                            script: `
                                                              const currentAppId = event.data.applicationId;
                                                              let appIdsRaw = JSON.parse(localStorage.getItem("applicationAddList")) || [];
                                                              let appIds = Array.isArray(appIdsRaw) ? appIdsRaw : (typeof appIdsRaw === 'string' ? appIdsRaw.split(',') : []);

                                                              appIds = appIds.filter(id => id !== currentAppId && id !== currentAppId.toString());

                                                              localStorage.setItem("applicationAddList", JSON.stringify(appIds));

                                                              doAction({
                                                                actionType: 'setValue',
                                                                componentId: 'create-group-form',
                                                                value: {
                                                                  appAddList: appIds
                                                                }
                                                              });

                                                              doAction({
                                                                actionType: 'reload',
                                                                componentId: 'crud'
                                                              });

                                                              // 刷新新建分组中的应用显示
                                                              doAction({
                                                                actionType: 'reload',
                                                                componentId: 'selectedAppsDisplay'
                                                              });

                                                              // 刷新所有分组标签页的数据，确保状态同步
                                                              setTimeout(() => {
                                                                try {
                                                                  // 使用广播事件刷新所有CRUD组件
                                                                  doAction({
                                                                    actionType: 'broadcast',
                                                                    eventName: 'refreshAllTabs'
                                                                  });
                                                                } catch (error) {
                                                                  console.log('新建分组移除应用：广播刷新时出错:', error);
                                                                }
                                                              }, 200);
                                                            `
                                                          }
                                                        ]
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            },
                                            loadingConfig: {
                                              className:
                                                'workbench-application-cards-loading'
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        },
        actions: [
          {
            type: 'button',
            label: '取消',
            actionType: 'close',
            className: 'cancel-btn',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script: `
                      // 取消时清理新建分组的应用数据
                      localStorage.setItem('applicationAddList', JSON.stringify([]));
                    `
                  }
                ]
              }
            }
          },
          {
            type: 'button',
            label: '保存',
            actionType: 'submit',
            level: 'primary'
          }
        ]
      };
    };

    // 获取应用列表数据
    const fetchApplications = async () => {
      if (!props.store?.userInfo?.id) return;

      setLoading(true);
      try {
        // 请求参数
        const data: any = {
          pageNo: 1,
          pageSize: 100 // 获取足够多的应用显示
        };

        // 根据选中分组处理查询条件
        // if (activeGroupId === 'all') {
        //   // 全部应用不需要设置groupId
        // } else if (activeGroupId === 'ungrouped') {
        //   data.notInGroup = true; // 未分组的应用
        // } else {
        data.id = activeGroupId; // 添加分组ID过滤
        // }

        // 调用API获取应用列表
        const res = await getPublishApplicationGroup(data);

        if (res.code === 0 && res.data) {
          setApplications(res.data.applicationList || []);
        } else {
          setApplications([]);
        }
      } catch (error) {
        setApplications([]);
      } finally {
        setLoading(false);
      }
    };

    // 手动创建一个刷新函数
    const refreshData = useCallback(() => {
      fetchApplicationGroups();
    }, [fetchApplicationGroups]);

    // 构建AMIS渲染Schema
    const buildSchema = () => {
      const headerItems: any[] = [
        {
          type: 'tpl',
          tpl: `<div class="workbench-header-title">${getDisplayTitle()}</div>`,
          inline: false
        }
      ];

      let bodyItems: any[] = [
        {
          type: 'html',
          className: 'workbench-group-tabs group-tabs-draggable',
          html: `
            <div id="group-tabs-container" class="group-tabs-draggable">
              ${applicationGroups
                .map(
                  (group, index) => `
                <div
                  class="group-btn ${
                    activeGroupId == group.id ? 'group-btn-active' : ''
                  }"
                  draggable="true"
                  data-group-id="${group.id}"
                  data-group-index="${index}"
                  onclick="window.handleGroupChange('${group.id}')"
                >
                  <span class="drag-handle">⋮⋮</span>
                  <span>${group.name}</span>
                </div>
              `
                )
                .join('')}
            </div>
          `
        },
        // 拖拽功能初始化
        {
          type: 'custom',
          id: 'drag-init',
          onMount: `
            setTimeout(() => {
              if (window.initializeDragEvents) {
                window.initializeDragEvents();
              }
            }, 200);
          `
        }
      ];

      const bodyItemsAuth: any[] = [
        {
          type: 'html',
          className: 'workbench-group-tabs group-tabs-draggable',
          html: `
            <div id="group-tabs-container" class="group-tabs-draggable">
              ${applicationGroups
                .map(
                  (group, index) => `
                <div
                  class="group-btn ${
                    activeGroupId == group.id ? 'group-btn-active' : ''
                  }"
                  draggable="true"
                  data-group-id="${group.id}"
                  data-group-index="${index}"
                  onclick="window.handleGroupChange('${group.id}')"
                >
                  <span class="drag-handle">⋮⋮</span>
                  <span>${group.name}</span>
                </div>
              `
                )
                .join('')}
              <div class="group-btn" onclick="window.openCreateGroupDrawer()">
                ＋ 新建分组
              </div>
            </div>
          `
        },
        // 拖拽功能初始化
        {
          type: 'custom',
          id: 'drag-init',
          onMount: `
            setTimeout(() => {
              if (window.initializeDragEvents) {
                window.initializeDragEvents();
              }
            }, 200);
          `
        },

        // 隐藏的新建分组按钮，用于触发抽屉
        {
          type: 'button',
          label: '新建分组',
          className: 'hidden-create-group-btn',
          style: {display: 'none'},
          actionType: 'drawer',
          drawer: getCreateGroupDrawerSchema(),
          onEvent: {
            'custom:openCreateGroupDrawer': {
              actions: [
                {
                  actionType: 'click'
                }
              ]
            }
          }
        }
      ];

      if(props?.store?.userInfo.hasOrgAuth){
        headerItems.push({
          type: 'container',
          className: 'workbench-header-settings',
          body: {
            type: 'flex',
            items: [
              {
                type: 'icon',
                icon: 'fas fa-gear',
                inline: false,
                className: 'workbench-header-settings-text'
              },
              {
                type: 'tpl',
                tpl: ' 设置 ',
                className: 'workbench-header-settings-text',
                inline: false
              }
            ]
          },
          justify: 'space-between',
          alignItems: 'center',
          onEvent: {
            click: {
              actions: [
                {
                  actionType: 'drawer',
                  drawer: {
                    title: '分组管理',
                    size: 'md',
                    body: {
                      id: 'workbench-group-manager',
                      type: 'crud',
                      api: {
                        method: 'get',
                        url: `/admin-api/system/publish-application-groups/list`,
                        data: {
                          pageNo: '${page}',
                          pageSize: '100'
                        },
                        adaptor: `
                        if (payload.code == 0) {
                          if (Array.isArray(payload.data)) {
                            payload.data.forEach(item => {
                              item.useScope = item.userIds === 'all' ? 'all' : 'part';
                            });
                          }
                        }
                        return {
                          status: payload.code === 0 ? 0 : 1,
                          msg: payload.msg,
                          data: payload.data
                        };
                      `
                      },
                      syncLocation: false,
                      className: 'workbench-group-manager',
                      headerToolbar: [
                        {
                          type: 'button',
                          level: 'primary',
                          label: '+ 新建分组',
                          actionType: 'drawer',
                          drawer: getCreateGroupDrawerSchema()
                        }
                      ],
                      footerToolbar: [],
                      mode: 'list',
                      listItem: {
                        className: 'group-manager-list-item',
                        body: {
                          type: 'flex',
                          className: 'group-item-row',
                          direction: 'column',
                          items: [
                            {
                              type: 'flex',
                              className: 'group-item-header',
                              justify: 'space-between',
                              alignItems: 'center',
                              items: [
                                {
                                  type: 'tpl',
                                  tpl: '${name}',
                                  className: 'group-item-name'
                                },
                                {
                                  type: 'flex',
                                  alignItems: 'center',
                                  className: 'group-item-right',
                                  items: [
                                    {
                                      type: 'tpl',
                                      tpl: '<span class=${userIds === "all"? "group-scope-all" :  "group-scope-part"}>${userIds === "all" ? "全员可见" : "部分可见"}</span>',
                                      className: 'group-item-scope'
                                    },
                                    {
                                      type: 'tpl',
                                      tpl: '<i class="fa fa-angle-right"></i>',
                                      className: 'group-item-arrow'
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              type: 'container',
                              className: 'group-item-apps',
                              visibleOn:
                                'applicationList && applicationList.length > 0',
                              body: {
                                type: 'flex',
                                className: 'group-apps-list',
                                wrap: true,
                                items: {
                                  type: 'each',
                                  name: 'applicationList',
                                  items: {
                                    type: 'tpl',
                                    className: 'group-app-item',
                                    tpl: `
                                      <div class="app-mini-icon">
                                        \${applicationLogo ?
                                          '<img src="' + applicationLogo + '" />' :
                                          '<div class="default-app-mini-icon"></div>'
                                        }
                                        <span class="app-mini-name">\${applicationName}</span>
                                      </div>
                                    `
                                  }
                                }
                              }
                            }
                          ]
                        }
                      },
                      itemAction: {
                        actionType: 'drawer',
                        drawer: getEditGroupDrawerSchema('${id}')
                      },
                      placeholder: '暂无分组数据',
                      columns: []
                    },
                    actions: [
                      {
                        type: 'button',
                        label: '取消',
                        close: true,
                        actionType: 'close'
                      }
                    ]
                  }
                }
              ]
            }
          }
        });

        bodyItems = bodyItemsAuth;
      }
      setSchema({
        type: 'page',
        title: '',
        body: [
          {
            type: 'flex',
            className: 'workbench-header',
            items: headerItems,
            justify: 'space-between',
            alignItems: 'center'
          },
          {
            type: 'flex',
            className: 'workbench-group',
            direction: 'row',
            wrap: true,
            justify: 'flex-start',
            items: bodyItems
          },
          {
            type: 'grid',
            columns: [
              {
                className: 'workbench-application-cards',
                md: 12,
                body: [
                  {
                    type: 'cards',
                    columnsCount: 4,
                    source: '$applications',
                    placeholder: '暂无应用',
                    masonryLayout: false,
                    card: {
                      className: 'workbench-application-cards-app',
                      header: false,
                      footer: false,
                      body: [
                        {
                          type: 'flex',
                          className: '',
                          items: [
                            {
                              type: 'html',
                              html: '<div class="app-logo" style="width:32px;height:32px;" > <% if(data.applicationLogo && data.applicationLogo.startsWith("http")) { %><img src="<%= data.applicationLogo %>" /> <% } else if(data.applicationLogo) { %><div style="width:36px;height:36px;"><%= data.applicationLogo %></div> <% } else { %><div class="default-app-icon" style="width:32px;height:32px;background:#f5f5f5;border-radius:8px;"></div><% } %> </div>',
                              className: 'app-logo-wrapper'
                            },
                            {
                              type: 'html',
                              html: '<div class="workbench-application-cards-app-name">${applicationName}</div>'
                            }
                          ],
                          alignItems: 'center',
                          justifyContent: 'flex-start'
                        }
                      ],
                      itemAction: {
                        type: 'button',
                        actionType: 'url',
                        url: '/#/app${applicationId}/workbench'
                      }
                    },
                    multiple: false,
                    loadingConfig: {
                      className: 'workbench-application-cards-loading'
                    }
                  }
                ]
              }
            ]
          }
        ],
        data: {
          applications: applications,
          applicationGroups: applicationGroups
        }
      });
    };

    // 首次加载及store.userInfo变化时获取数据
    useEffect(() => {
      localStorage.setItem('applicationAddList', '');
      sessionStorage.setItem('fromAppbuild', 'false');
      if (props.store?.userInfo) {
        fetchApplicationGroups();
      }
    }, [props.store?.userInfo]);

    // 当activeGroupId变化时获取应用列表
    useEffect(() => {
      if (activeGroupId) {
        fetchApplications();
      }
    }, [activeGroupId]);

    // 数据变化时更新schema
    useEffect(() => {
      buildSchema();
    }, [applicationGroups, applications, activeGroupId]);

    // 拖拽初始化函数
    const initializeDragEvents = useCallback(() => {
      const container = document.getElementById('group-tabs-container');
      if (!container || container.dataset.dragInitialized) {
        console.log('initializeDragEvents - 容器不存在或已初始化');
        return;
      }

      container.dataset.dragInitialized = 'true';
      let draggedIndex: number | null = null;

      // 为每个可拖拽项添加事件监听器
      const items = container.querySelectorAll('.group-btn[data-group-id]');

      items.forEach(item => {
        const element = item as HTMLElement;

        element.addEventListener('dragstart', function (e: DragEvent) {
          const target = this as HTMLElement;
          draggedIndex = parseInt((target.dataset as any).groupIndex);
          target.classList.add('dragging');
          if (e.dataTransfer) {
            e.dataTransfer.effectAllowed = 'move';
          }
        });

        element.addEventListener('dragend', function () {
          const target = this as HTMLElement;
          target.classList.remove('dragging');
          items.forEach(item =>
            (item as HTMLElement).classList.remove('drag-over')
          );
          draggedIndex = null;
        });

        element.addEventListener('dragover', function (e: DragEvent) {
          e.preventDefault();
          if (e.dataTransfer) {
            e.dataTransfer.dropEffect = 'move';
          }
          (this as HTMLElement).classList.add('drag-over');
        });

        element.addEventListener('dragleave', function () {
          (this as HTMLElement).classList.remove('drag-over');
        });

        element.addEventListener('drop', function (e: DragEvent) {
          e.preventDefault();
          const target = this as HTMLElement;
          target.classList.remove('drag-over');

          if (
            draggedIndex !== null &&
            draggedIndex !== parseInt((target.dataset as any).groupIndex)
          ) {
            const targetIndex = parseInt((target.dataset as any).groupIndex);
            if ((window as any).handleGroupSort) {
              (window as any).handleGroupSort(draggedIndex, targetIndex);
            }
          }
        });
      });
    }, []);

    // 重新初始化拖拽事件函数
    const reinitializeDragEvents = useCallback(() => {
      setTimeout(() => {
        const container = document.getElementById('group-tabs-container');
        if (!container) {
          console.log('reinitializeDragEvents - 容器不存在');
          return;
        }

        // 重置初始化标志
        container.dataset.dragInitialized = '';

        // 重新初始化
        initializeDragEvents();
      }, 200);
    }, [initializeDragEvents]);

    // 暴露给AMIS调用的函数
    useEffect(() => {
      // 暴露拖拽相关函数
      (window as any).initializeDragEvents = initializeDragEvents;
      (window as any).reinitializeDragEvents = reinitializeDragEvents;

      // 全局更新按钮状态函数
      (window as any).updateGroupButtonsState = (newActiveGroupId: string) => {
        const container = document.getElementById('group-tabs-container');
        if (!container) return;

        const buttons = container.querySelectorAll('.group-btn[data-group-id]');
        buttons.forEach(btn => {
          if ((btn as any).dataset.groupId == newActiveGroupId) {
            btn.classList.add('group-btn-active');
          } else {
            btn.classList.remove('group-btn-active');
          }
        });
      };

      (window as any).setSelectGroup = (applications: any) => {
        selectGroupApplications.current = applications;
      };

      (window as any).delGroup = () => {
        // 1. 刷新主页面的分组数据
        refreshData();
      };

      // 删除分组并关闭相关弹窗的函数
      (window as any).delGroupAndClose = () => {
        refreshData();
        // 2. 延迟关闭弹窗，确保数据刷新完成
        setTimeout(() => {
          try {
            // 方法1：通过类名查找移动到分组抽屉
            const moveToGroupDrawer =
              document.querySelectorAll('.cxd-Drawer-footer');
            if (moveToGroupDrawer) {
              moveToGroupDrawer.forEach(btn => {
                const closeBtn = btn.querySelector('.cancel-btn');
                if (closeBtn) {
                  (closeBtn as HTMLElement).click();
                  return;
                }
              });
            }
          } catch (error) {
            console.log('关闭弹窗时出错:', error);
          }
        }, 300);
      };

      (window as any).handleApplicationClick = (applicationId: string) => {
        console.log('点击主图标卡', applicationId);
      };

      (window as any).handleApplicationEdit = (
        applicationId: string,
        x: number,
        y: number
      ) => {
        // 移除可能已存在的菜单
        const existingMenu = document.getElementById('app-edit-menu');
        if (existingMenu) {
          document.body.removeChild(existingMenu);
        }

        // 创建弹出菜单元素
        const menuEl = document.createElement('div');
        menuEl.id = 'app-edit-menu';
        menuEl.className = 'app-edit-menu';
        menuEl.innerHTML = `
          <div class="app-edit-menu-item" onclick="window.handleAdjustOrder('${applicationId}')">调整顺序</div>
          <div class="app-edit-menu-item" onclick="window.handleMoveToGroup('${applicationId}')">移动至其他分组</div>
          <div class="app-edit-menu-item" onclick="window.handleEditGroupRemoveApp('${applicationId}')">移除应用</div>
        `;

        // 设置菜单位置，使用传入的坐标
        menuEl.style.position = 'absolute';
        menuEl.style.left = `${x}px`;
        menuEl.style.top = `${y}px`;
        menuEl.style.zIndex = '9999';

        // 添加到文档中
        document.body.appendChild(menuEl);

        // 点击其他地方关闭菜单
        const closeMenu = (e: MouseEvent) => {
          const target = e.target as HTMLElement;
          if (!menuEl.contains(target)) {
            if (document.body.contains(menuEl)) {
              document.body.removeChild(menuEl);
            }
            document.removeEventListener('click', closeMenu);
          }
        };

        // 延迟添加事件监听器，避免立即触发
        setTimeout(() => {
          document.addEventListener('click', closeMenu);
        }, 100);
      };

      // 添加处理菜单项点击的函数
      (window as any).handleAdjustOrder = (applicationId: string) => {
        // 查找隐藏的应用排序按钮并点击
        const hiddenBtn = document.querySelector(
          '.hidden-app-sort-btn'
        ) as HTMLButtonElement;
        if (hiddenBtn) {
          hiddenBtn.click();
        } else {
          console.log('未找到隐藏的应用排序按钮');
        }
      };

      (window as any).handleMoveToGroup = (applicationId: string) => {
        // 先关闭编辑菜单
        const existingMenu = document.getElementById('app-edit-menu');
        if (existingMenu) {
          document.body.removeChild(existingMenu);
        }

        // 存储当前要移动的应用ID到全局变量
        (window as any).currentMoveApplicationId = applicationId;

        // 查找隐藏的移动到分组按钮并点击
        const hiddenBtn = document.querySelector(
          '.hidden-move-to-group-btn'
        ) as HTMLButtonElement;
        if (hiddenBtn) {
          hiddenBtn.click();
        } else {
          console.log('未找到隐藏的移动到分组按钮，使用备用方案');
        }
      };

      // 编辑分组中添加应用的处理函数
      (window as any).handleEditGroupAddApp = async (applicationId: string) => {
        try {
          // 获取当前分组信息
          const currentGroupInfo = (window as any).currentGroupInfo || {};
          const currentGroupId = currentGroupInfo.id;

          if (!currentGroupId) {
            console.log('编辑分组添加应用 - 无法获取当前分组ID');
            return;
          }

          // 获取当前分组的应用列表
          const currentApplicationIds = currentGroupInfo.applicationIds
            ? currentGroupInfo.applicationIds
                .split(',')
                .filter((id: string) => id.trim())
            : [];

          // 检查应用是否已在分组中
          if (currentApplicationIds.includes(applicationId.toString())) {
            console.log('编辑分组添加应用 - 应用已在分组中');
            return;
          }

          // 添加应用到分组
          const updatedAppIds = [
            ...currentApplicationIds,
            applicationId.toString()
          ];

          // 更新分组信息
          const updateData = {
            id: currentGroupId,
            name: currentGroupInfo.name,
            userIds: currentGroupInfo.userIds || '',
            applicationIds: updatedAppIds.join(','),
            sort: currentGroupInfo.sort || 0
          };

          // 调用更新接口
          const result = await updatePublishApplicationGroup(updateData);

          if (result.code !== 0) {
            console.log('编辑分组添加应用 - 更新失败:', result.msg);
            return;
          }

          // 更新全局变量中的应用列表
          if ((window as any).currentGroupInfo) {
            (window as any).currentGroupInfo.applicationIds =
              updatedAppIds.join(',');
          }
        } catch (error) {
          console.log('编辑分组添加应用 - 处理过程中出错:', error);
        }
      };

      // 新建分组中删除应用的处理函数
      (window as any).handleCreateGroupRemoveApp = (applicationId: string) => {
        const currentAppId = applicationId;
        let appIdsRaw = JSON.parse(
          localStorage.getItem('applicationAddList') || '[]'
        );

        // 处理类型匹配问题
        const targetId = currentAppId;
        const targetIdNum = parseInt(currentAppId);

        const appIds = appIdsRaw.filter((id: any) => {
          const idStr = id.toString();
          const idNum = parseInt(id);
          const shouldKeep = idStr !== targetId && idNum !== targetIdNum;
          return shouldKeep;
        });

        localStorage.setItem('applicationAddList', JSON.stringify(appIds));

        
      };

      // 编辑分组中删除应用的处理函数
      (window as any).handleEditGroupRemoveApp = async (
        applicationId: string
      ) => {
        try {
          // 获取当前分组信息
          const currentGroupInfo = (window as any).currentGroupInfo || {};
          const currentGroupId = currentGroupInfo.id;

          if (!currentGroupId) {
            console.log('编辑分组移除应用 - 无法获取当前分组ID');
            return;
          }

          // 获取当前分组的应用列表
          const currentApplicationIds = currentGroupInfo.applicationIds
            ? currentGroupInfo.applicationIds
                .split(',')
                .filter((id: string) => id.trim())
            : [];

          // 从当前分组中移除该应用
          const updatedAppIds = currentApplicationIds.filter(
            (id: string) => id.toString() !== applicationId.toString()
          );

          // 更新分组信息
          const updateData = {
            id: currentGroupId,
            name: currentGroupInfo.name,
            userIds: currentGroupInfo.userIds || '',
            applicationIds: updatedAppIds.join(','),
            sort: currentGroupInfo.sort || 0
          };

          // 调用更新接口
          const result = await updatePublishApplicationGroup(updateData);

          if (result.code !== 0) {
            console.log('编辑分组移除应用 - 更新失败:', result.msg);
            return;
          }

          // 更新全局变量中的应用列表
          if ((window as any).currentGroupInfo) {
            (window as any).currentGroupInfo.applicationIds =
              updatedAppIds.join(',');
          }

          // 关闭菜单
          const existingMenu = document.getElementById('app-edit-menu');
          if (existingMenu && document.body.contains(existingMenu)) {
            document.body.removeChild(existingMenu);
          }
        } catch (error) {
          console.log('编辑分组移除应用 - 处理过程中出错:', error);
        }
      };

      // 关闭移动到分组抽屉的函数
      (window as any).closeMoveToGroupDrawer = () => {
        try {
          // 方法1：通过类名查找移动到分组抽屉
          const moveToGroupDrawer = document.querySelector(
            '.move-to-group-drawer'
          );
          if (moveToGroupDrawer) {
            const closeBtn =
              moveToGroupDrawer.querySelector('.cxd-Drawer-close');
            if (closeBtn) {
              (closeBtn as HTMLElement).click();
              return;
            }
          }
        } catch (error) {
          console.log('关闭移动到分组抽屉时出错:', error);
        }
      };

      // 移动应用到指定分组的核心函数
      (window as any).moveApplicationToGroup = async (
        applicationId: string,
        targetGroupId: number,
        targetGroupName: string
      ) => {
        try {
          // 获取当前分组信息
          const currentGroupInfo = (window as any).currentGroupInfo || {};
          const currentGroupId = currentGroupInfo.id;

          // 重新获取当前分组的最新数据，确保 applicationIds 是最新的
          const currentGroupData = await getPublishApplicationGroup({
            id: currentGroupId
          });

          // 获取当前分组的应用列表（使用最新数据）

          const currentApplicationIds = currentGroupData.data.applicationIds
            ? currentGroupData.data.applicationIds
                .split(',')
                .filter((id: string) => id.trim())
            : [];

          // 从当前分组中移除该应用
          const updatedCurrentGroupAppIds = currentApplicationIds.filter(
            (id: string) => {
              const shouldKeep = id.toString() !== applicationId.toString();
              return shouldKeep;
            }
          );

          // 获取目标分组信息
          const targetGroupData = await getPublishApplicationGroup({
            id: targetGroupId
          });

          // 获取目标分组的应用列表
          const targetApplicationIds = targetGroupData.data.applicationIds
            ? targetGroupData.data.applicationIds
                .split(',')
                .filter((id: string) => id.trim())
            : [];

          // 添加应用到目标分组（如果不存在的话）
          if (!targetApplicationIds.includes(applicationId.toString())) {
            targetApplicationIds.push(applicationId.toString());
          }

          // 更新当前分组（移除应用）- 使用最新的分组数据
          const updateCurrentGroupResult = await updatePublishApplicationGroup({
            id: currentGroupId,
            name: currentGroupData.data.name,
            userIds: currentGroupData.data.userIds,
            applicationIds: updatedCurrentGroupAppIds.join(','),
            sort: currentGroupData.data.sort
          });

          // 更新目标分组（添加应用）
          const updateTargetGroupResult = await updatePublishApplicationGroup({
            id: targetGroupId,
            name: targetGroupData.data.name,
            userIds: targetGroupData.data.userIds,
            applicationIds: targetApplicationIds.join(','),
            sort: targetGroupData.data.sort
          });

          // 更新 window.currentGroupInfo 中的应用列表
          if ((window as any).currentGroupInfo) {
            (window as any).currentGroupInfo.applicationIds =
              updatedCurrentGroupAppIds.join(',');
          }

          // 直接重新获取分组数据，不触发页面重新渲染
          fetchApplicationGroups();

          // 2. 更新编辑分组中的应用显示

          // 注意：不要更新 applicationAddList，因为这会导致编辑分组显示变空
          // 编辑分组应该显示当前分组的所有应用，而不是基于 applicationAddList

          // 更新 localStorage 中的 applicationList，移除已移动的应用
          const currentApplicationList = JSON.parse(
            localStorage.getItem('applicationList') || '[]'
          );
          const updatedApplicationList = currentApplicationList.filter(
            (app: any) => {
              const appId = app.applicationId || app.id;
              return appId.toString() !== applicationId.toString();
            }
          );
          localStorage.setItem(
            'applicationList',
            JSON.stringify(updatedApplicationList)
          );
        } catch (error) {}
      };

      // 关闭移动到分组对话框
      (window as any).closeMoveToGroupDialog = () => {
        const dialog = document.getElementById('move-to-group-dialog');
        if (dialog) {
          dialog.remove();
        }
      };

      // 选择目标分组
      (window as any).selectTargetGroup = (
        applicationId: string,
        targetGroupId: number,
        targetGroupName: string
      ) => {
        // 关闭对话框
        (window as any).closeMoveToGroupDialog();

        // 调用移动应用函数
        (window as any).moveApplicationToGroup(
          applicationId,
          targetGroupId,
          targetGroupName
        );
      };

      // 提供一个refreshGroups函数给AMIS调用
      (window as any).refreshGroups = (groupData?: any) => {
        // 重新获取分组数据
        fetchApplicationGroups();

        // 处理参数：可能是分组对象，也可能是分组ID
        let newActiveGroupId: string | null = null;
        if (groupData) {
          if (typeof groupData === 'object' && groupData.id) {
            // 如果是分组对象
            newActiveGroupId = groupData.id.toString();
          } else if (
            typeof groupData === 'string' ||
            typeof groupData === 'number'
          ) {
            // 如果是分组ID
            newActiveGroupId = groupData.toString();
          }

          if (newActiveGroupId) {
            setActiveGroupId(newActiveGroupId);
          }
        }

        // 重新初始化拖拽事件和更新按钮状态
        setTimeout(() => {
          try {
            // 重新初始化拖拽事件
            if ((window as any).reinitializeDragEvents) {
              (window as any).reinitializeDragEvents();
            }

            // 更新按钮状态
            if ((window as any).updateGroupButtonsState && newActiveGroupId) {
              setTimeout(() => {
                (window as any).updateGroupButtonsState(newActiveGroupId);
              }, 200);
            }
          } catch (error) {
            console.log('更新按钮状态时出错:', error);
          }
        }, 300);
      };

      // 应用排序后刷新编辑分组应用显示的函数
      (window as any).refreshEditGroupAppsAfterSort = (
        sortedAppIds: string[]
      ) => {
        try {
          // 更新 window.currentGroupInfo 中的应用列表
          if ((window as any).currentGroupInfo) {
            (window as any).currentGroupInfo.applicationIds =
              sortedAppIds.join(',');
          }

          // 更新 localStorage 中的应用列表，确保编辑分组中的应用显示也会更新
          const currentApplicationList = JSON.parse(
            localStorage.getItem('applicationList') || '[]'
          );
          if (
            currentApplicationList &&
            currentApplicationList.length > 0 &&
            sortedAppIds &&
            sortedAppIds.length > 0
          ) {
            // 更新 localStorage 中的应用列表的排序
            const updatedApplicationList = currentApplicationList.map(
              (app: any) => {
                const newAppIndex = sortedAppIds.findIndex(
                  sortedId => sortedId === app.applicationId
                );
                if (newAppIndex !== -1) {
                  return {
                    ...app,
                    sort: newAppIndex
                  };
                }
                return app;
              }
            );

            localStorage.setItem(
              'applicationList',
              JSON.stringify(updatedApplicationList)
            );
          }
        } catch (error) {
          console.log('应用排序刷新 - 刷新过程中出错:', error);
        }
      };

      // 从分组中移除应用的函数
      (window as any).removeAppFromGroup = (applicationId: string) => {
        // 获取当前已选应用列表
        let appIds = JSON.parse(
          localStorage.getItem('applicationAddList') || '[]'
        );

        // 移除指定的应用ID（处理类型匹配问题）
        const targetId = applicationId;
        const targetIdNum = parseInt(applicationId);
        appIds = appIds.filter((id: any) => {
          const idStr = id.toString();
          const idNum = parseInt(id);
          const shouldKeep = idStr !== targetId && idNum !== targetIdNum;
          return shouldKeep;
        });
        // 更新localStorage
        localStorage.setItem('applicationAddList', JSON.stringify(appIds));
      };

      // 打开新建分组抽屉
      (window as any).openCreateGroupDrawer = () => {
        // 查找隐藏的新建分组按钮并点击
        const hiddenBtn = document.querySelector(
          '.hidden-create-group-btn'
        ) as HTMLButtonElement;
        if (hiddenBtn) {
          hiddenBtn.click();
        }
      };

      // 提供一个handleGroupChange函数给AMIS调用
      (window as any).handleGroupChange = (groupId: string) => {
        // 更新状态
        setActiveGroupId(groupId);

        // 更新按钮状态和重新初始化拖拽事件
        setTimeout(() => {
          if ((window as any).updateGroupButtonsState) {
            (window as any).updateGroupButtonsState(groupId);
          }

          // 重新初始化拖拽事件，因为状态更新可能导致DOM重新渲染
          setTimeout(() => {
            if ((window as any).reinitializeDragEvents) {
              (window as any).reinitializeDragEvents();
            }
          }, 500);
        }, 10);
      };

      // 处理分组拖拽排序
      (window as any).handleGroupSort = async (
        fromIndex: number,
        toIndex: number
      ) => {
        if (fromIndex === toIndex) {
          return;
        }

        // 防止重复操作
        if ((window as any).isGroupSorting) {
          return;
        }

        (window as any).isGroupSorting = true;

        try {
          // 重新获取最新的分组数据，确保使用最新的sort值
          const res = await getPublishApplicationGroupList({
            userId: props?.store?.userInfo.id
          });
          if (res.code !== 0 || !res.data) {
            (window as any).isGroupSorting = false;
            return;
          }

          const latestGroups = res.data;

          // 获取要交换的两个分组（使用最新数据）
          const fromGroup = latestGroups[fromIndex];
          const toGroup = latestGroups[toIndex];

          if (!fromGroup || !toGroup) {
            console.log('分组排序 - 找不到要交换的分组');
            (window as any).isGroupSorting = false;
            return;
          }

          // 准备更新数据：交换两个分组的sort值
          const fromGroupUpdateData = {
            id: fromGroup.id,
            name: fromGroup.name,
            userIds: fromGroup.userIds || '',
            applicationIds: fromGroup.applicationIds || '',
            sort: toGroup.sort // 使用目标分组的sort值
          };

          const toGroupUpdateData = {
            id: toGroup.id,
            name: toGroup.name,
            userIds: toGroup.userIds || '',
            applicationIds: toGroup.applicationIds || '',
            sort: fromGroup.sort // 使用源分组的sort值
          };

          // 只调用两次接口，交换两个分组的sort值
          const updatePromises = [
            updatePublishApplicationGroup(fromGroupUpdateData),
            updatePublishApplicationGroup(toGroupUpdateData)
          ];

          // 等待两个更新完成
          const results = await Promise.all(updatePromises);

          // 检查是否有失败的请求
          const hasError = results.some(result => result.code !== 0);

          if (hasError) {
            console.log('分组排序 - 交换失败:', results);
            return;
          }

          // 刷新分组数据
          setTimeout(async () => {
            // 刷新主页面的分组数据，等待数据更新完成
            await fetchApplicationGroups();

            // 延迟重新初始化拖拽事件，确保数据更新后再绑定事件
            setTimeout(() => {
              if ((window as any).reinitializeDragEvents) {
                (window as any).reinitializeDragEvents();
              }

              // 重置排序标志，允许下次拖拽
              (window as any).isGroupSorting = false;
            }, 300);
          }, 200);
        } catch (error) {
          // 出错时也要重置标志
          (window as any).isGroupSorting = false;
        }
      };

      return () => {
        // 组件卸载时清理全局函数
        delete (window as any).refreshGroups;
        delete (window as any).handleGroupChange;
        delete (window as any).handleApplicationEdit;
        delete (window as any).handleApplicationClick;
        delete (window as any).setSelectGroup;
        delete (window as any).delGroup;
        delete (window as any).delGroupAndClose;
        delete (window as any).removeAppFromGroup;
        delete (window as any).handleAdjustOrder;
        delete (window as any).handleMoveToGroup;
        delete (window as any).handleEditGroupAddApp;
        delete (window as any).handleEditGroupRemoveApp;
        delete (window as any).handleCreateGroupRemoveApp;
        delete (window as any).updateGroupButtonsState;
        delete (window as any).openCreateGroupDrawer;
        delete (window as any).reinitializeDragEvents;
        delete (window as any).handleGroupSort;
        delete (window as any).moveApplicationToGroup;
        delete (window as any).closeMoveToGroupDialog;
        delete (window as any).selectTargetGroup;
      };
    }, [refreshData, activeGroupId]);

    // 根据selectedGroupId获取显示的标题
    const getDisplayTitle = () => {
      const group = applicationGroups.find(g => g.id == activeGroupId);
      return group ? group.name : '全部应用';
    };

    return (
      <div className="workbench">
        <AMISRenderer
          schema={schema}
          loading={loading}
          data={{
            applications: applications,
            applicationGroups: applicationGroups
          }}
        />
      </div>
    );
  })
);

export default WorkbenchView;
