/**
 * 路由工具函数
 * 提供与路由和导航相关的通用工具函数
 */

/**
 * 检查当前路由是否来自appbuild
 * @returns boolean 是否来自appbuild
 */
export const isFromAppbuild = (): boolean => {
  // 从sessionStorage获取来源信息
  const storedSource = sessionStorage.getItem('fromAppbuild');
  return storedSource === 'true';
};

/**
 * 设置当前路由来自appbuild
 */
export const utilsSetFromAppbuild = (): void => {
  sessionStorage.setItem('fromAppbuild', 'true');
};

/**
 * 导航到指定路径，同时保留来源信息
 * @param props 组件props，包含history对象
 * @param path 目标路径
 */
export const navigateWithSource = (props: any, path: string): void => {
  props.history.push({
    pathname: path,
    state: { fromAppbuild: isFromAppbuild() }
  });
};

/**
 * 返回工作台或搭建平台
 * @param props 组件props，包含history对象
 */
export const goToWorkbench = (props: any): void => {
  if (isFromAppbuild()) {
    // 返回搭建平台
    props.history.push('/appbuild');
  } else {
    // 返回工作台
    props.history.push('/platform/workbench');
  }
};

/**
 * 清除来源信息
 * 在用户明确执行某些操作（如登出）后调用，以重置来源状态
 */
export const clearSourceInfo = (): void => {
  sessionStorage.removeItem('fromAppbuild');
}; 