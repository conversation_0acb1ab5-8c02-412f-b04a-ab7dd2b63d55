export const GetTeamMember = () => {
    return {
      "type": "dialog",
      //"initApi": "/admin-api/system/team-and-project-members/get-selected-users?teamOrProjectId=${teamOrProjectId}",
      "title": "邀请成员",
      "body": [
        {
          "label": "分组",
          "type": "transfer",
          "name": "transfer",
          "selectMode": "tree",
          "resultListModeFollowSelect": false,
          "id": "u:d1646dc0a71c",
          "onlyChildren": true,
          "autoCheckChildren": true,
          "source": "/admin-api/system/user/list-dept-user?teamOrProjectOrApplicationId=${teamOrProjectId}&type=${type}",
          "labelField": "name",
          "valueField": "id",
          "searchable": true,
          "statistics": true,
          // "value":"${selectMemberIds}",
  
        }
      ],
      "actions": [
        {
          type: 'button',
          actionType: 'cancel',
          label: '取消',
          id: 'u:d2f9d50b6428'
        },
        {
          "type": "button",
          "label": "确定",
          "close": true,
          "primary": true,
          "actionType": "ajax",
          "api": {
            "method": "post",
            "url": "/admin-api/system/team-project-application-members/batch-create",
            "data": {
              "teamOrProjectOrApplicationId": "${teamOrProjectId}",
              "userIds": "${transfer}",
              "type": "${type}"
            }
          },
        }
      ],
      "size": "md"
    };
  };