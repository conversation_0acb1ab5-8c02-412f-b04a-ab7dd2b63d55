import React, {FC, useState, useRef} from 'react';
import './index.scss';
import {Button, Tabs, Tab, toast, Avatar, Link} from 'amis';
import itemShow from './image/itemShow.png';
import {javaApplicationList, updateTeamProject, updateIcon, getTeamMember} from '@/utils/api/api';
import { EditProjectNameDialog } from '@/utils/schemaDataSet/EditProjectNameDialog';
import { EditProjectDescribeDialog } from '@/utils/schemaDataSet/EditProjectDescribeDialog';
import { EditTeamMember } from '@/utils/schemaDataSet/EditTeamMember';
import { GetTeamMember } from '@/utils/schemaDataSet/GetTeamMember';
import AMISRenderer from '@/component/AMISRenderer';


 




 
const ApplyPermission: FC<any> = (props: any) => {

    const store = props.store;

    const [totalAll, setTotalAll] = React.useState(0)
  
  
    const [memberListKey, setMemberListKey] = React.useState(0);
  
    // 选项卡的 工具栏
    const TabsToolbar = () => {
      return <div className="projectMain-main-tabsToolbar"></div>;
    };
    
    // 从URL查询参数中获取activeTabKey

    
    //项目的信息
    const [applyInfo, setApplyInfo] = React.useState<any>(false);

    
    // 项目的 应用列表

  
    const [roleId, setroleId] = React.useState(0);
    
    const [showAddMemberDialog, setShowAddMemberDialog] = React.useState(false);
    
    const inviteMemberPopup = () => {
      setShowAddMemberDialog(true);
    };

    React.useEffect(() => {
        if (props.applyData.id) {
            setApplyInfo(props.applyData)

            const data = {
                teamOrProjectOrApplicationId: props.applyData.id,
                userId: props.store.userInfo?.id,
                pageNo: 1,
                pageSize: 10,
                type: '3'
            }
            getTeamMember(data)
            .then(res => {
                if(res.data.list[0].role > 0){
                    console.log(res.data.list[0],'------------role')
                    setroleId(res.data.list[0].role);    
                }
    
            })
                .catch(error => {
                console.error('获取团队成员失败:', error);
            });
          }

      }, [props.applyData]);


  
  /* created 初始化 end */
  return (
    
    <div className="projectMain">
        <div className="projectMain-top">
            <div className="projectMain-top-left">
            <div className="projectMain-top-left-name">应用管理员设置</div>
            <div className="projectMain-top-left-describe">
                为该应用配置管理员，应用管理员可以对应用进行编辑/设置/应用软删除的管理。了解
                <Link>更多</Link>
            </div>
            </div>
        </div>
        
        <div className="projectMain-main">

            <div className="itemList">
                    <Button
                    size="lg"
                    level="primary"
                    onClick={() => inviteMemberPopup()}
                    >
                    <span className="teamMain-top-right-btn-addIcon">+</span>
                    <span className="teamMain-top-right-btn-name">
                        邀请成员
                    </span>
                    </Button>
            </div>
            <div className="itemList">
                    <div className="teamMain-main-memberList-header">
                        <div className="teamMain-main-memberList-header-title">
                            <span className="teamMain-main-memberList-header-title-text">应用成员数量</span>
                            <span className="teamMain-main-memberList-header-title-count">({totalAll})</span>
                            </div>
                        <div className="teamMain-main-memberList-header-view">
                            <span className="teamMain-main-memberList-header-title-text">
                                {/* {props.dataInfo.creator == props.store.userInfo?.id ? '创建者视角' : '管理员和成员视角'} */}
                            </span>
                        </div>
                    </div>
            </div>
              {showAddMemberDialog && (
                <AMISRenderer
                  show={showAddMemberDialog}
                  schema={GetTeamMember()}
                  onClose={() => {
                    setShowAddMemberDialog(false);
                    setMemberListKey(prev => prev + 1);
                  }}
                  data={{
                    teamOrProjectId: applyInfo?.id,
                    type: '3',
                  }}
                />
              )}

              {/* 测试roleId是否有值 */}
              {console.log('Current roleId:', roleId)}
              {roleId !== 0  ? (
                <AMISRenderer
                  key={`${applyInfo?.id}-${memberListKey}`}
                  schema={EditTeamMember(setroleId,setTotalAll)}
                  embedMode={true}
                  data={{
                    teamOrProjectId: applyInfo?.id,
                    creator: applyInfo.creator,
                    user_id: props.store.userInfo?.id,
                    type: '3',
                    roleId: roleId
                  }}
                  id="memberCrud"
                />
              ) : (
                <div>Loading roleId...</div>
              )}
        </div>
    </div>

  );
}

export default ApplyPermission;
