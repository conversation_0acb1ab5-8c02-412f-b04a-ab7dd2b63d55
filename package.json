{"name": "zerocode", "version": "1.0.0", "description": "零代码", "main": "index.js", "scripts": {"extract-vars": "node scripts/extract-css-vars-enhanced.js", "predev": "npm run extract-vars", "dev": "set NODE_OPTIONS=--max-old-space-size=8192 && amis dev", "prebuild": "npm run extract-vars", "build": "set NODE_OPTIONS=--max-old-space-size=8192 && amis build"}, "repository": {"type": "git", "url": "git+https://github.com/aisuda/amis-editor-demo.git"}, "keywords": ["amis", "editor"], "author": "fex", "license": "ISC", "bugs": {"url": "https://github.com/aisuda/amis-editor-demo/issues"}, "homepage": "https://github.com/aisuda/amis-editor-demo#readme", "dependencies": {"@fortawesome/fontawesome-free": "^6.6.0", "amis": "npm:@fjyueke/amis@^6.12.8", "amis-core": "6.12.0", "amis-editor": "npm:@fjyueke/amis-editor@^6.12.7", "amis-editor-core": "npm:@fjyueke/amis-editor-core@6.12.0-beat10", "amis-formula": "6.12.0", "amis-ui": "npm:@fjyueke/amis-ui@6.12.4", "axios": "^0.20.0", "copy-to-clipboard": "^3.2.0", "dnd-core": "^15.1.2", "echarts": "5.5.1", "mobx": "4.15.7", "mobx-react": "6.3.1", "mobx-state-tree": "3.17.3", "or": "^0.2.0", "qs": "6.5.3", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^15.1.2", "react-dnd-html5-backend": "^15.1.3", "react-dom": "^17.0.2", "react-hook-form": "^7.39.3", "react-router": "5.0.1", "react-router-dom": "5.0.1", "vue-template-compiler": "^2.7.16", "xlsx": "^0.18.5"}, "devDependencies": {"@types/deep-diff": "^1.0.0", "@types/lodash": "^4.14.123", "@types/node": "^12.12.6", "@types/qs": "^6.5.3", "@types/react": "^17.0.38", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^17.0.11", "@types/react-router": "5.0.1", "@types/react-router-dom": "^5.3.3", "@types/xlsx": "^0.0.36", "acorn": "^8.8.2", "amis-widget-cli": "^3.2.0", "file-loader": "^6.2.0", "monaco-editor-webpack-plugin": "6.0.0", "prettier": "^2.2.1", "sass": "^1.63.4", "typescript": "^4.5.5"}}