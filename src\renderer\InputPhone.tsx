import React from 'react';
import {Renderer, addRule} from 'amis';
import {FormControlProps, FormItemProps} from 'amis-core';

// 注册验证规则 - 使用更宽松的手机号验证
// 确保与项目其他地方的验证规则保持一致
addRule('isPhoneNumber', function (value: any) {
  if (!value) {
    return true; // 允许空值，由required规则控制必填
  }
  // 中国手机号码规则：1开头，总共11位数字
  const isValid = typeof value === 'string' && /^1\d{10}$/.test(value);
  console.log('手机号验证:', value, '长度:', value?.length, '结果:', isValid);
  return isValid;
} as any, '请输入正确的手机号码');

addRule('isTelNumber', function (value: any) {
  if (!value) {
    return true; // 允许空值，由required规则控制必填
  }
  return typeof value === 'string' && /^(0\d{2,3}[-]?)?\d{7,8}$/.test(value);
} as any, '请输入正确的固定电话');

export interface InputPhoneProps extends FormControlProps {
  name: string;
  placeholder?: string;
  value?: string;
  clearable?: boolean;
  phoneType?: 'mobile' | 'landline'; // 电话类型：手机或固话
  // 支持扁平化的数据结构
  'addOn.enable'?: boolean;
  'addOn.type'?: 'text' | 'button' | 'submit';
  'addOn.position'?: 'left' | 'right';
  'addOn.text'?: string;
  'addOn.icon'?: string;
  'addOn.className'?: string;
  // 支持嵌套结构
  addOn?: {
    enable?: boolean;
    type?: 'text' | 'button' | 'submit';
    position?: 'left' | 'right';
    text?: string;
    icon?: string;
    className?: string;
  };
  validations?: {
    isPhoneNumber?: boolean;
    isTelNumber?: boolean;
    isUrl?: boolean;
    url?: string;
    [key: string]: any;
  };
  maxLength?: {
    enable?: boolean;
    length?: number;
    errorMessage?: string;
  } | number;
  minLength?: {
    enable?: boolean;
    length?: number;
    errorMessage?: string;
  } | number;
  trimContents?: boolean;
  borderMode?: 'full' | 'half' | 'none';
  showCounter?: boolean;
  autoFill?: any; // 自动填充API
  validateOnChange?: 'submit' | 'change' | 'blur';
}

@Renderer({
  type: 'input-phone',
  name: 'input-phone'
})
export class InputPhoneRenderer extends React.PureComponent<InputPhoneProps> {
  static defaultProps = {
    clearable: true,
    phoneType: 'mobile',
    showCounter: false,
    placeholder: '请输入',
    validations: {
      isPhoneNumber: true
    }
  };

  constructor(props: InputPhoneProps) {
    super(props);
  }

  render() {
    const props = this.props as any;
    const {
      name,
      placeholder,
      value,
      clearable,
      phoneType = 'mobile',
      addOn,
      trimContents,
      borderMode,
      showCounter,
      maxLength,
      minLength,
      render,
      validations = {},
      autoFill,
      size,
      remark,
      labelRemark,
      description,
      className,
      mode,
      id,
      label,
      required,
      disabled,
      validateOnChange,
      // ...rest
    } = props;

    // 构建基本配置
    const config: any = {
      type: 'input-text',
      id,
      name,
      label,
      placeholder: placeholder || (phoneType === 'mobile' ? '请输入手机号码' : '请输入固定电话号码'),
      clearable: clearable !== false,
      trimContents: trimContents !== false,
      borderMode: borderMode || 'full',
      showCounter: showCounter || false,
      autoFill,
      size,
      remark,
      labelRemark,
      description,
      className,
      mode,
      required,
      disabled,
      validateOnChange,
      // 手机号输入时通常使用数字键盘
      inputMode: 'tel',
    };

    // 处理默认值
    if (value !== undefined) {
      config.value = value;
    }

    // 处理最大长度配置
    if (typeof maxLength === 'object' && maxLength && maxLength.enable) {
      config.maxLength = maxLength.length;
      if (maxLength.errorMessage) {
        config.validations = config.validations || {};
        config.validations.maxLengthError = maxLength.errorMessage;
      }
    } else if (typeof maxLength === 'number') {
      config.maxLength = maxLength;
    }

    // 处理最小长度配置
    if (typeof minLength === 'object' && minLength && minLength.enable) {
      config.minLength = minLength.length;
      if (minLength.errorMessage) {
        config.validations = config.validations || {};
        config.validations.minLengthError = minLength.errorMessage;
      }
    } else if (typeof minLength === 'number') {
      config.minLength = minLength;
    }

    // 处理AddOn配置 - 支持扁平化的结构
    const addOnEnabled = props['addOn.enable'] || (addOn && addOn.enable);
    
    if (addOnEnabled) {
      config.addOn = {
        label: props['addOn.text'] || (addOn && addOn.text) || '按钮',
        icon: props['addOn.icon'] || (addOn && addOn.icon) || 'fa fa-phone',
        type: props['addOn.type'] || (addOn && addOn.type) || 'text',
        position: props['addOn.position'] || (addOn && addOn.position) || 'left',
        className: props['addOn.className'] || (addOn && addOn.className) || 'phone-addon'
      };
    }

    // 处理校验规则 - 使用直接的正则表达式验证
    const customValidations = { ...validations };

    // 根据电话类型应用不同的校验规则
    if (phoneType === 'mobile') {
      // 直接使用正则表达式验证，不依赖全局注册的规则
      customValidations.matchRegexp = '^1\\d{10}$';
      // 删除其他可能冲突的规则
      delete customValidations.isPhoneNumber;
      delete customValidations.isTelNumber;
    } else if (phoneType === 'landline') {
      // 固话验证
      customValidations.matchRegexp = '^(0\\d{2,3}[-]?)?\\d{7,8}$';
      // 删除其他可能冲突的规则
      delete customValidations.isPhoneNumber;
      delete customValidations.isTelNumber;
    }

    // 删除内部使用的标记，防止冲突
    delete customValidations.type;

    // 处理接口校验
    if (customValidations.isUrl && customValidations.url) {
      // 这里可以添加接口校验的逻辑
      // 例如：customValidations.remote = customValidations.url;
      delete customValidations.isUrl;
      delete customValidations.url;
    }
    
    // 将处理后的校验规则添加到配置中
    config.validations = config.validations || {};
    Object.assign(config.validations, customValidations);

    // 添加自定义错误消息
    config.validationErrors = config.validationErrors || {};
    if (phoneType === 'mobile') {
      config.validationErrors.matchRegexp = '请输入正确的手机号码';
    } else if (phoneType === 'landline') {
      config.validationErrors.matchRegexp = '请输入正确的固定电话';
    }

    if (render) {
      return render('input-text', config);
    }
    
    //兜底
    return null;
  }
}
