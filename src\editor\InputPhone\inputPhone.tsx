import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';
import {union} from 'lodash';
import {addRule} from 'amis';

// 添加手机号和固话验证规则
addRule('isPhoneNumber', function (value: any) {
  if (!value) {
    return true; // 允许空值，由required规则控制必填
  }
  // 中国手机号码规则：1开头，总共11位数字
  // 与项目中其他地方的验证规则保持一致
  return typeof value === 'string' && /^1\d{10}$/.test(value);
} as any, '请输入正确的手机号码');

addRule('isTelNumber', function (value: any) {
  if (!value) {
    return true; // 允许空值，由required规则控制必填
  }
  return typeof value === 'string' && /^(0\d{2,3}[-]?)?\d{7,8}$/.test(value);
} as any, '请输入正确的固定电话');

export class InputPhoneControlPlugin extends BasePlugin {
  static id = 'InputPhoneControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'input-phone';
  $schema = '/schemas/InputPhoneControlSchema.json';

  // 组件基本信息
  name = '手机号';
  panelTitle = '手机号';
  icon = 'fa fa-phone';
  panelIcon = 'fa fa-phone';
  pluginIcon = 'icon-phone-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于输入手机号码或固定电话';
  docLink = '/amis/zh-CN/components/form/input-phone';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'input-phone',
    label: '手机号',
    name: 'phone',
    clearable: true,
    placeholder: '请输入手机号码',
    phoneType: 'mobile', // 默认为手机号
    maxLength: 11,
    showCounter: true,
    required: false,
    validations: {
      isPhoneNumber: true
    }
  };

  // 添加事件定义
  events = [
    {
      eventName: 'change',
      eventLabel: '值变化',
      description: '输入内容变化时触发'
      // ...
    },
    {
      eventName: 'focus',
      eventLabel: '获得焦点',
      description: '输入框获得焦点时触发'
      // ...
    },
    {
      eventName: 'blur',
      eventLabel: '失去焦点',
      description: '输入框失去焦点时触发'
      // ...
    },
    {
      eventName: 'validateSuccess',
      eventLabel: '校验成功',
      description: '校验成功时触发'
    },
    {
      eventName: 'validateFail',
      eventLabel: '校验失败',
      description: '校验失败时触发'
    }
  ];

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                getSchemaTpl('formItemName', {
                  required: true
                }),
                getSchemaTpl('label'),
                {
                  type: 'button-group-select',
                  name: 'phoneType',
                  label: '类型',
                  value: 'mobile',
                  options: [
                    {
                      label: '手机',
                      value: 'mobile'
                    },
                    {
                      label: '固话',
                      value: 'landline'
                    }
                  ],
                  onChange: (value: string, oldValue: string, model: any, form: any) => {
                    // 根据电话类型更新验证规则
                    if (value === 'mobile') {
                      // 更新默认最大长度
                      form.setValueByName('maxLength', 11);
                      form.setValueByName('label',  '手机号');
                      form.setValueByName('placeholder',  '请输入手机号码');
                      
                      // 更新验证规则为手机号验证
                      form.setValueByName('validations', {
                        isPhoneNumber: true
                      });
                    } else {
                      // 固话验证
                      form.setValueByName('maxLength', 20); 
                      form.setValueByName('label',  '固定电话号码');
                      form.setValueByName('placeholder',  '请输入固定电话号码');
                      
                      // 更新验证规则为固话验证
                      form.setValueByName('validations', {
                        isTelNumber: true
                      });
                    }
                  }
                },
                getSchemaTpl('valueFormula'),
                getSchemaTpl('switch', {
                  name: 'clearable',
                  label: '可清除',
                  value: true
                }),
                getSchemaTpl('switch', {
                  name: 'showCounter',
                  label: '计数器',
                  value: false
                }),
                getSchemaTpl('maxLength', {
                  name: 'maxLength',
                  label: '最大字数',
                  value: 11
                }),
                getSchemaTpl('switch', {
                  name: 'addOn.enable',
                  label: 'AddOn',
                  value: false,
                  onChange: (value: boolean, oldValue: boolean, model: any, form: any) => {
                    if (value) {
                      form.setValues({
                        'addOn.type': 'text',
                        'addOn.position': 'left',
                        'addOn.text': '按钮',
                        'addOn.icon': 'fa fa-phone'
                      });
                    }
                  }
                }),
                {
                  type: 'container',
                  className: 'ae-ExtendMore',
                  visibleOn: 'data.addOn && data.addOn.enable',
                  body: [
                    {
                      type: 'button-group-select',
                      name: 'addOn.type',
                      label: '类型',
                      value: 'text',
                      options: [
                        {
                          label: '文本',
                          value: 'text'
                        },
                        {
                          label: '按钮',
                          value: 'button'
                        },
                        {
                          label: '提交',
                          value: 'submit'
                        }
                      ]
                    },
                    {
                      type: 'button-group-select',
                      name: 'addOn.position',
                      label: '位置',
                      value: 'left',
                      options: [
                        {
                          label: '左边',
                          value: 'left'
                        },
                        {
                          label: '右边',
                          value: 'right'
                        }
                      ]
                    },
                    {
                      type: 'input-text',
                      name: 'addOn.text',
                      label: '文字',
                      value: '按钮'
                    },
                    {
                      type: 'icon-picker',
                      name: 'addOn.icon',
                      label: '图标',
                      placeholder: '点击选择图标',
                      value: 'fa fa-phone'
                    }
                  ]
                },
                getSchemaTpl('labelRemark'),
                getSchemaTpl('remark'),
                getSchemaTpl('placeholder', {
                  label: '占位提示',
                  value: '请输入'
                }),
                getSchemaTpl('description'),
                getSchemaTpl('autoFillApi')
              ]
            },
            
            getSchemaTpl('status', {
              isFormItem: true,
              readonly: true
            }),
            {
              title: '校验',
              body: [
                getSchemaTpl('switch', {
                  name: 'validations.isPhoneNumber',
                  label: '手机号校验',
                  value: true,
                  disabled: true,
                  visibleOn: "data.phoneType === 'mobile'"
                }),
                getSchemaTpl('switch', {
                  name: 'validations.isTelNumber',
                  label: '固话校验',
                  value: true,
                  disabled: true,
                  visibleOn: "data.phoneType === 'landline'"
                }),
                getSchemaTpl('required', {
                  name: 'required',
                  label: '必填',
                  value: false
                }),
                getSchemaTpl('validationApiControl'),
                {
                  type: 'select',
                  name: 'validateOnChange',
                  label: '校验触发',
                  value: 'default',
                  clearable: true,
                  options: [
                    {
                      label: '提交后每次修改修改即触发',
                      value: 'default'
                    },
                    {
                      label: '修改即触发',
                      value: true
                    },
                    {
                      label: '提交触发',
                      value: false
                    }
                  ],
                  pipeIn: (value: any) => {
                    if (value === undefined || value === null) {
                      return 'default';
                    }
                    return value;
                  },
                  pipeOut: (value: any) => {
                    if (value === 'default') {
                      return undefined;
                    }
                    return value;
                  }
                }
              ]
            },
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          getSchemaTpl('theme:common', {
            exclude: ['layout']
          }),
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(InputPhoneControlPlugin);
