{"name": "java-amis项目规则", "description": "用于基于amis框架的React+TypeScript项目的规则", "languages": ["typescript", "javascript", "tsx", "jsx", "scss", "css", "html", "json"], "rules": [{"name": "组件命名规则", "description": "React组件应使用PascalCase命名", "pattern": "src/component/*.{tsx,jsx}", "rule": "文件名应使用PascalCase命名方式，确保与组件名称一致"}, {"name": "TypeScript类型定义", "description": "使用TypeScript接口或类型来定义组件Props和State", "pattern": "src/**/*.tsx", "rule": "为每个组件定义明确的Props和State类型，避免使用any类型"}, {"name": "AMIS Schema规范", "description": "AMIS配置应遵循schema规范", "pattern": "src/**/*.{tsx,jsx,ts,js}", "rule": "AMIS配置对象应具有明确的类型，并遵循AMIS官方文档的schema规范"}, {"name": "路由定义规范", "description": "路由定义应清晰且一致", "pattern": "src/route/**/*.{tsx,ts,jsx,js}", "rule": "路由定义应包含必要的元数据，如title、权限等"}, {"name": "样式规范", "description": "样式应使用SCSS并遵循BEM命名方式", "pattern": "src/scss/**/*.scss", "rule": "使用BEM命名方式；避免使用!important；使用变量定义颜色和尺寸"}, {"name": "工具函数规范", "description": "工具函数应有良好的文档和类型定义", "pattern": "src/utils/**/*.{ts,js}", "rule": "每个工具函数应有JSDoc文档和明确的参数与返回类型定义"}, {"name": "状态管理规范", "description": "状态管理应使用MobX并遵循一致的模式", "pattern": "src/store/**/*.{ts,js}", "rule": "使用MobX进行状态管理；状态变更应通过action处理；避免直接修改状态"}, {"name": "API调用规范", "description": "API调用应集中管理", "pattern": "src/**/*.{ts,js,tsx,jsx}", "rule": "API调用应使用axios并集中在特定服务模块中；处理API错误和加载状态"}, {"name": "编辑器组件规范", "description": "AMIS编辑器组件应遵循特定规范", "pattern": "src/editor/**/*.{tsx,jsx,ts,js}", "rule": "遵循AMIS编辑器的扩展规范；组件应包含必要的定义和配置功能"}, {"name": "自定义渲染器规范", "description": "自定义渲染器应遵循AMIS规范", "pattern": "src/renderer/**/*.{tsx,jsx,ts,js}", "rule": "自定义渲染器应实现必要的接口；注册到AMIS环境中；包含适当的文档"}, {"name": "代码格式化规范", "description": "代码应遵循统一的格式化规范", "pattern": "src/**/*.{ts,js,tsx,jsx,scss,css}", "rule": "使用项目配置的Prettier规则格式化代码；遵循ESLint规则"}]}