.layoutRenderAside {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  border-right: 1px solid var(--borderColor);
  background-color: var(--body-bg);
  transition: background-color 0.3s;

  // &::-webkit-scrollbar {
  //   width: 2px;
  //   height: 2px;
  //   background-color: var(--borderColor);
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--colors-neutral-fill-6);
    border-radius: 3px;
  }

  &-menu {
    position: relative;
    margin-bottom: 2.5rem;
    width: 100%;
    padding: 10px 0;

    &-item {
      position: relative;
      width: calc(100% - 1rem);
      height: 40px;
      display: flex;
      align-items: center;
      margin: 4px 0.5rem;
      border-radius: 6px;
      padding: 12px 8px;
      cursor: pointer;
      color: var(--colors-neutral-text-3);
      transition: all 0.3s;

      &:hover {
        background-color: var(--colors-neutral-fill-9);
        color: var(--colors-brand-5);

        .LayoutRenderAside-menu-item-name {
          color: var(--colors-brand-5);
        }
      }

      &.itemClick {
        background-color: var(--colors-neutral-fill-12);
        color: var(--colors-brand-5);

        .LayoutRenderAside-menu-item-name {
          color: var(--colors-brand-5);
          font-weight: 500;
        }
      }

      &-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        text-align: center;
        transition: color 0.3s;

        &-teamImage {
          width: 20px;
          height: 20px;
          border-radius: 4px;
        }

        &-teamIcon {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background-color: var(--primary);
        }
      }

      &-name {
        flex: 1;
        margin-left: 8px;
        font-size: 14px;
        transition: color 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-arrow {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        i {
          font-size: 12px;
          transition: transform 0.3s;

          &.open {
            transform: rotate(90deg);
          }
        }
      }
    }
  }

  &-subitem {
    width: calc(100% - 1rem);
    height: 2.5rem;
    display: flex;
    align-items: center;
    margin: 0 0.5rem 0 0.5rem;
    border-radius: 0.5rem;
    padding-left: 3.5rem;
    padding-right: 0.5rem;
    cursor: default;

    &:hover {
      background-color: var(--light-bg);
    }

    &-name {
      flex: 1;
      padding: 0.25rem;
    }
  }

  &-submenu {
    overflow: hidden;
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding-left: 20px;

    &-item {
      @extend .LayoutRenderAside-menu-item;
      height: 36px;
      margin: 2px 0.5rem;
      padding: 0 12px;
      font-size: 13px;
    }
  }
}

// 暗黑模式
[data-theme='dark'] .LayoutRenderAside {
  background-color: var(--colors-neutral-fill-10);

  &::-webkit-scrollbar-thumb {
    background: var(--colors-neutral-fill-4);
  }

  &-menu-item,
  &-submenu-item {
    color: var(--colors-neutral-text-2);

    &:hover {
      background-color: var(--colors-neutral-fill-9);
      color: var(--colors-brand-5);

      .LayoutRenderAside-menu-item-name {
        color: var(--colors-brand-5);
      }
    }

    &.itemClick {
      background-color: var(--colors-brand-9);
      color: var(--colors-brand-5);

      .LayoutRenderAside-menu-item-name {
        color: var(--colors-brand-5);
      }
    }
  }
}

.arrowDown {
  transform: rotate(90deg);
}

.itemClick {
  background-color: var(--light-bg);
}


.createTeamviews {
  position: fixed;
  bottom: 0;
  height: 2.5rem;
  width: 240px;
  left: 0;
  border-right: 1px solid var(--borderColor);
  border-top: 1px solid rgba($color: #000000, $alpha: 0.08);
  background-color: var(--background);
  box-sizing: border-box;

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
     background-color: var(--colors-neutral-fill-9);
     color: var(--colors-brand-5);
  }

  &-icon {
    font-size: 1.125rem;
    // color: rgba($color: #000000, $alpha: 0.45);

    &:hover {
      background-color: var(--colors-neutral-fill-9);
      color: var(--colors-brand-5);
   }
  }

  &-name {
    font-size: 0.875rem;
    margin-left: 0.5rem;
  }
}

.displayNone {
  display: none !important;
}