
export const EditNameDialog = (show: boolean, currentName?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑姓名</b>'
    },
    showCloseButton: false,
    data: {
      realname: currentName
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入真实姓名'
      },
      body: [
        {
          type: 'input-text',
          name: 'realname',
          placeholder: '真实姓名',
          required: true,
          maxLength: 20,
          validations: {
            maxLength: 20
          },
          inputClassName: 'name-input',
          labelClassName: 'name-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 