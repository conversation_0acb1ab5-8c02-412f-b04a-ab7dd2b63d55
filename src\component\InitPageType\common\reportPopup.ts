// 二次删除团队
const reportPopup = () => {
  return {
    type: 'dialog',
    body: [
      {
        id: 'u:d717906b428d',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'input-text',
            label: '名称',
            name: 'name',
            row: 0,
            id: 'u:7f7cd9abac87',
            placeholder: '请输入名称',
            required: true
          },
          {
            type: 'input-number',
            label: '序号',
            name: 'orderNum',
            keyboard: true,
            row: 1,
            id: 'u:7f7cd9abac87',
            placeholder: '请输入序号',
            required: true
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'u:d717906b428d'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: true
      }
    ],
    title: '创建报表',
    id: 'u:2aac8a329856',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:d2f9d50b6428'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'u:50f63b7790e7',
        onEvent: {
          click: {
            weight: 0,
            actions: []
          }
        }
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '创建报表'
    },
    $$ref: 'modal-ref-2'
  };
};

export {reportPopup};
