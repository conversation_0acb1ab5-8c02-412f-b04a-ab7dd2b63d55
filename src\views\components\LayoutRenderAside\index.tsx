import React, {FC, useEffect} from 'react';
import './index.scss';
import AMISRenderer from '@/component/AMISRenderer';
import {CreateTeamPopup} from '@/utils/schemaDataSet/CreateTeamPopup';
import {CreateProjectSchema} from '@/utils/schemaDataSet/CreateProjectPopup';
import {updateTeamProject} from '@/utils/api/api';
const LayoutRenderAside: FC<any> = (props: any) => {
  // 是否展开（可以一堆）
  const [isOpenSub, setIsOpenSub] = React.useState<string[]>([]);
  // 是否选中（单个）
  const [checkedNav, setCheckedNav] = React.useState<any>(null);
  // 菜单列表
  const [menuList, setMenuList] = React.useState<any>([]);
  // 是否打开创建项目弹窗
  const [createTeamOpen, setCreateTeamOpen] = React.useState<boolean>(false);

  // 当前鼠标移入的团队ID
  const [hoverTeamId, setHoverTeamId] = React.useState<any>('');

  // 创建项目弹窗
  const [createProjectOpen, setCreateProjectOpen] =
    React.useState<boolean>(false);
  // 创建项目ID
  const [createProjectId, setCreateProjectId] = React.useState<any>('');

  // 编辑项目名称功能
  // 选中项目的Item
  const [activeProjectItem, setActiveProjectItem] = React.useState<any>(false);
  // 是否开启输入框模式
  const [editProjectInput, setEditProjectInput] =
    React.useState<boolean>(false);
  // 编辑项目名称的item
  const [editProjectItem, setEditProjectItem] = React.useState<any>(false);
  // 编辑项目名称的名称
  const [editProjectName, setEditProjectName] = React.useState<any>('');

  // 创建团队弹窗
  const openCreateTeam = () => {
    setCreateTeamOpen(true);
  };

  const openCreateProjectPopup = (id: any) => {
    setCreateProjectId(id);
    setCreateProjectOpen(true);
  };

  // 创建项目完成
  const createProject = (val: any) => {
    setCreateProjectOpen(false);
    props.updateMenuData();
  };

  // 双击开打输入框模式
  const onEditProjectInput = (e: any, item: any) => {
    if(item.type === 'appbuild'){
      return;
    }
    console.log(e.detail);
    // e.stopPropagation();
    if (e.detail === 2) {
      e.stopPropagation();
      setEditProjectInput(true);
      setEditProjectItem(item);
      setEditProjectName(item.name);
    }
  };
  // 保存编辑项目名称
  const saveEditPageName = () => {
    console.log('editProjectName', editProjectName);
    if (
      editProjectName.trim().length === 0 ||
      editProjectName.trim() === editProjectItem.name
    ) {
      setEditProjectInput(false);
      setEditProjectItem(false);
      setEditProjectName('');
      return;
    }

    let data = {
      id: editProjectItem.id,
      name: editProjectName
    };

    updateTeamProject(data).then((res: any) => {
      if (res.code === 0) {
        props.updateMenuData();
        setTimeout(() => {
          setEditProjectInput(false);
          setEditProjectItem(false);
          setEditProjectName('');
        }, 200);
      }
    });
  };

  // 初始化时,选中的菜单项
  const handleInitCheckedNav = (params: any) => {
    if (!params) return;
    if (params.menu) {
      setCheckedNav(() => {
        // 初始化选中项
        let initCheckedNav = null;
        menuList.forEach((navItem: any) => {
          if (navItem.path === params.menu) {
            initCheckedNav = navItem;
          }
        });
        return initCheckedNav;
      });
    } else {
      if (params.project) {
        setCheckedNav(() => {
          // 初始化选中项
          let initCheckedNav = null;
          menuList.forEach((teamItem: any) => {
            if (teamItem.id == params.team) {
              handleSubMenu(null, params.team);
              teamItem.children.forEach((projectItem: any) => {
                if (projectItem.id == params.project) {
                  initCheckedNav = projectItem;
                }
              });
            }
          });
          return initCheckedNav;
        });
      } else {
        setCheckedNav(() => {
          // 初始化选中项
          let initCheckedNav = null;
          menuList.forEach((teamItem: any) => {
            if (teamItem.id == params.team) {
              initCheckedNav = teamItem;
            }
          });
          return initCheckedNav;
        });
      }
    }
  };

  // 处理菜单展开/收起
  const handleSubMenu = (event: any, id: string | number) => {
    if (event) {
      event?.stopPropagation();
    }
    // 确保id总是字符串类型
    const idStr = id.toString();
    setIsOpenSub(prev =>
      prev.includes(idStr)
        ? prev.filter(item => item !== idStr)
        : [...prev, idStr]
    );
  };

  // 处理菜单点击
  const handleNavClick = (item: any) => {
    setCheckedNav(item);
    if (item.type) {
      console.log('item.type', item.type);
      console.log('item.path', item.path);
      props.history.push(`/${item.type}/${item.path}`);
    } else {
      if (item.parentId && item.parentId != 0) {
        props.history.push(
          `/${menuList[0].type}/team${item.parentId}/project${item.id}`
        );
      } else {
        props.history.push(`/${menuList[0].type}/team${item.id}`);
      }
    }
  };

  /* created 初始化 */
  useEffect(() => {
    if (props.menuList && props.menuList.length > 0) {
      setMenuList(props.menuList);
    }
  }, [props.menuList]);

  useEffect(() => {
    if (menuList.length > 0) {
      handleInitCheckedNav(props.match.params);
    }
  }, [menuList]);

  /* created 初始化 end */

  return (
    <div className="layoutRenderAside">
      <div className="layoutRenderAside-menu">
        {menuList?.map((item: any) => (
          <div key={item.id}>
            <div
              onMouseOver={() => setHoverTeamId(item.id)}
              onMouseOut={() => setHoverTeamId(-1)}
              className={`layoutRenderAside-menu-item${
                checkedNav?.id === item.id ? ' itemClick' : ''
              }`}
              onClick={() => handleNavClick(item)}
            >
              {/* 是否展开 */}
              <div
                className="layoutRenderAside-menu-item-arrow"
                onClick={event => handleSubMenu(event, item.id)}
              >
                {item.children && item.children.length > 0 && (
                  <i
                    className={`fa fa-angle-right${
                      isOpenSub.includes(item.id.toString()) ? ' open' : ''
                    }`}
                  ></i>
                )}
              </div>
              {/* 图标 */}
              <div className="layoutRenderAside-menu-item-icon">
                {item.type === 'appbuild' || item.type === 'platform' ? (
                  <i className={item.icon}></i>
                ) : item.icon ? (
                  <img
                    className="layoutRenderAside-menu-item-icon-teamImage"
                    src={item.icon}
                  />
                ) : (
                  <div className="layoutRenderAside-menu-item-icon-teamIcon"></div>
                )}
              </div>
              {/* 名称 */}
              {editProjectInput && editProjectItem.id == item.id ? (
                <div className="menuviews-groupItem-name">
                  <input
                    className="menuviews-groupItem-name-input"
                    type="text"
                    value={editProjectName}
                    autoFocus
                    onChange={e => {
                      setEditProjectName(e.target.value);
                    }}
                    onBlur={() => saveEditPageName()}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.keyCode === 13) {
                        saveEditPageName();
                      }
                    }}
                  />
                </div>
              ) : (
                <div
                  className="layoutRenderAside-menu-item-name"
                  onDoubleClick={e => onEditProjectInput(e, item)}
                >
                  {item.name}
                </div>
              )}

              <div
                className={
                  props.store.userInfo?.hasAppBuildAuth && !editProjectInput && item.type !== 'appbuild' && item.type !== 'platform' && hoverTeamId == item.id
                    ? 'menuviews-item-add'
                    : 'displayNone'
                }
                onClick={() => openCreateProjectPopup(item.id)}
              >
                <i className="fas fa-plus menuviews-item-add-icon"></i>
              </div>
            </div>
            {/* 子菜单 */}
            {item.children &&
              item.children.length > 0 &&
              isOpenSub.includes(item.id.toString()) && (
                <div className="layoutRenderAside-submenu">
                  {item.children.map((subItem: any) => (
                    <div
                      key={subItem.id}
                      className={`layoutRenderAside-menu-item${
                        checkedNav?.id === subItem.id ? ' itemClick' : ''
                      }`}
                      onClick={() => handleNavClick(subItem)}
                    >
                      <div className="layoutRenderAside-menu-item-icon">
                        <i className={subItem.icon}></i>
                      </div>
                      <div className="layoutRenderAside-menu-item-name">
                        {subItem.name}
                      </div>
                      {subItem.children && subItem.children.length > 0 && (
                        <div className="layoutRenderAside-menu-item-arrow">
                          <i
                            className={`fa fa-angle-right${
                              isOpenSub.includes(subItem.id.toString())
                                ? ' open'
                                : ''
                            }`}
                          ></i>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
          </div>
        ))}
      </div>

      {props.showCreateTeam && props.store.userInfo?.hasAppBuildAuth && (
        <div className="createTeamviews" onClick={openCreateTeam}>
          <div>
            <i className="fas fa-plus createTeamviews-icon"></i>
          </div>
          <div className="createTeamviews-name">创建团队</div>
        </div>
      )}

      {/* 创建团队弹窗：CreateTeamPopup */}
      <AMISRenderer
        schema={CreateTeamPopup()}
        show={createTeamOpen}
        onClose={() => setCreateTeamOpen(false)}
        onConfirm={() => {
          setCreateTeamOpen(false);
          props.updateMenuData();
        }}
      />
      {/* 创建项目弹窗：CreateProjectSchema */}
      <AMISRenderer
        show={createProjectOpen}
        onClose={() => setCreateProjectOpen(false)}
        onConfirm={(val: any) => createProject(val)}
        schema={CreateProjectSchema(createProjectId)}
      />
    </div>
  );
};

export default LayoutRenderAside;
