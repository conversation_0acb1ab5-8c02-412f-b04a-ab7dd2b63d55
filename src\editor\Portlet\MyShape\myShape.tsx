import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class MyShapePlugin extends BasePlugin {
  static id = 'MyShapePlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'shape';
  $schema = '/schemas/MyShapeSchema.json';

  // 组件基本信息
  name = '图形';
  panelTitle = '图形组件';
  icon = 'fa fa-shapes';
  panelIcon = 'fa fa-shapes';
  pluginIcon = 'fa fa-shapes';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于展示各种图形，如矩形、圆形、三角形等';
  docLink = '/amis/zh-CN/components/shape';
  tags = ['门户'];

  // 组件默认配置
  scaffold = {
    type: 'shape',
    shapeType: 'circle',
    radius: 5,
    width: 100,
    height: 100,
    color: '#1867c0'
  };

  // 预览界面
  previewSchema = {
    type: 'shape',
    className: 'text-left',
    shapeType: 'circle',
    radius: 5,
    width: 50,
    height: 50,
    color: '#1867c0'
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                {
                  type: 'input-text',
                  name: 'className',
                  label: 'CSS类名',
                  description: '自定义CSS类名'
                },
                {
                  type: 'select',
                  name: 'shapeType',
                  label: '图形类型',
                  value: 'circle',
                  options: [
                    {label: '圆形', value: 'circle'},
                    {label: '矩形', value: 'square'},
                    {label: '三角形', value: 'triangle'},
                    {label: '菱形', value: 'diamond'},
                    {label: '水滴形', value: 'water-drop'},
                    {label: '自定义', value: 'custom'}
                  ],
                  description: '选择图形类型'
                },
                {
                  type: 'input-number',
                  name: 'width',
                  label: '宽度',
                  value: 100,
                  description: '图形宽度'
                },
                {
                  type: 'input-number',
                  name: 'height',
                  label: '高度',
                  value: 100,
                  description: '图形高度'
                },
                {
                  type: 'input-number',
                  name: 'radius',
                  label: '圆角大小',
                  value: 5,
                  min: 1,
                  max: 10,
                  description: '圆角大小，范围1~10'
                },
                {
                  type: 'input-color',
                  name: 'color',
                  label: '颜色',
                  value: '#1867c0',
                  description: '图形填充颜色'
                },
                {
                  type: 'combo',
                  name: 'paths',
                  label: '自定义路径',
                  multiple: true,
                  visibleOn: 'this.shapeType === "custom"',
                  items: [
                    {
                      type: 'input-text',
                      name: 'path',
                      label: 'SVG路径',
                      required: true,
                      description: 'SVG路径数据'
                    }
                  ],
                  description: '自定义SVG路径，仅在图形类型为"自定义"时有效'
                }
              ]
            },
            {
              title: '边框设置',
              body: [
                {
                  type: 'input-color',
                  name: 'stroke',
                  label: '边框颜色',
                  description: '图形边框颜色'
                },
                {
                  type: 'input-number',
                  name: 'strokeWidth',
                  label: '边框宽度',
                  description: '图形边框宽度'
                },
                {
                  type: 'select',
                  name: 'strokeType',
                  label: '边框类型',
                  options: [
                    {label: '实线', value: 'line'},
                    {label: '虚线', value: 'dash'},
                    {label: '点线', value: 'dot'}
                  ],
                  description: '图形边框类型'
                }
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '样式设置',
              body: [
               
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(MyShapePlugin);
