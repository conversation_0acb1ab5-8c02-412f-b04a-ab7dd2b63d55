import {post} from '@/utils/services/dashboardRequest';
import {get} from '@/utils/services/dashboardRequest';


// 数据集列表
interface datasetPageData {
  applyId: string | number;
  current: number;
  size: number;
}
const datasetPage = (data: datasetPageData): Promise<any> =>
  get('/dataset/page', data);
export {datasetPage};

// 数据集详情
interface datasetInfoData {
  id: string | number;
}
const datasetInfo = (data: datasetInfoData): Promise<any> =>
  get(`/dataset/datasetInfo/${data.id}`);
export {datasetInfo};

