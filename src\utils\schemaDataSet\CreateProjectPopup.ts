// 创建项目弹窗
const CreateProjectSchema = (teamId: any) => {
  return {
    type: 'dialog',
    body: [
      {
        type: 'container',
        body: [
          {
            type: 'tpl',
            tpl: '创建项目',
            inline: true,
            wrapperComponent: '',
            id: 'u:1eafdeaa66ea',
            themeCss: {
              baseControlClassName: {
                'font:default': {
                  fontSize: '1.5rem',
                  fontWeight: 'var(--fonts-weight-4)'
                },
                'padding-and-margin:default': {
                  marginBottom: '0.75rem'
                }
              }
            }
          },
          {
            type: 'tpl',
            tpl: '项目可独立管理该项目下的应用',
            inline: true,
            wrapperComponent: '',
            id: 'u:bce13c037362',
            themeCss: {
              baseControlClassName: {
                'font:default': {
                  color: '#999999',
                  fontSize: '0.875rem',
                  fontWeight: 'var(--fonts-weight-5)'
                },
                'padding-and-margin:default': {
                  marginBottom: '2.5rem'
                }
              }
            }
          },
          {
            id: 'u:87295a3e7321',
            type: 'form',
            title: '',
            mode: 'flex',
            labelAlign: 'top',
            dsType: 'api',
            feat: 'Insert',
            body: [
              {
                name: 'text_1731307771159',
                label: '项目名称',
                required: true,
                row: 0,
                type: 'input-text',
                id: 'u:2a9b33011443'
              },
              {
                name: 'textarea_1731307833062',
                label: '描述（可选）',
                row: 1,
                type: 'textarea',
                id: 'u:454d938135f4'
              }
            ],
            actions: [
              {
                type: 'button',
                label: '提交',
                onEvent: {
                  click: {
                    actions: [
                      {
                        actionType: 'submit',
                        componentId: 'u:87295a3e7321'
                      }
                    ]
                  }
                },
                level: 'primary',
                id: 'u:be83c76a460a'
              }
            ],
            resetAfterSubmit: true,
            canAccessSuperData: false,
            wrapperCustomStyle: {
              root: {
                'width': '100%',
                'box-shadow': 'none!important'
              }
            },
            themeCss: {
              panelClassName: {
                'border:default': {
                  'top-border-width': 'var(--borders-width-1)',
                  'left-border-width': 'var(--borders-width-1)',
                  'right-border-width': 'var(--borders-width-1)',
                  'bottom-border-width': 'var(--borders-width-1)'
                }
              },
              headerControlClassName: {
                'border:default': {
                  'top-border-width': 'var(--borders-width-1)',
                  'left-border-width': 'var(--borders-width-1)',
                  'right-border-width': 'var(--borders-width-1)',
                  'bottom-border-width': 'var(--borders-width-1)'
                }
              },
              bodyControlClassName: {
                'padding-and-margin:default': {
                  paddingTop: 'var(--sizes-size-0)',
                  paddingRight: 'var(--sizes-size-0)',
                  paddingBottom: 'var(--sizes-size-0)',
                  paddingLeft: 'var(--sizes-size-0)'
                }
              },
              actionsControlClassName: {
                'border:default': {
                  'top-border-width': 'var(--borders-width-1)',
                  'left-border-width': 'var(--borders-width-1)',
                  'right-border-width': 'var(--borders-width-1)',
                  'bottom-border-width': 'var(--borders-width-1)'
                }
              }
            },
            api: {
              method: 'post',
              requestAdaptor: '',
              adaptor: '',
              messages: {},
              data: {
                description: '${textarea_1731307833062}',
                name: '${text_1731307771159}',
                parentId: teamId
              },
              url: '/admin-api/system/team-and-project/create'
            },
            __origin: 'scaffold'
          }
        ],
        style: {
          position: 'relative',
          display: 'flex',
          inset: 'auto',
          flexWrap: 'nowrap',
          flexDirection: 'column',
          alignItems: 'flex-start'
        },
        size: 'none',
        wrapperBody: false,
        id: 'u:661593394ef7',
        isFixedHeight: false,
        isFixedWidth: false,
        themeCss: {
          baseControlClassName: {
            'background:default': 'var(--colors-neutral-fill-11)',
            'padding-and-margin:default': {
              paddingLeft: '1.5rem',
              paddingRight: '1.5rem'
            },
            'radius:default': {
              'top-left-border-radius': '16px',
              'bottom-left-border-radius': '16px'
            }
          }
        },
        __editorStatebaseControlClassName: 'default'
      }
    ],
    title: '',
    id: 'u:b2a84191c34c',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:bb9a6e447d20'
      },
      {
        type: 'button',
        actionType: 'submit',
        label: '确定',
        primary: true,
        id: 'u:49b63f191394'
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    hideActions: true,
    $$ref: 'modal-ref-1',
    editorSetting: {
      displayName: '创建项目'
    },
    themeCss: {
      dialogClassName: {
        border: {}
      }
    }
  };
};
export {CreateProjectSchema};
