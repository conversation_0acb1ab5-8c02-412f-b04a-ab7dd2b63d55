// 修改数据源弹窗
const modifyDataSetPopup = (apply_id: string) => {
  return {
    type: 'dialog',
    title: '修改数据源',
    body: [
      {
        id: 'u:945e0976447b',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'select',
            label: '',
            name: 'dataSetId',
            placeholder: '请选择需要导入的数据源',
            source: {
              method: 'get',
              data: {
                pageSize: 100,
                pageNo: 1
              },
              url: '/admin-api/system/data-set/page?applicantOrBackend=2&pageNo=1&pageSize=100',
              responseData: {
                options: "${list|pick:label~name,value~id}"
              }
            }            // options: [
            //     {
            //         label: '数据源1',
            //         value: '1'
            //     },
            //     {
            //         label: '数据源2',
            //         value: '2'
            //     },
                
            // ]
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'u:945e0976447b'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: false,
        closeDialogOnSubmit: true
      }
    ],
    id: 'u:be54fbc63e4a',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:1835cdeb9cf0'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'u:5f6513bbc44e'
      }
    ],
    showCloseButton: false,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '创建页面'
    },
    themeCss: {
      dialogClassName: {
        radius: {
          'top-left-border-radius': '8px',
          'top-right-border-radius': '8px',
          'bottom-left-border-radius': '8px',
          'bottom-right-border-radius': '8px'
        }
      }
    }
  };
};

export {modifyDataSetPopup};
