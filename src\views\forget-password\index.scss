.forget-password-page {
  width: 460px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: var(--boxShadow);

  .form-content {
    padding: 30px;
    width: 420px;
    border: 1px dashed var(--borderColor);
    margin: 20px;
  }

  .page-title {
    font-size: 30px;
    color: var(--text-color);
    margin-bottom: 30px;
    font-weight: bold;
    text-align: center;
  }

  .form-content {
    .form-item {
      margin-bottom: 30px;

      .form-control {
        width: 100%;
        height: 40px;
        padding: 10px;
        background: var(--light-bg);
        border-width: 1px;
        border-style: solid;
        border-color: var(--colors-neutral-fill-none);
        border-radius: 4px;
        font-size: 14px;
        color: var(--text-color);

        &:hover:not(:disabled) {
          border-color: var(--primary);
          color: var(--primary);
        }

        &::placeholder {
          color: var(--text--muted-color);
        }

        &:focus {
          border-color: var(--primary);
          outline: none;
        }
      }
    }

    .next-step-btn {
      height: 40px;
      font-size: 14px;
      margin-bottom: 20px;
      background: var(--primary);
      border: none;
      border-radius: 4px;
      color: var(--button-primary-default-font-color);
    }

    .login-divider {
      text-align: center;
      color: var(--text--muted-color);
      font-size: 14px;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: calc(50% - 20px);
        height: 0.1px;
        background: var(--borderColor);
      }

      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    .bottom-links {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      font-size: 14px;

      a {
        color: var(--text--muted-color);
        text-decoration: none;
        cursor: pointer;
      }
    }
  }
} 