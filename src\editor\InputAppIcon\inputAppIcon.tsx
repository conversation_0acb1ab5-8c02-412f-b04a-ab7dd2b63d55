import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class AppIconControlPlugin extends BasePlugin {
  static id = 'AppIconControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'input-appicon';
  $schema = '/schemas/AppIconControlSchema.json';

  // 组件基本信息
  name = '应用图标';
  panelTitle = '应用图标';
  icon = 'fa fa-picture-o';
  panelIcon = 'fa fa-picture-o';
  pluginIcon = 'icon-image-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '上传应用图标';
  docLink = '/amis/zh-CN/components/form/input-image';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'input-appicon',
    label: '应用图标',
    name: 'appIcon',
    clearable: true,
    placeholder: '请选择应用图标',
    accept: 'image/jpeg,image/jpg,image/png,image/svg+xml',
    crop: false,
    uploadBtnText: ' ',
    width: 80,
    height: 80,
    borderRadius: '8px', // 默认为圆角矩形
    uploadType: 'fileReceptor',
    autoUpload: true,
    proxy: true,
    multiple: false,
    frameImage: '', // 添加占位图片
  };

  // 添加事件定义
  events = [
    {
      eventName: 'click',
      eventLabel: '点击事件',
      description: '点击图标时触发'
    },
    {
      eventName: 'change',
      eventLabel: '值变化',
      description: '图标变化时触发'
    },
    {
      eventName: 'success',
      eventLabel: '上传成功',
      description: '图标上传成功时触发'
    },
    {
      eventName: 'fail',
      eventLabel: '上传失败',
      description: '图标上传失败时触发'
    },
    {
      eventName: 'remove',
      eventLabel: '移除图标',
      description: '移除图标时触发'
    }
  ];

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                getSchemaTpl('formItemName', {
                  required: true
                }),
                getSchemaTpl('label'),
                getSchemaTpl('required'),
                getSchemaTpl('title', {
                  name: 'accept',
                  label: '图片类型',
                  value: 'image/jpeg,image/jpg,image/png,image/svg+xml',
                }),
                getSchemaTpl('textareaFormulaControl', {
                  name: 'frameImage',
                  label: '背景图片地址',
                  placeholder: '请输入图片URL地址',
                  description: '设置图标容器的背景图片，图标将显示在背景图片上方'
                }),
                // getSchemaTpl('switch', {
                //   name: 'crop',
                //   label: '裁剪功能',
                //   value: false,
                // }),
                // getSchemaTpl('switch', {
                //   name: 'clearable',
                //   label: '可清除',
                //   value: true,
                // })
              ]
            },
            getSchemaTpl('status', {
              isFormItem: true,
              readonly: true
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '图标设置',
              body: [
                getSchemaTpl('layout:width:v2', {
                  name: 'borderRadius',
                  label: '圆角设置',
                  value: '8px',
                  unitOptions: ['px', '%'],
                }),
                getSchemaTpl('style:widthHeight', {
                  widthSchema: {
                    label: '图标宽度',
                    value: 80
                  },
                  heightSchema: {
                    label: '图标高度',
                    value: 80
                  }
                }),
              ]
            },
            // getSchemaTpl('theme:form-label'),
            // getSchemaTpl('theme:form-description'),
            {
              title: '标签样式',
              body: [
                ...inputStateTpl(
                  'themeCss.labelClassName',
                  '--form-item-base'
                )
              ]
            },
            {
              title: '标题样式',
              body: [
                ...inputStateTpl(
                  'themeCss.titleClassName',
                  '--form-label-base'
                )
              ]
            },
            {
              title: '上传区域样式',
              body: [
                ...inputStateTpl(
                  'themeCss.addBtnControlClassName',
                  '--inputImage-base'
                ),
                // 图标选择
                {
                  type: 'icon-select',
                  name: 'themeCss.addBtnControlClassName.--inputImage-base-default-icon',
                  label: '图标选择',
                  description: '设置组件的图标',
                  returnSvg: true
                },
                // 图标颜色
                getSchemaTpl('theme:colorPicker', {
                  name: 'themeCss.addBtnControlClassName.icon-color:default',
                  label: '图标颜色'
                }),
                // 图标大小
                getSchemaTpl('layout:width:v2', {
                  name: 'themeCss.addBtnControlClassName.iconSize',
                  label: '图标大小',
                  value: '24px',
                  unitOptions: ['px', 'rem', 'em']
                })
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(AppIconControlPlugin);
