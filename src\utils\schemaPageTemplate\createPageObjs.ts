import { height } from '@/editor/EChartsEditor/Common';
import IFrame from 'amis/lib/renderers/IFrame';

interface Page {
  pageType?: string;
  pageRoute?: string;
  type?: string;
  regions?: string[];
  id?: string;
  pullRefresh?: {disabled: boolean};
  asideResizor?: boolean;
  title?: string;
  body?: any[];
  definitions?: {};
  themeCss?: any;
  data?: any;
}
// 固定字段：id,creator,createTime
const createFromObj = (label: string, applicationPageId: string): any => {
  return {
    type: 'page',
    regions: ['body'],
    id: 'u:06ba71552faa',
    pullRefresh: {
      disabled: true
    },
    asideResizor: false,
    title: `${label}`,
    body: [
      {
        type: 'crud',
        syncLocation: false,
        api: {
          method: 'get',
          url: `/admin-api/system/form-field-value/page/${applicationPageId}`
        },
        bulkActions: [],
        itemActions: [],
        id: 'u:67582b11dcf4',
        perPageAvailable: [5, 10, 20, 50, 100],
        messages: {},
        filterSettingSource: ['id', 'entries', 'creator', 'createTime'],
        headerToolbar: [
          {
            label: '新增',
            type: 'button',
            actionType: 'drawer',
            level: 'primary',
            editorSetting: {
              behavior: 'create'
            },
            id: 'u:215cf1bb50d5',
            drawer: {
              type: 'drawer',
              title: '新增',
              body: [
                {
                  type: 'form',
                  api: {
                    method: 'post',
                    url: `/admin-api/system/form-field-value/create/${applicationPageId}`
                  },
                  body: [],
                  id: 'u:6dc50e8d3d85',
                  actions: [
                    {
                      type: 'submit',
                      label: '提交',
                      primary: true
                    }
                  ],
                  feat: 'Insert'
                }
              ],
              actionType: 'drawer',
              id: 'u:360e7093b381',
              actions: [
                {
                  type: 'button',
                  actionType: 'cancel',
                  label: '取消',
                  id: 'u:b43c0a83e65a'
                },
                {
                  type: 'button',
                  actionType: 'confirm',
                  label: '确定',
                  primary: true,
                  id: 'u:973eb72462b5'
                }
              ],
              showCloseButton: true,
              closeOnOutside: false,
              closeOnEsc: false,
              showErrorMsg: true,
              showLoading: true,
              draggable: false,
              size: 'lg',
              resizable: false,
              editorSetting: {
                displayName: '新增'
              }
            }
          },
          {
            type: 'bulk-actions'
          },
          {
            type: 'export-csv',
            tpl: '内容',
            wrapperComponent: ''
          },
          {
            type: 'export-excel',
            tpl: '内容',
            wrapperComponent: ''
          }
        ],
        columns: [
          {
            label: 'ID',
            type: 'text',
            name: 'id',
            sortable: true
          },
          {
            label: '创建者',
            type: 'text',
            name: 'creator',
            sortable: true
          },
          {
            label: '创建时间',
            valueFormat: 'x',
            type: 'input-datetime',
            name: 'createTime',
            sortable: true
          },
          {
            label: '更新者',
            type: 'text',
            name: 'updater',
            sortable: true
          },
          {
            label: '更新时间',
            valueFormat: 'x',
            type: 'input-datetime',
            name: 'updateTime',
            sortable: true
          },
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                label: '编辑',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'update'
                },
                id: 'u:4d423864d6c0',
                drawer: {
                  type: 'drawer',
                  title: '编辑',
                  body: [
                    {
                      type: 'form',
                      api:
                        `put:/admin-api/system/form-field-value/batch-update/${applicationPageId}/` +
                        '${entries}',
                      body: [],
                      id: 'u:1fb5ccb04730',
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ],
                      feat: 'Edit',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      resetAfterSubmit: true
                    }
                  ],
                  actionType: 'drawer',
                  id: 'u:6fbb412d9abe',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:2e86c554e48e'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:392213d59e8d'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '编辑'
                  }
                }
              },
              {
                label: '查看',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'view'
                },
                id: 'u:88e384769ff8',
                drawer: {
                  type: 'drawer',
                  title: '查看详情',
                  body: [
                    {
                      type: 'form',
                      initApi:
                        `/admin-api/system/form-field-value/get/${applicationPageId}/` +
                        '${entries}',
                      body: [
                        {
                          name: 'ID',
                          label: 'id',
                          type: 'static'
                        },
                        {
                          name: 'creator',
                          label: '创建者',
                          type: 'static'
                        },
                        {
                          name: 'createTime',
                          label: '创建时间',
                          valueFormat: 'x',
                          type: 'input-datetime',
                          static: true
                        },
                        {
                          name: 'updater',
                          label: '更新者',
                          type: 'static'
                        },
                        {
                          name: 'updateTime',
                          label: '更新时间',
                          valueFormat: 'x',
                          type: 'input-datetime',
                          static: true
                        }
                      ],
                      id: 'u:2af2f2b5c2fd',
                      feat: 'View',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      static: true,
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ]
                    }
                  ],
                  actionType: 'drawer',
                  id: 'u:f202e0cd0692',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:c1bc83c9be43'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:25ec3026c1b7'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '查看详情'
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                actionType: 'dialog',
                level: 'link',
                className: 'text-danger',
                id: 'u:afacccd6355e',
                dialog: {
                  type: 'dialog',
                  title: '',
                  className: 'py-2',
                  actions: [
                    {
                      type: 'action',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:bf2ded085c52'
                    },
                    {
                      type: 'action',
                      actionType: 'submit',
                      label: '删除',
                      level: 'danger',
                      id: 'u:6f5c3305ac5b'
                    }
                  ],
                  body: [
                    {
                      type: 'form',
                      wrapWithPanel: false,
                      api:
                        `delete:/admin-api/system/form-field-value/delete/${applicationPageId}/` +
                        '${entries}',
                      body: [
                        {
                          type: 'tpl',
                          className: 'py-2',
                          tpl: '确认删除选中项？',
                          id: 'u:b5980d190942'
                        }
                      ],
                      id: 'u:130a702e34da',
                      feat: 'Insert',
                      dsType: 'api',
                      labelAlign: 'left'
                    }
                  ],
                  actionType: 'dialog',
                  id: 'u:3c6bb0356c6a',
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  editorSetting: {
                    displayName: '删除'
                  }
                }
              }
            ],
            id: 'u:0d6e1a5d6a6d'
          }
        ],
        footerToolbar: [
          {
            type: 'statistics'
          },
          {
            type: 'pagination'
          },
          {
            type: 'switch-per-page',
            tpl: '内容',
            wrapperComponent: '',
            id: 'u:265fb46b2bce'
          }
        ],
        pageField: 'pageNo',
        perPageField: 'pageSize',
        alwaysShowPagination: true
      }
    ],
    themeCss: {
      baseControlClassName: {
        'background:default': '#f6f6f6'
      }
    }
  };
};

export {createFromObj};

// 固定字段：id,creator,createTime
const createDatasetImportObj = (
  label: string,
  applicationPageId: string,
  otherFields: any[],
  datasetId: string,
  backend: boolean = false
): any => {
  console.log('otherFields', otherFields);

  const waitAddFields = [
    {
      name: 'id',
      control: 'input-text',
      label: 'ID'
    },
    {
      name: 'creator',
      control: 'select',
      label: '创建者'
    },
    {
      name: 'createTime',
      control: 'input-datetime',
      label: '创建时间',
      valueFormat: 'x'
    }
  ];
  if (!backend) {
    otherFields = [...waitAddFields, ...otherFields];
  }

  const needFilterFields = ['id', 'creator', 'createTime'];
  const filterFields = otherFields.filter(
    (item: any) => !needFilterFields.includes(item.name)
  );

  const otherColumns = otherFields.map((item: any) => {
    console.log('otherColumns', item);
    if (item.control == 'input-image') {
      return {
        label: item.label,
        type: 'image',
        name: item.name,
        enlargeAble: true,
        sortable: true,
        textOverflow: 'ellipsis',
        needWordBreak: false
      };
    } else if (item.control == 'input-file') {
      return {
        label: item.label,
        type: 'link',
        name: item.name,
        blank: true,
        sortable: true,
        textOverflow: 'ellipsis',
        needWordBreak: false,
        height: "50px",
        className: "h-8"

      };
    } else if (item.control == 'icon') {
      return {
        label: item.label,
        type: 'icon',
        //name: item.name,
        icon: '${' + item.name + '|raw}',
        sortable: true,
        textOverflow: 'ellipsis',
        needWordBreak: false,
        height: "50px",
        className: "h-8"
      };
    } else if (item.control == 'select') {
      let dictType = item.dictType;
      if (dictType) {
        let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
        return {
          ...item.json,
          label: item.label,
          type: item.control ?? 'input-text',
          name: item.name,
          valueFormat: 'x',
          static: true,
          sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          source: {
            method: 'get',
            url: api,
            responseData: {
              options: "${list}"
            }
          },
          height: "50px",
        className: "h-8"
        };
      } else {
        return {
          ...item.json,
          label: item.label,
          type: item.control ?? 'input-text',
          name: item.name,
          valueFormat: 'x',
          static: true,
          sortable: true,
          textOverflow: 'ellipsis',
          needWordBreak: false,
          height: "50px",
        className: "h-8"
        };
      }
    }  else {
      return {
        ...item.json,
        label: item.label,
        // type: 'text',
        type: item.control ?? 'input-text',
        name: item.name,
        static: true,
        valueFormat: 'x',
        sortable: true,
        textOverflow: 'ellipsis',
        needWordBreak: false,
        height: "50px",
        className: "h-8"
      };
    }
  });


  const addColumns = filterFields.filter((item: any) => item.isCreate == 1).map((item: any) => {
    if (item.control == 'select') {
      let dictType = item.dictType;
      if (dictType) {
        let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
        return {
          ...item.json,
          label: item.label,
          type: item.control ?? 'input-text',
          name: item.name,
          valueFormat: 'x',
          source: {
            method: 'get',
            url: api,
            responseData: {
              options: "${list}"
            }
          },
          static: item.isRead === 1
        };
      } else {
        return {
          ...item.json,
          label: item.label,
          type: item.control ?? 'input-text',
          name: item.name,
          valueFormat: 'x',
          static: item.isRead === 1
        };
      }
    }

    return {
      ...item.json,
      label: item.label,
      type: item.control ?? 'input-text',
      name: item.name,
      valueFormat: 'x',
      static: item.isRead === 1
    };
  });

  const editColumns = filterFields.filter((item: any) => item.isUpdate == 1).map((item: any) => {
    if (item.control == 'select') {
      let dictType = item.dictType;
      if (dictType) {
        let api = `/admin-api/system/dict-data/page?dictType=${dictType}`;
        return {
          ...item.json,
          label: item.label,
          type: item.control ?? 'input-text',
          name: item.name,
          valueFormat: 'x',
          source: {
            method: 'get',
            url: api,
            responseData: {
              options: "${list}"
            }
          },
          static: item.isRead === 1
        };
      } else {
        return {
          ...item.json,
          label: item.label,
          type: item.control ?? 'input-text',
          name: item.name,
          valueFormat: 'x',
          static: item.isRead === 1
        };
      }
    }

    return {
      ...item.json,
      label: item.label,
      type: item.control ?? 'input-text',
      name: item.name,
      valueFormat: 'x',
      static: item.isRead === 1
    };
  });


  const apiData: {[key: string]: any} = { // 添加索引签名
    dateSetId: datasetId,
    orderBy: '${orderBy|default:undefined}',
    orderDir: '${orderDir|default:undefined}'
  };

  otherFields.forEach((item: any) => {
    //amis内置参数，会有冲突
    if (item.name != 'params') {
      apiData[item.name] = `\${${item.name}|default:undefined}`;
    }
  });


  return {
    type: 'page',
    regions: ['body'],
    pullRefresh: {
      disabled: true
    },
    asideResizor: false,
    title: `${label}`,
    body: [
      {
        type: 'crud',
        lineHeight: 'middle',
        syncLocation: false,
        api: {
          method: 'get',
          url: `/admin-api/system/form-field-value/page/${applicationPageId}`,
          data: {...apiData,
            pageNo: '${pageNo}',
            pageSize: '${pageSize}'
          }
        },
        bulkActions: [],
        itemActions: [],
        perPageAvailable: [5, 10, 20, 50, 100],
        messages: {},
        filterSettingSource: ['id', 'creator', 'createTime'],
        headerToolbar: [
          {
            label: '新增',
            type: 'button',
            actionType: 'drawer',
            level: 'primary',
            editorSetting: {
              behavior: 'create'
            },
            drawer: {
              type: 'drawer',
              title: '新增',
              position: "right",
              body: [
                {
                  type: 'form',
                  api: {
                    method: 'post',
                    url: `/admin-api/system/form-field-value/create/${applicationPageId}`
                  },
                  mode: 'horizontal',
                  horizontal: {
                    left: 2,
                    right: 10,
                    offset: 2
                  },
                  body: [...addColumns],
                  actions: [
                    {
                      type: 'submit',
                      label: '提交',
                      primary: true
                    }
                  ],
                  feat: 'Insert'
                }
              ],
              actionType: 'drawer',
              actions: [
                {
                  type: 'button',
                  actionType: 'cancel',
                  label: '取消'
                },
                {
                  type: 'button',
                  actionType: 'confirm',
                  label: '确定',
                  primary: true
                }
              ],
              showCloseButton: true,
              closeOnOutside: false,
              closeOnEsc: false,
              showErrorMsg: true,
              showLoading: true,
              draggable: false,
              size: 'lg',
              resizable: false,
              editorSetting: {
                displayName: '新增'
              }
            }
          },
          {
            type: 'export-csv',
            tpl: '内容',
            wrapperComponent: ''
          },
          {
            type: 'export-excel',
            tpl: '内容',
            wrapperComponent: ''
          }
        ],
        columns: [
          ...otherColumns,
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                label: '编辑',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'update'
                },
                id: 'u:4d423864d6c0',
                drawer: {
                  type: 'drawer',
                  title: '编辑',
                  position: "right",
                  body: [
                    {
                      type: 'form',
                      api:
                        `put:/admin-api/system/form-field-value/batch-update/${applicationPageId}/` +
                        '${entries}',
                      // initApi: `${applicationPageId}/` + '${entries}/edit',
                      mode: 'horizontal',
                      horizontal: {
                        left: 2,
                        right: 10,
                        offset: 2
                      },
                      body: [...editColumns, {
                        type: 'input-text',
                        label: 'ID',
                        name: 'id',
                        value: '${id}',
                        hidden: true
                      }],
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ],
                      feat: 'Edit',
                      dsType: 'api',
                      // labelAlign: 'top',
                      title: '表单',
                      resetAfterSubmit: true
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:2e86c554e48e'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:392213d59e8d'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '编辑'
                  }
                }
              },
              {
                label: '查看',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'view'
                },
                drawer: {
                  type: 'drawer',
                  title: '查看详情',
                  body: [
                    {
                      type: 'form',
                      initApi:
                        `/admin-api/system/form-field-value/get/${applicationPageId}/` +
                        '${entries}',
                      body: [...otherColumns],
                      id: 'u:2af2f2b5c2fd',
                      feat: 'View',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      static: true,
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ]
                    }
                  ],
                  actionType: 'drawer',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '查看详情'
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                actionType: 'dialog',
                level: 'link',
                className: 'text-danger',
                dialog: {
                  type: 'dialog',
                  title: '',
                  className: 'py-2',
                  actions: [
                    {
                      type: 'action',
                      actionType: 'cancel',
                      label: '取消'
                    },
                    {
                      type: 'action',
                      actionType: 'submit',
                      label: '删除',
                      level: 'danger'
                    }
                  ],
                  body: [
                    {
                      type: 'form',
                      wrapWithPanel: false,
                      api:
                        `delete:/admin-api/system/form-field-value/delete/${applicationPageId}/` +
                        '${entries}',
                      body: [
                        {
                          type: 'tpl',
                          className: 'py-2',
                          tpl: '确认删除选中项？'
                        }
                      ],
                      feat: 'Insert',
                      dsType: 'api',
                      labelAlign: 'left'
                    }
                  ],
                  actionType: 'dialog',
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  editorSetting: {
                    displayName: '删除'
                  }
                }
              }
            ]
          }
        ],
        pageField: 'pageNo',
        perPageField: 'pageSize',
        alwaysShowPagination: true
      }
    ],
    themeCss: {
      baseControlClassName: {
        'background:default': '#f6f6f6'
      }
    }
  };
};

export {createDatasetImportObj};
// 自定义页面
const createCustompageObj = (label: string): Page => {
  return {
    type: 'page',
    title: `${label}`,
    regions: ['body'],
    pullRefresh: {
      disabled: true
    },
    body: []
  };
};

export {createCustompageObj};

// IFrame页面
const createIframeObj = (url: string): Page => {
  return {
    type: 'page',
    regions: ['body'],
    pullRefresh: {
      disabled: true
    },
    body: [
      {
        type: 'iframe',
        src: url
      }
    ]
  };
};

export {createIframeObj};

const createReportObj = (url: string): Page => {
  return {
    type: 'page',
    regions: ['body'],
    pullRefresh: {
      disabled: true
    },
    body: [
      {
        type: 'iframe',
        src: url
      }
    ]
  };
};

export {createReportObj};

const createPanObj = (label: string, applicationPageId: string): Page => {
  return {
    type: 'page',
    title: '',
    body: [
      {
        type: 'crud',
        syncLocation: false,
        id: 'crudId',
        data: {
          applicationPageId: applicationPageId
        },
        api: {
          method: 'get',
          url: '/admin-api/infra/file/page',
          data: {
            pageNo: '${page}',
            pageSize: '${perPage}',
            applicationPageId: applicationPageId,
            name: '${name|default:undefined}',
            type: '${type|default:undefined}'
          }
        },
        headerToolbar: [
          {
            type: 'button',
            label: '上传文件',
            level: 'primary',
            icon: 'fa fa-upload',
            className: 'mr-2',
            actionType: 'dialog',
            feat: 'Insert',
            reload: true,
            dialog: {
              title: '上传文件',
              body: [
                {
                  type: 'input-file',
                  receiver: {
                    method: 'post',
                    url: '/admin-api/infra/file/uploadByApplication',
                    data: {
                      applicationPageId: applicationPageId
                    }
                  },
                  name: 'file',
                  drag: true,
                  feat: 'Insert'
                }
              ],
              onEvent: {
                confirm: {
                  actions: [
                    {
                      actionType: 'reload',
                      componentId: 'crudId'
                    }
                  ]
                }
              }
            }
          },
          {
            type: 'reload'
          }
        ],
        footerToolbar: ['statistics', 'switch-per-page', 'pagination'],
        columns: [
          {
            name: 'name',
            label: '文件名',
            type: 'tpl',
            tpl: "<div class=\"flex items-center\"><i class=\"${type === 'video/mp4' ? 'fa fa-file-video' : (type.includes('image') ? 'fa fa-file-image' : 'fa fa-file')}\" style=\"margin-right: 8px;\"></i>${name}</div>"
          },
          {
            name: 'size',
            label: '大小',
            type: 'tpl',
            tpl: '${ROUND(size / 1024)} KB'
          },
          {
            name: 'type',
            label: '类型',
            type: 'mapping',
            map: {
              // 视频文件
              'video/mp4': '视频',
              'video/ogg': '视频',
              'video/x-matroska': '视频',

              // 图片文件
              'image/jpeg': '图片',
              'image/png': '图片',
              'image/gif': '图片',
              'image/svg+xml': 'SVG图片',
              'image/webp': '图片',

              // 音频文件
              'audio/mp4': '音频',
              'audio/mpeg': '音频',
              'audio/aac': '音频',
              'audio/x-caf': '音频',
              'audio/flac': '音频',
              'audio/ogg': '音频',
              'audio/wav': '音频',

              'text/plain': '文本',
              'text/x-web-markdown': 'Markdown',
              'text/x-markdown': 'Markdown',
              'text/markdown': 'Markdown',

              // 文档文件
              'application/pdf': 'PDF文档',
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                'Word文档',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                'Excel文档',
              'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                'PPT文档',

              // 压缩文件
              'application/zip': '压缩文件',
              'application/x-rar-compressed': '压缩文件',
              'application/x-7z-compressed': '压缩文件',

              '*': '未知'
            }
          },
          {
            name: 'createTime',
            label: '上传时间',
            type: 'tpl',
            tpl: '${createTime / 1000 | date}'
          },
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                type: 'button',
                label: '预览',
                icon: 'fa fa-eye',
                actionType: 'dialog',
                dialog: {
                  title: '文件预览',
                  size: 'lg',
                  body: [
                    {
                      type: 'image',
                      src: '${url}',
                      // imageMode: "original",
                      showToolbar: true,
                      enlargeAble: true,
                      visibleOn:
                        '${type === "image/jpeg" || type === "image/png" || type === "image/gif" || type === "image/svg+xml" || type === "image/webp"}'
                    },
                    {
                      type: 'audio',
                      src: '${url}',
                      visibleOn:
                        '${type === "audio/mp4" || type === "audio/mpeg"|| type === "audio/aac"|| type === "audio/x-caf"|| type === "audio/flac"|| type === "audio/ogg"|| type === "audio/wav"}'
                    },
                    {
                      type: 'pdf-viewer',
                      src: '${url}',
                      visibleOn: '${type === "application/pdf"}'
                    },
                    {
                      type: 'office-viewer',
                      src: '${url}',
                      visibleOn:
                        '${type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" || type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}'
                    },
                    {
                      type: 'video',
                      src: '${url}',
                      visibleOn:
                        '${type === "video/mp4" || type === "video/ogg" || type === "video/x-matroska"}'
                    },
                    {
                      type: 'markdown',
                      src: '${url}',
                      visibleOn:
                        '${type === "text/x-web-markdown" || type === "text/x-markdown" || type === "text/markdown"}'
                    },
                    {
                      type: 'iframe',
                      src: '${url}',
                      visibleOn: '${type === "text/plain"}'
                    }
                  ]
                }
              },
              {
                type: 'button',
                label: '下载',
                icon: 'fa fa-download',
                //  actionType: 'download',
                api: '${url}',
                actionType: 'url',
                url: '${url}',
                blank: true
              },
              {
                type: 'button',
                label: '删除',
                icon: 'fa fa-trash',
                className: 'text-danger',
                actionType: 'dialog',
                dialog: {
                  type: 'dialog',
                  title: '',
                  className: 'py-2',
                  actions: [
                    {
                      type: 'action',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:bf2ded085c52'
                    },
                    {
                      type: 'action',
                      actionType: 'submit',
                      label: '删除',
                      level: 'danger',
                      id: 'u:6f5c3305ac5b'
                    }
                  ],
                  body: [
                    {
                      type: 'form',
                      wrapWithPanel: false,
                      api: 'delete:/admin-api/infra/file/delete?id=${id}',
                      body: [
                        {
                          type: 'tpl',
                          className: 'py-2',
                          tpl: '确认删除选中项？',
                          id: 'u:b5980d190942'
                        }
                      ],
                      id: 'u:130a702e34da',
                      feat: 'Insert',
                      dsType: 'api',
                      labelAlign: 'left'
                    }
                  ],
                  actionType: 'dialog',
                  showCloseButton: true,
                  showLoading: true,
                  draggable: false,
                  editorSetting: {
                    displayName: '删除文件'
                  }
                }
              }
            ]
          }
        ]
      }
    ]
  };
};

export {createPanObj};



const createFromWithFieldObj = (label: string, applicationPageId: string, fields: any[]): any => {
  let showFields = fields.map((item: any) => {  
    return {    
      label: item.label,
      type: item.controlType,
      name: item.name,
      static: true
    }
  })

  return {
    type: 'page',
    regions: ['body'],
    id: 'u:06ba71552faa',
    pullRefresh: {
      disabled: true
    },
    asideResizor: false,
    title: `${label}`,
    body: [
      {
        type: 'crud',
        syncLocation: false,
        api: {
          method: 'get',
          url: `/admin-api/system/form-field-value/page/${applicationPageId}`
        },
        bulkActions: [],
        itemActions: [],
        id: 'u:67582b11dcf4',
        perPageAvailable: [5, 10, 20, 50, 100],
        messages: {},
        filterSettingSource: ['id', 'entries', 'creator', 'createTime'],
        headerToolbar: [
          {
            label: '新增',
            type: 'button',
            actionType: 'drawer',
            level: 'primary',
            editorSetting: {
              behavior: 'create'
            },
            id: 'u:215cf1bb50d5',
            drawer: {
              type: 'drawer',
              title: '新增',
              body: [
                {
                  type: 'form',
                  api: {
                    method: 'post',
                    url: `/admin-api/system/form-field-value/create/${applicationPageId}`
                  },
                  body: [
                    ...fields
                  ],
                  id: 'u:6dc50e8d3d85',
                  actions: [
                    {
                      type: 'submit',
                      label: '提交',
                      primary: true
                    }
                  ],
                  feat: 'Insert'
                }
              ],
              actionType: 'drawer',
              id: 'u:360e7093b381',
              actions: [
                {
                  type: 'button',
                  actionType: 'cancel',
                  label: '取消',
                  id: 'u:b43c0a83e65a'
                },
                {
                  type: 'button',
                  actionType: 'confirm',
                  label: '确定',
                  primary: true,
                  id: 'u:973eb72462b5'
                }
              ],
              showCloseButton: true,
              closeOnOutside: false,
              closeOnEsc: false,
              showErrorMsg: true,
              showLoading: true,
              draggable: false,
              size: 'lg',
              resizable: false,
              editorSetting: {
                displayName: '新增'
              }
            }
          },
          {
            type: 'bulk-actions'
          },
          {
            type: 'export-csv',
            tpl: '内容',
            wrapperComponent: ''
          },
          {
            type: 'export-excel',
            tpl: '内容',
            wrapperComponent: ''
          }
        ],
        columns: [
          {
            label: 'ID',
            type: 'text',
            name: 'id',
            sortable: true
          },
          ...showFields,
          {
            label: '创建者',
            type: 'text',
            name: 'creator',
            sortable: true
          },
          {
            label: '创建时间',
            valueFormat: 'x',
            type: 'input-datetime',
            name: 'createTime',
            sortable: true,
            static: true
          },
          {
            label: '更新者',
            type: 'text',
            name: 'updater',
            sortable: true
          },
          {
            label: '更新时间',
            valueFormat: 'x',
            type: 'input-datetime',
            name: 'updateTime',
            sortable: true,
            static: true
          },
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                label: '编辑',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'update'
                },
                id: 'u:4d423864d6c0',
                drawer: {
                  type: 'drawer',
                  title: '编辑',
                  body: [
                    {
                      type: 'form',
                      api:
                        `put:/admin-api/system/form-field-value/batch-update/${applicationPageId}/` +
                        '${entries}',
                      body: [
                        ...fields
                      ],
                      id: 'u:1fb5ccb04730',
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:1fb5ccb04730'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ],
                      feat: 'Edit',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      resetAfterSubmit: true
                    }
                  ],
                  actionType: 'drawer',
                  id: 'u:6fbb412d9abe',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:2e86c554e48e'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:392213d59e8d'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '编辑'
                  }
                }
              },
              {
                label: '查看',
                type: 'button',
                actionType: 'drawer',
                level: 'link',
                editorSetting: {
                  behavior: 'view'
                },
                id: 'u:88e384769ff8',
                drawer: {
                  type: 'drawer',
                  title: '查看详情',
                  body: [
                    {
                      type: 'form',
                      initApi:
                        `/admin-api/system/form-field-value/get/${applicationPageId}/` +
                        '${entries}',
                      body: [
                        {
                          name: 'ID',
                          label: 'id',
                          type: 'static'
                        },
                        ...showFields,
                        {
                          name: 'creator',
                          label: '创建者',
                          type: 'static'
                        },
                        {
                          name: 'createTime',
                          label: '创建时间',
                          valueFormat: 'x',
                          type: 'input-datetime',
                          static: true
                        },
                        {
                          name: 'updater',
                          label: '更新者',
                          type: 'static'
                        },
                        {
                          name: 'updateTime',
                          label: '更新时间',
                          valueFormat: 'x',
                          type: 'input-datetime',
                          static: true
                        }
                      ],
                      id: 'u:2af2f2b5c2fd',
                      feat: 'View',
                      dsType: 'api',
                      labelAlign: 'top',
                      title: '表单',
                      mode: 'flex',
                      static: true,
                      actions: [
                        {
                          type: 'button',
                          label: '取消',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'cancel',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'default'
                        },
                        {
                          type: 'button',
                          label: '提交',
                          onEvent: {
                            click: {
                              actions: [
                                {
                                  actionType: 'submit',
                                  componentId: 'u:e9f0e2b1f53a'
                                }
                              ]
                            }
                          },
                          level: 'primary'
                        }
                      ]
                    }
                  ],
                  actionType: 'drawer',
                  id: 'u:f202e0cd0692',
                  actions: [
                    {
                      type: 'button',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:c1bc83c9be43'
                    },
                    {
                      type: 'button',
                      actionType: 'confirm',
                      label: '确定',
                      primary: true,
                      id: 'u:25ec3026c1b7'
                    }
                  ],
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  size: 'lg',
                  resizable: false,
                  editorSetting: {
                    displayName: '查看详情'
                  }
                }
              },
              {
                type: 'button',
                label: '删除',
                actionType: 'dialog',
                level: 'link',
                className: 'text-danger',
                id: 'u:afacccd6355e',
                dialog: {
                  type: 'dialog',
                  title: '',
                  className: 'py-2',
                  actions: [
                    {
                      type: 'action',
                      actionType: 'cancel',
                      label: '取消',
                      id: 'u:bf2ded085c52'
                    },
                    {
                      type: 'action',
                      actionType: 'submit',
                      label: '删除',
                      level: 'danger',
                      id: 'u:6f5c3305ac5b'
                    }
                  ],
                  body: [
                    {
                      type: 'form',
                      wrapWithPanel: false,
                      api:
                        `delete:/admin-api/system/form-field-value/delete/${applicationPageId}/` +
                        '${entries}',
                      body: [
                        {
                          type: 'tpl',
                          className: 'py-2',
                          tpl: '确认删除选中项？',
                          id: 'u:b5980d190942'
                        }
                      ],
                      id: 'u:130a702e34da',
                      feat: 'Insert',
                      dsType: 'api',
                      labelAlign: 'left'
                    }
                  ],
                  actionType: 'dialog',
                  id: 'u:3c6bb0356c6a',
                  showCloseButton: true,
                  closeOnOutside: false,
                  closeOnEsc: false,
                  showErrorMsg: true,
                  showLoading: true,
                  draggable: false,
                  editorSetting: {
                    displayName: '删除'
                  }
                }
              }
            ],
            id: 'u:0d6e1a5d6a6d'
          }
        ],
        footerToolbar: [
          {
            type: 'statistics'
          },
          {
            type: 'pagination'
          },
          {
            type: 'switch-per-page',
            tpl: '内容',
            wrapperComponent: '',
            id: 'u:265fb46b2bce'
          }
        ],
        pageField: 'pageNo',
        perPageField: 'pageSize',
        alwaysShowPagination: true
      }
    ],
    themeCss: {
      baseControlClassName: {
        'background:default': '#f6f6f6'
      }
    }
  };
};

export {createFromWithFieldObj};