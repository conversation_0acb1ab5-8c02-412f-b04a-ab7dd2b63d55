import React, {FC, useState, useRef} from 'react';
import './index.scss';
import {Button, Tabs, Tab, toast, Avatar} from 'amis';
import itemShow from './image/itemShow.png';
import {
  javaApplicationList,
  updateTeamProject,
  updateIcon,
  getTeamMember,
  delTeam
} from '@/utils/api/api';
import {EditProjectNameDialog} from '@/utils/schemaDataSet/EditProjectNameDialog';
import {EditProjectDescribeDialog} from '@/utils/schemaDataSet/EditProjectDescribeDialog';
import {EditTeamMember} from '@/utils/schemaDataSet/EditTeamMember';
import {GetTeamMember} from '@/utils/schemaDataSet/GetTeamMember';
import {EditEntryIcon} from '@/utils/schemaDataSet/EditEntryIcon';
import AMISRenderer from '@/component/AMISRenderer';
import {CreateApplyPopup} from '@/utils/schemaDataSet/CreateApplyPopup';
import {DeleteProjectSecondaryConfirm} from '@/utils/schemaDataSet/DeleteProjectSecondaryConfirm';
import {utilsSetFromAppbuild} from '@/utils/routeUtils';
import {javaApplicationCreate} from '@/utils/api/api';

const ProjectMain: FC<any> = (props: any) => {
  const store = props.store;

  const [totalAll, setTotalAll] = React.useState(0);

  const [memberListKey, setMemberListKey] = React.useState(0);

  // 是否打开创建应用弹窗 createApplyPopup
  const [openCreateApplyPopup, setOpenCreateApplyPopup] =
    React.useState<boolean>(false);
  // 是否打开删除项目弹窗
  const [delProjectPopup, setDelProjectPopup] = React.useState<boolean>(false);

  // 是否打开图标编辑弹窗
  const [showEditIconDialog, setShowEditIconDialog] = React.useState<boolean>(false);

  // 应用创建表单数据
  const [appFormData, setAppFormData] = React.useState<any>({
    icon: '',
    iconColor: '#000000',
    iconBg: '#52C41A'
  });

  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="projectMain-main-tabsToolbar"></div>;
  };

  // 从URL查询参数中获取activeTabKey
  const getActiveTabFromUrl = () => {
    // 使用props.location获取查询参数
    const search = props.location?.search || '';
    const urlParams = new URLSearchParams(search);
    const tabKey = urlParams.get('activeTabKey');

    // 映射URL参数到标签页索引
    const tabMapping: {[key: string]: string} = {
      app: '1',
      myapp: '2',
      member: '3',
      setting: '4',
      trash: '5'
    };

    return tabKey && tabMapping[tabKey] ? tabMapping[tabKey] : '1'; // 默认为应用标签
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromUrl());

  //项目的信息
  const [projectInfo, setProjectInfo] = React.useState<any>(false);
  // 项目的 应用列表
  const [appList, setAppList] = React.useState<any>([]);
  // 项目的 回收站列表
  const [projectTrashList, setProjectTrashList] = React.useState<any>([]);

  const [roleId, setroleId] = React.useState(0);

  // 我创建的 应用列表
  const [myAppList, setMyAppList] = React.useState<any>([]);

  // 编辑项目名称
  const [showEditProjectNameDialog, setshowEditProjectNameDialog] =
    useState(false);

  // 编辑项目描述
  const [showEditProjectDescribeDialog, setshowEditProjectDescribeDialog] =
    useState(false);

  const [showAddMemberDialog, setShowAddMemberDialog] = React.useState(false);

  const inviteMemberPopup = () => {
    setShowAddMemberDialog(true);
  };

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 解析应用图标内容，提取SVG、颜色和背景色
  const parseAppIcon = (iconHtml: string) => {
    if (!iconHtml) {
      return {
        icon: '',
        iconColor: '#000000',
        iconBg: '#52C41A'
      };
    }

    // 如果是完整的span元素，需要解析其中的内容
    if (iconHtml.includes('<span')) {
      // 提取style属性中的颜色和背景色
      const colorMatch = iconHtml.match(/color:\s*([^;]+)/);
      const bgMatch = iconHtml.match(/background:\s*([^;]+)/);

      // 提取SVG内容
      const svgMatch = iconHtml.match(/<svg[^>]*>.*?<\/svg>/);

      return {
        icon: svgMatch ? svgMatch[0] : '',
        iconColor: colorMatch ? colorMatch[1] : '#000000',
        iconBg: bgMatch ? bgMatch[1] : '#52C41A'
      };
    }

    // 如果是纯SVG或图标类名，直接返回
    return {
      icon: iconHtml,
      iconColor: '#000000',
      iconBg: '#52C41A'
    };
  };

  // 处理图标保存
  const handleIconSave = (values: any) => {
    console.log('处理图标保存:', values);

    if (!values || !Array.isArray(values) || values.length === 0) {
      console.log('无效的数据格式，取消更新');
      return;
    }

    const iconData = values[0];
    if (!iconData) {
      console.log('无图标数据，取消更新');
      return;
    }

    // 清理图标数据，移除可能的引号
    let cleanIcon = iconData.icon || '';
    if (typeof cleanIcon === 'string') {
      // 移除可能的外层引号
      cleanIcon = cleanIcon.replace(/^['"]|['"]$/g, '').trim();
    }

    // 使用新的图标数据
    const finalIconColor = iconData.iconColor || '#000000';
    const finalIconBg = iconData.iconBg || '#52C41A';

    // 构建新的图标HTML结构
    let newLogo = '';
    if (cleanIcon) {
      if (cleanIcon.includes('<svg')) {
        // SVG图标：构建完整的span结构
        newLogo = `<span style="color:${finalIconColor};background:${finalIconBg};width:48px;height:48px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:24px;vertical-align:middle">${cleanIcon}</span>`;
      } else {
        // 字体图标：构建完整的span结构
        newLogo = `<span style="color:${finalIconColor};background:${finalIconBg};width:48px;height:48px;display:inline-flex;align-items:center;justify-content:center;border-radius:8px;font-size:24px;vertical-align:middle"><i class="${cleanIcon}"></i></span>`;
      }
    }

    // 更新应用表单数据
    setAppFormData({
      icon: cleanIcon,
      iconColor: finalIconColor,
      iconBg: finalIconBg,
      logo: newLogo
    });

    // 更新DOM中的图标显示
    setTimeout(() => {
      const iconPreview = document.getElementById('app-icon-preview');
      if (iconPreview) {
        if (cleanIcon) {
          // 有图标时，移除虚线边框，设置实际颜色
          iconPreview.style.border = 'none';
          iconPreview.style.color = finalIconColor;
          iconPreview.style.background = finalIconBg;

          if (cleanIcon.includes('<svg')) {
            iconPreview.innerHTML = cleanIcon;
          } else {
            iconPreview.innerHTML = `<i class="${cleanIcon}"></i>`;
          }
        } else {
          // 没有图标时，显示占位符
          iconPreview.innerHTML = '<i class="fa fa-plus" style="font-size:16px;"></i>';
          iconPreview.style.color = '#999';
          iconPreview.style.background = '#f5f7fa';
          iconPreview.style.border = '2px dashed #d9d9d9';
        }
      }
    }, 100);

    setShowEditIconDialog(false);
    toast.success('图标设置成功');
  };

  React.useEffect(() => {
    const data = {
      teamOrProjectOrApplicationId: props.dataInfo.id,
      userId: props.store.userInfo?.id,
      type: '2',
      pageNo: 1,
      pageSize: 10
    };
    getTeamMember(data)
      .then(res => {
        if (res.data.list > 0 && res.data.list[0].role > 0) {
          setroleId(res.data.list[0].role);
        }
      })
      .catch(error => {
        console.error('获取团队成员失败:', error);
      });

    // 监听自定义事件
    const handleOpenIconEditor = () => {
      console.log('openIconEditor event received');
      setShowEditIconDialog(true);
    };

    window.addEventListener('openIconEditor', handleOpenIconEditor);

    // 清理函数
    return () => {
      window.removeEventListener('openIconEditor', handleOpenIconEditor);
    };
  }, []);

  React.useEffect(() => {
    setAppList([]);
    if (props.dataInfo.id) {
      setProjectInfo(props.dataInfo);
      getJavaApplicationList();
      getMyJavaApplicationList();
    }
  }, [props.dataInfo]);

  // 创建应用弹窗确认事件 onCreateApplyPopupConfirm
  const onCreateApplyPopupConfirm = (val: any) => {
    val = val[0];
    let data: any = {
      projectId: props.dataInfo.id,
      name: val.apply_name,
      description: val.description,
      type: val.type
    };

    // 只有在选择了图标时才添加logo字段
    if (appFormData.logo && appFormData.icon) {
      data.logo = appFormData.logo;
    }

    if (!props.dataInfo) {
      data.projectId = val.select;
    }
    javaApplicationCreate(data)
      .then((res: any) => {
        if (res.code == 0) {
          toast.success('创建应用成功');
          props.updateMenuData();
          setOpenCreateApplyPopup(false);
          // 重置图标数据
          setAppFormData({
            icon: '',
            iconColor: '#000000',
            iconBg: '#52C41A'
          });
        } else {
          toast.error(res.msg);
        }
      })
      .catch((err: any) => {
        console.error(err);
      });
  };

  // 获取应用列表页面 javaApplicationList
  const getJavaApplicationList = async () => {
    let data = {
      projectId: props.dataInfo.id,
      pageNo: 1,
      pageSize: 100
    };
    let res = await javaApplicationList(data);
    if (res.code == 0) {
      if (res.data?.list) {
        setAppList(res.data.list);
      }
    }
  };

  // 获取我创建的应用列表页面 javaMyApplicationList
  const getMyJavaApplicationList = async () => {
    let data = {
      projectId: props.dataInfo.id,
      pageNo: 1,
      pageSize: 100,
      creator: store.userInfo?.id // 添加 creator 字段
    };
    let res = await javaApplicationList(data);
    if (res.code == 0) {
      if (res.data?.list) {
        setMyAppList(res.data.list);
      }
    }
  };

  const handleInAppPage = (app: any) => {
    // console.log(app);
    utilsSetFromAppbuild();
    props.history.push(`/app${app.id}/admin`);
  };

  // 通用的更新项目方法
  const updateProfile = (
    data: any,
    fieldName: string,
    value: any,
    closeDialog: () => void
  ) => {
    console.log(`edit ${fieldName}:`, value);
    data.id = props.dataInfo.id;
    updateTeamProject(data)
      .then(res => {
        console.log('update user profile:', res);
        if (res.code === 0) {
          setProjectInfo({
            ...projectInfo,
            [fieldName]: value
          });
          closeDialog();
        } else {
          toast.error(res.message);
        }
      })
      .catch(err => {
        toast.error(err.message);
      });
  };

  const handleEditProjectName = (values: any) => {
    // 处理编辑项目名称的逻辑
    const name = values[0]?.name;
    if (name) {
      updateProfile({name: name}, 'name', name, () => {
        setshowEditProjectNameDialog(false);
        if (typeof props.handleGetTeamAndProjectList === 'function') {
          props.handleGetTeamAndProjectList();
        }
      });
    }
  };

  const handleEditdescribe = (values: any) => {
    // 处理编辑项目名称的逻辑
    const description = values[0]?.description;
    if (description) {
      updateProfile(
        {description: description},
        'description',
        description,
        () => setshowEditProjectDescribeDialog(false)
      );
    }
  };

  // 删除项目操作
  // id	是	string	项目id
  const onDelProject = (id: string) => {
    let data = {
      id: id
    };
    delTeam(data)
      .then((res: any) => {
        setDelProjectPopup(false);
        if (res.code === 0) {
          toast.success('已删除该项目');

          // 删除项目后导航到团队页面，取消显示项目页面
          props.history.push(`/appbuild/team${props.activeTeamItem.id}?page=1`);
          props.updateMenuData();
        } else {
          console.error(res.msg);
        }
      })
      .catch((err: any) => {
        console.error(err);
      });
  };

  // 二次确认删除弹窗- 确认处理
  const onDelProjectConfirm = (val: any) => {
    if (val[0].text == projectInfo.name) {
      onDelProject(projectInfo.id);
    } else {
      toast.error('删除失败,请输入正确的项目名称');
    }
  };

  // 处理头像上传
  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadFile(file, 'file', updateIcon, {
        allowedTypes: /^image\/(jpeg|png)$/,
        maxSize: 2 * 1024 * 1024,
        errorMessages: {
          typeError: '只支持 jpg、png 格式的图片',
          sizeError: '图片大小不能超过 2M'
        }
      });
    }
    // 清空 input 的值，这样同一个文件可以重复选择
    event.target.value = '';
  };

  // 通用的文件上传方法
  const uploadFile = (
    file: File,
    fieldName: string,
    uploadApi: (formData: FormData) => Promise<any>,
    options: {
      maxSize?: number;
      allowedTypes?: RegExp;
      errorMessages?: {
        typeError?: string;
        sizeError?: string;
      };
    } = {}
  ) => {
    // 默认配置
    const defaultOptions = {
      maxSize: 2 * 1024 * 1024, // 默认2MB
      allowedTypes: /^image\/(jpeg|png)$/, // 默认jpg和png
      errorMessages: {
        typeError: '只支持 jpg、png 格式的图片',
        sizeError: '图片大小不能超过 2M'
      }
    };

    const config = {...defaultOptions, ...options};

    // 验证文件类型
    if (!file.type.match(config.allowedTypes)) {
      store.alert(config.errorMessages.typeError || '文件类型不支持');
      return;
    }

    // 验证文件大小
    if (file.size > config.maxSize) {
      store.alert(config.errorMessages.sizeError || '文件大小超出限制');
      return;
    }

    // 创建 FormData
    const formData = new FormData();
    formData.append(fieldName, file);
    console.log('file:', file);

    // 打印FormData内容的正确方法
    console.log('FormData内容:');
    // 使用更兼容的方式打印FormData
    console.log(
      `${fieldName}: File(${file.name}, ${file.type}, ${file.size} bytes)`
    );

    // 临时预览（如果是图片）
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = e => {
        setProjectInfo({
          ...projectInfo,
          [fieldName]: e.target?.result as string
        });
      };
      reader.readAsDataURL(file);
    }

    // 调用上传API
    return uploadApi(formData)
      .then(res => {
        console.log('update user profile:', res);
        if (res.code === 0) {
          projectInfo.icon = res.data; //res.data
          updateTeamProject(projectInfo);
          setProjectInfo({
            ...projectInfo
            //icon: res.data.url
          });
        } else {
          console.log('上传失败');
          toast.error(res.message);
        }
        // console.log(`upload ${fieldName}:`, res);
        // return res;
      })
      .catch(err => {
        toast.error(err.message);
        throw err;
      });
  };

  // 处理标签页切换，更新URL
  const handleTabChange = (key: string) => {
    // 先更新本地状态，确保UI立即响应
    setActiveTab(key);

    // 映射标签页索引到URL参数
    const tabMapping: {[key: string]: string} = {
      '1': 'app',
      '2': 'myapp',
      '3': 'member',
      '4': 'setting',
      '5': 'trash'
    };

    // 使用props.history更新URL，但不触发组件重新渲染
    if (props.history) {
      const location = props.location || window.location;
      const pathname = location.pathname;

      // 创建新的查询参数
      const urlParams = new URLSearchParams(location.search);
      urlParams.set('activeTabKey', tabMapping[key]);

      // 使用replace而不是push，避免在历史记录中创建多余的条目
      props.history.replace({
        pathname,
        search: urlParams.toString()
      });
    }
  };

  // 初始化时从URL获取activeTabKey
  React.useEffect(() => {
    // 初始化时设置正确的标签页
    setActiveTab(getActiveTabFromUrl());
  }, []);

  // 监听URL变化
  React.useEffect(() => {
    // 使用props.history监听路由变化
    const unlisten = props.history?.listen(() => {
      // 当URL变化时，更新activeTab状态
      const newActiveTab = getActiveTabFromUrl();
      if (newActiveTab !== activeTab) {
        setActiveTab(newActiveTab);
      }
    });

    return () => {
      // 清理监听器
      if (unlisten) unlisten();
    };
  }, [props.history, props.location, activeTab]);

  return (
    <div className="projectMain">
      <div className="projectMain-top">
        <div className="projectMain-top-left">
          <div className="projectMain-top-left-name">{projectInfo.name}</div>
          <div className="projectMain-top-left-describe">
            {projectInfo.description}
          </div>
        </div>
        {(activeTab === '3' || props.store.userInfo?.hasAppBuildAuth) && (
          <div className="projectMain-top-right">
            <div className="projectMain-top-right-btn">
              <Button
                size="lg"
                level="primary"
                onClick={() =>
                  activeTab === '3'
                    ? inviteMemberPopup()
                    : setOpenCreateApplyPopup(true)
                }
              >
                <span className="teamMain-top-right-btn-addIcon">+</span>
                <span className="teamMain-top-right-btn-name">
                  {activeTab === '3' ? '邀请成员' : '创建应用'}
                </span>
              </Button>
            </div>
          </div>
        )}
      </div>
      <div className="projectMain-main">
        {/* Tabs:配置选项卡+工具栏  toolbar*/}

        <Tabs
          theme={localStorage.getItem('amis-theme') || 'cxd'}
          mode="line"
          activeKey={activeTab}
          onSelect={handleTabChange}
          toolbar={TabsToolbar()}
          linksClassName="projectMain-main-tabsTitle"
        >
          <Tab title="应用" eventKey="1">
            <div className="projectMain-main-tabsAppList">
              {appList.length > 0 &&
                appList.map((item: any, index: number) => {
                  return (
                    <div
                      key={item.id}
                      className="projectMain-main-tabsAppList-item"
                      onClick={() => {
                        handleInAppPage(item);
                      }}
                    >
                      <div className="projectMain-main-tabsAppList-item-content">
                        <div className="projectMain-main-tabsAppList-item-content-itemshow">
                          {item.logo ? (
                            item.logo.includes('<') ? (
                              // 如果是HTML格式的图标（SVG或span），直接渲染
                              <div
                                className="projectMain-main-tabsAppList-item-content-itemshow-icon"
                                dangerouslySetInnerHTML={{ __html: item.logo }}
                              />
                            ) : item.logo.startsWith('http') ? (
                              // 如果是图片URL，使用Avatar组件
                              <Avatar src={item.logo} shape="rounded" />
                            ) : (
                              // 如果是字体图标类名，使用i标签
                              <div className="projectMain-main-tabsAppList-item-content-itemshow-icon">
                                <i className={item.logo} style={{ fontSize: '24px' }} />
                              </div>
                            )
                          ) : (
                            <img
                              className="projectMain-main-tabsAppList-item-content-itemshow-img"
                              src={itemShow}
                            />
                          )}
                        </div>
                        <div className="projectMain-main-tabsAppList-item-content-content">
                          <div className="projectMain-main-tabsAppList-item-content-content-title">
                            {item.name}
                          </div>
                          <div className="projectMain-main-tabsAppList-item-content-content-describe">
                            {item.description}
                          </div>
                        </div>
                      </div>
                      <div className="projectMain-main-tabsAppList-item-play">
                        <div className="projectMain-main-tabsAppList-item-holder"></div>
                        <div className="projectMain-main-tabsAppList-item-play-btn">
                          <i className="fa-solid fa-ellipsis projectMain-main-tabsAppList-item-play-btn-icon"></i>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </Tab>
          <Tab title="我创建的" eventKey="2">
            <div className="projectMain-main-tabsAppList">
              {myAppList.length > 0 &&
                myAppList.map((item: any, index: number) => {
                  return (
                    <div
                      key={item.id}
                      className="projectMain-main-tabsAppList-item"
                      onClick={() => {
                        handleInAppPage(item);
                      }}
                    >
                      <div className="projectMain-main-tabsAppList-item-content">
                        <div className="projectMain-main-tabsAppList-item-content-itemshow">
                          {item.logo ? (
                            item.logo.includes('<') ? (
                              // 如果是HTML格式的图标（SVG或span），直接渲染
                              <div
                                className="projectMain-main-tabsAppList-item-content-itemshow-icon"
                                dangerouslySetInnerHTML={{ __html: item.logo }}
                              />
                            ) : item.logo.startsWith('http') ? (
                              // 如果是图片URL，使用Avatar组件
                              <Avatar src={item.logo} shape="rounded" />
                            ) : (
                              // 如果是字体图标类名，使用i标签
                              <div className="projectMain-main-tabsAppList-item-content-itemshow-icon">
                                <i className={item.logo} style={{ fontSize: '24px' }} />
                              </div>
                            )
                          ) : (
                            <img
                              className="projectMain-main-tabsAppList-item-content-itemshow-img"
                              src={itemShow}
                            />
                          )}
                        </div>
                        <div className="projectMain-main-tabsAppList-item-content-content">
                          <div className="projectMain-main-tabsAppList-item-content-content-title">
                            {item.name}
                          </div>
                          <div className="projectMain-main-tabsAppList-item-content-content-describe">
                            {item.description}
                          </div>
                        </div>
                      </div>
                      <div className="projectMain-main-tabsAppList-item-play">
                        <div className="projectMain-main-tabsAppList-item-holder"></div>
                        <div className="projectMain-main-tabsAppList-item-play-btn">
                          <i className="fa-solid fa-ellipsis projectMain-main-tabsAppList-item-play-btn-icon"></i>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </Tab>
          <Tab title="成员" eventKey="3">
            <div className="teamMain-main-memberList">
              <div className="teamMain-main-memberList-header">
                <div className="teamMain-main-memberList-header-title">
                  <span className="teamMain-main-memberList-header-title-text">
                    项目成员数量
                  </span>
                  <span className="teamMain-main-memberList-header-title-count">
                    ({totalAll})
                  </span>
                </div>
                <div className="teamMain-main-memberList-header-view">
                  <span className="teamMain-main-memberList-header-title-text">
                    {/* {props.dataInfo.creator == props.store.userInfo?.id ? '创建者视角' : '管理员和成员视角'} */}
                  </span>
                </div>
              </div>

              {showAddMemberDialog && (
                <AMISRenderer
                  show={showAddMemberDialog}
                  schema={GetTeamMember()}
                  onClose={() => {
                    setShowAddMemberDialog(false);
                    setMemberListKey(prev => prev + 1);
                  }}
                  data={{
                    teamOrProjectId: projectInfo?.id,
                    type: '2'
                  }}
                />
              )}

              {roleId >= 0 ? (
                <AMISRenderer
                  key={`${projectInfo?.id}-${memberListKey}`}
                  schema={EditTeamMember(setroleId, setTotalAll)}
                  embedMode={true}
                  data={{
                    teamOrProjectId: projectInfo?.id,
                    creator: props.dataInfo.creator,
                    user_id: props.store.userInfo?.id,
                    type: '2',
                    roleId: roleId
                  }}
                  id="memberCrud"
                />
              ) : (
                <div>Loading roleId...</div>
              )}
            </div>
          </Tab>
          <Tab title="设置" eventKey="4">
            <div className="projectMain-main-setCenter">
              <div className="projectMain-main-setCenter-left">
                <div className="projectMain-main-setCenter-left-name">
                  项目名称
                </div>
                <div className="projectMain-main-setCenter-left-content">
                  {projectInfo.name || '项目名称'}
                </div>
              </div>
              <div className="projectMain-main-setCenter-right">
                <div
                  className="projectMain-main-setCenter-right-btn"
                  onClick={() => setshowEditProjectNameDialog(true)}
                >
                  编辑资料
                </div>
              </div>
            </div>
            <div className="projectMain-main-setCenter">
              <div className="projectMain-main-setCenter-left">
                <div className="projectMain-main-setCenter-left-pjImage">
                  <div className="projectMain-main-setCenter-left-pjImage-image">
                    {projectInfo.icon && (
                      <img
                        className="projectMain-main-setCenter-left-pjImage-image-img"
                        src={projectInfo.icon}
                      />
                    )}
                  </div>
                  <div className="projectMain-main-setCenter-left-pjImage-name">
                    项目图标
                  </div>
                </div>

                <div className="projectMain-main-setCenter-left-content">
                  支持 2M 以内 JPG、PNG 图片
                </div>
              </div>
              <div className="projectMain-main-setCenter-right">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  style={{display: 'none'}}
                  onChange={handleAvatarUpload}
                />
                <div
                  className="projectMain-main-setCenter-right-btn"
                  onClick={() => fileInputRef.current?.click()}
                >
                  上传图标
                </div>
              </div>
            </div>
            <div className="projectMain-main-setCenter">
              <div className="projectMain-main-setCenter-left">
                <div className="projectMain-main-setCenter-left-name">
                  项目描述
                </div>
                <div className="projectMain-main-setCenter-left-content">
                  {projectInfo.description || '显示在项目主页'}
                </div>
              </div>
              <div className="projectMain-main-setCenter-right">
                <div
                  className="projectMain-main-setCenter-right-btn"
                  onClick={() => setshowEditProjectDescribeDialog(true)}
                >
                  编辑描述
                </div>
              </div>
            </div>
            <div className="projectMain-main-setCenter">
              <div className="projectMain-main-setCenter-left">
                <div className="projectMain-main-setCenter-left-name">
                  删除项目
                </div>
                <div className="projectMain-main-setCenter-left-content">
                  永久删除项目所有应用，且不可恢复
                </div>
              </div>
              <div className="projectMain-main-setCenter-right">
                <div
                  className="projectMain-main-setCenter-right-delbtn"
                  onClick={() => {
                    setDelProjectPopup(true);
                  }}
                >
                  删除项目
                </div>
              </div>
            </div>
          </Tab>
          <Tab title="回收站" eventKey="5">
            <div className="projectMain-main-tabsAppList">
              {projectTrashList.length > 0 &&
                projectTrashList.map((item: any, index: number) => {
                  return (
                    <div
                      key={item.id}
                      className="projectMain-main-tabsAppList-item"
                      onClick={() => {
                        props.history.push(`/${item.route}/admin`);
                      }}
                    >
                      <div className="projectMain-main-tabsAppList-item-content">
                        <div className="projectMain-main-tabsAppList-item-content-itemshow">
                          {item.logo ? (
                            item.logo.includes('<') ? (
                              // 如果是HTML格式的图标（SVG或span），直接渲染
                              <div
                                className="projectMain-main-tabsAppList-item-content-itemshow-icon"
                                dangerouslySetInnerHTML={{ __html: item.logo }}
                              />
                            ) : item.logo.startsWith('http') ? (
                              // 如果是图片URL，使用Avatar组件
                              <Avatar src={item.logo} shape="rounded" />
                            ) : (
                              // 如果是字体图标类名，使用i标签
                              <div className="projectMain-main-tabsAppList-item-content-itemshow-icon">
                                <i className={item.logo} style={{ fontSize: '24px' }} />
                              </div>
                            )
                          ) : (
                            <img
                              className="projectMain-main-tabsAppList-item-content-itemshow-img"
                              src={itemShow}
                            />
                          )}
                        </div>
                        <div className="projectMain-main-tabsAppList-item-content-content">
                          <div className="projectMain-main-tabsAppList-item-content-content-title">
                            {item.apply_name}
                          </div>
                          <div className="projectMain-main-tabsAppList-item-content-content-describe">
                            {item.depict}
                          </div>
                        </div>
                      </div>
                      <div className="projectMain-main-tabsAppList-item-play">
                        <div
                          className={`projectMain-main-tabsAppList-item-play-status ${
                            item.is_release === '1'
                              ? 'projectMain-main-tabsAppList-item-play-released'
                              : 'projectMain-main-tabsAppList-item-play-unreleased'
                          }`}
                        >
                          {item.is_release == '1' ? '已发布' : '未启用'}
                        </div>
                        <div className="projectMain-main-tabsAppList-item-play-btn">
                          <i className="fa-solid fa-ellipsis projectMain-main-tabsAppList-item-play-btn-icon"></i>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </Tab>
        </Tabs>
      </div>
      {showEditProjectNameDialog && (
        <AMISRenderer
          show={showEditProjectNameDialog}
          onClose={() => setshowEditProjectNameDialog(false)}
          onConfirm={handleEditProjectName}
          schema={EditProjectNameDialog(
            showEditProjectNameDialog,
            projectInfo.name
          )}
        />
      )}

      {showEditProjectDescribeDialog && (
        <AMISRenderer
          show={showEditProjectDescribeDialog}
          onClose={() => setshowEditProjectDescribeDialog(false)}
          onConfirm={handleEditdescribe}
          schema={EditProjectDescribeDialog(
            showEditProjectDescribeDialog,
            projectInfo.description
          )}
        />
      )}

      {/* 删除项目 - 二次确认弹窗 */}
      <AMISRenderer
        show={delProjectPopup}
        onClose={() => setDelProjectPopup(false)}
        onConfirm={(val: any) => onDelProjectConfirm(val)}
        schema={DeleteProjectSecondaryConfirm(delProjectPopup)}
      />

      {/* 新建应用弹窗 */}
      {openCreateApplyPopup && (
        <AMISRenderer
          show={openCreateApplyPopup}
          onClose={() => {
            setOpenCreateApplyPopup(false);
            // 重置图标数据
            setAppFormData({
              icon: '',
              iconColor: '#000000',
              iconBg: '#52C41A'
            });

          }}
          onConfirm={(val: any) => onCreateApplyPopupConfirm(val)}
          schema={CreateApplyPopup({
            group: props.activeTeamItem.children,
            type: !props.dataInfo
          })}

        />
      )}

      {/* 图标编辑弹框 */}
      {showEditIconDialog && (
        <AMISRenderer
          show={showEditIconDialog}
          onClose={() => setShowEditIconDialog(false)}
          onConfirm={handleIconSave}
          schema={EditEntryIcon({
            logo: {
              icon: appFormData.icon || '',
              iconColor: appFormData.iconColor || '#000000',
              iconBg: appFormData.iconBg || '#52C41A'
            }
          })}
        />
      )}
    </div>
  );
};

export default ProjectMain;
