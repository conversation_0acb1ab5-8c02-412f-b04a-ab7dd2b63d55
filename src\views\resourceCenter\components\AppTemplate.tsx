import React, { FC, useState, useEffect } from 'react';
import { Schema } from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import './AppTemplate.scss';
import { getComponentClassificationPage } from '@/utils/api/api';
import { visibleOn } from '@/editor/EChartsEditor/Common';

interface AppTemplateProps {
  onSelect?: (item: any) => void;
  history: any;
}

const AppTemplate: FC<AppTemplateProps> = (props) => {
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ id: 'all', name: '全部分类' });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [amisKey, setAmisKey] = useState(0);
  const [type, setType] = useState('2'); 

  // 监听模板类型变更事件
  useEffect(() => {
    const handleTemplateTypeChange = (event: any) => {
      const newType = event.detail.type;
      setType(newType);
      // 重新获取对应类型的分类列表
      fetchCategoryList(newType);
      // 重置分类选择
      setSelectedCategory({ id: 'all', name: '全部分类' });
      // 重置搜索关键词
      setSearchKeyword('');
      // 刷新AMIS渲染
      setAmisKey(Date.now());
    };

    window.addEventListener('templateTypeChange', handleTemplateTypeChange);

    return () => {
      window.removeEventListener('templateTypeChange', handleTemplateTypeChange);
    };
  }, []);

  // 获取分类列表
  useEffect(() => {
    fetchCategoryList(type);
  }, []);

  // 获取分类列表函数
  const fetchCategoryList = (templateType = type) => {
    getComponentClassificationPage({
      pageNo: 1,
      pageSize: 100,
      type: templateType
    }).then(result => {
      if (result.code === 0) {
        setCategories([
          {
            id: 'all',
            name: '全部分类',
            type: 2
          },
          ...result.data.list
        ]);
      }
    })
    .catch(error => {
      console.error(`获取分类列表失败：${templateType}`, error);
      // 加载失败时使用默认分类
      setCategories([
        {
          id: 'all',
          name: '全部分类',
          type: 2
        },
        {
          id: 1,
          name: '人事行政',
          type: 2
        },
        {
          id: 2,
          name: 'CRM销售',
          type: 2
        },
        {
          id: 3,
          name: '项目管理',
          type: 2
        },
        {
          id: 4,
          name: '财务报销',
          type: 2
        },
        {
          id: 5,
          name: '工单售后',
          type: 2
        }
      ]);
    });
  };

  // 处理分类切换
  const handleCategoryChange = (category: any) => {
    setSelectedCategory(category);
    setAmisKey(Date.now());
  };

  // 处理搜索
  const handleSearch = (keyword: string) => {
    setSearchKeyword(keyword);
    setAmisKey(Date.now());
  };

  // 定义AMIS Schema
  const schema: Schema = {
    type: 'page',
    body: [
      {
        type: 'grid',
        className: 'p-3',
        columns: [
          {
            body: [
              {
                type: 'button-group',
                buttons: categories.map(category => ({
                  type: 'button',
                  label: category.name,
                  level: selectedCategory.id === category.id ? 'primary' : 'default',
                  className: 'category-button',
                  onClick: () => handleCategoryChange(category)
                }))
              }
            ],
            md: 9
          },
          {
            body: [
              {
                type: 'input-group',
                body: [
                  {
                    type: 'input-text',
                    placeholder: '请输入',
                    inputClassName: 'search-input',
                    name: 'keyword',
                    value: searchKeyword,
                    onChange: (value: string) => setSearchKeyword(value),
                    onEnter: () => handleSearch(searchKeyword)
                  },
                  {
                    type: 'button',
                    icon: 'fa fa-search',
                    onClick: () => handleSearch(searchKeyword)
                  }
                ]
              }
            ],
            md: 3
          }
        ]
      },
      {
        type: 'crud',
        api: {
          method: 'get',
          url: '/admin-api/system/component-template/page',
          data: {
            pageNo: '${page}',
            pageSize: '${perPage}',
            classificationId: selectedCategory && selectedCategory.id !== 'all' 
              ? selectedCategory.id 
              : undefined,
            name: searchKeyword || undefined,
            type: 2,
            status: 1
          },
          requestAdaptor: function(api: any) {
            const data = {...api.data};
            Object.keys(data).forEach(key => {
              if (data[key] === undefined || data[key] === '' || data[key] === null) {
                delete data[key];
              }
            });
            api.data = data;
            return api;
          }
        },
        syncLocation: false,
        mode: 'cards',
        columnsCount: 4,
        card: {
          body: [
            {
              type: 'flex',
              alignItems: 'center',
              items: [
                {
                  type: 'container',
                  style: {
                    width: '48px',
                    height: '48px',
                    // background: '#4D8AFF',
                    // borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '16px',
                  },
                  body: [
                    {
                      type: 'html',
                      name: 'coverImage',
                    }
                  ]
                },
                {
                  type: 'container',
                  style: {
                    flex: 1
                  },
                  body: [
                    {
                      type: 'tpl',
                      tpl: '<div class="name-color" >${name}</div>'
                    },
                    {
                      type: 'tpl',
                      tpl: '<div class="remark-color" >${remark}</div>'
                    }
                  ]
                },
                {
                  type: 'dropdown-button',
                  label: '',
                  hideCaret: true,
                  icon: 'fa fa-ellipsis-h',
                  align: 'right',
                  trigger: 'hover',
                  size: 'sm',
                  btnClassName: 'border-0',
                  buttons: [
                    {
                      type: 'button',
                      label: '查看',
                      actionType: 'link',
                      link: '/app${applicationId}/admin'
                    },
                    {
                      type: 'button',
                      label: '使用',
                      actionType: 'dialog',
                      dialog: {
                        title: '选择项目',
                        body: {
                          type: 'form',
                          api: {
                            method: 'post',
                            url: '/admin-api/system/application/create',
                            data: {
                              componentTemplateId: '${id}',
                              name: '${name}',
                              remark: '${remark}',
                              logo: '${coverImage}',
                              projectId: '${projectId}',
                              isTemplate: 1
                            },
                            adaptor: function (payload: any, response: any, api: any, context: any) {
                              props.history.push(`/app${payload.data}/admin`);
                              return payload;
                            }
                          },
                          body: [
                            {
                              type: 'input-text',
                              name: 'name',
                              label: '应用名称',
                              value: '${name}'
                            },
                            {
                              type: 'textarea',
                              name: 'remark',
                              label: '应用描述',
                              value: '${remark}'
                            },
                            {
                              type: 'select',
                              selectMode: 'tree',
                              name: 'projectId',
                              label: '项目',
                              source: {
                                method: 'get',
                                url: '/admin-api/system/team-and-project/list',
                                adaptor: function (payload: any, response: any, api: any, context: any) {
                                  const filteredData = payload.data
                                  const options = filteredData.map((item: any) => ({
                                    label: item.name,
                                    // value: item.id,
                                    children: item.children.map((child: any) => ({
                                      label: child.name,
                                      value: child.id
                                    }))
                                  }));
                                  return {
                                    ...payload,
                                    data: {
                                      options: options
                                    }
                                  };
                                }
                              }
                            },
                          ]
                        }
                      }
                    },
 
                  ]
                }
              ]
            }
          ]
        },
        footerToolbar: [
          'statistics',
          'switch-per-page',
          'pagination'
        ]
      }
    ]
  };

  return <AMISRenderer key={amisKey} schema={schema} />;
};

export default AppTemplate; 