import React from 'react';
import {Renderer, FormItem} from 'amis';
import {FormControlProps} from 'amis-core';
import {EditEntryIcon} from '../../utils/schemaDataSet/EditEntryIcon';
import './input-appicon.scss'; // 假设我们会创建这个样式文件

// 定义图标数据类型
interface IconData {
  icon: string;
  iconColor: string;
  iconBg: string;
  [key: string]: any; // 允许其他属性
}

export interface InputAppIconProps extends FormControlProps {
  label?: string;
  name: string;
  clearable?: boolean;
  placeholder?: string;
  value?: string | IconData;
  static?: boolean;
  width?: number;
  height?: number;
  borderRadius?: number | string;
  frameImage?: string; // 背景图片地址
  limit: undefined;
}

@Renderer({
  type: 'input-appicon',
  name: 'input-appicon'
})
export class InputAppIconRenderer extends React.PureComponent<InputAppIconProps> {
  private componentId = Math.random().toString(36).substring(2, 11);

  state = {
    showIconDialog: false,
    internalValue: null as any
  };

  constructor(props: InputAppIconProps) {
    super(props);
    this.handleClick = this.handleClick.bind(this);
    this.handleIconSave = this.handleIconSave.bind(this);
    console.log(`InputAppIcon 组件创建，ID: ${this.componentId}`);
  }

  componentDidMount() {
    // 确保组件挂载时注册到表单数据模型
    const {name, value, data, onChange} = this.props as any;

    // 获取初始值
    let initialValue = value;
    if (!initialValue && data && name) {
      initialValue = data[name];
    }

    console.log(`[${this.componentId}] InputAppIcon componentDidMount:`, {
      name,
      value,
      dataValue: data ? data[name] : undefined,
      initialValue,
      hasOnChange: !!onChange
    });

    // 如果有初始值，设置到内部状态
    if (initialValue && typeof initialValue === 'string') {
      try {
        const parsedValue = JSON.parse(initialValue);
        this.setState({ internalValue: parsedValue });
        console.log(`[${this.componentId}] 设置初始内部状态:`, parsedValue);
      } catch (e) {
        console.log(`[${this.componentId}] 初始值不是有效的 JSON:`, initialValue);
      }
    }
  }

  // 处理点击事件
  handleClick() {
    try {
      console.log('InputAppIcon 点击事件');
      console.log('Props 中的函数:', {
        render: !!this.props.render,
        dispatchEvent: !!(this.props as any).dispatchEvent,
        onAction: !!(this.props as any).onAction,
        env: !!(this.props as any).env
      });

      // 显示图标编辑弹框
      this.setState({ showIconDialog: true });
    } catch (error) {
      console.error('InputAppIcon handleClick 错误:', error);
    }
  }

  
  // 处理图标保存
  handleIconSave(values: any) {
    try {
      console.log('handleIconSave 接收到的数据:', values);
      console.log('数据类型:', typeof values, '是否为数组:', Array.isArray(values));

      const {onChange} = this.props;

      // 处理接收到的数据 - 支持多层嵌套数组
      let iconData: any = {};

      // 递归展开嵌套数组，直到找到实际的数据对象
      let currentData = values;
      while (Array.isArray(currentData) && currentData.length > 0) {
        currentData = currentData[0];
        console.log('展开数组，当前数据:', currentData);
      }

      // 现在 currentData 应该是实际的图标数据对象
      if (currentData && typeof currentData === 'object' &&
          (currentData.icon || currentData.iconColor || currentData.iconBg)) {
        iconData = currentData;
      }

      console.log('最终提取的图标数据:', iconData);

      if (iconData && (iconData.icon || iconData.iconColor || iconData.iconBg)) {
        const newValue = {
          icon: iconData.icon ? iconData.icon.replace(/^['"]|['"]$/g, '') : '',
          iconColor: iconData.iconColor || '',
          iconBg: iconData.iconBg || ''
        };

        console.log('更新后的值:', newValue);

        // 序列化值用于表单
        const serializedValue = JSON.stringify(newValue);
        console.log('序列化后的值:', serializedValue);

        // 先更新内部状态
        this.setState({ internalValue: newValue }, () => {
          console.log(`[${this.componentId}] 图标选择 - 内部状态已更新:`, newValue);

          // 状态更新后，调用 onChange 更新表单数据
          if (onChange) {
            try {
              // 尝试多种 onChange 调用方式
              const {name} = this.props;

              // 方式1: 只传递值
              (onChange as any)(serializedValue);
              console.log(`[${this.componentId}] 图标选择 - onChange 方式1:`, { value: serializedValue });

              // 方式2: 传递值和名称
              setTimeout(() => {
                try {
                  (onChange as any)(serializedValue, name);
                  console.log(`[${this.componentId}] 图标选择 - onChange 方式2:`, { value: serializedValue, name });
                } catch (e2) {
                  console.log(`[${this.componentId}] onChange 方式2 失败:`, e2);
                }
              }, 10);

              // 方式3: 使用 dispatchEvent
              const {dispatchEvent} = this.props as any;
              if (dispatchEvent) {
                setTimeout(() => {
                  try {
                    dispatchEvent('change', {
                      value: serializedValue
                    });
                    console.log(`[${this.componentId}] 图标选择 - dispatchEvent:`, { value: serializedValue });
                  } catch (e3) {
                    console.log(`[${this.componentId}] dispatchEvent 失败:`, e3);
                  }
                }, 20);
              }

            } catch (e) {
              console.error(`[${this.componentId}] 图标选择 - onChange 调用失败:`, e);
            }
          } else {
            console.warn(`[${this.componentId}] 图标选择 - onChange 函数不存在`);
          }
        });
      } else {
        console.warn('没有有效的图标数据');
      }

      // 关闭弹框
      this.setState({ showIconDialog: false });
    } catch (error) {
      console.error('handleIconSave 错误:', error);
      this.setState({ showIconDialog: false });
    }
  }

  // 辅助函数：从HTML字符串中提取SVG并转换为Data URL，优化兼容性
  extractSvgFromHtml(htmlString: string): string | null {
    try {
      if (!htmlString || typeof htmlString !== 'string') return null;

      // 使用正则表达式提取SVG内容
      const svgMatch = htmlString.match(/<svg[^>]*>.*?<\/svg>/i);
      if (!svgMatch) return null;

      let svgContent = svgMatch[0];

      // 从HTML span中提取背景色信息
      let backgroundColor = '#f5f5f5'; // 默认背景色
      const spanMatch = htmlString.match(/background:\s*([^;]+)/);
      if (spanMatch) {
        backgroundColor = spanMatch[1].trim();
      }

      // 替换currentColor为具体颜色，使用对比色确保可见性
      let iconColor = '#ffffff'; // 默认白色
      if (backgroundColor !== '#f5f5f5') {
        // 如果有背景色，使用白色作为图标颜色确保对比度
        iconColor = '#ffffff';
      } else {
        // 如果没有背景色，使用深色
        iconColor = '#666666';
      }
      svgContent = svgContent.replace(/currentColor/g, iconColor);

      console.log(`[${this.componentId}] 颜色处理:`, {
        backgroundColor: backgroundColor,
        iconColor: iconColor,
        originalSvg: svgContent.substring(0, 100) + '...'
      });

      // 确保SVG有正确的尺寸，使用更大的尺寸以适应背景显示
      if (!svgContent.includes('width=') || !svgContent.includes('height=')) {
        svgContent = svgContent.replace('<svg', '<svg width="100" height="100"');
      } else {
        // 如果已有尺寸，确保足够大
        svgContent = svgContent.replace(/width="[^"]*"/, 'width="100"');
        svgContent = svgContent.replace(/height="[^"]*"/, 'height="100"');
      }

      // 为SVG添加背景，创建一个带背景的SVG
      const innerSvgContent = svgContent.replace(/<svg[^>]*>|<\/svg>/g, '');
      const wrappedSvg = `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <rect width="100" height="100" fill="${backgroundColor}" rx="8"/>
        <g transform="translate(50,50) translate(-8,-8) scale(2)">
          ${innerSvgContent}
        </g>
      </svg>`;

      console.log(`[${this.componentId}] 生成的包装SVG:`, wrappedSvg);

      // 转换为Data URL
      const encodedSvg = encodeURIComponent(wrappedSvg);
      return `data:image/svg+xml,${encodedSvg}`;
    } catch (error) {
      console.error('提取SVG失败:', error);
      return null;
    }
  }

  // 辅助函数：处理frameImage，支持变量替换和SVG提取
  processFrameImage(frameImage: string, data: any): string | null {
    try {
      if (!frameImage) return null;

      // 处理变量替换
      let processedImage = frameImage;
      if (frameImage.includes('${') && data) {
        // 简单的变量替换
        processedImage = frameImage.replace(/\$\{(\w+)\}/g, (match, varName) => {
          return data[varName] || match;
        });
      }

      console.log(`[${this.componentId}] 处理后的frameImage:`, processedImage);

      // 如果处理后的内容包含HTML/SVG，尝试提取SVG
      if (processedImage.includes('<svg') || processedImage.includes('<span')) {
        const svgDataUrl = this.extractSvgFromHtml(processedImage);
        if (svgDataUrl) {
          console.log(`[${this.componentId}] 从HTML中提取的SVG Data URL:`, svgDataUrl);
          return svgDataUrl;
        }
      }

      // 如果是普通的图片URL，直接返回
      if (processedImage.startsWith('http') || processedImage.startsWith('data:image/') || processedImage.startsWith('/')) {
        return processedImage;
      }

      console.warn(`[${this.componentId}] 无法处理的frameImage格式:`, processedImage);
      return null;
    } catch (error) {
      console.error(`[${this.componentId}] 处理frameImage时出错:`, error);
      return null;
    }
  }

  renderIcon() {
    try {
      const {value, data, name, width = 80, height = 80, borderRadius = '8px', themeCss, classnames: cx, frameImage} = this.props as any;

      // 添加详细的调试信息
      console.log(`[${this.componentId}] renderIcon 调试信息:`, {
        frameImage: frameImage,
        frameImageType: typeof frameImage,
        data: data,
        propsKeys: Object.keys(this.props),
        hasFrameImage: !!frameImage,
        allProps: this.props
      });

      // 如果frameImage为空，尝试直接测试
      if (!frameImage) {
        console.warn(`[${this.componentId}] frameImage 为空，请检查配置`);
      }

      // 获取当前值，支持多种数据源
      let currentValue = this.state.internalValue || value;

      // 如果还没有值，尝试从 data[name] 获取（用于编辑/查看模式）
      if (!currentValue && data && name) {
        currentValue = data[name];
      }

      console.log(`[${this.componentId}] renderIcon 调试信息:`, {
        propsValue: value,
        dataValue: data ? data[name] : undefined,
        internalValue: this.state.internalValue,
        currentValue: currentValue,
        currentValueType: typeof currentValue,
        name: name,
        themeCss: themeCss
      });

      // 解析当前值
      let iconData: IconData = {icon: '', iconColor: '', iconBg: ''};
      if (currentValue) {
        if (typeof currentValue === 'string') {
          try {
            const parsedValue = JSON.parse(currentValue);
            iconData = {
              icon: parsedValue.icon || '',
              iconColor: parsedValue.iconColor || '',
              iconBg: parsedValue.iconBg || ''
            };
            console.log(`[${this.componentId}] 从字符串解析的图标数据:`, iconData);
          } catch (e) {
            console.log(`[${this.componentId}] JSON 解析失败:`, e);
            // 解析失败，可能是直接的图标字符串
            iconData = { icon: currentValue, iconColor: '', iconBg: '' };
          }
        } else if (typeof currentValue === 'object') {
          iconData = {
            icon: (currentValue as IconData).icon || '',
            iconColor: (currentValue as IconData).iconColor || '',
            iconBg: (currentValue as IconData).iconBg || ''
          };
          console.log(`[${this.componentId}] 从对象获取的图标数据:`, iconData);
        }
      }

      console.log(`[${this.componentId}] 最终图标数据:`, iconData);

      // 基础样式
      let style: any = {
        width: width,
        height: height,
        borderRadius: borderRadius,
        background: iconData.iconBg || '#F5F7FA',
        color: iconData.iconColor || '#666',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '24px',
        cursor: 'pointer',
        border: '1px dashed #ddd'
      };

      // 处理 frameImage，支持变量替换和SVG提取
      let processedFrameImage = this.processFrameImage(frameImage, data);

      // 临时测试：如果没有frameImage，使用测试图片
      if (!processedFrameImage && frameImage === 'TEST') {
        processedFrameImage = 'https://via.placeholder.com/100x100/b8e986/ffffff?text=TEST';
        console.log(`[${this.componentId}] 使用测试图片:`, processedFrameImage);
      }

      // SVG测试：如果frameImage是SVGTEST，使用简单的SVG
      if (!processedFrameImage && frameImage === 'SVGTEST') {
        const testSvg = `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
          <rect width="100" height="100" fill="#b8e986" rx="8"/>
          <circle cx="50" cy="50" r="20" fill="#ffffff"/>
          <text x="50" y="55" text-anchor="middle" fill="#333" font-size="12">SVG</text>
        </svg>`;
        const encodedTestSvg = encodeURIComponent(testSvg);
        processedFrameImage = `data:image/svg+xml,${encodedTestSvg}`;
        console.log(`[${this.componentId}] 使用测试SVG:`, processedFrameImage);
      }

      if (processedFrameImage) {
        console.log(`[${this.componentId}] 应用背景图片:`, processedFrameImage);

        // 根据图片类型优化显示样式
        const isSvgDataUrl = processedFrameImage.startsWith('data:image/svg+xml');

        if (isSvgDataUrl) {
          // SVG背景优化显示 - 改为使用cover模式
          style.backgroundImage = `url(${processedFrameImage})`;
          style.backgroundSize = 'cover'; // 改为cover确保填满容器
          style.backgroundPosition = 'center';
          style.backgroundRepeat = 'no-repeat';
          // 使用简写属性确保优先级
          style.background = `url(${processedFrameImage}) center/cover no-repeat`;
          console.log(`[${this.componentId}] SVG背景样式:`, {
            backgroundImage: style.backgroundImage,
            backgroundSize: style.backgroundSize,
            backgroundPosition: style.backgroundPosition,
            backgroundRepeat: style.backgroundRepeat,
            svgDataUrlPreview: processedFrameImage.substring(0, 200) + '...'
          });
        } else {
          // 普通图片背景
          style.backgroundImage = `url(${processedFrameImage})`;
          style.backgroundSize = 'cover';
          style.backgroundPosition = 'center';
          style.backgroundRepeat = 'no-repeat';
          style.background = `url(${processedFrameImage}) center/cover no-repeat`;
          console.log(`[${this.componentId}] 图片背景样式:`, {
            backgroundImage: style.backgroundImage,
            backgroundSize: style.backgroundSize,
            background: style.background
          });
        }

        // 如果有背景图，调整边框样式
        style.border = '1px solid #ddd';

        // 强制显示背景图片的额外样式
        style.minWidth = width;
        style.minHeight = height;
        style.overflow = 'hidden';

        // 强制设置背景图片，确保显示
        style.backgroundImage = `url("${processedFrameImage}")`;
        style.backgroundSize = isSvgDataUrl ? 'cover' : 'cover';
        style.backgroundPosition = 'center';
        style.backgroundRepeat = 'no-repeat';

        // 添加一个测试背景色来验证容器是否可见
        if (!style.backgroundImage || style.backgroundImage === 'none') {
          style.backgroundColor = '#ff0000'; // 红色测试背景
          console.log(`[${this.componentId}] 应用测试红色背景`);
        }

        console.log(`[${this.componentId}] 最终样式:`, {
          width: style.width,
          height: style.height,
          backgroundImage: style.backgroundImage ? style.backgroundImage.substring(0, 50) + '...' : 'none',
          backgroundSize: style.backgroundSize,
          backgroundColor: style.backgroundColor
        });

        // 添加图片加载错误处理（只对非SVG Data URL进行检测）
        if (!isSvgDataUrl) {
          const img = new Image();
          img.onload = () => {
            console.log(`[${this.componentId}] 背景图片加载成功:`, processedFrameImage);
          };
          img.onerror = () => {
            console.error(`[${this.componentId}] 背景图片加载失败:`, processedFrameImage);
          };
          img.src = processedFrameImage;
        } else {
          console.log(`[${this.componentId}] SVG背景图片已应用:`, processedFrameImage.substring(0, 100) + '...');
        }
      }

      // 应用上传区域样式 (addBtnControlClassName)
      if (themeCss && themeCss.addBtnControlClassName) {
        const uploadAreaStyles = themeCss.addBtnControlClassName;

        // 应用各种状态的样式
        if (uploadAreaStyles['background:default']) {
          style.background = uploadAreaStyles['background:default'];
        }

        if (uploadAreaStyles['border:default']) {
          const borderConfig = uploadAreaStyles['border:default'];
          if (borderConfig['top-border-width']) {
            style.borderWidth = borderConfig['top-border-width'];
          }
          if (borderConfig['top-border-style']) {
            style.borderStyle = borderConfig['top-border-style'];
          }
          if (borderConfig['top-border-color']) {
            style.borderColor = borderConfig['top-border-color'];
          }
        }

        if (uploadAreaStyles['radius:default']) {
          const radiusConfig = uploadAreaStyles['radius:default'];
          if (radiusConfig['top-left-border-radius']) {
            style.borderRadius = radiusConfig['top-left-border-radius'];
          }
        }

        if (uploadAreaStyles['padding-and-margin:default']) {
          const paddingConfig = uploadAreaStyles['padding-and-margin:default'];
          if (paddingConfig.paddingTop) style.paddingTop = paddingConfig.paddingTop;
          if (paddingConfig.paddingRight) style.paddingRight = paddingConfig.paddingRight;
          if (paddingConfig.paddingBottom) style.paddingBottom = paddingConfig.paddingBottom;
          if (paddingConfig.paddingLeft) style.paddingLeft = paddingConfig.paddingLeft;
          if (paddingConfig.marginTop) style.marginTop = paddingConfig.marginTop;
          if (paddingConfig.marginRight) style.marginRight = paddingConfig.marginRight;
          if (paddingConfig.marginBottom) style.marginBottom = paddingConfig.marginBottom;
          if (paddingConfig.marginLeft) style.marginLeft = paddingConfig.marginLeft;
        }
      }

      // 生成 CSS 类名
      let className = 'input-appicon';
      if (cx && themeCss && themeCss.addBtnControlClassName) {
        className = cx('input-appicon', themeCss.addBtnControlClassName);
      }

      return (
        <div
          className={className}
          style={style}
          onClick={this.handleClick}
        >
          {iconData.icon ? (
            <div
              dangerouslySetInnerHTML={{ __html: iconData.icon }}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                // 如果有背景图，添加一些样式确保图标清晰可见
                ...(frameImage ? {
                  textShadow: '0 0 3px rgba(255,255,255,0.8), 0 0 6px rgba(255,255,255,0.6)',
                  filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.3))'
                } : {})
              }}
            />
          ) : (
            this.renderAddIcon()
          )}
        </div>
      );
    } catch (error) {
      console.error('renderIcon 错误:', error);
      return (
        <div style={{
          width: 80,
          height: 80,
          background: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid red'
        }}>
          错误
        </div>
      );
    }
  }

  renderIconDialog(currentValue: any) {
    try {
      const {render, env, dispatchEvent, onAction} = this.props as any;

      console.log('renderIconDialog 调试信息:', {
        render: !!render,
        env: !!env,
        dispatchEvent: !!dispatchEvent,
        onAction: !!onAction,
        currentValue: currentValue
      });

      if (!render) {
        console.warn('InputAppIcon: render 函数不可用，使用简单模态框');
        return this.renderSimpleDialog();
      }

      const dialogSchema = EditEntryIcon({
        logo: currentValue
      });

      console.log('对话框 schema:', dialogSchema);

      // 简单的对话框配置
      const enhancedSchema = {
        ...dialogSchema,
        show: true,
        onConfirm: (values: any) => {
          this.handleIconSave([values]);
        },
        onClose: () => {
          this.setState({ showIconDialog: false });
        }
      };


      const result = render('dialog', enhancedSchema);

      return result;
    } catch (error) {
      return this.renderSimpleDialog();
    }
  }

  renderSimpleDialog() {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          maxWidth: '500px',
          width: '90%'
        }}>
          <h3>图标选择</h3>
          <p>图标选择功能暂时不可用</p>
          <div style={{textAlign: 'right', marginTop: '20px'}}>
            <button
              onClick={() => this.setState({ showIconDialog: false })}
              style={{
                padding: '8px 16px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 渲染添加图标按钮，支持 themeCss 配置
  renderAddIcon() {
    try {
      const {themeCss, classnames: cx} = this.props as any;

      // 获取配置的图标
      let customIcon = null;
      let iconColor = '#999';
      let iconSize = '28px';
      let addBtnStyle: any = {};

      if (themeCss) {
        // 获取自定义图标
        const addBtnConfig = themeCss.addBtnControlClassName;
        if (addBtnConfig && addBtnConfig['--inputImage-base-default-icon']) {
          let iconString = addBtnConfig['--inputImage-base-default-icon'];
          // 移除可能的引号
          if (iconString.startsWith("'") && iconString.endsWith("'")) {
            iconString = iconString.slice(1, -1);
          }
          customIcon = iconString;
        }

        // 获取图标颜色
        if (addBtnConfig && addBtnConfig['icon-color:default']) {
          iconColor = addBtnConfig['icon-color:default'];
        }

        // 应用 addBtnControlClassName 的样式
        if (addBtnConfig) {
          // 字体样式
          if (addBtnConfig['font:default']) {
            const fontConfig = addBtnConfig['font:default'];
            if (fontConfig.fontSize) addBtnStyle.fontSize = fontConfig.fontSize;
            if (fontConfig.fontWeight) addBtnStyle.fontWeight = fontConfig.fontWeight;
            if (fontConfig.lineHeight) addBtnStyle.lineHeight = fontConfig.lineHeight;
            if (fontConfig['font-family']) addBtnStyle.fontFamily = fontConfig['font-family'];
          }

          // 背景样式
          if (addBtnConfig['background:default']) {
            addBtnStyle.background = addBtnConfig['background:default'];
          }

          // 边框样式
          if (addBtnConfig['border:default']) {
            const borderConfig = addBtnConfig['border:default'];
            if (borderConfig['top-border-width']) {
              addBtnStyle.borderWidth = borderConfig['top-border-width'];
            }
            if (borderConfig['top-border-style']) {
              addBtnStyle.borderStyle = borderConfig['top-border-style'];
            }
            if (borderConfig['top-border-color']) {
              addBtnStyle.borderColor = borderConfig['top-border-color'];
            }
          }

          // 内外边距
          if (addBtnConfig['padding-and-margin:default']) {
            const paddingConfig = addBtnConfig['padding-and-margin:default'];
            if (paddingConfig.paddingTop) addBtnStyle.paddingTop = paddingConfig.paddingTop;
            if (paddingConfig.paddingRight) addBtnStyle.paddingRight = paddingConfig.paddingRight;
            if (paddingConfig.paddingBottom) addBtnStyle.paddingBottom = paddingConfig.paddingBottom;
            if (paddingConfig.paddingLeft) addBtnStyle.paddingLeft = paddingConfig.paddingLeft;
            if (paddingConfig.marginTop) addBtnStyle.marginTop = paddingConfig.marginTop;
            if (paddingConfig.marginRight) addBtnStyle.marginRight = paddingConfig.marginRight;
            if (paddingConfig.marginBottom) addBtnStyle.marginBottom = paddingConfig.marginBottom;
            if (paddingConfig.marginLeft) addBtnStyle.marginLeft = paddingConfig.marginLeft;
          }

          // 圆角
          if (addBtnConfig['radius:default']) {
            const radiusConfig = addBtnConfig['radius:default'];
            if (radiusConfig['top-left-border-radius']) {
              addBtnStyle.borderRadius = radiusConfig['top-left-border-radius'];
            }
          }
        }

        // 从 addBtnControlClassName 获取图标大小配置
        if (addBtnConfig) {
          if (addBtnConfig.iconSize) {
            iconSize = addBtnConfig.iconSize;
          } else if (addBtnConfig['font-size']) {
            iconSize = addBtnConfig['font-size'];
          } else if (addBtnConfig['--icon-size']) {
            iconSize = addBtnConfig['--icon-size'];
          }

          // 也尝试从字体配置中获取大小
          if (addBtnConfig['font:default'] && addBtnConfig['font:default'].fontSize) {
            iconSize = addBtnConfig['font:default'].fontSize;
          }
        }
      }

      console.log(`[${this.componentId}] renderAddIcon 配置:`, {
        customIcon: !!customIcon,
        iconColor,
        iconSize,
        addBtnStyle,
        iconControlClassName: themeCss?.iconControlClassName,
        addBtnControlClassName: themeCss?.addBtnControlClassName,
        fullThemeCss: themeCss
      });

      // 合并样式
      const finalStyle = {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: iconColor,
        fontSize: iconSize,
        width: '100%',
        height: '100%',
        ...addBtnStyle
      };

      // 生成 CSS 类名
      let className = 'input-appicon-add-btn';
      if (cx && themeCss && themeCss.addBtnControlClassName) {
        className = cx('input-appicon-add-btn', themeCss.addBtnControlClassName);
      }

      // 如果有自定义图标，使用自定义图标
      if (customIcon) {
        // 处理 SVG 图标的大小
        let processedIcon = customIcon;
        if (customIcon.includes('<svg')) {
          // 为 SVG 添加或修改 width 和 height 属性
          processedIcon = customIcon.replace(
            /<svg([^>]*)>/,
            (_match: string, attributes: string) => {
              // 移除现有的 width 和 height 属性
              let newAttributes = attributes.replace(/\s*(width|height)="[^"]*"/g, '');
              // 添加新的大小属性
              return `<svg${newAttributes} width="${iconSize}" height="${iconSize}">`;
            }
          );
        }

        return (
          <div
            className={className}
            style={finalStyle}
            dangerouslySetInnerHTML={{ __html: processedIcon }}
          />
        );
      }

      // 否则使用默认的 + 图标
      return (
        <span
          className={className}
          style={finalStyle}
        >
          +
        </span>
      );
    } catch (error) {
      console.error(`[${this.componentId}] renderAddIcon 错误:`, error);
      return <span style={{color: '#999', fontSize: '28px'}}>+</span>;
    }
  }

  // 静态模式渲染，用于 CRUD 列表等只读场景
  renderStaticIcon() {
    try {
      const {value, data, name, width = 32, height = 32, borderRadius = '4px', frameImage} = this.props as any;

      // 在 CRUD 列表中，数据通过 data[name] 传递
      let actualValue = value;
      if (!actualValue && data && name) {
        actualValue = data[name];
      }

      console.log(`[${this.componentId}] renderStaticIcon - props:`, {
        value,
        data,
        name,
        actualValue
      });

      // 解析图标数据
      let iconData: IconData = {icon: '', iconColor: '', iconBg: ''};
      if (actualValue) {
        if (typeof actualValue === 'string') {
          try {
            iconData = JSON.parse(actualValue);
            console.log(`[${this.componentId}] 解析的图标数据:`, iconData);
          } catch (e) {
            console.warn(`[${this.componentId}] JSON 解析失败:`, e);
            // 如果解析失败，可能是直接的图标字符串
            iconData = { icon: actualValue, iconColor: '', iconBg: '' };
          }
        } else if (typeof actualValue === 'object') {
          iconData = actualValue as IconData;
        }
      }

      // 如果没有图标数据，显示空白占位符
      if (!iconData.icon) {
        const emptyStyle: any = {
          width: width,
          height: height,
          borderRadius: borderRadius,
          background: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          color: '#999',
          border: '1px solid #e0e0e0'
        };

        // 如果配置了背景图，也应用到空白占位符
        const processedFrameImageEmpty = this.processFrameImage(frameImage, data);
        if (processedFrameImageEmpty) {
          console.log(`[${this.componentId}] 空白占位符应用背景图片:`, processedFrameImageEmpty);

          const isSvgDataUrl = processedFrameImageEmpty.startsWith('data:image/svg+xml');

          if (isSvgDataUrl) {
            // SVG背景优化显示
            emptyStyle.backgroundImage = `url(${processedFrameImageEmpty})`;
            emptyStyle.backgroundSize = 'contain';
            emptyStyle.backgroundPosition = 'center';
            emptyStyle.backgroundRepeat = 'no-repeat';
            emptyStyle.background = `url(${processedFrameImageEmpty}) center/contain no-repeat`;
          } else {
            // 普通图片背景
            emptyStyle.backgroundImage = `url(${processedFrameImageEmpty})`;
            emptyStyle.backgroundSize = 'cover';
            emptyStyle.backgroundPosition = 'center';
            emptyStyle.backgroundRepeat = 'no-repeat';
            emptyStyle.background = `url(${processedFrameImageEmpty}) center/cover no-repeat`;
          }
        }

        return (
          <div style={emptyStyle}>
          </div>
        );
      }

      // 渲染图标
      const style: any = {
        width: width,
        height: height,
        borderRadius: borderRadius,
        background: iconData.iconBg || '#f5f5f5',
        color: iconData.iconColor || '#666',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '16px',
        border: '1px solid #e0e0e0'
      };

      // 如果配置了 frameImage，将其作为背景图
      const processedFrameImageStatic = this.processFrameImage(frameImage, data);
      if (processedFrameImageStatic) {
        console.log(`[${this.componentId}] 静态模式应用背景图片:`, processedFrameImageStatic);

        const isSvgDataUrl = processedFrameImageStatic.startsWith('data:image/svg+xml');

        if (isSvgDataUrl) {
          // SVG背景优化显示
          style.backgroundImage = `url(${processedFrameImageStatic})`;
          style.backgroundSize = 'contain';
          style.backgroundPosition = 'center';
          style.backgroundRepeat = 'no-repeat';
          style.background = `url(${processedFrameImageStatic}) center/contain no-repeat`;
        } else {
          // 普通图片背景
          style.backgroundImage = `url(${processedFrameImageStatic})`;
          style.backgroundSize = 'cover';
          style.backgroundPosition = 'center';
          style.backgroundRepeat = 'no-repeat';
          style.background = `url(${processedFrameImageStatic}) center/cover no-repeat`;
        }
      }

      return (
        <div
          className="input-appicon-static"
          style={style}
          title={`图标: ${iconData.icon ? '已设置' : '未设置'}`}
        >
          <div
            dangerouslySetInnerHTML={{ __html: iconData.icon }}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              height: '100%',
              // 如果有背景图，添加一些样式确保图标清晰可见
              ...(frameImage ? {
                textShadow: '0 0 2px rgba(255,255,255,0.8), 0 0 4px rgba(255,255,255,0.6)',
                filter: 'drop-shadow(0 0 1px rgba(0,0,0,0.3))'
              } : {})
            }}
          />
        </div>
      );
    } catch (error) {
      console.error(`[${this.componentId}] renderStaticIcon 错误:`, error);
      return (
        <div style={{
          width: 32,
          height: 32,
          background: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid red',
          fontSize: '12px',
          color: 'red'
        }}>
          错误
        </div>
      );
    }
  }

  render() {
    try {
      const {render, static: isStatic, name, value, label, themeCss, classnames: cx} = this.props as any;

      console.log(`[${this.componentId}] InputAppIcon render - 使用隐藏输入框方案`);

      if (isStatic) {
        // 静态模式：只显示图标，用于 CRUD 列表等场景
        return this.renderStaticIcon();
      }

      if (!render) {
        console.error('InputAppIcon: render 函数不可用');
        return <div>图标组件渲染错误：render 函数不可用</div>;
      }

      // 将图标数据序列化为字符串
      let serializedValue = '';
      if (this.state.internalValue) {
        serializedValue = JSON.stringify(this.state.internalValue);
      } else if (value) {
        serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      }

      console.log(`[${this.componentId}] 当前序列化值:`, serializedValue);
      console.log(`[${this.componentId}] 状态详情:`, {
        internalValue: this.state.internalValue,
        propsValue: value,
        serializedValue: serializedValue
      });

      // 创建一个隐藏的 input-text 来处理表单数据
      const hiddenInputConfig = {
        type: 'input-text',
        name,
        value: serializedValue,
        style: { display: 'none' },
        // 移除 onChange 避免死循环
      };

      const hiddenInput = render('input-text', hiddenInputConfig);

      // 处理整个组件的样式 (labelClassName)
      let wrapperStyle: any = {};
      let wrapperClassName = 'input-appicon-wrapper';

      if (themeCss && themeCss.labelClassName) {
        const wrapperConfig = themeCss.labelClassName;

        // 应用字体样式
        if (wrapperConfig['font:default']) {
          const fontConfig = wrapperConfig['font:default'];
          if (fontConfig.fontSize) wrapperStyle.fontSize = fontConfig.fontSize;
          if (fontConfig.fontWeight) wrapperStyle.fontWeight = fontConfig.fontWeight;
          if (fontConfig.lineHeight) wrapperStyle.lineHeight = fontConfig.lineHeight;
          if (fontConfig['font-family']) wrapperStyle.fontFamily = fontConfig['font-family'];
        }

        // 应用其他样式
        if (wrapperConfig['background:default']) {
          wrapperStyle.background = wrapperConfig['background:default'];
        }

        if (wrapperConfig['border:default']) {
          const borderConfig = wrapperConfig['border:default'];
          if (borderConfig['top-border-width']) {
            wrapperStyle.borderWidth = borderConfig['top-border-width'];
          }
          if (borderConfig['top-border-style']) {
            wrapperStyle.borderStyle = borderConfig['top-border-style'];
          }
          if (borderConfig['top-border-color']) {
            wrapperStyle.borderColor = borderConfig['top-border-color'];
          }
        }

        if (wrapperConfig['padding-and-margin:default']) {
          const paddingConfig = wrapperConfig['padding-and-margin:default'];
          if (paddingConfig.paddingTop) wrapperStyle.paddingTop = paddingConfig.paddingTop;
          if (paddingConfig.paddingRight) wrapperStyle.paddingRight = paddingConfig.paddingRight;
          if (paddingConfig.paddingBottom) wrapperStyle.paddingBottom = paddingConfig.paddingBottom;
          if (paddingConfig.paddingLeft) wrapperStyle.paddingLeft = paddingConfig.paddingLeft;
          if (paddingConfig.marginTop) wrapperStyle.marginTop = paddingConfig.marginTop;
          if (paddingConfig.marginRight) wrapperStyle.marginRight = paddingConfig.marginRight;
          if (paddingConfig.marginBottom) wrapperStyle.marginBottom = paddingConfig.marginBottom;
          if (paddingConfig.marginLeft) wrapperStyle.marginLeft = paddingConfig.marginLeft;
        }

        // 生成 CSS 类名
        if (cx) {
          wrapperClassName = cx('input-appicon-wrapper', themeCss.labelClassName);
        }
      }

      // 处理标题样式 (titleClassName)
      let titleStyle: any = {};
      let titleClassName = 'input-appicon-title';

      if (themeCss && themeCss.titleClassName) {
        const titleConfig = themeCss.titleClassName;

        // 应用字体样式
        if (titleConfig['font:default']) {
          const fontConfig = titleConfig['font:default'];
          if (fontConfig.fontSize) titleStyle.fontSize = fontConfig.fontSize;
          if (fontConfig.fontWeight) titleStyle.fontWeight = fontConfig.fontWeight;
          if (fontConfig.lineHeight) titleStyle.lineHeight = fontConfig.lineHeight;
          if (fontConfig['font-family']) titleStyle.fontFamily = fontConfig['font-family'];
        }

        // 应用其他样式
        if (titleConfig['background:default']) {
          titleStyle.background = titleConfig['background:default'];
        }

        if (titleConfig['border:default']) {
          const borderConfig = titleConfig['border:default'];
          if (borderConfig['top-border-width']) {
            titleStyle.borderWidth = borderConfig['top-border-width'];
          }
          if (borderConfig['top-border-style']) {
            titleStyle.borderStyle = borderConfig['top-border-style'];
          }
          if (borderConfig['top-border-color']) {
            titleStyle.borderColor = borderConfig['top-border-color'];
          }
        }

        // 生成 CSS 类名
        if (cx) {
          titleClassName = cx('input-appicon-title', themeCss.titleClassName);
        }
      }

      // 返回包含隐藏输入框和自定义界面的组合
      return (
        <div
          className={wrapperClassName}
          style={wrapperStyle}
        >
          {/* 隐藏的输入框，用于表单数据处理 */}
          {hiddenInput}

          {/* 自定义显示界面 */}
          <div className="input-appicon-display">
            {label && (
              <label
                className={titleClassName}
                style={titleStyle}
              >
                {label}
              </label>
            )}
            {this.renderIcon()}
          </div>

          {/* 图标选择对话框 */}
          {this.state.showIconDialog && this.renderIconDialog(this.props.value)}
        </div>
      );
    } catch (error) {
      console.error('InputAppIcon render 错误:', error);
      return (
        <div className="input-appicon-wrapper">
          <div style={{color: 'red', padding: '10px', border: '1px solid red'}}>
            图标组件渲染错误，请检查控制台
          </div>
        </div>
      );
    }
  }


}
