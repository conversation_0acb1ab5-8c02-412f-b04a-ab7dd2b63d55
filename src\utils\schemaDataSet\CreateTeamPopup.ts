// 创建团队弹窗
const CreateTeamPopup = () => {
  return {
    type: 'dialog',
    body: [
      {
        type: 'container',
        body: [
          {
            type: 'tpl',
            tpl: '创建新团队',
            inline: true,
            wrapperComponent: '',
            id: 'u:1eafdeaa66ea',
            themeCss: {
              baseControlClassName: {
                'font:default': {
                  fontSize: '1.5rem',
                  fontWeight: 'var(--fonts-weight-4)'
                },
                'padding-and-margin:default': {
                  marginBottom: '0.75rem'
                }
              }
            }
          },
          {
            type: 'tpl',
            tpl: '你可以和成员在团队中一起协作',
            inline: true,
            wrapperComponent: '',
            id: 'u:bce13c037362',
            themeCss: {
              baseControlClassName: {
                'font:default': {
                  color: '#999999',
                  fontSize: '0.875rem',
                  fontWeight: 'var(--fonts-weight-5)'
                },
                'padding-and-margin:default': {
                  marginBottom: '2.5rem'
                }
              }
            }
          },
          {
            id: 'u:87295a3e7321',
            type: 'form',
            title: '',
            mode: 'flex',
            labelAlign: 'top',
            dsType: 'api',
            feat: 'Insert',
            body: [
              {
                type: 'input-text',
                label: '团队名称',
                name: 'text_1731307771159',
                row: 0,
                id: 'u:5fb80f338e2c',
                value: '',
                clearable: true,
                required: true,
                labelAlign: 'top',
                placeholder: '输入团队描述',
                themeCss: {
                  inputControlClassName: {
                    'background:default': '#f0f0f0'
                  }
                }
              },
              {
                type: 'textarea',
                label: '描述（可选）',
                name: 'textarea_1731307833062',
                row: 1,
                id: 'u:09482fa3c134',
                minRows: 3,
                maxRows: 20,
                placeholder: '请输入团队描述',
                labelAlign: 'top',
                themeCss: {
                  inputControlClassName: {
                    'background:default': '#f0f0f0'
                  }
                }
              }
            ],
            actions: [
              {
                type: 'button',
                label: '创建团队',
                onEvent: {
                  click: {
                    actions: [
                      {
                        actionType: 'submit',
                        componentId: 'u:87295a3e7321'
                      }
                    ]
                  }
                },
                level: 'primary',
                id: 'u:e2a8322c9010',
                block: false,
                themeCss: {
                  className: {
                    'padding-and-margin:default': {},
                    'radius:default': {
                      'top-left-border-radius': '8px',
                      'top-right-border-radius': '8px',
                      'bottom-left-border-radius': '8px',
                      'bottom-right-border-radius': '8px'
                    },
                    'font:default': {
                      fontSize: '1.125rem'
                    }
                  },
                  iconClassName: {}
                },
                wrapperCustomStyle: {
                  root: {
                    width: '100%',
                    margin: '0 !important',
                    height: '3.375rem'
                  }
                }
              }
            ],
            resetAfterSubmit: true,
            canAccessSuperData: false,
            wrapperCustomStyle: {
              root: {
                'width': '100%',
                'box-shadow': 'none !important'
              }
            },
            themeCss: {
              panelClassName: {
                'border:default': {
                  'top-border-width': 'var(--borders-width-1)',
                  'left-border-width': 'var(--borders-width-1)',
                  'right-border-width': 'var(--borders-width-1)',
                  'bottom-border-width': 'var(--borders-width-1)'
                }
              },
              headerControlClassName: {
                'border:default': {
                  'top-border-width': 'var(--borders-width-1)',
                  'left-border-width': 'var(--borders-width-1)',
                  'right-border-width': 'var(--borders-width-1)',
                  'bottom-border-width': 'var(--borders-width-1)'
                }
              },
              bodyControlClassName: {
                'padding-and-margin:default': {
                  paddingTop: 'var(--sizes-size-0)',
                  paddingRight: 'var(--sizes-size-0)',
                  paddingBottom: 'var(--sizes-size-0)',
                  paddingLeft: 'var(--sizes-size-0)'
                }
              },
              actionsControlClassName: {
                'border:default': {
                  'top-border-width': 'var(--borders-width-1)',
                  'left-border-width': 'var(--borders-width-1)',
                  'right-border-width': 'var(--borders-width-1)',
                  'bottom-border-width': 'var(--borders-width-1)'
                }
              }
            },
            api: {
              url: '/admin-api/system/team-and-project/create',
              method: 'post',
              requestAdaptor: '',
              adaptor: '',
              messages: {},
              data: {
                parentId: 0,
                name: '${text_1731307771159}',
                description: '${textarea_1731307833062}',
              }
            }
          }
        ],
        style: {
          position: 'relative',
          display: 'flex',
          inset: 'auto',
          flexWrap: 'nowrap',
          flexDirection: 'column',
          alignItems: 'flex-start'
        },
        size: 'none',
        wrapperBody: false,
        id: 'u:661593394ef7',
        isFixedHeight: false,
        isFixedWidth: false,
        themeCss: {
          baseControlClassName: {
            'background:default': 'var(--colors-neutral-fill-11)',
            'padding-and-margin:default': {
              paddingLeft: '1.5rem',
              paddingRight: '1.5rem'
            },
            'radius:default': {
              'top-left-border-radius': '16px',
              'bottom-left-border-radius': '16px'
            }
          }
        }
      }
    ],
    title: '',
    id: 'u:b2a84191c34c',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'u:bb9a6e447d20'
      },
      {
        type: 'button',
        actionType: 'submit',
        label: '确定',
        primary: true,
        id: 'u:49b63f191394'
      }
    ],
    showCloseButton: true,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    hideActions: true,
    $$ref: 'modal-ref-1',
    editorSetting: {
      displayName: '创建团队'
    }
  }
}

export {CreateTeamPopup}