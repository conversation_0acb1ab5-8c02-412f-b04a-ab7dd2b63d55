{"id": {"desc": "<p>组件 ID。默认不指定。指定则可用于在 option 或者 API 中引用组件。</p>\n"}, "show": {"desc": "\n\n<p>是否显示  组件。如果设置为 <code class=\"codespan\">false</code>，不会显示，但是数据过滤的功能还存在。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "backgroundColor": {"desc": "\n\n<p>组件的背景颜色。</p>\n", "uiControl": {"type": "color", "default": "rgba(47,69,84,0)"}}, "dataBackground": {"desc": "<p>数据阴影的样式。</p>\n"}, "dataBackground.lineStyle": {"desc": "<p>阴影的线条样式</p>\n"}, "dataBackground.lineStyle.color": {"desc": "\n\n<p>线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "dataBackground.lineStyle.width": {"desc": "\n\n<p>线宽。</p>\n", "uiControl": {"type": "number", "value": "0.5", "min": "0", "step": "0.5"}}, "dataBackground.lineStyle.type": {"desc": "\n\n<p>线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "dataBackground.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "dataBackground.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "dataBackground.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "dataBackground.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "dataBackground.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "dataBackground.areaStyle": {"desc": "<p>阴影的填充样式</p>\n"}, "dataBackground.areaStyle.color": {"desc": "\n\n<p>填充的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "dataBackground.areaStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "dataBackground.areaStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "dataBackground.areaStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "dataBackground.areaStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "dataBackground.areaStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "0.2", "min": "0", "max": "1", "step": "0.01"}}, "selectedDataBackground": {"desc": "<blockquote>\n<p>从 <code class=\"codespan\">v5.0.0</code> 开始支持</p>\n</blockquote>\n<p>选中部分数据阴影的样式。</p>\n"}, "selectedDataBackground.lineStyle": {"desc": "<p>选中部分阴影的线条样式</p>\n"}, "selectedDataBackground.lineStyle.color": {"desc": "\n\n<p>线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "selectedDataBackground.lineStyle.width": {"desc": "\n\n<p>线宽。</p>\n", "uiControl": {"type": "number", "value": "0.5", "min": "0", "step": "0.5"}}, "selectedDataBackground.lineStyle.type": {"desc": "\n\n<p>线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "selectedDataBackground.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "selectedDataBackground.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "selectedDataBackground.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "selectedDataBackground.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "selectedDataBackground.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "selectedDataBackground.areaStyle": {"desc": "<p>选中部分阴影的填充样式</p>\n"}, "selectedDataBackground.areaStyle.color": {"desc": "\n\n<p>填充的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "selectedDataBackground.areaStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "selectedDataBackground.areaStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "selectedDataBackground.areaStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "selectedDataBackground.areaStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "selectedDataBackground.areaStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "0.2", "min": "0", "max": "1", "step": "0.01"}}, "fillerColor": {"desc": "\n\n<p>选中范围的填充颜色。</p>\n", "uiControl": {"type": "color", "default": "rgba(167,183,204,0.4)"}}, "borderColor": {"desc": "\n\n<p>边框颜色。</p>\n", "uiControl": {"type": "color", "default": "#ddd"}}, "handleIcon": {"desc": "\n\n<p>两侧缩放手柄的 icon 形状，支持路径字符串，默认为：</p>\n<pre><code class=\"lang-js\">&#39;M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z&#39;\n</code></pre>\n<p>可以通过 <code class=\"codespan\">&#39;image://url&#39;</code> 设置为图片，其中 URL 为图片的链接，或者 <code class=\"codespan\">dataURI</code>。</p>\n<p>URL 为图片链接例如：</p>\n<pre><code>&#39;image://http://xxx.xxx.xxx/a/b.png&#39;\n</code></pre><p>URL 为 <code class=\"codespan\">dataURI</code> 例如：</p>\n<pre><code>&#39;image://data:image/gif;base64,R0lGODlhEAAQAMQAAORHHOVSKudfOulrSOp3WOyDZu6QdvCchPGolfO0o/XBs/fNwfjZ0frl3/zy7////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAkAABAALAAAAAAQABAAAAVVICSOZGlCQAosJ6mu7fiyZeKqNKToQGDsM8hBADgUXoGAiqhSvp5QAnQKGIgUhwFUYLCVDFCrKUE1lBavAViFIDlTImbKC5Gm2hB0SlBCBMQiB0UjIQA7&#39;\n</code></pre><p>可以通过 <code class=\"codespan\">&#39;path://&#39;</code> 将图标设置为任意的矢量路径。这种方式相比于使用图片的方式，不用担心因为缩放而产生锯齿或模糊，而且可以设置为任意颜色。路径图形会自适应调整为合适的大小。路径的格式参见 <a href=\"http://www.w3.org/TR/SVG/paths.html#PathData\" target=\"_blank\">SVG PathData</a>。可以从 Adobe Illustrator 等工具编辑导出。</p>\n<p>例如：</p>\n<pre><code>&#39;path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z&#39;\n</code></pre>", "uiControl": {"type": "icon"}}, "handleSize": {"desc": "\n\n<p>控制手柄的尺寸，可以是像素大小，也可以是相对于 dataZoom 组件宽度的百分比，默认跟 dataZoom 宽度相同。</p>\n", "uiControl": {"type": "percent", "min": "0", "step": "1", "default": "100%"}}, "handleStyle": {"desc": "<p>两侧缩放手柄的样式配置。</p>\n"}, "handleStyle.color": {"desc": "\n\n<p>图形的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "handleStyle.borderColor": {"desc": "\n\n<p>图形的描边颜色。支持的颜色格式同 <code class=\"codespan\">color</code>，不支持回调函数。</p>\n", "uiControl": {"type": "color"}}, "handleStyle.borderWidth": {"desc": "\n\n<p>描边线宽。为 0 时无描边。</p>\n", "uiControl": {"type": "number", "value": "0", "min": "0", "step": "0.5"}}, "handleStyle.borderType": {"desc": "\n\n<p>柱条的描边类型，默认为实线，支持 <code class=\"codespan\">&#39;solid&#39;</code>, <code class=\"codespan\">&#39;dashed&#39;</code>, <code class=\"codespan\">&#39;dotted&#39;</code>。</p>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "handleStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "handleStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "handleStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "handleStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "handleStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "moveHandleIcon": {"desc": "\n\n\n\n<blockquote>\n<p>从 <code class=\"codespan\">v5.0.0</code> 开始支持</p>\n</blockquote>\n<p>移动手柄中间的 icon，支持路径字符串，默认为：</p>\n<pre><code class=\"lang-js\">&#39;M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z&#39;\n</code></pre>\n<p>可以通过 <code class=\"codespan\">&#39;image://url&#39;</code> 设置为图片，其中 URL 为图片的链接，或者 <code class=\"codespan\">dataURI</code>。</p>\n<p>URL 为图片链接例如：</p>\n<pre><code>&#39;image://http://xxx.xxx.xxx/a/b.png&#39;\n</code></pre><p>URL 为 <code class=\"codespan\">dataURI</code> 例如：</p>\n<pre><code>&#39;image://data:image/gif;base64,R0lGODlhEAAQAMQAAORHHOVSKudfOulrSOp3WOyDZu6QdvCchPGolfO0o/XBs/fNwfjZ0frl3/zy7////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAkAABAALAAAAAAQABAAAAVVICSOZGlCQAosJ6mu7fiyZeKqNKToQGDsM8hBADgUXoGAiqhSvp5QAnQKGIgUhwFUYLCVDFCrKUE1lBavAViFIDlTImbKC5Gm2hB0SlBCBMQiB0UjIQA7&#39;\n</code></pre><p>可以通过 <code class=\"codespan\">&#39;path://&#39;</code> 将图标设置为任意的矢量路径。这种方式相比于使用图片的方式，不用担心因为缩放而产生锯齿或模糊，而且可以设置为任意颜色。路径图形会自适应调整为合适的大小。路径的格式参见 <a href=\"http://www.w3.org/TR/SVG/paths.html#PathData\" target=\"_blank\">SVG PathData</a>。可以从 Adobe Illustrator 等工具编辑导出。</p>\n<p>例如：</p>\n<pre><code>&#39;path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z&#39;\n</code></pre>", "uiControl": {"type": "icon"}}, "moveHandleSize": {"desc": "\n\n\n\n<blockquote>\n<p>从 <code class=\"codespan\">v5.0.0</code> 开始支持</p>\n</blockquote>\n<p>移动手柄的尺寸高度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "7"}}, "moveHandleStyle": {"desc": "<blockquote>\n<p>从 <code class=\"codespan\">v5.0.0</code> 开始支持</p>\n</blockquote>\n<p>移动手柄的样式配置。</p>\n"}, "moveHandleStyle.color": {"desc": "\n\n<p>图形的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "moveHandleStyle.borderColor": {"desc": "\n\n<p>图形的描边颜色。支持的颜色格式同 <code class=\"codespan\">color</code>，不支持回调函数。</p>\n", "uiControl": {"type": "color"}}, "moveHandleStyle.borderWidth": {"desc": "\n\n<p>描边线宽。为 0 时无描边。</p>\n", "uiControl": {"type": "number", "value": "0", "min": "0", "step": "0.5"}}, "moveHandleStyle.borderType": {"desc": "\n\n<p>柱条的描边类型，默认为实线，支持 <code class=\"codespan\">&#39;solid&#39;</code>, <code class=\"codespan\">&#39;dashed&#39;</code>, <code class=\"codespan\">&#39;dotted&#39;</code>。</p>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "moveHandleStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "moveHandleStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "moveHandleStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "moveHandleStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "moveHandleStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "labelPrecision": {"desc": "\n\n<p>显示label的小数精度。默认根据数据自动决定。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "1"}}, "labelFormatter": {"desc": "<p>显示的label的格式化器。</p>\n<ul>\n<li><p>如果为 <code class=\"codespan\">string</code>，表示模板，例如：<code class=\"codespan\">aaaa{value}bbbb</code>，其中<code class=\"codespan\">{value}</code>会被替换为实际的数值。</p>\n</li>\n<li><p>如果为 <code class=\"codespan\">Function</code>，表示回调函数，例如：</p>\n</li>\n</ul>\n<pre><code class=\"lang-javascript\">/**\n * @param {*} value 如果 axis.type 为 &#39;category&#39;，则 `value` 为 axis.data 的 index。\n *                  否则 `value` 是当前值。\n * @param {strign} valueStr 内部格式化的结果。\n * @return {string} 返回最终的label内容。\n */\nlabelFormatter: function (value) {\n    return &#39;aaa&#39; + value + &#39;bbb&#39;;\n}\n</code></pre>\n"}, "showDetail": {"desc": "\n\n<p>是否显示detail，即拖拽时候显示详细数值信息。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "showDataShadow": {"desc": "\n\n<p>是否在 <code class=\"codespan\">dataZoom-silder</code> 组件中显示数据阴影。数据阴影可以简单地反应数据走势。</p>\n", "uiControl": {"type": "boolean"}}, "realtime": {"desc": "\n\n<p>拖动时，是否实时更新系列的视图。如果设置为 <code class=\"codespan\">false</code>，则只在拖拽结束的时候更新。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "textStyle.color": {"desc": "\n\n<p>dataZoom 文字的颜色。</p>\n", "uiControl": {"type": "color", "default": "#333"}}, "textStyle.fontStyle": {"desc": "\n\n<p>dataZoom 文字字体的风格。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;italic&#39;</code></li>\n<li><code class=\"codespan\">&#39;oblique&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,italic,oblique"}}, "textStyle.fontWeight": {"desc": "\n\n<p>data<PERSON>oom 文字字体的粗细。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;normal&#39;</code></li>\n<li><code class=\"codespan\">&#39;bold&#39;</code></li>\n<li><code class=\"codespan\">&#39;bolder&#39;</code></li>\n<li><code class=\"codespan\">&#39;lighter&#39;</code></li>\n<li>100 | 200 | 300 | 400...</li>\n</ul>\n", "uiControl": {"type": "enum", "default": "normal", "options": "normal,bold,bolder,lighter"}}, "textStyle.fontFamily": {"desc": "\n\n<p>dataZoom 文字的字体系列。</p>\n<p>还可以是 &#39;serif&#39; , &#39;monospace&#39;, &#39;Arial&#39;, &#39;Courier New&#39;, &#39;Microsoft YaHei&#39;, ...</p>\n", "uiControl": {"type": "enum", "default": "sans-serif", "options": "sans-serif,serif,monospace,Arial,Courier New"}}, "textStyle.fontSize": {"desc": "\n\n<p>dataZoom 文字的字体大小。</p>\n", "uiControl": {"type": "number", "default": "12", "min": "1", "step": "1"}}, "textStyle.lineHeight": {"desc": "\n\n<p>行高。</p>\n<p><code class=\"codespan\">rich</code> 中如果没有设置 <code class=\"codespan\">lineHeight</code>，则会取父层级的 <code class=\"codespan\">lineHeight</code>。例如：</p>\n<pre><code class=\"lang-js\">{\n    lineHeight: 56,\n    rich: {\n        a: {\n            // 没有设置 `lineHeight`，则 `lineHeight` 为 56\n        }\n    }\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "step": "1", "default": "12"}}, "textStyle.width": {"desc": "\n\n<p>文本显示宽度。</p>\n", "uiControl": {"type": "number", "default": "100", "min": "1", "max": "500", "step": "1"}}, "textStyle.height": {"desc": "\n\n<p>文本显示高度。</p>\n", "uiControl": {"type": "number", "default": "50", "min": "1", "max": "500", "step": "1"}}, "textStyle.textBorderColor": {"desc": "\n\n<p>文字本身的描边颜色。</p>\n", "uiControl": {"type": "color"}}, "textStyle.textBorderWidth": {"desc": "\n\n<p>文字本身的描边宽度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "textStyle.textShadowColor": {"desc": "\n\n<p>文字本身的阴影颜色。</p>\n", "uiControl": {"type": "color", "default": "#000"}}, "textStyle.textShadowBlur": {"desc": "\n\n<p>文字本身的阴影长度。</p>\n", "uiControl": {"type": "number", "min": "0", "step": "0.5"}}, "textStyle.textShadowOffsetX": {"desc": "\n\n<p>文字本身的阴影 X 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "textStyle.textShadowOffsetY": {"desc": "\n\n<p>文字本身的阴影 Y 偏移。</p>\n", "uiControl": {"type": "number", "step": "0.5"}}, "textStyle.overflow": {"desc": "\n\n<p>文字超出宽度是否截断或者换行。配置<code class=\"codespan\">width</code>时有效</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 截断，并在末尾显示<code class=\"codespan\">ellipsis</code>配置的文本，默认为<code class=\"codespan\">...</code></li>\n<li><code class=\"codespan\">&#39;break&#39;</code> 换行</li>\n<li><code class=\"codespan\">&#39;breakAll&#39;</code> 换行，跟<code class=\"codespan\">&#39;break&#39;</code>不同的是，在英语等拉丁文中，<code class=\"codespan\">&#39;breakAll&#39;</code>还会强制单词内换行</li>\n</ul>\n", "uiControl": {"type": "enum", "options": "truncate,break,breakAll"}}, "textStyle.ellipsis": {"desc": "<p>在<code class=\"codespan\">overflow</code>配置为<code class=\"codespan\">&#39;truncate&#39;</code>的时候，可以通过该属性配置末尾显示的文本。</p>\n"}, "textStyle.lineOverflow": {"desc": "<p>文本超出高度部分是否截断，配置<code class=\"codespan\">height</code>时有效。</p>\n<ul>\n<li><code class=\"codespan\">&#39;truncate&#39;</code> 在文本行数超出高度部分截断。</li>\n</ul>\n"}, "xAxisIndex": {"desc": "<p>设置 <code class=\"codespan\">dataZoom-slider</code> 组件控制的 <code class=\"codespan\">x轴</code>（即<a href=\"#xAxis\">xAxis</a>，是直角坐标系中的概念，参见 <a href=\"#grid\">grid</a>）。</p>\n<p>不指定时，当 <a href=\"#dataZoom-slider.orient\">dataZoom-slider.orient</a> 为 <code class=\"codespan\">&#39;horizontal&#39;</code>时，默认控制和 dataZoom 平行的第一个 <code class=\"codespan\">xAxis</code>。但是不建议使用默认值，建议显式指定。</p>\n<p>如果是 <code class=\"codespan\">number</code> 表示控制一个轴，如果是 <code class=\"codespan\">Array</code> 表示控制多个轴。</p>\n<p>例如有如下 ECharts option：</p>\n<pre><code class=\"lang-javascript\">option: {\n    xAxis: [\n        {...}, // 第一个 xAxis\n        {...}, // 第二个 xAxis\n        {...}, // 第三个 xAxis\n        {...}  // 第四个 xAxis\n    ],\n    dataZoom: [\n        { // 第一个 dataZoom 组件\n            xAxisIndex: [0, 2] // 表示这个 dataZoom 组件控制 第一个 和 第三个 xAxis\n        },\n        { // 第二个 dataZoom 组件\n            xAxisIndex: 3      // 表示这个 dataZoom 组件控制 第四个 xAxis\n        }\n    ]\n}\n</code></pre>\n"}, "yAxisIndex": {"desc": "<p>设置 <code class=\"codespan\">dataZoom-slider</code> 组件控制的 <code class=\"codespan\">y轴</code>（即<a href=\"#yAxis\">yAxis</a>，是直角坐标系中的概念，参见 <a href=\"#grid\">grid</a>）。</p>\n<p>不指定时，当 <a href=\"#dataZoom-slider.orient\">dataZoom-slider.orient</a> 为 <code class=\"codespan\">&#39;vertical&#39;</code>时，默认控制和 dataZoom 平行的第一个 <code class=\"codespan\">yAxis</code>。但是不建议使用默认值，建议显式指定。</p>\n<p>如果是 <code class=\"codespan\">number</code> 表示控制一个轴，如果是 <code class=\"codespan\">Array</code> 表示控制多个轴。</p>\n<p>例如有如下 ECharts option：</p>\n<pre><code class=\"lang-javascript\">option: {\n    yAxis: [\n        {...}, // 第一个 yAxis\n        {...}, // 第二个 yAxis\n        {...}, // 第三个 yAxis\n        {...}  // 第四个 yAxis\n    ],\n    dataZoom: [\n        { // 第一个 dataZoom 组件\n            yAxisIndex: [0, 2] // 表示这个 dataZoom 组件控制 第一个 和 第三个 yAxis\n        },\n        { // 第二个 dataZoom 组件\n            yAxisIndex: 3      // 表示这个 dataZoom 组件控制 第四个 yAxis\n        }\n    ]\n}\n</code></pre>\n"}, "radiusAxisIndex": {"desc": "<p>设置 <code class=\"codespan\">dataZoom-slider</code> 组件控制的 <code class=\"codespan\">radius 轴</code>（即<a href=\"#radiusAxis\">radiusAxis</a>，是直角坐标系中的概念，参见 <a href=\"#polar\">polar</a>）。</p>\n<p>如果是 <code class=\"codespan\">number</code> 表示控制一个轴，如果是 <code class=\"codespan\">Array</code> 表示控制多个轴。</p>\n<p>例如有如下 ECharts option：</p>\n<pre><code class=\"lang-javascript\">option: {\n    radiusAxis: [\n        {...}, // 第一个 radiusAxis\n        {...}, // 第二个 radiusAxis\n        {...}, // 第三个 radiusAxis\n        {...}  // 第四个 radiusAxis\n    ],\n    dataZoom: [\n        { // 第一个 dataZoom 组件\n            radiusAxisIndex: [0, 2] // 表示这个 dataZoom 组件控制 第一个 和 第三个 radiusAxis\n        },\n        { // 第二个 dataZoom 组件\n            radiusAxisIndex: 3      // 表示这个 dataZoom 组件控制 第四个 radiusAxis\n        }\n    ]\n}\n</code></pre>\n"}, "angleAxisIndex": {"desc": "<p>设置 <code class=\"codespan\">dataZoom-slider</code> 组件控制的 <code class=\"codespan\">angle 轴</code>（即<a href=\"#angleAxis\">angleAxis</a>，是直角坐标系中的概念，参见 <a href=\"#polar\">polar</a>）。</p>\n<p>如果是 <code class=\"codespan\">number</code> 表示控制一个轴，如果是 <code class=\"codespan\">Array</code> 表示控制多个轴。</p>\n<p>例如有如下 ECharts option：</p>\n<pre><code class=\"lang-javascript\">option: {\n    angleAxis: [\n        {...}, // 第一个 angleAxis\n        {...}, // 第二个 angleAxis\n        {...}, // 第三个 angleAxis\n        {...}  // 第四个 angleAxis\n    ],\n    dataZoom: [\n        { // 第一个 dataZoom 组件\n            angleAxisIndex: [0, 2] // 表示这个 dataZoom 组件控制 第一个 和 第三个 angleAxis\n        },\n        { // 第二个 dataZoom 组件\n            angleAxisIndex: 3      // 表示这个 dataZoom 组件控制 第四个 angleAxis\n        }\n    ]\n}\n</code></pre>\n"}, "filterMode": {"desc": "\n\n\n\n<p><code class=\"codespan\">dataZoom</code> 的运行原理是通过 <code class=\"codespan\">数据过滤</code> 以及在内部设置轴的显示窗口来达到 <code class=\"codespan\">数据窗口缩放</code> 的效果。</p>\n<p>数据过滤模式（<a href=\"#dataZoom.filterMode\">dataZoom.filterMode</a>）的设置不同，效果也不同。</p>\n<p>可选值为：</p>\n<ul>\n<li><p>&#39;filter&#39;：当前数据窗口外的数据，被 <strong>过滤掉</strong>。即 <strong>会</strong> 影响其他轴的数据范围。每个数据项，只要有一个维度在数据窗口外，整个数据项就会被过滤掉。</p>\n</li>\n<li><p>&#39;weakFilter&#39;：当前数据窗口外的数据，被 <strong>过滤掉</strong>。即 <strong>会</strong> 影响其他轴的数据范围。每个数据项，只有当全部维度都在数据窗口同侧外部，整个数据项才会被过滤掉。</p>\n</li>\n<li><p>&#39;empty&#39;：当前数据窗口外的数据，被 <strong>设置为空</strong>。即 <strong>不会</strong> 影响其他轴的数据范围。</p>\n</li>\n<li><p>&#39;none&#39;: 不过滤数据，只改变数轴范围。</p>\n</li>\n</ul>\n<p>如何设置，由用户根据场景和需求自己决定。经验来说：</p>\n<ul>\n<li><p>当『只有 X 轴 或 只有 Y 轴受 <code class=\"codespan\">dataZoom</code> 组件控制』时，常使用 <code class=\"codespan\">filterMode: &#39;filter&#39;</code>，这样能使另一个轴自适应过滤后的数值范围。</p>\n</li>\n<li><p>当『X 轴 Y 轴分别受 <code class=\"codespan\">dataZoom</code> 组件控制』时：</p>\n<ul>\n<li><p>如果 X 轴和 Y 轴是『同等地位的、不应互相影响的』，比如在『双数值轴散点图』中，那么两个轴可都设为 <code class=\"codespan\">fiterMode: &#39;empty&#39;</code>。</p>\n</li>\n<li><p>如果 X 轴为主，Y 轴为辅，比如在『柱状图』中，需要『拖动 <code class=\"codespan\">dataZoomX</code> 改变 X 轴过滤柱子时，Y 轴的范围也自适应剩余柱子的高度』，『拖动 <code class=\"codespan\">dataZoomY</code> 改变 Y 轴过滤柱子时，X 轴范围不受影响』，那么就 X轴设为 <code class=\"codespan\">fiterMode: &#39;filter&#39;</code>，Y 轴设为 <code class=\"codespan\">fiterMode: &#39;empty&#39;</code>，即主轴 <code class=\"codespan\">&#39;filter&#39;</code>，辅轴 <code class=\"codespan\">&#39;empty&#39;</code>。</p>\n</li>\n</ul>\n</li>\n</ul>\n<p>下面是个具体例子：</p>\n<pre><code class=\"lang-javascript\">option = {\n    dataZoom: [\n        {\n            id: &#39;dataZoomX&#39;,\n            type: &#39;slider&#39;,\n            xAxisIndex: [0],\n            filterMode: &#39;filter&#39;\n        },\n        {\n            id: &#39;dataZoomY&#39;,\n            type: &#39;slider&#39;,\n            yAxisIndex: [0],\n            filterMode: &#39;empty&#39;\n        }\n    ],\n    xAxis: {type: &#39;value&#39;},\n    yAxis: {type: &#39;value&#39;},\n    series{\n        type: &#39;bar&#39;,\n        data: [\n            // 第一列对应 X 轴，第二列对应 Y 轴。\n            [12, 24, 36],\n            [90, 80, 70],\n            [3, 9, 27],\n            [1, 11, 111]\n        ]\n    }\n}\n</code></pre>\n<p>上例中，<code class=\"codespan\">dataZoomX</code> 的 <code class=\"codespan\">filterMode</code> 设置为 <code class=\"codespan\">&#39;filter&#39;</code>。于是，假设当用户拖拽 <code class=\"codespan\">dataZoomX</code>（不去动 <code class=\"codespan\">dataZoomY</code>）导致其 valueWindow 变为 <code class=\"codespan\">[2, 50]</code> 时，<code class=\"codespan\">dataZoomX</code> 对 series.data 的第一列进行遍历，窗口外的整项去掉，最终得到的 series.data 为：</p>\n<pre><code class=\"lang-javascript\">[\n    // 第一列对应 X 轴，第二列对应 Y 轴。\n    [12, 24, 36],\n    // [90, 80, 70] 整项被过滤掉，因为 90 在 dataWindow 之外。\n    [3, 9, 27]\n    // [1, 11, 111] 整项被过滤掉，因为 1 在 dataWindow 之外。\n]\n</code></pre>\n<p>过滤前，series.data 中对应 Y 轴的值有 <code class=\"codespan\">24</code>、<code class=\"codespan\">80</code>、<code class=\"codespan\">9</code>、<code class=\"codespan\">11</code>，过滤后，只剩下 <code class=\"codespan\">24</code> 和 <code class=\"codespan\">9</code>，那么 Y 轴的显示范围就会自动改变以适应剩下的这两个值的显示（如果 Y 轴没有被设置 <code class=\"codespan\">min</code>、<code class=\"codespan\">max</code> 固定其显示范围的话）。</p>\n<p>所以，<code class=\"codespan\">filterMode: &#39;filter&#39;</code> 的效果是：过滤数据后使另外的轴也能自动适应当前数据的范围。</p>\n<p>再从头来，上例中 <code class=\"codespan\">dataZoomY</code> 的 <code class=\"codespan\">filterMode</code> 设置为 <code class=\"codespan\">&#39;empty&#39;</code>。于是，假设当用户拖拽 <code class=\"codespan\">dataZoomY</code>（不去动 <code class=\"codespan\">dataZoomX</code>）导致其 dataWindow 变为 <code class=\"codespan\">[10, 60]</code> 时，<code class=\"codespan\">dataZoomY</code> 对 series.data 的第二列进行遍历，窗口外的值被设置为 empty （即替换为 NaN，这样设置为空的项，其所对应柱形，在 X 轴还有占位，只是不显示出来）。最终得到的 series.data 为：</p>\n<pre><code class=\"lang-javascript\">[\n    // 第一列对应 X 轴，第二列对应 Y 轴。\n    [12, 24, 36],\n    [90, NaN, 70], // 设置为 empty (NaN)\n    [3, NaN, 27],  // 设置为 empty (NaN)\n    [1, 11, 111]\n]\n</code></pre>\n<p>这时，series.data 中对应于 X 轴的值仍然全部保留不受影响，为 <code class=\"codespan\">12</code>、<code class=\"codespan\">90</code>、<code class=\"codespan\">3</code>、<code class=\"codespan\">1</code>。那么用户对 <code class=\"codespan\">dataZoomY</code> 的拖拽操作不会影响到 X 轴的范围。这样的效果，对于离群点（outlier）过滤功能，比较清晰。</p>\n<p>如下面的例子：</p>\n<iframe  data-src=\"http://localhost/incubator-echarts-website/next/examples/zh/view.html?c=doc-example/bar-dataZoom-filterMode&edit=1&reset=1\" width=\"600\" height=\"400\"><iframe />\n\n\n\n\n\n", "uiControl": {"type": "enum", "options": "filter,weakFilter,empty,none", "default": "filter"}}, "start": {"desc": "\n\n<p>数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。</p>\n<p><a href=\"#dataZoom-slider.start\">dataZoom-slider.start</a> 和 <a href=\"#dataZoom-slider.end\">dataZoom-slider.end</a> 共同用 <strong>百分比</strong> 的形式定义了数据窗口范围。</p>\n<p>关于坐标轴范围（axis extent）和 <code class=\"codespan\">dataZoom-slider.start</code> 的关系的更多信息，请参见：<a href=\"#dataZoom-slider.rangeMode\">dataZoom-slider.rangeMode</a>。</p>\n", "uiControl": {"type": "number", "min": "0", "max": "100", "step": "0.5"}}, "end": {"desc": "\n\n<p>数据窗口范围的结束百分比。范围是：0 ~ 100。</p>\n<p><a href=\"#dataZoom-slider.start\">dataZoom-slider.start</a> 和 <a href=\"#dataZoom-slider.end\">dataZoom-slider.end</a> 共同用 <strong>百分比</strong> 的形式定义了数据窗口范围。</p>\n<p>关于坐标轴范围（axis extent）和 <code class=\"codespan\">dataZoom-slider.end</code> 的关系的更多信息，请参见：<a href=\"#dataZoom-slider.rangeMode\">dataZoom-slider.rangeMode</a>。</p>\n", "uiControl": {"type": "number", "min": "0", "max": "100", "default": "100", "step": "0.5"}}, "startValue": {"desc": "<p>数据窗口范围的起始数值。如果设置了 <a href=\"#dataZoom-slider.start\">dataZoom-slider.start</a> 则 <code class=\"codespan\">startValue</code> 失效。</p>\n<p><a href=\"#dataZoom-slider.startValue\">dataZoom-slider.startValue</a> 和 <a href=\"#dataZoom-slider.endValue\">dataZoom-slider.endValue</a> 共同用 <strong>绝对数值</strong> 的形式定义了数据窗口范围。</p>\n<p>注意，如果轴的类型为 <code class=\"codespan\">category</code>，则 <code class=\"codespan\">startValue</code> 既可以设置为 <code class=\"codespan\">axis.data</code> 数组的 <code class=\"codespan\">index</code>，也可以设置为数组值本身。\n但是如果设置为数组值本身，会在内部自动转化为数组的 index。</p>\n<p>关于坐标轴范围（axis extent）和 <code class=\"codespan\">dataZoom-slider.startValue</code> 的关系的更多信息，请参见：<a href=\"#dataZoom-slider.rangeMode\">dataZoom-slider.rangeMode</a>。</p>\n"}, "endValue": {"desc": "<p>数据窗口范围的结束数值。如果设置了 <a href=\"#dataZoom-slider.end\">dataZoom-slider.end</a> 则 <code class=\"codespan\">endValue</code> 失效。</p>\n<p><a href=\"#dataZoom-slider.startValue\">dataZoom-slider.startValue</a> 和 <a href=\"#dataZoom-slider.endValue\">dataZoom-slider.endValue</a> 共同用 <strong>绝对数值</strong> 的形式定义了数据窗口范围。</p>\n<p>注意，如果轴的类型为 <code class=\"codespan\">category</code>，则 <code class=\"codespan\">endValue</code> 即可以设置为 <code class=\"codespan\">axis.data</code> 数组的 <code class=\"codespan\">index</code>，也可以设置为数组值本身。\n但是如果设置为数组值本身，会在内部自动转化为数组的 index。</p>\n<p>关于坐标轴范围（axis extent）和 <code class=\"codespan\">dataZoom-slider.endValue</code> 的关系的更多信息，请参见：<a href=\"#dataZoom-slider.rangeMode\">dataZoom-slider.rangeMode</a>。</p>\n"}, "minSpan": {"desc": "\n\n<p>用于限制窗口大小的最小值（百分比值），取值范围是 <code class=\"codespan\">0</code> ~ <code class=\"codespan\">100</code>。</p>\n<p>如果设置了 <a href=\"#dataZoom-slider.minValueSpan\">dataZoom-slider.minValueSpan</a> 则 <code class=\"codespan\">minSpan</code> 失效。</p>\n", "uiControl": {"type": "number", "min": "0", "max": "100", "step": "0.5"}}, "maxSpan": {"desc": "\n\n<p>用于限制窗口大小的最大值（百分比值），取值范围是 <code class=\"codespan\">0</code> ~ <code class=\"codespan\">100</code>。</p>\n<p>如果设置了 <a href=\"#dataZoom-slider.maxValueSpan\">dataZoom-slider.maxValueSpan</a> 则 <code class=\"codespan\">maxSpan</code> 失效。</p>\n", "uiControl": {"type": "number", "min": "0", "max": "100", "step": "0.5"}}, "minValueSpan": {"desc": "<p>用于限制窗口大小的最小值（实际数值）。</p>\n<p>如在时间轴上可以设置为：<code class=\"codespan\">3600 * 24 * 1000 * 5</code> 表示 5 天。\n在类目轴上可以设置为 <code class=\"codespan\">5</code> 表示 5 个类目。</p>\n"}, "maxValueSpan": {"desc": "<p>用于限制窗口大小的最大值（实际数值）。</p>\n<p>如在时间轴上可以设置为：<code class=\"codespan\">3600 * 24 * 1000 * 5</code> 表示 5 天。\n在类目轴上可以设置为 <code class=\"codespan\">5</code> 表示 5 个类目。</p>\n"}, "orient": {"desc": "\n\n<p>布局方式是横还是竖。不仅是布局方式，对于直角坐标系而言，也决定了，缺省情况控制横向数轴还是纵向数轴。</p>\n<p>可选值为：</p>\n<ul>\n<li><p><code class=\"codespan\">&#39;horizontal&#39;</code>：水平。</p>\n</li>\n<li><p><code class=\"codespan\">&#39;vertical&#39;</code>：竖直。</p>\n</li>\n</ul>\n", "uiControl": {"type": "enum", "options": "horizontal,vertical"}}, "zoomLock": {"desc": "\n\n<p>是否锁定选择区域（或叫做数据窗口）的大小。</p>\n<p>如果设置为 <code class=\"codespan\">true</code> 则锁定选择区域的大小，也就是说，只能平移，不能缩放。</p>\n", "uiControl": {"type": "boolean"}}, "throttle": {"desc": "\n\n<p>设置触发视图刷新的频率。单位为毫秒（ms）。</p>\n<p>如果 <a href=\"#animation\">animation</a> 设为 <code class=\"codespan\">true</code> 且 <a href=\"#animationDurationUpdate\">animationDurationUpdate</a> 大于 <code class=\"codespan\">0</code>，可以保持 <code class=\"codespan\">throttle</code> 为默认值 <code class=\"codespan\">100</code>（或者设置为大于 <code class=\"codespan\">0</code> 的值），否则拖拽时有可能画面感觉卡顿。</p>\n<p>如果 <a href=\"#animation\">animation</a> 设为 <code class=\"codespan\">false</code> 或者 <a href=\"#animationDurationUpdate\">animationDurationUpdate</a> 设为 <code class=\"codespan\">0</code>，且在数据量不大时，拖拽时画面感觉卡顿，可以把尝试把 <code class=\"codespan\">throttle</code> 设为 <code class=\"codespan\">0</code> 来改善。</p>\n", "uiControl": {"type": "number", "default": "100", "min": "0", "step": "20"}}, "rangeMode": {"desc": "<p>形式为：<code class=\"codespan\">[rangeModeForStart, rangeModeForEnd]</code>。</p>\n<p>例如 <code class=\"codespan\">rangeMode: [&#39;value&#39;, &#39;percent&#39;]</code>，表示 start 值取绝对数值，end 取百分比。</p>\n<p>每项可选值为：<code class=\"codespan\">&#39;value&#39;</code>, <code class=\"codespan\">&#39;percent&#39;</code></p>\n<ul>\n<li><code class=\"codespan\">&#39;value&#39;</code> 模式：处于此模式下，坐标轴范围（axis extent）总只会被<code class=\"codespan\">dataZoom.startValue</code> and <code class=\"codespan\">dataZoom.endValue</code> 决定，而不管数据是多少，以及不管，<code class=\"codespan\">axis.min</code> 和 <code class=\"codespan\">axis.max</code> 怎么设置。</li>\n<li><code class=\"codespan\">&#39;percent&#39;</code> 模式：处于此模式下，<code class=\"codespan\">100</code> 代表 100% 的 <code class=\"codespan\">[dMin, dMax]</code>。这里 <code class=\"codespan\">dMin</code> 表示，如果 <code class=\"codespan\">axis.min</code> 设置了就是 <code class=\"codespan\">axis.min</code>，否则是 <code class=\"codespan\">data.extent[0]</code>；<code class=\"codespan\">dMax</code> 表示，如果 <code class=\"codespan\">axis.max</code> 设置了就是 <code class=\"codespan\">axis.max</code>，否则是 <code class=\"codespan\">data.extent[1]</code>。<code class=\"codespan\">[dMin, dMax]</code> 乘以 percent 的结果得到坐标轴范围（axis extent）。</li>\n</ul>\n<p>默认情况下，<code class=\"codespan\">rangeMode</code> 总是被自动设定。如果指定了 <code class=\"codespan\">option.start</code>/<code class=\"codespan\">option.end</code> 那么就设定为 <code class=\"codespan\">&#39;percent&#39;</code>，如果指定了 <code class=\"codespan\">option.startValue</code>/<code class=\"codespan\">option.endValue</code> 那么就设定为 <code class=\"codespan\">&#39;value&#39;</code>。以及当用户用不用操作触发视图改变时，<code class=\"codespan\">rangeMode</code> 也可能会相应得变化（如，通过 <code class=\"codespan\">toolbox.dataZoom</code> 触发视图改变时，<code class=\"codespan\">rangeMode</code> 会自动被设置为 <code class=\"codespan\">value</code>，通过 <code class=\"codespan\">dataZoom-inside</code> 和 <code class=\"codespan\">dataZoom-slider</code> 触发视图改变时，会自动被设置为 <code class=\"codespan\">&#39;percent&#39;</code>）。</p>\n<p>如果我们手动在 <code class=\"codespan\">option</code> 中设定了 <code class=\"codespan\">rangeMode</code>，那么它只在 <code class=\"codespan\">start</code> 和 <code class=\"codespan\">startValue</code> 都设置了或者 <code class=\"codespan\">end</code> 和 <code class=\"codespan\">endValue</code> 都设置了才有意义。所以通常我们没必要在 <code class=\"codespan\">option</code> 中指定 <code class=\"codespan\">rangeMode</code>。</p>\n<p>举例一个使用场景：当我们使用动态数据时（即，周期性得通过 <code class=\"codespan\">setOption</code> 来改变数据），如果 <code class=\"codespan\">rangeMode</code> 在 <code class=\"codespan\">&#39;value</code>&#39; 模式，<code class=\"codespan\">dataZoom</code> 的窗口会一直保持在一个固定的值区间，无论数据怎么改变添加了多少；如果 <code class=\"codespan\">rangeMode</code> 在 <code class=\"codespan\">&#39;percent&#39;</code> 模式，窗口会随着数据的添加而改变（假设 <code class=\"codespan\">axis.min</code> 和 <code class=\"codespan\">axis.max</code> 没有被设置）。</p>\n"}, "zlevel": {"desc": "<p>所有图形的 zlevel 值。</p>\n<p><code class=\"codespan\">zlevel</code>用于 Canvas 分层，不同<code class=\"codespan\">zlevel</code>值的图形会放置在不同的 Canvas 中，Canvas 分层是一种常见的优化手段。我们可以把一些图形变化频繁（例如有动画）的组件设置成一个单独的<code class=\"codespan\">zlevel</code>。需要注意的是过多的 Canvas 会引起内存开销的增大，在手机端上需要谨慎使用以防崩溃。</p>\n<p><code class=\"codespan\">zlevel</code> 大的 Canvas 会放在 <code class=\"codespan\">zlevel</code> 小的 Canvas 的上面。</p>\n"}, "z": {"desc": "<p>组件的所有图形的<code class=\"codespan\">z</code>值。控制图形的前后顺序。<code class=\"codespan\">z</code>值小的图形会被<code class=\"codespan\">z</code>值大的图形覆盖。</p>\n<p><code class=\"codespan\">z</code>相比<code class=\"codespan\">zlevel</code>优先级更低，而且不会创建新的 Canvas。</p>\n"}, "left": {"desc": "\n\n<p>dataZoom-slider组件离容器左侧的距离。</p>\n<p><code class=\"codespan\">left</code> 的值可以是像 <code class=\"codespan\">20</code> 这样的具体像素值，可以是像 <code class=\"codespan\">&#39;20%&#39;</code> 这样相对于容器高宽的百分比，也可以是 <code class=\"codespan\">&#39;left&#39;</code>, <code class=\"codespan\">&#39;center&#39;</code>, <code class=\"codespan\">&#39;right&#39;</code>。</p>\n<p>如果 <code class=\"codespan\">left</code> 的值为<code class=\"codespan\">&#39;left&#39;</code>, <code class=\"codespan\">&#39;center&#39;</code>, <code class=\"codespan\">&#39;right&#39;</code>，组件会根据相应的位置自动对齐。</p>\n", "uiControl": {"type": "percent", "default": "0%"}}, "top": {"desc": "\n\n<p>dataZoom-slider组件离容器上侧的距离。</p>\n<p><code class=\"codespan\">top</code> 的值可以是像 <code class=\"codespan\">20</code> 这样的具体像素值，可以是像 <code class=\"codespan\">&#39;20%&#39;</code> 这样相对于容器高宽的百分比，也可以是 <code class=\"codespan\">&#39;top&#39;</code>, <code class=\"codespan\">&#39;middle&#39;</code>, <code class=\"codespan\">&#39;bottom&#39;</code>。</p>\n<p>如果 <code class=\"codespan\">top</code> 的值为<code class=\"codespan\">&#39;top&#39;</code>, <code class=\"codespan\">&#39;middle&#39;</code>, <code class=\"codespan\">&#39;bottom&#39;</code>，组件会根据相应的位置自动对齐。</p>\n", "uiControl": {"type": "percent", "default": "0%"}}, "right": {"desc": "\n\n<p>dataZoom-slider组件离容器右侧的距离。</p>\n<p><code class=\"codespan\">right</code> 的值可以是像 <code class=\"codespan\">20</code> 这样的具体像素值，可以是像 <code class=\"codespan\">&#39;20%&#39;</code> 这样相对于容器高宽的百分比。</p>\n<p>默认自适应。</p>\n", "uiControl": {"type": "percent", "default": "0%"}}, "bottom": {"desc": "\n\n<p>dataZoom-slider组件离容器下侧的距离。</p>\n<p>bottom 的值可以是像 <code class=\"codespan\">20</code> 这样的具体像素值，可以是像 <code class=\"codespan\">&#39;20%&#39;</code> 这样相对于容器高宽的百分比。</p>\n<p>默认自适应。</p>\n", "uiControl": {"type": "percent", "default": "0%"}}, "brushSelect": {"desc": "<blockquote>\n<p>从 <code class=\"codespan\">v5.0.0</code> 开始支持</p>\n</blockquote>\n<p>是否开启刷选功能。在下图的 brush 区域你可以按住鼠标左键后框选出选中部分。</p>\n<p><img width=\"600\" height=\"auto\" src=\"documents/asset/img/dataZoom-zone.png\"></p>\n"}, "brushStyle": {"desc": "<blockquote>\n<p>从 <code class=\"codespan\">v5.0.0</code> 开始支持</p>\n</blockquote>\n<p>刷选框样式设置。</p>\n"}, "brushStyle.color": {"desc": "\n\n<p>图形的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "brushStyle.borderColor": {"desc": "\n\n<p>图形的描边颜色。支持的颜色格式同 <code class=\"codespan\">color</code>，不支持回调函数。</p>\n", "uiControl": {"type": "color"}}, "brushStyle.borderWidth": {"desc": "\n\n<p>描边线宽。为 0 时无描边。</p>\n", "uiControl": {"type": "number", "value": "0", "min": "0", "step": "0.5"}}, "brushStyle.borderType": {"desc": "\n\n<p>柱条的描边类型，默认为实线，支持 <code class=\"codespan\">&#39;solid&#39;</code>, <code class=\"codespan\">&#39;dashed&#39;</code>, <code class=\"codespan\">&#39;dotted&#39;</code>。</p>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "brushStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "brushStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "brushStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "brushStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "brushStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "emphasis": {"desc": "<blockquote>\n<p>从 <code class=\"codespan\">v5.0.0</code> 开始支持</p>\n</blockquote>\n<p>高亮样式设置。</p>\n"}, "emphasis.handleStyle.color": {"desc": "\n\n<p>图形的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "emphasis.handleStyle.borderColor": {"desc": "\n\n<p>图形的描边颜色。支持的颜色格式同 <code class=\"codespan\">color</code>，不支持回调函数。</p>\n", "uiControl": {"type": "color"}}, "emphasis.handleStyle.borderWidth": {"desc": "\n\n<p>描边线宽。为 0 时无描边。</p>\n", "uiControl": {"type": "number", "value": "0", "min": "0", "step": "0.5"}}, "emphasis.handleStyle.borderType": {"desc": "\n\n<p>柱条的描边类型，默认为实线，支持 <code class=\"codespan\">&#39;solid&#39;</code>, <code class=\"codespan\">&#39;dashed&#39;</code>, <code class=\"codespan\">&#39;dotted&#39;</code>。</p>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "emphasis.handleStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "emphasis.handleStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "emphasis.handleStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "emphasis.handleStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "emphasis.handleStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}, "emphasis.moveHandleStyle.color": {"desc": "\n\n<p>图形的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "emphasis.moveHandleStyle.borderColor": {"desc": "\n\n<p>图形的描边颜色。支持的颜色格式同 <code class=\"codespan\">color</code>，不支持回调函数。</p>\n", "uiControl": {"type": "color"}}, "emphasis.moveHandleStyle.borderWidth": {"desc": "\n\n<p>描边线宽。为 0 时无描边。</p>\n", "uiControl": {"type": "number", "value": "0", "min": "0", "step": "0.5"}}, "emphasis.moveHandleStyle.borderType": {"desc": "\n\n<p>柱条的描边类型，默认为实线，支持 <code class=\"codespan\">&#39;solid&#39;</code>, <code class=\"codespan\">&#39;dashed&#39;</code>, <code class=\"codespan\">&#39;dotted&#39;</code>。</p>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "emphasis.moveHandleStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "emphasis.moveHandleStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "emphasis.moveHandleStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "emphasis.moveHandleStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "emphasis.moveHandleStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "1", "min": "0", "max": "1", "step": "0.01"}}}