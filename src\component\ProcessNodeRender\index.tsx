import React, {useRef, forwardRef, useImperativeHandle} from 'react';
import './index.scss';
import Avatar from '@/component/ProcessAvatar';

interface ProcessNodeRenderProps {
  desc?: any;
  task: any;
  error?: any;
  onAddUser?: any;
  onDelUser?: any;
}

const ProcessNodeRender = forwardRef<any, ProcessNodeRenderProps>((props, ref) => {
  const {desc, task, error, onAddUser, onDelUser} = props;
  const tipRef = useRef<HTMLDivElement>(null);

  const delUser = (index: any) => {
    if (onDelUser) {
      onDelUser(task.id, index);
    }
  };

  const errorShake = () => {
    const el = tipRef.current;
    if (el) {
      el.classList.add('shake-tip');
      setTimeout(() => el.classList.remove('shake-tip'), 1200);
    }
  };

  // 必须放在 return 之前，且不是在任何函数体内
  useImperativeHandle(ref, () => ({
    errorShake
  }));

  return (
    <div ref={tipRef} className="process-node-render">
      <div className={error ? 'process-node-error' : ''}>
        <div className="node-title">
          {task.enableEdit && <span className="required">*</span>}
          {task.title}
        </div>
        {desc && (
          <div
            className="node-desc"
            dangerouslySetInnerHTML={{__html: desc}}
          ></div>
        )}
      </div>

      <div className={`user-list ${
              task.enableEdit &&
              (task.multiple || (task.users?.length || 0) === 0)
                ? 'has-add-btn'
                : ''
            }`}>
        {task.users && task.users.length > 0 && (
          <div
            className={`avatar-list ${
              task.enableEdit &&
              (task.multiple || (task.users?.length || 0) === 0)
                ? 'has-add-btn'
                : ''
            }`}
          >
            {task.users.map((user: any, i: any) => (
              <Avatar
                key={`user_${i}`}
                src={user.avatar}
                type={user.type}
                name={user.name}
                size={38}
                showY
                closeable={task.enableEdit}
                onClose={() => delUser(i)}
              />
            ))}
          </div>
        )}
      </div>

      {task.enableEdit &&
        (task.multiple || (task.users?.length || 0) === 0) && (
          <div
            className="add-user"
            onClick={() => onAddUser && onAddUser(task)}
          >
            <i className="fa fa-plus" />
            <div>添加</div>
          </div>
        )}
    </div>
  );
});

export default ProcessNodeRender;
