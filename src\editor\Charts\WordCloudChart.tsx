import {Button} from 'amis';
import React from 'react';
import {
  registerEditorPlugin,
  BaseEventContext,
  BasePlugin,
  RendererPluginAction,
  diff,
  defaultValue,
  getSchemaTpl,
  CodeEditor as AmisCodeEditor,
  RendererPluginEvent,
  tipedLabel
} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
// import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import ChartDataInteraction from '@/editor/component/ChartDataInteraction/WordCloudChartDataInteraction';
const ChartConfigEditor = ({value, onChange}: any) => {
  return (
    <div className="ae-JsonEditor">
      <AmisCodeEditor value={value} onChange={onChange} />
    </div>
  );
};

const DEFAULT_EVENT_PARAMS = [
  {
    type: 'object',
    properties: {
      data: {
        type: 'object',
        title: '数据',
        properties: {
          componentType: {
            type: 'string',
            title: 'componentType'
          },
          seriesType: {
            type: 'string',
            title: 'seriesType'
          },
          seriesIndex: {
            type: 'number',
            title: 'seriesIndex'
          },
          seriesName: {
            type: 'string',
            title: 'seriesName'
          },
          name: {
            type: 'string',
            title: 'name'
          },
          dataIndex: {
            type: 'number',
            title: 'dataIndex'
          },
          data: {
            type: 'object',
            title: 'data'
          },
          dataType: {
            type: 'string',
            title: 'dataType'
          },
          value: {
            type: 'number',
            title: 'value'
          },
          color: {
            type: 'string',
            title: 'color'
          }
        }
      }
    }
  }
];

const chartDefaultConfig = {
  series: [
    {
      type: 'wordCloud',
      fontSizeMin: 12,
      fontSizeMax: 60,
      sizeRange: [12, 60],
      shape: 'circle',
      data: [
        {name: 'Example', value: 10000},
        {name: 'ECharts', value: 6181},
        {name: 'Word Cloud', value: 4386},
        {name: 'Apache', value: 4053}
      ],
      // rotationRange: [0, 0],
      rotation: true,
      rotationRange: [-90, 90],
      rotationStep: 45,
      // maskImage: '',
    }
  ]
};




const otherConfig = {
  title: '其他',
  className: 'p-none',
  data: {
    config: chartDefaultConfig
  },
  body: getSchemaTpl('collapseGroup', [
    {
      title: '标题配置',
      body: [
        {
          type: 'switch',
          label: '标题显示',
          name: 'config.title.show',
          value: "${config.title.show}",
        },
        getSchemaTpl('label', {
          label: '标题文本',
          hiddenOn: '!config.title.show',
          name: 'config.title.text',
          value: "${config.title.text}",
        }),
        {
          type: 'switch',
          label: '标题提示',
          hiddenOn: '!config.title.show',
          name: 'config.title.showSubtext',
          value: "${config.title.showSubtext}",
        },
        {
          type: "formula",
          name: "config.title.subtextStyle.fontSize",
          formula: "${config.title.showSubtext ? 14 : 0}"
        },
        getSchemaTpl('label', {
          label: '提示内容',
          hiddenOn: '!config.title.show || !config.title.showSubtext',
          name: 'config.title.subtext',
          value: "${config.title.subtext}",
        }),
      ]
    }
  ])
};

const normalStyleConfig = {
  title: '样式',
  body: [
    {
      label: '主题',
      type: 'select',
      name: 'chartThemeName',
      value: "${chartThemeName}",
      options: [
        {label: '默认', value: ''},
        {label: 'vintage', value: 'vintage'},
        {label: 'westeros', value: 'westeros'},
        {label: 'wonderland', value: 'wonderland'},
        {label: 'chalk', value: 'chalk'},
        {label: 'macarons', value: 'macarons'},
        {label: 'shine', value: 'shine'},
        {label: 'dark', value: 'dark'},
        {label: 'essos', value: 'essos'},
        {label: 'walden', value: 'walden'},
        {label: 'infographic', value: 'infographic'},
        {label: 'roma', value: 'roma'},
        {label: 'purple-passion', value: 'purple-passion'},        
      ]
    },
    getSchemaTpl('label', {
      label: '颜色',
      name: 'config.color',
      value: "${config.color}",
    }),
    {
      "type": "input-group",
      "name": "input-group",
      "label": "字号范围",
      "body": [
        {
          "type": "input-number",
          "name": "config.series[0].fontSizeMin",
        },
        {
          "type": "input-number",
          "name": "config.series[0].fontSizeMax",
        }
      ]
    },
    {
      type: "formula",
      name: "config.series[0].sizeRange",
      formula: "${config.series[0].fontSizeMin},${config.series[0].fontSizeMax}"
    },
    {
      type: 'switch',
      label: '旋转文字',
      name: 'config.series[0].rotation',
      value: "${config.series[0].rotation}",
    },
    {
      type: "formula",
      name: "config.series[0].rotationRange",
      formula: "${config.series[0].rotation ? [-90, 90] : [0, 0]}"
    },
    // {
    //   type: "input-file",
    //   name: "config.series[0].maskImage",
    //   label: "形状",
    //   accept: ".png",
    //   asBlob: true
    // }
  ]
};

const legendConfig = {
  title: '图例',
  body: [
    {
      type: 'switch',
      label: '显示图例',
      name: 'config.legend.show',
      value: "${config.legend.show}",
    },
    {
      type: 'switch',
      label: '是否分页',
      hiddenOn: '!config.legend.show',
      name: 'config.legend.pageShow',
      value: "${config.legend.pageShow}",
    },
    {
      type: "formula",
      name: "config.legend.type",
      formula: "${config.legend.pageShow ? 'scroll' : 'plain'}"
    },
  ]
}

const labelConfig = {
  title: '数据标签',
  body: [
    {
      type: 'switch',
      label: '显示标签',
      name: 'config.series[0].label.show',
      value: "${config.series[0].label.show}",
    },
    {
      type: 'switch',
      label: '连接线',
      name: 'config.series[0].labelLine.show',
      value: "${config.series[0].labelLine.show}",
    },
    {
      label: '位置',
      type: 'select',
      name: 'config.series[0].label.customPosition',
      value: "${config.series[0].label.customPosition}",
      options: [
        {label: '外', value: 'outside'},
        {label: '内', value: 'inside'},
        {label: '蜘蛛图', value: 'spider'},
      ]
    },
    {
      type: "formula",
      name: "config.series[0].label.position",
      formula: "${config.series[0].label.customPosition == 'spider' ? 'outside' : config.series[0].label.customPosition}"
    },
    {
      type: "formula",
      name: "config.series[0].labelLine.minTurnAngle",
      formula: "${config.series[0].label.customPosition == 'spider' ? '90' : '180'}"
    },
    {
      label: '标签字号',
      type: "input-number",
      name: "config.series[0].label.fontSize",
      value: "${config.series[0].label.fontSize}",
      hiddenOn: '!config.series[0].label.show',
    },
    {
      label: '标签颜色',
      type: "input-color",
      name: 'config.series[0].label.color',
      value: "${config.series[0].label.color}",
      hiddenOn: '!config.series[0].label.show || config.series[0].label.customPosition == "spider"',
    },
    {
      label: '内容',
      type: 'select',
      name: 'config.series[0].label.labelType',
      value: "${config.series[0].label.labelType}",
      options: [
        {label: '标题，百分比', value: '1'},
        {label: '标题，值(百分比)', value: '2'},
        {label: '标题', value: '3'},
        {label: '标题，值', value: '4'},
        {label: '值', value: '5'},
        {label: '百分比', value: '6'},
        {label: '值(百分比)', value: '7'},
      ]
    },
    {
      type: "formula",
      name: "config.series[0].label.formatter",
      formula: "${config.series[0].label.labelType == 1 ? '{b}\n{d}%' : config.series[0].label.labelType == 2 ? '{b}\n{c} ({d}%)' : config.series[0].label.labelType == 3 ? '{b}' : config.series[0].label.labelType == 4 ? '{b}\n{c}' : config.series[0].label.labelType == 5 ? '{c}' : config.series[0].label.labelType == 6 ? '{d}%' : '{c} ({d}%)'}"
    }
  ]
}

const tooltipConfig = {
  title: '提示信息',
  body: [
    {
      type: 'switch',
      label: '显示提示信息',
      name: 'config.tooltip.show',
      value: "${config.tooltip.show}",
    }
  ]
}

export class WordCloudChartPlugin extends BasePlugin {
  static id = 'WordCloudChartPlugin';
  // 关联渲染器名字
  rendererName = 'word-cloud-chart';
  $schema = '/schemas/ChartSchema.json';

  // 组件名称
  name = '词云图';
  isBaseComponent = true;
  description = 'echarts 词云图';
  docLink = '/amis/zh-CN/components/chart';
  tags = ['报表'];
  icon = 'fa fa-pie-chart';
  pluginIcon = 'chart-plugin';
  scaffold = {
    type: 'word-cloud-chart',
    isEdit: true,
    config: chartDefaultConfig,
    replaceChartOption: true,
    datasetInfo: {},
    xAxisField: [],
    yAxisField: []
  };
  previewSchema = {
    ...this.scaffold,
    api: ''
  };

  // 事件定义
  events: RendererPluginEvent[] = [
    {
      eventName: 'init',
      eventLabel: '初始化',
      description: '组件实例被创建并插入 DOM 中时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              description: '当前数据域，可以通过.字段名读取对应的值'
            }
          }
        }
      ]
    },
    {
      eventName: 'click',
      eventLabel: '鼠标点击',
      description: '鼠标点击时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'mouseover',
      eventLabel: '鼠标悬停',
      description: '鼠标悬停时触发',
      dataSchema: DEFAULT_EVENT_PARAMS
    },
    {
      eventName: 'legendselectchanged',
      eventLabel: '切换图例选中状态',
      description: '切换图例选中状态时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              title: '数据',
              properties: {
                name: {
                  type: 'string',
                  title: 'name'
                },
                selected: {
                  type: 'object',
                  title: 'selected'
                }
              }
            }
          }
        }
      ]
    }
  ];

  // 动作定义
  actions: RendererPluginAction[] = [
    {
      actionType: 'reload',
      actionLabel: '重新加载',
      description: '触发组件数据刷新并重新渲染'
    },
    {
      actionType: 'setValue',
      actionLabel: '变量赋值',
      description: '触发组件数据更新'
    }
    // 特性动作太多了，这里先不加了，可以通过写代码配置
  ];

  panelTitle = '词云图';
  panelJustify = true;
  panelBodyCreator = (context: BaseEventContext) => {
    const handleConfig = (data:any) => {
      // 创建配置的深拷贝
      const configCopy = JSON.parse(JSON.stringify(context.schema.config));
      const previewConfigCopy = JSON.parse(JSON.stringify(this.previewSchema.config));

      if (data.yAxis.length == 1 && data.xAxis.length == 1) {
        let yname = data.yAxis[0].name
        let xname = data.xAxis[0].name
        configCopy.series[0].data = '${list|pick:name~' + xname + ',value~' + yname + '}'
        previewConfigCopy.series[0].data = '${list|pick:name~' + xname + ',value~' + yname + '}'

        const newSchema = {
          ...context.schema,
          config: configCopy,
          xAxisField: data.xAxis,
          yAxisField: data.yAxis
        };
        context.schema.config = configCopy;
        context.schema.xAxisField = data.xAxis;
        context.schema.yAxisField = data.yAxis;
        this.previewSchema.config = previewConfigCopy;
        
        // 通知编辑器进行更新
        context.node && this.manager.store.changeValueById(context.node.id, newSchema);
        
        this.panelBodyCreator(context);
      }

    }

    const handleDataSet = (data:any) => {
      const apiUrl = `/admin-api/system/form-field-value/page/${data.associatedDataTable}?pageNo=1&pageSize=100`;
      
      // 创建完整的新schema以触发更新
      const newSchema = {
        ...context.schema,
        api: apiUrl,
        datasetInfo: data
      };
      
      this.previewSchema.api = apiUrl;
      context.schema.api = apiUrl;
      context.schema.datasetInfo = data;

      // 通知编辑器进行更新
      context.node && this.manager.store.changeValueById(context.node.id, newSchema);
      
      this.panelBodyCreator(context);
    }

    return !context.schema.isEdit?[]:[
      getSchemaTpl('tabs', [
        {
          title: '交互',
          data: {
            config: chartDefaultConfig
          },
          body: [<ChartDataInteraction 
            context={context} 
            config={chartDefaultConfig} 
            handleConfig={handleConfig} 
            selectDataSet={handleDataSet} 
            datasetInfo={context.schema.datasetInfo}
            xAxisField={context.schema.xAxisField}
            yAxisField={context.schema.yAxisField}
          />],
          className: 'p-none editorpanel'
        },
        {
          title: '样式',
          body: getSchemaTpl('collapseGroup', [
            tooltipConfig,
            normalStyleConfig,
            {
              title: '宽高设置',
              body: [
                getSchemaTpl('style:widthHeight', {
                  widthSchema: {
                    label: tipedLabel(
                      '宽度',
                      '默认宽度为父容器宽度，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('100%')
                  },
                  heightSchema: {
                    label: tipedLabel(
                      '高度',
                      '默认高度为300px，值单位默认为 px，也支持百分比等单位 ，如：100%'
                    ),
                    pipeIn: defaultValue('300px')
                  }
                })
              ]
            },
            ...getSchemaTpl('theme:common', {exclude: ['layout']})
          ])
        },
        otherConfig,
        {
          title: '事件',
          className: 'p-none',
          body: [
            getSchemaTpl('eventControl', {
              name: 'onEvent',
              ...getEventControlConfig(this.manager, context)
            })
          ]
        }
      ])
    ];
  };

  editDrillDown(id: string) {
    const manager = this.manager;
    const store = manager.store;
    const node = store.getNodeById(id);
    const value = store.getValueOf(id);

    const dialog = (value.clickAction && value.clickAction.dialog) || {
      title: '标题',
      body: ['<p>内容 <code>${value|json}</code></p>']
    };

    node &&
      value &&
      this.manager.openSubEditor({
        title: '配置 DrillDown 详情',
        value: {
          type: 'container',
          ...dialog
        },
        slot: {
          type: 'container',
          body: '$$'
        },
        typeMutable: false,
        onChange: newValue => {
          newValue = {
            ...value,
            clickAction: {
              actionType: 'dialog',
              dialog: newValue
            }
          };
          manager.panelChangeValue(newValue, diff(value, newValue));
        }
      });
  }
}

registerEditorPlugin(WordCloudChartPlugin);
