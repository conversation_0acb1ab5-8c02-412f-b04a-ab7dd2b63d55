export const GetPagePermission = () => {
    return {
      "type": "page",
      "regions": [
          "body"
      ],
      "pullRefresh": {
          "disabled": true
      },
      "asideResizor": false,
      "title": "5312",
      "style": {
        "height": "300px",  // Set the height to 600px (you can adjust this value)
        "overflow": "auto"  // Add scroll if content exceeds the height
      },
      "body": [
          {
              "type": "crud",
              "syncLocation": false,
              "api": {
                  "method": "get",
                  "url": "/admin-api/system/application-page-and-class/list",
                  "data": {
                      "applicationId":39,
                      "applicantOrBackend":1

                    //   "orderBy": "${orderBy|default:undefined}",
                    //   "orderDir": "${orderDir|default:undefined}",
                    //   "id": "${id|default:undefined}",
                    //   "creator": "${creator|default:undefined}",
                    //   "createTime": "${createTime|default:undefined}",
                    //   "text_1744356176428": "${text_1744356176428|default:undefined}",
                    //   "radios_1744356181971": "${radios_1744356181971|default:undefined}"
                  }
              },
              "bulkActions": [
  
              ],
              "itemActions": [
  
              ],
              "perPageAvailable": [
                  5,
                  10,
                  20,
                  50,
                  100
              ],
              "messages": {
  
              },
              "filterSettingSource": [
                  "id",
                  "creator",
                  "createTime",
              ],
              "headerToolbar": [
                  {
                      "label": "新增",
                      "type": "button",
                      "actionType": "drawer",
                      "level": "primary",
                      "editorSetting": {
                          "behavior": "create"
                      },
                      "drawer": {
                          "type": "drawer",
                          "title": "新增",
                          "body": [
                              {
                                  "type": "form",
                                  "api": {
                                      "method": "post",
                                      "url": "/admin-api/system/form-field-value/create/635"
                                  },
                                  "mode": "horizontal",
                                  "horizontal": {
                                      "left": 2,
                                      "right": 10,
                                      "offset": 2
                                  },
                                  "body": [
                                      {
                                          "label": "文本",
                                          "type": "input-text",
                                          "name": "text_1744356176428",
                                          "valueFormat": "x"
                                      },
                                      {
                                          "label": "单选框",
                                          "type": "input-text",
                                          "name": "radios_1744356181971",
                                          "valueFormat": "x"
                                      }
                                  ],
                                  "actions": [
                                      {
                                          "type": "submit",
                                          "label": "提交",
                                          "primary": true
                                      }
                                  ],
                                  "feat": "Insert"
                              }
                          ],
                          "actionType": "drawer",
                          "actions": [
                              {
                                  "type": "button",
                                  "actionType": "cancel",
                                  "label": "取消"
                              },
                              {
                                  "type": "button",
                                  "actionType": "confirm",
                                  "label": "确定",
                                  "primary": true
                              }
                          ],
                          "showCloseButton": true,
                          "closeOnOutside": false,
                          "closeOnEsc": false,
                          "showErrorMsg": true,
                          "showLoading": true,
                          "draggable": false,
                          "size": "lg",
                          "resizable": false,
                          "editorSetting": {
                              "displayName": "新增"
                          }
                      }
                  },
                  {
                      "type": "export-csv",
                      "tpl": "内容",
                      "wrapperComponent": ""
                  },
                  {
                      "type": "export-excel",
                      "tpl": "内容",
                      "wrapperComponent": ""
                  }
              ],
              "columns": [
                    {
                        "label": "操作权限",
                        "type": "input-text",
                        "name": "name",
                        "static": true,
                        "valueFormat": "x",
                        "sortable": true,
                        "textOverflow": "ellipsis",
                        "needWordBreak": false,
                        "searchable": null
                    },
                  {
                      "label": "权限组名称",
                      "type": "input-text",
                      "name": "id",
                      "static": true,
                      "valueFormat": "x",
                      "sortable": true,
                      "textOverflow": "ellipsis",
                      "needWordBreak": false,
                      "searchable": null
                  },
                  {
                      "label": "描述",
                      "type": "select",
                      "name": "dataSetId",
                      "static": true,
                      "valueFormat": "x",
                      "sortable": true,
                      "textOverflow": "ellipsis",
                      "needWordBreak": false,
                      "searchable": {
                          "type": "input-text",
                          "name": "creator",
                          "label": "创建者",
                          "placeholder": "输入创建者",
                          "mode": "v"
                      }
                  },
                  {
                      "label": "权限成员",
                      "type": "input-datetime",
                      "name": "createTime",
                      "static": true,
                      "valueFormat": "x",
                      "sortable": true,
                      "textOverflow": "ellipsis",
                      "needWordBreak": false,
                      "searchable": {
                          "type": "input-text",
                          "name": "createTime",
                          "label": "创建时间",
                          "placeholder": "输入创建时间",
                          "mode": "v"
                      }
                  },
                  
                  {
                      "label": "字段权限",
                      "type": "input-text",
                      "name": "pageType",
                      "static": true,
                      "valueFormat": "x",
                      "sortable": true,
                      "textOverflow": "ellipsis",
                      "needWordBreak": false,
                      "searchable": null
                  },
                  {
                      "type": "operation",
                      "label": "操作",
                      "name": "operation",
                      "buttons": [
                          {
                              "label": "编辑",
                              "type": "button",
                              "actionType": "dialog",
                              "level": "link",
                              "dialog": {
                                "type": "dialog",
                                "title": "编辑权限组",
                                "width": "800px",
                                "size": "xs",
                                "body": {
                                    "type": "form",
                                    
                                    "mode": "horizontal",
                                    "horizontal": {
                                        "left": 1,
                                        "right": 8
                                    },
                                    "body": [
                                        {
                                            "type": "input-text",
                                            "name": "name",
                                            "label": "名称",
                                            "required": true,
                                            "placeholder": "请输入权限组名称",
                                            "value": "全部成员可提交数据"
                                        },
                                        {
                                            "type": "textarea",
                                            "name": "description",
                                            "label": "描述",
                                            "placeholder": "请输入描述信息",
                                            "value": "可以提交当前表单的权限"
                                        },
                                        {
                                            "type": "radios",
                                            "name": "memberType",
                                            "label": "权限成员",
                                 
                                            "options": [
                                                {
                                                    "label": "全部成员",
                                                    "value": "all",
                                                    
                                                },
                                                {
                                                    "label": "自定义",
                                                    "value": "memberTypeCustom",
                                                }
                                            ],
                                            
                                        },
                                        {
                                            "type": "tpl",
                                            "tpl": "<% if (data.memberType && data.memberType.includes('memberTypeCustom')) { %><span class='link-button text-primary' style='position: relative;top: -45px;left: 250px;color: var(--primary); border: none; background: none; padding: 0; cursor: pointer;'>添加成员(0)</span><% } %>",
                                            "inline": true,
                                            "className": "custom-department-setting",
                                            "onEvent": {
                                                "click": {
                                                    "actions": [
                                                        {
                                                            "actionType": "dialog",
                                                            "dialog": {
                                                                "type": "dialog",
                                                                //"initApi": "/admin-api/system/team-and-project-members/get-selected-users?teamOrProjectId=${teamOrProjectId}",
                                                                "title": "邀请成员",
                                                                    "body": [
                                                                          {
                                                                    "label": "分组",
                                                                    "type": "transfer",
                                                                    "name": "transfer",
                                                                    "selectMode": "tree",
                                                                    "resultListModeFollowSelect": false,
                                                                    "id": "u:d1646dc0a71c",
                                                                    "onlyChildren": true,
                                                                    "autoCheckChildren": true,
                                                                    "source": "/admin-api/system/user/list-dept-user?teamOrProjectOrApplicationId=${teamOrProjectId}&type=${type}",
                                                                    "labelField": "name",
                                                                    "valueField": "id",
                                                                              "searchable": true,
                                                                    "statistics": true,
                                                                    // "value":"${selectMemberIds}",
                                                            
                                                                  }
                                                                ],
                                                                "actions": [
                                                                  {
                                                                    type: 'button',
                                                                    actionType: 'cancel',
                                                                    label: '取消',
                                                                    id: 'u:d2f9d50b6428'
                                                                  },
                                                                  {
                                                                    "type": "button",
                                                                    "label": "确定",
                                                                    "close": true,
                                                                    "primary": true,
                                                                    "actionType": "ajax",
                                                                    "api": {
                                                                      "method": "post",
                                                                      "url": "/admin-api/system/team-project-application-members/batch-create",
                                                                      "data": {
                                                                      //  "teamOrProjectOrApplicationId": "${teamOrProjectId}",
                                                                      //  "userIds": "${transfer}",
                                                                      //  "type": "${type}"
                                                                      }
                                                                    },
                                                                  }
                                                                ],
                                                                  "size": "md"
                                                              }
                                                        }
                                                    ]
                                                }
                                            }
                                        },
                                        
                                        {
                                            "type": "checkboxes",
                                            "name": "operationPermissions",
                                            "label": "操作权限",
                                            "options": [
                                                {
                                                    "label": "提交",
                                                    "value": "submit"
                                                }
                                            ],
                                            "value": ["submit"],
                                            "style": {  // 单独设置某个选项的样式
                                                "clear": "both"
                                            },
                                        },
                                        {
                                            "type": "radios",
                                            "name": "fieldPermissionType",
                                            "label": "字段权限",
                                            "options": [
                                                {
                                                    "label": "继承表单设计中维护的状态",
                                                    "value": "inherit"
                                                },
                                                {
                                                    "label": "自定义",
                                                    "value": "custom"
                                                }
                                            ],
                                            "value": "inherit"
                                        }
                                    ],
                                    "actions": [
                                        {
                                            "type": "button",
                                            "label": "取消",
                                            "actionType": "close"
                                        },
                                        {
                                            "type": "button",
                                            "label": "保存",
                                            "level": "primary",
                                            "actionType": "submit"
                                        }
                                    ]
                                }
                            }
                          },
                          {
                              "label": "查看",
                              "type": "button",
                              //"actionType": "drawer",
                              "level": "link",
                              "editorSetting": {
                                  "behavior": "view"
                              },
                              "actionType": "dialog",
                              "dialog": {
                                "type": "dialog",
                                "title": "编辑权限组",
                                "width": "800px",
                                "size": "xs",
                                "body": {
                                    "type": "form",
                                    
                                    "mode": "horizontal",
                                    "horizontal": {
                                        "left": 1,
                                        "right": 8
                                    },
                                    "body": [
                                        {
                                            "type": "input-text",
                                            "name": "name",
                                            "label": "名称",
                                            "required": true,
                                            "placeholder": "请输入权限组名称",
                                            "value": "全部成员可提交数据"
                                        },
                                        {
                                            "type": "textarea",
                                            "name": "description",
                                            "label": "描述",
                                            "placeholder": "请输入描述信息",
                                            "value": "可以提交当前表单的权限"
                                        },
                                        {
                                            "type": "radios",
                                            "name": "memberType",
                                            "label": "权限成员",
                                 
                                            "options": [
                                                {
                                                    "label": "全部成员",
                                                    "value": "all",
                                                    
                                                },
                                                {
                                                    "label": "自定义",
                                                    "value": "memberTypeCustom",
                                                }
                                            ],
                                            
                                        },
                                        {
                                            "type": "tpl",
                                            "tpl": "<% if (data.memberType && data.memberType.includes('memberTypeCustom')) { %><span class='link-button text-primary' style='position: relative;top: -45px;left: 250px;color: var(--primary); border: none; background: none; padding: 0; cursor: pointer;'>添加成员(0)</span><% } %>",
                                            "inline": true,
                                            "className": "custom-department-setting",
                                            "onEvent": {
                                                "click": {
                                                    "actions": [
                                                        {
                                                            "actionType": "dialog",
                                                            "dialog": {
                                                                "type": "dialog",
                                                                //"initApi": "/admin-api/system/team-and-project-members/get-selected-users?teamOrProjectId=${teamOrProjectId}",
                                                                "title": "邀请成员",
                                                                    "body": [
                                                                          {
                                                                    "label": "分组",
                                                                    "type": "transfer",
                                                                    "name": "transfer",
                                                                    "selectMode": "tree",
                                                                    "resultListModeFollowSelect": false,
                                                                    "id": "u:d1646dc0a71c",
                                                                    "onlyChildren": true,
                                                                    "autoCheckChildren": true,
                                                                    "source": "/admin-api/system/user/list-dept-user?teamOrProjectOrApplicationId=${teamOrProjectId}&type=${type}",
                                                                    "labelField": "name",
                                                                    "valueField": "id",
                                                                    "searchable": true,
                                                                    "statistics": true,
                                                                    // "value":"${selectMemberIds}",
                                                            
                                                                  }
                                                                ],
                                                                "actions": [
                                                                  {
                                                                    type: 'button',
                                                                    actionType: 'cancel',
                                                                    label: '取消',
                                                                    id: 'u:d2f9d50b6428'
                                                                  },
                                                                  {
                                                                    "type": "button",
                                                                    "label": "确定",
                                                                    "close": true,
                                                                    "primary": true,
                                                                    "actionType": "ajax",
                                                                    "api": {
                                                                      "method": "post",
                                                                      "url": "/admin-api/system/team-project-application-members/batch-create",
                                                                      "data": {
                                                                      //  "teamOrProjectOrApplicationId": "${teamOrProjectId}",
                                                                      //  "userIds": "${transfer}",
                                                                      //  "type": "${type}"
                                                                      }
                                                                    },
                                                                  }
                                                                ],
                                                                  "size": "md"
                                                              }
                                                        }
                                                    ]
                                                }
                                            }
                                        },
                                        
                                        {
                                            "type": "checkboxes",
                                            "name": "operationPermissions",
                                            "label": "操作权限",
                                            "options": [
                                                {
                                                    "label": "提交",
                                                    "value": "submit"
                                                }
                                            ],
                                            "value": ["submit"],
                                            "style": {  // 单独设置某个选项的样式
                                                "clear": "both"
                                            },
                                        },
                                        {
                                            "type": "radios",
                                            "name": "fieldPermissionType",
                                            "label": "字段权限",
                                            "options": [
                                                {
                                                    "label": "继承表单设计中维护的状态",
                                                    "value": "inherit"
                                                },
                                                {
                                                    "label": "自定义",
                                                    "value": "custom"
                                                }
                                            ],
                                            "value": "inherit"
                                        }
                                    ],
                                    "actions": [
                                        {
                                            "type": "button",
                                            "label": "取消",
                                            "actionType": "close"
                                        },
                                        {
                                            "type": "button",
                                            "label": "保存",
                                            "level": "primary",
                                            "actionType": "submit"
                                        }
                                    ]
                                }
                            }
                          },
                          {
                              "type": "button",
                              "label": "删除",
                              "actionType": "dialog",
                              "level": "link",
                              "className": "text-danger",
                              "dialog": {
                                  "type": "dialog",
                                  "title": "",
                                  "className": "py-2",
                                  "actions": [
                                      {
                                          "type": "action",
                                          "actionType": "cancel",
                                          "label": "取消"
                                      },
                                      {
                                          "type": "action",
                                          "actionType": "submit",
                                          "label": "删除",
                                          "level": "danger"
                                      }
                                  ],
                                  "body": [
                                      {
                                          "type": "form",
                                          "wrapWithPanel": false,
                                          "api": "delete:/admin-api/system/form-field-value/delete/635/${entries}",
                                          "body": [
                                              {
                                                  "type": "tpl",
                                                  "className": "py-2",
                                                  "tpl": "确认删除选中项？"
                                              }
                                          ],
                                          "feat": "Insert",
                                          "dsType": "api",
                                          "labelAlign": "left"
                                      }
                                  ],
                                  "actionType": "dialog",
                                  "showCloseButton": true,
                                  "closeOnOutside": false,
                                  "closeOnEsc": false,
                                  "showErrorMsg": true,
                                  "showLoading": true,
                                  "draggable": false,
                                  "editorSetting": {
                                      "displayName": "删除"
                                  }
                              }
                          }
                      ],
                      "searchable": null
                  }
              ],
              "pageField": "pageNo",
              "perPageField": "pageSize",
              "alwaysShowPagination": true,
            //   "autoGenerateFilter": {
            //       "columnsNum": 2,
            //       "showBtnToolbar": true,
            //       "defaultCollapsed": false
            //   }
          }
      ],
      "themeCss": {
          "baseControlClassName": {
              "background:default": "#f6f6f6"
          }
      }
  };
  }; 