import React, {<PERSON>} from 'react';
import './index.scss';
import cc_icon from './image/cc_icon.png';
// 抄送人
const CC: FC<any> = (props: any) => {
  return (
    <div className="process_cc">
      <div className="process_cc-info">
        <div className="process_cc-info-box">
          <img className="process_cc-info-box-icon" src={cc_icon} />
        </div>

        <div className="process_cc-info-text">
          <div className="process_cc-info-text-name">{props.item.name}</div>
          <div className="process_cc-info-text-desc">{props.item.desc}</div>
        </div>

        <div className="process_cc-info-member">
          {props.item.props.assignedUser.map((userItem: any) => {
            return (
              <div className="process_cc-info-member-item" key={userItem.id}>
                <div className="process_cc-info-member-item-header">
                  {userItem.avatar ? (
                    <img
                      className="process_cc-info-member-item-header-img"
                      src={userItem.avatar}
                    />
                  ) : (
                    <span>{userItem.name}</span>
                  )}
                </div>
                <div className="process_cc-info-member-item-name">
                  {userItem.name}
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <div className="process_cc-line"></div>
    </div>
  );
};

export default CC;
