export const editInterestStatus = () => {
    return {
        "type": "dialog",
        "title": "编辑权限组",
        "size": "md",
        "body": [
            {
                "type": "form",
                "api": {
                    "method": "put",
                    "url": "/admin-api/system/application-page-and-class/update/3"
                },
                "body": [
                    {
                        "type": "input-text",
                        "name": "name",
                        "label": "名称",
                        "required": true,
                        "placeholder": "请输入权限组名称"
                    },
                    {
                        "type": "textarea",
                        "name": "description",
                        "label": "描述",
                        "placeholder": "请输入描述信息"
                    },
                    {
                        "type": "radios",
                        "name": "permissionType",
                        "label": "权限成员",
                        "options": [
                            {
                                "label": "全部成员",
                                "value": "all"
                            },
                            {
                                "label": "自定义",
                                "value": "custom"
                            }
                        ],
                        "value": "all"
                    },
                    {
           
                        "type": "checkboxes",
                        "name": "dataRange",
                        "label": "数据范围",
                        "multiple": true,
                        "options": [
                            {
                                "label": "全部数据",
                                "value": "all"
                            },
                            {
                                "label": "本人提交",
                                "value": "self"
                            },
                            {
                                "label": "本部门提交",
                                "value": "department"
                            },
                            {
                                "label": "同级部门提交",
                                "value": "sameLevelDepartment"
                            },
                            {
                                "label": "下级部门提交",
                                "value": "subordinateDepartment"
                            },
                            {
                                "label": "免登提交",
                                "value": "freeLogin"
                            },
                            {
                                "label": "自定义过滤条件",
                                "value": "customFilter",
                                "onChange": {
                                      "actions": [
                                          {
                                              "actionType": "dialog",
                                              "component": {
                                                  "type": "dialog",
                                                  "title": "自定义过滤条件设置",
                                                  "body": "这里是自定义过滤条件的内容，可以根据实际需求进行设置"
                                              }
                                          }
                                      ]
                                 }
                            },
                            {
                              "label": "自定义部门",
                              "value": "customDepartment",
                            },
                        ],
                        "style": {  // 单独设置某个选项的样式
                                 //     "float": "left",
                                 },
                        "inline": true
                    },
                    {
                      "type": "tpl",
                      "tpl": "<% if (data.dataRange && data.dataRange.includes('customDepartment')) { %><span class='link-button text-primary' style='float: relative;top: 5px;color: var(--primary); border: none; background: none; padding: 0; cursor: pointer;'>设置部门 (0)</span><% } %>",
                      "inline": true,
                      "className": "custom-department-setting",
                      "onEvent": {
                          "click": {
                              "actions": [
                                  {
                                      "actionType": "dialog",
                                      "dialog": {
                                        "type": "dialog",
                                        //"initApi": "/admin-api/system/team-and-project-members/get-selected-users?teamOrProjectId=${teamOrProjectId}",
                                        "title": "邀请成员",
                                        "body": [
                                          {
                                            "label": "分组",
                                            "type": "transfer",
                                            "name": "transfer",
                                            "selectMode": "tree",
                                            "resultListModeFollowSelect": false,
                                            "id": "u:d1646dc0a71c",
                                            "onlyChildren": true,
                                            "autoCheckChildren": true,
                                            "source": "/admin-api/system/user/list-dept-user?teamOrProjectOrApplicationId=${teamOrProjectId}&type=${type}",
                                            "labelField": "name",
                                            "valueField": "id",
                                            "searchable": true,
                                            "statistics": true,
                                            // "value":"${selectMemberIds}",
                                    
                                          }
                                        ],
                                        "actions": [
                                          {
                                            type: 'button',
                                            actionType: 'cancel',
                                            label: '取消',
                                            id: 'u:d2f9d50b6428'
                                          },
                                          {
                                            "type": "button",
                                            "label": "确定",
                                            "close": true,
                                            "primary": true,
                                            "actionType": "ajax",
                                            "api": {
                                              "method": "post",
                                              "url": "/admin-api/system/team-project-application-members/batch-create",
                                              "data": {
                                                "teamOrProjectOrApplicationId": "${teamOrProjectId}",
                                                "userIds": "${transfer}",
                                                "type": "${type}"
                                              }
                                            },
                                          }
                                        ],
                                        "size": "md"
                                      }
                                  }
                              ]
                          }
                      }
                    },  
                    {
                        "type": "checkboxes",
                        "name": "operationPermissions",
                        "label": "操作权限",
                        "multiple": true,
                        "options": [
                            {
                                "label": "编辑",
                                "value": "edit"
                            },
                            {
                                "label": "删除",
                                "value": "delete"
                            },
                            {
                                "label": "变更记录",
                                "value": "changeLog"
                            },
                            {
                                "label": "评论",
                                "value": "comment"
                            },
                            {
                                "label": "打印",
                                "value": "print"
                            }
                        ]
                    },
                    {
                        "type": "radios",
                        "name": "fieldPermissionType",
                        "label": "字段权限",
                        "options": [
                            {
                                "label": "继承表单设计中维护的状态",
                                "value": "inherit"
                            },
                            {
                                "label": "自定义",
                                "value": "custom"
                            }

                        ],
                        "style": {  // 单独设置某个选项的样式
                                      "float": "left",
                                 },
                        "value": "inherit"
                    },
                    {
                      "type": "tpl",
                      "tpl": "<% if (data.fieldPermissionType && data.fieldPermissionType.includes('custom')) { %><span class='link-button text-primary' style='position: relative;top: 5px;color: var(--primary); border: none; background: none; padding: 0; cursor: pointer;'>设置字段</span><% } %>",
                      "inline": true,
                      "className": "custom-department-setting",
                      "onEvent": {
                          "click": {
                              "actions": [
                                  {
                                      "actionType": "dialog",
                                      "dialog": {
                                          "type": "dialog",
                                          "title": "字段权限",
                                          "size": "lg",
                                          "body": {
                                            "type": "table",
                                            "columns": [
                                              {
                                                "name": "field",
                                                "label": "字段",
                                                "width": 200
                                              },
                                              {
                                                "name": "permission",
                                                "label": "可编辑",
                                                "type": "radios",
                                                "options": [
                                                  {
                                                    "label": "",
                                                    "value": "editable"
                                                  }
                                                ],
                                                "columnClassName": "text-center"
                                              },
                                              {
                                                "name": "permission",
                                                "label": "只读",
                                                "type": "radios",
                                                "options": [
                                                  {
                                                    "label": "",
                                                    "value": "readonly1"
                                                  }
                                                ],
                                                "columnClassName": "text-center"
                                              },
                                              {
                                                "name": "permission",
                                                "label": "只读",
                                                "type": "radios",
                                                "options": [
                                                  {
                                                    "label": "",
                                                    "value": "readonly2"
                                                  }
                                                ],
                                                "columnClassName": "text-center"
                                              }
                                            ],
                                            "data": [
                                              {
                                                "field": "全选",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "单行文本",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "多行文本",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "单选",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "日期",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "日期区间",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "成员",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "图片上传",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "附件",
                                                "permission": "readonly1"
                                              },
                                              {
                                                "field": "富文本",
                                                "permission": "readonly1"
                                              }
                                            ]
                                          },
                                          "actions": [
                                            {
                                              "type": "button",
                                              "label": "取消",
                                              "actionType": "close"
                                            },
                                            {
                                              "type": "button",
                                              "label": "确认",
                                              "level": "primary",
                                              "actionType": "close"
                                            }
                                          ]
                                        }
                                  }
                              ]
                          }
                      }
                    }

                ],
                "actions": [
                    {
                        "type": "button",
                        "label": "取消",
                        "actionType": "close"
                    },
                    {
                        "type": "submit",
                        "label": "保存",
                        "level": "primary"
                    }
                ]
            }
        ]
    };
};