import React, {FC, useEffect, useState} from 'react';
import './index.scss';

import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj} from '@/utils/schemaPageTemplate/createPageObjs';
import {navigateWithSource} from '@/utils/routeUtils';
// 审批
import WorkbenchApproval from '@/views/workbenchApprovalCenter/indexTabs';
// wflow消息
import DatasetImportPage from '@/views/manage/component/datasetImportPage/index';
// amis图标库
import ShowAmisIcons from '@/views/workbench/component/ShowAmisIcons';
// fontAwesome图标库
import ShowFontAwesomeIcons from '@/views/workbench/component/ShowFontAwesomeIcons';
// 个人设置
import Account from '@/views/account/index';
// 搭建首页
import AppBuildHomePage from '@/views/appbuild/compontents/AppBuildHomePage';

// 团队主页
import TeamMian from '@/views/workbench/component/TeamMain/index';
// 项目主页
import ProjectMian from '@/views/workbench/component/ProjectMain/index';
import ComponentTemplatePageContent from '@/views/componentTemp/index';
import ImageManagePageContent from '@/views/imageManage/index';
import AppTemplatePageContent from '@/views/appTemp/index';
import { set } from 'lodash';
import {
  utilsSetFromAppbuild
} from '@/utils/routeUtils';
const SwitchRenderContent: FC<any> = (props: any) => {
  let [params, setParams] = useState<any>(null);

  // 当前选中的团队的item
  const [activeTeamItem, setActiveTeamItem] = useState<any>(false);
  // 当前选中的项目的item
  const [activeProjectItem, setActiveProjectItem] = useState<any>(false);
  // 团队列表
  const [teamList, setTeamList] = useState<any>([]);

  useEffect(() => {
    setParams(props.match.params);
  }, [props.location.pathname]);

  useEffect(() => {
    if (props?.teamList && props.teamList.length > 0) {
      setTeamList(props.teamList);
    }
  }, [props.teamList]);

  useEffect(() => {
    if (teamList.length > 0 && params?.team) {
      if (params?.project) {
        setActiveProjectItem(() => {
          // 初始化选中项
          let initCheckedNav = null;
          teamList.forEach((teamItem: any) => {
            if (teamItem.id == params.team) {
              setActiveTeamItem(teamItem)
              teamItem.children.forEach((projectItem: any) => {
                if (projectItem.id == params.project) {
                  initCheckedNav = projectItem;
                }
              });
            }
          });
          return initCheckedNav;
        });
      } else {
        setActiveTeamItem(() => {
          // 初始化选中项
          let initCheckedNav = null;
          teamList.forEach((teamItem: any) => {
            if (teamItem.id == params.team) {
              initCheckedNav = teamItem;
            }
          });
          return initCheckedNav;
        });
      }
    }
  }, [teamList,params]);

  return (
    <div className="switchRenderContent">
      {
        // 搭建首页
        params?.menu && params?.menu == 'home' && (
          <AppBuildHomePage
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
          />
        )
      }
      {
        // 审批
        params?.menu && params?.menu == 'approval' && (
          <WorkbenchApproval
            store={props.store}
            history={props.history}
            match={props.match}
            location={props.location}
          />
        )
      }
      {
        // 消息
        params?.menu && params?.menu == 'message' && (
          <AMISRenderer
            schema={createIframeObj(
              `${
                process.env.NODE_ENV === 'development'
                  ? 'http://localhost:88/#/workspace/notice'
                  : 'https://javaddm.fjpipixia.com/wflow/#/workspace/notice'
              }?userID=${props.store.userInfo?.id}&tenantId=${
                props.store.tenant_id
              }&authorization=${props.store.access_token}`
            )}
          />
        )
      }
      {
        //wflow消息
        params?.menu && params?.menu == 'importMessage' && (
          <DatasetImportPage
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            initPathItem={{
              id: 1480,
              applicantOrBackend: 2,
              applicationId: null,
              parentId: 0,
              dataSetId: 720,
              type: 1,
              sort: null,
              name: 'wflow消息',
              description: null,
              pageType: 13,
              url: '',
              jmalUserId: null,
              children: [],
              createTime: 1744074411000,
              path: '/platform/importMessage',
              icon: 'fa-solid fa-message'
            }}
          />
        )
      }
      {
        // 组件模板
        params?.menu && params?.menu == 'componentTemplate' && (
          <ComponentTemplatePageContent
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            type='1'
          />
        )
      }
      {
        // 应用模板
        params?.menu && params?.menu == 'appTemplate' && (
          <AppTemplatePageContent
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            type='2'
          />
        )
      }
      {
        // 表单模板
        params?.menu && params?.menu == 'formTemplate' && (
          //todo 表单模板
          <ComponentTemplatePageContent
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            type='3'
          />
        )
      }
            {
        // 页面模板
        params?.menu && params?.menu == 'pageTemplate' && (
          //todo 页面模板
          <ComponentTemplatePageContent
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            type='4'
          />
        )
      }
      {
        // 图片管理
        params?.menu && params?.menu == 'imageManage' && (
          <ImageManagePageContent
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            type='1'
          />
        )
      }
            {
        // 图标管理
        params?.menu && params?.menu == 'iconManage' && (
          <ImageManagePageContent
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
            type='2'
          />
        )
      }
      {
        // 应用后台
        params?.menu && params?.menu == 'appmanage' &&(
          (() => {
            utilsSetFromAppbuild();
            navigateWithSource(props, '/appbuild/manage');
            return null;
          })()
        )
      }
      {
        // 平台后台
        params?.menu && params?.menu == 'platformManage' &&(
          (() => {
            navigateWithSource(props, '/platform/manage');
            return null;
          })()
        )
      }
      {
        // 模版中心
        params?.menu && params?.menu == 'template' && <div>模版中心</div>
      }
      {
        // Amis图标库
        params?.menu && params?.menu == 'icons' && <ShowAmisIcons />
      }
      {
        // fontAwesome图标库
        params?.menu && params?.menu == 'fontawesome' && (
          <ShowFontAwesomeIcons />
        )
      }
      {
        //个人设置
        params?.menu && params?.menu == 'account' && (
          <Account
            store={props.store}
            history={props.history}
            location={props.location}
            match={props.match}
          />
        )
      }
      {/* 团队相关内容 */}
      {!params?.menu && params?.team && !params?.project && (
        <TeamMian
          store={props.store}
          dataInfo={activeTeamItem}
          history={props.history}
          location={props.location}
          updateMenuData={()=>props.updateMenuData()}
        ></TeamMian>
      )}
      {/* 项目相关内容 */}
      {!params?.menu && params?.team && params?.project && (
        <ProjectMian
          store={props.store}
          activeTeamItem={activeTeamItem}
          dataInfo={activeProjectItem}
          history={props.history}
          location={props.location}
          updateMenuData={()=> props.updateMenuData()}
        />
      )}
    </div>
  );
};

export default SwitchRenderContent;
