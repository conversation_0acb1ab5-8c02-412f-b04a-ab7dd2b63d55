// 创建简单的名称输入对话框
const createSimpleNameDialog = (title: string, placeholder: string = '请输入名称') => {
  return {
    type: 'dialog',
    title: title,
    size: 'm',
    body: [
      {
        id: 'simple-name-form',
        type: 'form',
        title: '表单',
        mode: 'flex',
        labelAlign: 'top',
        dsType: 'api',
        feat: 'Insert',
        body: [
          {
            type: 'input-text',
            label: '',
            name: 'name',
            id: 'name-input-field',
            placeholder: placeholder,
            required: true,
            inputClassName: 'name-input',
            labelClassName: 'name-label'
          }
        ],
        actions: [
          {
            type: 'button',
            label: '提交',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'submit',
                    componentId: 'simple-name-form'
                  }
                ]
              }
            },
            level: 'primary'
          }
        ],
        resetAfterSubmit: false,
        closeDialogOnSubmit: true
      }
    ],
    id: 'simple-name-dialog',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        id: 'cancel-btn'
      },
      {
        type: 'button',
        actionType: 'confirm',
        label: '确定',
        primary: true,
        id: 'confirm-btn'
      }
    ],
    showCloseButton: false,
    closeOnOutside: false,
    closeOnEsc: false,
    showErrorMsg: true,
    showLoading: true,
    draggable: false,
    editorSetting: {
      displayName: '输入名称'
    },
    themeCss: {
      dialogClassName: {
        radius: {
          'top-left-border-radius': '8px',
          'top-right-border-radius': '8px',
          'bottom-left-border-radius': '8px',
          'bottom-right-border-radius': '8px'
        }
      }
    }
  };
};

export default createSimpleNameDialog;
