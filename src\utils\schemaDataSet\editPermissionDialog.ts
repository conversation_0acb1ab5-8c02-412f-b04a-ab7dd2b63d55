// type 1 表单提交权限 2 表单查看权限
export const editPermissionDialog = (applicationPageId: string|number, permission: any, type: string|number) => {

    let isEdit = false;
    if (permission) {
        isEdit = true;
        permission.memberType = permission.userIds == 'all' ? 'all' : 'memberTypeCustom';
    }
    return {
        type: "dialog",
        title: "编辑权限组",
        width: "800px",
        size: "xs",
        body: {
            type: "form",
            mode: "horizontal",
            data: permission,
            api: {
                url: isEdit ? "/admin-api/system/application-page-permission/update" : "/admin-api/system/application-page-permission/create",
                method: isEdit ? "put" : "post",
                data: {
                    applicationPageId: applicationPageId,
                    type: type,
                    name: "${name}",
                    remark: "${remark}",
                    id: isEdit ? "${id}" : undefined,
                },
                requestAdaptor: function(api: any, context: any) {
                    if (context.memberType == 'memberTypeCustom') {
                        context.userIds = context.userIds
                        return {
                            ...api,
                            data: {
                                ...api.data,
                                userIds: context.userIds,
                                
                            }
                        }
                    } else {
                        return {
                            ...api,
                            data: {
                                ...api.data,
                                userIds: 'all'
                            }
                        }
                    }
                  }
            },
            horizontal: {
                left: 1,
                right: 8
            },
            body: [
                {
                    type: "input-text",
                    name: "name",
                    label: "名称",
                    required: true,
                    placeholder: "请输入权限组名称",
                },
                {
                    type: "textarea",
                    name: "remark",
                    label: "描述",
                    placeholder: "请输入描述信息",
                },
                {
                    type: "radios",
                    name: "memberType",
                    label: "权限成员",
                    required: true,
                    options: [
                        {
                            label: "全部成员",
                            value: "all",
                        },
                        {
                            label: "自定义",
                            value: "memberTypeCustom",
                        }
                    ],
                },
                {
                    type: "user-multi-select",
                    name: "userIds",
                    label: "成员",
                    placeholder: "请选择成员",
                    visibleOn: "${memberType == 'memberTypeCustom'}",
                    clearable: true,
                    multiple: true,
                    labelField: "name",
                    valueField: "id",
                    joinValues: true,
                    extractValue: true,
                    enableNodePath: false,
                    withChildren: true,
                    onlyChildren: true,
                    onlyLeaf: true,
                    cascade: true,
                    delimiter: ',',
                    source: {
                      data: JSON.parse(localStorage.getItem('userTreeList') || '[]')
                    }
                },
                {
                    type: "checkboxes",
                    name: "operationPermissions",
                    label: "操作权限",
                    disabled: true,
                    options: [
                        {
                            "label": type == 1 ? "提交" : "查看",
                            "value": type == 1 ? "submit" : "view"
                        }
                    ],
                    value: [type == 1 ? "submit" : "view"],
                    style: {  // 单独设置某个选项的样式
                        clear: "both"
                    },
                },
            ],
            actions: [
                {
                    type: "button",
                    label: "取消",
                    actionType: "close"
                },
                {
                    type: "button",
                    label: "保存",
                    level: "primary",
                    actionType: "submit"
                }
            ]
        }
    };
};