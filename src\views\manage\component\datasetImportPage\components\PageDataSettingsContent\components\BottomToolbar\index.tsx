import React from 'react';
import AMISRenderer from '@/component/AMISRenderer';
import './index.scss';

interface BottomToolbarProps {
  dataSetInfo: any;
  initPathItem: {
    id: number;
    name: string;
    path: string;
    icon: string;
    children?: any[];
    [key: string]: any;  // 为其他可能的属性添加索引签名
  };
}

const BottomToolbar: React.FC<BottomToolbarProps> = ({ dataSetInfo,initPathItem }) => {
  return (
    <div className="bottom-toolbar">
      <AMISRenderer 
        schema={{
          type: 'form',
          initApi: `/admin-api/system/data-set/get-pagination-config/${dataSetInfo?.id}`,
          api: `/admin-api/system/data-set/update-pagination-config/${dataSetInfo?.id}`,
          body: [
            {
              type: 'switch',
              name: 'enablePagination',
              label: '启用分页',
              onText: '是',
              offText: '否'
            },
            {
              type: 'input-number',
              name: 'defaultPageSize',
              label: '默认每页条数',
              min: 1,
              max: 100
            }
          ]
        }}
      />
    </div>
  );
};

export default BottomToolbar; 