.theme-switch {
  display: flex;
  align-items: center;
  
  .theme-switch-btn {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 4px;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    i {
      font-size: 16px;
    }
  }
  
  // 开关样式
  .switch-wrapper {
    position: relative;
    display: inline-block;
    width: 70px;
    height: 24px;
    
    input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      
      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
        z-index: 1;
      }
      
      .mode-text {
        font-size: 12px;
        color: white;
        z-index: 0;
      }
    }
    
    input:checked + .slider {
      background-color: #2468f2;
    }
    
    input:checked + .slider:before {
      transform: translateX(46px);
    }
  }
  
  // 添加文字标签样式
  .switch-label {
    font-size: 14px;
    color: var(--text-color);
    margin-left: 4px;
  }
} 