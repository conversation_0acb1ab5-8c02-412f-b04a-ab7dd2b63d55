.bind-mobile-dialog {
  margin: 0 !important;
  padding:0 24px !important;

  .dialog-header {
    margin-bottom: 16px;
    font-size: 14px;
    color: #151B26;
    font-weight: bold;
  }

  .form-item {
    margin-bottom: 20px;

    .form-control {
      width: 100%;
      height: 40px;
      padding: 10px;
      border: 1px solid #E8E9EB;
      border-radius: 4px !important;
      font-size: 14px;
      color: #151B26;
      &::placeholder {
        color: #B8BABF;
      }
      &:focus {
        border-color: #2468f2;
        outline: none;
      }
    }

    &.code-item {
      display: flex;
      gap: 16px;

      input {
        flex: 1;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 4px;

    .cancel-btn {
      height: 32px;
      padding: 6px 21px;
      border: 1px solid #e8e9eb;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      background: white;
      color: #151b26;

      &:hover {
        border-color: #2468f2;
        color: #2468f2;
      }
    }

    .confirm-btn {
      height: 32px;
      padding: 6px 21px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.verify-code-btn {
  border: 1px solid #e8e9eb;
  border-radius: 4px;
  color: #151b26;
  font-size: 14px;
  height: 40px !important;
  padding: 10px 25px;
  cursor: pointer;
  background: white;

  &:hover:not(:disabled) {
    border-color: #2468f2;
    color: #2468f2;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
} 