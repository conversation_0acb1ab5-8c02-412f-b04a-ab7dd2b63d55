import { registerEditorPlugin, BasePlugin } from 'amis-editor';
import {
  RendererEventContext,
  SubRendererInfo,
  BasicSubRenderInfo
} from 'amis-editor-core';
import './Charts/BarChart'
import './Charts/BoxplotChart'
import './Charts/CandlestickChart'
import './Charts/CalendarHeatmapChart'
import './Charts/WordCloudChart'
import './Charts/TreemapChart'
import './Charts/TreeChart'
import './Charts/ThemeRiverChart'
import './Charts/SunburstChart'
import './Charts/ScatterChart'
import './Charts/SankeyChart'
import './Charts/RadarChart'
import './Charts/PieChart'
import './Charts/ParallelChart'
import './Charts/MixLineBarChart'
import './Charts/FunnelChart'
import './Charts/GraphChart'
import './Charts/GaugeChart'
import './Charts/HeatmapChart'
import './Charts/LineChart'
import './Charts/MyRendererPlugin'
import './Charts/MapChart'
import './Charts/Gauge<PERSON>hart'
import './SelectIcon/IconSelectorPlugin'
import './SelectIcon/IconShowPlugin'
import './TreeSelectDept/treeSelectDept'
import './TreeSelectMember/treeSelectMember'
import './TreeSelectRole/treeSelectRole'
import './TreeSelectPos/treeSelectPos'
import './MultipleTreeSelectMember/multipleTreeSelectMember'
import './MultipleTreeSelectDept/multipleTreeSelectDept'
import './MultipleTreeSelectRole/multipleTreeSelectRole'
import './MultipleTreeSelectPos/multipleTreeSelectPos'
import './InputAvatar/inputAvatar'
import './InputAppIcon/inputAppIcon'
import './AppIcon/appIcon'
import './InputAddress/inputAddress'
import './InputPhone/inputPhone'
import './MyNumber/myNumber'
import './MyDrawer/myDrawer'
import './MyDialog/myDialog'
import './MyRadio/myRadio'
import './MyAmis/myAmis'
import './MyInputVerificationCode/myInputVerificationCode'
import './Serial/serial'
import './Relatedfiled/relatedfiled'
import './Portlet/MyPortlet/myPortlet'
import './Portlet/MyNavPage/myNavPage'
import './MyInputExcel/myInputExcel'
import './Portlet/MyCarousel/myCarousel'
import './Portlet/MyDataCardContainer/myDataCardContainer'
import './Portlet/MyApp/myApp'
import './Portlet/MyGridNav/myGridNav'
import './Portlet/MyShape/myShape'
import './Portlet/MySpinner/mySpinner'
import './Portlet/MyNav/myNav'
import './Portlet/ComponentViewer/ComponentViewer'
import './Portlet/MyJsonSchema/myJsonSchema'
import './Portlet/MyJsonSchemaEditor/myJsonSchemaEditor'
// import './Charts/Grid2D'
import './Charts/MediaCard'
import './Charts/UserCard'
import './Charts/IconCard'
/**
 * 用于隐藏一些不需要的Editor组件
 * 备注: 如果不知道当前Editor中有哪些预置组件，可以在这里设置一个断点，console.log 看一下 renderers。
 */

// 需要在组件面板中隐藏的组件
let disabledRenderers: string[] = [''];

// 组件配置数据结构
// [
//   {
//       "name": "页面",
//       "classificationName": "布局容器",//用与tag
//       "componentType": "page",//rendererName
//       "status": 1,//0关闭1启用
//       "sort": null,//order
//   },
//   {
//       "name": "包裹",
//       "classificationName": "布局容器",
//       "componentType": "wrapper",
//       "status": 1,
//       "sort": 2,
//   }
// ]
let sysRendererConfigs: any[] = []
let orgRendererConfigs: any[] = []
let appRendererConfigs: any[] = []

export function configureManagerEditorPlugin(disabledComponents: string[], sysComponentConfigs?: any[], orgComponentConfigs?: any[], appComponentConfigs?: any[]) {
  disabledRenderers = disabledComponents || [];
  if (sysComponentConfigs) {
    sysRendererConfigs = sysComponentConfigs;
  }
  if (orgComponentConfigs) {
    orgRendererConfigs = orgComponentConfigs;
  }

  if (appComponentConfigs) {
    appRendererConfigs = appComponentConfigs;
  }
 

  return ManagerEditorPlugin
}

// 导出函数获取最新的 appRendererConfigs
export function getAppRendererConfigs() {
  return appRendererConfigs;
}

export class ManagerEditorPlugin extends BasePlugin {
  order = 9999;
  beforeResolveJsonSchema(){
    // 隐藏json代码编辑器模块
    // event.preventDefault() 
  }
  buildSubRenderers(
    context: RendererEventContext,
    renderers: Array<SubRendererInfo>
  ): BasicSubRenderInfo | Array<BasicSubRenderInfo> | void {
    // 更新NPM自定义组件排序和分类
    for (let index = 0, size = renderers.length; index < size; index++) {
      // 判断是否需要隐藏 Editor预置组件
      const pluginRendererName = renderers[index].rendererName;
      
      // 默认隐藏所有预置组件
      renderers[index].disabledRendererPlugin = true;
      
      // 如果在启用列表中，则显示组件
      if (
        pluginRendererName &&
        disabledRenderers.indexOf(pluginRendererName) > -1
      ) {
        renderers[index].disabledRendererPlugin = false;
        
        // 查找对应的组件配置
        const config = sysRendererConfigs.find(config => 
          config.amisControlType === pluginRendererName
        );
        
        if (config) {
          // 应用组件配置
          if (config.controlType) {
            renderers[index].name = config.controlType;
          }
          
          if (config.classificationName) {
            // 设置组件分类标签
            renderers[index].tags = [config.classificationName];
          }
          
          if (typeof config.sort === 'number') {
            // 设置组件排序
            renderers[index].order = config.sort;
          }
          
          if (config.status === 0) {
            // 如果状态为禁用，则隐藏组件
            renderers[index].disabledRendererPlugin = true;
          }
        }
      }
    }
  }

  // buildAppSubRenderers(
  //   context: RendererEventContext,
  //   renderers: Array<SubRendererInfo>
  // ): BasicSubRenderInfo | Array<BasicSubRenderInfo> | void {
  //   // 更新NPM自定义组件排序和分类
  //   console.log("appRendererConfigs",appRendererConfigs);
  //   for (let index = 0, size = renderers.length; index < size; index++) {
  //     // 判断是否需要隐藏 Editor预置组件
  //     renderers[index].disabledRendererPlugin = true;

  //     const pluginRendererName = renderers[index].rendererName;
  //     if (pluginRendererName === 'select') {
  //       renderers[index].disabledRendererPlugin = false;

  //       // console.log("appRendererConfigs", appRendererConfigs);
  //     }
  //     // 默认隐藏所有预置组件
      
  //     // // 如果在启用列表中，则显示组件
  //     // if (
  //     //   pluginRendererName
  //     // ) {        
  //     //   // 查找对应的组件配置
  //     //   const config = appRendererConfigs.find(config => 
  //     //     config.amisControlType === pluginRendererName
  //     //   );

  //     //   if (config) {
  //     //       console.log("config=====",config,pluginRendererName);
  //     //       renderers[index].disabledRendererPlugin = true;
  //     //   }
  //     // }
  //   }
  // }

  // buildOrgSubRenderers(
  //   context: RendererEventContext,
  //   renderers: Array<SubRendererInfo>
  // ): BasicSubRenderInfo | Array<BasicSubRenderInfo> | void {
  //   // 更新NPM自定义组件排序和分类
  //   for (let index = 0, size = renderers.length; index < size; index++) {
  //     // 判断是否需要隐藏 Editor预置组件
  //     const pluginRendererName = renderers[index].rendererName;
      
  //     // 默认隐藏所有预置组件
  //     // renderers[index].disabledRendererPlugin = true;
      
  //     // 如果在启用列表中，则显示组件
  //     if (
  //       pluginRendererName
  //     ) {
        
  //       // 查找对应的组件配置
  //       const config = orgRendererConfigs.find(config => 
  //         config.amisControlType === pluginRendererName
  //       );
        
  //       if (config) {
  //         // 应用组件配置
  //         // if (config.status === 0) {
  //           // 如果状态为禁用，则隐藏组件
  //           renderers[index].disabledRendererPlugin = true;
  //         // } else {
  //         //   renderers[index].disabledRendererPlugin = false;
  //         // }
  //       }
  //     }
  //   }
  // }
}
registerEditorPlugin(ManagerEditorPlugin);

