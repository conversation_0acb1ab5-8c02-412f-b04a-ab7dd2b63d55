import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';

export class AMISPlugin extends BasePlugin {
  static id = 'AMISPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'amis';
  $schema = '/schemas/AIMSRenderSchema.json';

  // 组件基本信息
  name = 'AMIS 渲染器';
  panelTitle = 'AMIS 渲染器';
  icon = 'fa fa-window-maximize';
  panelIcon = 'fa fa-window-maximize';
  pluginIcon = 'fa fa-window-maximize';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '渲染数据里的 amis schema';
  docLink = '/amis/zh-CN/components/amis';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'amis',
    className: 'app-wrapper',
  };

  // 预览界面
  previewSchema = {
    type: 'amis',
    className: 'text-left',
  };
}

// 注册插件
registerEditorPlugin(AMISPlugin);
