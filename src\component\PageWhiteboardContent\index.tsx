import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj} from '@/utils/schemaPageTemplate/createPageObjs';
import PageSettings from '@/component/PageSettings/index';

const PageContent: FC<any> = (props: any) => {
  
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');
  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  const getToBlak = () => {
    let schema = JSON.parse(props.pageData.schema)
    let a = document.createElement('a');
    a.href = schema.body[0].src;
    window.open(a.href, '_blank');
  };

  React.useEffect(() => {
    const urlParams = new URLSearchParams(props.history.location.search);
    let tabkey =  urlParams.get('activeTabKey') || 'preview';
    setActiveKey(tabkey);
  }, [props.location]);

  return (
    <div className="pageBox">
      {props.match.params.playType == 'admin' && <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-6">
            {/* <Button size="lg" level="default" onClick={() => getToBlak()}>
              访问
            </Button> */}
          </div>
        </div>
      </div>}
      <div className="pageTabsLink">
        {/* activeKey */}
        <Tabs
          mode="line"
          theme={localStorage.getItem('amis-theme') || 'cxd'} // 显式传入主题
          onSelect={(key: any) => handleTabChange(key)}
          toolbar={TabsToolbar()}
          linksClassName="pageTabsLink-tabsTitle"
          activeKey={activeKey}
        >
          <Tab title="白板内容" eventKey="preview">
            <div className="pageTabsLink-tabsContent">
              <AMISRenderer
                schema={createIframeObj(props.pageData.url)}
                embedMode={true}
              />
            </div>
          </Tab>
          {props.match.params.playType == 'admin' && <Tab title="页面设置" eventKey="setting">
            <PageSettings
              history={props.history}
              pageData={props.pageData}
              store={props.store}
              update={() => {
                props.updatePage();
              }}
            />
          </Tab>}
         {props.match.params.playType == 'admin' &&  <Tab title="页面发布" eventKey="publish"></Tab>}
        </Tabs>
      </div>
    </div>
  );
};

export default PageContent;
