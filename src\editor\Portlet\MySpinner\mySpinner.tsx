import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';

export class MySpinnerPlugin extends BasePlugin {
  static id = 'MySpinnerPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'spinner';
  $schema = '/schemas/MySpinnerSchema.json';

  // 组件基本信息
  name = 'Spinner';
  panelTitle = 'Spinner 加载中';
  icon = 'fa fa-spinner';
  panelIcon = 'fa fa-spinner';
  pluginIcon = 'fa fa-spinner';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '加载中组件，用于展示加载状态';
  docLink = '/amis/zh-CN/components/spinner';
  tags = ['门户'];

  // 组件默认配置
  scaffold = {
    type: 'spinner',
    show: true,
    className: 'spinner-default',
    spinnerClassName: 'spinner-icon',
    spinnerWrapClassName: 'spinner-wrap',
    size: 'lg',
    icon: '',
    tip: '加载中...',
    tipPlacement: 'bottom',
    delay: 0,
    overlay: false,
    body: {
      type: 'tpl',
      tpl: '内容区域'
    }
  };

  // 预览界面
  previewSchema = {
    type: 'spinner',
    className: 'text-left',
    show: true,
    tip: '加载中...'
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                {
                  type: 'input-text',
                  name: 'className',
                  label: 'CSS类名',
                  description: '自定义CSS类名'
                },
                {
                  type: 'input-text',
                  name: 'spinnerClassName',
                  label: '图标类名',
                  description: 'spin图标位置包裹元素的自定义class'
                },
                {
                  type: 'input-text',
                  name: 'spinnerWrapClassName',
                  label: '容器类名',
                  description: '作为容器使用时最外层元素的class'
                },
                {
                  type: 'switch',
                  name: 'show',
                  label: '显示状态',
                  value: true,
                  description: '控制Spinner显示与隐藏'
                },
                {
                  type: 'select',
                  name: 'size',
                  label: '尺寸',
                  value: 'lg',
                  options: [
                    {label: '小', value: 'sm'},
                    {label: '默认', value: ''},
                    {label: '大', value: 'lg'}
                  ],
                  description: 'spinner Icon 大小'
                },
                {
                  type: 'icon-picker',
                  name: 'icon',
                  label: '自定义图标',
                  description: '自定义spinner的图标'
                },
                {
                  type: 'input-text',
                  name: 'tip',
                  label: '提示文字',
                  description: 'spinner文案'
                },
                {
                  type: 'select',
                  name: 'tipPlacement',
                  label: '文字位置',
                  value: 'bottom',
                  options: [
                    {label: '上方', value: 'top'},
                    {label: '右侧', value: 'right'},
                    {label: '下方', value: 'bottom'},
                    {label: '左侧', value: 'left'}
                  ],
                  description: 'spinner文案位置'
                },
                {
                  type: 'input-number',
                  name: 'delay',
                  label: '延迟显示',
                  value: 0,
                  description: '延迟显示的时间（毫秒）'
                },
                {
                  type: 'switch',
                  name: 'overlay',
                  label: '显示遮罩',
                  value: false,
                  description: '是否显示遮罩层'
                }
              ]
            },
            {
              title: '内容区域',
              body: [
                {
                  type: 'button-group-select',
                  name: 'bodyType',
                  label: '内容区域',
                  value: 'custom',
                  options: [
                    {
                      label: '自定义',
                      value: 'custom'
                    },
                    {
                      label: '表达式',
                      value: 'expression'
                    }
                  ],
                  description: '内容区域的渲染方式'
                },
                {
                  type: 'combo',
                  name: 'body',
                  label: '内容配置',
                  visibleOn: 'this.bodyType === "custom"',
                  description: '内容区域内容',
                  items: [
                    {
                      type: 'select',
                      name: 'type',
                      label: '类型',
                      value: 'tpl',
                      options: [
                        {label: '文本', value: 'tpl'},
                        {label: '图片', value: 'image'},
                        {label: '按钮', value: 'button'},
                        {label: '卡片', value: 'card'}
                      ]
                    },
                    {
                      type: 'input-text',
                      name: 'tpl',
                      label: '内容',
                      visibleOn: 'this.type === "tpl"'
                    },
                    {
                      type: 'input-text',
                      name: 'src',
                      label: '图片地址',
                      visibleOn: 'this.type === "image"'
                    },
                    {
                      type: 'input-text',
                      name: 'label',
                      label: '按钮文本',
                      visibleOn: 'this.type === "button"'
                    }
                  ]
                },
                {
                  type: 'input-text',
                  name: 'bodyExpression',
                  label: '表达式',
                  visibleOn: 'this.bodyType === "expression"',
                  description: '通过表达式配置内容'
                }
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '样式设置',
              body: [
                {
                  type: 'input-text',
                  name: 'style.spinnerWrap',
                  label: '容器样式',
                  description: '自定义容器样式'
                },
                {
                  type: 'input-text',
                  name: 'style.spinnerIcon',
                  label: '图标样式',
                  description: '自定义图标样式'
                },
                {
                  type: 'input-color',
                  name: 'style.spinnerColor',
                  label: '图标颜色',
                  description: '自定义图标颜色'
                },
                {
                  type: 'input-color',
                  name: 'style.tipColor',
                  label: '文字颜色',
                  description: '自定义提示文字颜色'
                }
              ]
            }
          ],
          {...context?.schema, configTitle: 'style'}
        )
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(MySpinnerPlugin);

