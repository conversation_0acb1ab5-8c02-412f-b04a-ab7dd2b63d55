import React, { <PERSON> } from 'react';
import './index.scss';
import { Input, Button } from 'amis-ui';
import { Icon } from 'amis';

interface PageReleaseProps {
  pageDataId: string | number;
  pageDataName: string;
  appId: string;
  form: string;
}

const PageRelease: FC<PageReleaseProps> = (props: PageReleaseProps) => {
  // 定义菜单列表
  const menuList = [
    {
      title: '组织内发布',
      path: 'organization'
    },
    {
      title: '公开发布',
      path: 'public'
    }
  ];

  // 状态管理：选中的菜单项
  const [activeItem, setActiveItem] = React.useState(menuList[0]);

  // 处理菜单项点击事件
  const handleMenuClick = (item: any) => {
    setActiveItem(item);
  };

  // 生成访问地址
  const generateVisitUrl = (appId: string, form: string, pageDataId: string | number, pageDataName: string) => {
    const url = `#/app${appId}/submission/${form}?editCode=${pageDataId}&pageName=${encodeURIComponent(pageDataName)}`;
    const baseUrl = window.location.origin + window.location.pathname;
    return baseUrl + url;
  };

  // 复制链接到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        console.log('文本已复制到剪贴板');
      },
      (err) => {
        console.error('无法复制文本: ', err);
      }
    );
  };

  return (
    <div className="pageReleaseBox">
      {/* 侧边栏 */}
      <div className="pageReleaseBox-aside">
        {menuList.map((item) => (
          <div
            key={item.path}
            className={`pageReleaseBox-aside-item ${activeItem.path === item.path ? 'active' : ''}`}
            onClick={() => handleMenuClick(item)}
          >
            {item.title}
          </div>
        ))}
      </div>

      {/* 内容区域 */}
      <div className="pageReleaseBox-content">
        {activeItem.path === 'organization' && (
          <div>
            <h2>对组织内成员发布</h2>
            <p>组织内成员访问该链接需要登录并授权。</p>
            <h3>访问地址</h3>
              <Input value={generateVisitUrl(props.appId, props.form, props.pageDataId, props.pageDataName)} readOnly style={{width: '780px'}}/>
              <Button type="primary" icon={<Icon type="copy" />} style={{marginLeft: '10px'}}
                      onClick={() => copyToClipboard(generateVisitUrl(props.appId, props.form, props.pageDataId, props.pageDataName))}>
                复制链接
              </Button>
              <Button type="default" icon={<Icon type="qrcode" />} style={{marginLeft: '10px'}}>
                生成二维码
              </Button>
              <Button type="default" icon={<Icon type="eye" />} style={{marginLeft: '10px'}}>
                预览
              </Button>
          </div>
        )}

        {activeItem.path === 'public' && (
          <div>
            <h2>公开发布</h2>
            <p>公开发布的链接可以被任何人访问。</p>
            <h3>访问地址</h3>
            <Input value={generateVisitUrl(props.appId, props.form, props.pageDataId, props.pageDataName)} readOnly
                   style={{width: '780px'}} />
            <Button type="primary" icon={<Icon type="copy" />} style={{marginLeft: '10px'}}
                    onClick={() => copyToClipboard(generateVisitUrl(props.appId, props.form, props.pageDataId, props.pageDataName))}>
              复制链接
            </Button>
            <Button type="default" icon={<Icon type="qrcode" />} style={{marginLeft: '10px'}}>
              生成二维码
            </Button>
            <Button type="default" icon={<Icon type="eye" />} style={{marginLeft: '10px'}}>
              预览
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PageRelease;
