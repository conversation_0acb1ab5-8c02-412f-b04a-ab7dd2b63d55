import React, {useState, useEffect} from 'react';
import {setDefaultTheme} from 'amis';
import {IMainStore} from '../../store';
import {darkThemeVars, cxdThemeVars} from './theme-vars';
import './index.scss';

interface ThemeSwitchProps {
  className?: string;
  store?: IMainStore;
  type?: 'button' | 'switch';
}

/**
 * 主题切换组件
 */
const ThemeSwitch: React.FC<ThemeSwitchProps> = ({
  className = '',
  store
}) => {
  // 从localStorage获取主题设置，如果没有则默认使用cxd
  const [theme, setTheme] = useState<string>(
    localStorage.getItem('amis-theme') || 'cxd'
  );

  // 切换主题
  const toggleTheme = () => {
    const newTheme = theme === 'cxd' ? 'dark' : 'cxd';
    setTheme(newTheme);
  };

  // 更新所有带有特定前缀的元素的类名
  const updateElementsWithPrefix = (oldTheme: string, newTheme: string) => {
    // 获取所有元素
    const allElements = document.getElementsByTagName('*');

    // 遍历所有元素
    for (let i = 0; i < allElements.length; i++) {
      const element = allElements[i];
      const classList = element.classList;

      // 遍历元素的所有类名
      for (let j = 0; j < classList.length; j++) {
        const className = classList[j];
        // 如果类名以旧主题为前缀
        if (className.startsWith(`${oldTheme}-`)) {
          // 移除旧主题类名
          classList.remove(className);
          // 添加新主题类名
          classList.add(`${newTheme}-${className.slice(oldTheme.length + 1)}`);
          // 因为classList是实时的，所以我们需要调整索引
          j--;
        }
      }
    }
  };

  // 更新 CSS 变量
  const updateCSSVariables = (theme: string) => {
    const root = document.documentElement;
    const vars = theme === 'dark' ? darkThemeVars : cxdThemeVars;

    Object.entries(vars).forEach(([key, value]) => {
      root.style.setProperty(key, value as string);
    });
  };

  // 当主题发生变化时，更新localStorage和document.body的class
  useEffect(() => {
    const oldTheme = theme === 'cxd' ? 'dark' : 'cxd';

    localStorage.setItem('amis-theme', theme);

    // 移除所有主题相关的class
    document.body.classList.remove('cxd', 'dark');
    document.body.classList.add(theme);

    // 更新 amis 默认主题
    setDefaultTheme(theme);

    // 更新 store 中的主题
    if (store) {
      store.updateTheme(theme);
    }

    // 更新所有主题相关的类名
    updateElementsWithPrefix(oldTheme, theme);

    // 更新 CSS 变量
    updateCSSVariables(theme);

    // 触发自定义事件，通知其他组件主题已变更
    const event = new CustomEvent('themeChange', {detail: {theme}});
    document.dispatchEvent(event);

    // 添加一个小延迟后刷新所有 amis 组件的样式
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
      // 再次检查并更新类名，以防有延迟加载的组件
      updateElementsWithPrefix(oldTheme, theme);
    }, 100);
  }, [theme, store]);

  // 根据类型渲染不同的UI
    return (
      <div className={`theme-switch ${className}`}>
        <label className="switch-wrapper">
          <input
            type="checkbox"
            checked={theme === 'dark'}
            onChange={toggleTheme}
          />
          <span className="slider">
            <span className="mode-text">
              {theme === 'dark' ? '暗黑' : '明亮'}
            </span>
          </span>
        </label>
      </div>
    );
};

export default ThemeSwitch;
