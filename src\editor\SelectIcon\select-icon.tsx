import React from 'react';
import {
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  RendererPluginEvent
} from 'amis-editor-core';
import { getEventControlConfig, getActionCommonProps } from 'amis-editor/lib/renderer/event-control/helper';
import { inputStateTpl } from 'amis-editor/lib/renderer/style-control/helper';

export class SelectIconControlPlugin extends BasePlugin {
  static id = 'SelectIconControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'icon-select';
  $schema = '/schemas/SelectIconControlSchema.json';
  
  // 组件基本信息
  name = '图标选择器';
  panelTitle = '图标选择器';
  icon = 'fa fa-picture-o';
  panelIcon = 'fa fa-picture-o';
  pluginIcon = 'icon-select-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;
  
  // 组件描述信息
  description = '从图标库中选择SVG图标，支持图标分组和搜索';
  docLink = '/amis/zh-CN/components/form/icon-select';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'icon-select',
    label: '选择图标',
    name: 'icon',
    clearable: true,
    placeholder: '请选择图标',
    returnSvg: true,
    static: false,
    // 可以设置默认值
    // value: '<svg>...</svg>' 或 {id: 'svg-icon-id', name: 'icon-name'} 
  };

  // 预览界面
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  // 事件定义
  events: RendererPluginEvent[] = [
    {
      eventName: 'click',
      eventLabel: '点击',
      description: '点击时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            context: {
              type: 'object',
              title: '上下文',
              properties: {
                nativeEvent: {
                  type: 'object',
                  title: '鼠标事件对象'
                }
              }
            }
          }
        }
      ]
    },
    {
      eventName: 'mouseenter',
      eventLabel: '鼠标移入',
      description: '鼠标移入时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            context: {
              type: 'object',
              title: '上下文',
              properties: {
                nativeEvent: {
                  type: 'object',
                  title: '鼠标事件对象'
                }
              }
            }
          }
        }
      ]
    },
    {
      eventName: 'mouseleave',
      eventLabel: '鼠标移出',
      description: '鼠标移出时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            context: {
              type: 'object',
              title: '上下文',
              properties: {
                nativeEvent: {
                  type: 'object',
                  title: '鼠标事件对象'
                }
              }
            }
          }
        }
      ]
    }
  ];


  // 动作定义
  actions = [
    {
      actionType: 'clear',
      actionLabel: '清空',
      description: '清空选中的图标',
      ...getActionCommonProps('clear')
    },
    {
      actionType: 'reset',
      actionLabel: '重置',
      description: '将值重置为初始值',
      ...getActionCommonProps('reset')
    },
    {
      actionType: 'setValue',
      actionLabel: '赋值',
      description: '给组件设置图标值',
      ...getActionCommonProps('setValue')
    }
  ];

  // 面板配置
  panelBodyCreator = (context: any) => {
    console.log(context.schema);
    return getSchemaTpl('tabs', [
      {
        title: '常规',
        body: getSchemaTpl('collapseGroup', [
          {
            title: '基本',
            id: 'properties-basic',
            body: [
              getSchemaTpl('layout:originPosition', {
                value: 'left-top'
              }),
              getSchemaTpl('formItemName', {
                required: true
              }),
              getSchemaTpl('label'),
              {
                type: 'icon-select',
                name: 'value',
                label: '默认图标'+context.schema.static,
                description: '设置组件的默认选中图标',
                returnSvg: true
              },
              getSchemaTpl('clearable'),
              getSchemaTpl('placeholder'),
              getSchemaTpl('labelRemark'),
              getSchemaTpl('remark'),
              getSchemaTpl('description')
            ]
          },
          getSchemaTpl('status', {
            isFormItem: true,
            readonly: true
          })
        ],
        {...context?.schema, configTitle: 'props'})
      },
      // {
      //   title: '外观',
      //   body: getSchemaTpl(
      //     'collapseGroup',
      //     [
      //       getSchemaTpl('theme:formItem'),
      //       getSchemaTpl('theme:form-label'),
      //       getSchemaTpl('theme:form-description'),
      //       {
      //         title: '选择框样式',
      //         body: [
      //           ...inputStateTpl('themeCss.selectControlClassName', '--icon-select-base')
      //         ]
      //       }
      //     ],
      //     {...context?.schema, configTitle: 'style'}
      //   )
      // },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };

  

}

// 注册插件
registerEditorPlugin(SelectIconControlPlugin);