import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {
  getFormDataPage //获取表单数据
} from '@/utils/api/api';
import PageSettings from '@/component/PageSettings/index';

const PageContent: FC<any> = (props: any) => {
    //  选项卡显示的activeKey
    const [activeKey, setActiveKey] = React.useState('preview');

  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };

  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };
  
  // 获取表单数据 pageData
  const [pageData, setPageData] = React.useState<any>({});


  const handleGetFormDataPage = () => {
    let data = {
      applicationPageId: props.pageData.id,
      pageNo: 1,
      pageSize: 10
    };
    getFormDataPage(data).then((res: any) => {
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          if (res.data.list[0]) {
            // toast.success('获取表单数据成功');
            res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
            setPageData(res.data.list[0]);
          }else{
            toast.success('暂无表单数据');
          }
        }else{
          toast.success('暂无表单数据');
        }
      } else {
        toast.error(res.msg);
      }
    });
  };

  const editForm = () => {
    props.history.push(
      `/app${props.computedMatch.params.appId}/design/${props.computedMatch.params.form}?editCode=${props.pageData.id}`
    );
  };
  React.useEffect(() => {
    if (props.pageData?.id) {
      handleGetFormDataPage();
    }
    // 只有在admin模式下才处理tab切换逻辑
    if (props.match.params.playType == 'admin') {
      const urlParams = new URLSearchParams(props.history.location.search);
      let tabKey = urlParams.get('activeTabKey') || 'preview';
      setActiveKey(tabKey);
    }
  }, [props.pageData?.id, props.history.location, props.match.params.playType]);

  return (
    <div className="pageBox">
     {props.match.params.playType == 'admin' && <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-1">
            <Button size="lg" level="primary" onClick={() => editForm()}>
              编辑自定义页面
            </Button>
          </div>
        </div>
      </div>}
      <div className="pageTabsLink">
        {props.match.params.playType == 'admin' ? (
          // admin模式：显示完整的tab标签栏
          <Tabs
            mode="line"
            onSelect={(key: any) => handleTabChange(key)}
            toolbar={TabsToolbar()}
            linksClassName="pageTabsLink-tabsTitle"
            activeKey={activeKey}
          >
            <Tab title="自定义页面预览" eventKey="preview">
              <div className="pageTabsLink-tabsContent">
                <AMISRenderer
                  schema={pageData.data}
                  embedMode={true}
                />
              </div>
            </Tab>
            <Tab title="页面设置" eventKey="setting">
              <PageSettings
                history={props.history}
                pageData={props.pageData}
                store={props.store}
                update={() => {
                  props.updatePage();
                }}
              />
            </Tab>
            <Tab title="页面发布" eventKey="publish"></Tab>
          </Tabs>
        ) : (
          // 非admin模式：只显示自定义页面预览内容，不显示tab标签栏
          <div className="pageTabsLink-tabsContent">
            <AMISRenderer
              schema={pageData.data}
              embedMode={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PageContent;
