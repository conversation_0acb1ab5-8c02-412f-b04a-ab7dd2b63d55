.login-page {
  width: 352px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  box-shadow: var(--boxShadow);
  .page-title {
    font-size: 18px;
    color: var(--text-color);
    margin-bottom: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;

    .back-btn {
      margin-right: 8px;
      padding: 2px 6px;
      font-size: 14px;
      background: transparent;
      border: none;
      color: var(--text-color);
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: var(--primary);
      }
    }
  }

  .login-form {
    .login-methods {
      display: flex;
      align-items: center;
      gap: 16.5px;

      .wx-login-btn {
        flex: 1;
        height: 40px;
        border-width: 1px;
        border-style: solid;
        border-color: var(--colors-neutral-fill-none);
        border-radius: 4px;
        color: var(--text--muted-color);
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--light-bg);
        i {
          margin-right: 4px;
          color: var(--primary);
        }

        &:hover:not(:disabled) {
          border-color: var(--primary);
          color: var(--primary) !important;
        }
      }

      .other-login {
        display: flex;
        gap: 16.5px;

        .login-btn {
          width: 40px;
          height: 40px;
          border-width: 1px;
          border-style: solid;
          border-color: var(--colors-neutral-fill-none);
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--text--muted-color);
          font-size: 16px;
          background: var(--light-bg);

          &:hover:not(:disabled) {
            border-color: var(--primary);
            color: var(--primary) !important;
          }
        }
      }
    }

    .login-divider {
      text-align: center;
      color: var(--text--muted-color);
      font-size: 14px;
      margin: 10px 0;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: calc(50% - 20px);
        height: 0.1px;
        background: var(--borderColor);
      }

      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    .wxlogin-container {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      &-code {
        width: 270px !important;
        height: 270px !important;
        overflow: hidden;
        
        iframe {
          display: block;
          margin: 0 auto;
          width: 270px !important;
          height: 270px !important;
          border: none;
        }
      }

      &-tips {
        margin-top: 16px;
        font-size: 14px;
        color: var(--text-color);
      }
    }

    .login-input {
      margin-bottom: 16px;

      &.code-input {
        display: flex;
        gap: 12px;

        .form-control {
          flex: 1;
        }

        .get-code-btn {
          width: 120px;
          height: 40px;
          border-radius: 4px;
          border-width: 1px;
          border-style: solid;
          border-color: var(--colors-neutral-fill-none);
          color: var(--text--muted-color);
          background: var(--light-bg);

          &:hover:not(:disabled) {
            border-color: var(--primary);
            color: var(--primary);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .form-control {
        width: 100%;
        height: 40px;
        padding: 10px;
        border-width: 1px;
        border-style: solid;
        border-color: var(--colors-neutral-fill-none);
        background: var(--light-bg) !important;
        border-radius: 4px;
        font-size: 14px;
        color: var(--text-color) !important;

        &:hover:not(:disabled) {
          border-color: var(--primary);
          color: var(--primary) !important;
        }

        &::placeholder {
          color: var(--text--muted-color);
        }

        &:focus {
          border-color: var(--primary);
          outline: none;
        }
      }

      .password-input {
        position: relative;

        .form-control {
          width: 100%;
          height: 40px;
          padding: 10px;
          padding-right: 40px;
          border-width: 1px;
          border-style: solid;
          border-color: var(--colors-neutral-fill-none);
          border-radius: 4px;
          font-size: 14px;
          color: var(--text--color);

          &:hover:not(:disabled) {
            border-color: var(--primary);
            color: var(--primary);
          }

          &::placeholder {
            color: var(--text--muted-color);
          }

          &:focus {
            border-color: var(--primary);
            outline: none;
          }
        }

        i {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: var(--text--muted-color);
          cursor: pointer;
          font-size: 16px;

          &:hover {
            color: var(--primary);
          }
        }
      }
    }

    .login-agreement {
      margin: 10px 0 20px;
      font-size: 14px;
      color: var(--text--muted-color);

      .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;

        input[type='checkbox'] {
          margin-right: 8px;
          cursor: pointer;
          width: 16px;
          height: 16px;
        }

        .checkbox-text {
          a {
            color: var(--primary);
            text-decoration: none;
            margin: 0 2px;
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }

    .login-submit {
      width: 100%;
      height: 44px;
      background: var(--primary);
      border: none;
      border-radius: 4px;
      color: var(--button-primary-default-font-color);
      font-size: 14px;
      margin: 16px 0;
    }

    .login-links {
      text-align: center;
      margin-top: 20px;

      .forget-password {
        display: block;
        color: var(--primary);
        font-size: 14px;
        text-decoration: none;
        margin-bottom: 8px;
      }

      .other-logins {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        color: var(--primary);
        font-size: 14px;

        .divider {
          width: 1px;
          height: 12px;
          background: var(--borderColor);
        }

        a {
          color: var(--primary);
          text-decoration: none;
        }
      }
    }

    .register-link {
      display: block;
      text-align: center;
      font-size: 14px;
      color: var(--text--muted-color);
      text-decoration: none;
      margin-top: 8px;

      a {
        color: var(--primary);
        text-decoration: none;
      }
    }
  }
}

// 组织选择Modal样式 - 参考登录页面样式
.tenant-selection-modal {
  background: var(--background);
  z-index: 1000 !important;

  .cxd-Modal-overlay,
  .dark-Modal-overlay {
    background-color: transparent !important; // 透明背景遮罩
  }

  .cxd-Modal-content,
  .dark-Modal-content {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 400px !important;
    max-width: 90vw !important;
    background-color: var(--background) !important;
    border-radius: 0 !important; // 去掉圆角，与登录页面保持一致
    border: none !important;
    box-shadow: var(--boxShadow) !important; // 使用与登录页面相同的阴影
    margin: 0 !important;
  }

  .tenant-selection-content {
    padding: 20px; // 与登录页面相同的内边距

    .tenant-selection-title {
      font-size: 18px; // 与登录页面标题相同大小
      font-weight: bold; // 与登录页面标题相同粗细
      color: var(--text-color);
      margin-bottom: 18px; // 与登录页面相同间距
      text-align: left;
    }

    .tenant-selection-list {
      max-height: 320px; // 固定高度
      overflow-y: auto; // 可滚动

      // 隐藏滚动条
      &::-webkit-scrollbar {
        display: none;
      }
      scrollbar-width: none;
      -ms-overflow-style: none;

      .tenant-selection-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        margin-bottom: 8px;
        border: 1px solid #f0f0f0;

    

        .tenant-selection-logo {
          width: 40px;
          height: 40px;
          margin-right: 12px;
          border-radius: 6px;
          overflow: hidden;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .tenant-selection-logo-placeholder {
            width: 100%;
            height: 100%;
            background: #74a9ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
          }
        }

        .tenant-selection-info {
          flex: 1;

          .tenant-selection-name {
            font-size: 14px;
            font-weight: 400;
            color: var(--text-color);
            line-height: 1.4;
          }
        }

        .tenant-selection-right {
          display: flex;
          align-items: center;
          gap: 8px;

          .last-login-badge {
            background: #e6f7ff;
            color: #1890ff;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            white-space: nowrap;
          }

          .tenant-selection-arrow {
            color: #bfbfbf;
            font-size: 12px;
          }
        }

        &:hover .tenant-selection-arrow {
          color: #666;
        }
      }
    }
  }
}

