import React, { useState, useMemo } from 'react';
import {Icon} from 'amis-editor';
import './index.scss';
import { toast } from 'amis';

const iconGroups = {
  '功能类组件': [
    'audio-plugin',
    'custom-plugin',
    'each-plugin',
    'form-plugin',
    'nav-plugin',
    'qrcode-plugin',
    'service-plugin', 
    'table-plugin',
    'tasks-plugin',
    'video-plugin',
    'wizard-plugin'
  ],
  '容器类组件': [
    'anchor-nav-plugin',
    'collapse-plugin',
    'flex-container-plugin',
    'container-plugin',
    'switch-container-plugin',
    'form-group-plugin',
    'panel-plugin',
    'grid-plugin',
    'iframe-plugin',
    'table-view-plugin',
    'tabs-plugin',
    'web-component-plugin',
    'tooltip-wrapper-plugin'
  ],
  '按钮类组件': [
    'btn-group-plugin',
    'btn-select-plugin',
    'btn-toolbar-plugin', 
    'button-plugin',
    'dropdown-btn-plugin'
  ],
  '展示类组件': [
    'breadcrumb-plugin',
    'card-plugin',
    'cards-plugin',
    'carousel-plugin',
    'chart-plugin',
    'code-plugin',
    'date-plugin',
    'datetime-plugin',
    'time-plugin',
    'image-plugin',
    'images-plugin',
    'list-plugin',
    'log-plugin',
    'mapping-plugin',
    'sparkline-plugin',
    'avatar-plugin',
    'progress-plugin',
    'json-view-plugin',
    'markdown-plugin',
    'steps-plugin',
    'status-plugin',
    'plain-plugin',
    'url-plugin'
  ],
  '表单类组件': [
    'chained-select-plugin',
    'checkbox-plugin',
    'checkboxes-plugin',
    'combo-plugin',
    'condition-builder-plugin',
    'diff-editor-plugin',
    'editor-plugin',
    'hidden-plugin',
    'input-city-plugin',
    'input-color-plugin',
    'input-date-range-plugin',
    'input-date-plugin',
    'input-datetime-plugin',
    'input-email-plugin',
    'input-excel-plugin',
    'input-file-plugin',
    'input-group-plugin',
    'input-image-plugin',
    'input-kv-plugin',
    'input-number-plugin',
    'input-password-plugin',
    'input-range-plugin',
    'input-rating-plugin',
    'input-repeat-plugin',
    'input-rich-text-plugin',
    'input-tag-plugin',
    'input-text-plugin',
    'input-time-plugin',
    'input-tree-plugin',
    'input-url-plugin',
    'input-year-plugin',
    'list-select-plugin',
    'location-picker-plugin',
    'matrix-checkboxes-plugin',
    'month-plugin',
    'nested-select-plugin',
    'picker-plugin',
    'quarter-plugin',
    'radios-plugin',
    'select-plugin',
    'static-plugin',
    'sub-form-plugin',
    'switch-plugin',
    'tabs-transfer-plugin',
    'textarea-plugin',
    'transfer-plugin',
    'tree-select-plugin',
    'uuid-plugin',
    'formula-plugin'
  ]
};

const ShowAmisIcons = () => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(Object.keys(iconGroups)));
  const [searchText, setSearchText] = useState('');

  const filteredIconGroups = useMemo(() => {
    if (!searchText) return iconGroups;
    
    const result: {[key: string]: string[]} = {};
    Object.entries(iconGroups).forEach(([groupName, icons]) => {
      const filteredIcons = icons.filter(icon => 
        icon.toLowerCase().includes(searchText.toLowerCase())
      );
      if (filteredIcons.length > 0) {
        result[groupName] = filteredIcons;
      }
    });
    return result;
  }, [searchText]);

  const handleIconClick = (iconName: string) => {
    navigator.clipboard.writeText(iconName).then(() => {
      toast.success(`已复制图标名称: ${iconName}`);
    }).catch(err => {
      toast.error('复制失败，请重试');
    });
  };

  const toggleGroup = (groupName: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupName)) {
        newSet.delete(groupName);
      } else {
        newSet.add(groupName);
      }
      return newSet;
    });
  };

  return (
    <div className="amis-icons-showcase">
      <h1 className="page-title">AMIS 编辑器图标库</h1>
      
      <div className="search-container">
        <input
          type="text"
          placeholder="搜索图标..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className="search-input"
        />
      </div>

      {Object.entries(filteredIconGroups).map(([groupName, icons]) => (
        <div key={groupName} className="icon-group">
          <div 
            className="group-header" 
            onClick={() => toggleGroup(groupName)}
          >
            <h2 className="group-title">
              <span className={`expand-icon ${expandedGroups.has(groupName) ? 'expanded' : ''}`}>
                <Icon icon="right-arrow" className="amis-icon" />
              </span>
              {groupName} ({icons.length})
            </h2>
          </div>
          
          <div className={`icon-grid-container ${expandedGroups.has(groupName) ? 'expanded' : ''}`}>
            <div className="icon-grid">
              {icons.map(iconName => (
                <div 
                  key={iconName} 
                  className="icon-item" 
                  onClick={() => handleIconClick(iconName)}
                  title="点击复制图标名称"
                >
                  <div className="icon-display">
                    <Icon icon={iconName} className="amis-icon" />
                  </div>
                  <div className="icon-name">{iconName}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ShowAmisIcons;
