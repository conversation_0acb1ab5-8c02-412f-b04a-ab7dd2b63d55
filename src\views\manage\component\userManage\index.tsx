import React, {FC, useContext, useState, useEffect} from 'react';
import {observer} from 'mobx-react';
import AMISRenderer from '@/component/AMISRenderer';
import {IMainStore} from '@/store';
import {MobXProviderContext} from 'mobx-react';
import {getTreeDeptList} from '@/utils/api/api';

import './index.scss';

const UserManage: FC<any> = () => {
  function useStore(): IMainStore {
    const {store} = useContext(MobXProviderContext);
    return store as IMainStore;
  }

  const store = useStore();
  const [selectedDeptId, setSelectedDeptId] = useState<string>('');
  const [userCrudRef, setUserCrudRef] = useState<any>(null);
  const [departments, setDepartments] = useState<any[]>([]);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [amis<PERSON><PERSON>, setAmisKey] = useState(0);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // 处理部门数据，添加层级和子节点信息
  const processDepts = (depts: any[], level = 0): any[] => {
    return depts.map(dept => ({
      id: dept.id,
      name: dept.name,
      level: level,
      children: dept.children || [],
      hasChildren: dept.children && dept.children.length > 0,
      parentId: dept.parentId
    }));
  };

  // 递归获取所有可见的部门节点
  const getVisibleDepts = (depts: any[], level = 0): any[] => {
    let result: any[] = [];
    depts.forEach(dept => {
      const processedDept = {
        id: dept.id,
        name: dept.name,
        level: level,
        children: dept.children || [],
        hasChildren: dept.children && dept.children.length > 0,
        parentId: dept.parentId
      };
      result.push(processedDept);

      // 如果节点展开且有子节点，递归添加子节点
      if (
        expandedNodes.has(dept.id) &&
        dept.children &&
        dept.children.length > 0
      ) {
        result = result.concat(getVisibleDepts(dept.children, level + 1));
      }
    });
    return result;
  };

  // 获取部门列表
  const fetchDepartments = async () => {
    try {
      const result = await getTreeDeptList();
      if (result.code === 0 && result.data) {
        // 保存原始树形数据
        const treeData = [
          {
            id: '',
            name: '全部人员',
            level: 0,
            children: [],
            hasChildren: false,
            parentId: null
          },
          ...processDepts(result.data)
        ];
        setDepartments(treeData);

        // 默认展开第一级节点
        const firstLevelIds = result.data.map((dept: any) => dept.id);
        setExpandedNodes(new Set(firstLevelIds));
      }
    } catch (error) {
      console.error('获取部门列表失败：', error);
    }
  };

  // 处理部门选择
  const handleDeptSelect = (deptId: string) => {
    setSelectedDeptId(deptId);
    setAmisKey(Date.now()); // 强制刷新AMIS渲染器
  };

  // 处理节点展开/收起
  const handleNodeToggle = (deptId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发部门选择
    const newExpandedNodes = new Set(expandedNodes);
    if (expandedNodes.has(deptId)) {
      newExpandedNodes.delete(deptId);
    } else {
      newExpandedNodes.add(deptId);
    }
    setExpandedNodes(newExpandedNodes);
  };

  // 获取可见的部门列表（考虑展开状态和搜索）
  const getFilteredVisibleDepts = () => {
    if (searchKeyword) {
      // 搜索模式：显示所有匹配的部门，忽略展开状态
      const allDepts: any[] = [];
      const collectAllDepts = (depts: any[], level = 0) => {
        depts.forEach(dept => {
          allDepts.push({...dept, level});
          if (dept.children && dept.children.length > 0) {
            collectAllDepts(dept.children, level + 1);
          }
        });
      };
      collectAllDepts(departments);
      return allDepts.filter(dept =>
        dept.name.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    } else {
      // 正常模式：根据展开状态显示部门
      return getVisibleDepts(departments);
    }
  };

  const visibleDepartments = getFilteredVisibleDepts();

  useEffect(() => {
    fetchDepartments();
  }, []);

  // 用户列表Schema
  const userManageSchema = {
    type: 'page',
    body: [
      {
        type: 'crud',
        autoGenerateFilter: {
          columnsNum: 2,
          showBtnToolbar: false
        },
        id: 'user-crud',
        api: {
          method: 'GET',
          url: '/admin-api/system/user/pageNew',
          data: {
            pageNo: '${page}',
            pageSize: '${perPage}',
            deptId: '${selectedDeptId}',
            username: '${username}',
            mobile: '${mobile}',
            status: '${status}'
          },
          // 过滤掉没有值的参数
          requestAdaptor: function (api: any) {
            const data = {...api.data};
            Object.keys(data).forEach(key => {
              if (
                data[key] === undefined ||
                data[key] === '' ||
                data[key] === null
              ) {
                delete data[key];
              }
            });
            api.data = data;
            return api;
          },
          adaptor: (payload: any) => {
            if (payload.code === 0 && payload.data) {
              // 获取创建者ID，如果API没有直接提供isCreator字段
              const creatorId = store.userInfo.companyId; // 假设当前用户ID就是创建者ID

              return {
                total: payload.data.total,
                items: payload.data.list.map((item: any) => ({
                  ...item,
                  // 假设userId为1的是创建者，或者根据其他规则判断
                  isCreator: item.id == creatorId
                }))
              };
            }
            return {
              total: 0,
              items: []
            };
          }
        },
        headerToolbar: [
          {
            type: 'button',
            label: '添加成员',
            icon: 'fa fa-plus',
            level: 'primary',
            actionType: 'drawer',
            drawer: {
              title: '添加成员',
              size: 'md',
              position: 'right',
              body: {
                type: 'form',
                api: {
                  url: '/admin-api/system/user/create',
                  method: 'post',
                  adaptor: `
                  const event = new CustomEvent('tenantSwitch',{});
                window.dispatchEvent(event);
                  return payload;
                `
                },
                body: [
                  {
                    type: 'input-text',
                    name: 'username',
                    label: '用户名',
                    required: true,
                    maxLength: 16,
                    minLength: 4,
                    placeholder: '请输入',
                    validations: {
                      matchRegexp: '^[a-zA-Z0-9]{4,16}$'
                    },
                    validationErrors: {
                      matchRegexp: '用户名必须由字母和数字组成，长度为4-16位'
                    }
                  },
                  {
                    type: 'input-text',
                    name: 'nickname',
                    label: '昵称',
                    maxLength: 12,
                    required: true,
                    placeholder: '请输入'
                  },
                  {
                    type: 'department-select',
                    name: 'deptId',
                    label: '部门',
                    required: true,
                    labelField: 'name',
                    valueField: 'id',
                    placeholder: '请选择'
                  },
                  {
                    type: 'pos-select',
                    name: 'postIds',
                    label: '岗位',
                    required: false,
                    labelField: 'name',
                    valueField: 'id',
                    placeholder: '请选择岗位',
                    source: {
                      method: 'GET',
                      url: '/admin-api/system/post/page?pageNo=1&pageSize=100',
                      adaptor: function(payload: any) {
                        if (payload.code === 0 && payload.data) {
                          // 只保留状态为0（启用）的岗位数据
                          const filteredList = payload.data.list.filter((item: any) => item.status === 0);
                          return {
                            ...payload,
                            data: filteredList
                          };
                        }
                        return {
                          ...payload,
                          data: []
                        };
                      }
                    }
                  },
                  {
                    type: 'input-phone',
                    name: 'mobile',
                    label: '手机号',
                    required: true,
                    maxLength: 11,
                    placeholder: '请输入手机号'
                  },
                  {
                    type: 'input-password',
                    name: 'password',
                    label: '密码',
                    maxLength: 16,
                    minLength: 6,
                    required: true,
                    placeholder: '请输入',
                    validations: {
                      minLength: 6,
                      maxLength: 16
                    },
                    validationErrors: {
                      minLength: '密码长度不能少于6位',
                      maxLength: '密码长度不能超过16位'
                    }
                  },
                  {
                    type: 'switch',
                    name: 'status',
                    label: '账号状态',
                    value: 0,
                    trueValue: 0,
                    falseValue: 1
                  }
                ]
              }
            }
          },
          {
            type: 'button',
            label: '邀请成员',
            icon: 'fa fa-user-plus',
            level: 'default',
            actionType: 'dialog',
            dialog: {
              title: '邀请成员',
              body: {
                type: 'form',
                api: {
                  url: '/admin-api/system/user-tenant/pull-user-to-tenant',
                  method: 'post',
                  data: {
                    userId: '${userSearchResult.userId}',
                    tenantId: store.tenant_id
                  },
                  
                  // requestAdaptor: function (api: any) {
                  //   delete api.data.userSearchResult;
                  //   delete api.data.mobile;
                  //   return api;
                  // }
                  adaptor: `
                  const event = new CustomEvent('tenantSwitch',{});
                window.dispatchEvent(event);
                  return payload;
                `
                },
                body: [
                  {
                    type: 'input-text',
                    name: 'mobile',
                    label: false,
                    required: true,
                    clearable: true,
                    maxLength: 11,
                    placeholder: '请输入成员的手机号',
                    onEvent: {
                      change: {
                        actions: [
                          {
                            actionType: 'ajax',
                            args: {
                              api: {
                                url: '/admin-api/system/user-tenant/get-by-mobile?mobile=${mobile}',
                                method: 'get',
                                adaptor: function (payload: any) {
                                  if (payload.data && payload.data.tenantIds) {
                                    const tenantIds = payload.data.tenantIds.split(',');
                                    payload.data.isAdded = tenantIds.indexOf(store.tenant_id+"") !== -1;
                                    payload.data.addedStatus = payload.data.isAdded ? '已添加' : '';
                                  }
                                  return payload;
                                }
                              },
                              messages: {
                                success: '',
                                failed: ''
                              }
                            }
                          },
                          {
                            actionType: 'setValue',
                            args: {
                              value: '${event.data.responseResult}'
                            },
                            componentId: 'userSearchResult'
                          }
                        ],
                        debounce: 200
                      }
                    }
                  },
                  {
                    type: 'hidden',
                    name: 'userSearchResult',
                    id: 'userSearchResult',
                  },
                  {
                    type: 'container',
                    visibleOn: '${userSearchResult && userSearchResult.username}',
                    body: [
                      {
                        type: 'tpl',
                        tpl: '<div class="user-info-card"><div class="avatar"><img src="${userSearchResult.avatar || "/default-avatar.png"}" alt="头像" /></div><div class="info"><div class="name">${userSearchResult.username}</div><div class="mobile">${mobile}</div></div><div class="status">${userSearchResult.addedStatus ? "<span class=\\"added\\">" + userSearchResult.addedStatus + "</span>" : ""}</div></div>'
                      }
                    ]
                  },
                  {
                    type: 'container',
                    visibleOn: '${mobile && mobile.length >= 11 && userSearchResult && !userSearchResult.username}',
                    body: [
                      {
                        type: 'tpl',
                        tpl: '<div style="color: #999; font-size: 12px; margin-top: 8px;">未找到该手机号对应的用户</div>'
                      }
                    ]
                  }
                ]
              }
            }
          }
        ],
        columns: [
          {
            name: 'index',
            label: '序号',
            type: 'tpl',
            tpl: '${(page - 1) * perPage + index + 1}',
            width: 60
          },
          {
            name: 'id',
            label: '账号ID',
            width: 80
          },
          {
            name: 'username',
            label: '用户名',
            width: 120,
            searchable: {
              type: 'input-text',
              name: 'username',
              label: '用户名',
              placeholder: '输入用户名'
            }
          },
          {
            name: 'nickname',
            label: '昵称',
            width: 120
          },
          {
            name: 'mobile',
            label: '手机号',
            width: 120,
            searchable: {
              type: 'input-text',
              name: 'mobile',
              label: '手机号',
              placeholder: '输入手机号'
            }
          },
          // {
          //   name: 'postIds',
          //   label: '岗位',
          //   width: 120,
          //   type: 'pos-select',
          //   static: true,
          // },
          {
            name: 'status',
            label: '账号状态',
            type: 'mapping',
            width: 100,
            map: {
              '0': '<span class="badge-status badge-success">已启用</span>',
              '1': '<span class="badge-status badge-danger">已停用</span>'
            },
            searchable: {
              type: 'select',
              name: 'status',
              label: '账号状态',
              placeholder: '请选择账号状态',
              options: [
                {label: '已启用', value: 0},
                {label: '已停用', value: 1}
              ]
            }
          },
          {
            name: 'creator',
            label: '创建人',
            width: 100
          },
          {
            name: 'createTime',
            label: '创建时间',
            type: 'input-datetime',
            static: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            width: 160
          },
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                type: 'dropdown-button',
                icon: 'fa fa-ellipsis-h',
                hideCaret: true,
                visibleOn: '${!isCreator}', // 如果是组织创建者则不显示操作按钮
                buttons: [
                  {
                    type: 'button',
                    label: '编辑账号',
                    actionType: 'drawer',
                    drawer: {
                      title: '编辑账号',
                      size: 'md',
                      position: 'right',
                      body: {
                        type: 'form',
                        api: {
                          url: '/admin-api/system/user/update',
                          method: 'put',
                          requestAdaptor: function (api: any) {
                            // 只传递需要的字段
                            const { id, username, nickname, deptId, postIds, mobile, status } = api.data;
                            api.data = {
                              id,
                              username,
                              nickname,
                              deptId,
                              postIds,
                              mobile,
                              status
                            };
                            return api;
                          }
                        },
                        initApi: {
                          url: '/admin-api/system/user/get?id=${id}',
                          method: 'get',
                          adaptor: function (payload: any) {
                            if (payload.code === 0 && payload.data) {
                              return {
                                status: 0,
                                msg: '',
                                data: payload.data
                              };
                            }
                            return payload;
                          }
                        },
                        body: [
                          {
                            type: 'hidden',
                            name: 'id',
                            value: '${id}'
                          },
                          {
                            type: 'input-text',
                            name: 'username',
                            label: '用户名',
                            required: true,
                            maxLength: 16,
                            minLength: 4,
                            placeholder: '请输入',
                            validations: {
                              matchRegexp: '^[a-zA-Z0-9]{4,16}$'
                            },
                            validationErrors: {
                              matchRegexp: '用户名必须由字母和数字组成，长度为4-16位'
                            }
                          },
                          {
                            type: 'input-text',
                            name: 'nickname',
                            label: '昵称',
                            maxLength: 12,
                            required: true,
                            placeholder: '请输入'
                          },
                          {
                            type: 'department-select',
                            name: 'deptId',
                            label: '部门',
                            required: true,
                            labelField: 'name',
                            valueField: 'id',
                            placeholder: '请选择'
                          },
                          {
                            type: 'pos-select',
                            name: 'postIds',
                            label: '岗位',
                            required: false,
                            labelField: 'name',
                            valueField: 'id',
                            placeholder: '请选择岗位',
                            source: {
                              method: 'GET',
                              url: '/admin-api/system/post/page?pageNo=1&pageSize=100',
                              adaptor: function(payload: any) {
                                if (payload.code === 0 && payload.data) {
                                  // 只保留状态为0（启用）的岗位数据
                                  const filteredList = payload.data.list.filter((item: any) => item.status === 0);
                                  return {
                                    ...payload,
                                    data: filteredList
                                  };
                                }
                                return {
                                  ...payload,
                                  data: []
                                };
                              }
                            }
                          },
                          {
                            type: 'input-phone',
                            name: 'mobile',
                            label: '手机号',
                            required: true,
                            maxLength: 11,
                            placeholder: '请输入手机号'
                          },
                          {
                            type: 'switch',
                            name: 'status',
                            label: '账号状态',
                            trueValue: 0,
                            falseValue: 1
                          }
                        ]
                      }
                    }
                  },
                  {
                    type: 'button',
                    label: "${status == 0 ? '停用账号' : '启用账号'}",
                    confirmTitle:
                      "${status == 0 ? '确定要停用吗？' : '确定要启用吗？'}",
                    confirmText:
                      "${status == 0 ? '停用后，该账号将不能访问本组织，确定要停用吗？' : '启用后，该账号可以访问本组织，确定要启用吗？'}",
                    actionType: 'ajax',
                    api: {
                      method: 'PUT',
                      url: '/admin-api/system/user/update-status',
                      data: {
                        id: '${id}',
                        status: "${status == 0 ? '1' : '0'}",
                      }
                    }
                  },
                  {
                    type: 'button',
                    label: '移除成员',
                    className: 'text-danger',
                    confirmTitle: '确定要移除吗？',
                    confirmText:
                      '移除后，该账号将不能访问本组织，确定要移除吗？',
                    actionType: 'ajax',
                    api: {
                      method: 'post',
                      url: '/admin-api/system/user-tenant/kick-out-user',
                      data: {
                        userId: '${id}',
                        tenantId: store.tenant_id
                      }
                    }
                  }
                ]
              }
            ]
          }
        ],
        bulkActions: [],
        itemActions: [],
        footerToolbar: ['statistics', 'pagination', 'switch-per-page'],
        syncLocation: false,
        affixHeader: true,
        initFetch: true,
        className: 'user-crud-table'
      }
    ]
  };

  return (
    <div className="user-manage-container">
      <div className="page-header">
        <div className="title">人员管理</div>
        <div className="subtitle">
          统筹组织全部人员信息、账号状态、保障人员管理规范有序 。
        </div>
      </div>

      <div className="user-manage-layout">
        {/* 左侧部门列表 */}
        <div className="dept-sidebar">
          <div className="sidebar-title">部门列表</div>
          <div className="dept-search">
            <input
              type="text"
              placeholder="搜索部门"
              value={searchKeyword}
              onChange={e => setSearchKeyword(e.target.value)}
              className="dept-search-input"
            />
            <span
              className={`search-icon ${searchKeyword ? 'clear' : 'search'}`}
              onClick={() => searchKeyword && setSearchKeyword('')}
            >
              {searchKeyword ? <i className="fa fa-times"></i> : <i className="fa fa-search"></i>}
            </span>
          </div>
          <div className="dept-list">
            {visibleDepartments.map(dept => (
              <div
                key={dept.id}
                className={`dept-item level-${dept.level} ${
                  selectedDeptId === dept.id ? 'active' : ''
                }`}
                onClick={() => handleDeptSelect(dept.id)}
              >
                {dept.hasChildren && (
                  <span
                    className={`expand-icon ${
                      expandedNodes.has(dept.id) ? 'expanded' : 'collapsed'
                    }`}
                    onClick={e => handleNodeToggle(dept.id, e)}
                  >
                    ▶
                  </span>
                )}
                {!dept.hasChildren && (
                  <span className="expand-placeholder"></span>
                )}
                <span className="dept-name">{dept.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧用户列表 */}
        <div className="user-content">
          <AMISRenderer
            key={amisKey}
            schema={userManageSchema}
            data={{
              selectedDeptId: selectedDeptId
            }}
            onAction={(type: string, data: any) => {
              console.log(type, data);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default observer(UserManage);
