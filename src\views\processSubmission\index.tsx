import React, {FC, useEffect, useState, useRef, RefObject} from 'react';
import {observer, inject} from 'mobx-react';
import {withRouter, RouteComponentProps} from 'react-router-dom';
import AMISRenderer from '@/component/AMISRenderer';
import {toast, Layout, Button, Icon, Avatar, Drawer, Tabs, Tab} from 'amis';
import {findCreateBody} from '@/utils/schemaDataSet/commonEditor';
import {IMainStore} from '@/store';
import './index.scss';
import itemShow from '../workbench/component/ProjectMain/image/itemShow.png';
import {
  getFormDataPage,
  startProcess,
  getModelById,
  wflowApproveCodeGet,
  amisCodeGet
} from '@/utils/api/api';
import {DeleteSubmissionConfirm} from '@/utils/schemaDataSet/DeleteSubmissionConfirm';
import {ApprovalDialog} from '@/component/ApprovalDialog';

// 定义路由参数接口
interface RouteParams {
  app: string;
  form: string;
}

// 提交页面组件 - 用于展示提交的表单数据
interface ProcessSubmissionPageProps extends RouteComponentProps<RouteParams> {
  store?: IMainStore;
}

// 页面显示模式枚举
enum PageMode {
  FORM = 'form', // 表单填写模式
  SUCCESS = 'success', // 提交成功模式
  DETAIL = 'detail' // 详情查看模式
}

const ProcessSubmissionPage: FC<ProcessSubmissionPageProps> = inject('store')(
  observer((props: ProcessSubmissionPageProps) => {
    const {store, location, history} = props;
    const [schema, setSchema] = useState<any>(null);
    const [pageData, setPageData] = useState<any>(null);
    const [pageMode, setPageMode] = useState<PageMode>(PageMode.FORM); // 使用页面模式替代isSubmitted
    const [submittedId, setSubmittedId] = useState<string | number>(''); // 添加提交后的ID
    const [submittedTime, setSubmittedTime] = useState(''); // 不用 useRef
    const formRef = useRef<any>(null);
    const [activeTab, setActiveTab] = useState('comment');
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState<any>({});
    const forms = useRef<any>([]);
    const form = useRef<any>(null);
    const instanceId = useRef<any>(null);
    const [showApprovalDialog, setShowApprovalDialog] = React.useState(false);
    const [formDataToSubmit, setFormDataToSubmit] = React.useState<any>(null);
    // wflow_code
    const [wflow, setWflow] = React.useState<any>(false);
    // 页面id
    const urlParams = new URLSearchParams(location.search);
    const editCode: any = urlParams.get('editCode');
    const wflowCode: any = urlParams.get('wflowCode');
    const pageName: any = urlParams.get('pageName');
    let edit_id = parseInt(editCode, 10);

    // 获取审批流程信息
    const getProcessData = async () => {
      let json = await amisCodeGet({applicationPageId: edit_id});
      console.log('getProcessData', json);
      let data = {
        applicationPageId: edit_id
      };
      wflowApproveCodeGet(data)
        .then((res: any) => {
          console.log('getProcessData res', res);
          if (res.code == 0) {
            if (res.data) {
              console.log('getProcessData res.data', res.data);
              setWflow(res.data);

              // loadFormInfo(res.data.wflowProcessId);

              // if (res.data.wflowProcessId) {
              //   setWflowData(JSON.parse(res.data.wflow_code));
              // }
            }
          } else {
            console.log('getProcessData err', res.msg);
          }
        })
        .catch((err: any) => {
          console.log('getProcessData err', err);
        });
    };

    // 获取页面的信息
    const getRuteDetails = async () => {
      let data = {
        applicationPageId: edit_id,
        pageNo: 1,
        pageSize: 10
      };
      getFormDataPage(data)
        .then(res => {
          if (res.code === 0) {
            if (res.data.list.length > 0) {
              if (res.data.list[0]) {
                res.data.list[0].data = window.JSON.parse(
                  res.data.list[0].data
                );
                let form_data = res.data.list[0];
                setPageData(form_data);
                // let value = findCreateBody(form_data.data);
                // let data = {
                //   type: 'page',
                //   regions: ['body'],
                //   pullRefresh: {
                //     disabled: true
                //   },
                //   body: value
                // };

                forms.current = findCreateBody(res.data.list[0].data);
                console.log(
                  'data=====>',
                  res.data.list[0].data.body[0].columns
                );
                // setSchema(data);
                setSchema(
                  createCustompageObj(findCreateBody(res.data.list[0].data))
                );
                console.log(findCreateBody(res.data.list[0].data));
                //          setSchema(
                //   formPreviewSchema(
                //     res.data.list[0].data,
                //     edit_id,
                //     pageName
                //   )
                // );
              } else {
                toast.success('暂无表单数据');
              }
            } else {
              toast.success('暂无表单数据');
            }
          }
        })
        .catch(err => {
          toast.error(err, 'error');
        });
    };

    // 表单预览的schema处理函数
    const formPreviewSchema = (
      schema: any,
      applicationPageId: any,
      label: any
    ) => {
      let formBody;
      try {
        formBody = findCreateBody(schema);
      } catch (e) {}
      console.log('tableName=====>', formBody);

      return {
        type: 'page',
        body: [
          {
            type: 'form',
            title: `${label}`,
            api: {
              method: 'post',
              url: `/admin-api/system/form-field-value/create/${applicationPageId}`,
              adaptor: function (
                payload: any,
                response: any,
                api: any,
                context: any
              ) {
                console.log(response); // 打印上下文数据
                console.log(payload); // 打印上下文数据
                console.log(payload.data != null); // 打印上下文数据

                if (payload.data != null) {
                  setSubmittedId(payload.data);
                  return {
                    ...payload
                  };
                }

                // setSubmittedId(payload.data);
              }
            },
            body: formBody || [],
            actions: [
              // {
              //   type: 'button',
              //   label: '取消',
              //   actionType: 'cancel'
              // },
              {
                type: 'button',
                label: '提交',
                level: 'primary',
                actionType: 'submit'
              }
            ],
            onEvent: {
              submitSucc: {
                actions: [
                  {
                    actionType: 'custom',
                    script: () => {
                      setPageMode(PageMode.SUCCESS); // 设置为成功模式
                    }
                  }
                ]
              }
            }
          }
        ]
      };
    };

    // 添加处理编辑和保存的函数
    const handleEdit = () => {
      setIsEditing(true);
    };

    // 修改详情页面的 schema 处理函数
    const showDetailSchema = (tableName: any) => {
      let formBody;
      try {
        formBody = findCreateBody(pageData.data);
      } catch (e) {}

      const staticFields = formBody
        ? formBody.map((field: any) => ({
            ...field,
            type: isEditing ? field.type : 'static' // 编辑状态使用原始类型，否则使用静态显示
          }))
        : [];

      return {
        type: 'page',
        body: [
          {
            type: 'form',
            initApi: {
              method: 'get',
              // url: `/apiTest/${tableName}/${submittedId}`,
              url: `/admin-api/system/form-field-value/get/${edit_id}/${submittedId}`,
              adaptor: function (payload: any) {
                setSubmittedTime(payload.data.createTime);
                return {
                  ...payload
                };
              }
            },
            api: isEditing
              ? {
                  method: 'put',
                  // url: `/apiTest/${tableName}/${submittedId}`
                  url: `/admin-api/system/form-field-value/batch-update/${edit_id}/${submittedId}`
                }
              : undefined,
            body: staticFields,
            labelAlign: 'top',
            title: '',
            mode: 'horizontal',
            labelWidth: 120,
            actions: isEditing
              ? [
                  {
                    type: 'button',
                    label: '取消',
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'custom',
                            script: () => {
                              setIsEditing(false);
                            }
                          }
                        ]
                      }
                    },
                    level: 'default'
                  },
                  {
                    type: 'button',
                    label: '保存',
                    level: 'primary',
                    actionType: 'submit'
                  }
                ]
              : [],
            onEvent: {
              submitSucc: {
                actions: [
                  {
                    actionType: 'custom',
                    script: () => {
                      setIsEditing(false);
                    }
                  }
                ]
              }
            },
            static: !isEditing
          }
        ]
      };
    };

    // 显示详情页面
    const showDetail = () => {
      // setPageMode(PageMode.DETAIL); // 切换到详情模式
      let a = document.createElement('a');
      a.href = `${
        process.env.NODE_ENV === 'development'
          ? 'http://localhost:88/#/workspace/ProcessInstancePreview'
          : 'https://javaddm.fjpipixia.com/wflow/#/workspace/ProcessInstancePreview'
      }?instanceId=${instanceId.current}&nodeId=''&userID=${
        store?.userInfo.id
      }&tenantId=${store?.tenant_id}&authorization=${store?.access_token}`;
      window.open(a.href, '_blank');
    };

    // 返回到上一页
    const goBack = () => {
      setPageMode(PageMode.FORM);
    };

    // 添加新方法，扫描表单项目，建立 name 到 id 的映射
    const scanFormItems = (items: any, nameToIdMap: any) => {
      if (!items || !Array.isArray(items)) return;

      items.forEach(item => {
        // 如果有 name 和 id 属性，建立映射
        if (item.name && item.id) {
          nameToIdMap[item.name] = item.id;
        }

        // 递归处理嵌套项目
        if (item.body) {
          scanFormItems(item.body, nameToIdMap);
        }
        if (item.columns) {
          scanFormItems(item.columns, nameToIdMap);
        }
        if (item.tabs) {
          item.tabs.forEach((tab: any) => {
            if (tab.body) {
              scanFormItems(tab.body, nameToIdMap);
            }
          });
        }
      });
    };

    // 加载表单信息
    const loadFormInfo = async () => {
      setFormData({});
      getModelById(wflowCode)
        .then((rsp: any) => {
          console.error('获取流程模型成功:', rsp);
          form.current = rsp;
        })
        .catch((err: any) => {
          console.error('获取流程模型失败:', err);
        });
    };

    // 在组件挂载时加载表单信息
    useEffect(() => {
      if (wflowCode) {
        loadFormInfo();
      }
      if (!pageData) {
        getRuteDetails();
      }
    }, [wflowCode]);

    const createCustompageObj = (bodyContent: any) => {
      // 确保 bodyContent 是数组
      const bodyArray = Array.isArray(bodyContent)
        ? bodyContent
        : [bodyContent];

      return {
        type: 'form',
        mode: 'horizontal',
        regions: ['body'],
        body: bodyArray,
        actions: [
          {
            type: 'button',
            label: '提交',
            level: 'primary',
            actionType: 'submit'
          }
        ],
        onSubmit: (values: any) => {
          // 在提交时捕获表单值
          // 将 name 转换为 id 作为 key
          const convertedData: Record<string, any> = {};

          // 扫描所有表单控件，获取 id 和 name 的映射关系
          const nameToIdMap: Record<string, string> = {};
          scanFormItems(forms.current, nameToIdMap);

          // 使用映射转换数据结构
          Object.keys(values).forEach(key => {
            if (nameToIdMap[key]) {
              // 使用 id 作为新的键
              convertedData[nameToIdMap[key]] = values[key];
            } else {
              // 如果没有找到映射，保留原键
              convertedData[key] = values[key];
            }
          });

          setFormDataToSubmit(convertedData);
          setShowApprovalDialog(true);
          // handleSubmit(convertedData);
          return true; // 阻止默认提交
        }
      };
    };

    const handleSubmit = (formDataS: any) => {
      let startParams = {
        applicationPageId: edit_id,
        tenantId: store?.tenant_id,
        deptId: store?.userInfo.dept.id,
        formData: formDataS,
        processUsers: {}
      };

      if (form.current.processDefId) {
        startProcess(form.current.processDefId, startParams)
          .then((rsp: any) => {
            console.log(rsp);
            instanceId.current = rsp.match(/wf\d+/)?.[0] || '';
            setShowApprovalDialog(false);
            setFormDataToSubmit(null);
            setPageMode(PageMode.SUCCESS);
            getProcessData();
          })
          .catch((err: any) => {
            console.error(err);
          });
      } else {
        console.error('processDefId is not defined');
      }
    };

    // 头部导航
    const renderHeader = () => {
      return (
        <div className="submission-header">
          <div className="submission-header-left">
            <div className="submission-header-left-appname">
              <img
                className="submission-header-left-appname-icon"
                src={itemShow}
              />
              <div className="submission-header-left-appname-name">
                {store?.apply_name || '应用'}
              </div>
            </div>
          </div>
          <div className="submission-header-right">
            {store?.userInfo && (
              <div className="submission-header-right-user">
                <Avatar
                  className="submission-header-right-user-avatar"
                  src={store.userInfo?.avatar}
                  icon={!store.userInfo?.avatar ? 'user' : undefined}
                />
              </div>
            )}
          </div>
        </div>
      );
    };

    // 处理删除确认
    const handleDelete = () => {
      toast.success('删除成功');
      setShowDeleteDialog(false);
      window.close();
    };

    // 渲染内容
    const renderContent = () => {
      switch (pageMode) {
        case PageMode.SUCCESS:
          // 提交成功页面
          return (
            <div className="submission-success-container">
              <div className="success-content">
                <div className="success-icon">
                  <Icon icon="check" className="check-icon" />
                </div>
                <div className="success-title">提交成功</div>
                <div className="success-desc">数据已提交并保存</div>
                <div className="success-actions">
                  <Button level="primary" onClick={goBack} className="m-r-sm">
                    继续提交
                  </Button>
                  <Button level="default" onClick={showDetail}>
                    查看详情
                  </Button>
                </div>
              </div>
            </div>
          );

        case PageMode.DETAIL:
          // 详情查看页面
          return (
            <div className="submission-detail-container">
              {/* 表单信息头部 */}
              <div className="submission-detail-header">
                <div className="header-main">
                  <div className="submission-detail-title">
                    {pageData?.label || '表单详情'}
                  </div>
                  <div className="header-actions">
                    <Button className="action-btn">复制</Button>
                    <Button className="action-btn">打印</Button>
                  </div>
                </div>
                <div className="submission-detail-info">
                  <div className="info-item">
                    <span className="info-label">提交时间：</span>
                    <span className="info-value">{submittedTime}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">发起人：</span>
                    <span className="info-value">
                      {store?.userInfo?.nickname}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">发起人部门：</span>
                    <span className="info-value">
                      {store?.userInfo?.dept?.name}
                    </span>
                  </div>
                </div>
              </div>

              {/* 表单内容 */}
              <div className="submission-detail-content">
                {pageData && (
                  <AMISRenderer
                    key="detail-view"
                    schema={showDetailSchema(pageData.table_name)}
                  />
                )}
              </div>

              {/* 表单底部内容 - 仅在非编辑状态显示 */}
              {!isEditing && (
                <div className="submission-detail-footer">
                  <div className="footer-section">
                    <div className="section-title">其他</div>
                    <Tabs
                      theme={store?.theme}
                      mode="radio"
                      activeKey={activeTab}
                      onSelect={(key: string) => setActiveTab(key)}
                      linksClassName="section-tabs"
                    >
                      <Tab eventKey="comment" title="评论">
                        <div className="empty-placeholder">暂无评论信息</div>
                      </Tab>
                      <Tab eventKey="history" title="变更记录">
                        <div className="empty-placeholder">暂无变更记录</div>
                      </Tab>
                    </Tabs>
                  </div>
                </div>
              )}

              {/* 底部操作按钮 - 仅在非编辑状态显示 */}
              {!isEditing && (
                <div className="submission-detail-actions">
                  <div className="actions-content">
                    <Button
                      level="primary"
                      className="action-btn"
                      onClick={handleEdit}
                    >
                      编辑
                    </Button>
                    <Button
                      level="default"
                      className="action-btn"
                      onClick={() => setShowDeleteDialog(true)}
                    >
                      删除
                    </Button>
                  </div>
                </div>
              )}
            </div>
          );

        case PageMode.FORM:
        default:
          // 表单填写页面
          return (
            <div className="submission-form-box">
              <div className="submission-form-content">
                {schema ? (
                  <AMISRenderer
                    key="submission-form"
                    schema={schema}
                    data={formData}
                    ref={formRef as RefObject<any>}
                    onAction={(type: string, action: any, data: any) => {
                      console.log('onAction:', type, action, data);
                      if (action.actionType === 'cancel') {
                        goBack();
                      }
                    }}
                  />
                ) : null}
              </div>
            </div>
          );
      }
    };

    // 渲染表单提交页面
    return (
      <Layout className="submission-layout" header={renderHeader()}>
        <div className="submission-page-container">
          {renderContent()}

          {/* 删除确认对话框 */}
          <AMISRenderer
            show={showDeleteDialog}
            onClose={() => setShowDeleteDialog(false)}
            onConfirm={handleDelete}
            schema={DeleteSubmissionConfirm}
            data={{
              tableName: edit_id,
              id: submittedId
            }}
          />

          <ApprovalDialog
            show={showApprovalDialog}
            formData={formDataToSubmit}
            processData={form.current?.process}
            onClose={() => {
              setShowApprovalDialog(false);
              setFormDataToSubmit(null);
            }}
            onConfirm={values => {
              // if (formDataToSubmit) {
              handleSubmit(formDataToSubmit);
              // }
              console.log('审批流程确认:', values);
            }}
          />
        </div>
      </Layout>
    );
  })
);

export default withRouter(ProcessSubmissionPage);
