import React, {FC} from 'react';
import './index.scss';
import {Button, toast, Modal, Input, Select, responseAdaptor} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {createIframeObj, createCustompageObj} from '@/utils/schemaPageTemplate/createPageObjs';
import fileAudio from '@/image/common_icons/file_audio.png';
import filePdf from '@/image/common_icons/file_pdf.png';
import fileWord from '@/image/common_icons/file_word.png';
import fileZip from '@/image/common_icons/file_zip.png';
import type_folder from '@/image/page_icons/type_folder.png';
import PagePanDataSettings from '@/component/PagePanDataSettings/index';
import FormTemplate from './components/FormTemplate';
import AppTemplate from './components/AppTemplate';
import Header from './components/Header';
import {
  getFilePage, //获取表单数据
  getFormDataPage,
  createFormData,
  getComponentClassificationPage,
  createComponentClassification,
  updateComponentClassification,
  deleteComponentClassification
} from '@/utils/api/api';
import PagePanSettings from '@/component/PagePanSettings';
import { height } from '@/editor/EChartsEditor/Common';
import { inject, observer } from 'mobx-react';
import { IMainStore } from '@/store';
import { RouteComponentProps } from 'react-router';

// 声明全局方法
declare global {
  interface Window {
    openUploadDialog: () => void;
    setShowFolderModal: (show: boolean) => void;
    handleDeleteFile: (id: number) => void;
  }
}

interface ComponentTemplatePageContentProps {
  store?: IMainStore;
  type: string;
  history: any;
  location: any;
  match: any;
  pageData?: any;
}

const ComponentTemplatePageContent: FC<ComponentTemplatePageContentProps> = inject('store')(
  observer((props: ComponentTemplatePageContentProps) => {
  const [pageData, setPageData] = React.useState<any>({});
  const [type, setType] = React.useState(() => {
    // 从URL参数中获取type值
    const urlParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
    const typeFromUrl = urlParams.get('type') || '1';
    return typeFromUrl; // 如果URL中没有type参数，则默认为'1'
  });
  
  // 监听模板类型变更事件
  React.useEffect(() => {
    const handleTemplateTypeChange = (event: any) => {
      const newType = event.detail.type;
      // 更新type状态
      setType(newType);
      // 更新URL参数
      const urlParams = new URLSearchParams(props.history.location.search);
      urlParams.set('type', newType);
      props.history.replace({
        pathname: props.history.location.pathname,
        search: urlParams.toString()
      });
      // 刷新页面或重新加载数据
      fetchCategoryList();
    };

    window.addEventListener('templateTypeChange', handleTemplateTypeChange);

    return () => {
      window.removeEventListener('templateTypeChange', handleTemplateTypeChange);
    };
  }, []);

  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');
  
  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 如果切换到数据页设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'dataset' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'datacollection');
    }
    
    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  const [fileList, setFileList] = React.useState<any>([]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [showUploadModal, setShowUploadModal] = React.useState(false);
  const [showFolderModal, setShowFolderModal] = React.useState(false);
  const [showCreateCategoryModal, setShowCreateCategoryModal] = React.useState(false);
  const [newFolderName, setNewFolderName] = React.useState('');
  const [newCategoryName, setNewCategoryName] = React.useState('');
  const [categoryEditMode, setCategoryEditMode] = React.useState('create'); // 'create' 或 'rename'
  const [categoryToEdit, setCategoryToEdit] = React.useState<any>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const appId = props.history.location.pathname.split('/')[1].slice(3);

  //card ,list
  const [mode, setMode] = React.useState('card');
  const [categories, setCategories] = React.useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = React.useState<any>(null);
  const [rightMenuVisible, setRightMenuVisible] = React.useState(false);
  const [rightMenuPosition, setRightMenuPosition] = React.useState({ x: 0, y: 0 });
  const [rightMenuItem, setRightMenuItem] = React.useState<any>(null);

  const [showDeleteCategoryModal, setShowDeleteCategoryModal] = React.useState(false);
  const [categoryToDelete, setCategoryToDelete] = React.useState<any>(null);

  const [searchKeyword, setSearchKeyword] = React.useState('');
  const [searchStatus, setSearchStatus] = React.useState('');
  const [searchCategory, setSearchCategory] = React.useState('');

  const [showCreateTemplateModal, setShowCreateTemplateModal] = React.useState(false);

  const [amisKey, setAmisKey] = React.useState(0);

  // 获取当前模板类型名称
  const getTemplateTypeName = () => {
    switch (type) {
      case '1':
        return '组件模板';
      case '2':
        return '应用模板';
      case '3':
      default:
        return '表单模板';
    }
  };

  const getToBlak = () => {
    if (!props.pageData?.schema) return;
    let schema = JSON.parse(props.pageData.schema);
    let a = document.createElement('a');
    a.href = schema.body[0].src;
    window.open(a.href, '_blank');
  };

  // 获取表单数据 pageData
  // const handleGetFormDataPage = () => {
  //   let data = {
  //     applicationPageId: 1,
  //     pageNo: 1,
  //     pageSize: 10
  //   };
  //   getFormDataPage(data).then((res: any) => {
  //     if (res.code == 0) {
  //       if (res.data.list.length > 0) {
  //         if (res.data.list[0]) {
  //           // toast.success('获取表单数据成功');
  //           res.data.list[0].data = window.JSON.parse(res.data.list[0].data);
  //           res.data.list[0].pageType = 13;
  //           res.data.list[0].appId = 1;
  //           setPageData(res.data.list[0]);
  //         }else{
  //           toast.success('暂无表单数据');
  //         }
  //       }else{
  //         toast.success('暂无表单数据');
  //       }
  //     } else {
  //       toast.error(res.msg);
  //     }
  //   });
  // };

  const initCustompageData = (pageId: any, info: any) => {
    // let data = {
    //   applicationPageId: pageId,
    //   data: window.JSON.stringify(createCustompageObj(info.name)),
    //   field: ''
    // };
    // createFormData(data).then((res: any) => {
    //   if (res.code == 0) {
    //     toast.success('初始化数据完成~');
        // setOpenCustomPagePopup(false);
        if(type == '3'){
          props.history.push(
            `/formTempEditor/${pageId}`
          );
        }else{
          props.history.push(
            `/componentTempEditor/${pageId}`
          );
        }
        // handleGetFormDataPage();
        // props.onSaveSuccess(pageId);
    //   } else {
    //     toast.error('初始化数据失败 ' + res.msg);
    //   }
    // });
  };

  React.useEffect(() => {
    // if (props.pageData?.id) {
      // handleGetFormDataPage();
    // }
    const urlParams = new URLSearchParams(props.history.location.search);
    let tabkey =  urlParams.get('activeTabKey') || 'preview';
    console.log(appId);

    setActiveKey(tabkey);
  }, [props.location]);

  // 创建新分类
  const handleCreateCategory = () => {
    setShowCreateCategoryModal(true);
    setNewCategoryName('');
    setCategoryEditMode('create');
    setCategoryToEdit(null);
  };

  // 重命名分类
  const handleRenameCategory = (category: any) => {
    if (!category || category.id === 'all') return; // 不允许修改"全部分类"
    setShowCreateCategoryModal(true);
    setCategoryEditMode('rename');
    setCategoryToEdit(category);
  };

  // 删除分类
  const handleDeleteCategory = (category: any) => {
    console.log(category);
    if (!category || category.id === 'all') return; // 不允许删除"全部分类"
    
    // 显示删除确认弹窗
    setCategoryToDelete(category);
    setShowDeleteCategoryModal(true);
    setRightMenuVisible(false);
  };
  
  // 执行删除分类操作
  const confirmDeleteCategory = () => {
    if (!categoryToDelete) return;
    
    // 调用删除分类API
    deleteComponentClassification({
      id: categoryToDelete.id
    }).then(result => {
        if (result.code === 0) {
          toast.success('分类删除成功');
          // 更新本地状态
          const updatedCategories = categories.filter(cat => cat.id !== categoryToDelete.id);
          setCategories(updatedCategories);
          if (selectedCategory?.id === categoryToDelete.id) {
            setSelectedCategory({
              id: 'all',
              name: '全部分类',
              type: 2
            });
          }
        } else {
          toast.error(result.msg || '删除失败');
        }
        setShowDeleteCategoryModal(false);
      })
      .catch(error => {
        console.error('删除分类失败', error);
        toast.error('删除分类失败，请稍后重试');
        setShowDeleteCategoryModal(false);
      });
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, category: any) => {
    e.preventDefault();
    const rect = document.documentElement.getBoundingClientRect();
    const windowHeight = rect.bottom - rect.top;
    
    // 计算菜单位置
    const isAboveHalf = e.clientY < windowHeight / 2;
    setRightMenuPosition({
      x: e.clientX,
      y: isAboveHalf ? e.clientY : e.clientY - 100
    });
    
    setRightMenuItem(category);
    setRightMenuVisible(true);
  };

  // 点击其他地方关闭右键菜单
  React.useEffect(() => {
    const handleClickOutside = () => {
      setRightMenuVisible(false);
    };

    if (rightMenuVisible) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [rightMenuVisible]);

  // 添加默认分类
  React.useEffect(() => {
    // 从接口获取分类列表
    fetchCategoryList();
  }, []);

  // 监听URL参数变化
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
    const urlType = urlParams.get('type');
    console.log('URL变化检测:', urlType, '当前type:', type);
    if (urlType && urlType !== type) {
      console.log('更新type值为:', urlType);
      setType(urlType);
      // 更新分类列表
      fetchCategoryList();
    }
  }, [props.location.search, type]);

  // 获取分类列表函数
  const fetchCategoryList = () => {
    // 使用当前的type状态值
    const currentType = type;
    console.log('获取分类列表，使用type:', currentType);
    getComponentClassificationPage({
      pageNo: 1,
      pageSize: 100,
      type: currentType
    }).then(result => {
      if (result.code === 0) {
        console.log('获取分类成功:', result.data.list);
        setCategories([
            {
              id: 'all',
              name: '全部分类',
              type: 2
            },
            ...result.data.list
          ]);
        }
      })
      .catch(error => {
        console.error('获取分类列表失败：', error);
        // 加载失败时使用默认分类
        setCategories([
          {
            id: 'all',
            name: '全部分类',
            type: 2
          },
          {
            id: 1,
            name: '报表组件',
            type: 2
          },
          {
            id: 2,
            name: '表单组件',
            type: 2
          },
          {
            id: 3,
            name: '弹窗基础组件',
            type: 2
          }
        ]);
      });
    setSelectedCategory({
      id: 'all',
      name: '全部分类',
      type: 2
    });
  };

  return (
    <div className="pageBox">
      <Header
        store={props.store}
        location={props.location}
        history={props.history}
        match={props.match}
          />
      <div className="pageTop">
        <div className="pageTop-title">{getTemplateTypeName()}</div>
      </div>
      
      <div className="component-container">


        {/* 右侧内容区 */}
        <div className="content-area">
          {/* 根据模板类型展示不同组件 */}
          {type === '2' ? (
            <AppTemplate onSelect={(item) => {
              console.log('选中项目', item);
            }} history={props.history} />
          ) : (
            <FormTemplate onSelect={(item) => {
              console.log('选中项目', item);
            }} />
          )}
          
        </div>
      </div>
    </div>
  );
}));

export default ComponentTemplatePageContent;
