import {
  EditorManager,
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  BaseEventContext,
  getEventControlConfig,
  EditorNodeType,
  RAW_TYPE_MAP,
  getActionCommonProps
} from 'amis-editor';
import type {SchemaType} from 'amis';
import {
  resolveOptionEventDataSchame,
  resolveOptionType
} from 'amis-editor/lib/util';
export class IconSelectorPlugin extends BasePlugin {
  // 这里要跟对应的渲染器名字对应上
  // 注册渲染器的时候会要求指定渲染器名字
  rendererName = 'icon-selector';

  // 暂时只支持这个，配置后会开启代码编辑器
  $schema = '/schemas/UnkownSchema.json';

  // 用来配置名称和描述
  name = '图标选择器';
  description = 'svg图标选择器';

  // tag，决定会在哪个 tab 下面显示的
  tags = ['表单项'];

  // 图标
  icon = 'fa fa-picture-o';
  panelIcon = 'fa fa-picture-o';
  pluginIcon = 'icon-select-plugin';
  isBaseComponent = true;

  // 拖入组件里面时的初始数据
  scaffold = {
    type: 'icon-selector',
    label: '选择图标',
    name: 'icon',
    clearable: true,
    placeholder: '请选择图标',
    returnSvg: true,
    static: false,
    value: ''
  };

  // 用来生成预览图的
  previewSchema = {
    type: 'form',
    className: 'text-left',
    mode: 'horizontal',
    wrapWithPanel: false,
    body: [this.scaffold]
  };

  notRenderFormZone = true;

  events = [
    {
      eventName: 'change',
      eventLabel: '值变化',
      description: '输入框内容变化',
      dataSchema: (manager: EditorManager) => {
        const node = manager.store.getNodeById(manager.store.activeId);
        const schemas = manager.dataSchema.current.schemas;
        const dataSchema = schemas.find(
          item => item.properties?.[node!.schema.name]
        );
        console.log('change manager', manager);
        console.log('change node', node);
        console.log('change schemas', schemas);
        console.log('change dataSchema', dataSchema);
        return [
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                title: '数据',
                properties: {
                  value: {
                    type: 'string',
                    ...((dataSchema?.properties?.[node!.schema.name] as any) ??
                      {}),
                    title: '当前文本内容'
                  }
                }
              }
            }
          }
        ];
      }
    }
  ];

  actions = [
    {
      actionType: 'clear',
      actionLabel: '清空',
      description: '清空输入框内容',
      ...getActionCommonProps('clear')
    },
    {
      actionType: 'reset',
      actionLabel: '重置',
      description: '将值重置为初始值',
      ...getActionCommonProps('reset')
    },
    {
      actionType: 'reload',
      actionLabel: '重新加载',
      description: '触发组件数据刷新并重新渲染',
      ...getActionCommonProps('reload')
    },
    {
      actionType: 'setValue',
      actionLabel: '赋值',
      description: '触发组件数据更新',
      ...getActionCommonProps('setValue')
    }
  ];

  // 右侧面板相关
  panelTitle = '图标选择器';

  panelBodyCreator = (context: BaseEventContext) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: [
          getSchemaTpl('collapseGroup', [
            // {
            //   title: '常规',
            //   body: [
            //     getSchemaTpl('formItemName', {
            //       required: true
            //     }),
            //     // getSchemaTpl('label')
            //     // getSchemaTpl('icon-select', {
            //     //   type: 'icon-select',
            //     //   name: 'value',
            //     //   placeholder: '请选择图标',
            //     //   label: '默认',
            //     //   returnSvg: true,
            //     // })
            //   ]
            // },

            getSchemaTpl('status', {
              isFormItem: true
            })
          ])
        ]
      },
      // {
      //   title: '外观',
      //   body: getSchemaTpl('collapseGroup', [
      //     {
      //       title: '基本样式',
      //       body: [
      //         getSchemaTpl('theme:select', {
      //           label: '尺寸',
      //           name: 'themeCss.className.iconSize'
      //         }),
      //         getSchemaTpl('theme:colorPicker', {
      //           label: '颜色',
      //           name: `themeCss.className.font.color`,
      //           labelMode: 'input'
      //         }),
      //         getSchemaTpl('theme:paddingAndMargin', {
      //           label: '边距'
      //         })
      //       ]
      //     }
      //   ])
      // },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
  buildDataSchemas(node: EditorNodeType, region: EditorNodeType) {
    const type = resolveOptionType(node.schema);
    let dataSchema: any = {
      type,
      title: node.schema?.label || node.schema?.name,
      rawType: RAW_TYPE_MAP[node.schema.type as SchemaType] || 'string',
      originalValue: node.schema?.value,
      value: node.schema?.value // 添加 value 属性
    };
    console.log('node.schema:', node.schema);
    return dataSchema;
  }
}

// 注册插件
registerEditorPlugin(IconSelectorPlugin);
