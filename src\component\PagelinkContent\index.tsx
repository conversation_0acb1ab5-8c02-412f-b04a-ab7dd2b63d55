import React, {FC} from 'react';
import './index.scss';
import {<PERSON><PERSON>, Ta<PERSON>, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';
import {
  createFromObj,
  createCustompageObj,
  createIframeObj,
  createReportObj,
  createDatasetImportObj
} from '@/utils/schemaPageTemplate/createPageObjs';
import PageLinkSettings from '@/component/PageLinkSettings';

const PagelinkContent: FC<any> = (props: any) => {

  
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');

  // 处理选项卡切换
  const handleTabChange = (key: string) => {
    setActiveKey(key);
    
    // 更新 URL 参数
    const urlParams = new URLSearchParams(props.history.location.search);
    urlParams.set('activeTabKey', key);
    
    // 如果切换到页面设置选项卡，并且没有activeMenu参数，则添加默认值
    if (key === 'setting' && !urlParams.has('activeMenu')) {
      urlParams.set('activeMenu', 'baseSetting');
    }
    
    // 使用 history.replace 而不是 push 以避免创建新的历史记录
    props.history.replace({
      pathname: props.history.location.pathname,
      search: urlParams.toString()
    });
  };

  const getToBlak = () => {
    let a = document.createElement('a');
    a.href = props.pageData.url;
    window.open(a.href, '_blank');
  };

  React.useEffect(() => {
    // 只有在admin模式下才处理tab切换逻辑
    if (props.match.params.playType == 'admin') {
      const urlParams = new URLSearchParams(props.history.location.search);
      let tabKey = urlParams.get('activeTabKey') || 'preview';
      setActiveKey(tabKey);
    }
  }, [props.history.location, props.match.params.playType]);

  return (
    <div className="pageBox">
      {props.match.params.playType == 'admin' &&<div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-6">
            <Button size="lg" level="default" onClick={() => getToBlak()}>
              访问
            </Button>
          </div>
        </div>
      </div>}
      <div className="pageTabsLink">
        {props.match.params.playType == 'admin' ? (
          // admin模式：显示完整的tab标签栏
          <Tabs
            mode="line"
            onSelect={(key: any) => handleTabChange(key)}
            toolbar={TabsToolbar()}
            linksClassName="pageTabsLink-tabsTitle"
            activeKey={activeKey}
          >
            <Tab title="链接预览" eventKey="preview">
              <div className="pageTabsLink-tabsContent">
                <AMISRenderer
                  schema={createIframeObj(props.pageData.url)}
                  embedMode={true}
                />
              </div>
            </Tab>
            <Tab title="页面设置" eventKey="setting">
              <PageLinkSettings
               history={props.history}
                formData={props.formData}
                pageData={props.pageData}
                store={props.store}
                update={() => {
                  props.updatePage();
                }}
              />
            </Tab>
            <Tab title="页面发布" eventKey="publish"></Tab>
          </Tabs>
        ) : (
          // 非admin模式：只显示链接预览内容，不显示tab标签栏
          <div className="pageTabsLink-tabsContent">
            <AMISRenderer
              schema={createIframeObj(props.pageData.url)}
              embedMode={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PagelinkContent;
