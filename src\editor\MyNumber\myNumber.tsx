import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';
import {union} from 'lodash';

export class MyNumberControlPlugin extends BasePlugin {
  static id = 'MyNumberControlPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'my-number';
  $schema = '/schemas/MyNumberControlSchema.json';

  // 组件基本信息
  name = '数字展示';
  panelTitle = '数字展示';
  icon = 'fa fa-calculator';
  panelIcon = 'fa fa-calculator';
  pluginIcon = 'icon-number-plugin';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于展示数字，支持格式化、精度设置、千分位分隔等功能';
  docLink = '/amis/zh-CN/components/number';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'my-number',
    label: '数字',
    name: 'number',
    value: 1234.56,
    precision: 2,
    kilobitSeparator: true,
    placeholder: '-'
  };

  // 添加事件定义
  events = [];

  // 预览界面
  previewSchema = {
    type: 'my-number',
    label: '数字',
    name: 'number',
    value: 1234.56
  };

  // 面板配置
  panelBodyCreator = (context: any) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              id: 'properties-basic',
              body: [
                getSchemaTpl('formItemName', {
                  required: true
                }),
                getSchemaTpl('label'),
                {
                  name: 'precision',
                  label: '精度',
                  type: 'input-number',
                  min: 0,
                  step: 1,
                  precision: 0,
                  description: '指定小数点后的位数'
                },
                {
                  name: 'kilobitSeparator',
                  label: '千分位分隔',
                  type: 'switch',
                  value: true,
                  description: '是否显示千分位分隔符'
                },
                {
                  name: 'percent',
                  label: '百分比显示',
                  type: 'switch',
                  value: false,
                  description: '是否以百分比形式显示',
                  onChange: (value: boolean, oldValue: boolean, model: any, form: any) => {
                    if (value) {
                      form.setValueByName('percentPrecision', 0);
                    }
                  }
                },
                {
                  name: 'percentPrecision',
                  visibleOn: 'data.percent',
                  label: '百分比精度',
                  type: 'input-number',
                  min: 0,
                  step: 1,
                  precision: 0,
                  description: '百分比小数点后的位数'
                },
                {
                  name: 'prefix',
                  label: '前缀',
                  type: 'input-text',
                  placeholder: '如：¥, $'
                },
                {
                  name: 'suffix',
                  label: '后缀',
                  type: 'input-text',
                  placeholder: '如：元, 美元'
                },
                {
                  name: 'unitOptions',
                  label: '单位列表',
                  type: 'input-array',
                  items: {
                    type: 'input-text'
                  },
                  description: '可选的单位列表'
                },
                getSchemaTpl('placeholder', {
                  value: '-'
                })
              ]
            },
            getSchemaTpl('status', {
              isFormItem: false
            })
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        body: getSchemaTpl(
          'collapseGroup',
          getSchemaTpl('theme:common', {
            exclude: ['layout']
          }),
          {...context?.schema, configTitle: 'style'}
        )
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(MyNumberControlPlugin);
