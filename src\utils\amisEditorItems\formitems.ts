const formItems: any = [{
  ref: '表单项',
  // value: '表单项',
  children: [
    {label: '文本框', value: 'input-text'},
    {label: '邮箱框', value: 'input-email'},
    {label: '密码框', value: 'input-password'},
    {label: 'URL输入框', value: 'input-url'},
    {label: '多行文本框', value: 'textarea'},
    {label: '数字框', value: 'input-number'},
    {label: '下拉框', value: 'select'},
    {label: '级联选择器', value: 'nested-select'},
    {label: '链式下拉框', value: 'chained-select'},
    {label: '下拉按钮', value: 'dropdown-button'},
    {label: '复选框', value: 'checkboxes'},
    {label: '单选框', value: 'radios'},
    {label: '勾选框', value: 'checkbox'},
    {label: '日期', value: 'input-date'},
    {label: '日期范围', value: 'input-date-range'},
    {label: '文件上传', value: 'input-file'},
    {label: '图片上传', value: 'input-image'},
    {label: '图标选择器', value: 'icon-selector'},
    {label: '树组件', value: 'input-tree'},
    {label: '标签选择', value: 'input-tag'},
    {label: '列表选择', value: 'list-select'},
    {label: '按钮点选', value: 'button-group-select'},
    {label: '按钮工具栏', value: 'button-toolbar'},
    {label: '列表选取', value: 'picker'},
    {label: '开关', value: 'switch'},
    {label: '滑块', value: 'input-range'},
    {label: '评分', value: 'input-rating'},
    {label: '城市选择', value: 'input-city'},
    {label: '穿梭器', value: 'transfer'},
    {label: '穿梭选择器', value: 'transfer-picker'},
    {label: '组合穿梭器', value: 'tabs-transfer'},
    {label: '颜色框', value: 'input-color'},
    {label: '条件组件', value: 'condition-builder'},
    {label: '字段集', value: 'fieldset'},
    {label: '组合输入', value: 'combo'},
    {label: '输入组合', value: 'input-group'},
    {label: '表格编辑框', value: 'input-table'},
    {label: '矩阵开关', value: 'matrix-checkboxes'},
    {label: '富文本编辑器', value: 'input-rich-text'},
    {label: 'Diff编辑器', value: 'diff-editor'},
    {label: '代码编辑器', value: 'editor'},
    {label: '搜索框', value: 'search-box'},
    {label: 'KV 键值对', value: 'input-kv'},
    {label: '重复周期选择', value: 'input-repeat'},
    {label: 'UUID', value: 'uuid'},
    {label: '地理位置选择', value: 'location-picker'},
    {label: '子表单项', value: 'input-sub-form'},
    {label: '隐藏域', value: 'hidden'},
    {label: '手写签', value: 'input-signature'},
    {label: '静态展示框', value: 'static'},
    {label: '数组输入框', value: 'input-array'},
    {label: '日期时间', value: 'input-datetime'},
    {label: '日期时间范围', value: 'input-datetime-range'},
    {label: '公式', value: 'formula'},
    {label: '表单组', value: 'group'},
    {label: '日期', value: 'input-month'},
    {label: '月份范围', value: 'input-month-range'},
    {label: '季度', value: 'input-quarter'},
    {label: '季度范围', value: 'input-quarter-range'},
    {label: '时间框', value: 'input-time'},
    {label: '日期范围', value: 'input-time-range'},
    {label: '树组件', value: 'tree-select'},
    {label: 'Year', value: 'input-year'},
    {label: '日期范围', value: 'input-year-range'},
    {label: '头像上传', value: 'input-avatar'},
    {label: '部门选择', value: 'department-select'},
    {label: '用户选择', value: 'user-select'},
    {label: '角色选择', value: 'role-select'},
    {label: '岗位选择', value: 'pos-select'},
    {label: '部门多选', value: 'department-multi-select'},
    {label: '用户多选', value: 'user-multi-select'},
    {label: '角色多选', value: 'role-multi-select'},
    {label: '岗位多选', value: 'pos-multi-select'},
    {label: '地址选择', value: 'input-address'},
  ]
}];

const containerItems: any = [{
  ref: '布局容器',
  children: [
    {label: 'Flex 布局', value: 'flex'},
    {label: '分栏', value: 'grid'},
    {label: '容器', value: 'container'},
    {label: '自由容器', value: 'container'},
    {label: '状态容器', value: 'switch-container'},
    {label: '折叠面板', value: 'collapse-group'},
    {label: '面板', value: 'panel'},
    {label: '选项卡', value: 'tabs'},
    {label: 'control', value: 'control'},
    {label: 'HBox', value: 'hbox'},
    {label: '包裹', value: 'wrapper'},
    {label: '页面', value: 'page'}
  ]
}];


const dataItems: any = [{
  ref: '数据容器',
  children: [
    {label: '表格2.0', value: 'crud2'},
    {label: '表单', value: 'form'},
    {label: '服务Service', value: 'service'},
    {label: '增删改查', value: 'crud'},
  ]
}];

const functionItems: any = [{
  ref: '功能',
  children: [
    {label: '按钮', value: 'button'},
    {label: '重置', value: 'reset'},
    {label: '提交', value: 'submit'},
    {label: '按钮组', value: 'button-group'},
    {label: '导航', value: 'nav'},
    {label: '锚点导航', value: 'anchor-nav'},
    {label: '文字提示', value: 'tooltip-wrapper'},
    {label: '提示', value: 'alert'},
    {label: '向导', value: 'wizard'},
    {label: '表格视图', value: 'table-view'},
    {label: 'Web Component', value: 'web-component'},
    {label: '音频', value: 'audio'},
    {label: '视频', value: 'video'},
    {label: '自定义代码', value: 'custom'},
    {label: '异步任务', value: 'tasks'},
    {label: '循环 Each', value: 'each'},
    {label: '属性表', value: 'property'},
    {label: 'iFrame', value: 'iframe'},
    {label: '二维码', value: 'qrcode'},
  ]
}];

const showItems:any = [
  {ref: '展示', children: [
  {label: '文字', value: 'tpl'},
  {label: '图标', value: 'icon'},
  {label: '链接', value: 'link'},
  {label: '列表', value: 'list'},
  {label: '卡片', value: 'card'},
  {label: '卡片列表', value: 'cards'},
  {label: '原子表格', value: 'table'},
  {label: '图表', value: 'chart'},
  {label: '走势图', value: 'sparkline'},
  {label: '轮播图', value: 'carousel'},
  {label: '图片展示', value: 'image'},
  {label: '图片集', value: 'images'},
  {label: '日期展示', value: 'date'},
  {label: '时间展示', value: 'time'},
  {label: '日期时间展示', value: 'datetime'},
  {label: '日历日程', value: 'calendar'},
  {label: '标签', value: 'tag'},
  {label: 'JSON展示', value: 'json'},
  {label: '进度展示', value: 'progress'},
  {label: '状态显示', value: 'status'},
  {label: '步骤条', value: 'steps'},
  {label: '时间轴', value: 'timeline'},
  {label: '分隔线', value: 'divider'},
  {label: '代码高亮', value: 'code'},
  {label: 'Markdown', value: 'markdown'},
  {label: '折叠器', value: 'collapse'},
  {label: '文档预览', value: 'office-viewer'},
  {label: 'PDF预览', value: 'pdf-viewer'},
  {label: '日志', value: 'log'},
  {label: '分页组件', value: 'pagination'},
  {label: '纯文本', value: 'plain'},
  {label: '图标展示', value: 'icon-show'},
  ]
}
]


const classOption:any = [
  {
    label: "表单项",
    value: "表单项"
  },
  {
    label: "布局容器",
    value: "布局容器"
  },
    {
      label: "数据容器",
    value: "数据容器"
  },
    {
      label: "功能",
    value: "功能"
  },
  {
    label: "展示",
    value: "展示"
  }
]

export {formItems, containerItems, dataItems, functionItems, classOption, showItems};
