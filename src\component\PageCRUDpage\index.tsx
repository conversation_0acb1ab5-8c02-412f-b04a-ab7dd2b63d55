import React, {FC} from 'react';
import './index.scss';
import {Button, Tabs, Tab, toast} from 'amis';
import AMISRenderer from '@/component/AMISRenderer';

const PageContent: FC<any> = (props: any) => {
  React.useEffect(() => {}, [props]);
  // 选项卡的 工具栏
  const TabsToolbar = () => {
    return <div className="pageTabsLink-tabsToolbar"></div>;
  };
  //  选项卡显示的activeKey
  const [activeKey, setActiveKey] = React.useState('preview');


  const editForm = () => {
    props.history.push(
      `/app${props.pageData.apply_id}/design/${props.pageData.path}?editCode=${props.pageData.id}`
    );
  };

  return (
    <div className="pageBox">
      <div className="pageTop">
        <div className="pageTop-title">{props.pageData.name}</div>
        <div className="pageTop-right">
          <div className="pl-1">
            <Button size="lg" level="primary" onClick={() => editForm()}>
              编辑增删改查页面
            </Button>
          </div>
        </div>
      </div>
      <div className="pageTabsLink">
        {/* activeKey */}
        <Tabs
          mode="line"
          onSelect={(key: any) => setActiveKey(key)}
          toolbar={TabsToolbar()}
          linksClassName="pageTabsLink-tabsTitle"
          activeKey={activeKey}
        >
          <Tab title="增删改查页面预览" eventKey="preview">
            <div className="pageTabsLink-tabsContent">
              <AMISRenderer
                schema={JSON.parse(props.pageData.schema)}
                embedMode={true}
              />
            </div>
          </Tab>
          <Tab title="页面设置" eventKey="3"></Tab>
          <Tab title="页面发布" eventKey="4"></Tab>
        </Tabs>
      </div>
    </div>
  );
};

export default PageContent;
