export const EditTenantRemarkDialog = (show: boolean, currentName?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑企业描述</b>'
    },
    showCloseButton: false,
    data: {
      remark: currentName
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入企业描述'
      },
      body: [
        {
          type: 'input-text',
          name: 'remark',
          placeholder: '企业描述',
          required: true,
          maxLength: 50,
          validations: {
            maxLength: 50
          },
          inputClassName: 'remark-input',
          labelClassName: 'remark-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 