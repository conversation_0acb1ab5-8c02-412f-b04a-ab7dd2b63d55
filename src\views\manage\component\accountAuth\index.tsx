import React, {FC, useContext} from 'react';
import {observer} from 'mobx-react';
import AMISRenderer from '@/component/AMISRenderer';
import {IMainStore} from '@/store';
import {MobXProviderContext} from 'mobx-react';
import './index.scss';

const AccountAuth: FC<any> = (props: any) => {
  function useStore(): IMainStore {
    const {store} = useContext(MobXProviderContext);
    return store as IMainStore;
  }

  const store = useStore();

  // 账号授权CRUD的Schema配置
  const accountAuthSchema = {
    type: 'page',
    body: [
      {
        type: 'crud',
        api: {
          url: `/admin-api/system/account-authorization/page?userId=${store.userInfo.id}`,
          method: 'get',
          // 数据处理适配器
          adaptor: (payload: any) => {
            if (payload.code === 0 && payload.data) {
              // 获取创建者ID，如果API没有直接提供isCreator字段
              const creatorId = store.userInfo.companyId; // 假设当前用户ID就是创建者ID

              return {
                total: payload.data.total,
                items: payload.data.list.map((item: any) => ({
                  ...item,
                  // 假设userId为1的是创建者，或者根据其他规则判断
                  isCreator: item.userId == creatorId
                }))
              };
            }
            return {
              total: 0,
              items: []
            };
          }
        },
        headerToolbar: [
          {
            type: 'button',
            label: '添加授权',
            icon: 'fa fa-plus',
            level: 'primary',
            actionType: 'dialog',
            dialog: {
              title: '',
              size: 'md',
              body: {
                type: 'form',
                api: {
                  url: '/admin-api/system/account-authorization/batch-create',
                  method: 'post',
                },
                body: [
                  {
                    label: '',
                    type: 'transfer',
                    name: 'userIds',
                    selectMode: 'tree',
                    resultListModeFollowSelect: false,
                    onlyChildren: true,
                    autoCheckChildren: true,
                    source: '/admin-api/system/user/list-dept-user',
                    labelField: 'name',
                    valueField: 'id',
                    searchable: true,
                    statistics: true,
                    required: true,
                    placeholder: '请选择需要授权的成员'
                  }
                ]
              },
              actions: [
                {
                  type: 'button',
                  label: '取消',
                  actionType: 'cancel',
                  className: 'dialog-cancel-btn'
                },
                {
                  type: 'button',
                  label: '确定',
                  level: 'primary',
                  actionType: 'submit',
                  className: 'dialog-confirm-btn'
                }
              ]
            }
          },
          {
            type: 'tpl',
            tpl: '成员（${total}）',
            className: 'auth-count-label'
          }
        ],
        columns: [
          {
            name: 'username',
            label: '姓名',
            type: 'tpl',
            tpl: '${username} ${isCreator ? "<span class=\\"org-creator\\">组织创建者</span>" : ""}',
          },
          {
            name: 'nickname',
            label: '昵称',
          },
          {
            name: 'deptName',
            label: '部门',
          },
          {
            name: 'status',
            label: '账号状态',
            type: 'mapping',
            map: {
              '1': '<span class="badge-status badge-success">已启用</span>',
              '2': '<span class="badge-status badge-danger">已停用</span>'
            }
          },
          {
            name: 'updater',
            label: '编辑人',
          },
          {
            name: 'updateTime',
            label: '编辑时间',
            type: 'input-datetime',
            format: 'YYYY-MM-DD HH:mm:ss',
            static: true,
          },
          {
            name: 'creator',
            label: '创建人',
          },
          {
            name: 'createTime',
            label: '创建时间',
            type: 'input-datetime',
            format: 'YYYY-MM-DD HH:mm:ss',
            static: true,
          },
          {
            type: 'operation',
            label: '操作',
            buttons: [
              {
                type: 'dropdown-button',
                icon: 'fa fa-ellipsis-h',
                hideCaret: true,
                visibleOn: '${!isCreator}', // 如果是组织创建者则不显示操作按钮
                buttons: [
                  {
                    type: 'button',
                    label: "${status == 1 ? '停用账号' : '启用账号'}",
                    confirmTitle:
                      "${status == 1 ? '确定要停用吗？' : '确定要启用吗？'}",
                    confirmText:
                      "${status == 1 ? '停用后，该账号将不能访问搭建平台，确定要停用吗？' : '启用后，该账号可以访问搭建平台，确定要启用吗？'}",
                    actionType: 'ajax',
                    api: {
                      method: 'PUT',
                      url: '/admin-api/system/account-authorization/update',
                      data: {
                        id: '${id}',
                        userId: '${userId}',
                        status: "${status == 1 ? '2' : '1'}"
                      }
                    }
                  },
                  {
                    type: 'button',
                    label: '移除成员',
                    className: 'text-danger',
                    confirmTitle: '确定要移除吗？',
                    confirmText:
                      '移除后，该账号将不能访问搭建平台，确定要移除吗？',
                    actionType: 'ajax',
                    api: 'DELETE:/admin-api/system/account-authorization/delete?id=${id}'
                  }
                ]
              }
            ]
          }
        ],
        bulkActions: [],
        itemActions: [],
        footerToolbar: ['statistics', 'pagination', 'switch-per-page'],
        autoGenerateFilter: false,
        syncLocation: false,
        affixHeader: true,
        initFetch: true,
        className: 'auth-crud-table'
      }
    ]
  };

  return (
    <div className="account-auth-container">
      <div className="page-header">
        <div className="title">搭建账号授权</div>
        <div className="subtitle">
          开启授权的账号具有访问搭建平台的权限，未授权的账号无访问权限。
        </div>
      </div>

      <AMISRenderer
        schema={accountAuthSchema}
        onAction={(type: string, data: any) => {
          console.log(type, data);
        }}
      />
    </div>
  );
};

export default observer(AccountAuth);
