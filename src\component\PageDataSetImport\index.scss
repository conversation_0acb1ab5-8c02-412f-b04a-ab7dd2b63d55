.pageBox {
  position: relative;
  width: 100%;
  height: calc(100vh - 3.125rem);
  overflow: auto;
  /* 隐藏WebKit浏览器（如Chrome、Safari）的滚动条 */
  -webkit-scrollbar {
    display: none;
  }
  /* 隐藏Firefox浏览器的滚动条 */
  scrollbar-width: none;
  
}

.pageTop {
  position: relative;
  width: 100%;
  height: 4.125rem;
  padding: 0 1.875rem;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-title {
    flex: 1;
    color: var(--text-color);
    font-size: 1.25rem;
  }

  &-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.pageTabsForm {
  position: relative;
  width: 100%;

  &-tabsTitle {
    padding: 0 1.875rem;
  }

  &-tabsContent{
    width: 100%;
    min-height: 30rem;
    padding: 1.25rem  1.125rem;

    &-form{
      width: 50%;
      max-width: 32.5rem;
    }
  }
}
