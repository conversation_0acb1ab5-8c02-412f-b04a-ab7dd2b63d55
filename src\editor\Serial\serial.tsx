import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import './serial.scss';

export class SerialPlugin extends BasePlugin {
  static id = 'SerialPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'serial';
  $schema = '/schemas/SerialSchema.json';

  // 生成默认的流水号规则
  generateDefaultSerialRule() {
    const now = new Date();
    const dateStr = now.getFullYear() +
                   String(now.getMonth() + 1).padStart(2, '0') +
                   String(now.getDate()).padStart(2, '0');
    return `IT${dateStr}001`;
  }

  // 组件基本信息
  name = '流水号';
  panelTitle = '流水号';
  icon = 'fa fa-barcode';
  panelIcon = 'fa fa-barcode';
  pluginIcon = 'fa fa-barcode';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于生成和显示流水号的组件';
  docLink = '/amis/zh-CN/components/serial';
  tags = ['表单项'];

  // 组件默认配置
  scaffold = {
    type: 'serial',
    label: '流水号',
    name: 'serial',
    // serialRule: this.generateDefaultSerialRule(),
    serialFields: [
      {
        name: '文本字符',
        type: 'text',
        value: 'IT',
        removable: false
      },
      {
        name: '创建时间',
        type: 'date',
        value: 'YYYY',
        removable: true
      },
      {
        name: '自动编号',
        type: 'auto',
        startValue: 1,
        increment: 2,
        resetType: 'never',
        removable: true
      }
    ]
  };

  // 预览界面
  previewSchema = {
    type: 'serial',
    label: '流水号'
  };

  // 面板配置
  panelBodyCreator = (_context: any) => {
    return getSchemaTpl('collapseGroup', [
      {
        title: '表单项',
        body: [
          getSchemaTpl('formItemMode'),
          getSchemaTpl('formItemSize')
        ]
      },
      {
        title: '基本',
        body: [
          getSchemaTpl('formItemName', {
            required: true
          }),
          getSchemaTpl('label'),
          getSchemaTpl('labelRemark'),
          getSchemaTpl('remark'),
          getSchemaTpl('description')
        ]
      },
      {
        title: '编号规则',
        body: [
          // {
          //   type: 'input-text',
          //   name: 'serialRule',
          //   label: '编号规则',
          //   className: 'serial-rule-preview',
          //   value: this.generateDefaultSerialRule(),
          //   description: '流水号生成规则预览',
          //   disabled: true
          // },
          {
            type: 'combo',
            name: 'serialFields',
            label: '',
            multiple: true,
            addable: true,
            removable: true,
            draggable: true,
            addButtonText: '添加规则',
            className: 'serial-fields-combo',
            labelAlign: 'left',
            mode: 'normal',

            onChange: (value: any, oldValue: any, _model: any, form: any) => {
              // 检测是否有项目被删除
              if (oldValue && Array.isArray(oldValue) && value && Array.isArray(value)) {
                if (value.length < oldValue.length) {
                  console.log('=== 检测到删除操作 ===');
                  console.log('删除前数量:', oldValue.length);
                  console.log('删除后数量:', value.length);

                  // 智能处理删除：找出被删除的项目，重新整理剩余数据的索引
                  try {
                    // 找出被删除的项目索引
                    const deletedIndexes: number[] = [];
                    for (let i = 0; i < oldValue.length; i++) {
                      const oldItem = oldValue[i];
                      const found = value.some((newItem: any) => {
                        return JSON.stringify(oldItem) === JSON.stringify(newItem);
                      });
                      if (!found) {
                        deletedIndexes.push(i);
                      }
                    }

                    console.log('被删除的索引:', deletedIndexes);

                    // 获取所有相关的localStorage数据
                    const allKeys = Object.keys(localStorage);
                    const autoSettingsKeys = allKeys.filter(key => key.startsWith('serial_auto_settings_'));
                    const counterKeys = allKeys.filter(key => key.startsWith('serial_counter_'));
                    const resetKeys = allKeys.filter(key => key.startsWith('serial_last_reset_'));

                    // 备份所有数据
                    const backupData: {[key: string]: any} = {};
                    [...autoSettingsKeys, ...counterKeys, ...resetKeys].forEach(key => {
                      const data = localStorage.getItem(key);
                      if (data) {
                        backupData[key] = data;
                      }
                    });

                    // 清理所有旧数据
                    [...autoSettingsKeys, ...counterKeys, ...resetKeys].forEach(key => {
                      localStorage.removeItem(key);
                    });

                    // 重新分配索引：跳过被删除的索引
                    let newIndex = 0;
                    for (let oldIndex = 0; oldIndex < oldValue.length; oldIndex++) {
                      if (!deletedIndexes.includes(oldIndex)) {
                        // 这个索引的数据需要保留，重新分配到newIndex
                        const oldAutoKey = `serial_auto_settings_${oldIndex}`;
                        const oldCounterKey = `serial_counter_${oldIndex}`;
                        const oldResetKey = `serial_last_reset_${oldIndex}`;

                        const newAutoKey = `serial_auto_settings_${newIndex}`;
                        const newCounterKey = `serial_counter_${newIndex}`;
                        const newResetKey = `serial_last_reset_${newIndex}`;

                        // 恢复数据到新索引
                        if (backupData[oldAutoKey]) {
                          localStorage.setItem(newAutoKey, backupData[oldAutoKey]);
                          console.log(`重新分配: ${oldAutoKey} -> ${newAutoKey}`);
                        }
                        if (backupData[oldCounterKey]) {
                          localStorage.setItem(newCounterKey, backupData[oldCounterKey]);
                          console.log(`重新分配: ${oldCounterKey} -> ${newCounterKey}`);
                        }
                        if (backupData[oldResetKey]) {
                          localStorage.setItem(newResetKey, backupData[oldResetKey]);
                          console.log(`重新分配: ${oldResetKey} -> ${newResetKey}`);
                        }

                        newIndex++;
                      } else {
                        console.log(`删除索引 ${oldIndex} 的数据`);
                      }
                    }

                    console.log(`删除操作完成：删除了索引 ${deletedIndexes.join(', ')}，重新整理了剩余数据`);

                  } catch (error) {
                    console.error('处理删除操作时出错:', error);
                  }
                }
              }

              // 生成预览规则
              if (value && Array.isArray(value)) {
                let preview = '';
                value.forEach((item: any) => {
                  if (item.type === 'text') {
                    preview += item.value || '';
                  } else if (item.type === 'date') {
                    const now = new Date();
                    const format = item.value || 'YYYYMMDD';
                    if (format === 'YYYY') {
                      preview += now.getFullYear();
                    } else if (format === 'YYYYMM') {
                      preview += now.getFullYear() + String(now.getMonth() + 1).padStart(2, '0');
                    } else if (format === 'YYYYMMDD') {
                      preview += now.getFullYear() +
                                 String(now.getMonth() + 1).padStart(2, '0') +
                                 String(now.getDate()).padStart(2, '0');
                    } else if (format === 'YY') {
                      preview += String(now.getFullYear()).slice(-2);
                    } else if (format === 'MM') {
                      preview += String(now.getMonth() + 1).padStart(2, '0');
                    } else if (format === 'DD') {
                      preview += String(now.getDate()).padStart(2, '0');
                    } else if (format === 'MMDD') {
                      preview += String(now.getMonth() + 1).padStart(2, '0') +
                                 String(now.getDate()).padStart(2, '0');
                    }
                  } else if (item.type === 'auto') {
                    const startValue = item.startValue || 1;
                    const digits = item.increment || 2; // increment字段实际存储的是位数
                    // 预览显示第一个编号（就是起始值），并按固定位数格式化
                    const previewNumber = startValue;
                    preview += String(previewNumber).padStart(digits, '0');
                  }
                });
                form.setValueByName('serialRule', preview || '请添加编号规则');
              } else {
                form.setValueByName('serialRule', '请添加编号规则');
              }
            },
            items: [
              {
                type: 'group',
                className: 'serial-field-row',
                body: [
                  {
                    type: 'select',
                    name: 'type',
                    label: '',
                    required: true,
                    className: 'field-type-select',
                    options: [
                      {label: '文本字符', value: 'text'},
                      {label: '创建时间', value: 'date'},
                      {label: '自动编号', value: 'auto'}
                    ],
                    onChange: (value: any, _oldValue: any, _model: any, form: any) => {
                      // 根据类型设置默认值
                      if (value === 'text') {
                        form.setValueByName('name', '文本字符');
                        form.setValueByName('value', '');
                      } else if (value === 'date') {
                        form.setValueByName('name', '创建时间');
                        form.setValueByName('value', 'YYYY');
                      } else if (value === 'auto') {
                        form.setValueByName('name', '自动编号');
                        form.setValueByName('startValue', 1);
                        form.setValueByName('increment', 2);
                        form.setValueByName('resetType', 'never');
                      }
                    }
                  },
                  {
                    type: 'input-text',
                    name: 'value',
                    label: '',
                    placeholder: '字段值',
                    visibleOn: 'this.type === "text"',
                    className: 'field-value-input'
                  },
                  {
                    type: 'select',
                    name: 'value',
                    label: '',
                    visibleOn: 'this.type === "date"',
                    className: 'field-value-select',
                    options: [
                      {label: '年 (YYYY)', value: 'YYYY'},
                      {label: '年月', value: 'YYYYMM'},
                      {label: '年月日', value: 'YYYYMMDD'},
                      {label: '年 (YY)', value: 'YY'},
                      {label: '月', value: 'MM'},
                      {label: '日', value: 'DD'}
                    ]
                  },
                  {
                    type: 'button',
                    label: '设置',
                    visibleOn: 'this.type === "auto"',
                    className: 'field-setting-button',
                    block: true,
                    // 通过data传递当前项的索引
                    data: {
                      fieldIndex: '${index}'
                    },
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'custom',
                            script: `
                              // 获取当前字段的索引，生成唯一的存储键
                              // 优先使用data中的fieldIndex，然后尝试其他方式
                              const fieldIndex = event.data.fieldIndex !== undefined ? event.data.fieldIndex :
                                                (event.data.index !== undefined ? event.data.index :
                                                (event.context && event.context.index !== undefined ? event.context.index : 0));
                              const storageKey = 'serial_auto_settings_' + fieldIndex;

                              console.log('当前字段索引:', fieldIndex, '存储键:', storageKey);
                              console.log('事件数据:', event.data);

                              let dialogData = {
                                startValue: 1,
                                increment: 2,
                                resetType: 'never',
                                _fieldIndex: fieldIndex,
                                _storageKey: storageKey
                              };

                              try {
                                const stored = localStorage.getItem(storageKey);
                                if (stored) {
                                  const savedSettings = JSON.parse(stored);
                                  console.log('读取到保存的设置:', savedSettings);
                                  dialogData = {
                                    startValue: savedSettings.startValue || 1,
                                    increment: savedSettings.increment || 2,
                                    resetType: savedSettings.resetType || 'never',
                                    _fieldIndex: fieldIndex,
                                    _storageKey: storageKey
                                  };
                                }
                              } catch (e) {
                                console.warn('读取本地存储失败:', e);
                              }

                              // 打开弹窗并传入数据
                              doAction({
                                actionType: 'dialog',
                                dialog: {
                                  title: '编号设置',
                                  size: 'md',
                                  data: dialogData,
                                  body: {
                                    type: 'form',
                                    body: [
                                      {
                                        type: 'hidden',
                                        name: '_fieldIndex'
                                      },
                                      {
                                        type: 'hidden',
                                        name: '_storageKey'
                                      },
                                      {
                                        type: 'input-number',
                                        name: 'startValue',
                                        label: '起始值',
                                        min: 1,
                                        required: true
                                      },
                                      {
                                        type: 'input-number',
                                        name: 'increment',
                                        label: '固定位数',
                                        min: 1,
                                        max: 10,
                                        value: 1,
                                        required: true,
                                        description: '自动编号的总位数，不足位数前面补0'
                                      },
                                      {
                                        type: 'select',
                                        name: 'resetType',
                                        label: '重复',
                                        options: [
                                          {label: '不重复', value: 'never'},
                                          {label: '每天重复', value: 'daily'},
                                          {label: '每周重复', value: 'weekly'},
                                          {label: '每月重复', value: 'monthly'},
                                          {label: '每季重复', value: 'quarterly'},
                                          {label: '每年重复', value: 'yearly'}
                                        ]
                                      }
                                    ]
                                  },
                                  actions: [
                                    {
                                      type: 'button',
                                      label: '取消',
                                      actionType: 'cancel'
                                    },
                                    {
                                      type: 'button',
                                      label: '确认',
                                      level: 'primary',
                                      actionType: 'confirm',
                                      onEvent: {
                                        click: {
                                          actions: [
                                            {
                                              actionType: 'custom',
                                              script: \`
                                                // 保存设置到本地存储
                                                const formData = event.data;
                                                const { startValue, increment, resetType, _fieldIndex, _storageKey } = formData;
                                                const storageKey = _storageKey || 'serial_auto_settings_0';

                                                console.log('保存自动编号设置:', {
                                                  startValue, increment, resetType,
                                                  fieldIndex: _fieldIndex, storageKey
                                                });

                                                try {
                                                  const settingsData = {
                                                    startValue: parseInt(startValue) || 1,
                                                    increment: parseInt(increment) || 2,
                                                    resetType: resetType || 'never',
                                                    fieldIndex: _fieldIndex,
                                                    timestamp: Date.now()
                                                  };

                                                  localStorage.setItem(storageKey, JSON.stringify(settingsData));
                                                  console.log('保存成功:', settingsData);

                                                  // 验证保存
                                                  const saved = localStorage.getItem(storageKey);
                                                  console.log('验证保存结果:', saved);

                                                  // 显示成功提示
                                                  doAction({
                                                    actionType: 'toast',
                                                    args: {
                                                      msgType: 'success',
                                                      msg: '自动编号设置保存成功!'
                                                    }
                                                  });

                                                } catch (e) {
                                                  console.error('保存失败:', e);
                                                  doAction({
                                                    actionType: 'toast',
                                                    args: {
                                                      msgType: 'error',
                                                      msg: '保存失败: ' + e.message
                                                    }
                                                  });
                                                }
                                              \`
                                            }
                                          ]
                                        }
                                      }
                                    }
                                  ]
                                }
                              });
                            `
                          }
                        ]
                      }
                    }
                  },
                  {
                    type: 'hidden',
                    name: 'name'
                  }
                ]
              }
            ],
            scaffold: {
              type: 'text',
              name: '文本字符',
              value: ''
            }
          }
        ]
      },
      {
        title: '状态',
        body: [...getSchemaTpl('status').body]
      }
    ]);
  };
}

// 注册插件
registerEditorPlugin(SerialPlugin);
