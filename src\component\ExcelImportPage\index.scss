.excel-import-page {
  .cxd-Dialog-body {
    padding: 24px;
  }

  .cxd-Steps {
    margin-bottom: 32px;

    .cxd-Steps-item-title {
      font-size: 14px;
    }
  }

  .step-content {
    min-height: 400px;
  }

  .loading-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 16px;
    color: #666;
  }
}

// 文件上传区域
.excel-upload-area {
  .cxd-InputFile {
    margin-bottom: 24px;

    .cxd-InputFile-dropzone {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      background: #fafafa;
      transition: all 0.3s;
      padding: 40px 20px;
      text-align: center;

      &:hover {
        border-color: #1890ff;
        background: #f0f8ff;
      }
    }
  }

  .upload-rules {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 16px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    ol {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        font-size: 13px;
        color: #666;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 字段映射表格
.excel-mapping-table {
  .cxd-Crud {
    border: 1px solid #f0f0f0;
    border-radius: 6px;

    .cxd-Table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
    }

    .cxd-Table-tbody > tr > td {
      border-bottom: 1px solid #f0f0f0;

      .cxd-TextControl-input,
      .cxd-Select {
        border-radius: 4px;
      }
    }
  }

  .mapping-actions {
    margin-top: 24px;
    text-align: right;

    .cxd-Button {
      border-radius: 6px;
      height: 36px;
      padding: 0 24px;
      font-weight: 500;
    }
  }
}

// 数据预览表格
.excel-preview-table {
  .cxd-Table {
    border: 1px solid #e6e6e6;
    border-radius: 0;
    margin-bottom: 24px;
    border-collapse: collapse;

    .cxd-Table-thead > tr > th {
      background: #f8f9fa;
      font-weight: 600;
      color: #333;
      border: 1px solid #e6e6e6;
      padding: 12px 8px;
      text-align: center;
      font-size: 14px;
    }

    .cxd-Table-tbody > tr > td {
      border: 1px solid #e6e6e6;
      font-size: 13px;
      padding: 0;
      text-align: center;

      // 自定义单元格内容样式
      > div {
        padding: 8px;

        > div:first-child {
          font-weight: bold;
          color: #666;
          font-size: 12px;
          margin-bottom: 4px;
        }

        > div:last-child {
          color: #333;
          font-size: 14px;
        }
      }
    }
  }

  .preview-actions {
    text-align: right;

    .cxd-Button {
      border-radius: 6px;
      height: 36px;
      padding: 0 24px;
      font-weight: 500;

      &:not(:last-child) {
        margin-right: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .excel-import-page {
    .cxd-Dialog {
      width: 95% !important;
      margin: 10px;
    }
  }

  .excel-import-page {
    .cxd-Steps {
      .cxd-Steps-item-title {
        font-size: 12px;
      }
    }

    .step-content {
      min-height: 300px;
    }
  }

  .excel-mapping-table,
  .excel-preview-table {
    .cxd-Table {
      font-size: 12px;

      .cxd-Table-thead > tr > th,
      .cxd-Table-tbody > tr > td {
        padding: 8px 4px;
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .excel-import-page {
    .upload-rules {
      background: #1f1f1f;
      border-color: #333;

      h4 {
        color: #fff;
      }

      li {
        color: #ccc;
      }
    }
  }
}
