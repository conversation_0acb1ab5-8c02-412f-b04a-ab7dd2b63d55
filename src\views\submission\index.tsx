import React, {FC, useEffect, useState, useRef, RefObject} from 'react';
import {observer, inject} from 'mobx-react';
import {withRouter, RouteComponentProps} from 'react-router-dom';
import AMISRenderer from '@/component/AMISRenderer';
import {toast, Layout, Button, Icon, Avatar, Drawer, Tabs, Tab} from 'amis';
import {
  findCreateBody,
  replacedViewBody
} from '@/utils/schemaDataSet/commonEditor';
import {IMainStore} from '@/store';
import './index.scss';
import itemShow from '../workbench/component/ProjectMain/image/itemShow.png';
import {getFormDataPage} from '@/utils/api/api';
import {DeleteSubmissionConfirm} from '@/utils/schemaDataSet/DeleteSubmissionConfirm';

// 定义路由参数接口
interface RouteParams {
  app: string;
  form: string;
}

// 提交页面组件 - 用于展示提交的表单数据
interface SubmissionPageProps extends RouteComponentProps<RouteParams> {
  store?: IMainStore;
}

// 页面显示模式枚举
enum PageMode {
  FORM = 'form', // 表单填写模式
  SUCCESS = 'success', // 提交成功模式
  DETAIL = 'detail' // 详情查看模式
}

const SubmissionPage: FC<SubmissionPageProps> = inject('store')(
  observer((props: SubmissionPageProps) => {
    const {store, location, history} = props;
    const [schema, setSchema] = useState<any>(null);
    const [pageData, setPageData] = useState<any>(null);
    const [pageMode, setPageMode] = useState<PageMode>(PageMode.FORM); // 使用页面模式替代isSubmitted
    const [submittedId, setSubmittedId] = useState<string | number>(''); // 添加提交后的ID
    const [submittedTime, setSubmittedTime] = useState(''); // 不用 useRef
    const formRef = useRef<any>(null);
    const [activeTab, setActiveTab] = useState('comment');
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [isEditing, setIsEditing] = useState(false);

    // 页面id
    const urlParams = new URLSearchParams(location.search);
    const editCode: any = urlParams.get('editCode');
    const pageName: any = urlParams.get('pageName');
    let edit_id = parseInt(editCode, 10);

    // 获取页面的信息
    const getRuteDetails = async () => {
      let data = {
        applicationPageId: edit_id,
        pageNo: 1,
        pageSize: 10
      };
      getFormDataPage(data)
        .then(res => {
          if (res.code === 0) {
            if (res.data.list.length > 0) {
              if (res.data.list[0]) {
                res.data.list[0].data = window.JSON.parse(
                  res.data.list[0].data
                );
                let form_data = res.data.list[0];
                setPageData(form_data);
                let value = findCreateBody(form_data.data);
                let data = {
                  type: 'page',
                  regions: ['body'],
                  pullRefresh: {
                    disabled: true
                  },
                  body: value
                };
                console.log('data=====>', res.data.list[0].data.body[0].columns);
                // setSchema(data);
                       setSchema(
                formPreviewSchema(
                  res.data.list[0].data,
                  edit_id,
                  pageName
                )
              );
              } else {
                toast.success('暂无表单数据');
              }
            } else {
              toast.success('暂无表单数据');
            }

            // res.data.schema = JSON.parse(res.data.schema);
            // setPageData(res.data);
            // if (res.data.type == 'form' || res.data.type == 'process') {
            //   setSchema(
            //     formPreviewSchema(
            //       res.data.schema,
            //       res.data.table_name,
            //       res.data.label
            //     )
            //   );
            // }
          }
        })
        .catch(err => {
          toast.error(err, 'error');
        });
    };

    // 表单预览的schema处理函数
    const formPreviewSchema = (schema: any, applicationPageId: any, label: any) => {
      let formBody;
      try {
        formBody = findCreateBody(schema);
      } catch (e) {}
      console.log('tableName=====>', formBody);

      return {
        type: 'page',
        body: [
          {
            type: 'form',
            title: `${label}`,
            api: {
              method: 'post',
              url: `/admin-api/system/form-field-value/create/${applicationPageId}`,
              adaptor: function (
                payload: any,
                response: any,
                api: any,
                context: any
              ) {
                console.log(response); // 打印上下文数据
                console.log(payload); // 打印上下文数据
                console.log(payload.data!=null); // 打印上下文数据

                if(payload.data!=null){
                  setSubmittedId(payload.data);
                  return {
                    ...payload
                  };
                }

                // setSubmittedId(payload.data);
                
              }
            },
            body: formBody || [],
            actions: [
              // {
              //   type: 'button',
              //   label: '取消',
              //   actionType: 'cancel'
              // },
              {
                type: 'button',
                label: '提交',
                level: 'primary',
                actionType: 'submit'
              }
            ],
            onEvent: {
              submitSucc: {
                actions: [
                  {
                    actionType: 'custom',
                    script: () => {
                      setPageMode(PageMode.SUCCESS); // 设置为成功模式
                    }
                  }
                ]
              }
            }
          }
        ]
      };
    };

    // 添加处理编辑和保存的函数
    const handleEdit = () => {
      setIsEditing(true);
    };

    // 修改详情页面的 schema 处理函数
    const showDetailSchema = (tableName: any) => {
      let formBody;
      try {
        formBody = findCreateBody(pageData.data);
      } catch (e) {}

      const staticFields = formBody
        ? formBody.map((field: any) => ({
            ...field,
            type: isEditing ? field.type : 'static' // 编辑状态使用原始类型，否则使用静态显示
          }))
        : [];

      return {
        type: 'page',
        body: [
          {
            type: 'form',
            initApi: {
              method: 'get',
              // url: `/apiTest/${tableName}/${submittedId}`,
              url: `/admin-api/system/form-field-value/get/${edit_id}/${submittedId}`,
              adaptor: function (payload: any) {
                setSubmittedTime(payload.data.createTime);
                return {
                  ...payload
                };
              }
            },
            api: isEditing
              ? {
                  method: 'put',
                  // url: `/apiTest/${tableName}/${submittedId}`
                  url: `/admin-api/system/form-field-value/batch-update/${edit_id}/${submittedId}`,
                }
              : undefined,
            body: staticFields,
            labelAlign: 'top',
            title: '',
            mode: 'horizontal',
            labelWidth: 120,
            actions: isEditing
              ? [
                  {
                    type: 'button',
                    label: '取消',
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'custom',
                            script: () => {
                              setIsEditing(false);
                            }
                          }
                        ]
                      }
                    },
                    level: 'default'
                  },
                  {
                    type: 'button',
                    label: '保存',
                    level: 'primary',
                    actionType: 'submit'
                  }
                ]
              : [],
            onEvent: {
              submitSucc: {
                actions: [
                  {
                    actionType: 'custom',
                    script: () => {
                      setIsEditing(false);
                    }
                  }
                ]
              }
            },
            static: !isEditing
          }
        ]
      };
    };

    // 显示详情页面
    const showDetail = () => {
      setPageMode(PageMode.DETAIL); // 切换到详情模式
    };

    // 返回到上一页
    const goBack = () => {
      setPageMode(PageMode.FORM);
    };

    // 头部导航
    const renderHeader = () => {
      return (
        <div className="submission-header">
          <div className="submission-header-left">
            <div className="submission-header-left-appname">
              <img
                className="submission-header-left-appname-icon"
                src={itemShow}
              />
              <div className="submission-header-left-appname-name">
                {store?.apply_name || '应用'}
              </div>
            </div>
          </div>
          <div className="submission-header-right">
            {store?.userInfo && (
              <div className="submission-header-right-user">
                <Avatar
                  className="submission-header-right-user-avatar"
                  src={store.userInfo?.avatar}
                  icon={!store.userInfo?.avatar ? 'user' : undefined}
                />
              </div>
            )}
          </div>
        </div>
      );
    };

    // 处理删除确认
    const handleDelete = () => {
      toast.success('删除成功');
      setShowDeleteDialog(false);
      window.close();
    };

    useEffect(() => {
      if (!pageData) {
        getRuteDetails();
      }
    }, []);

    // 渲染内容
    const renderContent = () => {
      switch (pageMode) {
        case PageMode.SUCCESS:
          // 提交成功页面
          return (
            <div className="submission-success-container">
              <div className="success-content">
                <div className="success-icon">
                  <Icon icon="check" className="check-icon" />
                </div>
                <div className="success-title">提交成功</div>
                <div className="success-desc">数据已提交并保存</div>
                <div className="success-actions">
                  <Button level="primary" onClick={goBack} className="m-r-sm">
                    继续提交
                  </Button>
                  <Button level="default" onClick={showDetail}>
                    查看详情
                  </Button>
                </div>
              </div>
            </div>
          );

        case PageMode.DETAIL:
          // 详情查看页面
          return (
            <div className="submission-detail-container">
              {/* 表单信息头部 */}
              <div className="submission-detail-header">
                <div className="header-main">
                  <div className="submission-detail-title">
                    {pageData?.label || '表单详情'}
                  </div>
                  <div className="header-actions">
                    <Button className="action-btn">复制</Button>
                    <Button className="action-btn">打印</Button>
                  </div>
                </div>
                <div className="submission-detail-info">
                  <div className="info-item">
                    <span className="info-label">提交时间：</span>
                    <span className="info-value">{submittedTime}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">发起人：</span>
                    <span className="info-value">
                      {store?.userInfo?.nickname}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">发起人部门：</span>
                    <span className="info-value">{store?.userInfo?.dept?.name}</span>
                  </div>
                </div>
              </div>

              {/* 表单内容 */}
              <div className="submission-detail-content">
                {pageData && (
                  <AMISRenderer
                    key="detail-view"
                    schema={showDetailSchema(pageData.table_name)}
                  />
                )}
              </div>

              {/* 表单底部内容 - 仅在非编辑状态显示 */}
              {!isEditing && (
                <div className="submission-detail-footer">
                  <div className="footer-section">
                    <div className="section-title">其他</div>
                    <Tabs
                      theme={store?.theme}
                      mode="radio"
                      activeKey={activeTab}
                      onSelect={(key: string) => setActiveTab(key)}
                      linksClassName="section-tabs"
                    >
                      <Tab eventKey="comment" title="评论">
                        <div className="empty-placeholder">暂无评论信息</div>
                      </Tab>
                      <Tab eventKey="history" title="变更记录">
                        <div className="empty-placeholder">暂无变更记录</div>
                      </Tab>
                    </Tabs>
                  </div>
                </div>
              )}

              {/* 底部操作按钮 - 仅在非编辑状态显示 */}
              {!isEditing && (
                <div className="submission-detail-actions">
                  <div className="actions-content">
                    <Button
                      level="primary"
                      className="action-btn"
                      onClick={handleEdit}
                    >
                      编辑
                    </Button>
                    <Button
                      level="default"
                      className="action-btn"
                      onClick={() => setShowDeleteDialog(true)}
                    >
                      删除
                    </Button>
                  </div>
                </div>
              )}
            </div>
          );

        case PageMode.FORM:
        default:
          // 表单填写页面
          return (
            <div className="submission-form-box">
              <div className="submission-form-content">
                {schema ? (
                  <AMISRenderer
                    key="submission-form"
                    schema={schema}
                    data={{}}
                    ref={formRef as RefObject<any>}
                    onAction={(type: string, action: any, data: any) => {
                      console.log('onAction:', type, action, data);
                      if (action.actionType === 'cancel') {
                        goBack();
                      }
                    }}
                  />
                ) : null}
              </div>
            </div>
          );
      }
    };

    // 渲染表单提交页面
    return (
      <Layout className="submission-layout" header={renderHeader()}>
        <div className="submission-page-container">
          {renderContent()}

          {/* 删除确认对话框 */}
          <AMISRenderer
            show={showDeleteDialog}
            onClose={() => setShowDeleteDialog(false)}
            onConfirm={handleDelete}
            schema={DeleteSubmissionConfirm}
            data={{
              tableName: edit_id,
              id: submittedId
            }}
          />
        </div>
      </Layout>
    );
  })
);

export default withRouter(SubmissionPage);
