export const EditNicknameDialog = (show: boolean, currentNickname?: string) => {
  return {
    type: 'dialog',
    title: {
      type: 'html',
      html: '<b>编辑昵称</b>'
    },
    showCloseButton: false,
    data: {
      nickname: currentNickname
    },
    body: {
      type: 'form',
      mode: 'normal',
      wrapWithPanel: false,
      messages: {
        validateFailed: '请输入昵称'
      },
      body: [
        {
          type: 'input-text',
          name: 'nickname',
          placeholder: '昵称',
          required: true,
          maxLength: 20,
          validations: {
            maxLength: 20
          },
          inputClassName: 'name-input',
          labelClassName: 'name-label'
        }
      ]
    },
    actions: [
      {
        type: 'button',
        label: '取消',
        actionType: 'cancel',
        className: 'cancel-btn'
      },
      {
        type: 'button',
        label: '确认',
        level: 'primary',
        actionType: 'confirm',
        className: 'confirm-btn'
      }
    ],
    show: show
  };
}; 