export const EditTeamMember = (setRoleId?: (roleId: number) => void,setTotalAll?: (total: number) => void) => {
  return {
    type: 'page',
    id: 'memberPage',
    body: {
      type: 'crud',
      id: 'memberCrud',
      api: {
        method: 'get',
        url: '/admin-api/system/team-project-application-members/page',
        data: {
          teamOrProjectOrApplicationId: '${teamOrProjectId}',
          pageNo: '${page}',
          pageSize: '${perPage}',
          type: '${type}'
          
        },
        adaptor: function (payload: any) {
          if (setTotalAll) {
            setTotalAll(payload.data.total);
          }
          return {
            status: 0,
            msg: '',
            data: {
              items: payload.data.list || [],
              total: payload.data.total || 0
            }
          };
        }
      },
      columns: [
        {
          name: 'userName',
          label: '成员名称',
          type: 'text'
        },
        {
          name: 'role',
          label: '角色',
          borderMode: "none",
          type: "select",
          quickEdit: {
            mode: "inline",
            type: "select",
            borderMode: "none",
            options: [
              { label: "创建者", value: "1" },
              { label: "管理员", value: "2" },
              { label: "成员", value: "3" }
            ],
            saveImmediately: {
              api: {
                method: "put",
                url: "/admin-api/system/team-project-application-members/update",
                data: {
                  id: "${id}",
                  role: "${role}", // 用户选择的值
                  teamOrProjectOrApplicationId: "${teamOrProjectId}",
                  userId: "${userId}",
                  type: "${type}"
                },
                adaptor: function (payload: any, response: any, api: any) {
                  // 从请求数据中获取用户选择的 role
                  
                  const selectedRole = api.data.role;
                  if (setRoleId) {
                    setRoleId(selectedRole==1 ? 2 : 1);
                  }
                  return {
                    // data: {
                       ...payload,
                    //  role_id: selectedRole==1 ? 2 : 1 // 动态设置为用户选择的 role
                    // }
                  };
                },
              }
            },
            staticOn: "roleId!=1 || (roleId==1 && userId == user_id) "
          }
        },
        {
          name: 'createTime',
          label: '添加时间',
          type: 'date', // 使用 date 类型
          format: 'YYYY-MM-DD HH:mm:ss', // 输出格式
          valueFormat: 'x' // 时间戳（毫秒）
        },
        {
          type: "operation",
          label: "操作", // 显示动态的 role_ids   //操作${role_id}---${role_id1}
          buttons: [
            {
              type: "button",
              label: "移除",
              actionType: "ajax",
              confirmText: "确定要移除该成员吗？",
              api: {
                method: "delete",
                url: "/admin-api/system/team-project-application-members/delete?type=${type}",
                data: { id: "${id}" }
              },
              visibleOn: "roleId == 1 && (userId != user_id)"
            },
            {
              type: "button",
              label: "退出",
              actionType: "ajax",
              confirmText: "确定退出成员吗？",
              api: {
                method: "delete",
                url: "/admin-api/system/team-project-application-members/delete",
                data: { id: "${id}" }
              },
              visibleOn: "this.role != 1 && this.userId == user_id"
            }
          ]
        }
      ],
      pageSize: 10,
      perPageAvailable: [10, 20, 50, 100]
    }
  };
};