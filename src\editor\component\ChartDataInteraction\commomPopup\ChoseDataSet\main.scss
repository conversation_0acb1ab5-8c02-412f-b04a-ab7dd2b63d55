.dataSetMain {
  width: 100%;
  box-sizing: border-box;
  padding: 0.5rem;

  &-searchBox {
    width: 100%;
    margin-bottom: 1rem;
  }

  &-list{
    width: 100%;

    &-item{
      width: 100%;
      margin-bottom: 0.5rem;
      padding: 0.5rem;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      height: 2rem;

      &:hover{
        background-color: rgba($color: #000000, $alpha:.05);
      }

      &-chose{
        width: 1rem;
        height: 1rem;
        margin-right: 0.5rem;
        display: flex;
        justify-content: center;
        align-items: center;

        &-icon{
            width: 1rem;
            height: 1rem;
            display: block;
        }
      }

      &-name{
        flex: 1;
        height: 2rem;
        line-height: 2rem;
        color: rgba($color: #000000, $alpha: .8);
        font-size: 0.875rem;
      }

    }
  }

  &-pagination{
    width: 100%;
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
  }

  &-btnBox{
    width: 100%;
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
  }
}
